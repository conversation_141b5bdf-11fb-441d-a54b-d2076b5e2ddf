package com.juneyaoair.utils.util;

import org.junit.Assert;
import org.junit.Test;

import java.time.LocalDateTime;
import java.util.Date;

import static org.junit.Assert.*;

/**
 * <AUTHOR>
 * @description
 * @date 2023/7/11 17:01
 */
public class DateUtilsTest {

    @Test
    public void millisecondDiff() {
        long startTime = 1686123253000L;
        Assert.assertTrue(DateUtils.millisecondDiff(startTime)>2*60);
    }

    @Test
    public void differOfYear() {
        LocalDateTime localDateTime = LocalDateTime.of(2020,11,20,15,18,0);
        LocalDateTime endDateTime = LocalDateTime.of(2024,10,20,16,0,0);
        long diff = DateUtils.differOfYear(localDateTime,endDateTime);
        System.out.println(diff);
        Assert.assertTrue(diff>0);
    }

    @Test
    public void differOfYear2() {
        LocalDateTime verifyDate = DateUtils.toLocalDateTime(1605856716000L);
        long diff = DateUtils.differOfYear(verifyDate,LocalDateTime.now());
        System.out.println(diff);
        Assert.assertTrue(diff>0);
    }

    @Test
    public void toLocalDateTime() {
        LocalDateTime localDateTime = DateUtils.toLocalDateTime(1699439196222L);
        Assert.assertNotNull(localDateTime);
    }

    @Test
    public void getAgeByBirthIncludeBirthDay() {
        int age = DateUtils.getAgeByBirthIncludeBirthDay("2022-07-02","2024-07-02","yyyy-MM-dd");
        Assert.assertTrue(age>0);
    }

    @Test
    public void getAgeByBirth() {
        int age = DateUtils.getAgeByBirth("2022-07-02","2024-07-02","yyyy-MM-dd");
        Assert.assertTrue(age>0);
    }

    @Test
    public void diffMinute() {
        Date date = new Date();
        Date start = DateUtils.toDate("2024-07-09 11:00",DateUtils.YYYY_MM_DD_HH_MM_PATTERN);
        //date - start
        long diffMinute = DateUtils.diffMinute(start,date);
        Assert.assertTrue(diffMinute>0);
    }
}
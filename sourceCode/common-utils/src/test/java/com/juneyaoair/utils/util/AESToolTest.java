package com.juneyaoair.utils.util;

import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import javax.crypto.BadPaddingException;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import java.io.UnsupportedEncodingException;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;

import static org.junit.Assert.*;

/**
 * <AUTHOR>
 * @description
 * @date 2025/6/5 13:49
 */
@Slf4j
public class AESToolTest {
    private final String aesKey = "9o3vRlBekTj1H7cQ24INp5UuWEbVxgqn";
    private final String ivParam = "VC12m8OURuJrP6kx";

    @Test
    public void encryptAndDecrypt() throws InvalidAlgorithmParameterException, UnsupportedEncodingException, NoSuchPaddingException, IllegalBlockSizeException, NoSuchAlgorithmException, BadPaddingException, InvalidKeyException {
        String str = "这是一个测试消息";
        String encrypt = AESTool.encrypt(str,aesKey,ivParam);
        log.error("密文:{}",encrypt);
        String decrypt = AESTool.decrypt(encrypt,aesKey,ivParam);
        log.error("明文:{}",decrypt);
    }

    @Test
    public void decrypt() {
    }
}
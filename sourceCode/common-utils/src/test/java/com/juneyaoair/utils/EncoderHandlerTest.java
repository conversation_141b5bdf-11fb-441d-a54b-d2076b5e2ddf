package com.juneyaoair.utils;

import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;

import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

/**
 * <AUTHOR>
 * @description
 * @date 2023/5/5 9:14
 */
@Slf4j
public class EncoderHandlerTest {

    @Test
    public void encodeByMD5() {
        String str = "CUXIAO" + "" +"CUXIAO2018";
        String md5 = EncoderHandler.encodeByMD5(str);
        System.out.println(md5);
        Assert.assertNotNull(md5);
    }

    @Test
    public void encodeFlightChange() {
        String str = "0182390504417" + "2024-03-27" +"CUSS"+"HO1093"+"RCA77ZWYQOX0J4VLEPT84WADYU0HJNAO";
        String key = "754d82613b21f9137f54f6b772366baf";
        String sign = EncoderHandler.encodeByMD5(EncoderHandler.encodeByMD5(str));
        Assert.assertTrue(key.equals(sign));
    }

    @Test
    public void checkLoginInfo() {
        String id = "6158807";
        String weixinkey = "poidfjfjeui8939eidrf9893i29edo9rikriw";
        String mobilekey = "poidfjfjeui8939eidrf9893i29edo9rimomo";
        String infoIdKey = EncoderHandler.encode("MD5", id + mobilekey).toUpperCase();
        Assert.assertTrue("AAF20B2157A5DF6A88EDFDBAC07C3752".equals(infoIdKey));
    }

    @Test
    public void testEncodeAndDecode() {
        try{
            String sign = "JAaqHGbY7pSFZ+coFMz3Og==";
            String encode = Base64.getUrlEncoder().encodeToString(sign.getBytes(StandardCharsets.UTF_8));
            System.out.println("URLEncoder:"+ encode);
            System.out.println("URLDEcoder:"+ new String(Base64.getUrlDecoder().decode(encode),StandardCharsets.UTF_8));
            Assert.assertNotNull(encode);
        }catch (Exception e){

        }
    }

    @Test
    public void testUrlDecode() {
        try{
            String s = URLDecoder.decode("JAaqHGbY7pSFZ+coFMz3Og==","utf-8");
            System.out.println(s);
            Assert.assertNotNull(s);
        }catch (Exception e){

        }
    }

    @Test
    public void testUrlDecode2() {
        try{
            String s = URLDecoder.decode("https%3A%2F%2Fmediaws.juneyaoair.com","utf-8");
            System.out.println(s);
            Assert.assertNotNull(s);
        }catch (Exception e){

        }
    }
}
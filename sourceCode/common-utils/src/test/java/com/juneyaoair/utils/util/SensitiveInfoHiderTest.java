package com.juneyaoair.utils.util;

import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;

import static org.junit.Assert.*;

/**
 * <AUTHOR>
 * @description
 * @date 2024/9/3 8:42
 */
@Slf4j
public class SensitiveInfoHiderTest {

    @Test
    public void hideMiddleSensitiveInfo() {
        //18位证件隐藏效果
        String s = SensitiveInfoHider.hideSensitiveInfo("320981198312012345");
        log.error("18位身份证隐藏后结果:{}",s);
        Assert.assertNotNull(s);
    }
    @Test
    public void hideMiddleSensitiveInfoOtherId() {
        String s = SensitiveInfoHider.hideSensitiveInfo("3209818312012345");
        log.error("16位身份证隐藏后结果:{}",s);
        Assert.assertNotNull(s);
    }
    @Test
    public void hideMiddleSensitiveInfoOther() {
        String s = SensitiveInfoHider.hideSensitiveInfo("**********");
        log.error("其他证件隐藏后结果:{}",s);
        Assert.assertNotNull(s);
    }
}
package com.juneyaoair.utils.util;

import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;

import static org.junit.Assert.*;

/**
 * <AUTHOR>
 * @description
 * @date 2024/8/13 13:57
 */
@Slf4j
public class VersionNoUtilTest {

    @Test
    public void compareVersions() {
        int c = VersionNoUtil.compareVersions("7.5.6","7.2");
        log.error("比较大小结果:{}",c);
        Assert.assertEquals(1,c);
    }

    @Test
    public void compareVersions2() {
        int c = VersionNoUtil.compareVersions("7.5.6","*******");
        log.error("比较大小结果:{}",c);
        Assert.assertEquals(1,c);
    }

    @Test
    public void compareVersions3() {
        int c = VersionNoUtil.compareVersions("7.5.6","7.4.0");
        log.error("比较大小结果:{}",c);
        Assert.assertEquals(1,c);
    }

    @Test
    public void compareVersions4() {
        int c = VersionNoUtil.compareVersions("7.5.6","*******");
        log.error("比较大小结果:{}",c);
        Assert.assertEquals(-1,c);
    }

    @Test
    public void toVerInt(){
        int v1 = VersionNoUtil.toVerInt("7.10.1");
        int v2 = VersionNoUtil.toVerInt("8.0.0");
        log.error("v1:{}",v1);
        log.error("v2:{}",v2);
    }
}
package com.juneyaoair.utils.util;

import com.alibaba.fastjson.JSONObject;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import java.util.Map;
import cn.hutool.core.codec.Base62;

/**
 * <AUTHOR>
 * @description
 * @date 2025/6/5 13:39
 */
@Slf4j
public class RsaUtilTest {

    @Test
    public void generateKeyPair() throws Exception {
        Map<String, String> keyPair = RsaUtil.generateKeyPair();
        String publicKey = keyPair.get("publicKey");
        String privateKey = keyPair.get("privateKey");
        log.error("公钥:{}", publicKey);
        log.error("私钥:{}", privateKey);
    }

    @Test
    public void signAndVerify()throws Exception{
        String msg = "这是测试信息";
        Map<String, String> keyPair = RsaUtil.generateKeyPair();
        String publicKey = keyPair.get("publicKey");
        String privateKey = keyPair.get("privateKey");
        String sign = RsaUtil.sign(msg,privateKey);
        log.error("RSA签名认证结果:{}",RsaUtil.verify(msg,sign,publicKey));
    }

    @SneakyThrows
    @Test
    public void jjsc(){
        String aesKey = "9o3vRlBekTj1H7cQ24INp5UuWEbVxgqn";
        String ivParam = "VC12m8OURuJrP6kx";
        String pk = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA3Ejy/kH59DVE/LjOCEARMWHKR33bSbw4EYFJA+YQ4l8Av3I4AIOXhp6KPpR6vtOJIN1QSK3QAwwvvveKOP117NpWwwi1hnvNneUJK//CP1dNOkuAwZ3qjOCHBoJWq6K8HKfTUrLIvbTbhTYkcoHyDS0/sqvzB/RqzwpgIwmLcMTXPAmjXwbcSofromWUx2/sByxvchQv/iWvfGH2wUE5Rc+LdA0QKxU6ipwnVZQMQwxwlDG4vJY+AcpBDciTF3OvGVThtVdX9SFanpVlVbu3iw36PaHG+YczbxFQTPWpy+qLQi7dQ/R+sTZsM2PBE3VtX5sosFuPRT9BB9RLpnxntQIDAQAB";
        String s = "GUKzKDQkBKJStUPdobJ7bjsPIZEadTSxGv5lWOOaxI3OaBZIeFAdSNDT4gD1VWJd6sRNJHzkLE9MVKJtyOFE9onTM1p5YCHiDJau6Y4kJpX2eIQHD5TyBkAMp18GeT63LaUULkM7NB3TpCeocwbVGgSYUNxN1jBUEuBDCV3HwxMfZxNwNhBU1bHAdlzAa2tJMOf0ct5OhZTChX5gwh6LQyyhPgTHXtZWOEnAFNSBztM31NG6QnE8xivgr0unbsZtIHrT2IhUWsSF3xBjjWZONy1U2kODq8zlgf7H30gz807L0y9sE8ParrFp61ho8yOiEylCQT4Xus7ECehguCXMNj7VYdf2VIocUaxUeyrQ4L82MKH0wo2tb6r6dOviUAukhmrcmxQNJzbWjJGJgm3TQGL1g7SOBabfPNVfxWXUFgX9K9uOs19UUOParyUxp2YusjxI8Mqi6IyvAzGZziYGvIaT5SIu3v2MichruiCIopbn5JOR71tvLW9pZm4VHWa3pOK1EjxjOHi9IF0rabGngdYWbQAKoOCHgroNahpkli6xEvGWquP2tNCTiEk3tD89EOKMRflbtGFHpXKbfeUb2gnbC07a3Yy2QKq47NW21Nap476I7b7WLdun583X8u82IUK3cUhAzq5YLuiIsVOqVlFAEItn7R29XRQdaiJLyhr35zZ37UoF7gKCBNRkbPhbwcjwOLJBpsMMKiuDiUmKWYJ6HUYnOFvxVh1aXfYfLEuDLz7eorPIEY6XKXoH7HB9DwOTkYMJxJHp8lcfBROptkjbw34r39FfgPsrecTohxJmrfWJqebXnNFrLuipQSzkaBEKrrr7ZrLRTnSXdDjlvaNKw8g6i24bz4OWmuiOjVeJvhtDt1NDPADcODcFafBsWtkT4vOldyAssNoo23VH0bv8oCc1HEhHg4sQgDcHmM6buzjKEP";
        byte[] bytes = JJSCBase62Util.decodeBase62(s);
        String decode = new String(bytes);
        log.error("Base62解码:{}", decode);
        String aes = AESTool.decrypt(decode,aesKey,ivParam);
        log.error("aes解密:{}", aes);
        JSONObject jsonObject = JSONObject.parseObject(aes);
        String data = jsonObject.getString("memberId")
                +jsonObject.getString("region")
                +jsonObject.getString("phone")
                +jsonObject.getString("nativeGivenName")
                +jsonObject.getString("surname")
                +jsonObject.getString("givenName")
                +jsonObject.getString("timestamp")
                ;
        log.error("RSA签名认证结果:{}",RsaUtil.verify(data,jsonObject.getString("sign"),pk));

    }
}
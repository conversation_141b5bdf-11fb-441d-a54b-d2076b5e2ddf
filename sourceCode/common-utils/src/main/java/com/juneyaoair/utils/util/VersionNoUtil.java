package com.juneyaoair.utils.util;

import org.apache.commons.lang3.StringUtils;

/**
 * Created by yaocf on 2018/6/4  18:12.
 */
//版本比较处理
public class VersionNoUtil {
    /**
     * 5.0.0格式版本转为int数值
     * @param versionNo
     * @return 50000
     */
    public static int toVerInt(String versionNo) {
        int verInt = 0;
        try {
            if (StringUtils.isBlank(versionNo)) {
                verInt = 0;
            } else {
                String[] vers = versionNo.split("\\.");
                for (int i = 0; i < vers.length; i++) {
                    String ver = vers[i];
                    if (i == 0) {//第一位
                        verInt = Integer.parseInt(ver)*1000000;
                    } else if (i == 1) {//中间
                        verInt = verInt +Integer.parseInt(ver)*10000;
                    } else if (i == 2) {//末尾
                        verInt = verInt +Integer.parseInt(ver);
                    }
                }
            }
        } catch (Exception e) {
            verInt = 0;
        }
        return verInt;
    }

    /**
     * M 站版本
     * param versionNo 11000
     * @return
     */
    public static int toMVerInt(String versionNo){
        int verInt;
        try{
            verInt = Integer.parseInt(versionNo);
        }catch (Exception e){
            verInt = 0;
        }
        return verInt;
    }

    /**
     * 比较两个版本号的大小
     *
     * @param version1 第一个版本号
     * @param version2 第二个版本号
     * @return 如果 version1 < version2，则返回负数；如果 version1 > version2，则返回正数；如果相等，则返回0
     */
    public static int compareVersions(String version1, String version2) {
        String[] parts1 = version1.split("\\.");
        String[] parts2 = version2.split("\\.");
        int length = Math.max(parts1.length, parts2.length);
        for (int i = 0; i < length; i++) {
            int num1 = i < parts1.length ? Integer.parseInt(parts1[i]) : 0;
            int num2 = i < parts2.length ? Integer.parseInt(parts2[i]) : 0;
            if (num1 < num2) {
                return -1;
            } else if (num1 > num2) {
                return 1;
            }
        }
        return 0;
    }

}

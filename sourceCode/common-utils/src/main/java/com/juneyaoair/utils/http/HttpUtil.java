package com.juneyaoair.utils.http;

import com.juneyaoair.utils.common.LogLevelEnum;
import com.juneyaoair.utils.json.JsonUtil;
import com.juneyaoair.utils.util.MdcUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.httpclient.DefaultHttpMethodRetryHandler;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.methods.PostMethod;
import org.apache.commons.httpclient.params.HttpMethodParams;
import org.apache.http.HttpEntity;
import org.apache.http.HttpHost;
import org.apache.http.HttpStatus;
import org.apache.http.NameValuePair;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpRequestBase;
import org.apache.http.client.utils.URLEncodedUtils;
import org.apache.http.conn.ConnectTimeoutException;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.SocketTimeoutException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.*;

/**
 * <AUTHOR>
 * @description
 * @date 2018/8/9  16:37.
 */
public class HttpUtil {
    private static Logger log = LoggerFactory.getLogger(HttpUtil.class);
    private static final String SERVICE_CHARSET = "utf-8";
    public static final String JSON_CONTENT_FORM = "application/json;charset=UTF-8";
    public static final String URLENCODED_CONTENT_FORM = "application/x-www-form-urlencoded";
    private static final int SERVICE_TIME_OUT = 0;
    private static final int connectTimeout = 10000;
    private static final int readTimeout = 20000;
    private static final int NEW_CONNECTTIMEOUT = 120000;
    private static final int NEW_READTIMEOUT = 120000;

    /**
     * .net接口调用
     * HttpURLConnection  POST
     *
     * @param req
     * @param url
     * @param log 是否打印debug日志
     * @return
     */
    public static HttpResult doPost(Object req, String url, boolean log) {
        return doHttp(req, url, "POST", log);
    }

    /**
     * 获取自定义的Map头部信息
     *
     * @param clientIp 用户真实的IP
     * @param type     不同的类型设置不同的头部信息
     * @return
     */
    public static Map<String, String> getHeaderMap(String clientIp, String type) {
        return getHeaderMap(clientIp, null, type);
    }

    /**
     * 获取自定义的Map头部信息
     *
     * @param clientIp 用户真实的IP
     * @param type     不同的类型设置不同的头部信息 默认为空
     * @return
     */
    public static Map<String, String> getHeaderMap(String clientIp, Map<String, String> paramMap, String type) {
        Map<String, String> headMap = new HashMap<>();
        if (paramMap != null && !paramMap.isEmpty()) {
            headMap.putAll(paramMap);
        }
        //默认的配置选项
        headMap.put("X-HO-Client-Host", clientIp);
        return headMap;
    }

    /**
     * 第三方接口请求方法
     */
    private static HttpResult doHttp(Object req, String url, String method, boolean showLog) {
        HttpResult result = null;
        long t1 = System.currentTimeMillis();
        try {
            String json = JsonUtil.objectToJson(req);
            if (showLog) {
                log.debug("请求ID:{},时间戳：{},请求的地址：{}，请求的参数：{}",MdcUtils.getRequestId(), t1, url, json);
            }
            result = WebHttpService.webHttpHerlp(url, method, "application/json", json.getBytes(SERVICE_CHARSET),
                    SERVICE_CHARSET,
                    SERVICE_TIME_OUT);
            if (showLog) {
                log.debug("请求ID:{},时间戳：{},请求地址：{}，响应耗时：{}ms,返回结果：{}", MdcUtils.getRequestId(),t1, url, System.currentTimeMillis() - t1, result.getResponse());
            }
        } catch (IOException e) {
            log.error("时间戳：{},网络请求异常：", t1, e);
        }
        return result;
    }

    /**
     * URL地址编码
     *
     * @param url
     * @return
     */
    public static String URLEncode(String url) {
        String encodeUrl = "";
        try {
            encodeUrl = URLEncoder.encode(url, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            log.error(e.getMessage(), e);
            encodeUrl = "";
        }
        return encodeUrl;
    }

    /**
     * java 接口调用
     * 自定义请求时间 自定义头部参数
     *
     * @param req
     * @param requestUrl
     * @return HttpResult  返回服务器状态码
     */
    public static HttpResult doPostClient(Object req, String requestUrl) {
        return doPostClient(req, requestUrl, null, readTimeout, connectTimeout);
    }

    public static HttpResult doPostClient(Object req, String requestUrl, int readTime, int connectTime) {
        return doPostClient(req, requestUrl, null, readTime, connectTime);
    }

    /**
     * java 接口调用
     * 自定义头部参数  默认请求时间
     *
     * @param req
     * @param requestUrl
     * @return HttpResult  返回服务器状态码
     */
    public static HttpResult doPostClient(Object req, String requestUrl, Map<String, String> headMap) {
        return doPostClient(req, requestUrl, headMap, readTimeout, connectTimeout);
    }

    /**
     * java 接口调用
     * 自定义请求时间 自定义头部参数
     *
     * @param req
     * @param requestUrl
     * @param headMap
     * @param readTimeout
     * @param connectTimeout
     * @return HttpResult  返回服务器状态码
     */
    public static HttpResult doPostClient(Object req, String requestUrl, Map<String, String> headMap, int readTimeout, int connectTimeout) {
        return doPostClient(req, requestUrl, headMap, readTimeout, connectTimeout, null);
    }

    /**
     * HttpPost
     *
     * @param req
     * @param requestUrl
     * @param headMap
     * @param readTimeout
     * @param connectTimeout
     * @param httpHost       服务代理设置
     * @return
     */
    public static HttpResult doPostClient(Object req, String requestUrl, Map<String, String> headMap, int readTimeout, int connectTimeout, HttpHost httpHost) {
        return doPostClient(req, requestUrl, JSON_CONTENT_FORM, headMap, readTimeout, connectTimeout, httpHost, LogLevelEnum.INFO);
    }

    /**
     * HttpPost
     * 通过urlencoded 方式提交
     *
     * @param req
     * @param requestUrl
     * @param httpHost   服务代理设置
     * @return
     */
    public static HttpResult doPostClientForm(Object req, String requestUrl, HttpHost httpHost) {
        return doPostClient(req, requestUrl, URLENCODED_CONTENT_FORM, null, readTimeout, connectTimeout, httpHost, LogLevelEnum.INFO);
    }

    /**
     * HttpPost
     *
     * @param req
     * @param requestUrl
     * @param content
     * @param headMap
     * @param readTimeout
     * @param connectTimeout
     * @param httpHost       服务代理设置
     * @return
     */
    public static HttpResult doPostClient(Object req, String requestUrl, String content, Map<String, String> headMap, int readTimeout, int connectTimeout, HttpHost httpHost, LogLevelEnum logLevel) {
        HttpResult result = new HttpResult();
        if (null == req) {
            result.setResult(false);
            result.setResponse("请求参数不能为空");
            return result;
        }
        String param = JsonUtil.objectToJson(req);
        long t1 = System.currentTimeMillis();
        String traceId = MdcUtils.getRequestId();
        printReqLog(logLevel, t1, requestUrl, param,traceId);
        RequestConfig requestConfig;
        if (httpHost != null) {
            requestConfig = RequestConfig.custom().setSocketTimeout(readTimeout).setConnectTimeout
                    (connectTimeout).setConnectionRequestTimeout(connectTimeout).setProxy(httpHost).build();
        } else {
            requestConfig = RequestConfig.custom().setSocketTimeout(readTimeout).setConnectTimeout
                    (connectTimeout).setConnectionRequestTimeout(connectTimeout).build();
        }
        HttpPost httpPost = new HttpPost(requestUrl);
        httpPost.setConfig(requestConfig);
        httpPost.setHeader("Content-Type", content);
        if (headMap != null && !headMap.isEmpty()) {
            for (Map.Entry<String, String> entry : headMap.entrySet()) {
                httpPost.setHeader(entry.getKey(), entry.getValue());
            }
        }
        //设置请求参数
        if (URLENCODED_CONTENT_FORM.equals(content)) {
            if (req instanceof Map) {
                Map paramMap = (Map) req;
                try {
                    UrlEncodedFormEntity urlEncodedFormEntity = new UrlEncodedFormEntity(getParams(paramMap), "utf-8");
                    httpPost.setEntity(urlEncodedFormEntity);
                } catch (UnsupportedEncodingException e) {
                    result.setResult(false);
                    result.setResponse("不支持的编码");
                    return result;
                }
            } else {
                result.setResult(false);
                result.setResponse("不支持的请求参数类型");
                return result;
            }
        } else {
            //默认的请求 post json
            StringEntity stringEntity = new StringEntity(param, SERVICE_CHARSET);
            httpPost.setEntity(stringEntity);
        }
        result = sendRequest(httpPost, SERVICE_CHARSET);
        printRespLog(logLevel, t1, requestUrl, System.currentTimeMillis() - t1, result.getResponse(),traceId);
        return result;
    }

    /**
     * 输出请求参数
     *
     * @param logLevel
     * @param t1
     * @param requestUrl
     * @param param
     */
    public static void printReqLog(LogLevelEnum logLevel, long t1, String requestUrl, String param,String traceId) {
        if (logLevel == null) {
            return;
        }
        String logFormat = "请求号:{},时间戳：{},请求地址：{}，请求参数：{}";
        switch (logLevel) {
            case INFO:
                log.info(logFormat, traceId, t1, requestUrl, param);
                break;
            case DEBUG:
                log.debug(logFormat, traceId, t1, requestUrl, param);
                break;
            case ERROR:
                log.error(logFormat, traceId, t1, requestUrl, param);
                break;
            case TRACE:
                log.trace(logFormat, traceId, t1, requestUrl, param);
                break;
            case WARN:
                log.warn(logFormat, traceId, t1, requestUrl, param);
                break;
        }
    }

    /**
     * 输出结果响应
     *
     * @param logLevel
     * @param t1
     * @param requestUrl
     * @param useTime
     * @param response
     */
    public static void printRespLog(LogLevelEnum logLevel, long t1, String requestUrl, long useTime, String response,String traceId) {
        if (logLevel == null) {
            return;
        }
        String logFormat = "请求号:{},时间戳：{},请求地址：{}，响应耗时：{}ms,返回结果：{}";
        switch (logLevel) {
            case INFO:
                log.info(logFormat, traceId, t1, requestUrl, useTime, response);
                break;
            case DEBUG:
                log.debug(logFormat, traceId, t1, requestUrl, useTime, response);
                break;
            case ERROR:
                log.error(logFormat, traceId, t1, requestUrl, useTime, response);
                break;
            case TRACE:
                log.trace(logFormat, traceId, t1, requestUrl, useTime, response);
                break;
            case WARN:
                log.warn(logFormat, traceId, t1, requestUrl, useTime, response);
                break;
        }
    }

    /*** 组织请求参数{参数名和参数值下标保持一致}
     * @param paramMap    参数
     * @return 参数对象
     */
    private static List<NameValuePair> getParams(Map<String, String> paramMap) {
        List<NameValuePair> nameValuePairList = new ArrayList<>();
        Set<Map.Entry<String, String>> set = paramMap.entrySet();
        for (Map.Entry<String, String> entry : set) {
            nameValuePairList.add(new BasicNameValuePair(entry.getKey(), entry.getValue()));
        }
        return nameValuePairList;
    }

    /**
     *
     * @param requestUrl
     * @param readTimeout 单位ms
     * @param connectTimeout 单位ms
     * @return
     */
    public static HttpResult doGetClient(String requestUrl, int readTimeout, int connectTimeout) {
        return doGetClient(requestUrl, null, readTimeout, connectTimeout, null);
    }

    /**
     * HttpGet
     *
     * @param requestUrl
     * @param headMap
     * @param readTimeout
     * @param connectTimeout
     * @param httpHost       服务代理设置
     * @return
     */
    public static HttpResult doGetClient(String requestUrl, Map<String, String> headMap, int readTimeout, int connectTimeout, HttpHost httpHost) {
        return doGetClient(requestUrl, null, headMap, readTimeout, connectTimeout, httpHost);
    }


    /**
     * @param requestUrl
     * @param param
     * @param headMap
     * @param readTimeout    数据读取时间 单位ms
     * @param connectTimeout 连接创建时间 单位ms
     * @param httpHost
     * @return
     */
    public static HttpResult doGetClient(String requestUrl, Map<String, String> param, Map<String, String> headMap, int readTimeout, int connectTimeout, HttpHost httpHost) {
        long t1 = System.currentTimeMillis();
        //get请求参数拼接
        if (param != null && !param.isEmpty()) {
            List<NameValuePair> nvps = new ArrayList<>();
            for (Map.Entry<String, String> entry : param.entrySet()) {
                nvps.add(new BasicNameValuePair(entry.getKey(), entry.getValue()));
            }
            requestUrl = requestUrl + "?" + URLEncodedUtils.format(nvps, SERVICE_CHARSET);
        }
        // 设置配置请求参数
        RequestConfig requestConfig;
        if (httpHost != null) {
            requestConfig = RequestConfig.custom().setSocketTimeout(readTimeout).setConnectTimeout
                    (connectTimeout).setConnectionRequestTimeout(connectTimeout).setProxy(httpHost).build();
        } else {
            requestConfig = RequestConfig.custom().setSocketTimeout(readTimeout).setConnectTimeout
                    (connectTimeout).setConnectionRequestTimeout(connectTimeout).build();
        }
        // 创建httpGet远程连接实例
        HttpGet httpGet = new HttpGet(requestUrl);
        httpGet.setConfig(requestConfig);
        httpGet.setHeader("Content-Type", "application/json;charset=utf-8");
        if (headMap != null && !headMap.isEmpty()) {
            for (Map.Entry<String, String> entry : headMap.entrySet()) {
                httpGet.setHeader(entry.getKey(), entry.getValue());
            }
        }
        log.info("请求号:{},时间戳：{},请求的地址：{}", MdcUtils.getRequestId(), t1, requestUrl);
        HttpResult result = sendRequest(httpGet, SERVICE_CHARSET);
        log.info("请求号:{},时间戳:{},请求地址:{}，响应耗时:{}ms,返回结果:{}", MdcUtils.getRequestId(), t1, requestUrl, System.currentTimeMillis() - t1, result.getResponse());
        return result;
    }

    private static HttpResult sendRequest(HttpRequestBase httpRequestBase, String encode) {
        CloseableHttpClient httpclient = HttpClients.createDefault();
        HttpResult httpResult = new HttpResult();
        CloseableHttpResponse response = null;
        try {
            response = httpclient.execute(httpRequestBase);
            log.info("请求ID:{},Path:{},返回状态：{}", MdcUtils.getRequestId(),httpRequestBase.getURI().getPath(), response.getStatusLine());
            int statusCode = response.getStatusLine().getStatusCode();
            if (HttpStatus.SC_OK != statusCode) {
                httpResult.setResult(false);
                httpResult.setResponse("服务器正忙，请稍候再试");
                httpResult.setServerCode("" + statusCode);
            } else {
                String responseString;
                httpResult.setResult(true);
                HttpEntity entity = response.getEntity();
                if (entity != null) {
                    responseString = EntityUtils.toString(entity, encode);
                } else {
                    responseString = "";
                }
                httpResult.setResponse(responseString);
                httpResult.setServerCode("" + statusCode);
            }
            return httpResult;
        } catch (ConnectTimeoutException e) {
            log.info("请求ID:{},URI:{}错误信息:", MdcUtils.getRequestId(), httpRequestBase.getURI(), e);
            httpResult.setResult(false);
            httpResult.setResponse("服务器连接超时)");
            if (null != response) {
                httpResult.setServerCode("" + response.getStatusLine().getStatusCode());
            }
        } catch (SocketTimeoutException e) {
            log.info("请求ID:{},URI:{}错误信息:", MdcUtils.getRequestId(), httpRequestBase.getURI(), e);
            httpResult.setResult(false);
            httpResult.setResponse("服务器超时)");
            if (null != response) {
                httpResult.setServerCode("" + response.getStatusLine().getStatusCode());
            }
        } catch (Exception e) {
            log.info("请求ID:{},URI:{}错误信息:", MdcUtils.getRequestId(), httpRequestBase.getURI(), e);
            httpResult.setResult(false);
            httpResult.setResponse("服务器错误");
            if (null != response) {
                httpResult.setServerCode("" + response.getStatusLine().getStatusCode());
            }
        } finally {
            //关闭对应的连接资源
            if (response != null) {
                try {
                    response.close();
                } catch (IOException e) {
                    log.error("关闭CloseableHttpResponse发生异常:", e);
                }
            }
            httpRequestBase.releaseConnection();
            if (httpclient != null) {
                try {
                    httpclient.close();
                } catch (IOException e) {
                    log.error("关闭CloseableHttpClient发生异常:", e);
                }

            }
        }
        return httpResult;
    }

    /**
     * 另外一种调第三方接口请求方法
     * org.apache.commons.httpclient  已停止更新
     */
    public static HttpResult doPayPost(String url, Map<String, String> map) {
        long t1 = System.currentTimeMillis();
        String respJson = JsonUtil.objectToJson(map);
        log.info("{},时间戳：{},doPayPost请求的地址：{}，请求的参数：{}", MdcUtils.getRequestId(), t1, url, respJson);
        HttpClient httpClient = new org.apache.commons.httpclient.HttpClient();
        // 连接时间
        httpClient.getHttpConnectionManager().getParams().setConnectionTimeout(0);
        // 数据传输时间
        httpClient.getHttpConnectionManager().getParams().setSoTimeout(90000);
        PostMethod method = new PostMethod(url);
        method.getParams().setParameter(HttpMethodParams.RETRY_HANDLER, new DefaultHttpMethodRetryHandler(0, false));
        method.getParams().setParameter(HttpMethodParams.HTTP_CONTENT_CHARSET, SERVICE_CHARSET);

        for (Map.Entry<String, String> entry : map.entrySet()) {
            method.addParameter(entry.getKey(), entry.getValue() == null ? "" : entry.getValue());
        }
        HttpResult result = new HttpResult();
        try {
            int statusCode = httpClient.executeMethod(method);
            if (statusCode != HttpStatus.SC_OK) {
                log.info("{},时间戳：{},支付请求失败: {}", MdcUtils.getRequestId(), t1, method.getStatusLine());
                result.setResponse("支付请求失败");
                result.setResult(false);
            } else {
                byte[] responseBody = method.getResponseBody();
                String responseBodyStr = new String(responseBody, SERVICE_CHARSET);
                result.setResponse(responseBodyStr);
                result.setResult(true);
            }
            String resJson = JsonUtil.objectToJson(result);
            log.info("{},时间戳：{},响应时间：{},doPayPost取得的执行结果：{}", MdcUtils.getRequestId(), t1, System.currentTimeMillis() - t1, resJson);
        } catch (Exception ex) {
            result.setResponse("支付请求失败");
            result.setResult(false);
        } finally {
            method.releaseConnection();
        }
        return result;
    }

    /**
     * 调java接口
     *
     * @param req
     * @param requestUrl
     * @return
     * @deprecated
     */
    @Deprecated
    public static String invokePost(Object req, String requestUrl) {
        return invokePost(req, requestUrl, NEW_READTIMEOUT, NEW_CONNECTTIMEOUT);

    }


    /**
     * 默认请求时间 ，自定义请求头
     *
     * @param req
     * @param requestUrl
     * @param headMap
     * @return
     * @deprecated
     */
    @Deprecated
    public static String invokePost(Object req, String requestUrl, Map<String, String> headMap) {
        return invokePost(req, requestUrl, headMap, NEW_READTIMEOUT, NEW_CONNECTTIMEOUT);
    }

    /**
     * 自定义请求时间 无请求头信息
     *
     * @param req
     * @param requestUrl
     * @param readTimeout
     * @param connectTimeout
     * @return
     * @deprecated
     */
    @Deprecated
    public static String invokePost(Object req, String requestUrl, int readTimeout, int connectTimeout) {
        return invokePost(req, requestUrl, null, readTimeout, connectTimeout);
    }

    /**
     * 自定义请求时间 自定义头部参数
     *
     * @param req
     * @param requestUrl
     * @param readTimeout
     * @param connectTimeout
     * @return 建议使用方法  doPostClient
     * @deprecated
     */
    @Deprecated
    public static String invokePost(Object req, String requestUrl, Map<String, String> headMap, int readTimeout, int connectTimeout) {
        if (null == req) {
            return "";
        }
        String param = JsonUtil.objectToJson(req);
        long t1 = System.currentTimeMillis();
        log.debug("时间戳：{},请求的地址：{}，请求的参数：{}", t1, requestUrl, param);
        RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(readTimeout).setConnectTimeout
                (connectTimeout).setConnectionRequestTimeout(connectTimeout).build();
        String responseString;
        HttpPost httpPost = new HttpPost(requestUrl);
        httpPost.setConfig(requestConfig);
        StringEntity s = new StringEntity(param, SERVICE_CHARSET);
        httpPost.setEntity(s);
        httpPost.setHeader("Content-Type", "application/json;charset=utf-8");
        if (headMap != null && !headMap.isEmpty()) {
            for (Map.Entry<String, String> entry : headMap.entrySet()) {
                httpPost.setHeader(entry.getKey(), entry.getValue());
            }
        }
        responseString = doRequest(httpPost, SERVICE_CHARSET);
        log.debug("时间戳：{},请求的地址：{}，响应耗时：{} ms,返回的结果：{}", t1, requestUrl, System.currentTimeMillis() - t1, responseString);
        return responseString;
    }

    private static String doRequest(HttpRequestBase httpRequestBase, String encode) {
        CloseableHttpClient httpclient = HttpClients.createDefault();
        HttpEntity entity = null;
        CloseableHttpResponse response = null;
        String responseString = null;
        try {
            response = httpclient.execute(httpRequestBase);
            log.info("返回状态：{}", response.getStatusLine());
            entity = response.getEntity();
            if (entity != null) {
                responseString = EntityUtils.toString(entity, encode);
            } else {
                responseString = "";
            }
        } catch (SocketTimeoutException e) {
            responseString = "";
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            responseString = "";
        } finally {
            //关闭对应的连接资源
            if (response != null) {
                try {
                    response.close();
                } catch (IOException e) {
                    log.error("关闭CloseableHttpResponse发生异常:", e);
                }
            }
            httpRequestBase.releaseConnection();
            try {
                httpclient.close();
            } catch (IOException e) {
                log.error("关闭CloseableHttpClient发生异常:", e);
            }
        }
        return responseString;
    }

    /**
     * @param allowList 允许访问的合法url前缀
     * @param path      当前参数url
     * @return
     */
    public static boolean checkUrl(List<String> allowList, String path) {
        if (CollectionUtils.isEmpty(allowList)) {
            return true;
        }
        String finalPath = URLDecoder.decode(path);
        return allowList.stream().anyMatch(s -> finalPath.startsWith(s));
    }

}

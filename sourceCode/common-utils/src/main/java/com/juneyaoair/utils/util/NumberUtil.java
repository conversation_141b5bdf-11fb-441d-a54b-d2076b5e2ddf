package com.juneyaoair.utils.util;

import com.juneyaoair.utils.StringUtil;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.Random;

/**
 * <AUTHOR>
 * @description
 * @date 2019/5/16  19:33.
 */
public class NumberUtil {

    private static Random random;

    private NumberUtil(){}

    //处理double数值的.0问题
    public static String formatNumber(String num){
        if(StringUtil.isNullOrEmpty(num)){
            return "";
        }
        if (num.endsWith(".0")) {
            num = num.substring(0, num.indexOf(".0"));
        }
        return num;
    }

    /**
     * double四舍五入取整
     * @param number
     * @return
     */
    public static int doubleRoundHalfUpInt(double number){
        try{
            BigDecimal bd = BigDecimal.valueOf(number).setScale(0, BigDecimal.ROUND_HALF_UP);
            return Integer.parseInt(bd.toString());
        }catch (Exception e){
            return 0;
        }
    }
    /**
     * double向上取整
     * @param number
     * @return
     */
    public static int doubleRoundUpInt(double number){
        try{
            BigDecimal bd = BigDecimal.valueOf(number).setScale(0, BigDecimal.ROUND_UP);
            return Integer.parseInt(bd.toString());
        }catch (Exception e){
            return 0;
        }
    }

    /**
     * 字符串转double
     * @param number
     * @return
     */
    public static Double stringToDouble(String number){
        try{
            return Double.valueOf(number);
        }catch (Exception e){
            return 0.0;
        }
    }

    /**
     * 字符串转int
     * @param number
     * @return
     */
    public static Integer stringToInt(String number){
        try{
            return Integer.valueOf(number);
        }catch (Exception e){
            return null;
        }
    }

    /**
     * 除法运算
     * @return
     */
    public static BigDecimal divide(long dividend, double divisor,int scale){
        //保留两位小数
        BigDecimal bigDecimal = BigDecimal.valueOf(dividend);
        BigDecimal divisorDecimal = BigDecimal.valueOf(divisor);
        BigDecimal result = bigDecimal.divide(divisorDecimal,scale,BigDecimal.ROUND_HALF_UP);
        return result;
    }

    /**
     * 随机获取6位随机数
     * @return
     */
    public static String getChkCode() {
        if (null == random) {
            random = new Random();
        }
        StringBuilder stringBuilder = new StringBuilder("");
        for (int i = 0; i < 6; i++) {
            String rand = String.valueOf(random.nextInt(10));
            stringBuilder.append(rand);
        }
        return stringBuilder.toString();
    }
}

package com.juneyaoair.utils.http;

import com.juneyaoair.utils.https.SSLClient;
import com.juneyaoair.utils.json.JsonUtil;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpRequestBase;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicHeader;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.net.ssl.*;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.SocketTimeoutException;
import java.net.URL;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.Map;
import java.util.Set;

/**
 * 无视Https证书是否正确的Java Http Client
 * <p>
 * <p>
 * <a href="HttpsUtil.java.html"><i>View Source</i></a>
 * </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">LiYan</a>
 * @version $Id$
 * @create Sep 10, 2009 9:59:35 PM
 */
public class HttpsUtil {
    private static Logger logger = LoggerFactory.getLogger(HttpsUtil.class.getName());
    private static final int CONNECT_TIMEOUT = 60000;
    private static final int READ_TIMEOUT = 60000;
    private static final String SERVICE_CHARSET = "utf-8";
    private static final boolean IGNORE_HOST_NAME = true;
    private static final String CONTENT_TYPE = "Content-Type";

    private static final boolean THROWS_EXCEPTIONS = false;

    private static final String PROTOCOL = "SSL";
    /**
     * 忽视证书HostName
     */
    private static HostnameVerifier ignoreHostnameVerifier = (s,sslsession) -> IGNORE_HOST_NAME;

    /**
     * Ignore Certification
     */
    private static TrustManager ignoreCertificationTrustManger = new X509TrustManager() {

        private X509Certificate[] certificates;

        @Override
        public void checkClientTrusted(X509Certificate[] certificates,
                                       String authType) throws CertificateException {
            if (this.certificates == null) {
                if (THROWS_EXCEPTIONS) {
                    throw new CertificateException();
                }
                this.certificates = certificates;
            }
        }

        @Override
        public void checkServerTrusted(X509Certificate[] ax509certificate, String s) throws CertificateException{
            if (this.certificates == null) {
                if (THROWS_EXCEPTIONS) {
                    throw new CertificateException();
                }
                this.certificates = ax509certificate;
            }
        }

        @Override
        public X509Certificate[] getAcceptedIssuers() {
            return new X509Certificate[]{};
        }

    };

    private static HttpsURLConnection getConn(URL url) throws IOException {
        //正式
        HttpsURLConnection connection = (HttpsURLConnection) url.openConnection();
        return connection;
    }

    public static byte[] doGet(String urlString) {

        ByteArrayOutputStream buffer = new ByteArrayOutputStream(512);
        try {

            URL url = new URL(urlString);

			/*
             * use ignore host name verifier
			 */
            HttpsURLConnection.setDefaultHostnameVerifier(ignoreHostnameVerifier);
            HttpsURLConnection connection = getConn(url);

            // Prepare SSL Context
            TrustManager[] tm = {ignoreCertificationTrustManger};
            SSLContext sslContext = SSLContext.getInstance(PROTOCOL, "SunJSSE");
            sslContext.init(null, tm, new java.security.SecureRandom());

            // 从上述SSLContext对象中得到SSLSocketFactory对象
            SSLSocketFactory ssf = sslContext.getSocketFactory();

            connection.setSSLSocketFactory(ssf);

            InputStream reader = connection.getInputStream();
            byte[] bytes = new byte[512];
            int length = reader.read(bytes);

            do {
                buffer.write(bytes, 0, length);
                length = reader.read(bytes);
            } while (length > 0);

            reader.close();
            connection.disconnect();
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }
        return buffer.toByteArray();
    }


    /**
     * 停止引用
     * @see HttpUtil
     * @param req
     * @param requestUrl
     * @return
     */
    @Deprecated
    //调java接口
    public static String invokePost(Object req, String requestUrl) {
        return invokePost(req, requestUrl, CONNECT_TIMEOUT, READ_TIMEOUT);
    }

    /**
     * 停止引用
     * @see HttpUtil
     * @param req
     * @param requestUrl
     * @param connectTimeout
     * @param readTimeout
     * @return
     */
    @Deprecated
    public static String invokePost(Object req, String requestUrl, int connectTimeout, int readTimeout) {
        if (null == req) {
            return "";
        }
        String param = JsonUtil.objectToJson(req);
        logger.debug("请求的地址：{} 请求的参数：{}",requestUrl, param);
        //setConnectTimeout：设置连接超时时间
        //setConnectionRequestTimeout：设置从connect Manager获取Connection 超时时间，单位毫秒。这个属性是新加的属性，因为目前版本是可以共享连接池的
        //setSocketTimeout：请求获取数据的超时时间，单位毫秒
        RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(readTimeout).setConnectTimeout(connectTimeout).setConnectionRequestTimeout(connectTimeout).build();
        String responseString;
        HttpPost httpPost = new HttpPost(requestUrl);
        httpPost.setConfig(requestConfig);
        StringEntity s = new StringEntity(param, SERVICE_CHARSET);
        httpPost.setEntity(s);
        httpPost.setHeader(CONTENT_TYPE, "application/json;charset=utf-8");
        responseString = doRequest(httpPost, SERVICE_CHARSET);
        logger.debug("返回的结果：{}", responseString);
        return responseString;
    }

    private static String doRequest(HttpRequestBase httpRequestBase, String encode) {
        CloseableHttpClient httpclient = HttpClients.createDefault();
        String responseString = null;
        try {
            CloseableHttpResponse response = httpclient.execute(httpRequestBase);
            try {
                HttpEntity entity = response.getEntity();
                try {
                    if (entity != null) {
                        responseString = EntityUtils.toString(entity, encode);
                    }
                } finally {
                    if (entity != null) {
                        entity.getContent().close();
                    }
                }
            } catch (Exception e) {
                responseString = "";
            } finally {
                response.close();
            }
        } catch (SocketTimeoutException e) {
            responseString = "";
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            responseString = "";
        } finally {
            httpRequestBase.releaseConnection();
            try {
                httpclient.close();
            } catch (IOException e) {
                logger.error(e.getMessage(), e);
            }
        }
        return responseString;
    }

    /**
     * HTTPS接口调用方法
     * isProxy 是否使用代理，true为使用
     * headerMap 类型，header集合
     **/
    public static String doHttpsPost(String url, String jsonstr, String charset, Map<String, String> headerMap) {
        HttpPost httpPost = null;
        String result = null;
        try(SSLClient httpClient = new SSLClient()) {
            httpPost = new HttpPost(url);
            httpPost.addHeader(CONTENT_TYPE, "application/json");
            if (null != headerMap) {
                Set<String> set = headerMap.keySet();
                for (String key : set) {
                    String value = headerMap.get(key);
                    httpPost.addHeader(key, value);
                }
            }
            StringEntity se = new StringEntity(jsonstr, charset);
            se.setContentType("text/json");
            se.setContentEncoding(new BasicHeader(CONTENT_TYPE, "application/json"));
            httpPost.setEntity(se);
            HttpResponse response = httpClient.execute(httpPost);
            if (response != null) {
                HttpEntity resEntity = response.getEntity();
                if (resEntity != null) {
                    result = EntityUtils.toString(resEntity, charset);
                }
            }
        } catch (Exception ex) {
            logger.error(ex.getMessage(),ex);
            return null;
        } finally {
            if (null != httpPost) {
                httpPost.releaseConnection();
            }
        }
        return result;
    }

    /**
     * HTTPS接口调用方法 GET
     *
     * @param req
     * @param requestUrl
     * @param charset
     * @param isProxy
     * @param headerMap
     * @return
     * @throws Exception
     */
    public static String doHttpsGet(Object req, String requestUrl, String charset, Boolean isProxy, Map<String, String> headerMap) {
        return doHttpsGet(req, requestUrl, charset, READ_TIMEOUT, CONNECT_TIMEOUT, isProxy, headerMap);
    }

    public static String doHttpsGet(Object req, String requestUrl, String charset, int readTimeout, int connectTimeout, Boolean isProxy, Map<String, String> headerMap) {
        long t1 = System.currentTimeMillis();
        String param = "";
        if (req != null) {
            param = JsonUtil.objectToJson(req);
        }
        logger.info("请求的地址：{}，请求的参数：{}", requestUrl, param);
        RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(readTimeout).setConnectTimeout
                (connectTimeout).setConnectionRequestTimeout(connectTimeout).build();
        HttpGet httpGet = new HttpGet(requestUrl);
        httpGet.setConfig(requestConfig);
        httpGet.setHeader(CONTENT_TYPE, "application/json;charset=utf-8");
        if (headerMap != null) {
            for (Map.Entry<String, String> entry : headerMap.entrySet()) {
                httpGet.setHeader(entry.getKey(), entry.getValue());
            }
        }
        CloseableHttpClient httpclient = HttpClients.createDefault();
        String responseString = null;
        try {
            CloseableHttpResponse response = httpclient.execute(httpGet);
            try {
                HttpEntity entity = response.getEntity();
                try {
                    if (entity != null) {
                        responseString = EntityUtils.toString(entity, charset);
                    }
                } finally {
                    if (entity != null) {
                        entity.getContent().close();
                    }
                }
            } catch (Exception e) {
                responseString = "";
            } finally {
                if (response != null) {
                    response.close();
                }
            }
        } catch (SocketTimeoutException e) {
            responseString = "";
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            responseString = "";
        } finally {
            httpGet.releaseConnection();
            if (null != httpclient) {
                try {
                    httpclient.close();
                } catch (IOException e) {
                    logger.error(e.getMessage(), e);
                }
            }
        }
        logger.info("请求的地址：{}，请求的参数：{}，响应耗时：{}ms,返回的结果：{}", requestUrl, param, System.currentTimeMillis() - t1, responseString);
        return responseString;
    }

    public static void main(String[] args) {
        String urlString = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=wx42f49ced634e312b&secret=e56becda367f88c69b1e0d7a20b038b4";
        String output = new String(HttpsUtil.doGet(urlString));
        logger.info(output);
    }
}

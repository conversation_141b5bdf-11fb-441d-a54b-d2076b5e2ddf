package com.juneyaoair.utils.apple;

import com.auth0.jwk.InvalidPublicKeyException;
import com.auth0.jwk.Jwk;
import com.juneyaoair.utils.apple.bean.AppleAuthKeysResponse;
import com.juneyaoair.utils.apple.bean.ApplePublicKey;
import com.juneyaoair.utils.apple.bean.AppleTokenClaims;
import com.juneyaoair.utils.apple.bean.AppleTokenHead;
import com.juneyaoair.utils.exception.BusinessException;
import com.juneyaoair.utils.http.HttpsUtil;
import com.juneyaoair.utils.json.JsonUtil;
import io.jsonwebtoken.*;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.security.PublicKey;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 苹果登录工具类
 * <AUTHOR>
 * @Description
 * @create 2020-07-23 15:18
 */
public class AppleLoginUtil {

    /**
     * 获取苹果公钥
     * 此处可能返回多个公钥，根据identifyToken中header中的kid来定位并生成对应的publicKey
     * @return
     */
    public static List<ApplePublicKey> getApplePublicKey() {
        String httpResult = new String(HttpsUtil.doGet("https://appleid.apple.com/auth/keys"));
        if (StringUtils.isBlank(httpResult)) {
            throw new BusinessException("获取公钥失败");
        }
        AppleAuthKeysResponse appleAuthKeysResponse = JsonUtil.fromJson(httpResult, AppleAuthKeysResponse.class);
        return appleAuthKeysResponse.getKeys();
    }

    /**
     * 生成公钥
     * @param applePublicKey ApplePublicKey 转map
     * @return
     * @throws InvalidPublicKeyException
     */
    public static PublicKey genPublicKey(Map<String, Object> applePublicKey) throws InvalidPublicKeyException {
        Jwk jwa = Jwk.fromValues(applePublicKey);
        return jwa.getPublicKey();
    }

    /**
     * 验证
     * @param key
     * @param jwt
     * @param audience
     * @param subject
     * @return
     */
    public static boolean verify(PublicKey key, String jwt, String audience, String subject) {
        JwtParser jwtParser = Jwts.parser().setSigningKey(key);
        jwtParser.requireIssuer("https://appleid.apple.com");
        jwtParser.requireAudience(audience);
        jwtParser.requireSubject(subject);
        try {
            Jws<Claims> claim = jwtParser.parseClaimsJws(jwt);
            if (claim != null && claim.getBody().containsKey("auth_time")) {
                return true;
            }
            return false;
        } catch (ExpiredJwtException e) {
            throw new BusinessException("苹果token过期", e);
        } catch (Exception e) {
            throw new BusinessException("非法token", e);
        }
    }

    /**
     * 验证token
     * @return 苹果用户唯一编码
     */
    public static String verifyIdentityToken(String identityToken){
        if (StringUtils.isBlank(identityToken)) {
            throw new BusinessException("token为空");
        }
        if (identityToken.split("\\.").length <= 1) {
            throw new BusinessException("不合法的token");
        }
        List<ApplePublicKey> applePublicKeys = getApplePublicKey();
        Map<String, List<ApplePublicKey>> applePublicKeyMap = applePublicKeys.stream().collect(Collectors.groupingBy(ApplePublicKey::getKid));
        String headStr = new String(Base64.decodeBase64(identityToken.split("\\.")[0]));
        String claimStr = new String(Base64.decodeBase64(identityToken.split("\\.")[1]));
        AppleTokenHead head = JsonUtil.fromJson(headStr, AppleTokenHead.class);
        AppleTokenClaims claims = JsonUtil.fromJson(claimStr, AppleTokenClaims.class);
        List<ApplePublicKey> publicKeys = applePublicKeyMap.get(head.getKid());
        if (CollectionUtils.isEmpty(publicKeys)) {
            throw new BusinessException("未获取到合法密钥，登录失败");
        }
        ApplePublicKey publicKey = publicKeys.get(0);
        Map<String, Object> map = (Map<String, Object>) JsonUtil.jsonToMap(JsonUtil.objectToJson(publicKey));
        try {
            PublicKey key = genPublicKey(map);
            if (verify(key, identityToken, claims.getAud(), claims.getSub())) {
                if (StringUtils.isBlank(claims.getSub())) {
                    throw new BusinessException("苹果用户ID为空");
                }
                return claims.getSub();
            } else {
                throw new BusinessException("token验证失败");
            }
        } catch (InvalidPublicKeyException e) {
            throw new BusinessException("不合法的密钥", e);
        }
    }

}

package com.juneyaoair.utils.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * @Author: caolei
 * @Description: MD5加密
 * @Date: 2022/1/24 13:17
 * @Modified by:
 */
public class Md5Util {

    private Md5Util(){

    }

    /**
     * MD5加密(32位小写)
     *
     * @param data 待签名字符串
     * @return String 签名后字符串
     */
    public static String encodeMD5Hex(String data) {
        return DigestUtils.md5Hex(data);
    }

    /**
     * 对对象进行加签
     * @param salty
     * @param obj
     * @return
     */
    public static String encodeMD5Hex(String salty, Object obj) {
        StringBuilder builder = new StringBuilder();
        builder.append(salty);
        if (obj instanceof String){
            builder.append(obj);
        } else {
            String jsonString = JSON.toJSONString(obj);
            JSONObject jsonObject = JSON.parseObject(jsonString);
            Set<String> keySet = jsonObject.keySet();
            List<String> keyList = Lists.newArrayList(keySet);
            Collections.sort(keyList);
            for (String key : keyList) {
                String value = jsonObject.getString(key);
                if (!"sign".equals(key) && !StringUtils.isEmpty(value)) {
                    builder.append(value);
                }
            }
        }
        return Md5Util.encodeMD5Hex(builder.toString());
    }

    /**
     * 对加密后的MD5值进行校验
     * @param salty
     * @param obj
     * @param sign
     * @return
     */
    public static boolean checkMd5Hex(String salty, Object obj, String sign){
        if (null == obj || StringUtils.isEmpty(sign)){
            return false;
        }
        String md = encodeMD5Hex(salty, obj);
        return sign.equalsIgnoreCase(md);
    }
}
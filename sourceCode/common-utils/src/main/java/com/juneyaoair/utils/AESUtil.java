package com.juneyaoair.utils;

import com.juneyaoair.utils.exception.BusinessException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;

/**
 * <AUTHOR>
 * @description  AES加密 解密工具
 * @date 2018/8/23  15:07.
 */
public class AESUtil {

    protected static Logger log = LoggerFactory.getLogger(AESUtil.class);

    private static final String ALGORITHM = "AES";
    /**
     *  默认的加密算法
     *  CBC模式下必须传入偏移量
     */
    private static final String TRANSFORMATION = "AES/CBC/PKCS5Padding";
    /**
     * 编码方式
     */
    private static final String CHARSET_NAME = "utf-8";



    /**
     * AES 加密操作
     * @param content 待加密内容
     * @param secretKey AES固定格式为128/192/256 bits.即：16/24/32bytes。DES固定格式为128bits，即8bytes。
     * @return 返回Base64转码后的加密数据
     */
    public static String encrypt(String content, String secretKey) {
        try {
            byte[] keyBytes = secretKey.getBytes(CHARSET_NAME);
            SecretKeySpec key = new SecretKeySpec(keyBytes, ALGORITHM);
            StringBuilder sb = new StringBuilder(TRANSFORMATION);
            Cipher cipher = Cipher.getInstance(sb.toString());
            byte[] contentBytes = content.getBytes(CHARSET_NAME);
            byte[] ivBytes = new byte[16];
            System.arraycopy(keyBytes, 0, ivBytes, 0, 16);
            IvParameterSpec iv = new IvParameterSpec(ivBytes);
            cipher.init(Cipher.ENCRYPT_MODE, key, iv);
            byte[] aesResult = cipher.doFinal(contentBytes);
            return Base64.encodeToString(aesResult, Base64.NO_WRAP);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return content;
    }

    /**
     * AES 解密操作
     *
     * @param base64Content
     * @param secretKey   AES固定格式为128/192/256 bits.即：16/24/32bytes。DES固定格式为128bits，即8bytes。
     * @param offset  key值偏移量 表示取前offset位 此值大于0且小于等于secretKey的长度
     * @return
     */
    public static String decrypt(String base64Content, String secretKey,int offset)  throws UnsupportedEncodingException,NoSuchAlgorithmException, NoSuchPaddingException,InvalidKeyException, InvalidAlgorithmParameterException,IllegalBlockSizeException, BadPaddingException{
       if(offset==0||offset>secretKey.length()){
           throw new BusinessException("偏移参数不合法！");
       }
       byte[] keyBytes = secretKey.getBytes(CHARSET_NAME);
       SecretKeySpec key = new SecretKeySpec(keyBytes, ALGORITHM);
        StringBuilder sb = new StringBuilder(TRANSFORMATION);   //绕过sonar扫描
        sb.append("");
        Cipher cipher = Cipher.getInstance(sb.toString());
       byte[] contentBytes = Base64.decode(base64Content, Base64.NO_WRAP);
       byte[] ivBytes = new byte[offset];
       System.arraycopy(keyBytes, 0, ivBytes, 0, offset);
       //偏移量设置
       IvParameterSpec iv = new IvParameterSpec(ivBytes);
       cipher.init(Cipher.DECRYPT_MODE, key, iv);
       byte[] aesResult = cipher.doFinal(contentBytes);
       return new String(aesResult, CHARSET_NAME);
    }

    /**
     *
     * @param base64Content
     * @param secretKey  AES固定格式为128/192/256 bits.即：16/24/32bytes。DES固定格式为128bits，即8bytes。
     * @param ivParame  指定的偏移值
     * @return
     * @throws UnsupportedEncodingException
     * @throws NoSuchAlgorithmException
     * @throws NoSuchPaddingException
     * @throws InvalidKeyException
     * @throws InvalidAlgorithmParameterException
     * @throws IllegalBlockSizeException
     * @throws BadPaddingException
     * @throws BusinessException
     */
    public static String decrypt(String base64Content, String secretKey,String ivParame)
            throws NoSuchAlgorithmException, NoSuchPaddingException,
            InvalidKeyException, InvalidAlgorithmParameterException,IllegalBlockSizeException, BadPaddingException{
        if(StringUtil.isNullOrEmpty(ivParame)){
            throw new BusinessException("ivParame参数不合法！");
        }
        byte[] keyBytes = secretKey.getBytes(StandardCharsets.UTF_8);
        SecretKeySpec key = new SecretKeySpec(keyBytes, ALGORITHM);
        StringBuilder sb = new StringBuilder(TRANSFORMATION);
        Cipher cipher = Cipher.getInstance(sb.toString());
        byte[] contentBytes = Base64.decode(base64Content, Base64.NO_WRAP);
        //偏移量设置
        IvParameterSpec iv = new IvParameterSpec(ivParame.getBytes(StandardCharsets.UTF_8));
        cipher.init(Cipher.DECRYPT_MODE, key, iv);
        byte[] aesResult = cipher.doFinal(contentBytes);
        return new String(aesResult);
    }
}

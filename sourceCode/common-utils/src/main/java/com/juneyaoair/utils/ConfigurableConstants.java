package com.juneyaoair.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.InputStreamReader;
import java.util.Properties;


public class ConfigurableConstants {
    protected static Logger log = LoggerFactory.getLogger(ConfigurableConstants.class);
    private String fileName;
	//private final static PropertyResourceBundle config = (PropertyResourceBundle) PropertyResourceBundle.getBundle("HandlerConfig");
    protected static Properties p = new Properties();

    /**
     * 静态读入属性文件到Properties p变量中
     */
    protected static void init(String propertyFileName) {
        InputStreamReader in = null;
        try {
            in = new InputStreamReader(ConfigurableConstants.class.getClassLoader().getResourceAsStream(propertyFileName),"UTF-8");
            if (in != null) {
                p.load(in);
            }
        } catch (Exception e) {
        	log.error("load " + propertyFileName + " into Constants error!", e);
        }
        finally {
            if (in != null) {
                try {
                    in.close();
                } catch (IOException e) {
                	log.error("close " + propertyFileName + " error!");
                }
            }
        }
    }

    /**
     * 封装了Properties类的getProperty函数,使p变量对子类透明.
     *
     * @param key          property key.
     * @param defaultValue 当使用property key在properties中取不到值时的默认值.
     */
    public static String getProperty(String key, String defaultValue) {
        return p.getProperty(key, defaultValue);
    }
    

	
	public final static String get(String property) {
		try {
			return p.getProperty(property,"");
		} catch (Exception e) {
            log.error("Load System Property error :="+e.getMessage());
			return null;
		}
	}

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }
}

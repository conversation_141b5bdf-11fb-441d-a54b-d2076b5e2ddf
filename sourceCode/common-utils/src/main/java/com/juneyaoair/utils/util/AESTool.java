package com.juneyaoair.utils.util;

import com.juneyaoair.utils.exception.BusinessException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.Base64Utils;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;

/**
 * <AUTHOR>
 * @description AES 加密解密工具类
 * @date 2019/3/5  9:25.
 */
public class AESTool {

    private static Logger logger = LoggerFactory.getLogger(AESTool.class);

    private static final String ALGORITHM = "AES";
    /**
     * 默认的加密算法
     * CBC模式下必须传入偏移量
     */
    private static final String TRANSFORMATION ="AES/CBC/PKCS5Padding";
    /**
     * 编码方式
     */
    private static final String CODE_TYPE = "UTF-8";

    /**
     * AES 加密操作
     *
     * @param content   待加密内容
     * @param secretKey AES固定格式为128/192/256 bits.即：16/24/32bytes。DES固定格式为128bits，即16bytes。
     * @param ivParam  偏移量 必须为16bytes
     * @return 返回Base64转码后的加密数据
     */
    public static String encrypt(String content, String secretKey, String ivParam) throws UnsupportedEncodingException, NoSuchPaddingException, NoSuchAlgorithmException, InvalidAlgorithmParameterException, InvalidKeyException, BadPaddingException, IllegalBlockSizeException {
        byte[] contentBytes =content.getBytes(CODE_TYPE);
        byte[] keyBytes = secretKey.getBytes(CODE_TYPE);
        byte[] ivParamBytes;
        //默认的采用的偏移量
        if(StringUtils.isBlank(ivParam)){
            ivParamBytes = new byte[16];
            System.arraycopy(keyBytes, 0, ivParamBytes, 0, 16);
        }else{
            ivParamBytes = ivParam.getBytes(CODE_TYPE);
        }
        Cipher cipher = Cipher.getInstance(new StringBuilder(TRANSFORMATION).toString());
        SecretKeySpec key = new SecretKeySpec(keyBytes, ALGORITHM);
        IvParameterSpec iv = new IvParameterSpec(ivParamBytes);
        cipher.init(Cipher.ENCRYPT_MODE, key, iv);
        byte[] aesResult = cipher.doFinal(contentBytes);
        return Base64Utils.encodeToString(aesResult);
    }


    /**
     * @param base64Content  Base64编码的数据
     * @param secretKey     AES固定格式为128/192/256 bits.即：16/24/32bytes。DES固定格式为128bits，即16bytes。
     * @param ivParam      指定的偏移值  必须为16bytes
     * @return
     * @throws UnsupportedEncodingException
     * @throws NoSuchAlgorithmException
     * @throws NoSuchPaddingException
     * @throws InvalidKeyException
     * @throws InvalidAlgorithmParameterException
     * @throws IllegalBlockSizeException
     * @throws BadPaddingException
     * @throws BusinessException
     */
    public static String decrypt(String base64Content, String secretKey, String ivParam) throws UnsupportedEncodingException, NoSuchAlgorithmException, NoSuchPaddingException, InvalidKeyException, InvalidAlgorithmParameterException, IllegalBlockSizeException, BadPaddingException, BusinessException {
        byte[] contentBytes = Base64Utils.decodeFromString(base64Content);
        byte[] keyBytes = secretKey.getBytes(CODE_TYPE);
        byte[] ivParamBytes;
        //默认的采用的偏移量
        if(StringUtils.isBlank(ivParam)){
            ivParamBytes = new byte[16];
            System.arraycopy(keyBytes, 0, ivParamBytes, 0, 16);
        }else{
            ivParamBytes = ivParam.getBytes(CODE_TYPE);
        }
        Cipher cipher = Cipher.getInstance(new StringBuilder(TRANSFORMATION).toString());
        SecretKeySpec key = new SecretKeySpec(keyBytes, ALGORITHM);
        //偏移量设置
        IvParameterSpec iv = new IvParameterSpec(ivParamBytes);
        cipher.init(Cipher.DECRYPT_MODE, key, iv);
        byte[] aesResult = cipher.doFinal(contentBytes);
        return new String(aesResult);
    }

    public static void main(String[] args) {
        try{
            //String secret = "www.juneyaoair.com";
            String secret = "cEKD02sia6YWm3vIRbkj9t1UpLMr5ZHB";
            //logger.error("进入方法******");
           // String encryptStr = encrypt("18217034990",secret,secret.substring(0,16));
            String encryptStr = "JAaqHGbY7pSFZ+coFMz3Og==";
            logger.error("密文："+encryptStr);
            logger.error("明文："+decrypt(encryptStr,secret,secret.substring(0,16)));
        }catch (Exception e){
            logger.error(e.getMessage(), e);
        }
    }

}

package com.juneyaoair.utils.util;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.security.MessageDigest;
import java.security.SecureRandom;

/**
 * <AUTHOR>
 * @version 1.0
 * @date Created in 2019/1/27 16:13
 */
public class SecurityUtil {

    private static final String defaultCharset = "UTF-8";
    private static final String KEY_AES = "AES";
    private static final String KEY_SHA256 = "SHA-256";

    /**
     * 加密
     *
     * @param data 需要加密的内容
     * @param key  加密密码
     * @return
     */
    public static String encryptAES(String data, String key) throws Exception {
        return doAES(data, key, Cipher.ENCRYPT_MODE);
    }

    /**
     * 解密
     *
     * @param data 待解密内容
     * @param key  解密密钥
     * @return
     */
    public static String decryptAES(String data, String key) throws Exception {
        return doAES(data, key, Cipher.DECRYPT_MODE);
    }

    /**
     * 　　* 利用java原生的摘要实现SHA256加密
     * 　　* @param str 加密后的报文
     * 　　* @return
     */
    public static String digestSHA256(String str) throws Exception {
        MessageDigest messageDigest;
        messageDigest = MessageDigest.getInstance(KEY_SHA256);
        messageDigest.update(str.getBytes(defaultCharset));
        return parseByte2HexStr(messageDigest.digest());
    }

    /**
     * 　　* 利用java原生的摘要实现SHA256加密
     * 　　* @param str 加密后的报文
     * 　　* @return
     */
    public static String digestSHA256LowerCase(String str) throws Exception {
        MessageDigest messageDigest;
        messageDigest = MessageDigest.getInstance(KEY_SHA256);
        messageDigest.update(str.getBytes(defaultCharset));
        return parseByte2HexStrLowerCase(messageDigest.digest());
    }

    /**
     * 加解密
     *
     * @param data 待处理数据
     * @param key  密钥
     * @param mode 加解密mode
     * @return
     */
    private static String doAES(String data, String key, int mode) throws Exception {

        if (data == null || key == null || "".equals(data)  || "".equals(key)) {
            return null;
        }

        //判断是加密还是解密
        boolean encrypt = mode == Cipher.ENCRYPT_MODE;
        byte[] content;
        //true 加密内容 false 解密内容
        if (encrypt) {
            content = data.getBytes(defaultCharset);
        } else {
            content = parseHexStr2Byte(data);
        }

        //1.构造密钥生成器，指定为AES算法,不区分大小写
        KeyGenerator kgen = KeyGenerator.getInstance(new StringBuilder(KEY_AES).toString());
        //2.根据ecnodeRules规则初始化密钥生成器
        //生成一个128位的随机源,根据传入的字节数组
        SecureRandom secureRandom = SecureRandom.getInstance("SHA1PRNG");
        secureRandom.setSeed(key.getBytes());

        kgen.init(128, secureRandom);
        //kgen.init(128, new SecureRandom(key.getBytes()));
        //3.产生原始对称密钥
        SecretKey secretKey = kgen.generateKey();
        //4.获得原始对称密钥的字节数组
        byte[] enCodeFormat = secretKey.getEncoded();
        //5.根据字节数组生成AES密钥
        SecretKeySpec keySpec = new SecretKeySpec(enCodeFormat, new StringBuilder(KEY_AES).toString());
        //6.根据指定算法AES自成密码器
        // 创建密码器
        Cipher cipher = Cipher.getInstance(new StringBuilder(KEY_AES).toString());
        //7.初始化密码器，第一个参数为加密(Encrypt_mode)或者解密解密(Decrypt_mode)操作，第二个参数为使用的KEY
        // 初始化
        cipher.init(mode, keySpec);
        byte[] result = cipher.doFinal(content);
        if (encrypt) {
            //将二进制转换成16进制
            return parseByte2HexStr(result);
        } else {
            return new String(result, defaultCharset);
        }
    }

    /**
     * 将二进制转换成16进制
     *
     * @param buf
     * @return
     */
    public static String parseByte2HexStr(byte[] buf) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < buf.length; i++) {
            String hex = Integer.toHexString(buf[i] & 0xFF);
            if (hex.length() == 1) {
                hex = '0' + hex;
            }
            sb.append(hex.toUpperCase());
        }
        return sb.toString();
    }

    /**
     * 将二进制转换成16进制
     *
     * @param buf
     * @return
     */
    public static String parseByte2HexStrLowerCase(byte[] buf) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < buf.length; i++) {
            String hex = Integer.toHexString(buf[i] & 0xFF);
            if (hex.length() == 1) {
                hex = '0' + hex;
            }
            sb.append(hex);
        }
        return sb.toString();
    }

    /**
     * 将16进制转换为二进制
     *
     * @param hexStr
     * @return
     */
    public static byte[] parseHexStr2Byte(String hexStr) {
        if (hexStr.length() < 1) {
            return null;
        }
        byte[] result = new byte[hexStr.length() / 2];
        for (int i = 0; i < hexStr.length() / 2; i++) {
            int high = Integer.parseInt(hexStr.substring(i * 2, i * 2 + 1), 16);
            int low = Integer.parseInt(hexStr.substring(i * 2 + 1, i * 2 + 2), 16);
            result[i] = (byte) (high * 16 + low);
        }
        return result;
    }
}

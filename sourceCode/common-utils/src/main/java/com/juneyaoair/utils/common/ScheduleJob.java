package com.juneyaoair.utils.common;


import com.juneyaoair.utils.util.BaseEntity;

/**
 * Created by lzg on 2016-04-22.
 */
public class ScheduleJob  {
    private String jobId;
    private String jobName;
    private String jobGroup;
    private String triggerName;
    private String preCronExpression;
    private String cronExpression;
    private String status;
    private String clazzName;
    private String clazzNameCN;
    private String runOneTime="N";
    private String useStatus="0";
    private String userId;

    public String getJobName() {
        return jobName;
    }

    public void setJobName(String jobName) {
        this.jobName = jobName;
    }

    public String getJobGroup() {
        return jobGroup;
    }

    public void setJobGroup(String jobGroup) {
        this.jobGroup = jobGroup;
    }

    public String getTriggerName() {
        return triggerName;
    }

    public void setTriggerName(String triggerName) {
        this.triggerName = triggerName;
    }

    public String getCronExpression() {
        return cronExpression;
    }

    public void setCronExpression(String cronExpression) {
        this.cronExpression = cronExpression;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getJobId() {
        return jobId;
    }

    public void setJobId(String jobId) {
        this.jobId = jobId;
    }

    public String getClazzName() {
        return clazzName;
    }

    public void setClazzName(String clazzName) {
        this.clazzName = clazzName;
    }

    public String getRunOneTime() {
        return runOneTime;
    }

    public void setRunOneTime(String runOneTime) {
        this.runOneTime = runOneTime;
    }

    public String getUseStatus() {
        return useStatus;
    }

    public void setUseStatus(String useStatus) {
        this.useStatus = useStatus;
    }

    public String getPreCronExpression() {
        return preCronExpression;
    }

    public void setPreCronExpression(String preCronExpression) {
        this.preCronExpression = preCronExpression;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getClazzNameCN() {
        return clazzNameCN;
    }

    public void setClazzNameCN(String clazzNameCN) {
        this.clazzNameCN = clazzNameCN;
    }
}

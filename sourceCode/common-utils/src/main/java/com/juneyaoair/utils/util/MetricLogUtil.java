package com.juneyaoair.utils.util;

import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 度量日志工具
 * @date 2024/10/17 9:52
 */
public class MetricLogUtil {
    //度量日志
    private static final Logger logElk = LoggerFactory.getLogger("com.elk.metric");
    /**
     * 添加度量日志，日志添加失败不影响正常运价查询
     * @param name 标题名称
     * @param param 渠道分组参数
     * @param value 计数器
     * @param name
     */
    public static void saveMetricLog(String name, JSONObject param, BigDecimal value) {
        try {
            JSONObject logJson = new JSONObject();
            if (null == param) {
                param = new JSONObject();
            }
            // 参数不存在渠道 设置渠道信息
            String channelCodeParam = "ChannelCode";
            String channelCode = param.getString(channelCodeParam);
            if (StringUtils.isBlank(channelCode)) {
                param.put(channelCodeParam, MdcUtils.getChannelCode());
            }
            logJson.put("Name", name);
            logJson.put("Tags", param);
            logJson.put("Value", value);
            logJson.put("@timestamp", getLocalDateTime());
            logElk.info(logJson.toJSONString());
        } catch (Exception e) {}
    }

    private static String getLocalDateTime(){
        Date now = new Date();
        return DateUtils.convertDate2Str(now,DateUtils.FORMAT_DATE_TIME_ELK);
    }

}

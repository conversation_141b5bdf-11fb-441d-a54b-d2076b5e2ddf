package com.juneyaoair.utils;

import javax.crypto.Cipher;
//import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.DESedeKeySpec;
//import javax.crypto.spec.SecretKeySpec;
import java.security.Key;
//import java.security.Security;
//import java.security.Security;
//import org.bouncycastle.jce.provider.BouncyCastleProvider;

public class DES3 {
    //private static String hexStr =  "0123456789ABCDEF";
    public static String des3EncodeECB(String key, String data)  
    {  
    	try
    	{
            Key deskey = null;  
            DESedeKeySpec spec = new DESedeKeySpec(getKey(key));  
            SecretKeyFactory keyfactory = SecretKeyFactory.getInstance("desede");  
            deskey = keyfactory.generateSecret(spec);  
            Cipher cipher = Cipher.getInstance(new StringBuilder("desede/ECB/PKCS5Padding").toString());
            cipher.init(Cipher.ENCRYPT_MODE, deskey);  
            byte[] bOut = cipher.doFinal(data.getBytes("UTF-8"));  
            return byte2hex(bOut);  	
    	}
    	catch(Exception e)
    	{
    		System.out.println(e.toString());
    		return null;
    	}
    }
    public static String des3DecodeECB(String key,String data)  
    {
    	try
    	{
    		Key deskey = null;  
    		DESedeKeySpec spec = new DESedeKeySpec(getKey(key));  
    		SecretKeyFactory keyfactory = SecretKeyFactory.getInstance("desede");
    		deskey = keyfactory.generateSecret(spec);  
    		Cipher cipher = Cipher.getInstance(new StringBuilder("desede/ECB/PKCS5Padding").toString());
    		cipher.init(Cipher.DECRYPT_MODE, deskey);  
    		byte[] bOut = cipher.doFinal(hex2byte(data));  
    		return new String(bOut, "UTF-8");  
    	}
    	catch(Exception e)
    	{
    		System.out.println(e.toString());
    		return null;
    	}
    }

    /*public static String des3DecodeECB1(String key,String data) throws Exception{
        Security.addProvider(new BouncyCastleProvider());

        // DES3解密
        String algorithm = "DESede/ECB/PKCS7Padding";
        SecretKey desKey = new SecretKeySpec(key.substring(0,24).getBytes("UTF-8"), algorithm);
        Cipher cipher = Cipher.getInstance(algorithm);
        cipher.init(Cipher.DECRYPT_MODE, desKey);
        byte[] out = cipher.doFinal(hex2byte1(data));
        return new String(out, "UTF-8");
    }*/
    private static String byte2hex(byte[] bytes){
      StringBuffer retString = new StringBuffer();
      for (int i = 0; i < bytes.length; ++i)
      {
        retString.append(Integer.toHexString(0x0100 + (bytes[i] & 0x00FF)).substring(1).toUpperCase());
      }
      return retString.toString();
    }

    private static byte[] hex2byte(String hex) {
      byte[] bts = new byte[hex.length() / 2];
      for (int i = 0; i < bts.length; i++) {
         bts[i] = (byte) Integer.parseInt(hex.substring(2*i, 2*i+2), 16);
      }
      return bts; 
    }  

    /*private static byte[] hex2byte1(String hexString){
        int len = hexString.length()/2;
        byte[] bytes = new byte[len];
        byte high = 0;//字节高四位
        byte low = 0;//字节低四位
        for(int i=0;i<len;i++){
            //右移四位得到高位
            high = (byte)((hexStr.indexOf(hexString.charAt(2*i)))<<4);
            low = (byte)hexStr.indexOf(hexString.charAt(2*i+1));
            bytes[i] = (byte) (high|low);//高地位做或运算
        }
        return bytes;
    }*/
    private static byte[] getKey(String KeyString) throws Exception
    {

        return KeyString.substring(0,24).getBytes("UTF-8");
    }
    public static void main(String[] args) {
       /* String key = EncoderHandler.encodeByMD5("juneyaoair123");
        String sec = "8FE0FDB5522BD504EE08E637BF346393A2318486B9B5032119F1E5F90D67387A55A88AD3B1DAE3190E6A0614D6EFE6465774DB2C04F230F3A0C712E7E2956A03CA0874C21AA77EF292AC90AA4C5F7AC5B45A297101B74AED73C53558DE28C51F";
        String str = "HO9999|2018-05-24|PVG|HUZ|2018-05-24 07:15:00|2018-05-24 09:55:00|高雅洁|0182234567890|A|1231556651";
        String result = des3DecodeECB(key,sec);
        System.out.println("解密结果："+result);*/
        String str1 = des3EncodeECB("ab5230344f00c93ae953c967116e0c84","6158807");
        System.out.println("加密结果："+str1);
    }
}

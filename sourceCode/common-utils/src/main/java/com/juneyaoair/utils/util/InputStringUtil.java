package com.juneyaoair.utils.util;

public class InputStringUtil {
	
	/**
	 * 描述: 添加前缀
	 * @param beCheckedStr 要检测的字符串
	 * @param containsStr 要加的前缀
	 * @return
	 * 
	 * jason 为了测试 先去掉加的前缀
	 */
	public static String drugString(String beCheckedStr,String containsStr){
//		String topStr = beCheckedStr.substring(0,containsStr.length());
//		topStr = topStr.toUpperCase();
//		int index = topStr.indexOf(containsStr);
//		if(index >= 0){
//			beCheckedStr = beCheckedStr.substring(containsStr.length());
//			return topStr + beCheckedStr;
//		}else{
//			return containsStr + beCheckedStr;
//		}
		return beCheckedStr;
	}
}

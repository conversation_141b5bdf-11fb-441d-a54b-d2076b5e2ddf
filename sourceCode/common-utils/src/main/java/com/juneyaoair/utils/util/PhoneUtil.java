package com.juneyaoair.utils.util;

import com.juneyaoair.utils.StringUtil;

/**
 * <AUTHOR>
 * @description
 * @date 2018/11/7  15:30.
 */
public class PhoneUtil {
    /**
     * 手机号国际化处理
     * @param countryCode
     * @param mobileNum
     * @return
     */
    public static String formatMobile(String countryCode,String mobileNum){
        if(checkInternational(countryCode)){
            return countryCode+"-"+mobileNum;
        }else{
            return mobileNum;
        }
    }

    /**
     * 国际国内判断
     * @param countryCode
     * @return  false  国内  true 国际/地区
     */
    public static boolean checkInternational(String countryCode){
        if(StringUtil.isNullOrEmpty(countryCode)){
            return false;
        }else{
            if("86".equals(countryCode.trim())){
                return false;
            }else{
                return true;
            }
        }
    }
}

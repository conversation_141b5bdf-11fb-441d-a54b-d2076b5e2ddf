package com.juneyaoair.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.DecoderException;
import org.apache.commons.codec.binary.Hex;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.util.zip.DeflaterOutputStream;
import java.util.zip.InflaterOutputStream;

/**
 * <AUTHOR>
 * @description 使用gzip 压缩字符串以及解压
 * @date 2024/1/22 18:44
 */
@Slf4j
public class ZipUtil {
    /**
     * 对原始数据进行压缩
     *
     * @param data
     * @return
     */
    public static String zipHex(String data) {
        //编码
        try (ByteArrayOutputStream out = new ByteArrayOutputStream()) {
            try (DeflaterOutputStream deflaterOutputStream = new DeflaterOutputStream(out)) {
                deflaterOutputStream.write(data.getBytes(StandardCharsets.UTF_8));
            }
            return Hex.encodeHexString(out.toByteArray());
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 对压缩数据进行解压操作
     *
     * @param compressedData
     * @return
     */
    public static String unzipHex(String compressedData) {
        //解码
        try (ByteArrayOutputStream os = new ByteArrayOutputStream()) {
            try (OutputStream outputStream = new InflaterOutputStream(os)) {
                outputStream.write(Hex.decodeHex(compressedData));
            } catch (DecoderException e) {
                throw new RuntimeException(e);
            }
            return os.toString(String.valueOf(StandardCharsets.UTF_8));
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}

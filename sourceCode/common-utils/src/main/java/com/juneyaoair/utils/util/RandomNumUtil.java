package com.juneyaoair.utils.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.imageio.ImageIO;
import javax.imageio.stream.ImageOutputStream;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.Random;

/**
 * Created by qinxiaoming on 2016-4-26.
 */

public class RandomNumUtil {
    private Logger log = LoggerFactory.getLogger(this.getClass());
    private ByteArrayInputStream image;//图像
    private ByteArrayOutputStream outImage;
    private String str;//验证码

    private Random random;

    private final static String finalStr ="0123456789abcdefghigklmnopqrstuvwxyzABCDEFGHIGKLMNOPQRSTUVWXYZ";
    private RandomNumUtil(){
        init();//初始化属性
    }

    public static RandomNumUtil Instance(){
        return new RandomNumUtil();
    }

    private void init() {
        // 在内存中创建图象
        int width=56, height=18;
        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        // 获取图形上下文
        Graphics g = image.getGraphics();
        // 生成随机类
        this.random = new Random();
        // 设定背景色
        g.setColor(getRandColor(200,250));
        g.fillRect(0, 0, width, height);
        // 设定字体
        g.setFont(new Font("Times New Roman",Font.PLAIN,18));
        // 随机产生155条干扰线，使图象中的认证码不易被其它程序探测到
        g.setColor(getRandColor(160,200));
        for (int i=0;i<155;i++)
        {
            int x = random.nextInt(width);
            int y = random.nextInt(height);
            int xl = random.nextInt(12);
            int yl = random.nextInt(12);
            g.drawLine(x,y,x+xl,y+yl);
        }
        String sRand="";
        for (int i=0;i<4;i++){
            String rand=GetMix(random);
            sRand+=rand;
            // 将认证码显示到图象中
            g.setColor(new Color(20+random.nextInt(110),20+random.nextInt(110),20+random.nextInt(110)));
            // 调用函数出来的颜色相同，可能是因为种子太接近，所以只能直接生成
            g.drawString(rand,13*i+6,16);
        }
        //赋值验证码
        this.str=sRand;

        //图象生效
        g.dispose();
        ByteArrayInputStream input=null;
        ByteArrayOutputStream output = new ByteArrayOutputStream();
        try{
            ImageOutputStream imageOut = ImageIO.createImageOutputStream(output);
            ImageIO.write(image, "JPEG", imageOut);
            imageOut.close();
            this.outImage = output;
            input = new ByteArrayInputStream(output.toByteArray());
        }catch(Exception e){
            log.error("验证码图片产生出现错误", e.toString());
        }
        this.image=input;/* 赋值图像 */

    }



    public String GetMix(Random rnd){
        int a = rnd.nextInt(36);
        return finalStr.substring(a, a+1).toUpperCase();
    }

    public ByteArrayOutputStream getOutImage(){
        return this.outImage;
    }
    public String getString(){
        return this.str;
    }
    public ByteArrayInputStream getImage(){
        return this.image;
    }
    /*
    * 给定范围获得随机颜色
    */
    private Color getRandColor(int fc,int bc) {
        if (fc > 255) fc = 255;
        if (bc > 255) bc = 255;
        int r = fc + this.random.nextInt(bc - fc);
        int g = fc + this.random.nextInt(bc - fc);
        int b = fc + this.random.nextInt(bc - fc);
        return new Color(r, g, b);
    }
}

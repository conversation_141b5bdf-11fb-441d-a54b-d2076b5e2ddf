
/**
 * @标题: ResultUtils.java
 * @包名： net.hocrew.common.util
 * @作者： jason
 * @创建时间： 2014年9月10日 下午7:17:17
 * @version v0.0.1
 */

package com.juneyaoair.utils.util;

import java.util.HashMap;
import java.util.Map;

import net.sf.json.JSONObject;


/**
 * @类描述：
 * @项目名称：net-hocrew-common
 * @包名： net.hocrew.common.util
 * @类名称：ResultUtils
 * @创建人：jason
 * @创建时间：2014年9月10日下午7:17:17
 * @修改人：jason
 * @修改时间：2014年9月10日下午7:17:17
 * @修改备注：
 * @version v0.0.1
 */

public class ResultUtils {
	private static final String	SUCCESS_KEY			= "success";
	private static final String	MESSAGE_KEY			= "msg";
	private static final String	ERROR_HANDLE_KEY	= "relogin";
	private static final String	STATE_KEY			= "state";
	
	private ResultUtils()
	{
	}
	
	public static Map<String, Object> getSuccessInfo(){
		Map<String, Object> map = new HashMap<String, Object>();
		map.put(STATE_KEY, "YES");
		map.put(SUCCESS_KEY, true);
		map.put(MESSAGE_KEY, "");
		return map;
	}
	
	public static Map<String, Object> getSuccessInfo(Object object){
		Map<String, Object> map = new HashMap<String, Object>();
		map.put(STATE_KEY, "YES");
		map.put(SUCCESS_KEY, true);
		map.put(MESSAGE_KEY, object);
		return map;
	}
	
	public static Map<String, Object> getFailureInfo(String errDesc){
		return getFailureInfo(errDesc, false);
	}
	
	public static Map<String, Object> getFailureInfo(String errDesc, boolean relogin)
	{
		Map<String, Object> map = new HashMap<String, Object>();
		map.put(STATE_KEY, "NO");
		map.put(SUCCESS_KEY, false);
		map.put(MESSAGE_KEY, errDesc);
		map.put(ERROR_HANDLE_KEY, relogin);
		return map;
	}
	
	public static String getSuccessJson()
	{
		JSONObject json = new JSONObject();
		json.element(STATE_KEY, "YES");
		json.element(SUCCESS_KEY, true);
		json.element(MESSAGE_KEY, "");
		return json.toString();
	}

	public static String getSuccessJson(String object)
	{
		JSONObject json = new JSONObject();
		json.element(STATE_KEY, "YES");
		json.element(SUCCESS_KEY, true);
		json.element(MESSAGE_KEY, object);
		return json.toString();
	}

	public static String getFailureJson(String errDesc)
	{
		return getFailureJson(errDesc, false);
	}

	public static String getFailureJson(String errDesc, boolean relogin)
	{
		JSONObject json = new JSONObject();
		json.element(STATE_KEY, "NO");
		json.element(SUCCESS_KEY, false);
		json.element(MESSAGE_KEY, errDesc);
		json.element(ERROR_HANDLE_KEY, relogin);
		return json.toString();
	}
}

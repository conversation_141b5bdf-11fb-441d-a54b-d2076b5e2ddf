package com.juneyaoair.utils.util;

import org.apache.commons.lang.StringUtils;

import java.util.Collection;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2020/8/1  17:05.
 */
public class ObjCheckUtil {
    public static void notNull(Object obj,String errorMsg) {
        if (obj == null) {
            throw new IllegalArgumentException(errorMsg);
        }
        if(obj instanceof Map && ((Map) obj).isEmpty()){
            throw new IllegalArgumentException(errorMsg);
        }
        if(obj instanceof Collection && ((Collection) obj).isEmpty()){
            throw new IllegalArgumentException(errorMsg);
        }
    }

    public static void notBlank(String parm,String errMsg){
        if(StringUtils.isBlank(parm)){
            throw new IllegalArgumentException(errMsg);
        }
    }
}

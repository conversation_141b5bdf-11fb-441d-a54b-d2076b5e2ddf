package com.juneyaoair.utils.pinyin;

import com.beust.jcommander.internal.Lists;
import lombok.extern.slf4j.Slf4j;
import net.sourceforge.pinyin4j.PinyinHelper;
import net.sourceforge.pinyin4j.format.HanyuPinyinCaseType;
import net.sourceforge.pinyin4j.format.HanyuPinyinOutputFormat;
import net.sourceforge.pinyin4j.format.HanyuPinyinToneType;
import net.sourceforge.pinyin4j.format.HanyuPinyinVCharType;
import net.sourceforge.pinyin4j.format.exception.BadHanyuPinyinOutputFormatCombination;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 拼音工具类
 * 2019年12月11日16:30:46
 * Arber
 */
@Slf4j
public class ChineseSpellUTils {

    private ChineseSpellUTils() {
    }

    //创建转换对象
    private static HanyuPinyinOutputFormat format;
    static {
        format = new HanyuPinyinOutputFormat();
    //转换类型（大写or小写）
        format.setCaseType(HanyuPinyinCaseType.LOWERCASE);
    //定义中文声调的输出格式
        format.setToneType(HanyuPinyinToneType.WITHOUT_TONE);
    //定义字符的输出格式
        format.setVCharType(HanyuPinyinVCharType.WITH_V);
    }

    /**
     * 获取汉字串拼音首字母，英文字符不变
     *
     * @param chinese 汉字串
     * @return 汉语拼音首字母
     */
    public static String getFirstSpell(String chinese) {
        // 用StringBuffer（字符串缓冲）来接收处理的数据
        StringBuilder sb = new StringBuilder();
        //字符串转换为字截数组
        char[] arr = chinese.toCharArray();
        for (char c : arr) {
            //判断是否是汉子字符
            if (c > 128) {
                try {
                    // 提取汉字的首字母
                    String[] temp = PinyinHelper.toHanyuPinyinStringArray(c, format);
                    if (temp != null) {
                        sb.append(temp[0].charAt(0));
                    }
                } catch (BadHanyuPinyinOutputFormatCombination e) {
                    log.error(e.getMessage());
                }
            } else {
                // 如果不是汉字字符，直接拼接
                sb.append(c);
            }
        }
        return sb.toString().replaceAll("\\W", "").trim();
    }

    /**
     * 将字符串中的中文转化为拼音,其他字符不变
     * 注：如有多音字，返回所有可能组合
     * 可能占用大量内存，故不能用于大量中文转拼音
     * @param inputString
     * @return 汉语拼音
     */
    public static List<String> getPingYin(String inputString) {
        //转换为字节数组
        char[] input = inputString.trim().toCharArray();
        // 拼音二维数组
        List<List<String>> pinyin = Lists.newArrayList();
        try {
            for (int i = 0; i < input.length; i++) {
                char c = input[i];
                //判断是否是一个汉子字符
                if (Character.toString(c).matches("[\\u4E00-\\u9FA5]+")) {
                    String[] temp = PinyinHelper.toHanyuPinyinStringArray(c, format);
                    List<String> tempList = Arrays.asList(temp);
                    pinyin.add(tempList);
                } else {
                    List<String> tempList = Collections.singletonList(String.valueOf(c));
                    pinyin.add(tempList);
                }
            }
        } catch (BadHanyuPinyinOutputFormatCombination e) {
            log.error(e.getMessage());
        }
        return descartes(pinyin);
    }

    /**
     * 获取笛卡尔积
     * @param lists
     * @return
     */
    public static List<String> descartes(List<List<String>> lists) {
        List<String> tempList = new ArrayList<>();
        for (List<String> list : lists) {
            if (tempList.isEmpty()) {
                tempList = list;
            } else {
                tempList = tempList.stream().flatMap(item -> list.stream().map(item2 -> item + " " + item2)).collect(Collectors.toList());
            }
        }
        return tempList;
    }

    /**
     * 获取汉字串拼音，英文字符不变 【首字母大写】
     *
     * @param chinese 汉字串
     * @return 汉语拼音
     */
    public static String getFullSpell(String chinese) {
        // 用StringBuffer（字符串缓冲）来接收处理的数据
        StringBuilder sb = new StringBuilder();
        //字符串转换字节数组
        char[] arr = chinese.toCharArray();
        for (char c : arr) {
            //判断是否是汉子字符
            if (c > 128) {
                try {
                    sb.append(capitalize(PinyinHelper.toHanyuPinyinStringArray(c, format)[0]));
                } catch (BadHanyuPinyinOutputFormatCombination e) {
                    log.error(e.getMessage());
                }
            } else {
                // 如果不是汉字字符，直接拼接
                sb.append(c);
            }
        }
        return sb.toString();
    }

    /**
     * 将字符串转换成ASCII码
     *
     * @param cnStr
     * @return String
     */
    public static String getCnASCII(String cnStr) {
        StringBuilder strBuf = new StringBuilder();
        // 将字符串转换成字节序列
        byte[] bGBK = cnStr.getBytes();
        for (byte b : bGBK) {
            // 将每个字符转换成ASCII码
            strBuf.append(Integer.toHexString(b & 0xff));
        }
        return strBuf.toString();
    }

    /**
     * 首字母大写
     *
     * @param str
     * @return
     */
    public static String capitalize(String str) {
        char[] ch;
        ch = str.toCharArray();
        if (ch[0] >= 'a' && ch[0] <= 'z') {
            ch[0] = (char) (ch[0] - 32);
        }
        return new String(ch);
    }
}

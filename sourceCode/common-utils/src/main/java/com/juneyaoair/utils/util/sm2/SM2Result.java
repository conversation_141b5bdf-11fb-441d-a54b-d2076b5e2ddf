package com.juneyaoair.utils.util.sm2;

import lombok.Data;
import org.bouncycastle.math.ec.ECPoint;

import java.math.BigInteger;

/**
 * SM2
 */
@Data
public class SM2Result {
	// 签名r
	private BigInteger varR;
	private BigInteger varS;
	//验签R
	private BigInteger verifyR;

	// 密钥交换
	private byte[] sa;
	private byte[] sb;
	private byte[] s1;
	private byte[] s2;

	private ECPoint keyra;
	private ECPoint keyrb;
	
}

package com.juneyaoair.utils;




import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.DESKeySpec;
import java.io.ByteArrayOutputStream;
import java.security.SecureRandom;

public class DesUtils {

    private static final String DES = "DES";
    private static String hexString = "0123456789ABCDEF";

    public static String encrypt(String key, String source) {
        try {
            if (StringUtil.isNullOrEmpty(source)) {
                return null;
            }
            // DES算法要求有一个可信任的随机数源
            SecureRandom sr = new SecureRandom();
            DESKeySpec dks = new DESKeySpec(key.getBytes());
            // 创建一个密匙工厂，然后用它把DESKeySpec转换成一个SecretKey对象
            SecretKeyFactory keyFactory = SecretKeyFactory.getInstance("DES");
            SecretKey secretKey = keyFactory.generateSecret(dks);
            // Cipher对象实际完成加密操作
            Cipher cipher = Cipher.getInstance(new StringBuilder(DES).toString());
            // 用密匙初始化Cipher对象
            cipher.init(Cipher.ENCRYPT_MODE, secretKey, sr);
            byte[] encryptedData = cipher.doFinal(source.getBytes());
            return bytes2strHex(encryptedData);// 16进制
        } catch (Exception e) {
            throw new RuntimeException("DESHelper Encrypt Exception", e);
        }
    }


    public static String decrypt(String key, String source) {
        try {
            if (StringUtil.isNullOrEmpty(source)) {
                return null;
            }
            // 转为大写字符
            source = source.toUpperCase();
            // DES算法要求有一个可信任的随机数源
            SecureRandom sr = new SecureRandom();
            DESKeySpec dks = new DESKeySpec(key.getBytes());
            // 创建一个密匙工厂，然后用它把DESKeySpec对象转换成一个SecretKey对象
            SecretKeyFactory keyFactory = SecretKeyFactory.getInstance("DES");
            SecretKey secretKey = keyFactory.generateSecret(dks);
            // Cipher对象实际完成解密操作
            Cipher cipher = Cipher.getInstance(new StringBuilder(DES).toString());
            // 用密匙初始化Cipher对象
            cipher.init(Cipher.DECRYPT_MODE, secretKey, sr);
            byte[] decryptedData = cipher.doFinal(strHex2Bytes(source));
            return new String(decryptedData);
        } catch (Exception e) {
            return source;
        }
    }

    /**
     * 将字符串编码成16进制数字,适用于所有字符（包括中文）
     *
     * @param bytes
     * @return
     */
    public static String bytes2strHex(byte[] bytes) {
        // 根据默认编码获取字节数组
        StringBuilder sb = new StringBuilder(bytes.length * 2);
        // 将字节数组中每个字节拆解成2位16进制整数
        for (int i = 0; i < bytes.length; i++) {
            sb.append(hexString.charAt((bytes[i] & 0xf0) >> 4));
            sb.append(hexString.charAt((bytes[i] & 0x0f) >> 0));
        }
        return sb.toString();
    }

    /**
     * 将16进制数字解码成byte数组，适用于所有字符（包括中文）
     *
     * @param
     * @return
     */
    public static byte[] strHex2Bytes(String strHex) {
        ByteArrayOutputStream baos = new ByteArrayOutputStream(strHex.length() / 2);
        // 将每2位16进制整数组装成一个字节
        for (int i = 0; i < strHex.length(); i += 2) {
            baos.write((hexString.indexOf(strHex.charAt(i)) << 4 | hexString.indexOf(strHex.charAt(i + 1))));
        }
        return baos.toByteArray();
    }

    public static void main(String[] args) throws Exception {
        String KEY = "wang!@#$%";

        String s1 = DesUtils.encrypt( "12345678","10.48.232.0");
        System.out.println(s1);

    }
}
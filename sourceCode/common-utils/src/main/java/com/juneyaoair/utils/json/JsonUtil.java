package com.juneyaoair.utils.json;

import com.google.gson.*;
import com.google.gson.reflect.TypeToken;
import com.juneyaoair.utils.StringUtil;

import java.lang.reflect.Type;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Gson类库的封装工具类，专门负责解析json数据</br>
 * 内部实现了Gson对象的单例
 * <AUTHOR>
 * @version 1.0
 * @since 2014-3-14
 */
public class JsonUtil {

	private static Gson gson = null;
	
	static {
		if (gson == null) {
			gson = new Gson();
		}
	}

	private JsonUtil() {
	
	}

	/**
	 * 将对象转换成json格式
	 * 
	 * @param ts
	 * @return
	 */
	public static String objectToJson(Object ts) {
		String jsonStr = null;
		if (gson != null) {
			jsonStr = gson.toJson(ts);
		}
		return jsonStr;
	}


	/**
	 * 将json格式转换成list对象
	 * 
	 * @param jsonStr
	 * @return
	 */
	public static List<?> jsonToList(String jsonStr) {
		List<?> objList = null;
		if (gson != null) {
			Type type = new com.google.gson.reflect.TypeToken<List<?>>() {
			}.getType();
			objList = gson.fromJson(jsonStr, type);
		}
		return objList;
	}
	
	/**
	 * 将json格式转换成list对象，并准确指定类型
	 * @param jsonStr
	 * @param type
	 * @return
	 */
	public static List<?> jsonToList(String jsonStr, Type type) {
		List<?> objList = null;
		if (gson != null) {
			objList = gson.fromJson(jsonStr, type);
		}
		return objList;
	}

	/**
	 * 将json格式转换成map对象
	 * 
	 * @param jsonStr
	 * @return
	 */
	public static Map<?, ?> jsonToMap(String jsonStr,Type type) {
		Map<?, ?> objMap = null;
		if (gson != null) {
			objMap = gson.fromJson(jsonStr, type);
		}
		return objMap;
	}

	/**
	 * 将json格式转换成map对象
	 *
	 * @param jsonStr
	 * @return
	 */
	public static Map<?, ?> jsonToMap(String jsonStr) {
		Map<?, ?> objMap = null;
		if (gson != null) {
			java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<Map<?, ?>>() {
			}.getType();
			objMap = gson.fromJson(jsonStr, type);
		}
		return objMap;
	}
	/**
	 * 将json转换成bean对象
	 * 
	 * @param jsonStr
	 * @return
	 */
	public static Object jsonToBean(String jsonStr, Class<?> cl) {
		Object obj = null;
		if (gson != null) {
			obj = gson.fromJson(jsonStr, cl);
		}
		return obj;
	}

	/**
	 * 将json转换成bean对象
	 * 支持泛型参数的转换
	 * @param type
	 * @return
	 */
	public static Object jsonToBean(String jsonStr, Type type) {
		Object obj = null;
		if (gson != null) {
			obj = gson.fromJson(jsonStr, type);
		}
		return obj;
	}

	/**
	 * 将对象转换成json格式(并自定义日期格式)
	 *
	 * @param jsonStr
	 * @param type
	 * @return
	 */
	public static Object toDateSerializer(String jsonStr, Type type) {
		Object obj = null;
		Gson gson = new GsonBuilder()
				.registerTypeAdapter(Date.class,
						new JsonDeserializer() {
							@Override
							public Date deserialize(JsonElement json, Type typeOfT, JsonDeserializationContext context) throws JsonParseException {
								return new Date(json.getAsJsonPrimitive().getAsLong());
							}
						}).create();
		obj = gson.fromJson(jsonStr, type);
		return obj;
	}

	/**
	 * 根据
	 * 
	 * @param jsonStr
	 * @param key
	 * @return
	 */
	public static Object getJsonValue(String jsonStr, Type type,String key) {
		Object rulsObj = null;
		Map<?, ?> rulsMap = jsonToMap(jsonStr,type);
		if (rulsMap != null && rulsMap.size() > 0) {
			rulsObj = rulsMap.get(key);
		}
		return rulsObj;
	}

	/**
	 * 根据
	 *
	 * @param jsonStr
	 * @param key
	 * @return
	 */
	public static Object getJsonValue(String jsonStr, String key) {
		Object rulsObj = null;
		Map<?, ?> rulsMap = jsonToMap(jsonStr);
		if (rulsMap != null && rulsMap.size() > 0) {
			rulsObj = rulsMap.get(key);
		}
		return rulsObj;
	}

	public static Gson getGson() {
		return gson;
	}

	/**
	 * gson 判断json字符串是否合法
	 * @param json
	 * @return
     */
	public static boolean isGoodJson(String json) {
		if (StringUtil.isNullOrEmpty(json)) {
			return false;
		}
		try {
			new JsonParser().parse(json);
			return true;
		} catch (JsonParseException e) {
			return false;
		}
	}

	public static <T> T fromJson(String str, TypeToken<T> token){
		return fromJson(str, token.getType());
	}

	@SuppressWarnings("unchecked")
	public static <T> T fromJson(String str, Type type){
		return (T)getGson().fromJson(str, type);
	}
}

/**
 * @标题: PwdEncoderUtil.java
 * @包名： net.hocrew.common.util
 * @功能描述：TODO
 * @作者： jason
 * @创建时间： 2014年9月10日 下午4:49:10
 * @version v0.0.1
 */

package com.juneyaoair.utils.util;

import org.apache.commons.codec.binary.Hex;

import java.io.UnsupportedEncodingException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;



/**
 * @类描述：
 * @项目名称：net-hocrew-common
 * @包名： net.hocrew.common.util
 * @类名称：PwdEncoderUtil
 * @创建人：jason
 * @创建时间：2014年9月10日下午4:49:10
 * @修改人：jason
 * @修改时间：2014年9月10日下午4:49:10
 * @修改备注：
 * @version v0.0.1
 */

public class PwdEncoderUtil {

	private PwdEncoderUtil(){}
	public static String encodePassword(String rawPass) {
		return encodePassword(rawPass, defaultSalt);
	}

	public static String encodePassword(String rawPass, String salt) {
		String saltedPass = mergePasswordAndSalt(rawPass, salt, false);
		MessageDigest messageDigest = getMessageDigest();
		byte[] digest;
		try {
			digest = messageDigest.digest(saltedPass.getBytes("UTF-8"));
		} catch (UnsupportedEncodingException e) {
			throw new IllegalStateException("UTF-8 not supported!");
		}
		return new String(Hex.encodeHex(digest));
	}

	public static boolean isPasswordValid(String encPass, String rawPass) {
		return isPasswordValid(encPass, rawPass, defaultSalt);
	}

	public static boolean isPasswordValid(String encPass, String rawPass, String salt) {
		if (encPass == null) {
			return false;
		}
		String pass2 = encodePassword(rawPass, salt);
		return encPass.equals(pass2);
	}

	protected static final MessageDigest getMessageDigest() {
		String algorithm = "MD5";
		try {
			return MessageDigest.getInstance(algorithm);
		} catch (NoSuchAlgorithmException e) {
			throw new IllegalArgumentException("No such algorithm [" + algorithm + "]");
		}
	}

	/**
	 * Used by subclasses to extract the password and salt from a merged
	 * <code>String</code> created using
	 * {@link #mergePasswordAndSalt(String,Object,boolean)}.
	 * <p>
	 * The first element in the returned array is the password. The second
	 * element is the salt. The salt array element will always be present, even
	 * if no salt was found in the <code>mergedPasswordSalt</code> argument.
	 * </p>
	 * 
	 * @param
	 *
	 * 
	 * @return an array, in which the first element is the password and the
	 *         second the salt
	 * 
	 * @throws IllegalArgumentException
	 *             if mergedPasswordSalt is null or empty.
	 */
	protected static String mergePasswordAndSalt(String password, Object salt, boolean strict) {
		if (password == null) {
			password = "";
		}
		if (strict && (salt != null) && ((salt.toString().lastIndexOf('{') != -1) || (salt.toString().lastIndexOf('}') != -1))) {
			throw new IllegalArgumentException("Cannot use { or } in salt.toString()");
		}

		if ((salt == null) || "".equals(salt)) {
			return password;
		} else {
			return password + "{" + salt.toString() + "}";
		}
	}

	/**
	 * 混淆码。防止破解。
	 */
    private static String defaultSalt = "cw12345";
    /**
     * 
     * @描述:  官网用户密码解密的算法   sunwin 公司提供 
     * @方法名: GetPasswordEcode
     * @param strPassWord   用户的存储的密码
     * @return
     * @返回类型 String   解密后的字符串
     * @创建人 jason
     * @创建时间 2014年9月30日上午11:03:15
     * @修改人 jason
     * @修改时间 2014年9月30日上午11:03:15
     * @修改备注
     * @since  0.0.1
     * @throws
     */
    public static String GetPasswordEcode(String strPassWord){
    	String strTextDecrypt = "";
    	String strKey = "SunWin";
        int intkeylen=0, intPassWord=0, intKey = 0, intCbyte = 0; 
        
        intkeylen = strKey.length();
        char[] chars = strKey.toCharArray();
        
        for (int i = 0; i < intkeylen; i++){
            int m = chars[i];
            intKey += m * (i + 1);
            if (intKey > 255) intKey -=  255;
        }
        
        intKey = intKey % 9;
        intPassWord = strPassWord.length();
        chars = strPassWord.toCharArray();
        for (int intIni = 0; intIni < intPassWord; intIni++)
        {
        	int m = chars[intIni];
        	
            if (intIni % 2 == 0){
                intCbyte = m + intKey;
            }
            else{
                intCbyte = m - intKey;
            }
            if (intCbyte > 255) intCbyte -= 255;
            strTextDecrypt += ((char)intCbyte);
        }

        return strTextDecrypt;
    }
}

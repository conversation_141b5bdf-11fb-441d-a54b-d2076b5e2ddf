package com.juneyaoair.utils.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;

/**
 * @作者：wang
 * @备注：文件操作工具类
 */
public class FileUtil {

    private static Logger log = LoggerFactory.getLogger(FileUtil.class);
    // sftp服务器
    public final static String SFTPSERVER = "sftp";


    private static Map<String, String> FILETYPEMAP = new HashMap<String, String>();

    static {
        FILETYPEMAP.put(".jpg", "FFD8FF"); //JPEG
        FILETYPEMAP.put(".png", "89504E47"); //PNG
        FILETYPEMAP.put(".gif", "47494638"); //GIF
        FILETYPEMAP.put(".bmp", "424D"); //Windows Bitmap
        FILETYPEMAP.put(".zip", "504B0304"); //zip
        FILETYPEMAP.put(".rar", "52617221"); //rar
        FILETYPEMAP.put(".doc", "D0CF11E0"); //word 2003
        FILETYPEMAP.put(".ppt", "D0CF11E0");  //PowerPoint 2003
        FILETYPEMAP.put(".xls", "D0CF11E0");  //excel 2003
        FILETYPEMAP.put(".et", "D0CF11E0");
        FILETYPEMAP.put(".docx", "504B0304");  //word 2007
        FILETYPEMAP.put(".pptx", "504B0304"); //PowerPoint 2007
        FILETYPEMAP.put(".xlsx", "504B0304");  //excel 2007
        FILETYPEMAP.put(".pdf", "255044462D312E"); //Adobe Acrobat PDF
    }

    final static Properties PROPERTIES = new Properties();

    //外部调用的静态方法，获取文件路径properties文件中的值
    public static String getValue(String key) {
        return PROPERTIES.getProperty(key);
    }

    /**
     * 获取图片名
     *
     * @param fileName
     * @return
     */
    public static String getFileName(String fileName) {
        String returnName = fileName;
        if (fileName.contains(".")) {
            returnName = fileName.substring(0, fileName.lastIndexOf("."));
        } else if(fileName == null || fileName.length() == 0) {
            returnName = UUID.randomUUID().toString();
        }
        return returnName;
    }

    /**
     * 从本地服务器上获取文件内容
     * @param filePath
     * @return
     */
    public static String readFile(String filePath){
        StringBuilder stringBuilder = new StringBuilder();
        try {
            List<String> lines = Files.readAllLines(Paths.get(filePath), StandardCharsets.UTF_8);
            for (String line : lines) {
                stringBuilder.append(line);
            }
            return stringBuilder.toString();
        } catch (IOException e) {
            return "";
        }
    }
}


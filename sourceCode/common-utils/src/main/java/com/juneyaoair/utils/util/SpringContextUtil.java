
/**
 * @标题: SpringContextUtil.java
 * @包名： com.cares.mcp.util
 * @功能描述：TODO
 * @作者： jason
 * @创建时间： 2014年10月6日 下午11:42:34
 * @version v0.0.1
 */
package com.juneyaoair.utils.util;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

 


/**
 * @类描述：
 * @项目名称：com-juneyaoair-preparation-war
 * @包名： com.cares.mcp.util
 * @类名称：SpringContextUtil
 * @创建人：jason
 * @创建时间：2014年10月6日下午11:42:34
 * @修改人：jason
 * @修改时间：2014年10月6日下午11:42:34
 * @修改备注：
 * @version v0.0.1
 */
public class SpringContextUtil implements ApplicationContextAware {
	
	//Spring应用上下文环境
	private static ApplicationContext applicationContext;    
	/**
	 * @描述:  实现ApplicationContextAware接口的回调方法，设置上下文环境   
	 * @方法名: setApplicationContext
	 * @param
	 * @throws org.springframework.beans.BeansException
	 * @创建人：jason
	 * @创建时间：2014年10月6日下午11:42:34
	 * @修改人：jason
	 * @修改时间：2014年10月6日下午11:42:34
	 * @修改备注：
	 * @throws
	 */
	@Override
	public void setApplicationContext(ApplicationContext applicationContext)
			throws BeansException {
		SpringContextUtil.applicationContext = applicationContext;
	}
	
	/**
	 * 
	 * @描述: return ApplicationContext
	 * @方法名: getApplicationContext
	 * @return
	 * @返回类型 ApplicationContext
	 * @创建人 jason
	 * @创建时间 2014年10月6日下午11:44:41
	 * @修改人 jason
	 * @修改时间 2014年10月6日下午11:44:41
	 * @修改备注
	 * @since
	 * @throws
	 */
	public static ApplicationContext getApplicationContext() {
	    return applicationContext;
	}
	/**
	 * 
	 * @描述: 获取对象
	 * @方法名: getBean
	 * @param name
	 * @return
	 * @throws org.springframework.beans.BeansException
	 * @返回类型 Object
	 * @创建人 jason
	 * @创建时间 2014年10月6日下午11:45:42
	 * @修改人 jason
	 * @修改时间 2014年10月6日下午11:45:42
	 * @修改备注
	 * @since
	 * @throws
	 */
	public static Object getBean(String name) throws BeansException {
	    return applicationContext.getBean(name);
	}
	/**
	 * 
	 * @描述: 获取类型为requiredType的对象
	 *       如果bean不能被类型转换，相应的异常将会被抛出
	 * @方法名: getBean
	 * @param name
	 * @param requiredType
	 * @return
	 * @throws org.springframework.beans.BeansException
	 * @返回类型 Object
	 * @创建人 jason
	 * @创建时间 2014年10月6日下午11:47:03
	 * @修改人 jason
	 * @修改时间 2014年10月6日下午11:47:03
	 * @修改备注
	 * @since
	 * @throws
	 */
	public static Object getBean(String name, Class requiredType) throws BeansException {
	    return applicationContext.getBean(name, requiredType);
	}
}

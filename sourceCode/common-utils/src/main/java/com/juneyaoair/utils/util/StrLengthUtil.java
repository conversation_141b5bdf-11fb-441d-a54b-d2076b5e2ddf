package com.juneyaoair.utils.util;

import java.util.TreeSet;

import org.apache.commons.lang.StringUtils;

/**
 * @作者：wang
 * @备注：校验用户输入字符长度工具类
 */
public class StrLengthUtil {
	
	public static boolean StrLengthValidate(String str, int limitLength) {
		//如果字符串为空，则返回false
		if(StringUtils.isBlank(str)) {
			return false;
		}
		//将字符串转换为字符型数组
		char[] c = str.toCharArray();
		int len = 0;
		//判断如果是英文则字符长度+1，如果是中文则再+1
		for(int i=0; i<c.length; i++) {
			len++;
			if(!isLetter(c[i])) {
				len++;
			}
		}
		//如果长度大于规定的长度则返回true，否则返回false
		if(len > limitLength) {
			return true;
		} else {
			return false;
		}
	}
	
	//判断是否为汉字类方法
	public static boolean isLetter(char c) {
		int k = 0x80;
		return c / k == 0 ? true : false;
	}
	
	/**
	 * @方法：isRepeat 判断字符中是否有重复
	 * @参数：str 需要判断的字符串
	 * @返回值：true为有重复，false为无重复
	 */
	public static boolean isRepeat(String str){
		//创建一个用于去除重复的TreeSet
		TreeSet<Character> treeSet = new TreeSet<Character>();
		//将字符串分成一个个字符放到treeSet中
		for(int i=0; i<str.length(); i++){
			treeSet.add(str.charAt(i));
		}
		//由于treeSet可以自动去除重复，所以如果treeSet的长度小于传入字符串str的长度
		//则证明传入字符串有重复数据
		if(treeSet.size() < str.length()){
			return true;
		} else {
			return false;
		}
	}

}

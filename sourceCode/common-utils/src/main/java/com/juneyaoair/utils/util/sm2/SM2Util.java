package com.juneyaoair.utils.util.sm2;

import java.io.IOException;

/**
 * <AUTHOR>
 * @description
 * @date 2019/11/6  11:17.
 */
public class SM2Util {
    //SM2公钥编码格式
    //HardPubKey:3059301306072A8648CE3D020106082A811CCF5501822D03420004+X+Y
    //SoftPubKey:04+X+Y
    public static final String SM2PubHardKeyHead = "3059301306072A8648CE3D020106082A811CCF5501822D034200";

    //公钥加密
    public static String SM2Enc(String pubKey, String src) throws Exception {
        //String encrypt = SM2EncDecUtils.encrypt(Util.hexStringToBytes(pubKey), src.getBytes());
        String encrypt = SM2EncDecUtils.encrypt(Util.hexToByte(pubKey), src.getBytes());
        //删除04
        encrypt = encrypt.substring(2, encrypt.length());
        return encrypt;
    }
}

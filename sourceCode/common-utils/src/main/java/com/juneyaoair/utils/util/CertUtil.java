package com.juneyaoair.utils.util;

import com.juneyaoair.utils.StringUtil;

/**
 * <AUTHOR>
 * @description  身份证件基本判别工具包
 * @date 2018/8/13  11:34.
 */
public class CertUtil {

    private CertUtil(){}

    private static int idEighteen = 18;
    private static int idFifteen = 15;
    /**
     * 根据证件号获取出生日期
     * @param certNo
     * @return
     */
    public static  String certNoToDate(String certNo){
        String year = "";
        String month = "";
        String day = "";
        String birthDate = "";
        if(certNo.length() == idEighteen){
            year = certNo.substring(6,10);
            month = certNo.substring(10,12);
            day = certNo.substring(12,14);
            birthDate = year+"-"+month+"-"+day;
        }else if(certNo.length() == idFifteen){
            //320981910226321
            year = "19"+certNo.substring(6,8);
            month = certNo.substring(8,10);
            day = certNo.substring(10,12);
            birthDate = year+"-"+month+"-"+day;
        }else{
            birthDate = "";
        }
        return birthDate;
    }


    /**
     * 身份证判别性别
     * @param certNo
     * @return
     */
    public static String checkSex(String certNo){
        String num = "";
        if(certNo.length()==idEighteen){
            num = certNo .substring(16,17);
        }else if(certNo.length()==idFifteen){
            num = certNo.substring(14,15);
        }
        if(StringUtil.isNullOrEmpty(num)){
            return "U";
        }
        int sex = Integer.parseInt(num);
        if(sex%2==0){ //偶数的为女性
            return "F";
        }else{  //男性
            return "M";
        }
    }
}

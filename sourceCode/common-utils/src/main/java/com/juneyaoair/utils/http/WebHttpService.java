package com.juneyaoair.utils.http;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.Proxy;
import java.net.URL;

import sun.net.www.protocol.http.Handler;

public class WebHttpService {
    /**
     * 默认的读取时间
     */
    private static final int DEFAULT_READTIMEOUT = 80000;

    /**
     * 自定义连接时间与读取时间
     * @param ActionURL
     * @param ActionMethod
     * @param ContentType
     * @param FormData
     * @param Charset
     * @param RPCTimeout
     * @param readTimeOut
     * @return
     * @throws IOException
     */
    public static HttpResult webHttpHerlp(String ActionURL, String ActionMethod, String ContentType,
                                          byte[] FormData, String Charset, int RPCTimeout,int readTimeOut) throws IOException{
        //默认数据读取时间为80秒
        return webHttpHelpWithProxy(ActionURL,ActionMethod,ContentType,FormData,Charset,RPCTimeout,readTimeOut,null);
    }

    /**
     * 自定义连接时间与读取时间  使用代理
     * @param ActionURL
     * @param ActionMethod
     * @param ContentType
     * @param FormData
     * @param Charset
     * @param RPCTimeout
     * @param readTimeOut
     * @param proxy
     * @return
     * @throws IOException
     */
    public static HttpResult webHttpHelpWithProxy(String ActionURL, String ActionMethod, String ContentType,
                                          byte[] FormData, String Charset, int RPCTimeout,int readTimeOut,Proxy proxy) throws IOException{
        Handler handler = new Handler();
        URL url = new URL(null,ActionURL,handler);
        HttpURLConnection urlConnection;
        if(proxy != null){
             urlConnection = (HttpURLConnection) url.openConnection(proxy);
        }else{
             urlConnection = (HttpURLConnection) url.openConnection();
        }
        urlConnection.setRequestMethod(ActionMethod);
        // 发送POST请求必须设置如下两行
        if(ActionMethod.equalsIgnoreCase("POST")){
            urlConnection.setDoOutput(true);
        }else{
            urlConnection.setDoOutput(false);
        }
        urlConnection.setDoInput(true);
        urlConnection.setUseCaches(false);
        urlConnection.setConnectTimeout(RPCTimeout);
        if(readTimeOut>0){
            urlConnection.setReadTimeout(readTimeOut);
        }
        urlConnection.setRequestProperty("Accept-Charset", Charset);
        //urlConnection.setRequestProperty("Accept-Encoding", "identity");
        urlConnection.setRequestProperty("Content-Type", ContentType);
        //urlConnection.connect();
        if (ActionMethod.equalsIgnoreCase("POST") && FormData != null) {
            urlConnection.getOutputStream().write(FormData);
            urlConnection.getOutputStream().flush();
            urlConnection.getOutputStream().close();
        }
        if(ActionMethod.equalsIgnoreCase("POST")){
            return makeContent(urlConnection);
        }else{//GET请求返回
            return makeContentByGET(urlConnection);
        }

    }

    /**
     * 系统默认的读取时间配置
     * @param ActionURL
     * @param ActionMethod
     * @param ContentType
     * @param FormData
     * @param Charset
     * @param RPCTimeout
     * @return
     * @throws IOException
     */
	public static HttpResult webHttpHerlp(String ActionURL, String ActionMethod, String ContentType, 
															byte[] FormData, String Charset, int RPCTimeout) throws IOException{
        //默认数据读取时间为80秒
        return webHttpHerlp(ActionURL,ActionMethod,ContentType,FormData,Charset,RPCTimeout,DEFAULT_READTIMEOUT);
	}
    /**
     * 系统默认的读取时间配置  使用代理
     * @param ActionURL
     * @param ActionMethod
     * @param ContentType
     * @param FormData
     * @param Charset
     * @param RPCTimeout
     * @return
     * @throws IOException
     */
    public static HttpResult webHttpHerlp(String ActionURL, String ActionMethod, String ContentType,
                                          byte[] FormData, String Charset, int RPCTimeout ,Proxy proxy) throws IOException{
        //默认数据读取时间为80秒
        return webHttpHelpWithProxy(ActionURL,ActionMethod,ContentType,FormData,Charset,RPCTimeout,DEFAULT_READTIMEOUT,proxy);
    }
    //POST请求的数据返回
    private static HttpResult makeContent(HttpURLConnection urlConnection) throws IOException {
        InputStream in =null;
        try { 
        	HttpResult result = new HttpResult();
        	int StatusCode = urlConnection.getResponseCode();
        	if (StatusCode != HttpURLConnection.HTTP_OK) {
        		//System.out.println(StatusCode);
        		result.setResponse(String.valueOf(StatusCode));
        		result.setResult(false);
        		return result;
        	}
            in = urlConnection.getInputStream();
            int s = urlConnection.getContentLength();
            byte[] streamData = StreamUtil.readStream(in, s);
          //  result.setResponse(ZipUtil.ungzip(streamData));         
            result.setResponse(new String(streamData,"utf-8"));
            result.setResult(true);
            return result; 
        } catch (IOException e) {  
            throw e;  
        } finally {
            if(in!=null){
                in.close();
            }
            if (urlConnection != null) {
                urlConnection.disconnect();
            }
        }  
    }
    //GET请求的数据返回
    private static HttpResult makeContentByGET(HttpURLConnection urlConnection) throws IOException {
        InputStream in = null;
        BufferedReader reader = null;
        try {
            HttpResult result = new HttpResult();
            StringBuilder strBuilder = new StringBuilder();
            int StatusCode = urlConnection.getResponseCode();
            if (StatusCode != HttpURLConnection.HTTP_OK) {
                result.setResponse(String.valueOf(StatusCode));
                result.setResult(false);
                return result;
            }
            in = urlConnection.getInputStream();//获取请求结果
            //转换成一个buffered流
            reader=new BufferedReader(new InputStreamReader(in));
            //把读到的内容赋值给result
            String line;
            while ((line = reader.readLine()) != null) {
                strBuilder.append(line);
            }
            result.setResponse(strBuilder.toString());
            result.setResult(true);
            return result;
        } catch (IOException e) {
            throw e;
        } finally {
            if(reader != null){
                reader.close();
            }
            //关闭输入流
            if(in != null){
                in.close();
            }
            if (urlConnection != null) {
                urlConnection.disconnect();
            }
        }
    }
}

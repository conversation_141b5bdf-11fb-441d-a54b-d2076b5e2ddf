package com.juneyaoair.utils.util;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;

/**
 * Created by yaocf on 2017/3/17.
 * 图片压缩,裁剪工具类
 */
public class ImageCompress {
    private Image img;
    private int width;
    private int height;
    /**
     * 构造函数,初始化图片
     */
    public ImageCompress(String fileName) throws IOException {
        File file = new File(fileName);// 读入文件
        img = ImageIO.read(file);      // 构造Image对象
        width = img.getWidth(null);    // 得到源图宽
        height = img.getHeight(null);  // 得到源图长
    }
    public ImageCompress(Image image) {
        img = image;      // 构造Image对象
        width = img.getWidth(null);    // 得到源图宽
        height = img.getHeight(null);  // 得到源图长
    }

    /**
     * 强制压缩/放大图片到固定的大小
     * @param w int 新宽度
     * @param h int 新高度
     */
    public BufferedImage resizeToImage(int w, int h)  {
        try {
            BufferedImage image = new BufferedImage(w, h, BufferedImage.TYPE_INT_RGB);
            image.getGraphics().drawImage(img, 0, 0, w, h, null); // 绘制缩小后的图
            return image;
        }catch (Exception e){
            return null;
        }
    }

}

package com.juneyaoair.utils.util;

import com.juneyaoair.utils.exception.BusinessException;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @作者：wang
 * @备注：获取删除数据工具类
 */
public class DeleteUtil {

	private DeleteUtil() {}
	/**
	 * @方法：getDeleteData
	 * @参数：uuid 用户删除的数据的uuid
	 * 		fileURL 用户删除的数据的文件路径
	 * @返回值：含有uuid和fileURL的map
	 */
	public static Map<String, List<String>> getDeleteData(String uuid, String fileURL) {
		if(StringUtils.isBlank(uuid)) {
			throw new BusinessException("参数不能为空！");
		}
		List<String> uuidList = new ArrayList<>();
		List<String> fileURLList = new ArrayList<>();
		Map<String, List<String>> map = new HashMap<>();
		String[] uuidStr = uuid.split(",");
		for(int i=0; i<uuidStr.length; i++) {
			uuidList.add(uuidStr[i]);
		}
		if(!StringUtils.isBlank(fileURL)) {
			String[] fileURLStr = fileURL.split(",");
			for(int j=0; j<fileURLStr.length; j++) {
				fileURLList.add(fileURLStr[j]);
			}
		}
		map.put("uuidList", uuidList);
		map.put("fileURLList", fileURLList);
		return map;
	}
	
	/**
	 * @方法：getDeleteData 重载的getDeleteData方法，获取用户需要删除的数据的uuid
	 * @参数：uuid 用户需要删除的所有数据的uuid字符串
	 * @返回值：list
	 */
	public static List<String> getDeleteData(String uuid) {
		if(StringUtils.isBlank(uuid)) {
			throw new BusinessException("参数不能为空！");
		}
		List<String> uuidList = new ArrayList<>();
		String[] str = uuid.split(",");
		for(int i=0; i<str.length; i++) {
			uuidList.add(str[i]);
		}
		return uuidList;
	}
}

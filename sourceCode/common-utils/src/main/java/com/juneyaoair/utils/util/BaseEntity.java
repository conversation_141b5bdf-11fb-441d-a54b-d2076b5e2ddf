
/**
 * @标题: BaseEntity.java
 * @包名： net.hocrew.common.domain
 * @功能描述：主要包括分页等公共的信息
 * @作者： jason
 * @创建时间： 2014年11月3日 上午11:36:13
 * @version v0.0.1
 */
package com.juneyaoair.utils.util;


import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.List;




/**
 * @类描述：主要包括分页等公共的信息
 * @项目名称：net-hocrew-common
 * @包名： net.hocrew.common.domain
 * @类名称：BaseEntity
 * @创建人：jason
 * @创建时间：2014年11月3日上午11:36:13
 * @修改人：jason
 * @修改时间：2014年11月3日上午11:36:13
 * @修改备注：
 * @version v0.0.1
 */
public class BaseEntity {
	
	public static final  Integer NO_PAGE = 1;
	public static final  Integer NO_ROW_LIMIT = 2147483647;
	/** 页号 */
	private Integer page = NO_PAGE;
	/** 分页大小 */
	private Integer count = NO_ROW_LIMIT;
	
	/** 分页排序信息 */
	private String orderBy;

	/**
	 * Title:
	 * Description:
	 */
	public BaseEntity() {
		super();
	}


	
	/**
	 * Title:
	 * Description:
	 * @param page
	 * @param count
	 * @param orderBy
	 * @param totalCount
	 */
	public BaseEntity(Integer page, Integer count, String orderBy) {
		super();
		this.page = page;
		this.count = count;
		this.orderBy = orderBy;
		 
	}



	/**
	 * @return page
	 */
	public Integer getPage() {
		return page;
	}



	/**
	 * @param page page
	 */
	public void setPage(Integer page) {
		this.page = page;
	}



	/**
	 * @return count
	 */
	public Integer getCount() {
		return count;
	}



	/**
	 * @param count count
	 */
	public void setCount(Integer count) {
		this.count = count;
	}



	/**
	 * @return orderBy
	 */
	public String getOrderBy() {
		return orderBy;
	}



	/**
	 * @param orderBy orderBy
	 */
	public void setOrderBy(String orderBy) {
		this.orderBy = orderBy;
	}
	
	/**
	 * @描述:
	 * @方法名: toString
	 * @return
	 * @创建人：jason
	 * @创建时间：2014年11月3日上午11:42:26
	 * @修改人：jason
	 * @修改时间：2014年11月3日上午11:42:26
	 * @修改备注：
	 * @throws
	 */
	@Override
	public String toString() {
		return "BaseEntity [page=" + page + ", count=" + count + ", orderBy="
				+ orderBy + "]";
	}
	
	public PageBounds getPageBounds(){
		List<Order> orders = new ArrayList<>();
		if(StringUtils.isNotBlank(orderBy)){
			// 读取排序信息
			orders = Order.formString(orderBy);
		}
		
		if(CollectionUtils.isNotEmpty(orders)){
			return new PageBounds(page, count, orders);
		}
		else{
			return new PageBounds(page, count);
		}
		
	}
}

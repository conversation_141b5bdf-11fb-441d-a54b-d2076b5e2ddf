package com.juneyaoair.utils.util;

import org.springframework.util.Base64Utils;
import javax.crypto.Cipher;
import java.nio.charset.StandardCharsets;
import java.security.*;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description RAS加密解密算法
 * @date 2019/10/31  8:34.
 */
/*
 --------------------------------------------**********--------------------------------------------
 该算法于1977年由美国麻省理工学院MIT(Massachusetts Institute of Technology)的Ronal Rivest，Adi Shamir和Len <PERSON>三位年轻教授提出，并以三人的姓氏Rivest，Shamir和Adlernan命名为RSA算法，是一个支持变长密钥的公共密钥算法，需要加密的文件快的长度也是可变的!
 所谓RSA加密算法，是世界上第一个非对称加密算法，也是数论的第一个实际应用。它的算法如下：
 1.找两个非常大的质数p和q（通常p和q都有155十进制位或都有512十进制位）并计算n=pq，k=(p-1)(q-1)。
 2.将明文编码成整数M，保证M不小于0但是小于n。
 3.任取一个整数e，保证e和k互质，而且e不小于0但是小于k。加密钥匙（称作公钥）是(e, n)。
 4.找到一个整数d，使得ed除以k的余数是1（只要e和n满足上面条件，d肯定存在）。解密钥匙（称作密钥）是(d, n)。
 加密过程： 加密后的编码C等于M的e次方除以n所得的余数。
 解密过程： 解密后的编码N等于C的d次方除以n所得的余数。
 只要e、d和n满足上面给定的条件。M等于N。
 --------------------------------------------**********--------------------------------------------
 */
public class RsaUtil {
    // 签名算法
    private static final String SIGNATURE_ALGORITHM = "SHA256withRSA";
    public static final String CHARSET = "UTF-8";
    private static final String INSTANCE = "RSA";
    // 密钥长度
    private static final int KEY_SIZE = 2048;
    // 加密算法名称
    private static final String ALGORITHM = "RSA";

    /**
     * 生成RSA密钥对
     * @return 包含公钥和私钥的Map
     */
    public static Map<String, String> generateKeyPair() throws Exception {
        KeyPairGenerator keyPairGen = KeyPairGenerator.getInstance(ALGORITHM);
        keyPairGen.initialize(KEY_SIZE);
        KeyPair keyPair = keyPairGen.generateKeyPair();

        PublicKey publicKey = keyPair.getPublic();
        PrivateKey privateKey = keyPair.getPrivate();

        Map<String, String> keyMap = new HashMap<>();
        keyMap.put("publicKey", Base64Utils.encodeToString(publicKey.getEncoded()));
        keyMap.put("privateKey", Base64Utils.encodeToString(privateKey.getEncoded()));

        return keyMap;
    }

    /**
     * RSA加密
     * @param plainText
     * @param pubKeyRemote 公钥
     * @return
     * @throws Exception
     */
    public static String encrypt(String plainText, String pubKeyRemote) throws Exception {
        PublicKey publicKey = getPublicKey(pubKeyRemote);
        Cipher cipher = Cipher.getInstance(INSTANCE);
        cipher.init(Cipher.ENCRYPT_MODE, publicKey);
        return Base64Utils.encodeToString(cipher.doFinal(plainText.getBytes(CHARSET)));
    }


    /**
     * 私钥生成签名
     * @param plainText
     * @param privateKeyStr
     * @return
     */
    public static String sign(String plainText, String privateKeyStr) throws Exception {
        PrivateKey privateKey = getPrivateKey(privateKeyStr);
        Signature privateSignature = Signature.getInstance(SIGNATURE_ALGORITHM);
        privateSignature.initSign(privateKey);
        privateSignature.update(plainText.getBytes(StandardCharsets.UTF_8));
        byte[] signature = privateSignature.sign();
        return Base64Utils.encodeToString(signature);
    }

    /**
     * RSA公钥验签
     * @param data 原始数据
     * @param sign 签名(Base64编码)
     * @param publicKey 公钥(Base64编码)
     * @return 验签结果
     */
    public static boolean verify(String data, String sign, String publicKey) throws Exception {
        byte[] keyBytes = Base64Utils.decodeFromString(publicKey);
        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance(ALGORITHM);
        PublicKey pubKey = keyFactory.generatePublic(keySpec);

        Signature signature = Signature.getInstance(SIGNATURE_ALGORITHM);
        signature.initVerify(pubKey);
        signature.update(data.getBytes());

        byte[] signBytes = Base64Utils.decodeFromString(sign);
        return signature.verify(signBytes);
    }

    /**
     * 获取私钥
     * @param privateKeyStr
     * @return
     * @throws Exception
     */
    private static PrivateKey getPrivateKey(String privateKeyStr) throws Exception {
        byte[] keyBytes = Base64Utils.decodeFromString(privateKeyStr);;
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance(INSTANCE);
        return keyFactory.generatePrivate(keySpec);
    }

    /**
     * 获取公钥
     * @param pubKeyStr
     * @return
     * @throws Exception
     */
    private static PublicKey getPublicKey(String pubKeyStr) throws Exception {
        byte[] keyBytes = Base64Utils.decodeFromString(pubKeyStr);
        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance(INSTANCE);
        return keyFactory.generatePublic(keySpec);
    }
}

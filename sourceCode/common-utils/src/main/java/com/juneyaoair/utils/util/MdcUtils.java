package com.juneyaoair.utils.util;

import com.juneyaoair.utils.IPUtil;
import com.juneyaoair.utils.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;

import javax.servlet.http.HttpServletRequest;

/**
 * @Author: caolei
 * @Description: Mdc工具
 * @Date: 2021/12/27 12:37
 * @Modified by:
 */
public class MdcUtils {
    private MdcUtils() {
    }

    ;

    /**
     * 请求IP
     */
    private static final String CLIENT_IP = "CLIENT_IP";
    private static final String CLIENT_VERSION = "CLIENT_VERSION";
    private static final String CHANNEL_CODE = "CHANNEL_CODE";
    private static final String REQUEST_ID = "REQUEST_ID";

    /**
     * 设置请求IP
     *
     * @param request
     */
    public static void setClientIp(HttpServletRequest request) {
        MDC.put(CLIENT_IP, IPUtil.getIpAddr(request));
    }

    /**
     * 获取请求IP
     *
     * @return
     */
    public static String getClientIp() {
        return MDC.get(CLIENT_IP);
    }

    /**
     * 客户端版本
     *
     * @param clientVersion
     */
    public static void setClientVersion(String clientVersion) {
        MDC.put(CLIENT_VERSION, clientVersion);
    }

    /**
     * 客户端版本
     *
     * @return
     */
    public static String getClientVersion() {
        return MDC.get(CLIENT_VERSION);
    }

    /**
     * 渠道号
     *
     * @param channelCode
     */
    public static void setChannelCode(String channelCode) {
        MDC.put(CHANNEL_CODE, channelCode);
    }

    /**
     * 渠道号
     *
     * @return
     */
    public static String getChannelCode() {
        return MDC.get(CHANNEL_CODE);
    }

    /**
     * 设置请求ID
     *
     * @param requestId
     * @return
     */
    public static void setRequestId(String requestId) {
        MDC.put(REQUEST_ID, requestId);
    }

    /**
     * 获取请求ID
     *
     * @return
     */
    public static String getRequestId() {
        String requestId = MDC.get(REQUEST_ID);
        if (StringUtils.isBlank(requestId)) {
            requestId = StringUtil.newGUID();
        }
        return requestId;
    }

    public static String getRequestId(String channelCode) {
        String requestId = MDC.get(REQUEST_ID);
        if (StringUtils.isBlank(requestId)) {
            requestId = StringUtil.newGUID();
        }
        return channelCode + "_" + requestId;
    }

    /**
     * 清理MDC
     */
    public static void clear() {
        MDC.clear();
    }
}
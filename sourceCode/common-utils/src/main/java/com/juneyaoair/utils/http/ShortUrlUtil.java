package com.juneyaoair.utils.http;

import com.juneyaoair.utils.StringUtil;
import com.juneyaoair.utils.common.UrlResponse;
import com.juneyaoair.utils.json.JsonUtil;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.http.HttpHost;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.net.Proxy;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by yaocf on 2018/1/8.
 * 短链接生成工具
 */
public class ShortUrlUtil {
    private static Logger logger = LoggerFactory.getLogger(ShortUrlUtil.class.getName());

    private static final String SERVICE_CHARSET = "utf-8";
    private static final String CONTENT_TYPE = "text/html;charset=utf-8";
    private static final int SERVICE_TIME_OUT = 5 * 1000;
    private static final String BAIDU_CREATE_API = "https://dwz.cn/admin/v2/create";
    private static final int CONNECT_TIMEOUT = 3000;
    private static final int READ_TIMEOUT = 5000;


    private ShortUrlUtil() {
    }

    public static String createShortUrl(String longUrl, Proxy proxy) {
        String SuoUrl = "http://suo.im/api.php?url=";
        String SinaUrl = "http://api.t.sina.com.cn/short_url/shorten.json?source=2815391962&url_long=";
        HttpResult result = null;
        String shortUrl = "";
        try {
            SinaUrl = SinaUrl + longUrl;
            SuoUrl = SuoUrl + longUrl;
            result = WebHttpService.webHttpHerlp(SinaUrl, "GET", CONTENT_TYPE, longUrl.getBytes(SERVICE_CHARSET), SERVICE_CHARSET, SERVICE_TIME_OUT, proxy);
            if (!result.isResult()) {//新浪API异常时 采用备用API
                result = WebHttpService.webHttpHerlp(SuoUrl, "GET", CONTENT_TYPE, longUrl.getBytes(SERVICE_CHARSET), SERVICE_CHARSET, SERVICE_TIME_OUT, proxy);
                shortUrl = result.getResponse();
            } else {
                if (!StringUtil.isNullOrEmpty(result.getResponse())) {
                    JSONArray array = JSONArray.fromObject(result.getResponse());
                    if (array.isArray() && !array.isEmpty()) {
                        JSONObject object = array.getJSONObject(0);
                        shortUrl = (String) object.get("url_short");
                    }
                }
            }
        } catch (IOException e) {//异常情况下链接原样返回
            logger.error(e.getMessage(), e);
            shortUrl = longUrl;
            return shortUrl;
        }
        if (StringUtil.isNullOrEmpty(shortUrl) || !shortUrl.contains("http://")) {
            shortUrl = longUrl;
        }
        return shortUrl;
    }
}

/**
 * @标题: DateUtils.java
 * @包名： com.cares.mcp.util
 * @功能描述：日期类工具方法
 * @作者： jason
 * @创建时间： 2014年9月25日 下午5:22:36
 * @version v0.0.1
 */

package com.juneyaoair.utils.util;

import com.juneyaoair.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.xml.datatype.DatatypeConfigurationException;
import javax.xml.datatype.DatatypeFactory;
import javax.xml.datatype.XMLGregorianCalendar;
import java.math.BigInteger;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;

/**
 * @version v0.0.1
 * @类描述：
 * @包名： com.cares.mcp.util
 * @类名称：DateUtils
 * @创建人：jason
 * @创建时间：2014年9月25日下午5:22:36
 * @修改人：jason
 * @修改时间：2014年9月25日下午5:22:36
 * @修改备注：
 */
@Slf4j
public class DateUtils {

    private DateUtils() {
    }

    public static final String YYYYMMDD_PATTERN = "yyyyMMdd";

    public static final String YYYY_MM_DD_PATTERN = "yyyy-MM-dd";

    public static final String YYYY_MM_DD_HH_MM_PATTERN = "yyyy-MM-dd HH:mm";

    public static final String YYYY_MM_DD_HH_MM_SS_PATTERN = "yyyy-MM-dd HH:mm:ss";

    public static final String YYYYMMDDHHMMSSSSS_PATTERN = "yyyyMMddHHmmssSSS";

    public static final String YYYY_MM_DD_CHINA = "yyyy年MM月dd日";

    public static final String YYYY_PATTERN = "yyyy";

    public static final String HH_MM_PATTERN = "HH:mm";

    public static final String UTC_PATTERN = "yyyy-MM-dd'T'HH:mm:ss.SSS'+00:00'";

    public static final String FORMAT_DATE_TIME_ELK = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX";

    public static SimpleDateFormat genYYYYMMDDDateFormat() {
        return new SimpleDateFormat(YYYY_MM_DD_PATTERN);
    }

    public static Date toDate(String dateString) {
        return toDate(dateString, YYYY_MM_DD_PATTERN);
    }

    /**
     * @param dateString
     * @return java.util.Date
     * <AUTHOR>
     * @Description 当只有日期信息时 填补上开始时间 00:00:00
     * @Date 14:51 2023/7/31
     **/
    public static Date toDatePlusBeginTime(String dateString) {
        if (StringUtils.isNotEmpty(dateString)) {
            dateString = dateString + " 00:00:00";
        }
        return toDate(dateString, YYYY_MM_DD_HH_MM_SS_PATTERN);
    }

    /**
     * @param dateString
     * @return java.util.Date
     * <AUTHOR>
     * @Description 当只有日期信息时 填补上结束时间 23:59:59
     * @Date 14:53 2023/7/31
     **/
    public static Date toDatePlusEndTime(String dateString) {
        if (StringUtils.isNotEmpty(dateString)) {
            dateString = dateString + " 23:59:59";
        }
        return toDate(dateString, YYYY_MM_DD_HH_MM_SS_PATTERN);
    }

    public static Date toAllDate(String dateString) {
        return toDate(dateString, YYYY_MM_DD_HH_MM_PATTERN);
    }

    public static Date toTotalDate(String dateString) {
        return toDate(dateString, YYYY_MM_DD_HH_MM_SS_PATTERN);
    }

    public static String getCurrentTimeStr() {
        Date currentTime = new Date();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMddHHmmss");
        return formatter.format(currentTime);
    }

    public static String getCurrentDateStr(String format) {
        Date currentTime = new Date();
        SimpleDateFormat formatter = new SimpleDateFormat(format);
        return formatter.format(currentTime);
    }

    public static String getCurrentDateStr() {
        Date currentTime = new Date();
        SimpleDateFormat formatter = new SimpleDateFormat(YYYY_MM_DD_PATTERN);
        return formatter.format(currentTime);
    }

    public static String getCurrentDateTimeStr() {
        Date currentTime = new Date();
        SimpleDateFormat formatter = new SimpleDateFormat(YYYY_MM_DD_HH_MM_PATTERN);
        return formatter.format(currentTime);
    }

    public static String getDateString(Date date) {
        if (date == null) {
            return "";
        } else {
            return dateToString(date, YYYY_MM_DD_PATTERN);
        }
    }

    public static Date convertToDate(XMLGregorianCalendar cal) throws Exception {
        GregorianCalendar ca = cal.toGregorianCalendar();
        return ca.getTime();
    }

    public static String getDateStringAll(Date date) {
        SimpleDateFormat formatter = new SimpleDateFormat(YYYY_MM_DD_HH_MM_PATTERN);
        return formatter.format(date);

    }

    public static String getDateStringAllDate(Date date) {
        SimpleDateFormat formatter = new SimpleDateFormat(YYYY_MM_DD_HH_MM_SS_PATTERN);
        return formatter.format(date);

    }

    public static String getDateStringHMDate(Date date) {
        SimpleDateFormat formatter = new SimpleDateFormat(HH_MM_PATTERN);
        return formatter.format(date);

    }

    /**
     * Convert String to Date according to the format string
     *
     * @param dateString
     * @param format
     * @return
     */
    public static Date toDate(String dateString, String format) {
        try {
            SimpleDateFormat formatter = new SimpleDateFormat(format);
            return formatter.parse(dateString);
        } catch (Exception e) {
            return null;
        }
    }

    public static String getWeek(Date dt) {
        String[] weekDays = {"星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"};
        Calendar cal = Calendar.getInstance();
        cal.setTime(dt);
        int w = cal.get(Calendar.DAY_OF_WEEK) - 1;
        if (w < 0) {
            w = 0;
        }
        return weekDays[w];
    }

    public static String getWeekStr(Date dt) {
        String[] weekDays = {"周日", "周一", "周二", "周三", "周四", "周五", "周六"};
        Calendar cal = Calendar.getInstance();
        cal.setTime(dt);
        int w = cal.get(Calendar.DAY_OF_WEEK) - 1;
        if (w < 0) {
            w = 0;
        }
        return weekDays[w];
    }

    public static String getStringDate() {
        Date currentTime = new Date();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        String dateString = formatter.format(currentTime);
        return dateString;
    }

    //日期转换时间戳 毫秒
    public static String dateToStamp(String s) throws Exception {
        String res;
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        Date date = simpleDateFormat.parse(s);
        long time = date.getTime();
        res = String.valueOf(time);
        return res;
    }

    //计算相差多少小时
    public static int differHours(String startTime, String endTime) {
        int hour = 0;
        try {
            String startStamp = dateToStamp(startTime);
            String endStamp = dateToStamp(endTime);
            BigInteger startStampInt = BigInteger.valueOf(Long.valueOf(startStamp));
            BigInteger endStampInt = BigInteger.valueOf(Long.valueOf(endStamp));
            BigInteger apartStamp = endStampInt.subtract(startStampInt);
            BigInteger hourStamp = BigInteger.valueOf(1000 * 60 * 60L);
            BigInteger hours = apartStamp.divide(hourStamp);//相差几小时
            hour = hours.intValue();
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }

        return hour;
    }

    /**
     *
     * @param startDate
     * @param endDate
     * @return endDate-startDate
     */
    public static long diffMinute(Date startDate,Date endDate){
        Instant instant1 = startDate.toInstant();
        Instant instant2 = endDate.toInstant();
        return ChronoUnit.MINUTES.between(instant1, instant2);
    }

    //返回没有“周”前缀
    public static String getWeekStrNoZhou(Date dt) {
        String[] weekDays = {"日", "一", "二", "三", "四", "五", "六"};
        Calendar cal = Calendar.getInstance();
        cal.setTime(dt);
        int w = cal.get(Calendar.DAY_OF_WEEK) - 1;
        if (w < 0) {
            w = 0;
        }
        return weekDays[w];
    }

    /**
     * 日期转化成字符串
     *
     * @param d      日期
     * @param format 格式
     * @return
     */
    public static String dateToString(Date d, String format) {
        if (StringUtils.isBlank(format)) {
            return "";
        }
        SimpleDateFormat df = new SimpleDateFormat(format);
        return df.format(d);
    }

    public static String convertDateToString(Date date, String format) {
        if (date != null) {
            try {
                SimpleDateFormat formatter = new SimpleDateFormat(format);
                return formatter.format(date);
            } catch (Exception e) {
                return "";
            }
        } else {
            return "";
        }
    }

    /**
     * yyyy年MM月dd日 HH时mm分ss秒
     *
     * @param date
     * @return
     */
    public static String convertToDateString(Date date) {
        return new SimpleDateFormat("yyyy年MM月dd日 HH时mm分ss秒").format(date);
    }

    public static XMLGregorianCalendar convertToXMLGregorianCalendar(String date, String format)
            throws DatatypeConfigurationException, ParseException {
        GregorianCalendar cal = new GregorianCalendar();
        cal.setTime(convertStr2Date(date, format));
        return DatatypeFactory.newInstance().newXMLGregorianCalendar(cal);
    }

    public static Date convertStr2Date(String dateAsStr, String format) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        return sdf.parse(dateAsStr);
    }

    public static String convertDate2Str(Date date, String format) {
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        return sdf.format(date);
    }

    /**
     * 增加或减少日期的天数
     * 增加为正数，减少为负数
     *
     * @param date
     * @param changeDays
     * @return
     */
    public static Date addOrLessDay(Date date, int changeDays) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.DAY_OF_MONTH, calendar.get(Calendar.DAY_OF_MONTH) + changeDays);
        return calendar.getTime();
    }

    /**
     * 增加或减少日期的天数
     * 增加为正数，减少为负数
     *
     * @param date
     * @param changeHours
     * @return
     */
    public static Date addOrLessHour(Date date, int changeHours) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR, calendar.get(Calendar.HOUR) + changeHours);
        return calendar.getTime();
    }


    /**
     * 增加或减少日期的月数
     * 增加为正数，减少为负数
     *
     * @param date
     * @param changeMonth
     * @return
     */
    public static Date addOrLessMonth(Date date, int changeMonth) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.MONTH, calendar.get(Calendar.MONTH) + changeMonth);
        return calendar.getTime();
    }


    /**
     * 增加或减少日期的秒数
     * 增加为正数，减少为负数
     *
     * @param date
     * @param second
     * @return
     */
    public static Date dateAddOrLessSecond(Date date, int second) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);

        calendar.add(Calendar.SECOND, second);
        return calendar.getTime();
    }

    /**
     * 两个时间差，单位天数(包含当天)
     *
     * @param startTime
     * @param endTime
     * @return
     */
    public static int dateDiff(String startTime, String endTime, String format) {
        SimpleDateFormat sd = new SimpleDateFormat(format);
        long diff;
        try {
            diff = sd.parse(endTime).getTime() - sd.parse(startTime).getTime();
        } catch (ParseException e) {
            return 0;
        }
        return (int) (diff / (1000 * 24 * 60 * 60) + 1);
    }

    /**
     * 两个时间差，单位天数（不包含当天）
     *
     * @param startTime
     * @param endTime
     * @return
     */
    public static int dateDiff(Date startTime, Date endTime) {
        try {
            long diff = startTime.getTime() - endTime.getTime();
            return (int) (diff / (1000 * 24 * 60 * 60));
        } catch (Exception e) {
            return 0;
        }
    }


    /**
     * 两个时间差，单位分钟
     *
     * @param startTime
     * @param endTime
     * @return
     */
    public static int dateminuteDiff(Date startTime, Date endTime) {
        try {
            long diff = startTime.getTime() - endTime.getTime();
            return (int) (diff / (1000 * 60));
        } catch (Exception e) {
            return 0;
        }
    }

    /**
     * 两个时间差，单位毫秒
     *
     * @param startDate
     * @param endDate
     * @return
     */
    public static long millisecondDiff(Date startDate, Date endDate) {
        return endDate.getTime() - startDate.getTime();
    }

    /**
     * 两个时间差，单位秒
     *
     * @param startTime
     * @return
     */
    public static long millisecondDiff(long startTime) {
        return (System.currentTimeMillis() - startTime) / 1000;
    }


    /**
     * 得到系统时间到第二天凌晨的时间差
     * 返回毫秒数
     *
     * @param date
     * @return
     */
    public static long getSecondDayDifference(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int day = cal.get(Calendar.DATE);
        cal.set(Calendar.DATE, day + 1);
        DateUtils.format(cal, "yyyy-MM-dd 00:00:00");
        Date beginDay = DateUtils.timeToBeginDay(cal.getTime());
        if (beginDay != null) {
            long timeDiff = beginDay.getTime() - System.currentTimeMillis();
            return timeDiff / 1000;
        } else {
            return 6 * 60 * 60L;
        }
    }

    /**
     * Calendar转换成string
     *
     * @param calendar Calendar对象
     * @param pattern  转换格式
     * @return
     */
    public static String format(Calendar calendar, String pattern) {
        if (StringUtils.isBlank(pattern)) {
            return "";
        }
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        return sdf.format(calendar.getTime());
    }

    /**
     * 将时间归至第二天零点
     *
     * @param date
     * @return
     */
    public static Date timeToBeginDay(Date date) {
        if (date != null) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);
            return calendar.getTime();
        }
        return null;
    }


    //供运价缓存用
    public static int dateDiffBySecond(String endTime) {
        SimpleDateFormat sd = new SimpleDateFormat(YYYY_MM_DD_PATTERN);
        long diff;
        try {
            Date queryDate = addOrLessDay(sd.parse(endTime), 1);
            diff = queryDate.getTime() - new Date().getTime();
        } catch (ParseException e) {
            return 0;
        }
        return (int) (diff / 1000);
    }

    /**
     * 获取当前月最后一天
     *
     * @return str获取当前月最后一天
     */
    public static String lastDayofMonth() {
        Calendar ca = Calendar.getInstance();
        ca.set(Calendar.DAY_OF_MONTH, ca.getActualMaximum(Calendar.DAY_OF_MONTH));
        return genYYYYMMDDDateFormat().format(ca.getTime());
    }

    /**
     * 获取传入日期所在月的最后一天
     *
     * @param date
     * @return
     */
    public static Date lastDayofMonth(Date date) {
        Calendar ca = Calendar.getInstance();
        ca.setTime(date);
        ca.set(Calendar.DAY_OF_MONTH, ca.getActualMaximum(Calendar.DAY_OF_MONTH));
        ca.set(Calendar.HOUR_OF_DAY, 0);
        ca.set(Calendar.MINUTE, 0);
        ca.set(Calendar.SECOND, 0);
        return ca.getTime();
    }

    //type 10检查开始 20检查结束
    public static String checkTime(String freTimeStr, String endTimeStr) {
        if (StringUtils.isBlank(freTimeStr) || StringUtils.isBlank(endTimeStr)) {
            throw new IllegalArgumentException("开始时间，结束时间不能为空");
        }
        Date newDate = new Date();
        long nowDate = newDate.getTime();
        Date freDate = DateUtils.toTotalDate(freTimeStr);
        Date endDate = DateUtils.toTotalDate(endTimeStr);
        long freTime = freDate == null ? 0 : freDate.getTime();
        long endTime = endDate == null ? 0 : endDate.getTime();
        if (nowDate > endTime) {
            return "over";
        } else if (nowDate < freTime) {
            return "wait";
        } else {
            return "start";
        }
    }

    //判断当前日期在某个范围
    public static boolean compareCurrenDate(String startDateStr, String endTimeStr) {
        return compareCurrentDate(startDateStr, endTimeStr, YYYY_MM_DD_HH_MM_SS_PATTERN);
    }

    //判断当前日期在某个范围
    public static boolean compareCurrentDate(String startDateStr, String endTimeStr, String dateFormat) {
        String currentDateStr = getCurrentDateStr(dateFormat);
        DateFormat df = new SimpleDateFormat(dateFormat);
        try {
            Date curDate = df.parse(currentDateStr);
            Date startDate = df.parse(startDateStr);
            Date endDate = df.parse(endTimeStr);
            return curDate.getTime() >= startDate.getTime() && curDate.getTime() <= endDate.getTime();
        } catch (Exception e) {
            return false;
        }
    }


    /**
     * 判断某个时间是否在某个范围
     *
     * @param begin  开始时间
     * @param end    结束时间
     * @param source 要比较的时间
     * @return
     */
    public static boolean compareDate(Date begin, Date end, Date source) {
        if (begin != null && end != null && begin.getTime() <= source.getTime() && end.getTime() >= source.getTime()) {
            return true;
        } else if (begin != null && end == null && begin.getTime() <= source.getTime()) {
            return true;
        } else if (end != null && begin == null && end.getTime() >= source.getTime()) {
            return true;
        } else {
            return begin == null && end == null;
        }
    }

    /**
     * 判断某个时间是否在某个范围
     *
     * @param begin  开始时间
     * @param end    结束时间
     * @param source 要比较的时间
     * @return
     */
    public static boolean compareDate(String begin, String end, String source,String format) {
        Date beginDate = toDate(begin,format);
        Date endDate = toDate(end,format);
        Date sourceDate = toDate(source,format);
        return  compareDate(beginDate,endDate,sourceDate);
    }

    /**
     * 返回周岁 生日当天周岁+1
     *
     * @param birthDateStr 2008-02-17
     * @param curDateStr   2020-02-17
     * @param format
     * @return
     */
    public static int getAgeByBirthIncludeBirthDay(String birthDateStr, String curDateStr, String format) {
        int age = getAgeByBirth(birthDateStr, curDateStr, format);
        if (age >= 0 && birthDateStr.substring(5).equals(curDateStr.substring(5))) {
            age++;
        }
        return age;
    }

    //返回周岁 生日当天不算
    public static int getAgeByBirth(String birthDateStr, String curDateStr, String format) {
        int age;
        try {
            Date curDate = toDate(curDateStr, format);
            Calendar now = Calendar.getInstance();
            now.setTime(curDate);// 当前时间
            Date birthDate = toDate(birthDateStr, format);
            Calendar birth = Calendar.getInstance();
            birth.setTime(birthDate);
            if (birth.after(now)) {
                age = -1;  //表示不存在的出生日期
            } else {
                age = now.get(Calendar.YEAR) - birth.get(Calendar.YEAR);
                if (age <= 0) {
                    return 0;
                }
                // 如果当前月份小于出生月份: age-1
                // 如果当前月份等于出生月份, 且当前日小于出生日: age-1
                int currMonth = now.get(Calendar.MONTH);
                int currDay = now.get(Calendar.DAY_OF_MONTH);
                int bornMonth = birth.get(Calendar.MONTH);
                int bornDay = birth.get(Calendar.DAY_OF_MONTH);
                if ((currMonth < bornMonth) || (currMonth == bornMonth && currDay <= bornDay)) {
                    age--;
                }
            }
        } catch (Exception e) {
            age = -2;//表示出错
        }
        return age;
    }

    //返回时间戳  默认本地服务器时间
    public static long getTime() {
        return new Date().getTime();
    }

    //时间戳转为Date
    public static String timeStampToDateStr(long seconds) {
        try {
            Date date = new Date(seconds);
            return genYYYYMMDDDateFormat().format(date);
        } catch (Exception e) {
            return "";
        }
    }

    /**
     * 计算两个时区的时间差
     *
     * @param depTime 起飞当地区时
     * @param depZone 起飞当地时区
     * @param arrTime 到达当地区时
     * @param arrZone 到达当地时区
     * @return
     */
    public static long calDuration(String depTime, String depZone, String arrTime, String arrZone) {
        try {
            if (StringUtil.isNullOrEmpty(depZone)) {
                depZone = "8";
            }
            if (StringUtil.isNullOrEmpty(arrZone)) {
                arrZone = "8";
            }
            Date depDate = DateUtils.toDate(depTime, YYYY_MM_DD_HH_MM_PATTERN);
            Date arrDate = DateUtils.toDate(arrTime, YYYY_MM_DD_HH_MM_PATTERN);
            long diff;
            if (!depZone.equals(arrZone)) {//不同时区
                //计算到达时间相对出发时区的区时
                //计算区时  计算的区时=已知区时-（已知区时的时区-要计算区时的时区）
                Double depZoneInt = Double.parseDouble(depZone);//出发时区
                Double arrZoneInt = Double.parseDouble(arrZone);//到达时区
                Double diffZone = (arrZoneInt - depZoneInt) * 3600;//到达地区与出发地区的时区差
                Date arrDateDepZone = DateUtils.dateAddOrLessSecond(arrDate, -diffZone.intValue());//转换为出发地区时区的区时
                diff = DateUtils.millisecondDiff(depDate, arrDateDepZone);
            } else {
                diff = DateUtils.millisecondDiff(depDate, arrDate);
            }
            return diff;
        } catch (Exception e) {
            return 0;
        }
    }

    /**
     * 将已知时区的区时转换为指定时区的区时
     *
     * @param dateTime       已知区时
     * @param dateTimeZone   已知区时的时区
     * @param targetTimeZone 要计算区时的时区
     * @return
     */
    public static Date toTargetDate(String dateTime, String dateTimeZone, String targetTimeZone) {
        try {
            if (StringUtils.isBlank(dateTimeZone)) {
                dateTimeZone = "8";
            }
            if (StringUtils.isBlank(targetTimeZone)) {
                targetTimeZone = "8";
            }
            //计算区时  计算的区时=已知区时-（已知区时的时区-要计算区时的时区）
            Double dateTimeZoneInt = Double.parseDouble(dateTimeZone);//已知区时的时区
            Double targetTimeZoneInt = Double.parseDouble(targetTimeZone);//要计算区时的时区
            Date date = DateUtils.toDate(dateTime, YYYY_MM_DD_HH_MM_PATTERN);
            Double diffZone = (dateTimeZoneInt - targetTimeZoneInt) * 3600;//到达地区与出发地区的时区差
            return DateUtils.dateAddOrLessSecond(date, -diffZone.intValue());//转换为出发地区时区的区时
        } catch (Exception e) {
            return DateUtils.toDate(dateTime, YYYY_MM_DD_HH_MM_PATTERN);
        }
    }

    /**
     * 计算两个日期的跨天数
     *
     * @param depTime yyyy-MM-dd
     * @param arrTime yyyy-MM-dd
     * @return
     */
    public static int diffDays(String depTime, String arrTime, String format) {
        Date depDate = DateUtils.toDate(depTime, format);
        Date arrDate = DateUtils.toDate(arrTime, format);
        int days = 0;
        if (depDate != null && arrDate != null) {
            days = DateUtils.dateDiff(arrDate, depDate);
        }
        return days;
    }

    /**
     * 计算两个日期的跨天数  带入时区转换
     *
     * @return
     */
    public static int diffDays(String depTime, String depZone, String arrTime, String arrZone) {
        try {
            if (StringUtil.isNullOrEmpty(depZone)) {
                depZone = "8";
            }
            if (StringUtil.isNullOrEmpty(arrZone)) {
                arrZone = "8";
            }
            Date depDate = DateUtils.toDate(depTime, YYYY_MM_DD_PATTERN);
            //当地到达时间
            Date arrDate = DateUtils.toDate(arrTime, YYYY_MM_DD_PATTERN);
            int diff;
            if (!depZone.equals(arrZone)) {
                //计算出发时间相对到达时区的区时
                //计算区时  计算的区时=已知区时-（已知区时的时区-要计算区时的时区）
                //出发时区
                Double depZoneInt = Double.parseDouble(depZone);
                //到达时区
                Double arrZoneInt = Double.parseDouble(arrZone);
                //到达地区与出发地区的时区差
                Double diffZone = (depZoneInt - arrZoneInt) * 3600;
                //转换为到达时区的出发时间
                Date depDateArrZone = DateUtils.dateAddOrLessSecond(depDate, -diffZone.intValue());
                //天数
                diff = DateUtils.dateDiff(arrDate, depDateArrZone);
            } else {
                //天数
                diff = DateUtils.dateDiff(arrDate, depDate);
            }
            return diff;
        } catch (Exception e) {
            return 0;
        }
    }

    /**
     * depTime  yyyy-MM-dd HH:mm
     * arrTime  yyyy-MM-dd HH:mm
     * 计算跨天数
     *
     * @return
     */
    public static int diffDaysWithHour(String depTime, String depZone, String arrTime, String arrZone) {
        try {
            if (StringUtil.isNullOrEmpty(depZone)) {
                depZone = "8";
            }
            if (StringUtil.isNullOrEmpty(arrZone)) {
                arrZone = "8";
            }
            Date depDate = DateUtils.toDate(depTime, YYYY_MM_DD_HH_MM_PATTERN);
            //当地到达时间
            Date arrDate = DateUtils.toDate(arrTime, YYYY_MM_DD_HH_MM_PATTERN);
            int diff;
            if (!depZone.equals(arrZone)) {
                //计算出发时间相对到达时区的区时
                //计算区时  计算的区时=已知区时-（已知区时的时区-要计算区时的时区）
                //出发时区
                Double depZoneInt = Double.parseDouble(depZone);
                //到达时区
                Double arrZoneInt = Double.parseDouble(arrZone);
                //到达地区与出发地区的时区差
                Double diffZone = (depZoneInt - arrZoneInt) * 3600;
                //转换为到达时区的出发时间
                Date depDateArrZone = DateUtils.dateAddOrLessSecond(depDate, -diffZone.intValue());
                //取出当地的日期yyyy-MM-dd
                String depDateArrZoneStr = convertDateToString(depDateArrZone, YYYY_MM_DD_PATTERN);
                String arrDateArrZoneStr = convertDateToString(arrDate, YYYY_MM_DD_PATTERN);
                //天数
                diff = diffDays(depDateArrZoneStr, arrZone, arrDateArrZoneStr, arrZone);
            } else {
                //取出当地的日期yyyy-MM-dd
                String depDateStr = convertDateToString(depDate, YYYY_MM_DD_PATTERN);
                String arrDateStr = convertDateToString(arrDate, YYYY_MM_DD_PATTERN);
                diff = diffDays(depDateStr, arrZone, arrDateStr, arrZone);
            }
            return diff;

        } catch (Exception e) {
            return 0;
        }
    }

    /**
     * 跨时区比较，统一转换为北京时间
     *
     * @param localDateStr 当前系统时间
     * @param localZone 当前系统时区
     * @param knownDateTime 已知当地时间  yyyy-MM-dd HH:mm
     * @param knownTimeZone 已知当地时区
     * @param filter        过滤时间 1小时表示为1000
     * @return
     */
    public static boolean filterDateTimeWithZone(String localDateStr, String localZone, String knownDateTime, String knownTimeZone, long filter) {
        Date localDate = DateUtils.toDate(localDateStr, YYYY_MM_DD_HH_MM_PATTERN);
        Date knownDateLocal;
        if (localZone.equals(knownTimeZone)) {//同一时区的可不需要转换，直接使用
            knownDateLocal = DateUtils.toDate(knownDateTime, YYYY_MM_DD_HH_MM_PATTERN);
        } else {
            knownDateLocal = toTargetDate(knownDateTime, knownTimeZone, localZone);
        }
        if (null == knownDateLocal || null == localDate) {
            throw new IllegalArgumentException("日期转化出错");
        }

        //knownDateLocal - localDate 需满足指定的分钟数
        return DateUtils.diffMinute(localDate,knownDateLocal) <= filter;
    }

    /**
     * 时间戳转换输出指定格式
     *
     * @param seconds
     * @param format
     * @return
     */
    public static String timeStampToDateStr(long seconds, String format) {
        try {
            Date date = new Date(seconds);
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat(format);
            return simpleDateFormat.format(date);
        } catch (Exception e) {
            return "";
        }
    }

    /**
     * 返回两日期的天数差
     * data1 - date2
     */
    public static long compareDatesByDay(Date date1, Date date2) {
        return (date1.getTime() - date2.getTime()) / (1000 * 3600 * 24);
    }

    /**
     * 返回两日期的月份差
     *
     * @param date1
     * @param date2
     * @return
     */
    public static int compareDatesByMonth(Date date1, Date date2) {
        Calendar calendar1 = Calendar.getInstance();
        calendar1.setTime(date1);
        Calendar calendar2 = Calendar.getInstance();
        calendar2.setTime(date2);
        int year = calendar1.get(Calendar.YEAR) - calendar2.get(Calendar.YEAR);
        int month = calendar1.get(Calendar.MONTH) - calendar2.get(Calendar.MONTH);
        return year * 12 + month;
    }

    public static String getCurrentTimeTOStr() {
        Date currentTime = new Date();
        SimpleDateFormat formatter = new SimpleDateFormat(YYYY_MM_DD_HH_MM_SS_PATTERN);
        return formatter.format(currentTime);
    }

    /**
     * 毫秒转化时分
     */
    public static String formatTime(Long ms) {
        Integer ss = 1000;
        Integer mi = ss * 60;
        Integer hh = mi * 60;
        Integer dd = hh * 24;

        Long day = ms / dd;
        Long hour = (ms - day * dd) / hh;
        Long minute = (ms - day * dd - hour * hh) / mi;
        StringBuilder sb = new StringBuilder();
        if (day > 0) {
            sb.append(day + "天");
        }
        if (hour > 0) {
            sb.append(hour + "小时");
        }
        if (minute > 0) {
            sb.append(minute + "分钟");
        }

        return sb.toString();
    }

    /**
     * @param day1
     * @param day2
     * 判断两个日期横跨天数（不含当天）
     * 使用场景：计算飞行跨天数
     * E.g., 2019-09-19 12:00:00  2019-09-20 06:00:00 return 1
     * E.g., 2019-09-19 12:00:00  2019-09-20 15:00:00 return 1
     * E.g., 2019-09-19 12:00:00  2019-09-21 06:00:00 return 2
     * E.g., 2019-09-19 12:00:00  2019-09-18 06:00:00 return -1
     * @return  day2-day1
     */
    public static long durDays(Date day1, Date day2) {
        if (day2.before(day1)) {
            return durDays(day2, day1) * -1;
        }
        Calendar calendar1 = Calendar.getInstance();
        calendar1.setTime(day1);
        Calendar calendar2 = Calendar.getInstance();
        calendar2.setTime(day2);
        long durDays = compareDatesByDay(day2, day1);
        if (calendar2.get(Calendar.HOUR_OF_DAY) < calendar1.get(Calendar.HOUR_OF_DAY)
                || (calendar2.get(Calendar.HOUR_OF_DAY) == calendar1.get(Calendar.HOUR_OF_DAY)
                && calendar2.get(Calendar.MINUTE) < calendar1.get(Calendar.MINUTE))
                || (calendar2.get(Calendar.HOUR_OF_DAY) == calendar1.get(Calendar.HOUR_OF_DAY)
                && calendar2.get(Calendar.MINUTE) == calendar1.get(Calendar.MINUTE)
                && calendar2.get(Calendar.SECOND) < calendar1.get(Calendar.SECOND))) {
            durDays += 1;
        }
        return durDays;
    }

    /**
     * 指定日期字符串获取时间戳
     *
     * @param dateStr
     * @param format
     * @return
     */
    public static long dateStrToTimestamp(String dateStr, String format) {
        Date date = toDate(dateStr, format);
        if (date != null) {
            return date.getTime();
        }
        return getTime();
    }

    /*计算飞行总时长 yyyy-MM-dd HH:mm*/
    public static String getFlyTime(String deptTime, String deptTimeZone, String arrTime, String arrTimeZone) {
        String s = null;
        if (StringUtil.isNullOrEmpty(deptTimeZone)) {
            deptTimeZone = "8";
        }
        if (StringUtil.isNullOrEmpty(arrTimeZone)) {
            arrTimeZone = "8";
        }
        if (!StringUtil.isNullOrEmpty(deptTime) && !StringUtil.isNullOrEmpty(arrTime)) {
            long lo = DateUtils.calDuration(deptTime, deptTimeZone, arrTime, arrTimeZone);
            s = DateUtils.formatTime(lo);
        }
        return s;
    }

    /**
     * 将UTC时间，转为北京时间
     *
     * @param UTCStr
     */
    public static String UTCToCST(String UTCStr) {
        Date date;
        SimpleDateFormat sdf = new SimpleDateFormat(UTC_PATTERN);
        String CSTStr = "";
        try {
            date = sdf.parse(UTCStr);
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);
            calendar.set(Calendar.HOUR, calendar.get(Calendar.HOUR) + 8);
            //calendar.getTime() 返回的是Date类型，也可以使用calendar.getTimeInMillis()获取时间戳
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat(YYYY_MM_DD_HH_MM_PATTERN);
            CSTStr = simpleDateFormat.format(calendar.getTime());
        } catch (ParseException e) {
            return null;
        }
        return CSTStr;
    }


    /**
     * 获取指定时间 指定出生日期的年龄
     *
     * @param time
     * @param birthDay
     * @return
     */
    public static Integer getDateAge(Date time, Date birthDay) {
        if (null == time || null == birthDay) {
            return null;
        }
        Calendar cal = Calendar.getInstance();
        cal.setTime(time);
        // 如果出生日期大于指定时间，则返回null
        if (cal.before(birthDay)) {
            return null;
        }
        // 取出系统指定时间的年、月、日部分
        int yearNow = cal.get(Calendar.YEAR);
        int monthNow = cal.get(Calendar.MONTH);
        int dayOfMonthNow = cal.get(Calendar.DAY_OF_MONTH);

        // 将日期设置为出生日期
        cal.setTime(birthDay);
        // 取出出生日期的年、月、日部分
        int yearBirth = cal.get(Calendar.YEAR);
        int monthBirth = cal.get(Calendar.MONTH);
        int dayOfMonthBirth = cal.get(Calendar.DAY_OF_MONTH);
        // 当前年份与出生年份相减，初步计算年龄
        int age = yearNow - yearBirth;
        // 当前月份与出生日期的月份相比，如果月份小于出生月份，则年龄上减1，表示不满多少周岁
        if (monthNow <= monthBirth) {
            //如果月份相等，在比较日期，如果当前日，小于出生日，也减1，表示不满多少周岁
            if (monthNow == monthBirth) {
                if (dayOfMonthNow < dayOfMonthBirth) {
                    age--;
                }
            } else {
                age--;
            }
        }
        return age;
    }


    /**
     * 获取距离明天凌晨00:00的毫秒值
     *
     * @return
     */
    public static long getTomorrowZeroClockSecond() {
        Calendar instance = Calendar.getInstance();
        instance.add(Calendar.DAY_OF_MONTH, 1);
        instance.set(Calendar.SECOND, 0);
        instance.set(Calendar.MINUTE, 0);
        instance.set(Calendar.HOUR_OF_DAY, 0);
        return (instance.getTimeInMillis() - System.currentTimeMillis()) / 1000;
    }

    /**
     * @param dateTime
     * @param dateLength
     * @return
     */
    public static String subDateStr(String dateTime, int dateLength) {
        if (StringUtils.isBlank(dateTime) || dateLength < 0) {
            return "";
        }
        if (dateTime.length() >= dateLength) {
            return dateTime.substring(0, dateLength);
        }
        return dateTime;
    }

    public static LocalDate toLocalDate(String date, String format) {
        try {
            DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern(format);
            return LocalDate.parse(date, dateFormatter);
        } catch (Exception e) {
            log.info("日期转换出错:{},格式{}", date, format, e);
            return null;
        }

    }

    public static LocalDateTime toLocalDateTime(String dateTime, String format) {
        try {
            DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(format);
            return LocalDateTime.parse(dateTime, dateTimeFormatter);
        } catch (Exception e) {
            log.info("日期转换出错:{},格式{}", dateTime, format, e);
            return null;
        }

    }

    /**
     * 检查某日期与当前日期之差是否超过指定值
     *
     * @param date
     * @param limit
     * @return
     */
    public static boolean checkDateLimit(Date date, long limit) {
        if (null == date || limit <= 0) {
            return false;
        }
        // convert Date into Java 8 LocalDate
        LocalDate givenDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate today = LocalDate.now();
        // count number of days between the given date and today
        long days = ChronoUnit.DAYS.between(givenDate, today);
        return days > limit;
    }

    /**
     * 两个时间差，单位秒
     *
     * @param startTime
     * @param endTime
     * @return
     */
    public static int secondDiff(Date startTime, Date endTime) {
        if (null == startTime || null == endTime) {
            return 0;
        }
        try {
            long diff = endTime.getTime() - startTime.getTime();
            return (int) (diff / 1000);
        } catch (Exception e) {
            return 0;
        }
    }


    /**
     * 判断当前时间是否在[startTime, endTime]区间，注意三个参数的时间格式要一致
     *
     * @param nowTime
     * @param startTime
     * @param endTime
     * @return 在时间段内返回true，不在返回false
     */
    public static boolean isEffectiveDate(Date nowTime, Date startTime, Date endTime) {
        if (nowTime.getTime() == startTime.getTime()
                || nowTime.getTime() == endTime.getTime()) {
            return true;
        }

        Calendar date = Calendar.getInstance();
        date.setTime(nowTime);

        Calendar begin = Calendar.getInstance();
        begin.setTime(startTime);

        Calendar end = Calendar.getInstance();
        end.setTime(endTime);

        return date.after(begin) && date.before(end);
    }

    public static Date toTotalTime(String dateString) {
        if (StringUtils.isEmpty(dateString)) {
            return null;
        }
        return toDate(dateString, YYYY_MM_DD_HH_MM_SS_PATTERN);
    }

    public static String getCurrentDateTimeAllStr() {
        Date currentTime = new Date();
        SimpleDateFormat formatter = new SimpleDateFormat(YYYY_MM_DD_HH_MM_SS_PATTERN);
        return formatter.format(currentTime);
    }

    public static LocalDateTime toLocalDateTime(long timestamp) {
        return toLocalDateTime(timestamp, "+8");
    }

    public static LocalDateTime toLocalDateTime(long timestamp, String zone) {
        if (StringUtils.isBlank(zone)) {
            zone = "+8";
        }
        ZoneId zoneId = ZoneId.of(zone);
        return Instant.ofEpochMilli(timestamp).atZone(zoneId).toLocalDateTime();
    }

    public static int differOfYear(LocalDate startDate, LocalDate endDate) {
        // 计算日期差值
        Period period = Period.between(startDate, endDate);
        return period.getYears();
    }

    /**
     * 计算endDateTime-startDateTime 周年年份差
     * @param startDateTime
     * @param endDateTime
     * @return
     */
    public static long differOfYear(LocalDateTime startDateTime, LocalDateTime endDateTime) {
        //endDate-startDate
        return startDateTime.until(endDateTime, ChronoUnit.YEARS);
    }

    /**
     * 对时间进行个数转换 由1640转换为16:40
     * @param time
     */
    public static String timeFormat(String time) {
        // 时间为空 或 长度小于等于2 直接返回原始值
        if (StringUtils.isEmpty(time) || time.length() <= 2){
            return time;
        }
        // 数据包含：直接返回原始值
        if (time.contains(":")) {
            return time;
        }
        return time.substring(0,2) + ":" + time.substring(2);
    }
}
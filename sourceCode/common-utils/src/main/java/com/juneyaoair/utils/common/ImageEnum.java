package com.juneyaoair.utils.common;

/**
 * Created by yaocf on 2017/7/3.
 * 图片压缩尺寸枚举
 */
public enum ImageEnum {
    WEIXUN("WX",555,278),
    POSITION_TOP("PT",640,220),
    POSITION_MIDDLE("PM",640,110),
    POSITION_BOTTOM("PB",600,155),
    POSITION_FLIGHT("PF",640,220),
    CITY("CITY",180,135),//城市图片
    BS("BS",295,370),//特价城市大图
    SS("SS",295,180)//特价城市小图
    ;
    private final String type;
    private final int width;
    private final int height;

    ImageEnum(String type,int width, int height) {
        this.type=type;
        this.width = width;
        this.height = height;
    }

    public int getWidth(){
        return width;
    }

    public int getHeight() {
        return height;
    }

    public String getType() {
        return type;
    }

    //校验类型
    public static ImageEnum getImageSet(String t){
        for (ImageEnum c: ImageEnum.values()) {
            if (c.type.equals(t)) {
                return c;
            }
        }
        return null;
    }
}

package com.juneyaoair.utils.util.sm2;

import org.apache.commons.lang.StringUtils;
import org.bouncycastle.crypto.AsymmetricCipherKeyPair;
import org.bouncycastle.crypto.engines.SM2Engine;
import org.bouncycastle.crypto.generators.ECKeyPairGenerator;
import org.bouncycastle.crypto.params.*;
import org.bouncycastle.jce.ECNamedCurveTable;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.jce.spec.ECParameterSpec;
import org.bouncycastle.math.ec.ECCurve;
import org.bouncycastle.math.ec.ECPoint;
import org.bouncycastle.util.encoders.Base64;

import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.security.Provider;
import java.security.SecureRandom;
import java.security.Security;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2022/1/24  14:10.
 */
public class CrmSM2Util {
    public static final String CURVE_NAME = "sm2p256v1";
    public static final String PRIKEY = "prikey";
    public static final String PUBKEY = "pubkey";
    private static final String ENCODING = StandardCharsets.UTF_8.name();
    private static final Provider PROVIDER = new BouncyCastleProvider();

    static {
        Security.addProvider(PROVIDER);
    }

    private static ECDomainParameters getECDomainParameters() {
        ECParameterSpec spec = ECNamedCurveTable.getParameterSpec(CURVE_NAME);
        return new ECDomainParameters(spec.getCurve(), spec.getG(), spec.getN(), spec.getH(), spec.getSeed());
    }

    private static ECPublicKeyParameters encodePublicKey(byte[] value) {
        byte[] x = new byte[32];
        byte[] y = new byte[32];
        System.arraycopy(value, 1, x, 0, 32);
        System.arraycopy(value, 33, y, 0, 32);
        BigInteger X = new BigInteger(1, x);
        BigInteger Y = new BigInteger(1, y);
        ECCurve curve = ECNamedCurveTable.getParameterSpec(CURVE_NAME).getCurve();
        ECPoint Q = curve.createPoint(X, Y);
        return new ECPublicKeyParameters(Q, getECDomainParameters());
    }

    /**
     * 生成随机秘钥对base64
     *
     * @return
     */
    public static Map<String, String> generateKeyPair() {
        ECDomainParameters domainParameters = getECDomainParameters();
        ECKeyPairGenerator generator = new ECKeyPairGenerator();
        ECKeyGenerationParameters parameters = new ECKeyGenerationParameters(domainParameters, new SecureRandom());
        generator.init(parameters);
        AsymmetricCipherKeyPair key = generator.generateKeyPair();
        ECPrivateKeyParameters ecpriv = (ECPrivateKeyParameters) key.getPrivate();
        ECPublicKeyParameters ecpub = (ECPublicKeyParameters) key.getPublic();
        BigInteger privateKey = ecpriv.getD();
        ECPoint publicKey = ecpub.getQ();
        Map<String, String> map = new HashMap<>();
        map.put(PRIKEY, Base64.toBase64String(privateKey.toByteArray()));
        map.put(PUBKEY, Base64.toBase64String(publicKey.getEncoded(false)));
        return map;
    }

    private static String C1C2C3ToC1C3C2(byte[] cipherText) {
        byte[] bytes = new byte[cipherText.length];
        System.arraycopy(cipherText, 0, bytes, 0, 65);
        System.arraycopy(cipherText, cipherText.length - 32, bytes, 65, 32);
        System.arraycopy(cipherText, 65, bytes, 97, cipherText.length - 97);
        return Base64.toBase64String(bytes);
    }

    private static byte[] C1C3C2ToC1C2C3(byte[] cipherText) {
        byte[] bytes = new byte[cipherText.length];
        System.arraycopy(cipherText, 0, bytes, 0, 65);
        System.arraycopy(cipherText, 97, bytes, 65, cipherText.length - 97);
        System.arraycopy(cipherText, 65, bytes, cipherText.length - 32, 32);
        return bytes;
    }

    /**
     * 数据加密base64
     *
     * @param msg
     * @param publicKey
     * @return
     * @throws Exception
     */
    public static String encrypt(String msg, String publicKey) throws Exception {
        if (StringUtils.isEmpty(publicKey) || StringUtils.isEmpty(msg)) {
            return null;
        }
        byte[] pubKey = Base64.decode(publicKey);
        byte[] msgBytes = msg.getBytes(ENCODING);
        ECPublicKeyParameters ecPublicKey = encodePublicKey(pubKey);
        SM2Engine engine = new SM2Engine();
        engine.init(true, new ParametersWithRandom(ecPublicKey, new SecureRandom()));
        byte[] cipherText = engine.processBlock(msgBytes, 0, msgBytes.length);
        return C1C2C3ToC1C3C2(cipherText);
    }

    /**
     * 数据解密base64
     *
     * @param msg
     * @param privateKey
     * @return
     * @throws Exception
     */
    public static String decrypt(String msg, String privateKey) throws Exception {
        if (StringUtils.isEmpty(privateKey) || StringUtils.isEmpty(msg)) {
            return null;
        }
        byte[] privateKeyByte = Base64.decode(privateKey);
        byte[] msgBytes = Base64.decode(msg);
        byte[] bytes = C1C3C2ToC1C2C3(msgBytes);
        BigInteger d = new BigInteger(1, privateKeyByte);
        ECPrivateKeyParameters priKey = new ECPrivateKeyParameters(d, getECDomainParameters());
        SM2Engine engine = new SM2Engine();
        engine.init(false, priKey);
        byte[] bytes1 = engine.processBlock(bytes, 0, bytes.length);
        return new String(bytes1, ENCODING);
    }
}

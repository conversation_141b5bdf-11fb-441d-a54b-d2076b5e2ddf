package com.juneyaoair.utils.http;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @description  判断request类型
 * @date 2018/10/8  13:38.
 */
public class ReqUtil {
    /**
     * 判断是否是JSON请求
     * @param request
     * @return
     */
    public static Boolean isJsonReq(HttpServletRequest request){
        String header = request.getHeader("content-type");
        return header != null && header.toLowerCase().contains("json");
    }
}

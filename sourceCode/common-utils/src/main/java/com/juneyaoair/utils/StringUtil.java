package com.juneyaoair.utils;

import com.juneyaoair.utils.json.JsonUtil;
import com.juneyaoair.utils.util.MdcUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.apache.commons.lang3.StringUtils;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * String工具类
 */
@Slf4j
public class StringUtil {


    /**
     * 数字正则表达式
     */
    public static final String NUMBER_REG_EX = "[^0-9]";

    /**
     * 格式化字符串
     * <p>
     * 例：formateString("xxx{0}bbb",1) = xxx1bbb
     *
     * @param str
     * @param params
     * @return
     */
    public static String formateString(String str, String... params) {
        for (int i = 0; i < params.length; i++) {
            str = str.replace("{" + i + "}", params[i] == null ? "" : params[i]);
        }
        return str;
    }

    public static String replaceBlank(String str) {
        String dest = "";
        if (str != null) {
            Pattern p = Pattern.compile("\\s*|\t|\r|\n");
            Matcher m = p.matcher(str);
            dest = m.replaceAll("");
        }
        return dest;
    }

    public static boolean isInteger(String value) {
        try {
            Integer.parseInt(value);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    public static boolean isNullOrEmpty(String value) {
        if (value == null || "".equals(value) || "".equals(value.trim()))
            return true;
        return false;
    }

    /**
     * 判断List是否为空
     *
     * @param list
     * @return
     */
    public static boolean isNullOrEmpty(List list) {
        if (list == null || list.size() == 0)
            return true;
        return false;
    }

    /**
     * 判断数组是否为空
     *
     * @param strArry
     * @return
     */
    public static boolean isNullOrEmpty(String[] strArry) {
        if (strArry == null || strArry.length == 0) {
            return true;
        }
        return false;
    }

    public static String newGUID() {
        UUID uuid = UUID.randomUUID();
        return uuid.toString().replace("-", "");
    }

    /**
     * 将请求参数拼成key=value&key=value&key=value&的形式
     *
     * @param map
     * @return
     */
    public static String assemblyGETParam(Map<String, Object> map) {
        if (map == null || map.isEmpty()) {
            return "";
        }
        String par = "";
        for (String key : map.keySet()) {
            String value = map.get(key) == null ? "" : (String) map.get(key);
            par += (key + "=" + value + "&");
        }
        return par.substring(0, par.length() - 1);
    }

    /**
     * 将请求参数拼成key=value&key=value&key=value&的形式  按照key默认升序方式
     *
     * @param map
     * @return
     */
    public static String assemblyGETParam(TreeMap<String, Object> map) {
        if (map == null || map.isEmpty()) {
            return "";
        }
        String par = "";
        for (String key : map.keySet()) {
            Object obj = map.get(key);
            String value = "";
            if (obj instanceof String) {
                value = map.get(key) == null ? "" : (String) map.get(key);
            } else if (obj instanceof Map) {
                value = map.get(key) == null ? "" : JsonUtil.objectToJson(map.get(key));
            }
            par += (key + "=" + value + "&");
        }
        return par.substring(0, par.length() - 1);
    }

    /**
     * <AUTHOR>
     * @Description 提取字符串中的数字
     * @Date 2019/4/11 15:34
     * @Param original
     * @Return 字符串中的数字
     * @Example 1. original = "1A2bc3[ ]4z" return "1234"
     * <p>
     * 2. original = null return null
     **/
    public static String extractNumbers(String original){
        if (StringUtils.isBlank(original)) {
            return null;
        } else {
            Pattern p = Pattern.compile(NUMBER_REG_EX);
            Matcher m = p.matcher(original);
            return m.replaceAll("").trim();
        }
    }

    /**
     * 此方法不做加密，用于解决sonarLint扫描过程中报告的阻断性漏洞
     *
     * @param pwd
     * @return
     */
    public static String encodePwd(String pwd) {
        return pwd;
    }

    /**
     * 过滤器 判断string是否包含在某个结果集中
     *
     * @param list
     * @param source
     * @return
     */
    public static boolean doStringListFilter(List<String> list, String source) {
        if (CollectionUtils.isEmpty(list)) {
            return true;
        } else {
            if (StringUtil.isNullOrEmpty(source)) {
                return false;
            } else {
                return list.contains(source);
            }
        }
    }

    /**
     * @Description:首字母大写，其他小写
     * @Param: No such property: code for class: Script1
     * @return:
     * @Author: zhangwanli
     * @Date: 2019/8/7
     */
    public static String firstToUp(String str) {
        if (str == null || " ".equals(str)) {
            return str;
        }
        if (str.length() > 1) {
            return str.substring(0, 1).toUpperCase() + str.substring(1).toLowerCase();
        }
        return str.toUpperCase();
    }

    /**
     * 根据位数获取指定位数的随机数 至少6位
     *
     * @param num
     * @return
     */
    public static String getChkCode(int num) throws NoSuchAlgorithmException {
        Random random = SecureRandom.getInstance("SHA1PRNG");
        StringBuilder stringBuilder = new StringBuilder();
        if (num < 6) {
            num = 6;
        }
        for (int i = 0; i < num; i++) {
            String rand = String.valueOf(random.nextInt(10));
            stringBuilder.append(rand);
        }
        return stringBuilder.toString();
    }

    /**
     * 统计原字符串中出现目标字符串的个数
     *
     * @param origin
     * @param target
     * @return
     * @Example abcabcdef  abc  return 2
     */
    public static int calculateContainTimes(String origin, String target) {
        if (StringUtils.isEmpty(origin) || StringUtils.isEmpty(target)) {
            return 0;
        }
        int targetLength = target.length();
        int originLength = origin.length();
        return (originLength - origin.replaceAll(target, "").length()) / targetLength;
    }

    /**
     * 截取某个字符串开头的字符串
     *
     * @param originStr 原始字符串
     * @param subStr    需要截取的字符串
     * @return
     */
    public static String subStrFromStart(String originStr, String subStr) {
        if (originStr.startsWith(subStr)) {
            originStr = originStr.substring(subStr.length());
        }
        return originStr;
    }

    /**
     * 字符串增加前缀
     *
     * @param originStr 原始字符串
     * @param prefix    前缀字符串
     * @return
     */
    public static String addPrefix(String originStr, String prefix) {
        if (originStr.startsWith(prefix)) {
            return originStr;
        } else {
            return prefix + originStr;
        }
    }

    /**
     * 判断字符中是否包含指定的正则
     *
     * @param str   原始字符
     * @param regex 正则表达式
     * @return
     */
    public static boolean isContainStr(String str, String regex) {
        Pattern p = Pattern.compile(regex);
        Matcher m = p.matcher(str);
        if (m.find()) {
            return true;
        }
        return false;
    }

    public static String nullToStr(String str) {
        if (StringUtils.isBlank(str)) {
            return "";
        } else {
            return str;
        }
    }

    /**
     * 将Map拼接成URL参数字符串
     * @param params 参数Map
     * @return 拼接后的参数字符串，如：key1=value1&key2=value2
     */
    public static String buildQueryParams(Map<String, String> params) {
        if (params == null || params.isEmpty()) {
            return "";
        }
        StringBuilder queryString = new StringBuilder();
        boolean first = true;
        for (Map.Entry<String, String> entry : params.entrySet()) {
            if (first) {
                first = false;
            } else {
                queryString.append("&");
            }
            // 对键和值进行URL编码
            String encodedKey = urlEncode(entry.getKey());
            String encodedValue = urlEncode(entry.getValue());
            queryString.append(encodedKey).append("=").append(encodedValue);
        }
        return queryString.toString();
    }

    public static String urlEncode(String originStr){
        String encodedKey= originStr;
        try {
            encodedKey = URLEncoder.encode(originStr, "UTF-8");
        } catch (UnsupportedEncodingException e) {
           log.error("{}编码异常：", MdcUtils.getRequestId(),e);
        }
        return encodedKey;
    }

    /**
     * 构建完整URL（基础URL + 参数）
     * @param baseUrl 基础URL（不带参数）
     * @param params 参数Map
     * @return 完整URL
     */
    public static String buildUrl(String baseUrl, Map<String, String> params) {
        String queryString = buildQueryParams(params);
        if (queryString.isEmpty()) {
            return baseUrl;
        }
        // 检查基础URL是否已包含问号
        String separator = baseUrl.contains("?") ? "&" : "?";
        return baseUrl + separator + queryString;
    }
}

package com.juneyaoair.utils.util;

import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.SftpATTRS;
import com.jcraft.jsch.SftpException;
import org.springframework.web.multipart.MultipartFile;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * Created by yaocf on 2018/6/14  16:26.
 */
public class UploadUtil {
    /**
     *
     * @param sftpUtil
     * @param multipartFile
     * @param fileName
     * @param directory   必须是全路径必须是全路径
     * @return
     */
    public static boolean sftpUpload(SFTPUtil sftpUtil, MultipartFile multipartFile,String fileName,String directory){
        try{
            //查看目录是否存在，不存在则创建目录
            ChannelSftp sftp= sftpUtil.getSftp();
            createDir(directory,sftp);
            sftp.cd(directory);
            sftp.put(multipartFile.getInputStream(), fileName);
            return true;
        }catch (Exception e){
            return false;
        }
    }

    /**
     * 设置上传的目录地址
     * @return
     */
    private static String createDirName(){ //只获取年份和月份
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMM");
        //获取当前时间戳
        Date date = new Date();
        String strTime = simpleDateFormat.format(date);
        return strTime;
    }

    /**
     * 创建远程目录
     *
     * @param sftpDirPath
     * @return 返回创建成功或者失败的代码和信息
     * @throws SftpException
     */
    public static String createDir(String sftpDirPath, ChannelSftp sftp) throws SftpException {
        cd("/", sftp);
        if (isDirExist(sftpDirPath, sftp)) {
            return "0:dir  is  exist  !";
        }
        String[] pathArry = sftpDirPath.split("/");
        for (String path : pathArry) {
            if (path.equals("")) {
                continue;
            }
            if (isDirExist(path, sftp)) {
                cd(path, sftp);
            } else {
                //建立目录
                sftp.mkdir(path);
                //进入并设置为当前目录
                sftp.cd(path);
            }
        }
        cd("/", sftp);
        return "1:创建目录成功";
    }
    /**
     * 判断文件是否存在
     *
     * @param directory
     * @param sftp
     * @return
     * @throws SftpException
     */
    public static boolean isDirExist(String directory, ChannelSftp sftp) throws SftpException {
        boolean isDirExistFlag = false;
        try {
            SftpATTRS sftpATTRS = sftp.lstat(directory);
            isDirExistFlag = true;
            return sftpATTRS.isDir();
        } catch (Exception e) {
            if (e.getMessage().toLowerCase().equals("no such file")) {
                isDirExistFlag = false;
            }
        }
        return isDirExistFlag;
    }

    /**
     * 进入指定的目录并设置为当前目录
     *
     * @param sftpPath
     * @throws Exception
     */
    public static void cd(String sftpPath, ChannelSftp sftp) throws SftpException {
        sftp.cd(sftpPath);
    }
}

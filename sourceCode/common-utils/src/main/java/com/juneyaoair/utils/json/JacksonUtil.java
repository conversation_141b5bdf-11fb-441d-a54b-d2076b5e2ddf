package com.juneyaoair.utils.json;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * spring mvc内置的json转换工具
 * 内部实现了对象的单例
 * <AUTHOR>
 * @since 2016-07-21
 */
public class JacksonUtil {

	private static Logger log = LoggerFactory.getLogger(JacksonUtil.class);

	private static ObjectMapper objectMapper = null;

	static {
		if (objectMapper == null) {
			objectMapper = new ObjectMapper();
		}
	}

	private JacksonUtil() {
	
	}

	/**
	 * 转成json序列，并把结果输出成字符串
	 * 
	 * @param obj
	 * @return
	 */
	public static String objectToJson(Object obj) {
		String jsonStr = null;
		if (objectMapper != null) {
			try {
				jsonStr = objectMapper.writeValueAsString(obj);
			} catch (JsonProcessingException e) {
				log.error(e.getMessage(), e);
			}
		}
		return jsonStr;
	}
}
package com.juneyaoair.utils.util;

/**
 * <AUTHOR>
 * @description 敏感信息隐藏方法
 * @date 2024/9/3 0:30
 */
public class SensitiveInfoHider {
    /**
     * 隐藏身份证或护照号码的中间部分字符
     *
     * @param sensitiveInfo 证件号码
     * @param start         隐藏开始的位置（从0开始计数）
     * @param length        需要隐藏的字符数
     * @return 隐藏后的证件号码字符串
     */
    private static String hideMiddleSensitiveInfo(String sensitiveInfo, int start, int length) {
        if (sensitiveInfo == null || sensitiveInfo.length() == 0 || start < 0 || length <= 0 || start + length > sensitiveInfo.length()) {
            // 如果输入无效，返回原始字符串或者抛出异常
            return sensitiveInfo;
        }
        StringBuilder hiddenInfo = new StringBuilder();
        // 保留证件号的前部分
        hiddenInfo.append(sensitiveInfo.substring(0, start));
        // 用星号替换指定位置的字符
        for (int i = 0; i < length; i++) {
            hiddenInfo.append('*');
        }
        // 追加证件号的后部分
        hiddenInfo.append(sensitiveInfo.substring(start + length));
        return hiddenInfo.toString();
    }

    /**
     * 隐藏证件号中间的敏感信息
     * 此方法的目的是保护个人隐私，通过隐藏证件号中间部分的数字或字符来防止信息泄露
     * 当前实现简单地返回一个固定的字符串，但注释中提到了根据不同长度证件号的处理方式
     *
     * @param certNo 证件号字符串，可以是身份证号或其他类型的证件号
     * @return 隐藏中间敏感信息后的证件号字符串
     */
    public static String hideMiddleSensitiveInfo(String certNo) {
        return "******";
        /*
        //超出18位的保留前6位隐藏中间10位
        if(certNo.length()>=18){
           return  hideIdCardMiddle(certNo);
        } else if (certNo.length() == 16) {
            return hideMiddleSensitiveInfo(certNo,6,8);
        } else{
            return hideOtherCardMiddle(certNo);
        }
         */
    }

    /**
     * 隐藏敏感信息根据证件号码的长度不同，应用不同的隐藏规则
     *
     * @param certNo 证件号码字符串
     * @return 隐藏后的证件号码字符串
     */
    public static String hideSensitiveInfo(String certNo) {
        //超出18位的保留前6位隐藏中间10位
        if (certNo.length() >= 18) {
            return hideIdCardMiddle(certNo);
        } else if (certNo.length() == 16) {
            return hideMiddleSensitiveInfo(certNo, 4, 10);
        } else {
            return hideOtherCardNumber(certNo);
        }
    }

    // 隐藏身份证号的中间部分（根据实际情况调整隐藏位数）
    private static String hideIdCardMiddle(String idCardNumber) {
        // 通常身份证号为18位，这里假设我们要隐藏中间的12位（实际情况请按需调整）
        int start = 4; // 从第3位开始隐藏
        int length = 12; // 隐藏12位
        return hideMiddleSensitiveInfo(idCardNumber, start, length);
    }

    // 隐藏中间部分（根据实际情况调整隐藏位数）
    private static String hideOtherCardNumber(String idCardNumber) {
        // 通常身份证号为18位，这里假设我们要隐藏中间的12位（实际情况请按需调整）
        int start = 2; // 从第3位开始隐藏
        int length = idCardNumber.length() - 4; // 隐藏除开头两位,结尾2位的其他位数
        return hideMiddleSensitiveInfo(idCardNumber, start, length);
    }

}

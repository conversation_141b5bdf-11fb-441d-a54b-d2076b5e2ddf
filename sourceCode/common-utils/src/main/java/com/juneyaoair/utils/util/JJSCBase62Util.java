package com.juneyaoair.utils.util;

import java.math.BigInteger;

/**
 * @description 此方法由竞价升舱系统提供，不具备通用性
 * <AUTHOR>
 * @date 2025/6/6 10:01
 **/
public class JJSCBase62Util {
    private static final String BASE62 = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";

    // 编码
    public static String encodeBase62(byte[] data) {
        BigInteger number = new BigInteger(1, data); // 保证正数
        StringBuilder sb = new StringBuilder();

        while (number.compareTo(BigInteger.ZERO) > 0) {
            BigInteger[] divmod = number.divideAndRemainder(BigInteger.valueOf(62));
            sb.append(BASE62.charAt(divmod[1].intValue()));
            number = divmod[0];
        }

        return sb.reverse().toString();
    }

    // 解码
    public static byte[] decodeBase62(String base62Str) {
        BigInteger number = BigInteger.ZERO;
        for (int i = 0; i < base62Str.length(); i++) {
            number = number.multiply(BigInteger.valueOf(62));
            number = number.add(BigInteger.valueOf(BASE62.indexOf(base62Str.charAt(i))));
        }
        return number.toByteArray();
    }
}

package com.juneyaoair.utils.util;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;

/**
 * Created by ji<PERSON> on 2016-07-28.
 */

public class FileUtils {
    private static Logger logger = LoggerFactory.getLogger(FileUtils.class.getName());

    private final static String PATH = "/jidouRule.json";
    private final static String PATH1 = "/wifiInfo.json";
    private static String readFile0(){
        BufferedReader reader = null;
        String laststr = "";
        try{
            InputStream inputStream = FileUtils.class.getResourceAsStream(PATH);
            InputStreamReader inputStreamReader = new InputStreamReader(inputStream, "UTF-8");
            reader = new BufferedReader(inputStreamReader);
            String tempString = null;
            while((tempString = reader.readLine()) != null){
                laststr += tempString;
            }
            reader.close();
        }catch(IOException e){
            logger.error(e.getMessage(), e);
        }finally{
            if(reader != null){
                try {
                    reader.close();
                } catch (IOException e) {
                    logger.error(e.getMessage(), e);
                }
            }
        }
        return laststr;
    }

    private static String readFile(String path){
        BufferedReader reader = null;
        String laststr = "";
        try{
            InputStream inputStream = FileUtils.class.getResourceAsStream(path);
            InputStreamReader inputStreamReader = new InputStreamReader(inputStream, "UTF-8");
            reader = new BufferedReader(inputStreamReader);
            String tempString = null;
            while((tempString = reader.readLine()) != null){
                laststr += tempString;
            }
            reader.close();
        }catch(IOException e){
            logger.error(e.getMessage(), e);
        }finally{
            if(reader != null){
                try {
                    reader.close();
                } catch (IOException e) {
                    logger.error(e.getMessage(), e);
                }
            }
        }
        return laststr;
    }


    public static int getJidouFromRule(int signedNum){

        String JsonContext = FileUtils.readFile0();
        JSONArray jsonArray = JSONArray.fromObject(JsonContext);
        JSONObject jsonObject = jsonArray.getJSONObject(signedNum);
        return (int) jsonObject.get("jidou");
    }

    public static String readJson(){
        String JsonContext = FileUtils.readFile0();
        JSONArray jsonArray = JSONArray.fromObject(JsonContext);
        return jsonArray.toString();
    }

    public static String readWifiJson(){
        return FileUtils.readFile(FileUtils.PATH1);
    }


    public static String readJson(String path){
        return FileUtils.readFile(path);
    }
    public static String readJsonByPath(String path){
        return  FileUtils.loadFile(path);
    }

    /**
     *文件读取
     * @param path
     * @return
     */
    public static String loadFile(String path){
        BufferedReader reader = null;
        InputStream inputStream = null;
        InputStreamReader inputStreamReader = null;
        String laststr = "";
        try{
            inputStream = new FileInputStream(path);
            inputStreamReader = new InputStreamReader(inputStream, "UTF-8");
            reader = new BufferedReader(inputStreamReader);
            String tempString = null;
            while((tempString = reader.readLine()) != null){
                laststr += tempString;
            }
        }catch(IOException e){
            logger.error(e.getMessage(), e);
        }finally{
            if(reader != null){
                try {
                    reader.close();
                } catch (IOException e) {
                    logger.error(e.getMessage(), e);
                }
            }
            if (null != inputStream) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    logger.error(e.getMessage(), e);
                }
            }
            if (null != inputStreamReader) {
                try {
                    inputStreamReader.close();
                } catch (IOException e) {
                    logger.error(e.getMessage(), e);
                }
            }
        }
        return laststr;
    }

    /**
     * 文件流写入本地方法
     * @param destination
     * @param input
     * @throws Exception
     */
    public static void writeToLocal(String destination, InputStream input) throws Exception{
        File file = new File(destination);
        //判断父目录是否存在，如果不存在，则创建
        if (file.getParentFile() != null && !file.getParentFile().exists()) {
            file.getParentFile().mkdirs();
        }
        if(!file.exists()){
            //不做业务处理
            if (!file.createNewFile()) {
                logger.info("文件已存在:{}",file);
            }
        }
        FileOutputStream downloadFile = new FileOutputStream(file);;
        try {
            int index;
            byte[] bytes = new byte[1024];
            while ((index = input.read(bytes)) != -1) {
                downloadFile.write(bytes, 0, index);
                downloadFile.flush();
            }
        } finally {
            downloadFile.close();
            input.close();
        }
    }
}

package com.juneyaoair.utils.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.net.URL;

/**
 * 修图工具类
 * 
 * <AUTHOR>
 * 
 */
public class GraphicsUtil {
	private Logger log = LoggerFactory.getLogger(this.getClass());

	private Font font = null;// 添加字体的属性设置

	private Graphics2D g = null;

	private int fontsize = 0;

	private int x = 0;

	private int y = 0;

	/**
	 * 导入本地图片到缓冲区
	 */
	public BufferedImage loadImageLocal(String imgName) {
		try {
				return ImageIO.read(new File(imgName));
		} catch (IOException e) {
			return null;
		}
	}

	/**
	 * 导入网络图片到缓冲区
	 */
	public BufferedImage loadImageUrl(String imgName) {
		try {
			URL url = new URL(imgName);
			return ImageIO.read(url);
		} catch (IOException e) {
			System.out.println(e.getMessage());
		}
		return null;
	}

	/**
	 * 生成新图片到本地
	 */
	public String writeImageLocal(String newImage, BufferedImage img) {
		String newImagePath = null;
		if (newImage != null && img != null) {
			try {
				File outputfile = new File(newImage);
				ImageIO.write(img, "jpg", outputfile);
				img.flush();
				img = null;
				newImagePath = outputfile.getPath();
			} catch (Exception e) {
				if (null != img) {
					img.flush();
				}
				img = null;
			}

		}
		return newImagePath;
	}

	/**
	 * 设定文字的字体等
	 */
	public void setFont(String fontStyle, int fontSize) {
		this.fontsize = fontSize;
		this.font = new Font(fontStyle, Font.BOLD, fontSize);
	}

	/**
	 * 修改图片,返回修改后的图片缓冲区（只输出一行文本）
	 *
	 */
	public BufferedImage modifyImage(BufferedImage img, Object content, int x,
			int y) {

		try {
			int w = img.getWidth();
			int h = img.getHeight();
			g = img.createGraphics();
			g.setBackground(Color.WHITE);
			g.setColor(Color.white);
			if (this.font != null) {
                g.setFont(this.font);
            }
			// 验证输出位置的纵坐标和横坐标
			if (x >= h || y >= w) {
				this.x = h - this.fontsize + 2;
				this.y = w;
			} else {
				this.x = x;
				this.y = y;
			}
			if (content != null) {
				g.drawString(content.toString(), this.x, this.y);
			}
			g.dispose();
		} catch (Exception e) {
			System.out.println(e.getMessage());
		}

		return img;
	}

	/**
	 * 修改图片,返回修改后的图片缓冲区（输出多个文本段） xory：true表示将内容在一行中输出；false表示将内容多行输出
	 * 
	 */
	public BufferedImage modifyImage(BufferedImage img, Object[] contentArr,
			int x, int y, boolean xory,Color color) {
		try {
			int w = img.getWidth();
			int h = img.getHeight();
			g = img.createGraphics();
			g.setBackground(Color.WHITE);
			g.setColor(color);
			if (this.font != null) {
                g.setFont(this.font);
            }
			// 验证输出位置的纵坐标和横坐标
			if (x >= h || y >= w) {
				this.x = h - this.fontsize + 2;
				this.y = w;
			} else {
				this.x = x;
				this.y = y;
			}
			if (contentArr != null) {
				int arrlen = contentArr.length;
				if (xory) {
					for (int i = 0; i < arrlen; i++) {
						g.drawString(contentArr[i].toString(), this.x, this.y);
						this.x += contentArr[i].toString().length()
								* this.fontsize / 2 + 5;// 重新计算文本输出位置
					}
				} else {
					for (int i = 0; i < arrlen; i++) {
						g.drawString(contentArr[i].toString(), this.x, this.y);
						this.y += this.fontsize - 5;// 重新计算文本输出位置
					}
				}
			}
			g.dispose();
			img.flush();
		} catch (Exception e) {
			log.error("卡样绘制会员信息出错："+e.getMessage());
		}
		return img;
	}

	/**
	 * 在卡片上粘贴二维码
	 * @param img
	 * @param logoImg
	 * @param x
	 * @param y
     * @return
     */
	public BufferedImage modifyImage(BufferedImage img,BufferedImage logoImg,int x, int y){
		try{
			this.x=x;
			//this.y=y;
			int w=img.getWidth();
			//图片高度
			int h=img.getHeight();
			int width=logoImg.getWidth();//二维码宽度
			int height=logoImg.getHeight();
			g = img.createGraphics();
			//调整二维码绘制坐标
			this.y=(h-height)/2+30;//垂直居中+调节系数
			//画笔大小
			g.setStroke(new BasicStroke(2));
			//将二维码画到名片上
			g.drawImage(logoImg,this.x,this.y,null);
			//g.drawRoundRect(this.x, this.y, width, height, 0 ,0);//绘制圆角矩形
			g.drawRect(this.x, this.y, width, height);//绘制矩形
			g.dispose();
		}catch (Exception e){
			log.error("电子卡二维码绘制异常:"+e.getMessage());
		}
        return img;
	}

	/**
	 * @param args
	 */
	public static void main(String[] args) {
		/*
		 * Test test = new Test(); String prePath =
		 * test.getClass().getResource("/").getPath()+"com//"; BufferedImage img
		 * = test.loadImageLocal(prePath+"123.jpg");
		 * test.setFont("Times New Roman", 30); String contentArr[] = new
		 * String[3]; contentArr[0] = "NO  1234567890"; contentArr[1] = "";
		 * contentArr[2] = "NAME  ZHUANG  JINLEI"; test.modifyImage(img,
		 * contentArr, 112, 502, false);
		 * test.writeImageLocal(prePath+"12345.jpg", img);
		 */
		GraphicsUtil test = new GraphicsUtil();
		System.out.println(test.getClass());
		System.out.println(test.getClass().getResource("/"));
		System.out.println(test.getClass().getResource("/").getPath());

	}

}

package com.juneyaoair.thirdentity.commonflycity;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CommonFlyCityReqDTO {
    private String id;
    private String cfcId;
    private String cityCode;
    private String cityName;
    private String cityEName;
    private String cityPinYin;
    private String isInternational;
    private String countryCode;
    @ApiModelProperty(value = "规则类型", example = "1,2", notes = "1:邻近航线,2:热门推荐,为空兼容默认查询邻近航线")
    private String ruleType;
}

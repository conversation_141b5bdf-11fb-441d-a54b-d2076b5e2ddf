package com.juneyaoair.thirdentity.specialrefundapply.request;

import com.juneyaoair.thirdentity.request.PtBaseRequest;
import lombok.Data;

import java.util.List;

/**
 * @ClassName RefundApplyRequestDto
 * <AUTHOR>
 * @Description
 * @Date 2021-01-18 15:28
 **/
@Data
public class RefundApplyRequestDto extends PtBaseRequest {
    private String FfpId; // 会员ID
    private String FfCardNo; //用户卡号
    private String TicketNo; //票号
    private String ApplicantName; //申请人姓名
    private String ApplicantMobile; //申请人手机号
    private String ApplicantEmail; //申请人邮箱
    private String RefundReason; //退票原因
    private List<String> RefundFiles; //退票附件地址列表
    public RefundApplyRequestDto(String version, String channelCode, String userNo){
        super(version,channelCode,userNo);
    }
}

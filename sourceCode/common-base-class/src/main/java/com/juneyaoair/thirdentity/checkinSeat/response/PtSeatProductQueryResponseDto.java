package com.juneyaoair.thirdentity.checkinSeat.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.NotBlank;

import java.util.List;

/**
 * @Author:zc
 * @Description:
 * @Date:Created in 17:08 2019/4/15
 * @Modified by:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PtSeatProductQueryResponseDto {
    private String Version;
    private String ChannelCode;
    private String UserNo;
    private String ResultCode;
    private String ErrorInfo;
    private String PlaneStyle;
    private String Cabin;
    private String DepDate;
    private String DepDateStr;
    private String DepAirport;
    private String ArrAirport;
    private String FType;
    private List<PtSeatChartDto> SeatMapList;
}

package com.juneyaoair.thirdentity.salecoupon.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description 订单返回基础信息
 * @created 2023/9/8 10:07
 */
@Data
public class OrderBaseResult {

    @ApiModelProperty(value = "版本号", required = true)
    private String Version;

    @ApiModelProperty(value = "渠道", required = true)
    private String ChannelCode;

    @ApiModelProperty(value = "返回编码 10001：成功，其它失败", required = true)
    private String ResultCode;

    @ApiModelProperty(value = "错误信息", required = true)
    private String ErrorInfo;
}

package com.juneyaoair.thirdentity.salecoupon.v2.request;

import com.google.gson.annotations.SerializedName;
import com.juneyaoair.baseclass.newcoupon.bean.SingleBookCondition;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @date 2020/7/29  18:27.
 */
@Data
public class ProductQueryRequestDto {
    @SerializedName("SearchTypes")
    private String searchTypes;
    @SerializedName("Category")
    private String category;
    @SerializedName("SingleBookCondition")
    private SingleBookCondition singleBookCondition;

    @ApiModelProperty("产品名称")
    @SerializedName("ProName")
    private String proName;

    //多个可以用 , 进行分割
    @ApiModelProperty("产品唯一标识")
    @SerializedName("ProNum")
    private String proNum;

    /**
     * 航线类型（国内-DOMESTIC，国际-INTL）
     */
    @SerializedName(value="FlightType")
    private String flightType;

    public SingleBookCondition createSingleBookCondition(String depCity,String arrCity){
        SingleBookCondition singleBookCondition = new SingleBookCondition();
        singleBookCondition.setDepCityCode(depCity);
        singleBookCondition.setArrCityCode(arrCity);
        singleBookCondition.setFlightType("OW");
        return singleBookCondition;
    }
}

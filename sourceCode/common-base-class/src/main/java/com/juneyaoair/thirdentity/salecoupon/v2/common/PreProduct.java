package com.juneyaoair.thirdentity.salecoupon.v2.common;/*
 *@program:ho-mobileapi
 *@author: <PERSON>
 *@Time: 2021/8/12  8:37
 *@description:
 */

import com.google.gson.annotations.SerializedName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import java.util.Date;
import java.util.List;

@Data
public class PreProduct {
    @NotBlank(message = "产品ID不可为空")
    @SerializedName("ProductId")
    private String productId;

    @NotBlank(message = "投放ID不可为空")
    @SerializedName("BookingId")
    private String bookingId;

    @NotBlank(message = "使用规则ID不可为空")
    @SerializedName("RuleId")
    private String ruleId;

    @NotBlank(message = "产品类型不可为空")
    @SerializedName("ProductType")
    private String productType;

    @NotBlank(message = "产品类型不可为空")
    @SerializedName("Category")
    private String category;

    @SerializedName("ProductName")
    private String productName;

    @SerializedName("SalePrice")
    private double salePrice;//产品销售价

    @SerializedName("OriginalPrice")
    private double originalPrice;//机场价格（额外行李专用）

    @SerializedName("StandardPrice")
    private double standardPrice;//产品标准价格

    @SerializedName("ProductSku")
    private String productSku;

    @SerializedName("SaleTotalCount")
    private int saleTotalCount;//产品已售数量

    @SerializedName("VaildityDays")
    private int vaildityDays;

    @SerializedName("LatestDate")
    private Date latestDate;

    @SerializedName("Remark")
    private String remark;

    @SerializedName("AvailPrdStock")
    private int availPrdStock;//库存

    @SerializedName("ProductRuleLimit")
    private ProductRuleLimit productRuleLimit;

    @SerializedName("BookingCount")
    private int bookingCount;//总的可购张数

    @SerializedName("RemainedCount")
    private int remainedCount;//剩余可购张数

    @SerializedName("TransferType")
    private String transferType; // 礼宾接机类型

    @SerializedName("FlightType")
    private String flightType; // 航线类型

    @SerializedName("AirportLocation")
    private String airportLocation; // 贵宾休息室 所在机场


    @SerializedName("ScheduledCommission")
    private double scheduledCommission; // 预定提成比例

    @SerializedName("Cost")
    private double cost; // 成本

    @SerializedName("Delivery")
    private String delivery; // 配送方式

    @SerializedName("BasicStock")
    private int basicStock; // 基础库存（航班号）

    @SerializedName("Channels")
    private String channels; // 渠道

    @SerializedName("BookingLimit")
    private BookingLimit bookingLimit; // 适用限制

    @NotBlank(message = "活动ID不可为空")
    @SerializedName("ProductNum")
    private String productNum;//所属活动编号，productId+bookingId+ruleId的组合ID

    @SerializedName("StockFlag")
    private String stockFlag; // 是否存在库存

    @SerializedName("ProFields")
    private List<ProField> proFields;

    @ApiModelProperty(value = "单价产品", notes = "前端控制字段,表明为预付行李单价产品", example = "true")
    private boolean unitPriceProduct;

    /**
     * @see com.juneyaoair.baseclass.response.prepaymentBaggage.Pricing
     * @see com.juneyaoair.mobile.handler.config.bean.HandConfig#extraBaggagePricing
     */
    @ApiModelProperty(value = "自定义订单下单重量限额", example = "30")
    private int perOrderWeightLimit;


    @ApiModelProperty(value = "新产品自定义购买KG数", example = "3",notes = "单位KG")
    private int count;


}

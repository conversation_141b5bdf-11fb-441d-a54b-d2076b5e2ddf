package com.juneyaoair.thirdentity.response.speedRefund;

import com.juneyaoair.baseclass.ticketInfo.Segment;
import com.juneyaoair.baseclass.ticketInfo.SegmentInfo;

import java.util.List;

/**
 * Created by yaocf on 2017/1/18.
 */
public class PtTicketPayoutInfo {
    private Double RefundAmount; //实退金额
    private Double PriceAmount;//票价
    private Double RefundDeduction;//退票手续费
    private Double YQTax;//燃油费
    private Double CNTax;//建设税
    private String DepAirport;//起飞机场三字码
    private String ArrAirport;//到达机场三字码
    private String DepCity;//起飞城市
    private String ArrCity;//到达城市
    private String DepDateTime;//起飞时间
    private String FlightNo;//航班编号
    private String TicketNo;//客票号
    private String TicketStatus;//客票状态
    private String SegmentStatus;//航段状态
    private String TicketPayoutState;//退票状态
    private String ApplyDate;//退票申请日期
    private Double TotalAmount;//客票总金额
    private String MaskBankAccountNO;//银行账户
    private String RouteType;//航程类型  OW单程  RT往返

    private String ChannelPayoutNo; //渠道退款编号
    private String TicketPayoutNO;  //客票退款编号
    private String IsVoluntaryRefund;  //是否自愿退票  Y是N否
    private String HandphoneNo;
    private String PassengerName;
    private String MaskBankCertNo;
    private String PayoutErrorInfo;  //失败原因

    private List<Segment> SegmentList; //航段信息

    public List<Segment> getSegmentList() {
        return SegmentList;
    }

    public void setSegmentList(List<Segment> SegmentList) {
        this.SegmentList = SegmentList;
    }

    public Double getRefundAmount() {
        return RefundAmount;
    }

    public void setRefundAmount(Double refundAmount) {
        RefundAmount = refundAmount;
    }

    public Double getPriceAmount() {
        return PriceAmount;
    }

    public void setPriceAmount(Double priceAmount) {
        PriceAmount = priceAmount;
    }

    public Double getRefundDeduction() {
        return RefundDeduction;
    }

    public void setRefundDeduction(Double refundDeduction) {
        RefundDeduction = refundDeduction;
    }

    public Double getYQTax() {
        return YQTax;
    }

    public void setYQTax(Double YQTax) {
        this.YQTax = YQTax;
    }

    public Double getCNTax() {
        return CNTax;
    }

    public void setCNTax(Double CNTax) {
        this.CNTax = CNTax;
    }

    public String getDepAirport() {
        return DepAirport;
    }

    public void setDepAirport(String depAirport) {
        DepAirport = depAirport;
    }

    public String getArrAirport() {
        return ArrAirport;
    }

    public void setArrAirport(String arrAirport) {
        ArrAirport = arrAirport;
    }

    public String getDepCity() {
        return DepCity;
    }

    public void setDepCity(String depCity) {
        DepCity = depCity;
    }

    public String getArrCity() {
        return ArrCity;
    }

    public void setArrCity(String arrCity) {
        ArrCity = arrCity;
    }

    public String getDepDateTime() {
        return DepDateTime;
    }

    public void setDepDateTime(String depDateTime) {
        DepDateTime = depDateTime;
    }

    public String getFlightNo() {
        return FlightNo;
    }

    public void setFlightNo(String flightNo) {
        FlightNo = flightNo;
    }

    public String getTicketNo() {
        return TicketNo;
    }

    public void setTicketNo(String ticketNo) {
        TicketNo = ticketNo;
    }

    public String getTicketStatus() {
        return TicketStatus;
    }

    public void setTicketStatus(String ticketStatus) {
        TicketStatus = ticketStatus;
    }

    public String getSegmentStatus() {
        return SegmentStatus;
    }

    public void setSegmentStatus(String segmentStatus) {
        SegmentStatus = segmentStatus;
    }

    public String getTicketPayoutState() {
        return TicketPayoutState;
    }

    public void setTicketPayoutState(String ticketPayoutState) {
        TicketPayoutState = ticketPayoutState;
    }

    public String getApplyDate() {
        return ApplyDate;
    }

    public void setApplyDate(String applyDate) {
        ApplyDate = applyDate;
    }

    public Double getTotalAmount() {
        return TotalAmount;
    }

    public void setTotalAmount(Double totalAmount) {
        TotalAmount = totalAmount;
    }

    public String getMaskBankAccountNO() {
        return MaskBankAccountNO;
    }

    public void setMaskBankAccountNO(String maskBankAccountNO) {
        MaskBankAccountNO = maskBankAccountNO;
    }

    public String getIsVoluntaryRefund() {
        return IsVoluntaryRefund;
    }

    public void setIsVoluntaryRefund(String isVoluntaryRefund) {
        IsVoluntaryRefund = isVoluntaryRefund;
    }

    public String getRouteType() {
        return RouteType;
    }

    public void setRouteType(String routeType) {
        RouteType = routeType;
    }

    public String getChannelPayoutNo() {
        return ChannelPayoutNo;
    }

    public void setChannelPayoutNo(String channelPayoutNo) {
        ChannelPayoutNo = channelPayoutNo;
    }

    public String getTicketPayoutNO() {
        return TicketPayoutNO;
    }

    public void setTicketPayoutNO(String ticketPayoutNO) {
        TicketPayoutNO = ticketPayoutNO;
    }

    public String getPayoutErrorInfo() {
        return PayoutErrorInfo;
    }

    public void setPayoutErrorInfo(String payoutErrorInfo) {
        PayoutErrorInfo = payoutErrorInfo;
    }

    public String getHandphoneNo() {
        return HandphoneNo;
    }

    public void setHandphoneNo(String handphoneNo) {
        HandphoneNo = handphoneNo;
    }

    public String getMaskBankCertNo() {
        return MaskBankCertNo;
    }

    public void setMaskBankCertNo(String maskBankCertNo) {
        MaskBankCertNo = maskBankCertNo;
    }

    public String getPassengerName() {
        return PassengerName;
    }

    public void setPassengerName(String passengerName) {
        PassengerName = passengerName;
    }
}

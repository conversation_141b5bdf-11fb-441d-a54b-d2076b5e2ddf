package com.juneyaoair.thirdentity.salecoupon.request;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description 权益使用校验 请求参数
 * @date 2019/2/28  10:07.
 */
@Data
@NoArgsConstructor
public class PtCouponProductUseCheckRequestDto {
    private String Version;
    private String ChannelCode;
    private String FfpId;
    private String FfpCardNo;
    private String VoucherNo;  //权益凭证号
    private List<PtCouponCheckSegmentInfo> SegmentInfoList;
    public PtCouponProductUseCheckRequestDto(String version,String channelCode,String ffpId,String ffpCardNo,String voucherNo){
        this.Version = version;
        this.ChannelCode = channelCode;
        this.FfpId = ffpId;
        this.FfpCardNo =  ffpCardNo;
        this.VoucherNo = voucherNo;

    }
}


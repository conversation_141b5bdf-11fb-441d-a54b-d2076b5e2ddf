package com.juneyaoair.thirdentity.salecoupon.response;

import com.juneyaoair.thirdentity.salecoupon.common.ProductInfo;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description 产品查询返回类
 * @date 2019/4/30  17:08.
 */
@Data
public class PtProductQueryResponseDto {
    private String Version;
    private String ChannelCode;
    private String ResultCode;
    private String ErrorInfo;
    private List<ProductInfo> ProductList;
    private int TotalRecords; //产品总数
    private int TotalPages;  //总页数
}

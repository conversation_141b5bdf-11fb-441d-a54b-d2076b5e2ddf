package com.juneyaoair.thirdentity.response.checkin;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;

@XmlRootElement(name = "CkiInfo")
@XmlAccessorType(XmlAccessType.FIELD)
public class CkiInfo{
	private String flightNo; // 航班号
	private String flightDate; //起飞日期
	private String deptAirport; //起飞机场
	private String fromCityNm;
	private String toCityNm;
	private String fromCityNmEn;
	private String toCityNmEn;	
	private String psrEnName; //英文名
	private String psrCnName;; //中文名
	private String gender;//性别
	private String seatNo;//座位号 
	private String asrSeat;
	private String cabin; //
	private String schDeptTime; //计划起飞时间
	private String arriveAirport; //到达机场
	private String expDeptTime; //预计起飞时间
	private String isFlightOpened;
	private String bordingTime; //登机时间
	private String boardingGateNumber; //登机口号
	private String boardingNo; //登机号
	private String planeType; //
	private String boardStream;
	private String phone;
	private String etCode; //票号电子
	private String checkInStat; //
	private String checkInDate;
	private String checkCode;//值机成功验证码
	private String tourIndex; //航段序号
	private String tourClass;//舱位等级
	private String cabinArea; //
	private String pstCert; //
	
	private String cardID; //
	private String cardAirLine; //
	
	private String checkInIp;
	private String checkInChannel;
	
	
	//private String groupCode; //
	//private String groupNumber; //
	//private String ckiPAEmail; //
	
	//private String ckiPAPcode; //渠道编代码
	
	public String getFlightNo() {
		return flightNo;
	}
	public void setFlightNo(String flightNo) {
		this.flightNo = flightNo;
	}
	public String getFlightDate() {
		return flightDate;
	}
	public void setFlightDate(String flightDate) {
		this.flightDate = flightDate;
	}
	public String getPsrEnName() {
		return psrEnName;
	}
	public void setPsrEnName(String psrEnName) {
		this.psrEnName = psrEnName;
	}
	public String getPsrCnName() {
		return psrCnName;
	}
	public void setPsrCnName(String psrCnName) {
		this.psrCnName = psrCnName;
	}
	public String getCabin() {
		return cabin;
	}
	public void setCabin(String cabin) {
		this.cabin = cabin;
	}
	public String getCardID() {
		return cardID;
	}
	public void setCardID(String cardID) {
		this.cardID = cardID;
	}
	public String getCardAirLine() {
		return cardAirLine;
	}
	public void setCardAirLine(String cardAirLine) {
		this.cardAirLine = cardAirLine;
	}
	public String getSchDeptTime() {
		return schDeptTime;
	}
	public void setSchDeptTime(String schDeptTime) {
		this.schDeptTime = schDeptTime;
	}
	public String getExpDeptTime() {
		return expDeptTime;
	}
	public void setExpDeptTime(String expDeptTime) {
		this.expDeptTime = expDeptTime;
	}
	public String getBordingTime() {
		return bordingTime;
	}
	public void setBordingTime(String bordingTime) {
		this.bordingTime = bordingTime;
	}
	public String getBoardingGateNumber() {
		return boardingGateNumber;
	}
	public void setBoardingGateNumber(String boardingGateNumber) {
		this.boardingGateNumber = boardingGateNumber;
	}
	public String getPlaneType() {
		return planeType;
	}
	public void setPlaneType(String planeType) {
		this.planeType = planeType;
	}
	public String getCabinArea() {
		return cabinArea;
	}
	public void setCabinArea(String cabinArea) {
		this.cabinArea = cabinArea;
	}
	public String getPstCert() {
		return pstCert;
	}
	public void setPstCert(String pstCert) {
		this.pstCert = pstCert;
	}
	
	public String getCheckInStat() {
		return checkInStat;
	}
	public void setCheckInStat(String checkInStat) {
		this.checkInStat = checkInStat;
	}
	public String getEtCode() {
		return etCode;
	}
	public void setEtCode(String etCode) {
		this.etCode = etCode;
	}
	public String getDeptAirport() {
		return deptAirport;
	}
	public void setDeptAirport(String deptAirport) {
		this.deptAirport = deptAirport;
	}
	public String getTourIndex() {
		return tourIndex;
	}
	public void setTourIndex(String tourIndex) {
		this.tourIndex = tourIndex;
	}
	public String getSeatNo() {
		return seatNo;
	}
	public void setSeatNo(String seatNo) {
		this.seatNo = seatNo;
	}
	public String getBoardStream() {
		return boardStream;
	}
	public void setBoardStream(String boardStream) {
		this.boardStream = boardStream;
	}
	public String getGender() {
		return gender;
	}
	public void setGender(String gender) {
		this.gender = gender;
	}
	public String getAsrSeat() {
		return asrSeat;
	}
	public void setAsrSeat(String asrSeat) {
		this.asrSeat = asrSeat;
	}
	public String getArriveAirport() {
		return arriveAirport;
	}
	public void setArriveAirport(String arriveAirport) {
		this.arriveAirport = arriveAirport;
	}
	public String getIsFlightOpened() {
		return isFlightOpened;
	}
	public void setIsFlightOpened(String isFlightOpened) {
		this.isFlightOpened = isFlightOpened;
	}
	public String getBoardingNo() {
		return boardingNo;
	}
	public void setBoardingNo(String boardingNo) {
		this.boardingNo = boardingNo;
	}
	public String getPhone() {
		return phone;
	}
	public void setPhone(String phone) {
		this.phone = phone;
	}
	public String getCheckInDate() {
		return checkInDate;
	}
	public void setCheckInDate(String checkInDate) {
		this.checkInDate = checkInDate;
	}
	public String getCheckCode() {
		return checkCode;
	}
	public void setCheckCode(String checkCode) {
		this.checkCode = checkCode;
	}	
	public String getCheckInIp() {
		return checkInIp;
	}
	public void setCheckInIp(String checkInIp) {
		this.checkInIp = checkInIp;
	}
	public String getCheckInChannel() {
		return checkInChannel;
	}
	public void setCheckInChannel(String checkInChannel) {
		this.checkInChannel = checkInChannel;
	}
	
	public String getFromCityNm() {
		return fromCityNm;
	}
	public void setFromCityNm(String fromCityNm) {
		this.fromCityNm = fromCityNm;
	}
	public String getToCityNm() {
		return toCityNm;
	}
	public void setToCityNm(String toCityNm) {
		this.toCityNm = toCityNm;
	}
	public String getFromCityNmEn() {
		return fromCityNmEn;
	}
	public void setFromCityNmEn(String fromCityNmEn) {
		this.fromCityNmEn = fromCityNmEn;
	}
	public String getToCityNmEn() {
		return toCityNmEn;
	}
	public void setToCityNmEn(String toCityNmEn) {
		this.toCityNmEn = toCityNmEn;
	}
	
	public String getTourClass() {
		return tourClass;
	}
	public void setTourClass(String tourClass) {
		this.tourClass = tourClass;
	}
	public CkiInfo(){}
	   
   
    

}

package com.juneyaoair.thirdentity.salecoupon.common;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

/**
 * <AUTHOR>
 * @description 随身wifi特有属性
 * @date 2019/5/9  13:04.
 */
@Data
public class WIFIExtInfo {
    /**
     * 设备押金
     */
    @SerializedName("Deposit")
    private Double deposit;
    /**
     * 提前预定天数
     */
    @SerializedName("AdvanceDays")
    private Integer advanceDays;
    /**
     * 提前预定小时数
     */
    @SerializedName("AdvanceHours")
    private Integer advanceHours;
    /**
     * 最小预定天数
     */
    @SerializedName("MinBookDays")
    private Integer minBookDays;
}

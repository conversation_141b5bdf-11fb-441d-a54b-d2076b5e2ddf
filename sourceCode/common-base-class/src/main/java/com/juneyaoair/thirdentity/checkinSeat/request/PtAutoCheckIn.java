package com.juneyaoair.thirdentity.checkinSeat.request;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName PtAutoCheckIn
 * @Description
 * @createTime 2022年01月10日 14:13
 */
@Data
public class PtAutoCheckIn {
    /**
     * 出发机场三字码
     */
    @NotNull(message = "出发机场三字码不能为空")
    private String deptAirport;

    /**
     * 到达机场三字码
     */
    @NotNull(message = "到达机场三字码不能为空")
    private String arrAirport;

    private String channelCode;

    private String version;
}

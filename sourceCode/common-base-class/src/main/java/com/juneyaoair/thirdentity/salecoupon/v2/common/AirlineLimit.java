package com.juneyaoair.thirdentity.salecoupon.v2.common;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description  航线限制条件类
 * @date 2020/7/30  8:47.
 */
@Data
public class AirlineLimit {
    @SerializedName("SelectAirline")
    private int selectAirline;  //1-航线选择 0-航线输入
    @SerializedName("Value")
    private List<String> value;
}

package com.juneyaoair.thirdentity.member.response;

import cn.jiguang.commom.utils.StringUtils;
import com.juneyaoair.appenum.member.CertificateTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.text.SimpleDateFormat;
import java.util.Date;

@Data
public class MemberCertificateResModel {
    @ApiModelProperty(value = "证件号码")
    private String CertificateNumber;
    @ApiModelProperty(value = "证件类型")
    private String CertificateType;
    @ApiModelProperty(value = "备注")
    private String Comments;
    @ApiModelProperty(value = "客户ID")
    private Integer Id;
    @ApiModelProperty(value = "是否认证（Y：已认证）")
    private String IsIdentification;
    @ApiModelProperty(value = "操作日期")
    private String OperateDate;
    @ApiModelProperty(value = "操作员")
    private String OperateUserId;
    @ApiModelProperty(value = "记录id")
    private Integer RecordId;
    @ApiModelProperty(value = "签发国家/地区")
    private String SigningAuthority;
    @ApiModelProperty(value = "状态")
    private String Status;
    @ApiModelProperty(value = "更新日期")
    private String UpdateDate;
    @ApiModelProperty(value = "更新人")
    private String UpdateUserId;
    @ApiModelProperty(value = "证件有效期")
    private String ValidDate;

    // 转为.net会员系统 MemberCertificateSoaModelV2
    public MemberCertificateSoaModelV2 convertToMemberCertificateSoaModelV2() {
        MemberCertificateSoaModelV2 memberCertificateSoaModelV2 = new MemberCertificateSoaModelV2();
        memberCertificateSoaModelV2.setCertificateNumber(this.CertificateNumber);
        memberCertificateSoaModelV2.setCertificateType(CertificateTypeEnum.getCodeByENAME(this.CertificateType));
        memberCertificateSoaModelV2.setRecordId(this.RecordId);
        memberCertificateSoaModelV2.setRemark(this.Comments);
        memberCertificateSoaModelV2.setOperateDate(this.OperateDate);
        memberCertificateSoaModelV2.setUpdateDate(this.UpdateDate);
        memberCertificateSoaModelV2.setVerify("Y".equals(this.IsIdentification));
        memberCertificateSoaModelV2.setSigningAuthority(this.SigningAuthority);
        memberCertificateSoaModelV2.setValidDate(StringUtils.isNotEmpty(this.ValidDate)?
                dateToString(toDate(this.ValidDate,"yyyy-MM-dd HH:mm:ss"),"yyyy-MM-dd"):"");
        return memberCertificateSoaModelV2;
    }

    public static Date toDate(String dateString, String format) {
        try {
            SimpleDateFormat formatter = new SimpleDateFormat(format);
            return formatter.parse(dateString);
        } catch (Exception e) {
            return null;
        }
    }

    public static String dateToString(Date d, String format) {
        if (StringUtils.isEmpty(format)) {
            return "";
        }
        SimpleDateFormat df = new SimpleDateFormat(format);
        return df.format(d);
    }

}

package com.juneyaoair.thirdentity.adtunlimitedfly;

import com.google.gson.annotations.SerializedName;
import com.juneyaoair.thirdentity.chdunlimitedfly.UnlimitedFlyV2BindRecord;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @create 2020-11-17 15:37
 */
@Data
public class PtQueryBindDetailResponse {

    /**
     * 是否到达绑定上限
     * yes-已达上限
     * no-未达上限
     */
    @SerializedName("ReachBindLimit")
    private String ReachBindLimit;

    /**
     * 会员ID
     */
    private String FfpId;

    /**
     * 会员卡号
     */
    private String FfpCardNo;

    /**
     * 绑定儿童数
     */
    private int ChildBindCount;

    /**
     * 绑定成人数
     */
    private int AdultBindCount;

    /**
     * 是否为别人赠送
     * 1:是  0:否
     */
    private int IsGiving;

    /**
     * 绑定信息列表
     *
     * 列表查询时返回
     */
    private List<UnlimitedFlyV2BindRecord> BindDetails;

    /**
     * 绑定信息
     * 券码查询时返回
     */
    private UnlimitedFlyV2BindRecord BindDetail;

}

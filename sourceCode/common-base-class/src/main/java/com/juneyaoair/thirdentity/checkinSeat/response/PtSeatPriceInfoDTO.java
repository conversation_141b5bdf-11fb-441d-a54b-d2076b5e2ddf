package com.juneyaoair.thirdentity.checkinSeat.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @Author:zc
 * @Description:
 * @Date:Created in 17:13 2019/4/15
 * @Modified by:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PtSeatPriceInfoDTO {
    private String Currency;//货币代码 人民币CNY 美元USD 日圆JPY 英镑GBP 泰铢THB等
    private BigDecimal Price;
    private BigDecimal Dis;//优惠价格
}

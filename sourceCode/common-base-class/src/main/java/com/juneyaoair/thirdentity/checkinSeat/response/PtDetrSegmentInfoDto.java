package com.juneyaoair.thirdentity.checkinSeat.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author:zc
 * @Description:
 * @Date:Created in 18:23 2019/4/15
 * @Modified by:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PtDetrSegmentInfoDto {
    private String TicketNo;
    private String PassengerName;
    private String PassengerType;//乘客类型,成人："0"， 儿童："1"， 无陪儿童："2"，婴儿："3"
    private String SegmentIndex;
    private String DepAirportCode;
    private String ArrAirportCode;
    private String DepCity;
    private String ArrCity;
    private String DepDate;//yyyy-MM-dd
    private String DepTime;//HHmm
    private String FlightNo;
    private String Cabin;
    private String PnrNo;
    private String TicketStatus;//客票状态"OPEN FOR USE","VOID".....
    private String DepAirportTerminal;
    private String ArrAirportTerminal;
    private String AirLine;//航空公司代码
    private String BoardingNo;//旅客在已飞行航段中的登机牌号
    private String SegmentStatus;//航段状态。"OK","RQ"....
    private boolean SelectedSeat;//是否选了座
    private String SeletSeatNo;//已选座位号
    private boolean OpenSelectSeat; //航班是否开放选座
}

package com.juneyaoair.thirdentity.salecoupon.common;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @date 2019/5/9  13:07.
 */
@Data
public class PhoneCardExtInfo {
    /**
     * 适用天数
     */
    @SerializedName("Days")
    private String days;
    /**
     * 提前预定天数
     */
    @SerializedName("AdvanceDays")
    private Integer advanceDays;
    /**
     * 提前预定小时数
     */
    @SerializedName("AdvanceHours")
    private Integer advanceHours;
    /**
     * 最小预定天数
     */
    @SerializedName("MinBookDays")
    private Integer minBookDays;
    /**
     * 国家编码
     */
    @SerializedName("CountryId")
    private String countryId;
}

package com.juneyaoair.thirdentity.salecoupon.request;
/*
 *@program:ho-mobileapi
 *@author: <PERSON>
 *@Time: 2021/8/10  13:38
 *@description:
 */

import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class PtPrePayCreateOrderResponsetDTO {
    private String Version; // 接口版本号 如 10.0;
    private String ChannelCode; // 渠道用户号 如 B2C,CC等;
    private String ResultCode; // 结果代码 10001 － 成功，其它失败
    private String ErrorInfo; // 错误信息 如 10.0;
    private PtBasicCreateOrderResponse Result; // 响应信息

}

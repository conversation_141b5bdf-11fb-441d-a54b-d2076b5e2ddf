package com.juneyaoair.thirdentity.change.response;

import com.juneyaoair.thirdentity.av.comm.Fare;
import com.juneyaoair.thirdentity.av.comm.V2FlightInfo;
import lombok.Data;

import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @description 客票改期手续费响应结果
 * @date 2018/12/21  18:27.
 */
@Data
public class PtFlightFareChangeResponse {
    private String Version;
    private String ChannelCode;
    private String UserNo;
    private String RandCode;
    private String RouteType;
    private String CurrencyCode;
    private String LangCode;
    private String InterFlag;
    //航段航班信息列表
    private List<V2FlightInfo> FlightInfoList;
    private HashMap<String,Fare> FareDic;
    private String ResultCode;
    private String ErrorInfo;
    //国际手续费
    private List<InterChangeFee> InterChangeFeeList;

    /**
     *改期备注
     */
    private String ChangeRemark;
    /**
     *改期提示
     */
    private String RescheduleReminder;
}

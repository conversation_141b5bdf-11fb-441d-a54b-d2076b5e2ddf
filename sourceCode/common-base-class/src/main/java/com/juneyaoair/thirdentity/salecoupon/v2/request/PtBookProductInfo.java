package com.juneyaoair.thirdentity.salecoupon.v2.request;

import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @date 2020/7/31  9:31.
 */
@Data
public class PtBookProductInfo {
    /**
     * ProductId : a1e7b56336bc4833acdbf24633e9ce31
     * BookingId : f39f6b144d404fb38209eeb9889a4950
     * RuleId : 016556b8977e4d51ab60a726288a92d6
     * ResourceType : ChildUnlimitedFly
     * StandardPrice : 200.0
     * SalePrice : 61.0
     * BookingCount : 1
     */

    private String ProductId;
    private String ProductSku;
    private String BookingId;
    private String ProductNum;//ProductId+BookingId+RuleId 组合ID
    private String RuleId;
    private String ResourceType;
    private Double StandardPrice;
    private Double SalePrice;
    private Integer BookingCount;
    private PtProductDeatil ProductDetail; //产品信息
}

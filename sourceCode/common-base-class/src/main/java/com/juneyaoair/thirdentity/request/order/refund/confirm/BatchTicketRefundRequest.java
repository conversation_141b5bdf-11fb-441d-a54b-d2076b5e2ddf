package com.juneyaoair.thirdentity.request.order.refund.confirm;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: caolei
 * @Description: 批量退票
 * @Date: 2021/5/20 15:53
 * @Modified by:
 */
@Data
public class BatchTicketRefundRequest {
    /** 接口版本号 */
    private String Version;
    /** 渠道 */
    private String ChannelCode;
    /** 退票订单详情 */
    private List<PtRefundReq> BatchTicketRefundRequest;

}
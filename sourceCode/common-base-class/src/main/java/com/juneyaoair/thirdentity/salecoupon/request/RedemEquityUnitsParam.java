package com.juneyaoair.thirdentity.salecoupon.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * <AUTHOR>
 * @Description 券码兑换请求参数
 * @created 2023/9/8 10:02
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class RedemEquityUnitsParam extends OrderBaseParam {

    @ApiModelProperty(value = "兑换码", required = true)
    private String ApprovalCode;

}

package com.juneyaoair.thirdentity.salecoupon.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName PtFlightTicketPriceByYResp
 * @Description
 * @createTime 2022年02月17日 13:12
 */
@Data
@ApiModel(value = "Y舱价格返回值")
public class PtFlightTicketPriceByYResp {
    @ApiModelProperty("出发日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date depDate;

    @ApiModelProperty("出发城市")
    private String depCity;

    @ApiModelProperty("到达城市")
    private String arrCity;

    @ApiModelProperty("价格")
    private Double price;

    @ApiModelProperty("价格字符串")
    private String priceStr;
}

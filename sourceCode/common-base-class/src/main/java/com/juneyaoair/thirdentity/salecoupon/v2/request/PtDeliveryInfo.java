package com.juneyaoair.thirdentity.salecoupon.v2.request;

import com.google.gson.annotations.SerializedName;
import com.juneyaoair.pattern.PatternCommon;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.Pattern;

/**
 * @ClassName PtDeliveryInfo
 * <AUTHOR>
 * @Description
 * @Date 2020-09-27 14:08
 **/
@Data
public class PtDeliveryInfo {
    @NotBlank(message="配送方式不能为空")
    @Pattern(regexp="[0123]{1}", message = "配送方式  0-无配送方式  1-贵宾休息室领取  2-机上配送  3-快递邮寄！")
    @SerializedName("DeliveryType")
    private String deliveryType; //配送方式  0-无配送方式  1-贵宾休息室领取  2-机上配送  3-快递邮寄
    @SerializedName("Address")
    private String address; // 配送地址 xx街xx号
    @SerializedName("Province")
    private String province; //省  快递配送必填
    @SerializedName("City")
    private String city; //市  快递配送必填
    @SerializedName("County")
    private String county; // 县/区
    @NotBlank(message="收货人姓名不能为空")
    @SerializedName("ReceiverName")
    private String receiverName; //收货人姓名
    @NotBlank(message="收货人手机号不能为空")
    @Pattern(regexp = PatternCommon.MOBILE_PHONE,message = "请输入正确的手机号")
    @SerializedName("ReceiverMobile")
    private String receiverMobile; //收货人手机号
}

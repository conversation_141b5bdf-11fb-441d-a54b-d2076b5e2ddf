package com.juneyaoair.thirdentity.salecoupon.v2.response;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Getter
@Setter
public class ProductActivityDto {
    @ApiModelProperty(value = "产品活动ID")
    private String productId;

    @ApiModelProperty(value = "资源标签;多个标签以|分隔")
    private String showTags;

    @ApiModelProperty(value = "产品类型")
    private String productType;

    @ApiModelProperty(value = "活动号")
    private String activityNo;


    @ApiModelProperty(value = "活动名称")
    private String activityName;


    @ApiModelProperty(value = "活动标题")
    private String activityTitle;

    @ApiModelProperty(value = "价格类型;T抵用券/D折扣券")
    private String salePriceType;

    @ApiModelProperty(value = "币种;CNY人民币/USD美元/JPY日元/EUR欧元/KRW韩元/THB泰铢")
    private String currencyCode;

    @ApiModelProperty(value = "是否整售 Y整售 N 非整售 默认N")
    private String isWholeSale;


    @ApiModelProperty(value = "原始销售价格/用户侧划线价")
    private BigDecimal originalPrice;

    /**
     * 打折/价格;PP 预定舱位票面价/Y Y舱票面价
     */
    @ApiModelProperty(value = "打折/价格")
    private BigDecimal priceOrDiscount;

    @ApiModelProperty(value = "销售总量")
    private Integer saleAmountTotal;

    //限制总量

    @ApiModelProperty(value = "销售剩余量")
    private Integer saleAmountAvilable;

    @ApiModelProperty(value = "销售单人最大购买量")
    private Integer maxAmountPer;

    @ApiModelProperty(value = "活动开始日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date beginDatetime;

    @ApiModelProperty(value = "活动结束日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date endDatetime;

    @ApiModelProperty(value = "舱位等级")
    private String suitCabin;

    @ApiModelProperty(value = "退票手续费类型")
    /*T金额/D折扣比例*/
    private String refundFeeType;

    @ApiModelProperty(value = "退票手续费")
    /*折扣时为百分比*/
    private Integer refundFee;

    @ApiModelProperty(value = "目标升舱舱位")
    /*主要用于升舱,适用舱位为限制原舱位，目标舱位为限制升舱后舱位*/
    private String targeCabin;


    @ApiModelProperty(value = "活动有效期是否有效 Y 有效 N 无效")
    private String effectiveStatus;


    @ApiModelProperty(value = "活动自定义属性")
    List<ProductFieldDto> productFieldList;
}

package com.juneyaoair.thirdentity.salecoupon.common;

import com.google.gson.annotations.SerializedName;
import com.juneyaoair.baseclass.visa.bean.VISAExtInfo;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description 资源信息
 * @date 2019/2/25  9:33.
 */
@Data
public class ResourceInfo {
    @SerializedName("ResourceId")
    private String resourceId;
    @SerializedName("VendorMessageId")
    private String vendorMessageId;
    @SerializedName("ResourceType")
    private String resourceType;
    @SerializedName("ResourceName")
    private String resourceName;
    @SerializedName("Description")
    private String description;
    @SerializedName("RightValidityDays")
    private int rightValidityDays; //资源有效天数
    @SerializedName("StandardPrice")
    private Double standardPrice; //标准售价（参考价值）
    @SerializedName("LoungeExt")
    private LoungeExtInfo loungeExt;
    @SerializedName("UpgradeExt")
    private UpgradeExtInfo upgradeExt;
    @SerializedName(value = "BaggageExt",alternate = "BaggageExtInfo")
    private BaggageExtInfo baggageExt;
    @SerializedName("CheckinSubstitutionExt")
    private CheckinSubstitutionExtInfo checkinSubstitutionExt;
    @SerializedName("Tags")
    private List<String> tags;
    @SerializedName("Imgs")
    private List<String> imgs;//资源图片列表
    @SerializedName("UseMode")
    private String useMode;//资源使用方式说明
    @SerializedName("PurchaseNotes")
    private String purchaseNotes;//购买须知
    @SerializedName("RefundRules")
    private String refundRules;//退款规则
    @SerializedName("OnboardWifiExt")
    private OnboardWifiExtInfo onboardWifiExt;
    //随身wifi特有属性
    @SerializedName("WIFIExt")
    private WIFIExtInfo wifiExt;
    //境外电话卡特有属性
    @SerializedName("PhoneCardExt")
    private PhoneCardExtInfo phoneCardExt;
    //接送机特有属性
    @SerializedName("VISAExt")
    private VISAExtInfo visaExt;
    //供应商ID
    @SerializedName("VendorID")
    private String vendorID;
    //供应商名称
    @SerializedName("VendorName")
    private String vendorName;
    //供应商名称
    @SerializedName("ProductName")
    private String productName;

    @SerializedName("ProNum")
    private String proNum;

    public Double getStandardPrice(){
        return this.standardPrice==null?0D:this.standardPrice;
    }
}

package com.juneyaoair.thirdentity.salecoupon.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 券码兑换结果
 * @created 2023/9/8 10:07
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class RedemEquityUnitsResult extends OrderBaseResult {

    @ApiModelProperty(value = "劵码信息", required = true)
    private List<VoucherNo> VoucherNoList;

    @Data
    public static class VoucherNo {

        @ApiModelProperty(value = "劵码号", required = true)
        private String EquityNo;

        @ApiModelProperty(value = "产品类型 权益劵：CouponProduct 优惠劵：Coupon 打包劵：CouponBag", required = true)
        private String ProductType;

        @ApiModelProperty(value = "订单号", required = true)
        private String OrderNo;

        /**
         * EconSingleCoupon-机票通兑券-经济
         * BusiSingleCoupon-机票通兑券-公务
         * TCoupon-代金券
         * MultiTimeCouponProduct-套卡
         * UpgradeCoupon-升舱券
         * LoungeCoupon-休息室券
         * Reschedule-改期券
         */
        @ApiModelProperty(value = "直播产品类型", required = true)
        private String  LiveThemeProductType;
    }

}

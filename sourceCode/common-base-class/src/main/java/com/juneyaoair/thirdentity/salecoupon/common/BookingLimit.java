package com.juneyaoair.thirdentity.salecoupon.common;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description 预订限制
 * @date 2019/2/25  9:47.
 */
@Data
public class BookingLimit {
    @SerializedName("BookingDates")
    private List<DateLimit> bookingDates; //购买日期限制 为空即无限制
    @SerializedName("TravelDates")
    private List<DateLimit> travelDates; //出行日期限制 为空即无限制
    @SerializedName("FlightType")
    private String flightType; //OW:仅限单程 RT:仅限往返 为空即无限制
    @SerializedName("FlightRoutes")
    private List<BlackList> flightRoutes;
    @SerializedName("FlightNos")
    private BlackList flightNos;
    @SerializedName("Cabins")
    private BlackList cabins;
    /**
     * 航班日期范围
     */
    @SerializedName("FlightDates")
    private List<DateLimit> flightDates;

}

package com.juneyaoair.thirdentity.boardingpass.common;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @date 2019/9/11  16:13.
 */
@Data
public class BasePassenger {
    @SerializedName("Name")
    private String name;
    @SerializedName("CertNo")
    private String certNo;
    @SerializedName("CertType")
    private String certType;
    @SerializedName("TicketNo")
    private String ticketNo;
    @SerializedName("Pnr")
    private String pnr;
    @SerializedName("ContactName")
    private String contactName;
    @SerializedName("ContactPhone")
    private String contactPhone;
}

package com.juneyaoair.thirdentity.salecoupon.v2.response;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @date 2020/7/31  9:54.
 */
@Data
public class PtBaseCouponOrderIdentity {
    @SerializedName("OrderId")
    private Long orderId;
    @SerializedName("OrderNo")
    private String orderNo;
    @SerializedName("OrderChannelOrderNo")
    private String orderChannelOrderNo;
    @SerializedName("CreateAt")
    private Long createAt;//订单创建时间
    @SerializedName("Status")
    private String status;//订单状态
    @SerializedName("OrderType")
    private String orderType;//订单类型
}

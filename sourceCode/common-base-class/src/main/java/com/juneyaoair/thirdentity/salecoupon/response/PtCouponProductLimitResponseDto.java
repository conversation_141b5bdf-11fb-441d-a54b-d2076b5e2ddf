package com.juneyaoair.thirdentity.salecoupon.response;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2019/3/12  8:34.
 */
@Data
public class PtCouponProductLimitResponseDto {
    private String Version;
    private String ChannelCode;
    private String ResultCode;
    private String ErrorInfo;
    private List<Airline> Airlines;
    private List<FlightInfo> FlightInfos;
    @Data
    public class Airline{
        private String DepCityCode;
        private String DepCityName;
        private String ArrCityCode;
        private String ArrCityName;
        private String IsIntl; //0：国内航线  1：国际/港澳台航线
    }
    @Data
    public class FlightInfo{
        private String FlightNo;
        private Airline Airline;
    }
}


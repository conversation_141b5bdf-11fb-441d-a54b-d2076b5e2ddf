package com.juneyaoair.thirdentity.salecoupon.response;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description
 * @create 2020-07-05 14:30
 */
@Data
public class PtQueryUpgradeCardResponse {

    @SerializedName("ResultCode")
    private String resultCode;

    @SerializedName("ErrorInfo")
    private String errorInfo;

    @SerializedName("Version")
    private String version;

    @SerializedName("ChannelCode")
    private String channelCode;

    @SerializedName("Count")
    private int count;

}

package com.juneyaoair.thirdentity.salecoupon.response;

import com.juneyaoair.thirdentity.salecoupon.common.MainVoucherInfo;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description 我的权益券返回
 * @date 2019/2/28  18:09.
 */
@Data
public class PtCouponProductGetResponseDto {
    private String Version;
    private String ChannelCode;
    private String ResultCode;
    private String ErrorInfo;
    private List<MainVoucherInfo> Vouchers;//主权益凭证 可用的权益券列表
    private int TotalRecords;
    private int TotalPages;
    private int Count;//当前现有的升舱券数量
}

package com.juneyaoair.thirdentity.boardingpass.req;

import com.juneyaoair.baseclass.newcoupon.bean.BookProductInfo;
import com.juneyaoair.thirdentity.boardingpass.common.BoardingPassengerInfo;
import com.juneyaoair.thirdentity.boardingpass.common.BoardingSegmentInfo;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @date 2019/9/9  19:51.
 * 参考文档 http://gitlab.juneyaoair.com:10080/vNextPlatform/vNextOrderPlatform/wikis/01%E5%B9%B3%E5%8F%B0%E6%9C%8D%E5%8A%A1API/%E4%BA%A7%E5%93%81%E6%9C%8D%E5%8A%A1-%E9%A2%84%E7%95%99%E7%99%BB%E6%9C%BA%E7%89%8CAPI#%E6%9F%A5%E8%AF%A2%E4%BC%91%E6%81%AF%E5%AE%A4
 */
@Data
public class BoardingCheckBuyRequestDto {
    private String CertNo;
    private String CertType;
    private String PassengerName;
    private String ChannelOrderNo;
    private String TotalAmount; //totalAmount = 产品总价-积分抵扣金额
    private String PhoneNo;
    private String Contact;//联系人名称
    private String TktNo;
    private String PnrNo;
    private String UseScore;//使用积分数
    private String Currency;
    private String Preference;
    private String DepAirportCode;
    private String ArrAirportCode;
    private String DepAirportTerminal;
    private String ArrAirportTerminal;
    private String FlightNo;
    private String FlightDate;
    private String ArrDate;//航班到达时间
    private String ArrTime;
    private String DepTime;
    private String PlaneType;
    private String Cabin;
    private BookProductInfo BookProductInfo;
    private String InvitationCode;
}

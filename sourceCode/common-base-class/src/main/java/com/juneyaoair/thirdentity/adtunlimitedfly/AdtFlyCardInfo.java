package com.juneyaoair.thirdentity.adtunlimitedfly;

import com.google.gson.annotations.SerializedName;
import com.juneyaoair.thirdentity.chdunlimitedfly.UnlimitedFlyBindRecord;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @create 2020-07-31 14:24
 */
@Data
public class AdtFlyCardInfo {

    @SerializedName("AdultFlyRecords")
    private List<UnlimitedFlyBindRecord> adultFlyRecords;

}

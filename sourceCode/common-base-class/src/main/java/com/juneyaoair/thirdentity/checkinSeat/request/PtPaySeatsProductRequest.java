package com.juneyaoair.thirdentity.checkinSeat.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Author:zc
 * @Description:
 * @Date:Created in 9:53 2019/3/26
 * @Modified by:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PtPaySeatsProductRequest {
    private String Version;
    private String ChannelCode;
    private String UserNo;
    private String OrderRequestIp;
    private BigDecimal PayAmount;
    private int UseScore;
    private List<PtPayReserveSeatRequest> SeatRequests;
    private String ChannelOrderNo;
}

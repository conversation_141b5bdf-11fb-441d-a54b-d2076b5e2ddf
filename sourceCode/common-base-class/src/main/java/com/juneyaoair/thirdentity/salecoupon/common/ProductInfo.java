package com.juneyaoair.thirdentity.salecoupon.common;

import com.google.gson.annotations.SerializedName;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2019/4/26  15:20.
 */
@Data
public class ProductInfo {
    @NotBlank(message = "产品ID不能为空")
    @SerializedName("ProductId")
    private String productId;
    @SerializedName("ProductName")
    private String productName;
    @SerializedName("ProductType")
    private String productType;
    @SerializedName("VaildityDays")
    private int vaildityDays; //产品有效期
    @SerializedName("MinPrice")
    private Double minPrice; //起价 即销售价
    private Double originalPrice;//原价 一般用于展示使用
    @SerializedName("ResourceList")
    private List<ResourceInfo> resourceList; //资源信息
    @SerializedName("BookingLimit")
    private BookingLimit bookingLimit; //预订限制
    @SerializedName("AvailableStock")
    private long availableStock; //可售库存
    @SerializedName("SaledCount")
    private int hasSales;//已售数量
    @SerializedName("Tags")
    private List<String> tags;
    @SerializedName("Imgs")
    private List<String> imgs;
    @SerializedName("UseMode")
    private String useMode;
    @SerializedName("PurchaseNotes")
    private String purchaseNotes;
    @SerializedName("RefundRules")
    private String refundRules;
    @SerializedName("ProductDescription")
    private String productDescription;
    @SerializedName("UnLimitedUpgrade")
    private String unLimitedUpgrade;
    @SerializedName("VaildityDate")
    private String vaildityDate;
    /**
     * 信息签名串
     */
    private String productSign;

    @SerializedName("IsIntl")
    private String isIntl;
    /**
     * 每人限购数量
     */
    @SerializedName(value="BookingCount")
    private int BookingCount;

    /**
     * 剩余可购买次数
     */
    @SerializedName(value="RemainedCount")
    private int RemainedCount;

    /**
     * 获取产品的签名字段
     */
    public String signProductField(){
        return this.productId+this.productType+this.minPrice;
    }
}

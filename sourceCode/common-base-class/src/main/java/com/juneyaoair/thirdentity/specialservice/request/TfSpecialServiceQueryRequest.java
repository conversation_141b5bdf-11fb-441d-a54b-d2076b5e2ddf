package com.juneyaoair.thirdentity.specialservice.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Auther: lizongjie
 * @Date: 2018/11/19 14:18
 * @Description:
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class TfSpecialServiceQueryRequest<T> {
    private String inftCode;
    private String memberId;

    /*  private List<String> ids;
    private String id;*/
    private TfSpecialServiceQueryRequestDTO data;

}

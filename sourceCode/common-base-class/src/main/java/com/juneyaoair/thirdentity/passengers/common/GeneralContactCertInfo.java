package com.juneyaoair.thirdentity.passengers.common;

import cn.jiguang.commom.utils.StringUtils;
import com.google.gson.annotations.SerializedName;
import com.juneyaoair.appenum.member.CertificateTypeEnum;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.AssertTrue;

/**
 * <AUTHOR>
 * @description  常用乘机人证件信息
 * @date 2019/7/18  19:07.
 */
@Data
public class GeneralContactCertInfo {
    @SerializedName("GeneralContactCertId")
    private Integer generalContactCertId;  //常用乘机人证件信息ID
    @SerializedName("GeneralContactId")
    private Integer generalContactId;  //常用乘机人信息ID
    @SerializedName("CertType")
    @NotBlank(message = "证件类型不能为空")
    private String certType;
    @SerializedName("CertNo")
    private String certNo;
    @SerializedName("BelongCountry")
    private String belongCountry;//发证国
    private String belongCountryName;//发证国名称
    @SerializedName("CertValidity")
    private String certValidity;//证件有效
    @SerializedName("IsRemove")//是否删除
    private Boolean isRemove;
    private String expiringMessage;//即将过期提醒，即护照到期时间小于12个月，其他证件小于6个月
    private String message;//提示信息
    private String record;
    /**
     * 乘机人可购买机票类型
     * @see com.juneyaoair.appenum.comonperson.CommonPersonCabinType
     */
    private String cabinType;
    /**
     * 券号
     * 包括但不限于畅飞卡卡号
     */
    private String couponNo;

    /**
     * 券类型
     */
    private String couponType;

    /**
     * 是否可以使用积分
     * 乘机人为受益人时可以使用积分
     */
    private boolean useScore;

    /**
     * 受益人此证件是否可用
     */
    private boolean isValid;
    /**
     * 是否是实名认证的证件
     */
    private boolean isVerify;

    private String  IsIdentification;
    /**
     * 证件类型为BC时，certNo证件号码可以为空,其他证件类型不能为空
     * @return
     */
    @AssertTrue(message = "证件号码不能为空")
    public boolean isCertNoEmpty() {
        if (StringUtils.isNotEmpty(certNo)) {
            return true;
        }
        return false;
    }

    public GeneralContactCertInfo() {
    }

    public GeneralContactCertInfo(String certType) {
        this.certType = certType;
    }

    public GeneralContactCertInfo(String certType, String certNo) {
        this.certType = certType;
        this.certNo = certNo;
    }

    public GeneralContactCertInfo(String certType, String certNo,boolean useScore) {
        this.certType = certType;
        this.certNo = certNo;
        this.useScore = useScore;
    }

    public boolean filterRemovedCert(){
        if(generalContactCertId==null&&isRemove!=null&&isRemove){
            return true;
        }
        return false;
    }
}

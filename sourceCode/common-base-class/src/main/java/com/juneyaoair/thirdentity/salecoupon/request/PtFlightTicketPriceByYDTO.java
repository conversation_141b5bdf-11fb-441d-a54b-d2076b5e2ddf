package com.juneyaoair.thirdentity.salecoupon.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName PtFlightTicketPriceByYDTO
 * @Description
 * @createTime 2022年02月17日 13:04
 */
@Data
@ApiModel(value = "Y舱价格请求内容")
public class PtFlightTicketPriceByYDTO {
    @ApiModelProperty("开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
//    @NotNull(message = "出发日期不能为空")
    private Date startDate;

    @ApiModelProperty("结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
//    @NotNull(message = "返回日期不能为空")
    private Date endDate;

    @ApiModelProperty("出发城市")
    @NotBlank(message = "出发城市不能为空")
    private String depCity;

    @ApiModelProperty("到达城市")
    @NotBlank(message = "到达城市不能为空")
    private String arrCity;
}

package com.juneyaoair.thirdentity.basic;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2020/7/13  17:19.
 */
@Data
public class PayMethodDTO {
    private String id;
    private String channel;
    private String payProductType;//产品类型
    private String orderNo;
    private String payLogoStream;//支付方式logo数据流
    private String unionpayLogoStream;//银联logo数据流
    private String payMethodCode;//支付方式码
    private String payMethodName;//支付方式名称
    private String status;
    private List<PayMethodActivityDTO> activityList;
    private String isDefault;//是否默认选中
    private String isHide;//是否默认隐藏
    /**
     * 位置  0 tab1  1 tab2
     */
    private String position;
}

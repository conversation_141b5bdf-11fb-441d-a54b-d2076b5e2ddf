package com.juneyaoair.thirdentity.specialservice.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by yaocf on 2018/7/4  17:21.
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class PtSpecialServiceQueryRequest {
    private String Version;
    private String ChannelCode;
    private String UserNo;
    private String FfpId;
    private String OrderNo;
    public PtSpecialServiceQueryRequest(String version,String channelCode,String userNo,String ffpId){
        this.Version=version;
        this.ChannelCode=channelCode;
        this.UserNo=userNo;
        this.FfpId=ffpId;

    }
}

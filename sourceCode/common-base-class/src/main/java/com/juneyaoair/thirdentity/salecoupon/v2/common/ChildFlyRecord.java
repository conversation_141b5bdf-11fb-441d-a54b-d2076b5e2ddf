package com.juneyaoair.thirdentity.salecoupon.v2.common;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @date 2020/8/1  9:13.
 */
@Data
public class ChildFlyRecord {
    @SerializedName("VoucherNo")
    private String voucherNo;
    @SerializedName("BindStatus")
    private String bindStatus;
    @SerializedName("BindingDate")
    private String bindingDate;
    @SerializedName("StartDate")
    private String startDate;
    @SerializedName("EndDate")
    private String endDate;
    @SerializedName("LimitBindingDate")
    private String limitBindingDate;//绑定截至日期  yyyy-MM-dd
    @SerializedName("BindingValidity")
    private String bindingValidity;//记录是否已过绑定有效期  Effective  有效  invalid  失效
    @SerializedName("ChildCnName")
    private String childCnName;
    @SerializedName("ChildEnName")
    private String childEnName;
    @SerializedName("ChildIdNumber")
    private String childIdNumber;
    @SerializedName("AdltName")
    private String adltName;
    @SerializedName("AdultIdNumber")
    private String adultIdNumber;
    @SerializedName("PhoneNumber")
    private String phoneNumber;
    @SerializedName("ReceiveMemberId")
    private String receiveMemberId;
    @SerializedName("ReceiveMemberCard")
    private String receiveMemberCard;
    @SerializedName("ReceiveVoucherNo")
    private String receiveVoucherNo;//受赠人券码
    @SerializedName("NoShowTimes")
    private int noshowTimes;//违规退票次数
    @SerializedName("NoShowUpperLimit")
    private int noShowUpperLimit;//违规退票次数上限
    @SerializedName("OutTimes")
    private int outTimes;//出票次数
}

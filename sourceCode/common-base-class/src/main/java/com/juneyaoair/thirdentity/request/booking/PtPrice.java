package com.juneyaoair.thirdentity.request.booking;

import com.google.gson.annotations.SerializedName;
import com.juneyaoair.thirdentity.av.comm.Fare;
import com.juneyaoair.thirdentity.av.comm.PrivilegeDto;
import com.juneyaoair.thirdentity.av.comm.PtChangeRuleInfo;
import com.juneyaoair.thirdentity.av.comm.PtRefundRuleInfo;
import com.juneyaoair.thirdentity.response.tax.InternatTaxInfo;
import io.swagger.annotations.ApiModelProperty;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.math.BigDecimal;
import java.util.List;

public class PtPrice {
	private int PriceNO; //运价序号	当次请求必须保证唯一性
	private String PriceProductType; //运价产品类型	1 - 公布,2 - 私有,3 - 多程惠达，4 - 中转联程
	private String PriceRouteType; //运价航程类型	OW - 单程,RT - 往返
	private String FareBasis; //运价基础
	private String TourCode; //旅行代码	
	private String Discount; //折扣率	与Y公布运价的比率,如:值为10表示10%
	private String EI; //限制条件	写入PNR的签注信息
	private String Comment; //备注项	用于前端显示说明信息，如：解释签注信息或备注中转联程提供住宿
	private String Baggage; //行李重量	计重制:20KG 计件制:2PC
	private String ValidityPeriod; //客票有效期(最长停留时间)	有效取值范围:1Y或1M或1D 该项值为空时表示没有限制，有值时需要与航班日期进行计算
	private String MinStay; //最短停留时间	1Y1M1D 该项值为空时表示没有限制，有值时需要与航班日期进行计算
	private double PriceValue; //8(其中2位小数)	票价	票价金额为-1时是无效票价不能进行销售
	/** 组合票价 ，中转-隔开,往返/隔开*/
	private String priceValueComb;
	private double RSP; //8(其中2位小数)	销售参考价	用于前端宣传打折效果使用,当值为-1时表示找不到有效的销售参考价
	private boolean RefundedFlag; //是否可退票	
	private String RefundedComment; //退票政策描述	说明退票费收取或计算方法
	private String ChangedComment; //变更政策描述	说明改期、签转和升舱政策
	private boolean RescheduledFlag; //是否可免费改期	
	private int FreeChangeTimes; //免费改期次数	免费改期不限次数时，目前运价系统会给出99次
	private boolean ChangeAirLineFlag; //是否可改签	
	private boolean UpgradeFlag; //是否可升舱	
	private Double YPrice; //8(其中2位小数)	经济舱公布运价全价金额
	private String FareID; //运价编号	运价系统中该条运价的主键，计算退票费时有用
	private Double YQTax; //7(其中2位小数)	燃油费
	private Double CNTax; //7(其中2位小数)	建设税
	private Double OtherTax; //7(其中2位小数)	其他税
	private	String	DynamicCabin;
	private	String	DynamicFareID;
	private	String  CombineId;
	private String FareSign; //运价验证串	航班查询结果中Price.FareSign字段
	private boolean IsUseScore; //是否使用积分true - 使用,false - 不使用
	private boolean IsGiftScore; //是否赠送积分true - 赠送,false - 不赠送
	private PtScoreUse ScoreUseInfo; //积分使用信息
	private PtScoreGift ScoreGiftInfo; //积分赠送信息
	private double UpgradeFee;//  升舱改期手续费
	//退改规则列表
	private List<PtRefundRuleInfo> RefundedRules; //退票规则信息  RefundedFlag为false时该字段信息为空
	private List<PtChangeRuleInfo> ChangeRules ; //更改规则信息 UpgradeFlag为false并且RescheduledFlag为true时该字段信息为空
	private List<InternatTaxInfo> OtherTaxList;//税费明细  国际税费，国际有
	private Double Discountdiff;   //多程惠达优惠金额
	@ApiModelProperty(value = "规则编码")
	private String RuleId;
	@ApiModelProperty(value = "规则编码")
	private String FbrId;
	@ApiModelProperty(value = "特殊产品代码")
	private String SpecialFareCode;

	private BigDecimal PreferentialPrice;  //拥军优惠价格




	/**
	 * 原改期费
	 */
	private int OriginalChangeFee;


	/**
	 * 变更备注
	 */
	private String ChangedRemark;

	public BigDecimal getPreferentialPrice() {
		return PreferentialPrice;
	}

	public void setPreferentialPrice(BigDecimal preferentialPrice) {
		PreferentialPrice = preferentialPrice;
	}


	public String getChangedRemark() {
		return ChangedRemark;
	}

	public void setChangedRemark(String changedRemark) {
		ChangedRemark = changedRemark;
	}


	public int getOriginalChangeFee() {
		return OriginalChangeFee;
	}

	public void setOriginalChangeFee(int originalChangeFee) {
		OriginalChangeFee = originalChangeFee;
	}

	/**
	 * 拥军优惠后价格
	 */
	private  BigDecimal  DiscountedPriceValue;


	public BigDecimal getDiscountedPriceValue() {
		return DiscountedPriceValue;
	}

	public void setDiscountedPriceValue(BigDecimal discountedPriceValue) {
		DiscountedPriceValue = discountedPriceValue;
	}


	//改期升舱手续费
	private double changeServiceCharge;
	//改期升舱票面差价
	private double ticketPriceDiff;
	//改期升舱税费差价
	private double taxDiff;
	//燃油差价
	private double yqTaxDiff;
	//机建差价
	private double cnTaxDiff;
	//改期升舱总差价   票面差价+税费差价+机建差价+燃油差价+手续费
	private double totalDiff;
	// 子运价
	@SerializedName("Fares")
	private List<Fare> fares;
	private String BrandCode; //2021-05-12 国际品牌运价号 国际品牌运价下单时使用

	private List<PrivilegeDto> PrivilegeList;

	public List<PrivilegeDto> getPrivilegeList() {
		return PrivilegeList;
	}

	public void setPrivilegeList(List<PrivilegeDto> privilegeList) {
		PrivilegeList = privilegeList;
	}

	public boolean isUseScore() {
		return IsUseScore;
	}

	public void setUseScore(boolean useScore) {
		IsUseScore = useScore;
	}

	public int getPriceNO() {
		return PriceNO;
	}
	public void setPriceNO(int priceNO) {
		PriceNO = priceNO;
	}
	public String getPriceProductType() {
		return PriceProductType;
	}
	public void setPriceProductType(String priceProductType) {
		PriceProductType = priceProductType;
	}
	public String getPriceRouteType() {
		return PriceRouteType;
	}
	public void setPriceRouteType(String priceRouteType) {
		PriceRouteType = priceRouteType;
	}
	public String getFareBasis() {
		return FareBasis;
	}
	public void setFareBasis(String fareBasis) {
		FareBasis = fareBasis;
	}
	public String getTourCode() {
		return TourCode;
	}
	public void setTourCode(String tourCode) {
		TourCode = tourCode;
	}
	public String getDiscount() {
		return Discount;
	}
	public void setDiscount(String discount) {
		Discount = discount;
	}
	public String getEI() {
		return EI;
	}
	public void setEI(String eI) {
		EI = eI;
	}
	public String getComment() {
		return Comment;
	}
	public void setComment(String comment) {
		Comment = comment;
	}
	public String getBaggage() {
		return Baggage;
	}
	public void setBaggage(String baggage) {
		Baggage = baggage;
	}
	public String getValidityPeriod() {
		return ValidityPeriod;
	}
	public void setValidityPeriod(String validityPeriod) {
		ValidityPeriod = validityPeriod;
	}
	public String getMinStay() {
		return MinStay;
	}
	public void setMinStay(String minStay) {
		MinStay = minStay;
	}
	public double getPriceValue() {
		return PriceValue;
	}
	public void setPriceValue(double priceValue) {
		PriceValue = priceValue;
	}
	public double getRSP() {
		return RSP;
	}
	public void setRSP(double rSP) {
		RSP = rSP;
	}
	public boolean isRefundedFlag() {
		return RefundedFlag;
	}
	public void setRefundedFlag(boolean refundedFlag) {
		RefundedFlag = refundedFlag;
	}
	public String getRefundedComment() {
		return RefundedComment;
	}
	public void setRefundedComment(String refundedComment) {
		RefundedComment = refundedComment;
	}
	public String getChangedComment() {
		return ChangedComment;
	}
	public void setChangedComment(String changedComment) {
		ChangedComment = changedComment;
	}
	public boolean isRescheduledFlag() {
		return RescheduledFlag;
	}
	public void setRescheduledFlag(boolean rescheduledFlag) {
		RescheduledFlag = rescheduledFlag;
	}
	public int getFreeChangeTimes() {
		return FreeChangeTimes;
	}
	public void setFreeChangeTimes(int freeChangeTimes) {
		FreeChangeTimes = freeChangeTimes;
	}
	public boolean isChangeAirLineFlag() {
		return ChangeAirLineFlag;
	}
	public void setChangeAirLineFlag(boolean changeAirLineFlag) {
		ChangeAirLineFlag = changeAirLineFlag;
	}
	public boolean isUpgradeFlag() {
		return UpgradeFlag;
	}
	public void setUpgradeFlag(boolean upgradeFlag) {
		UpgradeFlag = upgradeFlag;
	}

	public Double getYPrice() {
		return YPrice;
	}

	public void setYPrice(Double YPrice) {
		this.YPrice = YPrice;
	}

	public String getFareID() {
		return FareID;
	}
	public void setFareID(String fareID) {
		FareID = fareID;
	}

	public Double getYQTax() {
		return YQTax;
	}

	public void setYQTax(Double YQTax) {
		this.YQTax = YQTax;
	}

	public Double getCNTax() {
		return CNTax;
	}

	public void setCNTax(Double CNTax) {
		this.CNTax = CNTax;
	}

	public String getFareSign() {
		return FareSign;
	}
	public void setFareSign(String fareSign) {
		FareSign = fareSign;
	}
	public boolean getIsUseScore() {
		return IsUseScore;
	}
	public void setIsUseScore(boolean isUseScore) {
		IsUseScore = isUseScore;
	}
	public boolean getIsGiftScore() {
		return IsGiftScore;
	}
	public void setIsGiftScore(boolean isGiftScore) {
		IsGiftScore = isGiftScore;
	}
	public PtScoreUse getScoreUseInfo() {
		return ScoreUseInfo;
	}
	public void setScoreUseInfo(PtScoreUse scoreUseInfo) {
		ScoreUseInfo = scoreUseInfo;
	}
	public PtScoreGift getScoreGiftInfo() {
		return ScoreGiftInfo;
	}
	public void setScoreGiftInfo(PtScoreGift scoreGiftInfo) {
		ScoreGiftInfo = scoreGiftInfo;
	}
	public String getDynamicCabin() {
		return DynamicCabin;
	}
	public void setDynamicCabin(String dynamicCabin) {
		DynamicCabin = dynamicCabin;
	}
	public String getDynamicFareID() {
		return DynamicFareID;
	}
	public void setDynamicFareID(String dynamicFareID) {
		DynamicFareID = dynamicFareID;
	}
	public String getCombineId() {
		return CombineId;
	}
	public void setCombineId(String combineId) {
		CombineId = combineId;
	}

	public double getChangeServiceCharge() {
		return changeServiceCharge;
	}

	public void setChangeServiceCharge(double changeServiceCharge) {
		this.changeServiceCharge = changeServiceCharge;
	}

	public double getTicketPriceDiff() {
		return ticketPriceDiff;
	}

	public void setTicketPriceDiff(double ticketPriceDiff) {
		this.ticketPriceDiff = ticketPriceDiff;
	}

	public double getTaxDiff() {
		return taxDiff;
	}

	public void setTaxDiff(double taxDiff) {
		this.taxDiff = taxDiff;
	}

	public double getYqTaxDiff() {
		return yqTaxDiff;
	}

	public void setYqTaxDiff(double yqTaxDiff) {
		this.yqTaxDiff = yqTaxDiff;
	}

	public double getCnTaxDiff() {
		return cnTaxDiff;
	}

	public void setCnTaxDiff(double cnTaxDiff) {
		this.cnTaxDiff = cnTaxDiff;
	}

	public double getTotalDiff() {
		return totalDiff;
	}

	public void setTotalDiff(double totalDiff) {
		this.totalDiff = totalDiff;
	}

	public double getUpgradeFee() {
		return UpgradeFee;
	}

	public void setUpgradeFee(double upgradeFee) {
		UpgradeFee = upgradeFee;
	}

	public List<PtRefundRuleInfo> getRefundedRules() {
		return RefundedRules;
	}

	public void setRefundedRules(List<PtRefundRuleInfo> refundedRules) {
		RefundedRules = refundedRules;
	}

	public List<PtChangeRuleInfo> getChangeRules() {
		return ChangeRules;
	}

	public void setChangeRules(List<PtChangeRuleInfo> changeRules) {
		ChangeRules = changeRules;
	}

	public Double getDiscountdiff() {
		return Discountdiff;
	}

	public void setDiscountdiff(Double discountdiff) {
		Discountdiff = discountdiff;
	}

	public String getPriceValueComb() {
		return priceValueComb;
	}

	public void setPriceValueComb(String priceValueComb) {
		this.priceValueComb = priceValueComb;
	}

	public Double getOtherTax() {
		return OtherTax;
	}

	public void setOtherTax(Double otherTax) {
		OtherTax = otherTax;
	}

	public List<InternatTaxInfo> getOtherTaxList() {
		return OtherTaxList;
	}

	public void setOtherTaxList(List<InternatTaxInfo> otherTaxList) {
		OtherTaxList = otherTaxList;
	}

	public List<Fare> getFares() {
		return fares;
	}

	public void setFares(List<Fare> fares) {
		this.fares = fares;
	}

	public String getBrandCode() {
		return BrandCode;
	}

	public void setBrandCode(String brandCode) {
		BrandCode = brandCode;
	}

	public String getRuleId() {
		return RuleId;
	}

	public void setRuleId(String ruleId) {
		RuleId = ruleId;
	}

	public String getFbrId() {
		return FbrId;
	}

	public void setFbrId(String fbrId) {
		FbrId = fbrId;
	}

	public String getSpecialFareCode() {
		return SpecialFareCode;
	}

	public void setSpecialFareCode(String specialFareCode) {
		SpecialFareCode = specialFareCode;
	}
}
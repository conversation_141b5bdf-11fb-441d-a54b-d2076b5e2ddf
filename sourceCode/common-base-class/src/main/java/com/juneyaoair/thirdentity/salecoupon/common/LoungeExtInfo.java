package com.juneyaoair.thirdentity.salecoupon.common;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description 休息室特有属性
 * @date 2019/2/25  9:41.
 */
@Data
public class LoungeExtInfo {
    @SerializedName("AirportCode")
    private String airportCode;
    @SerializedName("Terminal")
    private String terminal;
    @SerializedName("Address")
    private List<String> address;
    @SerializedName("ServiceStartTime")
    private String serviceStartTime;
    @SerializedName("ServiceEndTime")
    private String serviceEndTime;
    @SerializedName("ApplyAheadDays")
    private int applyAheadDays;  //提前申请天数  0表示可即订即用

    @SerializedName("AppointmentStatus")
    private int appointmentStatus;
}

package com.juneyaoair.thirdentity.specialservice.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Auther: lizongjie
 * @Date: 2018/11/19 14:44
 * @Description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TfSpecialServiceQueryResponse {

    private int code; // 结果代码 接口调用：0成功，1失败

    private String msg; // 结果描述


    private TfSpecialServiceQueryListResponse data;
}

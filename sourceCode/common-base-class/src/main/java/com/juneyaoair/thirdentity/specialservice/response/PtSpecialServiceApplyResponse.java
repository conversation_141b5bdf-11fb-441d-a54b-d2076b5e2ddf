package com.juneyaoair.thirdentity.specialservice.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by yaocf on 2018/7/3  11:33.
 * 特殊服务申请返回
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class PtSpecialServiceApplyResponse {
    private String Version;
    private String ChannelCode;
    private String UserNo;
    private String OrderNo;
    private String ChannelOrderNo;
    private String FfpId;
    private String FfpCardNo;
    private String ResultCode;
    private String ErrorInfo;
}

package com.juneyaoair.thirdentity.checkinSeat.response;

public class PtReserveSeatResponse {
    private String Id;
    private String FlightNo;
    private Integer AirsegIndex;
    private String FlightDate;
    private String DepAirportCode;
    private String ArrAirportCode;
    private String PsgrName;
    private String SeatNo;
    private String TktNo;
    private String Cabin;
    private String CardLevel;
    private String CardId;
    private String CardAirline;
    private Integer Sequence;
    private Boolean Success;
    private String Msg;
    private String Code;

    private String depTerminal;
    private String arrTerminal;

    public PtReserveSeatResponse() {
        super();
    }

    public String getId() {
        return Id;
    }

    public void setId(String id) {
        Id = id;
    }

    public String getFlightNo() {
        return FlightNo;
    }

    public void setFlightNo(String flightNo) {
        FlightNo = flightNo;
    }

    public Integer getAirsegIndex() {
        return AirsegIndex;
    }

    public void setAirsegIndex(Integer airsegIndex) {
        AirsegIndex = airsegIndex;
    }

    public String getFlightDate() {
        return FlightDate;
    }

    public void setFlightDate(String flightDate) {
        FlightDate = flightDate;
    }

    public String getDepAirportCode() {
        return DepAirportCode;
    }

    public void setDepAirportCode(String depAirportCode) {
        DepAirportCode = depAirportCode;
    }

    public String getArrAirportCode() {
        return ArrAirportCode;
    }

    public void setArrAirportCode(String arrAirportCode) {
        ArrAirportCode = arrAirportCode;
    }

    public String getPsgrName() {
        return PsgrName;
    }

    public void setPsgrName(String psgrName) {
        PsgrName = psgrName;
    }

    public String getSeatNo() {
        return SeatNo;
    }

    public void setSeatNo(String seatNo) {
        SeatNo = seatNo;
    }

    public String getTktNo() {
        return TktNo;
    }

    public void setTktNo(String tktNo) {
        TktNo = tktNo;
    }

    public String getCabin() {
        return Cabin;
    }

    public void setCabin(String cabin) {
        Cabin = cabin;
    }

    public String getCardLevel() {
        return CardLevel;
    }

    public void setCardLevel(String cardLevel) {
        CardLevel = cardLevel;
    }

    public String getCardId() {
        return CardId;
    }

    public void setCardId(String cardId) {
        CardId = cardId;
    }

    public String getCardAirline() {
        return CardAirline;
    }

    public void setCardAirline(String cardAirline) {
        CardAirline = cardAirline;
    }

    public Integer getSequence() {
        return Sequence;
    }

    public void setSequence(Integer sequence) {
        Sequence = sequence;
    }

    public Boolean getSuccess() {
        return Success;
    }

    public void setSuccess(Boolean success) {
        Success = success;
    }

    public String getMsg() {
        return Msg;
    }

    public void setMsg(String msg) {
        Msg = msg;
    }

    public String getCode() {
        return Code;
    }

    public void setCode(String code) {
        Code = code;
    }

    public String getDepTerminal() {
        return depTerminal;
    }

    public void setDepTerminal(String depTerminal) {
        this.depTerminal = depTerminal;
    }

    public String getArrTerminal() {
        return arrTerminal;
    }

    public void setArrTerminal(String arrTerminal) {
        this.arrTerminal = arrTerminal;
    }
}

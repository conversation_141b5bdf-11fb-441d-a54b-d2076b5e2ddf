package com.juneyaoair.thirdentity.salecoupon.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Created by yaocf on 2018/7/19  17:04.
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class PtSaleCouponBuyRequest {
    private String Version;
    private String ChannelCode;
    private String UserNo;
    private String FfpId;
    private String FfpCardNo;
    private String ChannelOrderNo;
    private String OrderRequestIp;
    /**
     * 实际支付金额，不包含积分抵扣
     */
    private double PayAmount;
    //积分支付
    private int UseScore;
    private String Linker;
    private String LinkerHandphone;
    private List<SaleCouponBuy> SaleCouponList;
    @NoArgsConstructor
    @AllArgsConstructor
    @Data
    public static class SaleCouponBuy{
        private String ActivityNo;
        private int Count;//购买数量
        private String CouponSource;//券来源
    }

    public PtSaleCouponBuyRequest(String version,String channelCode,String userNo){
            this.Version = version;
            this.ChannelCode = channelCode;
            this.UserNo = userNo;
    }

    public SaleCouponBuy getSaleCouponBuy(){
        SaleCouponBuy saleCouponBuy = new SaleCouponBuy();
        return  saleCouponBuy;
    }
}


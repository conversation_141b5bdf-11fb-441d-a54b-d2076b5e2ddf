package com.juneyaoair.thirdentity.specialservice.request;

import com.juneyaoair.thirdentity.specialservice.SpecialService;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Created by yaocf on 2018/7/3  10:55.
 * 特殊服务申请
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class PtSpecialServiceApplyRequest {
    private String Version;  //接口版本号
    private String ChannelCode;  //渠道用户号
    private String UserNo;  //渠道工作人员号
    private String FfpId;  //常旅客ID
    private String FfpCardNo;  //常旅客卡号
    private String ChannelOrderNo;  //渠道订单编号
    private String OrderRequestIp;  //预订人IP地址
    private String Linker;  //联系人
    private String LinkerHandphone; //联系人移动电话
    private String LinkerEmail;  //联系人邮箱
    private String Remark;  //备注
    private List<SpecialService> SpecialServiceList;  //特殊服务列表

    public PtSpecialServiceApplyRequest(String version,String channelCode,String userNo){
        this.Version = version;
        this.ChannelCode = channelCode;
        this.UserNo = userNo;

    }

}

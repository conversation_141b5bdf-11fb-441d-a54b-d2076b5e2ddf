package com.juneyaoair.thirdentity.salecoupon.request;

import lombok.Data;

/**
 * <AUTHOR>
 * @description  我的权益券请求
 * @date 2018/8/21  16:51.
 */
@Data
public class PtSaleCouponGetRequest {
    /**
     * 接口版本号
     */
    private String Version;
    /**
     * 渠道用户号
     */
    private String ChannelCode;
    /**
     * 渠道工作人员号
     */
    private String UserNo;
    /**
     * 常旅客ID
     */
    private String FfpId;
    /**
     * 常旅客卡号
     */
    private String FfpCardNo;
    /**
     * 可售券状态
     */
    private String CouponStates;
    /**
     * 页码
     */
    private Integer PageNo;
    /**
     * 每页大小
     */
    private Integer PageSize;

    public PtSaleCouponGetRequest(String version, String channelCode, String userNo, String ffpId, String ffpCardNo) {
        Version = version;
        ChannelCode = channelCode;
        UserNo = userNo;
        FfpId = ffpId;
        FfpCardNo = ffpCardNo;
    }
}

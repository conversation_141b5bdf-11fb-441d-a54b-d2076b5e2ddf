package com.juneyaoair.thirdentity.boardingpass.common;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @date 2019/9/11  16:16.
 */
@Data
public class BasePay {
    @SerializedName("Payed")
    private String payed;
    @SerializedName("PayAt")
    private Long payAt;
    @SerializedName("PayAmount")
    private Long payAmount;  //精确到分： 1000表示 10元
    @SerializedName("PayLimit")
    private Long payLimit; //支付剩余的时间:秒为单位
    @SerializedName("Currency")
    private String currency;
    @SerializedName("UseScore")
    private int useScore;
    @SerializedName("RefundRule")
    private String refundRule; //退款规则
}

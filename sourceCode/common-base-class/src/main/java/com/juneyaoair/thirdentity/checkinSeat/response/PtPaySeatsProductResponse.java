package com.juneyaoair.thirdentity.checkinSeat.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Author:zc
 * @Description:
 * @Date:Created in 8:59 2019/3/26
 * @Modified by:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PtPaySeatsProductResponse {
    private String Version;
    private String ChannelCode;
    private String UserNo;
    private double PayAmount;
    private int UseScore;
    private String ResultCode;
    private String ErrorInfo;
    private List<PtReserveSeatResponse> Infos;
    private String OrderNo;
    private String ChannelOrderNo;

}

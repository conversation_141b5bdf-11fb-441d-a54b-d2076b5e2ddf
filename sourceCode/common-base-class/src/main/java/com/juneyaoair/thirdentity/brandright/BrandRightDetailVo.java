package com.juneyaoair.thirdentity.brandright;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName BrandRightDetailVo
 * <AUTHOR>
 * @Description
 * @Date 2021-05-11 16:53
 **/
@Data
@NoArgsConstructor
public class BrandRightDetailVo {
    private String RightCode; //权益码
    private String RightName; //权益名称
    private String RightType; //权益类型
    private String ServiceState; //服务状态 F,//免费服务 √  C,//需付费 B,//部分舱位允许 N,//无此服务 X  E //该业务模式不存在 -
    private String ServiceStateCon; //服务状态 F,//免费服务 √  C,//需付费 B,//部分舱位允许 N,//无此服务 X  E //该业务模式不存在 -
    private Double Quantity; //数量
    private String Spec; //规格
    private String Unit; //单位
    private int Seq; //排序
    private String Description; //描述
    private String Remark; //备注
}

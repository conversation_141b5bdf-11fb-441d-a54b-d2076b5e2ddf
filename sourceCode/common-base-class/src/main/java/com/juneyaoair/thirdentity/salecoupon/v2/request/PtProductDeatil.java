package com.juneyaoair.thirdentity.salecoupon.v2.request;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

/**
 * @ClassName PtProductDeatil
 * <AUTHOR>
 * @Description 产品相关信息
 * @Date 2020-10-12 9:36
 **/
@Data
public class PtProductDeatil {

    @SerializedName("ProductName")
    private String productName; //产品名称
    @SerializedName("ProductImg")
    private String productImg; //产品图片
    @SerializedName("ProductStandard")
    private String productStandard; //产品规格
    @SerializedName("ProductType")
    private String productType; //产品类型
    @SerializedName("ScheduledCommission")
    private Double scheduledCommission; // 提成比 2020-11-17
}

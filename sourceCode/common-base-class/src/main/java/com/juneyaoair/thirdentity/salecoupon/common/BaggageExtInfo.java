package com.juneyaoair.thirdentity.salecoupon.common;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

/**
 * <AUTHOR>
 * @description 行李额度特有属性
 * @date 2019/2/25  9:42.
 */
@Data
public class BaggageExtInfo {
    @SerializedName("BaggageUnit")
    private String baggageUnit;
    @SerializedName("BaggageValue")
    private int baggageValue;
    @SerializedName("BaggageSize")
    private String baggageSize;
    @SerializedName("IsIntl")
    private String isIntl;//国内DOMESTIC;国际INTL
    @SerializedName("ProductName")
    private String productName;
}

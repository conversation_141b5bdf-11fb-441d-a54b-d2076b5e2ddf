package com.juneyaoair.thirdentity.specialservice.response;

import com.juneyaoair.thirdentity.specialservice.SpecialService;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Created by yaocf on 2018/7/4  17:22.
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class PtSpecialServiceQueryResponse {
    private String Version;
    private String ChannelCode;
    private String UserNo;
    private String ResultCode;
    private String ErrorInfo;
    private List<SpecialService> SpecialServiceList;
}

package com.juneyaoair.thirdentity.response.checkin;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;

@XmlRootElement(name = "SeatChart")
@XmlAccessorType(XmlAccessType.FIELD)
public class SeatChart {
	private String isRow;//是否是座位行 'T' 'F'
	private String isNoColumn;//数字列
	private String isExit;//是否是安全通道 'T' 'F'
	private String rowNo; //行号  '4'
	private String columnNo; //列号 ''为序号  'A'为列号
	private String seatNumber; //座位号
	private String seatValue; //可用标始  如 * . =
	
	
	public String getIsRow() {
		return isRow;
	}
	public void setIsRow(String isRow) {
		this.isRow = isRow;
	}
	public String getIsNoColumn() {
		return isNoColumn;
	}
	public void setIsNoColumn(String isNoColumn) {
		this.isNoColumn = isNoColumn;
	}
	public String getIsExit() {
		return isExit;
	}
	public void setIsExit(String isExit) {
		this.isExit = isExit;
	}
	public String getRowNo() {
		return rowNo;
	}
	public void setRowNo(String rowNo) {
		this.rowNo = rowNo;
	}
	public String getColumnNo() {
		return columnNo;
	}
	public void setColumnNo(String columnNo) {
		this.columnNo = columnNo;
	}
	public String getSeatNumber() {
		return seatNumber;
	}
	public void setSeatNumber(String seatNumber) {
		this.seatNumber = seatNumber;
	}
	public String getSeatValue() {
		return seatValue;
	}
	public void setSeatValue(String seatValue) {
		this.seatValue = seatValue;
	}
	
		
	
	
	
}

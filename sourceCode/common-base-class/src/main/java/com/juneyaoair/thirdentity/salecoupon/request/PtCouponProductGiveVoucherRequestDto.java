package com.juneyaoair.thirdentity.salecoupon.request;

import lombok.Data;

/**
 * <AUTHOR>
 * @description  权益赠送请求
 * @date 2019/3/8  9:46.
 */
@Data
public class PtCouponProductGiveVoucherRequestDto {
    private String Version;
    private String ChannelCode;
    private String UserNo;
    private String FfpId;
    private String FfpCardNo;
    private String[] VoucherNoList;
    private int CancelType; // 2020-07-23 取消类型  1-由人为操作取消 0-后台定时任务取消  默认值0
    private String CouponSource;

    public PtCouponProductGiveVoucherRequestDto(String version,String channelCode,String userNo){
        this.Version = version;
        this.ChannelCode = channelCode;
        this.UserNo = userNo;
    }
}

package com.juneyaoair.thirdentity.passengers.common;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 常用乘机人信息
 * @date 2019/7/18  18:39.
 */
@Data
public class GeneralContactInfo {
    private Integer GeneralContactId;
    private String ChannelCustomerNo;
    private String ChannelCustomerType;
    private String PassengerName;
    private String PassEnNameS;//乘客英文姓
    private String PassEnNameF;//乘客英文名
    private String PassengerType;//ADT － 成人，CHD － 儿童，INF － 婴儿
    private String FfCardNo;//HO+卡号
    private String SaCardNo;//星盟 XX+卡号
    private String CountryTelCode;//星盟 XX+卡号
    private String HandphoneNo;//星盟 XX+卡号
    private String Birthdate;//出生日期yyyy-MM-dd
    private String Sex;//  M/F
    private String Nationality;
    private String LastBookingTime;
    private Integer BookingNum;
    private String IsGMJC; //是否军警残，Y是，N否
    private String ContactType;// 常用乘机人类型 A=儿童畅飞，B=成人畅飞
    private List<GeneralContactCertInfo> ContactCertList;
    //  修改人
    private String ModiferName;

    //  修改时间
    private Date ModifyDatetime;


    //  创建人
    private String CreatorName;

    //  创建时间
    private Date CreateDatetime;
}

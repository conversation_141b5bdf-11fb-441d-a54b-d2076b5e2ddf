package com.juneyaoair.thirdentity.salecoupon.common;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @date 2019/3/15  13:24.
 */
@Data
public class EquityUnitsUsage {
    /**
     * 凭证号
     */
    @SerializedName(value="EquityNo")
    private String equityNo;

    /**
     * 航班号
     */
    @SerializedName(value="FlightNo")
    private String flightNo;

    /**
     * 出发机场
     */
    @SerializedName(value="DepCity")
    private String depCity;

    /**
     * 到达机场
     */
    @SerializedName(value="ArrCity")
    private String arrCity;

    /**
     * 起飞时间
     */
    @SerializedName(value="DepTime")
    private String depTime;

    /**
     * 到达时间
     */
    @SerializedName(value="ArrTime")
    private String arrTime;

    /**
     * 舱位
     */
    @SerializedName(value="Cabin")
    private String cabin;
}

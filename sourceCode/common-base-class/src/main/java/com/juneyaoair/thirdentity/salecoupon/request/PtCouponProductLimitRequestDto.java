package com.juneyaoair.thirdentity.salecoupon.request;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description 资源适配航线航班请求参数
 * @date 2019/3/11  19:04.
 */
@Data
@NoArgsConstructor
public class PtCouponProductLimitRequestDto {
    private String Version;
    private String ChannelCode;
    private String UserNo;
    private String FfpId;
    private String FfpCardNo;
    private String SearchTypes;  //查询品类

    public PtCouponProductLimitRequestDto(String version,String channelCode,String userNo,String searchTypes){
        this.Version = version;
        this.ChannelCode = channelCode;
        this.UserNo = userNo;
        this.SearchTypes = searchTypes;
    }
}

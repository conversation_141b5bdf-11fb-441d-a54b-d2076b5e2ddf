package com.juneyaoair.thirdentity.request.booking;

import io.swagger.annotations.ApiModelProperty;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

@XmlRootElement(name = "TicketInfoRequest")
@XmlAccessorType(XmlAccessType.FIELD)
public class PtTicketBookingReq {

    private String Version; //接口版本号	10
    private String ChannelCode; //渠道用户号	B2C,CC等
    private String UserNo; //渠道工作人员号	分配给渠道用户的工作人员号
    private String ChannelOrderNo; //渠道订单编号	同一渠道不能有相同的渠道订单编号
    private String ChannelCustomerNo; //渠道客户编号	通过渠道下订单的购买者，在渠道自己系统识别该客户的编号,该值为-1时表示匿名用户
    private String OrderRequestIp; //预订人IP地址	下订单购买者的IP
    private String RouteType; //航程类型	单程：OW；往返：RT（RT时请注意飞行方向）
    private String InterFlag; //国内国际标识	固定国内票标识：D
    private String CurrencyCode; //币种代码	CNY人民币
    private String LangCode;    //语言
    private String TicketOrderSort; //订单种类	普通订单：Normal
    private String TicketOrderType; //散客:Person;团队:Group
    private double PayAmount; //8(其中2位小数)	订单总金额
    private List<PtTicketOrderInfo> FormerTicketOrderInfoList;//2021-11-22 旧的机票信息
    private List<PtTicketOrderInfo> TicketOrderInfoList; //机票订单信息列表	"成人与儿童不同舱，或同一航段一次预订一个以上舱位价格时（如：预订5人，Ｘ舱３人，Ｖ舱２人），一次产生多个PNR"
    private boolean IsPostTripCert; //是否配送行程单
    private PtTripCertSendInfo TripCertSendOTO; //报销用行程单配送信息
    private boolean IsBuyLounge;
    private List<PtLoungeQuery> LoungeQueryList;
    private boolean IsBuyWifi;
    private List<PtWifiQuery> WifiQueryList;
    private int UseScoreTotal;
    private String CouponCheckFlag;
    private String RandCode;//校验参数，此参数必须与订单中心配的参数一致
    private String FareSource;//运价来源 Searchone,HO(根据运价查询时的运价来源传入)
    private String DesignatedClassFlag;//是否是免票下单  Y/N
    private String MultiBuy;//是否是多程购买 Y/N
    private String IsNewProcessInsurance;// 是否使用新流程购买保险
    private String BrandBuy; //2021-05-12 Y表示使用国际品牌运价下单
    //2021-05-31 旅客类型 首页聚合查询旅客类型
    private List<String> PassengerType;

    private String DisneyVisitDate;


    private String FlightFareType; //指定类型

    /**
     * 是否一单多券入口下单 "1": 是一单多券
     */
    private String CouponScene;

    private String UuId;

    @ApiModelProperty("是否购买权益券")
    private Boolean IsBuyCoupon;
    @ApiModelProperty("权益券预定列表")
    private List<CouponProductInfo> CouponProductInfoList;

    public Boolean getIsBuyCoupon() {
        return IsBuyCoupon;
    }

    public void setIsBuyCoupon(Boolean buyCoupon) {
        IsBuyCoupon = buyCoupon;
    }

    public List<CouponProductInfo> getCouponProductInfoList() {
        return CouponProductInfoList;
    }

    public void setCouponProductInfoList(List<CouponProductInfo> couponProductInfoList) {
        CouponProductInfoList = couponProductInfoList;
    }

    public String getUuId() {
        return UuId;
    }

    public void setUuId(String uuId) {
        UuId = uuId;
    }

    public String getDisneyVisitDate() {
        return DisneyVisitDate;
    }

    public void setDisneyVisitDate(String disneyVisitDate) {
        DisneyVisitDate = disneyVisitDate;
    }

    public PtTicketBookingReq() {

    }

    public PtTicketBookingReq(String version, String channelCode, String userNo, String currency, String langCode) {
        this.Version = version;
        this.ChannelCode = channelCode;
        this.UserNo = userNo;
        this.CurrencyCode = currency;
        this.LangCode = langCode;
    }

    public String getVersion() {
        return Version;
    }

    public void setVersion(String version) {
        Version = version;
    }

    public String getChannelCode() {
        return ChannelCode;
    }

    public void setChannelCode(String channelCode) {
        ChannelCode = channelCode;
    }

    public String getUserNo() {
        return UserNo;
    }

    public void setUserNo(String userNo) {
        UserNo = userNo;
    }

    public String getChannelOrderNo() {
        return ChannelOrderNo;
    }

    public void setChannelOrderNo(String channelOrderNo) {
        ChannelOrderNo = channelOrderNo;
    }

    public String getChannelCustomerNo() {
        return ChannelCustomerNo;
    }

    public void setChannelCustomerNo(String channelCustomerNo) {
        ChannelCustomerNo = channelCustomerNo;
    }

    public String getOrderRequestIp() {
        return OrderRequestIp;
    }

    public void setOrderRequestIp(String orderRequestIp) {
        OrderRequestIp = orderRequestIp;
    }

    public String getRouteType() {
        return RouteType;
    }

    public void setRouteType(String routeType) {
        RouteType = routeType;
    }

    public String getInterFlag() {
        return InterFlag;
    }

    public void setInterFlag(String interFlag) {
        InterFlag = interFlag;
    }

    public String getCurrencyCode() {
        return CurrencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        CurrencyCode = currencyCode;
    }

    public String getTicketOrderSort() {
        return TicketOrderSort;
    }

    public void setTicketOrderSort(String ticketOrderSort) {
        TicketOrderSort = ticketOrderSort;
    }

    public double getPayAmount() {
        return PayAmount;
    }

    public void setPayAmount(double payAmount) {
        PayAmount = payAmount;
    }

    public List<PtTicketOrderInfo> getFormerTicketOrderInfoList() {
        return FormerTicketOrderInfoList;
    }

    public void setFormerTicketOrderInfoList(List<PtTicketOrderInfo> formerTicketOrderInfoList) {
        FormerTicketOrderInfoList = formerTicketOrderInfoList;
    }

    public List<PtTicketOrderInfo> getTicketOrderInfoList() {
        return TicketOrderInfoList;
    }

    public void setTicketOrderInfoList(List<PtTicketOrderInfo> ticketOrderInfoList) {
        TicketOrderInfoList = ticketOrderInfoList;
    }

    public String getFlightFareType() {
        return FlightFareType;
    }

    public void setFlightFareType(String flightFareType) {
        FlightFareType = flightFareType;
    }

    public String getTicketOrderType() {
        return TicketOrderType;
    }

    public void setTicketOrderType(String ticketOrderType) {
        TicketOrderType = ticketOrderType;
    }

    public boolean isIsPostTripCert() {
        return IsPostTripCert;
    }

    public void setIsPostTripCert(boolean isPostTripCert) {
        IsPostTripCert = isPostTripCert;
    }

    public PtTripCertSendInfo getTripCertSendOTO() {
        return TripCertSendOTO;
    }

    public void setTripCertSendOTO(PtTripCertSendInfo tripCertSendOTO) {
        TripCertSendOTO = tripCertSendOTO;
    }

    public String getLangCode() {
        return LangCode;
    }

    public void setLangCode(String langCode) {
        LangCode = langCode;
    }

    public boolean isPostTripCert() {
        return IsPostTripCert;
    }

    public void setPostTripCert(boolean postTripCert) {
        IsPostTripCert = postTripCert;
    }

    public boolean isBuyLounge() {
        return IsBuyLounge;
    }

    public void setBuyLounge(boolean buyLounge) {
        IsBuyLounge = buyLounge;
    }

    public List<PtLoungeQuery> getLoungeQueryList() {
        return LoungeQueryList;
    }

    public void setLoungeQueryList(List<PtLoungeQuery> loungeQueryList) {
        LoungeQueryList = loungeQueryList;
    }

    public boolean isBuyWifi() {
        return IsBuyWifi;
    }

    public void setBuyWifi(boolean buyWifi) {
        IsBuyWifi = buyWifi;
    }

    public List<PtWifiQuery> getWifiQueryList() {
        return WifiQueryList;
    }

    public void setWifiQueryList(List<PtWifiQuery> wifiQueryList) {
        WifiQueryList = wifiQueryList;
    }

    public int getUseScoreTotal() {
        return UseScoreTotal;
    }

    public void setUseScoreTotal(int useScoreTotal) {
        UseScoreTotal = useScoreTotal;
    }

    public String getCouponCheckFlag() {
        return CouponCheckFlag;
    }

    public void setCouponCheckFlag(String couponCheckFlag) {
        CouponCheckFlag = couponCheckFlag;
    }

    public String getRandCode() {
        return RandCode;
    }

    public void setRandCode(String randCode) {
        RandCode = randCode;
    }

    public String getFareSource() {
        return FareSource;
    }

    public void setFareSource(String fareSource) {
        FareSource = fareSource;
    }

    public String getDesignatedClassFlag() {
        return DesignatedClassFlag;
    }

    public void setDesignatedClassFlag(String designatedClassFlag) {
        DesignatedClassFlag = designatedClassFlag;
    }

    public String getMultiBuy() {
        return MultiBuy;
    }

    public void setMultiBuy(String multiBuy) {
        MultiBuy = multiBuy;
    }

    public String getCouponScene() {
        return CouponScene;
    }

    public void setCouponScene(String couponScene) {
        CouponScene = couponScene;
    }

    public String getIsNewProcessInsurance() {
        return IsNewProcessInsurance;
    }

    public void setIsNewProcessInsurance(String isNewProcessInsurance) {
        this.IsNewProcessInsurance = isNewProcessInsurance;
    }

    public String getBrandBuy() {
        return BrandBuy;
    }

    public void setBrandBuy(String brandBuy) {
        BrandBuy = brandBuy;
    }

    public List<String> getPassengerType() {
        return PassengerType;
    }

    public void setPassengerType(List<String> passengerType) {
        PassengerType = passengerType;
    }

}
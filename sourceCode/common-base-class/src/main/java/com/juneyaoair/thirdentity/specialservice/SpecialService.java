package com.juneyaoair.thirdentity.specialservice;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by yaocf on 2018/7/4  17:36.
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class SpecialService {
    private String OrderNo;//客户订单号
    private String FfpId;//常旅客ID
    private String Linker; //联系人
    private String LinkerHandphone;//联系人移动电话
    private String LinkerEmail;//联系人邮箱
    private String Remark;//备注
    private String DepAirport;  //起飞机场
    private String ArrAirport;  //到达机场
    private String DepAirportName;  //起飞机场名称
    private String ArrAirportName;  //到达机场名称
    private String FlightDate;  //航班日期
    private String FlightNo;    //航班号
    private String TicketNo;    //票号
    private String ServiceType; //服务类型
    private String ServiceTypeName; //服务类型名称
    private String ServiceState;//特服状态
    private String ServiceStateName;//特服状态名称
    private String PassengerName;   //乘客姓名
    private String Birthdate;  //出生日期
    private String CertType;  //证件类型
    private String CertNo;  //证件号
    private String HandphoneNo; //手机号
    private String Sex;  //F-男 M-女
    private int Height;//身高CM
    private int Weight;//体重KG
    private int Age;//年龄
    private String MeetName;    //接机人
    private String MeetPhone;   //接机人手机
    private String MeetCertNo;  //接机人证件号
    private String MeetCertType;    //接机人证件类型
    private String SendName;  //送机人
    private String SendPhone;   //送机人手机
    private String SendCertNo;  //送机人证件号
    private String SendCertType;  //送机人证件类型
    private String Keeper;  //监护人
    private String KeeperHandphone; //监护人移动电话
    private String KeeperEmail;  //监护人邮箱
    private String CreateDatetime;  //申请时间
    private int PregnancyWeeks;

    public SpecialService(String serviceType){
        this.ServiceType = serviceType;
    }
}

package com.juneyaoair.thirdentity.specialservice.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by guan<PERSON>yin on 2018/11/15.
 * <AUTHOR>
 * 特服返回结果集
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TfSpecialServiceApplyResponse {
    /**
     * 结果代码   0：成功 1：失败
     */
    private int code;
    /**
     * 描述信息
     */
    private String msg;
    /**
     * 结果集
     */
    private ResultData data;

}

package com.juneyaoair.thirdentity.salecoupon.response;



import com.juneyaoair.thirdentity.salecoupon.common.MainVoucherInfo;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
public class QueryMyProductCouponResponse {
    private String Version;
    private String ChannelCode;
    private String ResultCode;
    private String ErrorInfo;
    private List<MainVoucherInfo> Vouchers;//主权益凭证 可用的权益券列表
    private String FfpId;
    private String FfpCardNo;
    private int PageNo;
    private int PageSize;
    private int PageCount;
    private int RecordCount;
    private int Count;//当前现有的升舱券数量
}

package com.juneyaoair.thirdentity.geetest.request;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description
 * @create 2020-07-17 14:15
 */
@Data
public class GeetestOneLoginReq {

    /**
     * SDK返回的流水号，一次有效
     */
    @SerializedName("process_id")
    private String processId;

    /**
     * 用户签名，用于账号校验
     * 生成方式：
     * 1.将app_id, 以及当前时间戳（毫秒为单位）通过”&&”连接。（app_id + ‘&&’ + timestamp）
     * 2.使用app_key进行hmac-sha256签名。将结果转换为16进制。
     */
    @SerializedName("sign")
    private String sign;

    /**
     * SDK返回的运营商Token
     */
    @SerializedName("token")
    private String token;

    /**
     * 返回手机号是否加密，默认不加密，加密传输情况加密方式为AES
     */
    @SerializedName("is_phone_encode")
    private boolean isPhoneEncode;

    /**
     * 当前时间（单位为毫秒），sign加密所使用的当前时间戳与其相同
     */
    @SerializedName("timestamp")
    private String timestamp;

    /**
     * 电信新版本接口校验码，仅电信手机号需此入参
     */
    @SerializedName("authcode")
    private String authcode;

}

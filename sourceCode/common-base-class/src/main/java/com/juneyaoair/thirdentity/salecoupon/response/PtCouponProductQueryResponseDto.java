package com.juneyaoair.thirdentity.salecoupon.response;

import com.juneyaoair.thirdentity.salecoupon.common.UpCouponProductInfo;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description 权益产品返回类
 * @date 2019/2/25  9:15.
 */
@Data
public class PtCouponProductQueryResponseDto {
    private String Version;
    private String ChannelCode;
    private String ResultCode;
    private String ErrorInfo;
    private List<UpCouponProductInfo> ProductList;
    private int TotalRecords; //产品总数
    private int TotalPages;  //总页数
}

package com.juneyaoair.thirdentity.salecoupon.v2.response;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

@Getter
@Setter
public class ProductAvailableRespDto {
    @ApiModelProperty(value = "活动号")
    private List<String> activityNo;

    @ApiModelProperty(value = "活动开始日期 yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date beginDatetime;

    @ApiModelProperty(value = "活动结束日期 yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date endDatetime;

    @ApiModelProperty(value = "通用属性")
    private List<ProductFieldDto> commonFieldList;

    @ApiModelProperty(value = "产品列表")
    private List<ProductActivityDto> productList;
}

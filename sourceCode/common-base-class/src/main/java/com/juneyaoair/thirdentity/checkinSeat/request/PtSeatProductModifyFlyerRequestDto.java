package com.juneyaoair.thirdentity.checkinSeat.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author:zc
 * @Description:
 * @Date:Created in 17:42 2019/4/15
 * @Modified by:
 */
@Data
public class PtSeatProductModifyFlyerRequestDto {
    private String Version;
    private String ChannelCode;
    private String UserNo;
    private String RequestIp;
    private PtFlightTicketSimpleDto FlightTicket;
    private String Id;
    private String CardID;
    private String CardAirline;
    private String AsrSeatNo;

    public PtSeatProductModifyFlyerRequestDto(String version, String channelCode, String userNo, String requestIp, PtFlightTicketSimpleDto flightTicket, String Id, String cardID, String cardAirline, String asrSeatNo) {
        Version = version;
        ChannelCode = channelCode;
        UserNo = userNo;
        RequestIp = requestIp;
        FlightTicket = flightTicket;
        this.Id = Id;
        CardID = cardID;
        CardAirline = cardAirline;
        AsrSeatNo = asrSeatNo;
    }

    public PtSeatProductModifyFlyerRequestDto(String version, String channelCode, String userNo, String requestIp) {
        Version = version;
        ChannelCode = channelCode;
        UserNo = userNo;
        RequestIp = requestIp;
    }

    public PtSeatProductModifyFlyerRequestDto() {
    }
}

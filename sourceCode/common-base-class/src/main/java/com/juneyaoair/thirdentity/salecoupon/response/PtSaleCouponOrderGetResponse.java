package com.juneyaoair.thirdentity.salecoupon.response;

import com.juneyaoair.baseclass.newcoupon.resp.SearchProductResponse;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Created by yaocf on 2018/7/18  16:06.
 * 我的权益券订单查看结果
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class PtSaleCouponOrderGetResponse {
    private String Version;  //接口版本号
    private String ChannelCode;  //渠道用户号
    private String UserNo;  //渠道工作人员号
    private String FfpId;  //常旅客ID
    private String FfpCardNo;   //常旅客卡号
    //可售优惠券订单集合
    private List<CouponOrder> CouponOrderList;
    private String ResultCode;  //结果代码  1001 － 成功，其它失败
    private String ErrorInfo;   //错误信息
    private int PageNo; //页码
    private int PageSize;//每页大小
    private int PageCount;//总页数
    private int RecordCount;//总记录数

    @NoArgsConstructor
    @AllArgsConstructor
    @Data
    /**
     * 订单信息
     */
    public class CouponOrder{
        private String ChannelOrderNo;
        private String OrderNo;
        private String ChannelNo;
        private String OrderDatetime;
        private String OrderState;
        private String OrderStateName;
        private String PayState;
        private String PaidDateTime;//支付时间
        private String PayMethod;//支付方式
        private double OrderTotalAmount;
        private String Currency;
        private int FfpUseTotalScore;
        private String Linker;
        private String LinkerHandphone;
        private List<String> CouponRemarks;
        private List<SaleCoupon> SaleCouponList;
        private int SaleCouponCount;
        private String IsBundleSale;//Y表示礼包销售
        private String BundleId;//礼包记录ID
        private String BundleName;//礼包名称
        private int BundleCount;//礼包销售数量
        private String Remark;


        @NoArgsConstructor
        @AllArgsConstructor
        @Data
        /**
         * 优惠券信息
         */
        public class SaleCoupon{
            private String CouponOrderId;//中台权益订单id
            private String ActivityNo;  //活动号
            private String ActivityName;//活动名称
            private String CouponCode;//券号
            private String CouponState;//券状态
            private String CouponStateName;//券状态名称
            //优惠券抵扣金额
            private double CouponPrice;
            private String CouponSource;//活动来源
            private double Price;//购买价格
            private int FfpUseScore;
            private String StartDate; //券有效期开始时间
            private String EndDate; //券有效期截止时间
            private String Remark;  //备注
            private String TktNo;  //票号
            private String CertNo; //证件号
            private String CertType;
            private String UseDays;//入园天数
            private String IsRefunded;
            private String CustomerName;//客户名称
            private String VisitDate;//迪士尼入园日期
            private Boolean EnableChange;//迪士尼是否可以改期
            /**
             * 出发机场
             */
            private String DepAirport;
            /**
             * 到达机场
             */
            private String ArrAirport;
            /**
             * 出发城市
             */
            private String DepCity;
            /**
             * 到达城市
             */
            private String ArrCity;
            /**
             * 联系人
             */
            private String Sender;
            /**
             * 联系号码
             */
            private String SenderNum;
            /**
             * 收件人
             */
            private String Receiver;
            /**
             * 收件号码
             */
            private String ReceiverNum;
            private String PurchaseNotes;
            private String RefundRules;
            private String VoucherState;
            private String FlightNumber;
            private String ContactTelphone;
            private String FlightNo;
            private String DepTime;
            private String LinkerNum;
            /**
             * 固包券时券类型
             */
            private String VoucherType;
            private String MainVoucherNo;
            /**
             * 动态（值机选座，行程单）属性
             */
            private String CouponDynamicParam;
            /**
             *
             */
            private SearchProductResponse.ProductInfo.RescourceInfo.TrafficExtInfo TrafficExt;
            /**
             * 物流单号
             */
            private String ExpressNo;
            /**
             * 无限卡出票次数
             */
            private Integer outTimes;
            /**
             * 可评价状态 2021-03-11  COMMENTABLE 可评论
             */
            private String CommentState;
            /**
             * 退款状态 2021-09-03
             */
            private String RebateState;

            @ApiModelProperty(value = "数量", notes = "预付费行李", example = "7")
            private int Amount;

            @ApiModelProperty(value = "单位", notes = "预付费行李", example = "KG")
            private String Unit;

            /**
             * 到期是否可退 1:可退 0：不可退
             */
            @ApiModelProperty(value = "到期是否可退 1:可退 0：不可退")
            private int RefundExpire;

            /**
             * 乘客类型
             */
            private String  TicketType;

            /**
             * 改期状态
             */
            private Boolean HasChanged;

            @ApiModelProperty(value = "预订时间")
            private String BookingTime;

            @ApiModelProperty(value = "出发地")
            private String FromAddress;

            @ApiModelProperty(value = "目的地")
            private String ToAddress;

            private List<CouponOrderGuestDto> GuestInfoList;

            private boolean IsPreBundle;

            private String ThirdSvcOrderNo;

            private String ThirdSvcChannelOrderNo;

            private String ThirdSvcCouponState;

            private boolean IsThirdSvcEquityUse;
        }
    }


}


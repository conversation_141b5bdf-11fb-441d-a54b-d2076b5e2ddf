package com.juneyaoair.thirdentity.specialservice.request;

import com.juneyaoair.baseclass.specialservice.request.OnePic;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * Created by guan<PERSON><PERSON>n on 2018/11/28.
 * <AUTHOR>
 * 单张图片上传请求体
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class TfSpecialServiceOnePicRequest {
    private String inftCode;
    private String memberId;
    private OnePic data;
}

package com.juneyaoair.thirdentity.salecoupon.response;

import com.juneyaoair.baseclass.request.changfly.ChangflyReq;
import com.juneyaoair.baseclass.request.changfly.ChangflyResp;
import com.juneyaoair.baseclass.response.changfly.ChangFeiCardBindingDTO;
import lombok.Data;

import java.util.List;

/**
 * @ClassName ptChangFlyResponseDTO
 * <AUTHOR>
 * @Description
 * @Date 2021-12-30 14:51
 **/
@Data
public class ptChangFlyResponseDTO {
    private String Version;
    private String ChannelCode;
    private String ResultCode;
    private String ErrorInfo;
    private List<ChangflyResp> RedeemDetails;
}

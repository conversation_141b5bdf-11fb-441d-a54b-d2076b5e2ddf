package com.juneyaoair.thirdentity.salecoupon.request;
/*
 *@program:ho-mobileapi
 *@author: <PERSON>
 *@Time: 2021/8/10  12:44
 *@description:
 */

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class PtBasicCreateOrderRequest {
    private String RequestIp; // 请求IP

    private String ChannelOrderNo; // 渠道订单号 唯一不可重复

    private BigDecimal TotalAmount; // 订单总金额，即需要支付的金额

    private String Currency; // 币种

    private Integer UseTotalScore; // 订单使用总积分

    private String PhoneCountryCode; // 手机归属国区号  86 (前面不带+)

    private String PhoneNo; // 联系人手机号

    private String Linker; // 联系人姓名

    private List<PtSubCouponOrder> SubCouponOrderList; // 子订单列表信息

    @ApiModelProperty("返程类型 单程：OW 往返：RT")
    private String FlightType;

    @ApiModelProperty("国际国内  D:国内  I:国际")
    private String InterFlag;




}

package com.juneyaoair.thirdentity.brandright.response;

import com.juneyaoair.thirdentity.brandright.BrandRightDetailVo;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName BrandRightQueryResponse
 * <AUTHOR>
 * @Description
 * @Date 2021-05-11 16:55
 **/
@NoArgsConstructor
@Data
public class BrandRightQueryResponse{
    private String BrandRightCode; //品牌权益码
    private String BrandRightName; //品牌权益名称
    private String LangCode; //语言
    private List<BrandRightDetailVo> BrandRightDetail; //品牌权益明细
}

package com.juneyaoair.thirdentity.member.response;

import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * Created by yaocf on 2018/7/23  13:10.
 */
@Data
@NoArgsConstructor
public class MemberBasicInfoSoaModel {
    //会员ID
    private long MemberId;
    //会员卡号
    private String CardNO;
    //中文姓
    private String CLastName;
    //中文名
    private String CFirstName;
    //乘客英文姓
    private String ELastName;
    //乘客英文名
    private String EFirstName;
    private String SpecialLastName;
    private String SpecialFirstName;
    private int Sex;
    private long BirthDay;
    private int MemberSalutation;
    private long  SubmitDate;
    private String GroupCard;  //集团卡
    private int PostLanguage;   //邮寄语言
    private String Remark;  //备注
    private String InviterCardNo;   //邀请人会员卡号
    private String HeadImageUrl;    //头像Url
    private String Department;  //部门
    private String Post;    //职务
    private String CompanyName; //单位名称

    /**
     * 是否设置过登录密码
     */
    private Boolean IsSetLoginPwd;
    /**
     * 是否设置过消费密码
     */
    private Boolean IsSetConsumePwd;
}

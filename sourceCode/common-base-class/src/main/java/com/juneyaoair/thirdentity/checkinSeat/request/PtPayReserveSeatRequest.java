package com.juneyaoair.thirdentity.checkinSeat.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * @Author:zc
 * @Description:
 * @Date:Created in 15:27 2019/4/1
 * @Modified by:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PtPayReserveSeatRequest {
    @NotNull(
            message = "pnrNo不能为空"
    )
    private String PnrNo;
    private int AirsegIndex;
    @NotNull(
            message = "旅客姓名不能为空"
    )
    private String PsgrName;
    @NotNull(
            message = "座位号不能为空"
    )
    private String SeatNo;
    @NotNull(
            message = "航班号不能为空"
    )
    private String FlightNo;
    @NotNull(
            message = "出发机场三字码不能为空"
    )
    private String DepAirportCode;
    @NotNull(
            message = "到达机场三字码不能为空"
    )
    private String ArrAirportCode;
    @NotNull(
            message = "航班号不能为空"
    )
    private String FlightDate;
    @NotNull(
            message = "证件号不能为空"
    )
    private String CertNo;
    @NotNull(
            message = "票号不能为空"
    )
    private String TktNo;
    private String Gender;
    private String Cabin;
    private String Mobile;
    private String Email;
    private String DepTime;
    private String ArrTime;
    private String FfpId;
    private String FfpCardNo;
    private String VisaCardNo;
    private Integer Sequence;
    private String ExistSeatNo;
    private String OrderNo;
    private BigDecimal Price;
    private String Currency;
}

package com.juneyaoair.thirdentity.salecoupon.v2.common;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description 航班日期限制
 * @date 2020/7/30  8:54.
 */
@Data
public class FlightDateLimit {
    @SerializedName("Suit")
    private List<DateLimits> suit; //适用日期
    @SerializedName("NotSuit")
    private List<DateLimits> notSuit; //不适用日期
}

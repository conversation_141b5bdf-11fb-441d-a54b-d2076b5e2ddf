package com.juneyaoair.thirdentity.salecoupon.common;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

/**
 * <AUTHOR>
 * @description 日期限制值
 * @date 2019/2/25  10:12.
 */
@Data
public class DateLimit {
    @SerializedName("LimitType")
    private int limitType; //0：不适用条件 1：适用条件 多条日期范围可能会有交集，以不适用优先。例：2018-11-11至2019-06-30适用但 2019-02-01至2019-02-20不适用
    @SerializedName("StartDate")
    private String startDate; //起始日期 为空即无限制
    @SerializedName("EndDate")
    private String endDate; //结束日期 为空即无限制
}

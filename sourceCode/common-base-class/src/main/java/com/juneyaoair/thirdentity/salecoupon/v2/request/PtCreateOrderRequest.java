package com.juneyaoair.thirdentity.salecoupon.v2.request;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2020/7/31  9:28.
 */
@Data
public class PtCreateOrderRequest {
    /**
     * RefundRule : <p>券在有效期内且未使用、未转赠时可提交退款申请。</p>
     * ProductName : 儿童畅飞卡-测试
     * FfpId : 3501537
     * FfpCardNo : 2881531575
     * PassengerName : 刘文超
     * PhoneNo : 17772893229
     * PhoneCountryCode : 86
     * ChannelOrderNo : CT072209192694496
     * TotalAmount : 6100
     * Currency : CNY
     * FlightDate :
     * ArrTime :
     * DepTime :
     * PlaneType :
     * UseScore : 0
     * BookProductInfo : [{"ProductId":"a1e7b56336bc4833acdbf24633e9ce31","BookingId":"f39f6b144d404fb38209eeb9889a4950","RuleId":"016556b8977e4d51ab60a726288a92d6","ResourceType":"ChildUnlimitedFly","StandardPrice":"200.0","SalePrice":"61.0","BookingCount":"1"}]
     */

    private String RefundRule;
    private String ProductName;
    private String FfpId;
    private String FfpCardNo;
    private String PassengerName;
    private String PhoneNo;
    private String travellerName;//客户姓名
    private String PhoneCountryCode;
    private String ChannelOrderNo;
    private BigDecimal TotalAmount; //单位为分
    private String Currency;
    private String FlightDate; //航班日期
    private String ArrTime; //到达时间 格式: HH:mm
    private String DepTime; //出发时间 格式: HH:mm
    private String PlaneType; //飞机类型 格式: 波音787
    private Integer UseScore;
    private List<PtBookProductInfo> BookProductInfo;
    private PtDeliveryInfo DeliveryInfo; //配送信息
    private String FlightNo; //航班号 2020-10-12
    private String TktNo; //票号 2020-10-12
    private String DepAirportCode; //出发机场3字码 2020-10-12
    private String ArrAirportCode; //到达机场3字码 2020-10-12
    private String DepAirportTerminal; //出发机场航站楼 2020-10-12
    private String ArrAirportTerminal; //到达机场航站楼 2020-10-12
    private String Cabin;// 仓位 2020-11-17
    /**
     * 证件号
     */
    private String CertNo;
    private String InvitationCode; //邀请码 2021-04-29
}

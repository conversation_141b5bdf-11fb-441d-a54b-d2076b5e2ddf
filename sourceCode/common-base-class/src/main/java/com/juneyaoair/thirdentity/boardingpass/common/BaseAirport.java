package com.juneyaoair.thirdentity.boardingpass.common;

import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description
 * @date 2019/9/11  16:07.
 */
@Data
@NoArgsConstructor
public class BaseAirport {
    @SerializedName("AirportCode")
    private String airportCode;
    @SerializedName("Terminal")
    private String terminal;
    @SerializedName("Time")
    private String time;//HHmm
    @SerializedName("Date")
    private String date;//yyyy-MM-dd
    private String airportName;
    private String cityName;

    public BaseAirport(String airportCode){
        this.airportCode = airportCode;
    }
}

package com.juneyaoair.thirdentity.salecoupon.v2.response;

import com.google.gson.annotations.SerializedName;
import com.juneyaoair.thirdentity.salecoupon.v2.common.ChildFlyRecord;
import com.juneyaoair.thirdentity.salecoupon.v2.common.UnlimitedFlyBindDetail;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2020/8/1  9:11.
 */
@Data
public class PtFlyCardInfo {
    @SerializedName("ChildFlyRecords")
    private List<ChildFlyRecord> childFlyRecords;
    @SerializedName("FfpId")
    private String ffpId;
    @SerializedName("FfpCardNo")
    private String ffpCardNo;
    /**
     * 已达绑定上限:yes   未达绑定上限:no
     */
    @SerializedName("ReachBindLimit")
    private String reachBindLimit;

    @SerializedName("AdultFlyRecords")
    private List<ChildFlyRecord> adultFlyRecords;

    /**
     * 是否是受赠券  1:是  0:否 2020-08-11
     */
    @SerializedName(value="IsGiving")
    private Integer isGiving;

    /**
     * 绑定儿童数 2020-11-17
     */
    @SerializedName(value="ChildBindCount")
    private Integer childBindCount;

    /**
     * 绑定成人数 2020-11-17
     */
    @SerializedName(value="AdultBindCount")
    private Integer adultBindCount;

    /**
     * 绑定信息 （券码查询时返回）2020-11-17
     */
    @SerializedName(value="BindDetail")
    private UnlimitedFlyBindDetail bindDetail;

    /**
     * 绑定信息列表 （列表查询时返回）2020-11-17
     */
    @SerializedName(value="BindDetails")
    private List<UnlimitedFlyBindDetail> bindDetails;

}

package com.juneyaoair.thirdentity.salecoupon.v2.response;


import com.juneyaoair.baseclass.response.prepaymentBaggage.Pricing;
import com.juneyaoair.thirdentity.salecoupon.v2.common.PrePayProductInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 兼容旧版本 询行李价格返回的产品Do
 */
@Data
public class QueryPrePaymentBaggagePriceResp extends PrePayProductInfo {

    @ApiModelProperty("额外行李购买收费标准表")
    private List<List<String>> PreBaggagePricingTable;

    @ApiModelProperty("是否新付费行李产品")
    private boolean useNewBaggageProduct;
}

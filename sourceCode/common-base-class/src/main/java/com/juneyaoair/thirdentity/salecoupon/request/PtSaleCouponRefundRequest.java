package com.juneyaoair.thirdentity.salecoupon.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Created by yaocf on 2018/7/19  17:22.
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class PtSaleCouponRefundRequest {
    private String Version;
    private String ChannelCode;
    private String UserNo;
    private String ChannelOrderNo;
    private String OrderNo;
    private String FfpId;
    private String FfpCardNo;
    private List<Coupon> SaleCouponList;

    @NoArgsConstructor
    @AllArgsConstructor
    @Data
    public class Coupon{
        private String CouponCode;

    }

}
//
//@NoArgsConstructor
//@AllArgsConstructor
//@Data
//class Coupon{
//    private String CouponCode;
//}
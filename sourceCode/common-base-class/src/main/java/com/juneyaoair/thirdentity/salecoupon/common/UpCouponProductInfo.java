package com.juneyaoair.thirdentity.salecoupon.common;

import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

/**
 * <AUTHOR>
 * @description 产品信息
 * @date 2019/2/25  9:17.
 * http://*************:10080/vNextPlatform/vNextOrderPlatform/wikis/01%E5%B9%B3%E5%8F%B0%E6%9C%8D%E5%8A%A1API/%E8%AE%A2%E5%8D%95(%E4%BC%98%E6%83%A0%E5%88%B8)%E6%9C%8D%E5%8A%A1API#%E5%8F%AF%E5%94%AE%E4%BA%A7%E5%93%81%E6%9F%A5%E8%AF%A2%E4%BA%A7%E5%93%81%E7%AE%A1%E7%90%86%E7%B3%BB%E7%BB%9Forderquerycouponproduct
 */
@Data
public class UpCouponProductInfo extends ProductInfo {
    @NotBlank(message="出发城市不能为空")
    private String depCityCode;
    @NotBlank(message="到达城市不能为空")
    private String arrCityCode;
    private String flightUnAvailDate; //航班不可用日期 升舱卡2.0使用
    private String flightAvailDate; //航班不可用日期 升舱卡2.0使用

}

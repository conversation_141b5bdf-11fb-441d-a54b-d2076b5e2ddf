package com.juneyaoair.thirdentity.salecoupon.request;

import com.juneyaoair.baseclass.newcoupon.bean.SingleBookCondition;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @description  我的权益券查看
 * @date 2019/2/28  17:23.
 */
@Data
@NoArgsConstructor
public class PtCouponProductGetRequestDto {
    private String Version;
    private String ChannelCode;
    private String FfpId;
    private String IdNbr;  //证件号
    private String FfpCardNo;
    private List<String> VoucherNos; //权益凭证号
    private List<String> VoucherTypes;
    private List<String> CouponState;//券状态
    private String RuleModel;
    /**

     * 用于区分app上权益券列表里的 可使用和已使用   可使用:Not 已使用:Used 已过期:Expired
     */
    private String AvailableStatus;
    private List<SingleBookCondition> SingleBookConditions;
    private Integer PageNo ;
    private Integer PageSize ;
    private String PassengerName; //旅客姓名 2021-04-13 升舱卡仅限本人使用

    public PtCouponProductGetRequestDto(String version,String channelCode){
        this.Version = version;
        this.ChannelCode = channelCode;
    }
    public PtCouponProductGetRequestDto(String version,String channelCode,String ffpId,String ffpCardNo){
        this.Version = version;
        this.ChannelCode = channelCode;
        this.FfpId = ffpId;
        this.FfpCardNo = ffpCardNo;
    }
}

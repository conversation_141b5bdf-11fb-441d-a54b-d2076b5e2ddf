package com.juneyaoair.thirdentity.boardingpass.common;

import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @date 2019/9/23  10:52.
 */
@Data
public class BoardingSegmentInfo {
    private int SegNo; //旅行顺序	第一段为从0开始，第二段为1，依次增加
    private String FlightDirection;
    private String FlightNo;
    private String DepDateTime; //yyyy-MM-dd HH:mm:ss
    private String ArrDateTime;
    private String DepAirport;
    private String ArrAirport;
    private String Cabin;
}

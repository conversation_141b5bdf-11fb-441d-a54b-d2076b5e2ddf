package com.juneyaoair.thirdentity.checkinSeat.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.PostUpdate;

/**
 * @Author:zc
 * @Description:
 * @Date:Created in 17:47 2019/4/15
 * @Modified by:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PtSeatProductModifyFlyerResponseDto {
    private String Version;
    private String ChannelCode;
    private String UserNo;
    private String RequestIp;
    private String ResultCode;
    private String ErrorInfo;
}

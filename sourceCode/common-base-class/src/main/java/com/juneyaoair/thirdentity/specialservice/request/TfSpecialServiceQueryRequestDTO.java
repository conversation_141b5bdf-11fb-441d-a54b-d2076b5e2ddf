package com.juneyaoair.thirdentity.specialservice.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Auther: lizongjie
 * @Date: 2018/11/20 19:02
 * @Description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class TfSpecialServiceQueryRequestDTO {
    private String startTime; // 开始时间
    private String endTime;   // 结束时间
    private String applyStatus; // 审核状态 1.预约成功、2.申请成功、3.受理中、4.受理失败、5.已取消
    private List<String> ids; // 申请单id
    private String id;//取消订单的id
    private String ticket_no; //票号
    private String flight_no; //航班号
    private String flight_date; //航班日期
    private String depature_station; //始发站
    private String destination_station; //到达站
}


package com.juneyaoair.thirdentity.request.order.refund.insurance;

/**
 * Created by qinxiaoming on 2016-6-1.
 */
public class PtRefundInsureDetailReq {
    private String Version; //接口版本号	10
    private String ChannelCode; //渠道用户号	B2C,CC等
    private String UserNo; //渠道工作人员号	分配给渠道用户的工作人员号

    private String InsuranceBillNo;
    private String CustomerNo;



    public PtRefundInsureDetailReq() {
    }

    public PtRefundInsureDetailReq(String version, String channelCode, String userNo) {
        Version = version;
        ChannelCode = channelCode;
        UserNo = userNo;
    }

    public String getVersion() {
        return Version;
    }

    public void setVersion(String version) {
        Version = version;
    }

    public String getChannelCode() {
        return ChannelCode;
    }

    public void setChannelCode(String channelCode) {
        ChannelCode = channelCode;
    }

    public String getUserNo() {
        return UserNo;
    }

    public void setUserNo(String userNo) {
        UserNo = userNo;
    }

    public String getInsuranceBillNo() {
        return InsuranceBillNo;
    }

    public void setInsuranceBillNo(String insuranceBillNo) {
        InsuranceBillNo = insuranceBillNo;
    }

    public String getCustomerNo() {
        return CustomerNo;
    }

    public void setCustomerNo(String customerNo) {
        CustomerNo = customerNo;
    }
}

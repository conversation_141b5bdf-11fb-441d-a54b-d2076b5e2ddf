package com.juneyaoair.thirdentity.salecoupon.common;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

/**
 * <AUTHOR>
 * @description  预留登机牌特有属性
 * @date 2019/2/25  9:43.
 */
@Data
public class CheckinSubstitutionExtInfo {
    @SerializedName("MinMinutesAheadTakeoff")
    private int minMinutesAheadTakeoff; //最少提前分钟数
    @SerializedName("MaxMinutesAheadTakeoff")
    private int maxMinutesAheadTakeoff; //最大提前分钟数
}

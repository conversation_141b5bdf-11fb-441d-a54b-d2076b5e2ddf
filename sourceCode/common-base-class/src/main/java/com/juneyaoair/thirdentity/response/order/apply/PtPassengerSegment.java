package com.juneyaoair.thirdentity.response.order.apply;

import com.google.gson.annotations.SerializedName;
import com.juneyaoair.baseclass.av.common.TravelPrivilege;
import com.juneyaoair.baseclass.response.order.query.InsuranceInfo;
import com.juneyaoair.baseclass.response.order.query.SegmentInfo;
import com.juneyaoair.thirdentity.av.comm.PtChangeRuleInfo;
import com.juneyaoair.thirdentity.av.comm.PtRefundRuleInfo;
import io.swagger.annotations.ApiModelProperty;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.math.BigDecimal;
import java.util.List;
import java.util.Set;


@XmlRootElement(name = "PassengerSegment")
@XmlAccessorType(XmlAccessType.FIELD)
public class PtPassengerSegment {
	private int PassengerSegmentID; //人航段ID	
	private int SegmentID; //航段ID	保存在统一订单数据库中的ID
	private int PassengerID; //乘客ID	保存在统一订单数据库中的ID
	private String TicketNo; //票号	
	private int SegmentSeq; //票面航段序号	
	private String TicketState; //机票状态	OPEN FOR USE,USED/FLOWN,REFUNDED
	/**
	 * yyyy-MM-dd HH:mm:ss
	 */
	private String TicketDatetime;
	private String Currency; //币种	
	private double TicketPrice; //票面价	
	private double PricePaid; //实际支付票价	
	private double YQTax; //燃油费	
	private double CNTax; //机建税	
	private double InsuranceAmount; //保险金额	
	private String InsuranceState; //保险状态
	private String Comment;/// 备注项	用于前端显示说明信息，如：解释签注信息或备注中转联程提供住宿
	private String Baggage;/// 行李重量
	private String ValidityPeriod;/// 客票有效期	1Y1M1D
	private String MinStay;/// 最短停留时间	1Y1M1D

	private boolean RefundedFlag; //是否可退票	
	private String RefundedComment; //退票政策描述	说明退票费收取或计算方法
	private String ChangedComment; //变更政策描述	说明改期、签转和升舱政策
	private boolean RescheduledFlag; //是否可免费改期	
	private int FreeChangeTimes; //免费改期次数	免费改期不限次数时，目前运价系统会给出99次
	private boolean ChangeAirLineFlag; //是否可改签	
	private boolean UpgradeFlag; //是否可升舱	
	private String FareID; //运价编号	运价系统中该条运价的主键，计算退票费时有用
	private boolean IsUseScore; //是否使用积分	"退票费计算时：1.使用积分抵用金额的在算退票费时不含税航段票价=PricePaid + Deductibls
		//2.未使用积分抵用金额的不含税航段票价=PricePaid"
	private double Deductibls; //积分抵用金额
	private double CouponAmount; //优惠券金额
	private double GifiScore; //赠送积分

	private Double UpgradeFee;/// 升舱手续费
	private List<InsuranceInfo> InsuranceList;/// 18保险信息列表
	private SegmentInfo SegmentInfoOTO;/// 改期前的航段信息

	@SerializedName(value = "TourCode")
	private String tourCode;

	/**
	 *退票规则
	 */
	@SerializedName(value = "RefundedRules")
	private List<PtRefundRuleInfo> refundedRules;
	/**
	 *变更规则
	 */
	@SerializedName(value = "ChangeRules")
	private List<PtChangeRuleInfo> changeRules;

	/**
	 * 原机票子订单ID
	 * @return
	 */
	private int LaterPassengerSegmentId;

	/**
	 *原人航段Id
	 * @return
	 */
	private int FormerPassengerSegmentId;
	private String PriceProductType;
	private String FareBasis;

	/**
	 * 订单退票申请状态
	 * @see com.juneyaoair.appenum.order.OrderRefundStateEnum
	 */
	@SerializedName("RefundState")
	private String refundState;
	/**
	 * 退款流程状态
	 * @see com.juneyaoair.appenum.order.RefundRebateStateEnum
	 */
	@SerializedName("RefundRebateState")
	private String refundRebateState;

	/**
	 * 行李额信息
	 */
	@SerializedName("OrderDetailBaggage")
	private OrderDetailBaggage orderDetailBaggage;

	/**
	 * 出行尊享
	 */
	@SerializedName("PrivilegeList")
	private List<TravelPrivilege> privilegeList;

	private String BrandCode; //2021-05-12 国际品牌运价号 国际品牌运价下单时使用

	@SerializedName(value = "ShippingRulesLabel")
	@ApiModelProperty(value = "规则标签 ShippingRulesLabelEnum")
	private Set<String> shippingRulesLabel;

	//单个航段优惠的金额(拥军)
	@SerializedName(value = "DiscountAmount")
	private BigDecimal  discountAmount;

	public BigDecimal getDiscountAmount() {
		return discountAmount;
	}

	public void setDiscountAmount(BigDecimal discountAmount) {
		this.discountAmount = discountAmount;
	}

	public BigDecimal getDiscountedPrice() {
		return discountedPrice;
	}

	public void setDiscountedPrice(BigDecimal discountedPrice) {
		this.discountedPrice = discountedPrice;
	}

	//单个航段打完折后价格(拥军)
	@SerializedName(value = "DiscountedPrice")
	private BigDecimal    discountedPrice;


	@SerializedName(value = "OriginalChangeFee")
	private BigDecimal  OriginalChangeFee;

	public BigDecimal getOriginalChangeFee() {
		return OriginalChangeFee;
	}

	public void setOriginalChangeFee(BigDecimal originalChangeFee) {
		OriginalChangeFee = originalChangeFee;
	}

	public String getPriceProductType() {
		return PriceProductType;
	}

	public void setPriceProductType(String priceProductType) {
		PriceProductType = priceProductType;
	}

	public String getFareBasis() {
		return FareBasis;
	}

	public void setFareBasis(String fareBasis) {
		FareBasis = fareBasis;
	}

	public int getLaterPassengerSegmentId() {
		return LaterPassengerSegmentId;
	}

	public void setLaterPassengerSegmentId(int laterPassengerSegmentId) {
		LaterPassengerSegmentId = laterPassengerSegmentId;
	}

	public int getFormerPassengerSegmentId() {
		return FormerPassengerSegmentId;
	}

	public void setFormerPassengerSegmentId(int formerPassengerSegmentId) {
		FormerPassengerSegmentId = formerPassengerSegmentId;
	}

	public int getPassengerSegmentID() {
		return PassengerSegmentID;
	}
	public void setPassengerSegmentID(int passengerSegmentID) {
		PassengerSegmentID = passengerSegmentID;
	}
	public int getSegmentID() {
		return SegmentID;
	}
	public void setSegmentID(int segmentID) {
		SegmentID = segmentID;
	}
	public int getPassengerID() {
		return PassengerID;
	}
	public void setPassengerID(int passengerID) {
		PassengerID = passengerID;
	}
	public String getTicketNo() {
		return TicketNo;
	}
	public void setTicketNo(String ticketNo) {
		TicketNo = ticketNo;
	}
	public int getSegmentSeq() {
		return SegmentSeq;
	}
	public void setSegmentSeq(int segmentSeq) {
		SegmentSeq = segmentSeq;
	}
	public String getTicketState() {
		return TicketState;
	}
	public void setTicketState(String ticketState) {
		TicketState = ticketState;
	}
	public String getTicketDatetime() {
		return TicketDatetime;
	}
	public void setTicketDatetime(String ticketDatetime) {
		TicketDatetime = ticketDatetime;
	}
	public String getCurrency() {
		return Currency;
	}
	public void setCurrency(String currency) {
		Currency = currency;
	}
	public double getTicketPrice() {
		return TicketPrice;
	}
	public void setTicketPrice(double ticketPrice) {
		TicketPrice = ticketPrice;
	}
	public double getPricePaid() {
		return PricePaid;
	}
	public void setPricePaid(double pricePaid) {
		PricePaid = pricePaid;
	}
	public double getYQTax() {
		return YQTax;
	}
	public void setYQTax(double yQTax) {
		YQTax = yQTax;
	}
	public double getCNTax() {
		return CNTax;
	}
	public void setCNTax(double cNTax) {
		CNTax = cNTax;
	}
	public double getInsuranceAmount() {
		return InsuranceAmount;
	}
	public void setInsuranceAmount(double insuranceAmount) {
		InsuranceAmount = insuranceAmount;
	}
	public String getInsuranceState() {
		return InsuranceState;
	}
	public void setInsuranceState(String insuranceState) {
		InsuranceState = insuranceState;
	}
	public boolean isRefundedFlag() {
		return RefundedFlag;
	}
	public void setRefundedFlag(boolean refundedFlag) {
		RefundedFlag = refundedFlag;
	}
	public String getRefundedComment() {
		return RefundedComment;
	}
	public void setRefundedComment(String refundedComment) {
		RefundedComment = refundedComment;
	}
	public String getChangedComment() {
		return ChangedComment;
	}
	public void setChangedComment(String changedComment) {
		ChangedComment = changedComment;
	}
	public boolean isRescheduledFlag() {
		return RescheduledFlag;
	}
	public void setRescheduledFlag(boolean rescheduledFlag) {
		RescheduledFlag = rescheduledFlag;
	}
	public int getFreeChangeTimes() {
		return FreeChangeTimes;
	}
	public void setFreeChangeTimes(int freeChangeTimes) {
		FreeChangeTimes = freeChangeTimes;
	}
	public boolean isChangeAirLineFlag() {
		return ChangeAirLineFlag;
	}
	public void setChangeAirLineFlag(boolean changeAirLineFlag) {
		ChangeAirLineFlag = changeAirLineFlag;
	}
	public boolean isUpgradeFlag() {
		return UpgradeFlag;
	}
	public void setUpgradeFlag(boolean upgradeFlag) {
		UpgradeFlag = upgradeFlag;
	}
	public String getFareID() {
		return FareID;
	}
	public void setFareID(String fareID) {
		FareID = fareID;
	}
	public boolean isIsUseScore() {
		return IsUseScore;
	}
	public void setIsUseScore(boolean isUseScore) {
		IsUseScore = isUseScore;
	}
	public double getDeductibls() {
		return Deductibls;
	}
	public void setDeductibls(double deductibls) {
		Deductibls = deductibls;
	}
	public double getCouponAmount() {
		return CouponAmount;
	}
	public void setCouponAmount(double couponAmount) {
		CouponAmount = couponAmount;
	}
	public double getGifiScore() {
		return GifiScore;
	}
	public void setGifiScore(double gifiScore) {
		GifiScore = gifiScore;
	}

	public String getComment() {
		return Comment;
	}

	public void setComment(String comment) {
		Comment = comment;
	}

	public String getBaggage() {
		return Baggage;
	}

	public void setBaggage(String baggage) {
		Baggage = baggage;
	}

	public String getValidityPeriod() {
		return ValidityPeriod;
	}

	public void setValidityPeriod(String validityPeriod) {
		ValidityPeriod = validityPeriod;
	}

	public String getMinStay() {
		return MinStay;
	}

	public void setMinStay(String minStay) {
		MinStay = minStay;
	}

	public Double getUpgradeFee() {
		return UpgradeFee;
	}

	public void setUpgradeFee(Double upgradeFee) {
		UpgradeFee = upgradeFee;
	}

	public List<InsuranceInfo> getInsuranceList() {
		return InsuranceList;
	}

	public void setInsuranceList(List<InsuranceInfo> insuranceList) {
		InsuranceList = insuranceList;
	}

	public SegmentInfo getSegmentInfoOTO() {
		return SegmentInfoOTO;
	}

	public void setSegmentInfoOTO(SegmentInfo segmentInfoOTO) {
		SegmentInfoOTO = segmentInfoOTO;
	}

	public List<PtChangeRuleInfo> getChangeRules() {
		return changeRules;
	}

	public void setChangeRules(List<PtChangeRuleInfo> changeRules) {
		this.changeRules = changeRules;
	}

	public List<PtRefundRuleInfo> getRefundedRules() {
		return refundedRules;
	}

	public void setRefundedRules(List<PtRefundRuleInfo> refundedRules) {
		this.refundedRules = refundedRules;
	}

	public String getRefundState() {
		return refundState;
	}

	public void setRefundState(String refundState) {
		this.refundState = refundState;
	}

	public String getRefundRebateState() {
		return refundRebateState;
	}

	public void setRefundRebateState(String refundRebateState) {
		this.refundRebateState = refundRebateState;
	}

	public OrderDetailBaggage getOrderDetailBaggage() {
		return orderDetailBaggage;
	}

	public void setOrderDetailBaggage(OrderDetailBaggage orderDetailBaggage) {
		this.orderDetailBaggage = orderDetailBaggage;
	}

	public List<TravelPrivilege> getPrivilegeList() {
		return privilegeList;
	}

	public void setPrivilegeList(List<TravelPrivilege> privilegeList) {
		this.privilegeList = privilegeList;
	}

	public String getBrandCode() {
		return BrandCode;
	}

	public void setBrandCode(String brandCode) {
		BrandCode = brandCode;
	}

	public String getTourCode() {
		return tourCode;
	}

	public void setTourCode(String tourCode) {
		this.tourCode = tourCode;
	}

	public Set<String> getShippingRulesLabel() {
		return shippingRulesLabel;
	}

	public void setShippingRulesLabel(Set<String> shippingRulesLabel) {
		this.shippingRulesLabel = shippingRulesLabel;
	}
}
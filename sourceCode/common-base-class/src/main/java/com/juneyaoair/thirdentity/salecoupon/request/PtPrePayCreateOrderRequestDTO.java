package com.juneyaoair.thirdentity.salecoupon.request;
/*
 *@program:ho-mobileapi
 *@author: <PERSON>
 *@Time: 2021/8/10  11:03
 *@description: 预付费行李下单请求体
 */

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class PtPrePayCreateOrderRequestDTO {
    private String Version; // 接口版本号
    private String ChannelCode; // 渠道用户号
    private String UserNo; // 渠道工作人员号
    private String FfpId; // 常旅客ID
    private String FfpCardNo; // 常旅客卡号
    private PtBasicCreateOrderRequest Request;  // 请求信息
}

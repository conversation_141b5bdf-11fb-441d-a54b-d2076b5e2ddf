package com.juneyaoair.thirdentity.specialservice.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by guan<PERSON>yin on 2018/11/28.
 * <AUTHOR>
 * 详情添加单张图片响应体
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class TfSpecialServiceOnePicResponse {
    /**
     * 结果代码   0：成功 1：失败
     */
    private int code;
    /**
     * 描述信息
     */
    private String msg;
    /**
     * 图片地址
     */
    private ImgData data;
}

package com.juneyaoair.thirdentity.response.activity;

import java.util.List;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;

@XmlRootElement(name = "QueryMyWinRecordsResponse")
@XmlAccessorType(XmlAccessType.FIELD)
public class QueryMyWinRecordsResponse {
	private String Version;
	private String ChannelCode;
	private String ResultCode;
	private String ErrorInfo;
	private List<PrizeInfo> PrizeInfos;
	
	public String getVersion() {
		return Version;
	}
	public void setVersion(String version) {
		Version = version;
	}
	public String getChannelCode() {
		return ChannelCode;
	}
	public void setChannelCode(String channelCode) {
		ChannelCode = channelCode;
	}
	public String getResultCode() {
		return ResultCode;
	}
	public void setResultCode(String resultCode) {
		ResultCode = resultCode;
	}
	public String getErrorInfo() {
		return ErrorInfo;
	}
	public void setErrorInfo(String errorInfo) {
		ErrorInfo = errorInfo;
	}

	public List<PrizeInfo> getPrizeInfos() {
		return PrizeInfos;
	}

	public void setPrizeInfos(List<PrizeInfo> prizeInfos) {
		PrizeInfos = prizeInfos;
	}
}

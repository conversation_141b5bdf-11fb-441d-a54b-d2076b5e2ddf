package com.juneyaoair.thirdentity.member.request;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> by jiang<PERSON><PERSON>
 * @date 2018/12/11 9:20
 */
@NoArgsConstructor
@Data
public class ContactReq {
    @NotNull(message = "联系方式不可为空")
    private Integer RecordId;
    private Integer ContactType;
    private String ContactValue;
    private String MemberId;
    private String Remark;
}

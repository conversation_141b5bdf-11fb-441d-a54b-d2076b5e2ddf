package com.juneyaoair.thirdentity.salecoupon.common;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description  权益凭证
 * @date 2019/3/6  17:20.
 */
@Data
public class VoucherInfo {
    private String VoucherNo; //权益凭证号
    private String VoucherState; //凭证状态
    private String VoucherType;  //品类（同资源品类）
    private String ActivateTime; //生效时间
    private String ExpireTime;  //过期时间
    private ResourceInfo VoucherDetail; //权益信息
    private BookingLimit BookingLimit;
    private EquityUnitsUsage EquityUnitsUsage;
    private String RuleRemark;
    private int IsGiving; //是否可转赠 1-可转赠  0-不可转赠
    /**
     * 不可用原因
     */
    private String UnAvailableMsg;
    /**
     * 是否可以使用
     */
    private boolean AvailableStatus;

    private String Display; //  返回权益券是否可直接查看订单信息  2020-06-19

    private int BookCancelCount; // 贵宾休息室剩余可取消预约次数 2020-06-19

    private String MainOrderNo; //订单号 2020-06-19
    private String ChannelOrderNo; //渠道订单号 2020-11-22
    private String UnLimitedUpgrade;//表示无限升舱卡  yes - 无限升舱卡， 其他情况视为普通升舱卡
    private String LimitBindingDate;//绑定截至日期
    private String LimitBindingStatus;//绑定状态 no-未绑定
    private String UpgradeBindingValidity;//记录是否已过绑定有效期  有效:Effective  失效:invalid
    /**
     * 儿童中文姓名
     */
    private String ChildCnName;
    /**
     * 儿童英文姓
     */
    private String ChildElastName;
    /**
     * 儿童英文名
     */
    private String ChildEfirstName;
    /**
     * 儿童身份证号
     */
    private String ChildIdNumber;
    /**
     * 儿童出生日期
     */
    private String ChildBirthDate;

    /**
     * 成人姓名
     */
    private String AdultName;

    /**
     * 成人身份证号
     */
    private String AdultIdNumber;
    /**
     * 成人英文姓
     */
    private String AdultElastName;
    /**
     * 成人英文名
     */
    private String AdultEfirstName;
    /**
     * 证件类型
     */
    private String CertType;
    /**
     * 绑定类型  ADT:绑定本人  CHD:绑定儿童
     */
    private String BindingType;
    /**
     * 受赠人会员ID
     */
    private String ReceiveMemberId;
    /**
     * 受赠人会员卡号
     */
    private String ReceiveMemberCard;
    /**
     * 受赠人券码
     */
    private String ReveiveVoucherNo;
    /**
     * 是否他人赠送标识   1:是  0:否
     */
    private Integer ReceiveStatus;
    /**
     * 使用状态
     */
    private String UsedStatus;

    /**
     * 权益券是否限制本人使用  1:是  0:否  2021-04-07
     */
    private int SelfUse;

    /**
     * 下单类型
     */
    private String SubOrderType;

    private String RescheduleType;

    private String   AdvanceHour;
    private String ProNum;
}

package com.juneyaoair.thirdentity.response.payment;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;


@XmlRootElement(name = "PtPaymentResp")
@XmlAccessorType(XmlAccessType.FIELD)
public class PtPaymentResp {
	private String ChannelNo;//渠道用户,支付平台分配给业务系统的识别号
	private String ChannelOrderNo;//渠道订单编号,由业务系统生成，必须保证在业务系统的唯一性。
	private String OrderNo;//订单编号
	private String Currency;//支付币种
	private String Amount;//支付金额,一千元表示为：1000.00。
	private String ChannelPriInfo;//渠道用户私有域,为渠道用户自定义的字段、交易完成由支付平台原样返回。
	private String PaymentSeq;//支付交易流水号,支付平台生成的支付交易唯一标识
	private String PaymentTransId;//支付交易编号,支付平台提交给支付网关的支付交易编号。（支付网关称为商户订单编号）
	private String ConfirmTime;//第三方支付网关时间（如果支付网关不提供该时间，则该时间为支付平台收到交易结果时间）：yyyyMMddHHmmss
	private String GatewayNo;//在业务系统中选择使用的支付网关。该字段为空时将会在支付平台进行支付网关选择
	private String MerchantNo;//支付时选择的支付商户号。
	private String RespCode;//支付结果，1001为支付成功；其它为查询失败，失败具体原因见ErrMsg，失败不一定有序号为3的信息。
	private String ErrorMsg;//支付错误的原因。
	//如果参数为可选项并且为空，则该参数值不参与签名。	交易状态不为1001,1002,1003,1004时没有验证串
	private String ChkValue;//验证串，


	public PtPaymentResp() {
		super();
	}

	public String getChannelNo() {
		return ChannelNo;
	}

	public void setChannelNo(String channelNo) {
		ChannelNo = channelNo;
	}

	public String getChannelOrderNo() {
		return ChannelOrderNo;
	}

	public void setChannelOrderNo(String channelOrderNo) {
		ChannelOrderNo = channelOrderNo;
	}

	public String getOrderNo() {
		return OrderNo;
	}

	public void setOrderNo(String orderNo) {
		OrderNo = orderNo;
	}

	public String getCurrency() {
		return Currency;
	}

	public void setCurrency(String currency) {
		Currency = currency;
	}

	public String getAmount() {
		return Amount;
	}

	public void setAmount(String amount) {
		Amount = amount;
	}

	public String getChannelPriInfo() {
		return ChannelPriInfo;
	}

	public void setChannelPriInfo(String channelPriInfo) {
		ChannelPriInfo = channelPriInfo;
	}

	public String getPaymentSeq() {
		return PaymentSeq;
	}

	public void setPaymentSeq(String paymentSeq) {
		PaymentSeq = paymentSeq;
	}

	public String getPaymentTransId() {
		return PaymentTransId;
	}

	public void setPaymentTransId(String paymentTransId) {
		PaymentTransId = paymentTransId;
	}

	public String getConfirmTime() {
		return ConfirmTime;
	}

	public void setConfirmTime(String confirmTime) {
		ConfirmTime = confirmTime;
	}

	public String getGatewayNo() {
		return GatewayNo;
	}

	public void setGatewayNo(String gatewayNo) {
		GatewayNo = gatewayNo;
	}

	public String getMerchantNo() {
		return MerchantNo;
	}

	public void setMerchantNo(String merchantNo) {
		MerchantNo = merchantNo;
	}

	public String getRespCode() {
		return RespCode;
	}

	public void setRespCode(String respCode) {
		RespCode = respCode;
	}

	public String getErrorMsg() {
		return ErrorMsg;
	}

	public void setErrorMsg(String errMsg) {
		ErrorMsg = errMsg;
	}

	public String getChkValue() {
		return ChkValue;
	}

	public void setChkValue(String chkValue) {
		ChkValue = chkValue;
	}

}
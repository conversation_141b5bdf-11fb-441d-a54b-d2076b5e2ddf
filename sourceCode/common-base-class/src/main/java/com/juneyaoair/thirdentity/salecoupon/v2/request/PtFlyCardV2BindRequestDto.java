package com.juneyaoair.thirdentity.salecoupon.v2.request;

import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @date 2020/11/18  13:04.
 */
@Data
public class PtFlyCardV2BindRequestDto {
    private String VoucherNo;
    private String ChildCnName;//儿童中文名
    private String ChildElastName;//儿童英文姓
    private String ChildEfirstName;//儿童英文名
    private String ChildIdNumber;//儿童证件号
    private String BirthDate; //儿童生日 2020-11-18 畅飞卡2.0 绑定增加信息
    private String CertType; //证件类型 2020-11-18 畅飞卡2.0 绑定增加信息
    private String AdultName;//成人中文名 2020-11-18 畅飞卡2.0 绑定增加信息
    private String AdultElastName;//成人英文姓 2020-11-18 畅飞卡2.0 绑定增加信息
    private String AdultEfirstName;//成人英文名 2020-11-18 畅飞卡2.0 绑定增加信息
    private String AdultIdNumber;//成人证件号 2020-11-18 畅飞卡2.0 绑定增加信息
    private String BindingType; //绑定类型 ADT-成人 CHD-儿童 畅飞卡2.0 绑定增加信息  2020-11-18
}

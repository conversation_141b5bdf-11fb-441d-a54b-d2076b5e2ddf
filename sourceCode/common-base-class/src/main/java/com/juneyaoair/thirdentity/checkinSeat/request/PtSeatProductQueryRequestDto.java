package com.juneyaoair.thirdentity.checkinSeat.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author:zc
 * @Description:
 * @Date:Created in 17:06 2019/4/15
 * @Modified by:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PtSeatProductQueryRequestDto {
    private String Version;
    private String ChannelCode;
    private String UserNo;
    private String FlightDate;
    private String FlightNo;
    private String DepAirport;
    private String ArrAirport;
    private String Cabin;
    private String PnrNo;
    private String TicketNo;
    private String Currency;//付费选座新增。不传，默认为人民币CNY 美元USD 日圆JPY 英镑GBP 泰铢THB等
}

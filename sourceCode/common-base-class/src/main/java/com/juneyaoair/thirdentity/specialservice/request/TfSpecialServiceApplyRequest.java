package com.juneyaoair.thirdentity.specialservice.request;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * Created by guanshiyin on 2018/11/15.
 * <AUTHOR>
 * 特服请求信息
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@ApiModel(value="TfSpecialServiceApplyRequest",description="公用请求结构体")
public class TfSpecialServiceApplyRequest<T> implements Serializable {
    /**
     * 接口编码：
     *      申请：apply
     *      取消申请：cancelApply
     *      我的申请列表查询：queryAllApply
     *      进度查询：queryApply
     */
    @NotNull(message="接口编码不能为空")
    private String inftCode;
    /**
     * 会员Id
     */
    @NotNull(message="会员ID不能为空")
    private String memberId;

    /**
     * 业务参数
     */
    @Valid
    private List<T> data;

   /* public TfSpecialServiceApplyRequest(String inftCode, String memberId, List<T> request) {
        this.inftCode = inftCode;
        this.memberId = memberId;
        this.request = request;
    }*/
}

package com.juneyaoair.thirdentity.salecoupon.v2.common;

import com.google.gson.annotations.SerializedName;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2020/7/29  18:33.
 */
@Data
public class ResourceBase {
    /**
     * 资源ID
     */
    @NotBlank(message = "资源ID不可为空")
    @SerializedName("ResourceId")
    private String resourceId;
    /**
     * 产品名称
     */
    @SerializedName("ResourceName")
    private String resourceName;
    /**
     * 资源种类
     * @see com.juneyaoair.appenum.rightcoupon.VoucherTypesEnum
     */
    @SerializedName("ResourceType")
    private String resourceType;
    /**
     * 产品类别
     */
    @SerializedName("Category")
    private String category;
    /**
     * 资源描述
     */
    @SerializedName("ResourceDesc")
    private String resourceDesc;
    /**
     * 供应商名称
     */
    @SerializedName("VendorName")
    private String vendorName;
    /**
     * 资源标签
     */
    @SerializedName("Tags")
    private List<String> tags;
    /**
     * 资源图片列表
     */
    @SerializedName("Imgs")
    private List<String> imgs;
    /**
     * 	使用方式
     */
    @SerializedName("UseMode")
    private String useMode;
    /**
     * 购买须知
     */
    @SerializedName("PurchaseNotes")
    private String purchaseNotes;
    /**
     * 退款规则
     */
    @SerializedName("RefundRules")
    private String refundRules;
    /**
     * 资源类型
     */
    @SerializedName("Classification")
    private String classification;
    /**
     * 品类顺序 2020-10-12
     */
    @SerializedName("CategorySequence")
    private int categorySequence;
    //2020-09-27 以下参数用于前端产品列表显示 其中产品销售价、产品标准价格、单位取同一资源下最低价产品信息
    private int monthTotalCount;//产品已售数量 多个不同规格产品的总销量
    private Double salePrice;//产品销售价
    private Double standardPrice;//产品标准价格
    private String company; //单位
    //2021-04-25 贵宾休息室 买券即享配置
    @SerializedName("BuyCouponEnjoyList")
    private List<ResourceBuyCouponEnjoy> buyCouponEnjoyList;
}

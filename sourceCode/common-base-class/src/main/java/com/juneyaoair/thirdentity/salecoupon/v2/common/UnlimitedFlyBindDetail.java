package com.juneyaoair.thirdentity.salecoupon.v2.common;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @date 2020/11/17  10:13.
 */
@Data
public class UnlimitedFlyBindDetail {
    @SerializedName("VoucherNo")
    private String voucherNo;
    @SerializedName("ResourceType")
    private String resourceType; // 券类型 2020-11-17
    @SerializedName("BindStatus")
    private String bindStatus;
    @SerializedName("BindingDate")
    private String bindingDate;
    @SerializedName("StartDate")
    private String startDate;
    @SerializedName("EndDate")
    private String endDate;
    @SerializedName("LimitBindingDate")
    private String limitBindingDate;//绑定截至日期  yyyy-MM-dd
    @SerializedName("BindingValidity")
    private String bindingValidity;//记录是否已过绑定有效期  Effective  有效  invalid  失效
    @SerializedName("ChildCnName")
    private String childCnName; //儿童中文名
    @SerializedName("ChildElastName")
    private String childElastName; //儿童英文姓
    @SerializedName("ChildEfirstName")
    private String childEfirstName; //儿童英文名
    @SerializedName("ChildIdNumber")
    private String childIdNumber; //儿童身份证号
    @SerializedName("ChildBirthDate")
    private String childBirthDate; //儿童生日
    @SerializedName("AdultName")
    private String adultName; //成人姓名
    @SerializedName("AdultElastName")
    private String adultElastName; //成人英文姓
    @SerializedName("AdultEfirstName")
    private String adultEfirstName; //成人英文名
    @SerializedName("AdultIdNumber")
    private String adultIdNumber; //成人证件号
    @SerializedName("CertType")
    private String certType; //证件类型
    @SerializedName("BindingType")
    private String bindingType; //绑定类型 ADT-成人 CHD-儿童
    @SerializedName("PhoneNumber")
    private String phoneNumber;
    @SerializedName("ReceiveMemberId")
    private String receiveMemberId; //受赠人会员ID
    @SerializedName("ReceiveMemberCard")
    private String receiveMemberCard; //受赠人会员卡号
    @SerializedName("ReveiveVoucherNo")
    private String receiveVoucherNo;//受赠人券码
    @SerializedName("NoShowStatus")
    private String  noShowStatus;// noshow是否已达上限 yes-是
    @SerializedName("NoShowTimes")
    private int noShowTimes;//违规退票次数
    @SerializedName("NoShowUpperLimit")
    private int noShowUpperLimit;//违规退票次数上限
    @SerializedName("OutTimes")
    private int outTimes;//出票次数
    @SerializedName("SaveAmount")
    private int saveAmount;//无限升舱2.0 节省金额 2020-12-22
}

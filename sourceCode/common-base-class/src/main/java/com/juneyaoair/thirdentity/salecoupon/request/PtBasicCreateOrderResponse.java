package com.juneyaoair.thirdentity.salecoupon.request;
/*
 *@program:ho-mobileapi
 *@author: <PERSON>
 *@Time: 2021/8/10  13:42
 *@description:
 */

import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class PtBasicCreateOrderResponse {
    private Long OrderId; // 订单ID
    private String OrderNo; // 订单编号
    private String OrderChannelOrderNo; // 渠道订单编号
    private Long CreateAt; // 创建时间
    private String Status; // 订单状态

}

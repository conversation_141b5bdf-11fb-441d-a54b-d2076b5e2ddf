package com.juneyaoair.thirdentity.checkinSeat.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author:zc
 * @Description:
 * @Date:Created in 18:19 2019/4/15
 * @Modified by:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PtCustomerCertRequestDto {
    private String Version;
    private String ChannelCode;
    private String UserNo;
    private String RequestIp;
    private String CertNo;//票号：TN, 身份证号：NI， 护照等：PP
    private String PassengerNm;
    private String DepAirportCode;
}

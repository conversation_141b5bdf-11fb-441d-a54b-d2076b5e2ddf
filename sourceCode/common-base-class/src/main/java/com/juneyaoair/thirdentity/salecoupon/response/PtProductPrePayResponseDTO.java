package com.juneyaoair.thirdentity.salecoupon.response;

import com.juneyaoair.thirdentity.salecoupon.v2.common.PrePayProductInfo;
import com.juneyaoair.thirdentity.salecoupon.v2.common.ProductInfo;
import lombok.Data;

import java.util.List;

/*
 *@program:ho-mobileapi
 *@author: <PERSON>
 *@Time: 2021/8/10  9:13
 *@description: 预付费行李查询返回体
 */
@Data
public class PtProductPrePayResponseDTO {
    private String Version;
    private String ChannelCode;
    private String ResultCode;
    private String ErrorInfo;
    private String TotalRecords; // 产品总数量
    private String TotalPages; // 总页数
    private List<PrePayProductInfo> ProductList;
}

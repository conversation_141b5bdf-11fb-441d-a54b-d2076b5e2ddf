package com.juneyaoair.thirdentity.chdunlimitedfly;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.Date;

/**
 * 畅飞卡2.0绑定记录
 * <AUTHOR>
 * @Description
 * @create 2020-11-17 16:29
 */
@Data
public class UnlimitedFlyV2BindRecord extends UnlimitedFlyBindRecord{

    /**
     * 券类型
     * @see com.juneyaoair.appenum.rightcoupon.VoucherTypesEnum
     */
    @SerializedName("ResourceType")
    private String resourceType;

    /**
     * 成人姓名
     */
    @SerializedName(value="AdultName")
    private String adultName;

    /**
     * 成人证件号
     */
    @SerializedName(value="AdultIdNumber")
    private String aultIdNumber;

    /**
     * 成人英文姓
     */
    @SerializedName(value="AdultElastName")
    private String adultElastName;

    /**
     * 成人英文名
     */
    @SerializedName(value="AdultEfirstName")
    private String adultEfirstName;

    /**
     * 证件类型
     */
    @SerializedName(value="CertType")
    private String certType;

    /**
     * 绑定类型
     * ADT-成人 CHD-儿童
     */
    @SerializedName(value="BindingType")
    private String bindingType;

    /**
     * 受赠人会员ID
     */
    @SerializedName(value="ReceiveMemberId")
    private String receiveMemberId;

    /**
     * 受赠人会员卡号
     */
    @SerializedName(value="ReceiveMemberCard")
    private String receiveMemberCard;

    /**
     * 受赠人券码
     */
    @SerializedName(value="ReveiveVoucherNo")
    private String reveiveVoucherNo;

    /**
     * 违规退票次数
     */
    @SerializedName(value="NoShowTimes")
    private String noShowTimes;

    @SerializedName(value="BindRecordId")
    private Integer  bindRecordId;

    /**
     * 可违规次数上限
     */
    @SerializedName(value="NoShowUpperLimit")
    private String noShowUpperLimit;

    /**
     * 出票次数
     */
    @SerializedName(value="OutTimes")
    private String outTimes;

    @SerializedName("ChildElastName")
    private String childElastName;

    @SerializedName("ChildEfirstName")
    private String childEfirstName;

    /**
     * 记录noshow是否已达上限   yes：是
     */
    @SerializedName(value="NoShowStatus")
    private String noShowStatus;

    private String BelongCountry;

    private String Nationality;

    private Date  CertValidity;

    private String PhoneNumber;

    private String CountryTelCode;

    private String Sex;

    private Date BirthDate;
}

package com.juneyaoair.thirdentity.salecoupon.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description 订单基础请求信息
 * @created 2023/9/8 10:02
 */
@Data
public class OrderBaseParam {

    @ApiModelProperty(value = "版本号", required = true)
    private String Version;

    @ApiModelProperty(value = "渠道", required = true)
    private String ChannelCode;

    @ApiModelProperty(value = "会员ID", required = true)
    private String FfpId;

    @ApiModelProperty(value = "会员卡号", required = true)
    private String FfpCardNo;

}

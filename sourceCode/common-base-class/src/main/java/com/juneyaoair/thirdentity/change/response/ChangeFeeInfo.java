package com.juneyaoair.thirdentity.change.response;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description
 * @date 2018/12/17  17:21.
 */
@Data
public class ChangeFeeInfo {
    private String PassengerName;
    private String PassengerType;
    private Double ChangeFee;
    //是否可改期  true可改期  false 不可改期
    private boolean RescheduledFlag;
    /**
     *身份标识  迪士尼运价
     */
    private String PassengerIdentity;
    /**
     * 原改期费
     */
    private BigDecimal OriginalChangeFee;

    /**
     * 改期政策备注
     */
    private String ChangeRemark;

    /**
     * 改期政策提示
     */
    private String RescheduleReminder;
}

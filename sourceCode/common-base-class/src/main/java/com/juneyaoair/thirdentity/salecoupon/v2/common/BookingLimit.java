package com.juneyaoair.thirdentity.salecoupon.v2.common;
/*
 *@program:ho-mobileapi
 *@author: <PERSON>
 *@Time: 2021/8/12  9:12
 *@description:
 */

import com.google.gson.annotations.SerializedName;
import com.juneyaoair.thirdentity.salecoupon.common.BlackList;
import com.juneyaoair.thirdentity.salecoupon.common.DateLimit;
import lombok.Data;

import java.util.List;

@Data
public class BookingLimit {
    @SerializedName("BookingDates")
    private List<DateLimit> bookingDates; //购买日期限制 为空即无限制

    @SerializedName("TravelDates")
    private List<DateLimit> travelDates; //出行日期限制 为空即无限制

    /**
     * 航班日期范围
     */
    @SerializedName("FlightDates")
    private List<DateLimit> flightDates;

    /**
     * 航程类型 OW:限单程 RT:限往返（含缺口程） 为空代表不限制
     */
    @SerializedName("FlightType")
    private String flightType;

    @SerializedName("FlightRoutes")
    private List<BlackList> flightRoutes;

    @SerializedName("FlightNos")
    private BlackList flightNos;

    @SerializedName("Cabins")
    private BlackList cabins;

    @SerializedName("AircraftModel")
    private BlackList aircraftModel; // 适用机型


}

package com.juneyaoair.thirdentity.adtunlimitedfly;

import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Description
 * @create 2020-07-05 14:28
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PtQueryUnlimitedFlyRequest {

    @SerializedName("Version")
    private String Version;

    @SerializedName("ChannelCode")
    private String ChannelCode;

    @SerializedName("UserNo")
    private String UserNo;

    @SerializedName("ChannelCustomerNo")
    private String ChannelCustomerNo;

    @SerializedName("FfCardNo")
    private String FfCardNo;

    /** 权益卡号*/
    @SerializedName(value="CouponNo")
    private String couponNo;

}

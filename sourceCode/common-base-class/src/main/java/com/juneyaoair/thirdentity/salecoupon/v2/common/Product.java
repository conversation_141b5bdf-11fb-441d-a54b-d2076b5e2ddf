package com.juneyaoair.thirdentity.salecoupon.v2.common;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.google.gson.annotations.SerializedName;
import com.juneyaoair.baseclass.onboardShopping.OnboardProduct;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2020/7/29  18:46.
 */
@Data
@JsonIgnoreProperties(value = {"productRuleLimit"})
public class Product {
    @NotBlank(message = "活动ID不可为空")
    @SerializedName("ProductNum")
    private String productNum;//所属活动编号，productId+bookingId+ruleId的组合ID
    @NotBlank(message = "产品ID不可为空")
    @SerializedName("ProductId")
    private String productId;
    @NotBlank(message = "投放ID不可为空")
    @SerializedName("BookingId")
    private String bookingId;
    @NotBlank(message = "使用规则ID不可为空")
    @SerializedName("RuleId")
    private String ruleId;
    @NotBlank(message = "产品类型不可为空")
    @SerializedName("ProductType")
    private String productType;
    @SerializedName("ProductName")
    private String productName;
    @SerializedName("SalePrice")
    private Double salePrice;//产品销售价
    @SerializedName("StandardPrice")
    private Double standardPrice;//产品标准价格
    @SerializedName("ProductSku")
    private String productSku;
    @SerializedName("SaleTotalCount")
    private int saleTotalCount;//产品已售数量
    @SerializedName("VaildityDays")
    private int vaildityDays;
    @SerializedName("LatestDate")
    private String latestDate;
    @SerializedName("AvailPrdStock")
    private int availPrdStock;//库存
    @SerializedName("ProductRuleLimit")
    private ProductRuleLimit productRuleLimit;
    @SerializedName("BookingCount")
    private int bookingCount;//总的可购张数
    @SerializedName("RemainedCount")
    private int remainedCount;//剩余可购张数
    @NotBlank(message = "产品验证不可为空")
    private String sign;
    /**
     * 机上购物产品特有信息
     */
    @SerializedName("OnboardProduct")
    private OnboardProduct onboardProduct;
    @SerializedName("Delivery")
    private String delivery;

    private String vipLoungeType;

    private String moreAdress;

    private String moreStartDate;

    /**
     * 是否可用餐食
     */
    private boolean mealAvailable;
    /**
     * 是否显示选中产品规格提示信息
     */
    private Boolean showChooseTip;

    private String productSequence;

    @SerializedName("AdvanceHour")
    private String advanceHour;//航班起飞几小时前可用
    /**
     * 产品是否存在库存  yes:存在   no:不存在 2020-10-19
     */
    @SerializedName("StockFlag")
    private String stockFlag;
    @SerializedName("ScheduledCommission")
    private Double scheduledCommission; // 提成比 2020-11-17
    @SerializedName("FlightType")
    private String flightType; // 升舱券类型 2020-12-17 国内:DOMESTIC 国际:INTL
    /**
     * 休息室产品特有属性 2020-12-25
     */
    @SerializedName("LoungeCouponExt")
    private LoungeCouponExt loungeCouponExt;
    /**
     * 休息室特有属性，所在机场 （机场三字码）2021-02-26
     */
    @SerializedName("AirportLocation")
    private String airportLocation;
    private Boolean availPrdStockFlag; //标识是否包含库存可用库存 2020-11-17 （畅飞卡2.0产品  不在使用库存参数，判断库存是否大于0，赋值该标识）
    private String availDate; //可用日期 2020-11-17 （畅飞卡2.0 使用）
    private String unAvailDate;// 不可用日期 2020-11-17 （畅飞卡2.0 使用）
    private String startAvailDate; //可用日期 2020-11-17
    private String endAvailDate;
    private String startUnAvailDate;// 不可用日期 2020-11-17
    private String endUnAvailDate;
    @SerializedName("ProFields")
    private List<ProField> proFields;
    private String productRule;

    @ApiModelProperty(value = "活动开始日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date beginDatetime;

    @ApiModelProperty(value = "活动结束日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date endDatetime;

    @ApiModelProperty(value = "活动名称")
    private String activityName;

    //创建参与签名的值
    public String createSignField(String resourceId) {
        return resourceId + this.productNum + this.productId + this.bookingId + this.ruleId + this.productSku + this.productType + this.salePrice;
    }
}

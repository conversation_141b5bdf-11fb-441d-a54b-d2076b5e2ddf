package com.juneyaoair.thirdentity.salecoupon.request;/*
 *@program:ho-mobileapi
 *@author: <PERSON>
 *@Time: 2021/8/10  9:28
 *@description:
 */

import com.juneyaoair.baseclass.newcoupon.bean.SingleBookCondition;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@NoArgsConstructor
@Accessors(chain = true)
public class PtProductPrePayRequestDTO {
    private String Version; // 接口版本号
    private String ChannelCode; // 渠道用户号
    private String UserNo; // 渠道工作人员号
    private String SearchTypes; // 查询品类
    /*
    贵宾休息室:Lounge
    逾重行李:Baggage
    升舱券:Upgrade
    WIFI:WIFI
    电话卡:PhoneCard
    签证:VISA
    接送机:Traffic
    预留登机牌:CheckinSubstitution
    改期券:RescheduleCoupon
    机上产品:OnboardProduct
    礼宾接机:ProtocolTraffic
     */
    private SingleBookCondition SingleBookCondition;
}

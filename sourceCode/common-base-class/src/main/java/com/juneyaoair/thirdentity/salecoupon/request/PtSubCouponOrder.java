package com.juneyaoair.thirdentity.salecoupon.request;
/*
 *@program:ho-mobileapi
 *@author: <PERSON>
 *@Time: 2021/8/10  12:57
 *@description:
 */

import com.google.gson.annotations.SerializedName;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PtSubCouponOrder {
    private String ProductNum; // 产品投放唯一标识
    private String ResourceType; // 资源种类
    private String ProductName; // 产品名称
    private Double StandardPrice; // 标准售价
    private Double SalePrice; // 实际销售价
    private int BookingCount; // 订购份数
    private String CertNo; // 证件号
    private String CertType; // 证件类型
    private String PassengerName; // 乘客姓名
    private String TktNo; // 票号
    private String PnrNo; // pnr信息
    private String DepAirportCode; // 出发机场三字码
    private String ArrAirportCode; // 到达机场三字码
    private String DepCity; // 出发城市
    private String DepAirportTerminal; // 出发机场航站楼
    private String ArrAirportTerminal; // 到达机场航站楼
    private String FlightNo; // 航班号
    private String FlightDate; // 航班日期 yyyy-MM-dd
    private String DepTime; // 出发时间 HH:mm
    private String ArrTime; // 到达时间 HH:mm
    private String ArrCity; // 到达城市
    private String PlaneType; // 飞机类型
    private String Cabin; // 舱位
    private String TicketType; //  航线类型-国际,国内
    private String AirlineType; // 航线类型
    private String SeatNos; // 座位号
    private String Trip; // 往返标识

    @ApiModelProperty(value = "使用优惠券/权益券信息", notes = "额外行李-私域行李券")
    private CouponInfo CouponInfo;

    private int UseScore;//使用积分

    @ApiModelProperty("航班日期 yyyy-MM-dd")
    private String DepFlightDate;

    @ApiModelProperty("航班日期 yyyy-MM-dd")
    private String ArrFlightDate;


}

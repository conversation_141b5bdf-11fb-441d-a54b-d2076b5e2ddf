package com.juneyaoair.thirdentity.specialservice;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by guan<PERSON>yin on 2018/11/16.
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class TfSpecialService {
    private String applicationSource;
    /** 是否校验成人选座 1：校验 */
    private String checkPtPerson;
    /**
     * 乘机人信息：姓名、年龄、性别、出生日期、身高、体重、证件类型、证件号、联系方式、孕周、用氧量
     */
    private String passName;
    private int passAge;
    private String passSex;
    private String passBirthDate;
    private int passHeight;
    private int passWeight;
    private String passCertType;
    private String passCertification;
    private String passContact;
    private int pregnancyWeek;
    private int userOxygen;
    /**
     * 航班信息：票号、航班号、航班日期、始发站、到达站
     */
    private String ticketNo;
    private String flightNo;
    private String flightDate;
    private String depatureStation;
    private String destinationStation;
    /**
     * 送机人信息：姓名、出生日期、性别、手机号、证件类型、证件号、地址
     */
    private String sjPersonName;
    private String sjPersonBirthDate;
    private String sjPersonSex;
    private String sjPersonContact;
    private String sjPersionCertType;
    private String sjPersonCert;
    private String sjPersonAddress;
    /**
     * 接机人信息：姓名、出生日期、性别、手机号、证件类型、证件号、地址
     */
    private String jjPersonName;
    private String jjPersonBirthDate;
    private String jjPersonSex;
    private String jjPersonContact;
    private String jjPersonCertType;
    private String jjPersonCert;
    private String jjPersonAddress;
    /**
     * 陪同人信息：姓名、出生日期、性别、手机号、证件类型、证件号
     */
    private String ptPersonName;
    private String ptPersonBirthDate;
    private String ptPersonSex;
    private String ptPersonContact;
    private String ptPersonCertType;
    private String ptPersonCert;
    /**
     * 预定人信息：姓名、手机号、邮箱
     */
    private String ydPersonName;
    private String ydPersonPhone;
    private String ydPersonEmail;


    private String passengerType;//特殊旅客类型
    private int speServiceType;//特殊服务类型
    private String cabinAttendant;//客舱乘务员
    private String checkInPerson;//值机员
    private int disabilityNum;//残疾人团队人数
    private String disabilityType;//残疾类型
    private String guidePerson;//引导员
    private String attachment;//附件
    private String isCarryWheelchair;//是否携带轮椅
    private String jjPerson;//接机员
    private String seatNum;//座位号
    private String serviceDogType;//服务犬类型 导听犬：1 导盲犬：2
    private String applyReason;//申请理由
    /**
     * 五类残疾种类
     */
    private String disability;

    /**
     *是否有陪同人
     */
    private String isPt;

    /**
     *  有无适宜乘机医疗证明（1：是，0：否）
     */
    private  int  isMedicalCertificate;

}

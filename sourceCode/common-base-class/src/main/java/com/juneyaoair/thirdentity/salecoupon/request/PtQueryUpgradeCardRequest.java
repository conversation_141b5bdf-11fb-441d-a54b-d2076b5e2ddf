package com.juneyaoair.thirdentity.salecoupon.request;

import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Description
 * @create 2020-07-05 14:28
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PtQueryUpgradeCardRequest {

    @SerializedName("Version")
    private String Version;

    @SerializedName("ChannelCode")
    private String ChannelCode;

    @SerializedName("UserNo")
    private String UserNo;

    @SerializedName("ChannelCustomerNo")
    private String ChannelCustomerNo;

    @SerializedName("FfCardNo")
    private String FfCardNo;

    /**
     * 升舱卡1.0：UpgradeUnlimited,升舱卡2.0：UnlimitUpgradeYear
     */
    @SerializedName("SearchUpgradeCardType")
    private String searchUpgradeCardType;

}

package com.juneyaoair.thirdentity.member.response;

import com.google.gson.annotations.SerializedName;
import com.juneyaoair.appenum.member.CertificateTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by yaocf on 2018/7/23  13:24.
 */
@Data
@NoArgsConstructor
public class MemberCertificateSoaModelV2 {
    @SerializedName("RecordId")
    private long recordId;
    /**
     * @see CertificateTypeEnum#getCode()
     */
    @SerializedName("CertificateType")
    private int certificateType;
    @SerializedName("CertificateNumber")
    private String certificateNumber;
    @SerializedName("Remark")
    private String remark;
    /**
     * 是否是实名认证的证件
     */
    @SerializedName("IsVerify")
    private boolean isVerify;
    @SerializedName("OperateDate")
    private String operateDate;
    @SerializedName("UpdateDate")
    private String updateDate;
    private String record;//recordId加密后得信息
    private String  certificateShowCode;
    @ApiModelProperty(value = "证件有效期")
    @SerializedName(value = "ValidDate")
    private String validDate;
    @ApiModelProperty(value = "签发国")
    @SerializedName("SigningAuthority")
    private String signingAuthority;
    @ApiModelProperty(value = "签发国名称")
    private String signingAuthorityName;

}

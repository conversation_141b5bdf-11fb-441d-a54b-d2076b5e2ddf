package com.juneyaoair.thirdentity.salecoupon.response;

import com.juneyaoair.baseclass.salecoupon.response.SaleCoupon;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Created by yaocf on 2018/7/18  15:28.
 * 可售优惠券查询结果
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class PtSaleCouponQueryResponse {
    private String Version;
    private String ChannelCode;
    private String UserNo;
    private List<SaleCoupon> SaleCouponList;
    private String ResultCode;
    private String ErrorInfo;
}

package com.juneyaoair.thirdentity.boardingpass.resp;

import com.google.gson.annotations.SerializedName;
import com.juneyaoair.thirdentity.boardingpass.common.BaseAirport;
import com.juneyaoair.thirdentity.boardingpass.common.BaseCouponOrderIdentity;
import com.juneyaoair.thirdentity.boardingpass.common.BasePassenger;
import com.juneyaoair.thirdentity.boardingpass.common.BasePay;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @date 2019/9/11  8:42.
 */
@Data
public class CouponOrderInfo {
    @SerializedName("FlightNo")
    private String flightNo;
    @SerializedName("FlightType")
    private String flightType; //机型
    @SerializedName("FlightDate")
    private String flightDate;
    @SerializedName("DepInfo")
    private BaseAirport depInfo;
    @SerializedName("ArrInfo")
    private BaseAirport arrInfo;
    @SerializedName("PassengerInfo")
    private BasePassenger passengerInfo;
    @SerializedName("OrderInfo")
    private BaseCouponOrderIdentity orderInfo;
    @SerializedName("PayInfo")
    private BasePay PayInfo;
    @SerializedName("Remark")
    private String remark;
}

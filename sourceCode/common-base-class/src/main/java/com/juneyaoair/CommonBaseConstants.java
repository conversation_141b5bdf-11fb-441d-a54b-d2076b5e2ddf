package com.juneyaoair;

/**
 * Created by qinxiaoming on 2016-4-13.
 */
public class CommonBaseConstants  {

    private CommonBaseConstants(){}

    //运价查询
    public static final String PASSENGER_TYPE_CHD = "CHD";
    public static final String PASSENGER_TYPE_ADT = "ADT";
    public static final String PASSENGER_TYPE_INF = "INF";

    //客票中的乘客类型
    public static final String IBE_PASSENGER_TYPE_ADT = "PASSENGER_ADULT";
    public static final String IBE_PASSENGER_TYPE_CHD = "PASSENGER_CHILD";
    public static final String IBE_PASSENGER_TYPE_INF = "PASSENGER_INFANT";
    public static final String IBE_PASSENGER_TYPE_GMJC ="PASSENGER_GMJC";
    public static final String IBE_PASSENGER_TYPE_UNACCOMPANIED = "PASSENGER_CHILD_UNACCOMPANIED";

    public static final String YQ_TAX_CODE = "YQ"; /// 燃油费代码
    public static final String CN_TAX_CODE = "CN";/// 建设税代码
    public static final String Q_FEE_CODE = "Q";/// Q费代码

    //客票状态
    public static final String OPEN_FOR_USE = "OPEN FOR USE";   /// 客票有效，可以使用
    public static final String USED_FLOWN = "USED/FLOWN"; /// 客票已使用
    public static final String REFUND_APPLICATION = "REFUND APPLICATION";/// 退票申请（统一订单平台内部状态航信系统没有此状态，退票已申请在退票处理流程中）
    public static final String REFUNDED = "REFUNDED"; /// 客票已退票
    public static final String EXCHANGED = "EXCHANGED"; /// 客票已换开到其它电子票上
    public static final String SUSPENDED = "SUSPENDED";/// 挂起
    public static final String VOID = "VOID";         /// 作废
    public static final String FIM_EXCH = "FIM EXCH";/// 客票已换开为飞行中断旅客舱单
    public static final String PRINT_EXCH = "PRINT EXCH";/// 客票已换开为纸票

}

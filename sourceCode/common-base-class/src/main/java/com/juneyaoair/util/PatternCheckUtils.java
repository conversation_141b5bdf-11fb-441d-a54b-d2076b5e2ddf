package com.juneyaoair.util;

import cn.jiguang.commom.utils.StringUtils;
import com.juneyaoair.pattern.PatternCommon;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @ClassName PatternCheckUtils
 * @Description 常用格式校验 手机号 邮箱等的格式校验
 * <AUTHOR>
 * @Date 2024/2/22 14:48
 * @Version 1.0
 */
public class PatternCheckUtils {

    private static final String DOMESTIC_MOBILE = "86"; //国内区号

    /**
     * @param valueType
     * @param value
     * @return boolean
     * <AUTHOR>
     * @Description 校验某值的格式是否正确
     * @Date 14:56 2024/2/22
     **/

    public static boolean toCheckValueFormat(int valueType, String value,String countryCode) {
        switch (valueType) {
            //手机号格式校验
            case 1:
                return toCheckPhoneFormat(value,countryCode);
            //邮箱校验
            case 5:
                return toCheckEmailFormat(value);
            default:
                return false;
        }
    }


    /**
     * @param phone
     * @return boolean
     * <AUTHOR>
     * @Description 校验手机号格式
     * @Date 14:53 2024/2/22
     **/
    public static boolean toCheckPhoneFormat(String phone,String countryCode) {
        if (StringUtils.isEmpty(phone) || StringUtils.isEmpty(countryCode)) {
            return false;
        }
        //校验手机号格式
        boolean rightTelNum;
        if (DOMESTIC_MOBILE.equals(countryCode)) {
            Pattern pattern = Pattern.compile(PatternCommon.MOBILE_PHONE);
            Matcher matcher = pattern.matcher(phone);
            rightTelNum = matcher.matches();
        } else {
            //国际手机号只校验位数
            Pattern pattern = Pattern.compile(PatternCommon.MOBILE_INTERNATIONAL);
            Matcher matcher = pattern.matcher(phone);
            rightTelNum = matcher.matches();
        }
        return rightTelNum;
    }

    /**
     * @param email
     * @return boolean
     * <AUTHOR>
     * @Description 校验邮箱格式
     * @Date 14:53 2024/2/22
     **/

    public static boolean toCheckEmailFormat(String email) {
        if (StringUtils.isEmpty(email)) {
            return false;
        }
        //校验邮箱格式
        Pattern pattern = Pattern.compile(PatternCommon.EMAIL);
        Matcher matcher = pattern.matcher(email);
        return matcher.matches();
    }


}

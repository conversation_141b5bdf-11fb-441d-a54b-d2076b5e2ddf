package com.juneyaoair.baseclass.smsorder.req;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.NotEmpty;

/**
 * <AUTHOR> by jiang<PERSON><PERSON>
 * @date 2019/6/17 16:34
 */
@Data
@NoArgsConstructor
public class SmsOrderDetailReq {
    @NotEmpty(message = "订单号不能为空")
    private String orderNo;
    @NotEmpty(message = "渠道订单号不能为空")
    private String channelOrderNo;
    @NotEmpty(message = "用户卡号不能为空")
    private String customerNo;
    @NotEmpty(message = "key不能为空")
    private String key;
    private String timeStamp;
    private String cardNo;
}

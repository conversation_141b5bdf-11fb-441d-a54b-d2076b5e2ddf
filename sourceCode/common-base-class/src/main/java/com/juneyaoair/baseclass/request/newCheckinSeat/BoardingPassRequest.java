package com.juneyaoair.baseclass.request.newCheckinSeat;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/**
 * @Author:zc
 * @Description:
 * @Date:Created in 18:28 2018/7/20
 * @Modified by:
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BoardingPassRequest {

    /**
     * 客票号
     * */
    @NotBlank(message = "客票号不能为空")
    private String tktNum;

    /**
     * 航段序号
     * */
    @NotBlank(message = "航段序号不能为空")
    private String tourIndex;

    /**
     * 承运航班号
     * */
    @NotBlank(message = "承运航班号不能为空")
    private String hostFlightNo;

    /**
     * 航班日期（yyyy-MM-dd）
     * */
    @NotBlank(message = "航班日期不能为空")
    @Pattern(regexp="^[0-9]{4}-[0-9]{2}-[0-9]{2}$",message="航班日期格式只能是yyyy-MM-dd")
    private String flightDate;

    /**
     * 起飞机场三字码
     * */
    @NotBlank(message = "起飞机场三字码不能为空")
    private String deptCode;

    /**
     * 到达机场三字码
     * */
    @NotBlank(message = "到达机场三字码不能为空")
    private String destCode;

//    /**
//     * 客票状态.票面状态信息 USED/FLOWN
//     OPEN FOR USE
//     VOID
//     CHECK IN
//     * */
//   // @NotBlank(message = "客票状态不能为空")
//    private String ticketStatus;

    /**
     * 乘客姓名
     * */
    @NotBlank(message = "乘客姓名不能为空")
    private String passengerName;

    @NotNull(message="渠道号 不能为空")
    private String channelCode;    //值机渠道(网站、手机、微信)
    @NotNull(message="客户ID号不能为空")
    private String ffpId;
    @NotNull(message="客户卡号不能为空")
    private String cardNo;
    @NotNull(message="验证码不能为空")
    private String loginKeyInfo;
    private String clientIp;
    private String token;
}

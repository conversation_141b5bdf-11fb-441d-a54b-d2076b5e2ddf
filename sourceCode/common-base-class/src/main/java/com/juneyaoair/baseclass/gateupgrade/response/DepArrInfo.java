package com.juneyaoair.baseclass.gateupgrade.response;

import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName DepArrInfo
 * @Description
 * <AUTHOR>
 * @Date 2019/8/12 13:40
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DepArrInfo {

    /**
     * 机场三字码
     */
    @SerializedName("AirportCode")
    private String airportCode;

    /**
     * 机场名
     */
    @SerializedName("AirportName")
    private String airportName;

    /**
     * 城市
     */
    @SerializedName("City")
    private String city;

    /**
     * 航站楼
     */
    @SerializedName("Terminal")
    private String terminal;

    /**
     * 时间
     */
    @SerializedName("Time")
    private String time;

}

package com.juneyaoair.baseclass.onBoardWifi.req;

import com.fasterxml.jackson.databind.annotation.JsonAppend;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR> by jiangming<PERSON>
 * @date 2019/4/4 9:18
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class WifiOrderRequestParam {
    private String FlightNum;
    private String FlightDate;
    private String OriginIata;
    private String DestinationIata;
    private String CertNo;
    private String EtrDate;
    private String MdfDate;
    private String OrderStat;
    private String SaleType;
    private BigDecimal SaleNum;
    private BigDecimal SaleUse;
    private String MemberCardNo;
    private String MemberName;
    private String FftId;
    private String PassengerNm;
    private String TktNo;
    private String PhoneNo;
    private String PlanFlightTime;
    private String Remark;
    private String Cabin;
}

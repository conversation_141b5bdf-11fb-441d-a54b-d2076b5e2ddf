package com.juneyaoair.baseclass.waitingpassager.request;

import com.juneyaoair.baseclass.common.base.UserInfoMust;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.NotBlank;

/**
 * 查询航班信息
 *
 * <AUTHOR>
 * @project mobile
 * @create 2018-11-27 9:30
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class FlightQuery extends UserInfoMust{
    @NotBlank(message = "航班日期不能为空")
    private String flightDate;
    @NotBlank(message = "出发城市不能为空")
    private String depCity;
    @NotBlank(message = "到达城市不能为空")
    private String arrCity;
}

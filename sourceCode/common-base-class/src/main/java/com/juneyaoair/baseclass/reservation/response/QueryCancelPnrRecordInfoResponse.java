package com.juneyaoair.baseclass.reservation.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @ClassName QueryCancelPnrRecordInfoResponse
 * <AUTHOR>
 * @Description 查询取消订座PNR记录列表信息 响应
 * @Date 2021-08-03 15:36
 **/
@Data
public class QueryCancelPnrRecordInfoResponse {
    @ApiModelProperty(value="取消记录信息",name="recordInfos",required=true)
    private List<CancelPnrRecordInfo> recordInfos; //取消记录信息
    @ApiModelProperty(value="总页数",name="totalPage",example="1",required=true)
    private int totalPage;
}

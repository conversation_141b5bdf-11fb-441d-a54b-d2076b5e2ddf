package com.juneyaoair.baseclass.request.ibe;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * @Author:zc
 * @Description:
 * @Date:Created in 15:52 2018/3/20
 * @Modified by:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ParamReserveSeat {
    @NotNull(message = "pnrNo不能为空")
    private String pnrNo;
    private int airsegIndex;
    @NotNull(message = "旅客姓名不能为空")
    private String psgrName ;
    @NotNull(message = "座位号不能为空")
    private String seatNo ;
    @NotNull(message = "航班号不能为空")
    private String flightNo;
    @NotNull(message = "出发机场三字码不能为空")
    private String depAirportCode;
    @NotNull(message = "到达机场三字码不能为空")
    private String arrAirportCode;
    @NotNull(message = "航班号不能为空")
    private String flightDate;
    @NotNull(message = "证件号不能为空")
    private String certNo;
    @NotNull(message = "票号不能为空")
    private String tktNo;
    private String gender;
    private String cabin;
    private String mobile;
    private String email;
    private String depTime;
    private String arrTime;

    private String clientIp;
    @NotNull(message="客户ID号不能为空")
    private String ffpId;
    @NotNull(message="客户卡号不能为空")
    private String ffpCardNo;
    @NotNull(message="验证码不能为空")
    private String loginKeyInfo;
    @NotNull(message = "渠道用户号不能为空")
    private String channelCode;
    private String token;
    /**
     * visa卡号
     */
    private String visaCardNo;
    /**
     * 座位状态，*，C,,,
     */
    private String seatStatus;
}

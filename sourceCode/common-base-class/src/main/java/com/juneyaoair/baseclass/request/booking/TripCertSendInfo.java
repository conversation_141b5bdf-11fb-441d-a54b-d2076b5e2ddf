package com.juneyaoair.baseclass.request.booking;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * Created by qinxiaoming on 2016-5-19.
 */

@XmlRootElement(name = "TripCertSendInfo")
@XmlAccessorType(XmlAccessType.FIELD)
public class TripCertSendInfo {
    private String DeliveryType;/// 邮寄方式
    private int DeliveryFee;/// 费用
    private String Linker; /// 收件人
    private String HandphoneNumber;/// 收件人手机
    private String TicketNo;  //电子票号
    private String DeliverToProvince;/// 省份代码
    private String DeliverToCity;/// 城市代码
    private String DeliveryAddress;/// 详细地址
    private String Remark;/// 备注
    private String ProvinceCityAddress;/// 详细地址(带省市)
    private String DeliveryState;

    public TripCertSendInfo() {
        super();
    }

    public String getDeliveryType() {
        return DeliveryType;
    }

    public void setDeliveryType(String deliveryType) {
        DeliveryType = deliveryType;
    }

    public int getDeliveryFee() {
        return DeliveryFee;
    }

    public void setDeliveryFee(int deliveryFee) {
        DeliveryFee = deliveryFee;
    }

    public String getLinker() {
        return Linker;
    }

    public void setLinker(String linker) {
        Linker = linker;
    }

    public String getHandphoneNumber() {
        return HandphoneNumber;
    }

    public void setHandphoneNumber(String handphoneNumber) {
        HandphoneNumber = handphoneNumber;
    }

    public String getTicketNo() {
        return TicketNo;
    }

    public void setTicketNo(String ticketNo) {
        TicketNo = ticketNo;
    }

    public String getDeliverToProvince() {
        return DeliverToProvince;
    }

    public void setDeliverToProvince(String deliverToProvince) {
        DeliverToProvince = deliverToProvince;
    }

    public String getDeliverToCity() {
        return DeliverToCity;
    }

    public void setDeliverToCity(String deliverToCity) {
        DeliverToCity = deliverToCity;
    }

    public String getDeliveryAddress() {
        return DeliveryAddress;
    }

    public void setDeliveryAddress(String deliveryAddress) {
        DeliveryAddress = deliveryAddress;
    }

    public String getRemark() {
        return Remark;
    }

    public void setRemark(String remark) {
        Remark = remark;
    }

    public String getProvinceCityAddress() {
        return ProvinceCityAddress;
    }

    public void setProvinceCityAddress(String provinceCityAddress) {
        ProvinceCityAddress = provinceCityAddress;
    }

    public String getDeliveryState() {
        return DeliveryState;
    }

    public void setDeliveryState(String deliveryState) {
        DeliveryState = deliveryState;
    }
}

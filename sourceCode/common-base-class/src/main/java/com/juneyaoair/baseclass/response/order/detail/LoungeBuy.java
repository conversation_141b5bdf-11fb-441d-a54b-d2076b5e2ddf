package com.juneyaoair.baseclass.response.order.detail;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * Created by qinxiaoming on 2016-6-2.
 */
@XmlRootElement(name = "LoungeBuy")
@XmlAccessorType(XmlAccessType.FIELD)
public class LoungeBuy {

  
    private String LoungeNo;
  
    private String LoungeName;
  
    private String DepAirport;
  
    private String DepAirportNm;
  
    private String ArrAirport;
  
    private String FlightNo;
  
    private String FlightDate;
  
    private String Cabin;
  
    private String PassengerName;
  
    private String CertNo;
  
    private String TicketNo;
  
    private String HandphoneNo;
  
    private Double LoungeAmount;
  
    private int UseScore;

    /**
     * Booking-预定；Finish-已购；Cancel-取消；ApplyRefund-申请退单；Refund-已退；Used-已用
     */
    private String LoungeState;
  
    private String LoungeTitle;
  
    private String LoungeCountent;//此字段拼写有误
    private String LoungeContent;//休息室产品详细介绍
  
    private String LoungePicUrl;
  
    private String Address;
  
    private String LoungeCoupon;

    //有效期起始
    private String ValidDateBegin;
    //有效期结束
    private String ValidDateEnd;
  
    private int LoungeCount;
  
    private Double LoungeOrderAmount;
  
    private String OrderDatetime;
  
    private String Remark;
  
    private String IsSingleOrder;
  
    private String IsUnitedOrder;

    public LoungeBuy() {
    }

    public String getLoungeNo() {
        return LoungeNo;
    }

    public void setLoungeNo(String loungeNo) {
        LoungeNo = loungeNo;
    }

    public String getLoungeName() {
        return LoungeName;
    }

    public void setLoungeName(String loungeName) {
        LoungeName = loungeName;
    }

    public String getDepAirport() {
        return DepAirport;
    }

    public void setDepAirport(String depAirport) {
        DepAirport = depAirport;
    }

    public String getDepAirportNm() {
        return DepAirportNm;
    }

    public void setDepAirportNm(String depAirportNm) {
        DepAirportNm = depAirportNm;
    }

    public String getArrAirport() {
        return ArrAirport;
    }

    public void setArrAirport(String arrAirport) {
        ArrAirport = arrAirport;
    }

    public String getFlightNo() {
        return FlightNo;
    }

    public void setFlightNo(String flightNo) {
        FlightNo = flightNo;
    }

    public String getFlightDate() {
        return FlightDate;
    }

    public void setFlightDate(String flightDate) {
        FlightDate = flightDate;
    }

    public String getCabin() {
        return Cabin;
    }

    public void setCabin(String cabin) {
        Cabin = cabin;
    }

    public String getPassengerName() {
        return PassengerName;
    }

    public void setPassengerName(String passengerName) {
        PassengerName = passengerName;
    }

    public String getCertNo() {
        return CertNo;
    }

    public void setCertNo(String certNo) {
        CertNo = certNo;
    }

    public String getTicketNo() {
        return TicketNo;
    }

    public void setTicketNo(String ticketNo) {
        TicketNo = ticketNo;
    }

    public String getHandphoneNo() {
        return HandphoneNo;
    }

    public void setHandphoneNo(String handphoneNo) {
        HandphoneNo = handphoneNo;
    }

    public Double getLoungeAmount() {
        return LoungeAmount;
    }

    public void setLoungeAmount(Double loungeAmount) {
        LoungeAmount = loungeAmount;
    }

    public int getUseScore() {
        return UseScore;
    }

    public void setUseScore(int useScore) {
        UseScore = useScore;
    }

    public String getLoungeState() {
        return LoungeState;
    }

    public void setLoungeState(String loungeState) {
        LoungeState = loungeState;
    }

    public String getLoungeTitle() {
        return LoungeTitle;
    }

    public void setLoungeTitle(String loungeTitle) {
        LoungeTitle = loungeTitle;
    }

    public String getLoungeCountent() {
        return LoungeCountent;
    }

    public void setLoungeCountent(String loungeCountent) {
        LoungeCountent = loungeCountent;
    }

    public String getLoungeContent() {
        return LoungeContent;
    }

    public void setLoungeContent(String loungeContent) {
        LoungeContent = loungeContent;
    }

    public String getLoungePicUrl() {
        return LoungePicUrl;
    }

    public void setLoungePicUrl(String loungePicUrl) {
        LoungePicUrl = loungePicUrl;
    }

    public String getAddress() {
        return Address;
    }

    public void setAddress(String address) {
        Address = address;
    }

    public String getLoungeCoupon() {
        return LoungeCoupon;
    }

    public void setLoungeCoupon(String loungeCoupon) {
        LoungeCoupon = loungeCoupon;
    }

    public String getValidDateBegin() {
        return ValidDateBegin;
    }

    public void setValidDateBegin(String validDateBegin) {
        ValidDateBegin = validDateBegin;
    }

    public String getValidDateEnd() {
        return ValidDateEnd;
    }

    public void setValidDateEnd(String validDateEnd) {
        ValidDateEnd = validDateEnd;
    }

    public int getLoungeCount() {
        return LoungeCount;
    }

    public void setLoungeCount(int loungeCount) {
        LoungeCount = loungeCount;
    }

    public Double getLoungeOrderAmount() {
        return LoungeOrderAmount;
    }

    public void setLoungeOrderAmount(Double loungeOrderAmount) {
        LoungeOrderAmount = loungeOrderAmount;
    }

    public String getOrderDatetime() {
        return OrderDatetime;
    }

    public void setOrderDatetime(String orderDatetime) {
        OrderDatetime = orderDatetime;
    }

    public String getRemark() {
        return Remark;
    }

    public void setRemark(String remark) {
        Remark = remark;
    }

    public String getIsSingleOrder() {
        return IsSingleOrder;
    }

    public void setIsSingleOrder(String isSingleOrder) {
        IsSingleOrder = isSingleOrder;
    }

    public String getIsUnitedOrder() {
        return IsUnitedOrder;
    }

    public void setIsUnitedOrder(String isUnitedOrder) {
        IsUnitedOrder = isUnitedOrder;
    }
}

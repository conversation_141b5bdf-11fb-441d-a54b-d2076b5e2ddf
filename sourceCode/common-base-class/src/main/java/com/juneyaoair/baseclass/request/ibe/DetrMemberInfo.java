package com.juneyaoair.baseclass.request.ibe;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * @Author:zc
 * @Description:
 * @Date:Created in 14:52 2018/3/20
 * @Modified by:
 */
@Data
@NoArgsConstructor
public class DetrMemberInfo {
    @NotNull(message = "证件号不能为空")
    private String certNo;
    private String depAirportCode;
    @NotNull(message = "旅客姓名不能为空")
    private String passengerNm;
    @NotNull(message = "渠道用户号不能为空")
    private String channelNo;
    @ApiModelProperty(value = "解析联票",notes = "默认为false,不进行联票解析")
    private boolean analyzeTicket;
    private String clientIp;
}

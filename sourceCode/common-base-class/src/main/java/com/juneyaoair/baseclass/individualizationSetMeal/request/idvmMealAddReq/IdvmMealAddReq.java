package com.juneyaoair.baseclass.individualizationSetMeal.request.idvmMealAddReq;

import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Classname IdvmMealAddReq
 * @Description 新增/修改 餐食申请实体类
 * @Date 2019/8/5 10:16
 * @Created by yzh
 */
@Data
public class IdvmMealAddReq {
    //用户编号
    @NotBlank(message = "用户id不能为空")
    private String ffpId;
    private String ffpCardNo;
    //登录令牌信息
    @NotBlank(message = "登录信息不能为空")
    private String loginKeyInfo;
    //证件类型
    private String idCardType;
    //旅客联系方式
    private String accountContact;
    //出发机场三字码
    @NotBlank(message = "出发机场三字码不能为空")
    private String departureStation;
    //到达机场三字码
    private String arrivalStation;
    //舱位
    @NotBlank(message = "舱位信息不能为空")
    private String cabinType;
    //航班日期 yyyy-MM-dd
    @NotBlank(message = "航班日期不能为空")
    private String flightDate;
    //航班号
    @NotBlank(message = "航班号不能为空")
    private String flightNo;
    //旅客证件号
    @NotBlank(message = "乘客证件号不能为空")
    private String passIdcard;
    //旅客姓名
    @NotBlank(message = "旅客姓名不能为空")
    private String passName;
    //票号
    @NotBlank(message = "票号不能为空")
    private String ticketNo;
    //具体申请数据
    @NotNull(message = "餐食列表不能为空")
    private List<MealData> bookMealList;
    //申请截至日期 2019-07-24 00:55:00
    private String endLimitDate;
    //备注
    private String remark;
}

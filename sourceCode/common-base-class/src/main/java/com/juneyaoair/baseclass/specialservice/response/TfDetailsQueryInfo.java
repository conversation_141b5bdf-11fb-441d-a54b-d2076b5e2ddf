package com.juneyaoair.baseclass.specialservice.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by guan<PERSON>yin on 2018/11/27.
 * <AUTHOR>
 * 特服详情返回值
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class TfDetailsQueryInfo {
    private String id; // 申请单id
    private String recordStatus; // 审核状态 1.预约成功、2.申请成功、3.受理中、4.受理失败、5.已取消
    private String speServiceName;//特殊服务名称
    /**
     * 乘机人：姓名、年龄、性别、身高、体重、证件类型、证件号、出生日期、联系方式、孕周、用氧量
     */
    private String passName; // 乘机人姓名
    private int passAge;//年龄
    private String passSex; // 乘机人性别
    private int passHeight ; // 乘机人身高(cm)
    private int passWeight ; // 乘机人体重(kg)
    private String passCertType; // 乘机人证件类型
    private String passCertification; // 乘机人证件号
    private String passBirthDate; // 乘机人出生日期
    private String passCountryTelCode;//乘机人联系方式 区号
    private String passContact; // 乘机人联系方式

    private int pregnancyWeek; // 孕周
    private int userOxygen;//用氧量
    /**
     * 航班信息：票号、航班号、航班日期、到达站三字码、出发站三字码
     */
    private String ticketNo;//票号
    private String flightNo; // 航班号
    private String flightDate; // 航班日期
    private String departureStationName;//始发站名称
    private String destinationStationName;//到达站名称
    /**
     * 始发站三字码
     */
    private String depAirCode;
    /**
     * 到达站三字码
     */
    private String arrAirCode;

    /**
     * 预订人:姓名、手机号、邮箱
     */
    private String ydPersonName;//预定人姓名
    private String ydPersonPhone;//预定人手机号
    private String ydPersonCountryTelCode;//预定人手机区号
    private String ydPersonEmail;//预定人邮箱
    /**
     * 陪同人：姓名、证件类型、证件号、性别、出生日期、联系方式
     */
    private String ptPersonName;//陪同人姓名
    private String ptPersonCertType;//陪同人证件类型
    private String ptPersonCert;//陪同人证件号
    private String ptPersonSex;//陪同人性别
    private String ptPersonBirthDate;//陪同人出生日期
    private String ptPersonContact;//陪同人联系方式
    private String ptPersonCountryTelCode;//乘机人联系方式 区号
    /**
     * 接机人：姓名、证件类型、证件号、性别、出生日期、联系方式、地址
     */
    private String jjPersonName; // 接机人姓名
    private String jjPersonCertType;//接机人证件类型
    private String jjPersonCert; // 接机人证件
    private String jjPersonSex; // 接机人性别
    private String jjPersonBirthDate; // 接机人出生日期
    private String jjPersonContact; // 接机人联系方式
    private String jjPersonCountryTelCode;//乘机人联系方式 区号
    /**
     * 送机人：姓名、证件类型、证件号、地址、性别、联系方式、出生日期
     */
    private String sjPersonName;//送机人姓名
    private String sjPersionCertType;//送机人证件类型
    private String sjPersonCert;//送机人证件号
    private String sjPersonSex;//送机人性别
    private String sjPersonContact;//送机人联系方式
    private String sjPersonBirthDate;//送机人出生日期
    private String sjPersonCountryTelCode;//乘机人联系方式 区号

    private int disabilityNum ; // 残疾人团队人数
    private String disabilityType; // 残疾类型
    private String zlDisability;//智力残疾
    private String ztDisability;//肢体残疾
    private String jsDisability;//精神残疾
    private String attachment; // 附件
    private String serviceDogType;//服务犬类型
    private String isCarryWheelchair; // 是否携带轮椅 1是 0 否
    private String speServiceType; // 特殊服务类型
    private String cancelReason; //2020-07-15 取消原因
    private String auditReason; //2020-07-15 审核原因
    private String number;
}

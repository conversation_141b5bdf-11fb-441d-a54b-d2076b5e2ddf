package com.juneyaoair.baseclass.request.crm;

import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2019-3-1 14:10
 * @description：给前端的升级积分返回值
 * @modified By：
 * @version: $
 */
@Data
public class MemberUpGradeResp {
    /**
     * 接口返回码，”000”表示调用成功，其他表示调用失败
     */
    private String StatusCode;
    /**
     * 接口返回提示信息；成功返回成功
     */
    private String Message;

    /**
     * 新级别名称
     */
    private String New_level_name;

    /**
     * 当前级别Code
     */
    private String New_level_Code;
    /**
     * 升级所需积分
     */
    private int LevelUP_mile;
    /**
     * 升级所需航段
     */
    private int LevelUP_segment;
    /**
     * 截止日期（格式yyyy-mm-dd）
     */
    private String EndDate;

    /**
     * 升级所需积分百分比
     */
    private String upMilePercent;
    /**
     * 升级所需航段百分比
     */
    private String upSegmentPercent;
    /**
     * 定级积分
     */
    private int NextLevel_mile;
    /**
     * 定级航段
     */
    private int NextLevel_segment;
}

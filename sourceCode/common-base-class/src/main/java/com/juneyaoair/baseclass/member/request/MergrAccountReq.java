package com.juneyaoair.baseclass.member.request;

import com.juneyaoair.baseclass.common.base.UserInfoMust;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.NotBlank;

/**
 * @Classname MergrAccountReq
 * @Description 合并账户请求
 * @Date 2019/10/15 15:48
 * @Created by yzh
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MergrAccountReq extends UserInfoMust {
    @NotBlank(message = "证件号不能为空")
    private String certificateNumber;
}

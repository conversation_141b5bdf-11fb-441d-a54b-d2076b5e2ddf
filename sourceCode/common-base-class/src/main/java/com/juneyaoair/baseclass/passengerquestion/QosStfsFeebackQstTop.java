package com.juneyaoair.baseclass.passengerquestion;

import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2021-3-23 18:50
 * @description：
 * @modified By：
 * @version: $
 */
@Data
public class QosStfsFeebackQstTop {
    private String answerTriggContent;//答案触发文本内容
    private String createDate;// 创建时间 ,
    private String createUserId;// 创建人系统ID ,
    private String createUserName;// 创建人名称 ,
    private String feebackAnswer;//反馈答案编号 ,
    private String feebackAnswerContent;// 反馈答案内容 ,
    private String feebackId;// 反馈ID ,
    private QosStfsFeedback feedback;//  反馈的旅客信息 ,
    private String feedbackInfo;//  反馈内容(导出使用) ,
    private String id;// 主键 ,
    private String isRectify;//分发整改状态{0:未分发;1:已分发,处理中;2:已处理} ,
    private QosStfsTopic qosStfsTopic;// (QosStfsTopic, optional): 题目 ,
    private String questId;// 问卷ID ,
    private String questTitle;//  问卷标题(导出使用) ,
    private String status;// 记录状态:{1:在用,0:弃用} ,
    private String topicId;//  题目ID ,
    private String unsatisfyAnswerContent;//  不满意题目答案内容 ,
    private String unsatisfyFbkAnswer;//  不满意反馈答案编号 ,
    private String unsatisfyTopicAnswer;//  对应的不满意题目答案触发的文本内容 ,
    private String unsatisfyTopicId;// 不满意题目ID ,
    private String updateDate;// 更新时间 ,
    private String updateUserId;//  更新任系统ID ,
    private String updateUserName;// 更新人名称
}

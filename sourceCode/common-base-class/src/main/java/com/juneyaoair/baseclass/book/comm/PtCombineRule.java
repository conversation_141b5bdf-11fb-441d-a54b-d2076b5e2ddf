package com.juneyaoair.baseclass.book.comm;

import lombok.Data;

/**
 * <AUTHOR>
 * @description  组合限制条件
 * @date 2019/1/9  19:16.
 */
@Data
public class PtCombineRule {
    private	String[]	CombineSegList	;	//	为空没有限制。有值时表示可以与该条运价组合的后续起飞到达机场对列表。数组中每一行一条起飞到达机场对	PVGPEK
    private	String[]	Cabins	;	//	为空没有限制。有值时表示可以和该条运价组合的舱位代码（CabinFare.CabinCode）。数组中每一行一个舱位代码（多程惠达会限制组合舱位，如：头等与头等，经济与经济）
    private	String	CombineId	;	//	组合Id,为空没有限制,有值时相同值的CombineId的运价才能组合
}

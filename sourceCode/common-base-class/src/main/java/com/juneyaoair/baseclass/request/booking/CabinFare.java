package com.juneyaoair.baseclass.request.booking;

import com.google.gson.Gson;
import com.juneyaoair.appenum.av.PackageTypeEnum;
import com.juneyaoair.baseclass.av.common.TravelPrivilege;
import com.juneyaoair.thirdentity.av.comm.PtChangeRuleInfo;
import com.juneyaoair.thirdentity.av.comm.PtRefundRuleInfo;
import io.swagger.annotations.ApiModelProperty;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.math.BigDecimal;
import java.util.List;

@XmlRootElement(name = "CabinFare")
@XmlAccessorType(XmlAccessType.FIELD)
public class CabinFare {
	
	private String PriceShowType;
	private String ID;
	private String CabinCode; //舱位
	private String CabinComb; //联程舱位
	private String CabinClass; //舱位等级
	private String CabinNumber; //舱位数量
	private String PriceProductType; //运价产品类型1 - 公布,2 - 私有,3 - 多程惠达，4 - 中转联程
	private String PriceRouteType; //运价航程类型OW - 单程,RT - 往返
	private String PassengerType; //乘客类型ADT － 成人，CHD － 儿童，INF － 婴儿

	private String  PassengerIdentity;//乘客身份标识  OLD 老人  迪士尼

	private String FareBasis; //运价基础
	@ApiModelProperty(value = "规则编码")
	private String RuleId;

	public String getPassengerIdentity() {
		return PassengerIdentity;
	}

	public void setPassengerIdentity(String passengerIdentity) {
		PassengerIdentity = passengerIdentity;
	}

	private String TourCode; //旅行代码
	private String Discount; //折扣率与Y公布运价的比率,如:值为10表示10%
	private String EI; //限制条件写入PNR的签注信息
	private String Comment; //备注项用于前端显示说明信息，如：解释签注信息或备注中转联程提供住宿
	private String Baggage; //行李重量计重制:20KG 计件制:2PC
	private String ValidityPeriod; //客票有效期(最长停留时间)有效取值范围:1Y或1M或1D 该项值为空时表示没有限制，有值时需要与航班日期进行计算
	private String MinStay; //最短停留时间1Y1M1D 该项值为空时表示没有限制，有值时需要与航班日期进行计算
	private double PriceValue; //票价票价金额为-1时是无效票价不能进行销售,使用积分时此价格为PriceValue - Deductibls
	/** 组合票价 ，中转-隔开,往返/隔开*/
	private String priceValueComb;
	private double RSP; //销售参考价用于前端宣传打折效果使用,当值为-1时表示找不到有效的销售参考价
	private boolean  RefundedFlag; //是否可退票
	private String RefundedComment; //退票政策描述说明退票费收取或计算方法
	private String ChangedComment; //变更政策描述说明改期、签转和升舱政策
	private boolean  RescheduledFlag; //是否可免费改期
	private int FreeChangeTimes; //免费改期次数免费改期不限次数时，目前运价系统会给出99次
	private boolean ChangeAirLineFlag; //是否可改签
	private boolean UpgradeFlag; //是否可升舱
	private double UpgradeFee;//升舱改期手续费
	private double YPrice; //经济舱公布运价全价金额
	private String FareID; //运价编号运价系统中该条运价的主键，计算退票费时有用
	private double YQTax; //燃油费国内航程时该字段有效,国际时该字段无效值为-1
	private double CNTax; //建设税国内航程时该字段有效,国际时该字段无效值为-1
	private	String	DynamicCabin;
	private	String	DynamicFareID;
	private	String  CombineId;
	private String FareSign; //运价验证串航班查询结果中Price.FareSign字段
	private CombineRule CombineRuleInfo;
	private ScoreUse[] ScoreUseInfoList; //积分使用信息列表
	private ScoreGift ScoreGiftInfo; //积分赠送信息
	private int IntDiscount;  //国际票折扣率

	//退改规则列表
	private List<PtRefundRuleInfo> RefundedRules; //退票规则信息  RefundedFlag为false时该字段信息为空
	private List<PtChangeRuleInfo> ChangeRules ; //更改规则信息 UpgradeFlag为false并且RescheduledFlag为true时该字段信息为空

	//改期升舱手续费
	private double changeServiceCharge;
	//改期升舱票面差价
	private double ticketPriceDiff;
	//改期升舱税费差价
	private double taxDiff;
	//国际票时存放税费
	private double tax;
	//燃油差价
	private double yqTaxDiff;
	//机建差价
	private double cnTaxDiff;
	//改期升舱总差价   票面差价+税费差价+机建燃油差价+机建差价+燃油差价
	private double totalDiff;
	private	double	discountPriceValue;	//票价优惠金额

	/**
	 * @see PackageTypeEnum
	 */
	//ROUND_TRIP_PRIVILEGES  --往返特惠 具体参考 PackageTypeEnum
	private String cabinType;
	private List<TravelPrivilege> travelPrivilegeList; //溢价权益 出行尊享

	private BigDecimal PreferentialPrice;  //拥军优惠价格

	/**
	 * 产品编号   九元运价需要
	 */
    private String  productNo;

	/**
	 * 拥军优惠后价格
	 */
	private  BigDecimal  DiscountedPriceValue;

	private String specialFareCode;

	public String getSpecialFareCode() {
		return specialFareCode;
	}

	public void setSpecialFareCode(String specialFareCode) {
		this.specialFareCode = specialFareCode;
	}

	public BigDecimal getDiscountedPriceValue() {
		return DiscountedPriceValue;
	}

	public void setDiscountedPriceValue(BigDecimal discountedPriceValue) {
		DiscountedPriceValue = discountedPriceValue;
	}

	public BigDecimal getPreferentialPrice() {
		return PreferentialPrice;
	}

	public void setPreferentialPrice(BigDecimal preferentialPrice) {
		PreferentialPrice = preferentialPrice;
	}


	public String getPriceShowType() {
		return PriceShowType;
	}
	public void setPriceShowType(String priceShowType) {
		PriceShowType = priceShowType;
	}
	public String getID() {
		return ID;
	}
	public void setID(String iD) {
		ID = iD;
	}
	public String getCabinCode() {
		return CabinCode;
	}
	public void setCabinCode(String cabinCode) {
		CabinCode = cabinCode;
	}
	public String getCabinClass() {
		return CabinClass;
	}
	public void setCabinClass(String cabinClass) {
		CabinClass = cabinClass;
	}
	public String getCabinNumber() {
		return CabinNumber;
	}
	public void setCabinNumber(String cabinNumber) {
		CabinNumber = cabinNumber;
	}
	public String getPriceProductType() {
		return PriceProductType;
	}
	public void setPriceProductType(String priceProductType) {
		PriceProductType = priceProductType;
	}
	public String getPriceRouteType() {
		return PriceRouteType;
	}
	public void setPriceRouteType(String priceRouteType) {
		PriceRouteType = priceRouteType;
	}
	public String getPassengerType() {
		return PassengerType;
	}
	public void setPassengerType(String passengerType) {
		PassengerType = passengerType;
	}
	public String getFareBasis() {
		return FareBasis;
	}
	public void setFareBasis(String fareBasis) {
		FareBasis = fareBasis;
	}
	public String getTourCode() {
		return TourCode;
	}
	public void setTourCode(String tourCode) {
		TourCode = tourCode;
	}
	public String getDiscount() {
		return Discount;
	}
	public void setDiscount(String discount) {
		Discount = discount;
	}
	public String getEI() {
		return EI;
	}
	public void setEI(String eI) {
		EI = eI;
	}
	public String getComment() {
		return Comment;
	}
	public void setComment(String comment) {
		Comment = comment;
	}
	public String getBaggage() {
		return Baggage;
	}
	public void setBaggage(String baggage) {
		Baggage = baggage;
	}
	public String getValidityPeriod() {
		return ValidityPeriod;
	}
	public void setValidityPeriod(String validityPeriod) {
		ValidityPeriod = validityPeriod;
	}
	public String getMinStay() {
		return MinStay;
	}
	public void setMinStay(String minStay) {
		MinStay = minStay;
	}
	public double getPriceValue() {
		return PriceValue;
	}
	public void setPriceValue(double priceValue) {
		PriceValue = priceValue;
	}
	public double getRSP() {
		return RSP;
	}
	public void setRSP(double rSP) {
		RSP = rSP;
	}
	public boolean isRefundedFlag() {
		return RefundedFlag;
	}
	public void setRefundedFlag(boolean refundedFlag) {
		RefundedFlag = refundedFlag;
	}
	public String getRefundedComment() {
		return RefundedComment;
	}
	public void setRefundedComment(String refundedComment) {
		RefundedComment = refundedComment;
	}
	public String getChangedComment() {
		return ChangedComment;
	}
	public void setChangedComment(String changedComment) {
		ChangedComment = changedComment;
	}
	public boolean isRescheduledFlag() {
		return RescheduledFlag;
	}
	public void setRescheduledFlag(boolean rescheduledFlag) {
		RescheduledFlag = rescheduledFlag;
	}
	public int getFreeChangeTimes() {
		return FreeChangeTimes;
	}
	public void setFreeChangeTimes(int freeChangeTimes) {
		FreeChangeTimes = freeChangeTimes;
	}
	public boolean isChangeAirLineFlag() {
		return ChangeAirLineFlag;
	}
	public void setChangeAirLineFlag(boolean changeAirLineFlag) {
		ChangeAirLineFlag = changeAirLineFlag;
	}
	public boolean isUpgradeFlag() {
		return UpgradeFlag;
	}
	public void setUpgradeFlag(boolean upgradeFlag) {
		UpgradeFlag = upgradeFlag;
	}
	public double getYPrice() {
		return YPrice;
	}
	public void setYPrice(double yPrice) {
		YPrice = yPrice;
	}
	public String getFareID() {
		return FareID;
	}
	public void setFareID(String fareID) {
		FareID = fareID;
	}
	public double getYQTax() {
		return YQTax;
	}
	public void setYQTax(double yQTax) {
		YQTax = yQTax;
	}
	public double getCNTax() {
		return CNTax;
	}
	public void setCNTax(double cNTax) {
		CNTax = cNTax;
	}
	public String getFareSign() {
		return FareSign;
	}
	public void setFareSign(String fareSign) {
		FareSign = fareSign;
	}
	public ScoreUse[] getScoreUseInfoList() {
		return ScoreUseInfoList;
	}
	public void setScoreUseInfoList(ScoreUse[] scoreUseInfoList) {
		ScoreUseInfoList = scoreUseInfoList;
	}
	public ScoreGift getScoreGiftInfo() {
		return ScoreGiftInfo;
	}
	public void setScoreGiftInfo(ScoreGift scoreGiftInfo) {
		ScoreGiftInfo = scoreGiftInfo;
	}
	public int getIntDiscount() {
		return IntDiscount;
	}
	public void setIntDiscount(int intDiscount) {
		IntDiscount = intDiscount;
	}
	public CombineRule getCombineRuleInfo() {
		return CombineRuleInfo;
	}
	public void setCombineRuleInfo(CombineRule combineRuleInfo) {
		CombineRuleInfo = combineRuleInfo;
	}	
	public String getDynamicCabin() {
		return DynamicCabin;
	}
	public void setDynamicCabin(String dynamicCabin) {
		DynamicCabin = dynamicCabin;
	}
	public String getDynamicFareID() {
		return DynamicFareID;
	}
	public void setDynamicFareID(String dynamicFareID) {
		DynamicFareID = dynamicFareID;
	}
	public String getCombineId() {
		return CombineId;
	}
	public void setCombineId(String combineId) {
		CombineId = combineId;
	}

	public double getChangeServiceCharge() {
		return changeServiceCharge;
	}

	public void setChangeServiceCharge(double changeServiceCharge) {
		this.changeServiceCharge = changeServiceCharge;
	}

	public double getTicketPriceDiff() {
		return ticketPriceDiff;
	}

	public void setTicketPriceDiff(double ticketPriceDiff) {
		this.ticketPriceDiff = ticketPriceDiff;
	}

	public double getTaxDiff() {
		return taxDiff;
	}

	public void setTaxDiff(double taxDiff) {
		this.taxDiff = taxDiff;
	}

	public double getYqTaxDiff() {
		return yqTaxDiff;
	}

	public void setYqTaxDiff(double yqTaxDiff) {
		this.yqTaxDiff = yqTaxDiff;
	}

	public double getCnTaxDiff() {
		return cnTaxDiff;
	}

	public void setCnTaxDiff(double cnTaxDiff) {
		this.cnTaxDiff = cnTaxDiff;
	}

	public double getTotalDiff() {
		return totalDiff;
	}

	public void setTotalDiff(double totalDiff) {
		this.totalDiff = totalDiff;
	}

	public double getTax() {
		return tax;
	}

	public void setTax(double tax) {
		this.tax = tax;
	}

	public List<PtRefundRuleInfo> getRefundedRules() {
		return RefundedRules;
	}

	public void setRefundedRules(List<PtRefundRuleInfo> refundedRules) {
		RefundedRules = refundedRules;
	}

	public List<PtChangeRuleInfo> getChangeRules() {
		return ChangeRules;
	}

	public void setChangeRules(List<PtChangeRuleInfo> changeRules) {
		ChangeRules = changeRules;
	}

	public double getUpgradeFee() {
		return UpgradeFee;
	}

	public void setUpgradeFee(double upgradeFee) {
		UpgradeFee = upgradeFee;
	}

	public double getDiscountPriceValue() {
		return discountPriceValue;
	}

	public void setDiscountPriceValue(double discountPriceValue) {
		this.discountPriceValue = discountPriceValue;
	}

	public String getPriceValueComb() {
		return priceValueComb;
	}

	public void setPriceValueComb(String priceValueComb) {
		this.priceValueComb = priceValueComb;
	}

	public String getCabinComb() {
		return CabinComb;
	}

	public void setCabinComb(String cabinComb) {
		CabinComb = cabinComb;
	}

	public String getCabinType() {
		return cabinType;
	}

	public void setCabinType(String cabinType) {
		this.cabinType = cabinType;
	}

	public List<TravelPrivilege> getTravelPrivilegeList() {
		return travelPrivilegeList;
	}

	public void setTravelPrivilegeList(List<TravelPrivilege> travelPrivilegeList) {
		this.travelPrivilegeList = travelPrivilegeList;
	}
	public String getProductNo() {
		return productNo;
	}

	public void setProductNo(String productNo) {
		this.productNo = productNo;
	}
	public String getRuleId() {
		return RuleId;
	}

	public void setRuleId(String ruleId) {
		RuleId = ruleId;
	}

	@Override
	public String toString() {
		return new Gson().toJson(this);
	}
}
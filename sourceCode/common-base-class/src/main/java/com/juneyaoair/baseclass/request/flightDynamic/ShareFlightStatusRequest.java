package com.juneyaoair.baseclass.request.flightDynamic;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
@ApiModel(value = "NewFlightStatusRequest")
public class ShareFlightStatusRequest {
    @ApiModelProperty(value = "航班号")
    private String flightNo;
    @ApiModelProperty(value = "航班日期")
    private String flightDate;
    @ApiModelProperty(value = "出发机场三字码")
    private String departureAirport;
    @ApiModelProperty(value = "到达机场三字码")
    private String arrivalAirport;
    @NotNull(message = "签名不能为空")
    private String sign;
    private String ffpCardNo;
    private String ffpId;
}

package com.juneyaoair.baseclass.newcoupon.resp;

import com.juneyaoair.baseclass.newcoupon.req.protocol.TicketInsuranceDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: caolei
 * @Description: 付费选座订单详情
 * @Date: 2021/8/12 19:52
 * @Modified by:
 */
@Data
public class PaySeatCouponOrderDetail {

    @ApiModelProperty(value = "航班号")
    private String flightNo;

    @ApiModelProperty(value = "起飞机场")
    private String depAirPortCode;

    @ApiModelProperty(value = "起飞城市名称")
    private String depCityName;

    @ApiModelProperty(value = "起飞机场名称")
    private String depAirportName;

    @ApiModelProperty(value = "起飞机场航站楼")
    private String depAirportTerminal;

    @ApiModelProperty(value = "到达机场")
    private String arrAirPortCode;

    @ApiModelProperty(value = "到达城市名称")
    private String arrCityName;

    @ApiModelProperty(value = "到达机场名称")
    private String arrAirportName;

    @ApiModelProperty(value = "到达机场航站楼")
    private String arrAirportTerminal;

    @ApiModelProperty(value = "国际码")
    private String phoneCountryCode;

    @ApiModelProperty(value = "通电电话")
    private String contactTelphone;

    @ApiModelProperty(value = "航班日期")
    private String flightDate;

    @ApiModelProperty(value = "出发时间")
    private String depTime;

    @ApiModelProperty(value = "到达时间")
    private String arrTime;

    @ApiModelProperty(value = "飞行时间")
    private Long flightTime;

    @ApiModelProperty(value = "飞行天数")
    private Integer flightDay;

    @ApiModelProperty(value = "机型名称")
    private String flightTypeName;

    @ApiModelProperty(value = "舱位名称")
    private String cabinName;

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "渠道订单号")
    private String channelOrderNo;

    @ApiModelProperty(value = "订单状态")
    private String orderState;

    @ApiModelProperty(value = "订单状态名称")
    private String orderStateName;

    @ApiModelProperty(value = "订单创建时间")
    private String orderCreateDatetime;

    @ApiModelProperty(value = "支付状态")
    private String payState;

    @ApiModelProperty(value = "订单总金额")
    private String orderTotalAmount;

    @ApiModelProperty(value = "订单增送积分总计")
    private String ffpGiftTotalScore;

    @ApiModelProperty(value = "订单使用积分总计")
    private String ffpUseTotalScore;

    @ApiModelProperty(value = "币种")
    private String currency;

    @ApiModelProperty(value = "优惠劵金额合计")
    private String couponAmountSum;

    @ApiModelProperty(value = "支付方式")
    private String payMethod;

    @ApiModelProperty(value = "支付时间")
    private String paidDateTime;

    @ApiModelProperty(value = "支付超时时间/单位（分钟）")
    private Integer payDatetimeLimit;

    @ApiModelProperty(value = "订单剩余支付时间")
    private Long orderRemainTime;

    @ApiModelProperty(value = "订单类型")
    private String couponSource;

    @ApiModelProperty(value = "航班状态")
    private String flightStatus;

    @Deprecated
    @ApiModelProperty(value = "支付方式 1:积分; 2:现金; 3:积分现金组合支付(现金转积分); 4:积分现金组合支付")
    private Integer payType;

    @ApiModelProperty(value = "支付方式(PayTypeEnum) 1:积分; 2:现金; 3:积分现金组合支付(现金转积分); 4:积分现金组合支付")
    private List<String> payTypeList;

    @ApiModelProperty(value = "是否内部渠道")
    private String isSelf;

    @ApiModelProperty(value = "子订单信息")
    private List<PaySeatSubOrderDetail> subOrderList;

    @ApiModelProperty(value = "保险状态")
    private String insuranceState;

    @ApiModelProperty(value = "保险状态名称")
    private String insuranceStateName;

    @ApiModelProperty(value = "保险信息列表")
    private List<TicketInsuranceDto> ticketInsuranceList;

    @ApiModelProperty(value = "航联是否推送保险数据 Y：已推送 N:未推送")
    private String isPushInsurance;

}
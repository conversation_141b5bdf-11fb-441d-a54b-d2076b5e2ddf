package com.juneyaoair.baseclass.response.commonPerson;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.juneyaoair.thirdentity.passengers.common.GeneralContactCertInfo;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.Size;
import java.util.List;

@Data
public class QueryCommonPersonInfo {
	private Integer CommonContactId; //常用旅客联系信息ID,主键自动生成
	private String ChannelCustomerNo;//渠道客户编号,渠道客户类型为CRM时为常客的CRM_ID，其它时为自编号
	private String ChannelCustomerType;//渠道客户类型,CRM - 常客,Other - 其它
	private String PassengerName; //乘客姓名	英文姓名可以根据/分出姓和名
	private String PassengerType; //乘客类型	ADT － 成人，CHD － 儿童，INF － 婴儿
    private String PassengerIdentity;//乘客身份标识   OLD 老人    --迪士尼
	private String CertType; //证件类型	身份证:NI,护照:PP,其它证件:CC
	private String CertNo; //证件号码
	private String FfCardNo; //常客卡号	HO+卡号
	private String CountryTelCode; //手机号国际区号
	private String HandphoneNo; //手机号
	private String Birthdate; //出生日期yyyy-MM-dd	
	private String Sex; //性别	
	private String Nationality; //国籍	
	private String nationalityName; //国籍名称
	private String BelongCountry; //发证国
	private String CertValidity; //证件有效期yyyy-MM-dd	
	private String LastBookingTime; //最后预订时间yyyy-MM-dd HH:mm:ss	
	private String BookingNum; //预订次数	
	private String CreateDatetime; //机票订单创建时间格式:yyyy-MM-dd HH:mm:ss
	private String InterFlag;
	private String SaCardNo;//星盟卡号 XX+卡号
	/**
	 * 乘机人类型 A儿童畅飞卡 B成人畅飞卡
	 * @see com.juneyaoair.appenum.comonperson.CommonPersonContactType
	 */
	private String ContactType;

	//2019-08-06 新增
	private String passEnNameS;//乘客英文姓
	private String passEnNameF;//乘客英文名
	//中文姓
	private String CLastName;
	//中文名
	private String CFirstName;
	private String isGmJC;//是否军残警残

	private Boolean isOwn;//是否本人标记
	private Boolean isBeneficiary;//是否受益人标记
	private int priority;//优先级调整

	@Valid
	@Size(min = 1,message = "至少添加一个证件信息")
	private List<GeneralContactCertInfo> contactCertList;//证件信息列表

	/**
	 * 错误提示信息
	 */
	private String alertMessage;

	/**
	 * 提示类型
	 * @see com.juneyaoair.appenum.comonperson.CommonPersonMessageType
	 */
	private String messageType;

	/**
	 * 畅飞卡2.0类型
	 * @see com.juneyaoair.appenum.rightcoupon.VoucherTypesEnum
	 * "UnlimitedFly","吉祥畅飞卡2.0"
	 *  UnlimitedFlySF","吉祥畅飞卡2.0 春运版"
	 */
	@JsonIgnore
	private String unlimitedCardType;

	/**
	 * 是否拥军优属
	 */
	private Boolean isYjYs;

	/**
	 * 是否CRm数据
	 */
	private Boolean isCrmData;
}
package com.juneyaoair.baseclass.coupon.response.v3;

import lombok.Data;

import java.util.List;

/**
 * @ClassName NewChangeCouponProductResponse
 * <AUTHOR>
 * @Description 查询权益券产品列表响应
 * @Date 2020-12-17 10:48
 **/
@Data
public class NewChangeCouponProductResponse {
    private List<CommonCouponProductInfo> domestic; //国内改期权益券
    private List<CommonCouponProductInfo> region; //国际改期权益券

    private String productDesc;//券的名称 根据券类型显示产品  改期券、升舱券
    /**
     * 券的类型
     * @see com.juneyaoair.appenum.rightcoupon.VoucherTypesEnum
     */
    private String productType;
}

package com.juneyaoair.baseclass.insurance.apollo;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName WinkConfig
 * @Description 下单页保险温馨提示语配置
 * @createTime 2022年09月27日 13:29
 */

public class WinkConfig {
    private String selectBefore;//选中之前的提示语
    private String selectBehind;//选中之后的提示语

    public String getSelectBefore() {
        return selectBefore;
    }

    public void setSelectBefore(String selectBefore) {
        this.selectBefore = selectBefore;
    }

    public String getSelectBehind() {
        return selectBehind;
    }

    public void setSelectBehind(String selectBehind) {
        this.selectBehind = selectBehind;
    }

    public WinkConfig() {
    }

    public WinkConfig(String selectBefore, String selectBehind) {
        this.selectBefore = selectBefore;
        this.selectBehind = selectBehind;
    }
}

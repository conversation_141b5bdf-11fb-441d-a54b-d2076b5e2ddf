package com.juneyaoair.baseclass.response.order.detail;

/**
 * Created by qinxiaoming on 2016-6-7.
 */
public class TicketDeliveryGetResp {
  
    private String ResultCode; //结果代码1001 － 成功，其它失败
  
    private String ErrorInfo; //错误信息
  
    private String orderSign;//订单查询唯一标始
  
    private String customerNo; //对应CRM ffpId
  
    private String cardNo; //对应卡号
  
    private String loginKeyInfo; //登录key文件
  
    private String  FfpId;
  
    private String  FfpCardNo;
  
    private String  ChannelOrderNo;
  
    private String  OrderNo;
  
    private TripCertSendInfo TripCertSendInfo;
  
    private String ChannelCode;
  
    private String OrderDatetime;
  
    private String PayState;
  
    private int DeliveryFee;
  
    private String DeliveryState;

    public String getResultCode() {
        return ResultCode;
    }

    public void setResultCode(String resultCode) {
        ResultCode = resultCode;
    }

    public String getErrorInfo() {
        return ErrorInfo;
    }

    public void setErrorInfo(String errorInfo) {
        ErrorInfo = errorInfo;
    }

    public String getFfpId() {
        return FfpId;
    }

    public void setFfpId(String ffpId) {
        FfpId = ffpId;
    }

    public String getFfpCardNo() {
        return FfpCardNo;
    }

    public void setFfpCardNo(String ffpCardNo) {
        FfpCardNo = ffpCardNo;
    }

    public String getChannelOrderNo() {
        return ChannelOrderNo;
    }

    public void setChannelOrderNo(String channelOrderNo) {
        ChannelOrderNo = channelOrderNo;
    }

    public String getOrderNo() {
        return OrderNo;
    }

    public void setOrderNo(String orderNo) {
        OrderNo = orderNo;
    }

    public com.juneyaoair.baseclass.response.order.detail.TripCertSendInfo getTripCertSendInfo() {
        return TripCertSendInfo;
    }

    public void setTripCertSendInfo(com.juneyaoair.baseclass.response.order.detail.TripCertSendInfo tripCertSendInfo) {
        TripCertSendInfo = tripCertSendInfo;
    }

    public String getChannelCode() {
        return ChannelCode;
    }

    public void setChannelCode(String channelCode) {
        ChannelCode = channelCode;
    }

    public String getOrderDatetime() {
        return OrderDatetime;
    }

    public void setOrderDatetime(String orderDatetime) {
        OrderDatetime = orderDatetime;
    }

    public String getPayState() {
        return PayState;
    }

    public void setPayState(String payState) {
        PayState = payState;
    }

    public int getDeliveryFee() {
        return DeliveryFee;
    }

    public void setDeliveryFee(int deliveryFee) {
        DeliveryFee = deliveryFee;
    }

    public String getOrderSign() {
        return orderSign;
    }

    public void setOrderSign(String orderSign) {
        this.orderSign = orderSign;
    }

    public String getCustomerNo() {
        return customerNo;
    }

    public void setCustomerNo(String customerNo) {
        this.customerNo = customerNo;
    }

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public String getLoginKeyInfo() {
        return loginKeyInfo;
    }

    public void setLoginKeyInfo(String loginKeyInfo) {
        this.loginKeyInfo = loginKeyInfo;
    }

    public String getDeliveryState() {
        return DeliveryState;
    }

    public void setDeliveryState(String deliveryState) {
        DeliveryState = deliveryState;
    }
}

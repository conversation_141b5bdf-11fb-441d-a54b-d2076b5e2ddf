package com.juneyaoair.baseclass.salecoupon.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.juneyaoair.baseclass.airbookstore.bean.SupportFlightInfo;
import com.juneyaoair.baseclass.salecoupon.bean.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Created by yaocf on 2018/8/1  13:08.
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public  class CouponShow{
    private String couponOrderId;//中台权益订单id
    private String activityName;  //券名称
    private String startDate;   //有效开始时间
    private String endDate; //有效结束时间
    //主题卡
    private String couponCode; //券号
    private String couponState;//优惠券状态
    private String couponStateName;  //优惠券状态名称
    private String customerName;//客户名称
    private String visitDate;//迪士尼入园日期
    private Boolean enableChange;//迪士尼是否可以改期
    /**
     * 是否展示退款按钮  true-代表可以申请退款
     */
    private boolean refundBtn;
    private String couponSource;//活动来源

    private String useDays;//入园天数

    /**
     * 乘客类型
     */
    private String  TicketType;

    /**
     * 改期状态
     */
    private Boolean HasChanged;

    /**
     * 优惠券售价
     */
    private Double price;
    /**
     * 优惠券优惠金额或行李公斤数
     */
    private Double couponPrice;
    /**
     * 出发机场
     */
    private String depAirport;
    /**
     * 到达机场
     */
    private String arrAirport;
    /**
     * 出发城市
     */
    private String depCity;
    /**
     * 到达城市
     */
    private String arrCity;
    /**
     * 出发城市名
     */
    private String depCityName;
    /**
     * 到达城市名
     */
    private String arrCityName;
    /**
     * 联系人
     */
    private String sender;
    /**
     * 联系号码
     */
    private String senderNum;
    /**
     * 收件人
     */
    private String receiver;
    /**
     * 收件号码
     */
    private String receiverNum;
    private String purchaseNotes;
    private String refundRules;
    private String voucherState;
    /**
     * 出发机场中文名
     */
    private String depAirportCName;
    /**
     *
     */
    private String IsRefunded;
    /**
     * 到达机场中文名
     */
    private String arrAirportCName;

    /**
     * 航班号
     */
    private String flightNo;
    /**
     * 出发机场航站楼
     */
    private String depAirportTerminal;
    /**
     * 到达机场航站楼
     */
    private String arrAirportTerminal;
    /**
     * 起飞时间
     */
    private String depDateTime;
    /**
     * 到达时间
     */
    private String arrDateTime;
    /**
     * 联系人手机号
     */
    private String contactTelphone;
    /**
     * 预约时间
     */
    private String depTime;
    private String voucherType;
    /**
     * 票号
     */
    private String tktNo;
    /**
     * 证件号
     */
    private String certNo;

    /**
     * 证件类型
     */
    private String certType;

    /**
     * 固包券时存储子券信息
     */
    private List<CouponShow> vouchers;
    /**
     * 备注
     */
    private String remark;
    /**
     * 主权益券号
     */
    private String mainVoucherNo;
    /**
     * 值机选座特有属性
     */
    private SeatInfo seatInfo;
    /**
     * 航班信息
     */
    private SimpleFlightInfo simpleFlightInfo;
    /**
     * 航班综合属性
     */
    private SupportFlightInfo supportFlightInfo;
    /**
     * 接送机特有属性
     */
    private TrafficInfo trafficInfo;
    /**
     * 邮寄行程单特有属性
     */
    private List<MailTravelInfo> mailTravelInfos;
    /**
     * 签证特有属性
     */
    private VisaInfo visaInfo;
    /**
     * 接送机服务类型
     * p-接机,c-送机
     */
    private String serviceType;

    /**
     * 机上购物特有属性  2020-10-12
     */
    private OnboardShoppingInfo onboardShoppingInfo;
    /**
     * 用于机上购物筛选相同产品 2020-10-12
     */
    private String activityNo;
    /**
     * 用于机上购物产品 保存同一个产品预定数量 2020-10-12
     */
    private Integer bookingCount;
    /**
     *  用于机上购物产品 周几 2020-10-12
     */
    private String weekDay;
    /**
     *  用于机上购物产品 航班日期 2020-10-12
     */
    private String flightDate;

    /**
     * 配送方式 2020-10-15 方便前端展示订单列表
     */
    private String deliveryType;
    /**
     * 配送方式中文描述 2020-10-15 方便前端展示订单列表
     */
    private String deliveryName;

    private String availDate; //可用日期 2020-11-19 （畅飞卡2.0 订单详情使用 2021-01-21至2021-01-31?2021-03-01至2021-06-30  或者 2021-01-21至2021-06-30）
    /**
     * 可评价状态 2021-03-11  COMMENTABLE 可评论
     */
    private String commentState;
    /**
     * 退款状态 2021-09-03
     */
    private String rebateState;


    @ApiModelProperty(value = "数量", notes = "预付费行李", example = "7")
    private int amount;

    @ApiModelProperty(value = "单位", notes = "预付费行李", example = "KG")
    private String unit;

    private boolean IsPreBundle;
}


/*
 * Copyright ®2018 juneyaoair Group.
 *
 *
 *
 */
package com.juneyaoair.baseclass.request.trip;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * 我的行程dto
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2018年3月5日 下午3:58:58
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MytripDetaiInfoReq {
    @NotNull(message = "会员编号不能为空")
    private String ffpId;
    @NotNull(message = "会员卡号不能为空")
    private String ffpCardNo;
    @NotNull(message = "登录验证信息不能为空")
    private String loginKeyInfo;
    @NotNull(message = "渠道号不能为空")
    private String channelCode;
    @NotNull(message = "客票号不能为空")
    private String passengerNo;
    @NotNull(message = "出发城市不能为空")
    private String depCity;
    @NotNull(message = "到达城市不能为空")
    private String arrCity;
    //旅客姓名
    private String psgName;
    // 语言类型
    private String languageType;
}

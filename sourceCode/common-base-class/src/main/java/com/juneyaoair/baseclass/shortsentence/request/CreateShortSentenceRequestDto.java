package com.juneyaoair.baseclass.shortsentence.request;

import com.juneyaoair.baseclass.common.base.UserInfoMust;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import java.io.Serializable;

/**
 * 创建吉祥语请求参数
 */
@Data
public class CreateShortSentenceRequestDto extends UserInfoMust implements Serializable{

    /**
     * 跳转链接
     */
    @NotBlank(message = "跳转链接不能为空")
    private String url;

    /**
     * 类型
     * 1:砍价口令
     */
    private int type;

}

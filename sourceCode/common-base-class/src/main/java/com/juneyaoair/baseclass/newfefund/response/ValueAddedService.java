package com.juneyaoair.baseclass.newfefund.response;

import com.juneyaoair.baseclass.response.order.detail.TripCertSendInfo;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description
 * @create 2020-06-19 9:58
 */
@Data
public class ValueAddedService {

    private String serviceName;// 服务名称  邮寄报销凭证

    private String detailName;// 行程单

    private String serviceType;// 服务类型  目前只有邮寄行程单

    private double priceValue;// 增值服务价格

    private double refundValue;// 可退金额

    private boolean refundable;// 是否可退

    private TripCertSendInfo tripCertSendInfo;
}

package com.juneyaoair.baseclass.response.memberDevelop;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.juneyaoair.baseclass.member.request.MemberRightsResponse;
import com.juneyaoair.baseclass.member.request.MemberStarRightsDynResponse;
import com.juneyaoair.baseclass.member.request.MemberStarRightsResponse;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName MemberCurrentInfo
 * @Description 用户当前信息
 * <AUTHOR>
 * @Date 2023/11/23 13:38
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MemberCurrentInfo {
    @ApiModelProperty(value = "近12月内有效航段数")
    private int effectiveSegment;

    @ApiModelProperty(value = "用户当前卡级别有效期内，有效航段数")
    private int validSegment;

    @ApiModelProperty(value = "用户当前卡等级")
    private int cardLevelCode;

    @ApiModelProperty(value = "用户当前星级")
    private int memberStarCode;

    @ApiModelProperty(value = "用户当前卡等级名称")
    private String cardLevelName;

    @ApiModelProperty(value = "当前卡等级有效截止日期")
    private String cardEndDate;

    @ApiModelProperty(value = "会员权益列表")
    private List<MemberRightsResponse> memberRightsResponse;

    @ApiModelProperty(value = "会员星级权益")
    private MemberStarRightsResponse memberStarRightsResponse;

    @ApiModelProperty(value = "会员全部星级权益")
    private List<MemberStarRightsDynResponse> totalStarRights;

}

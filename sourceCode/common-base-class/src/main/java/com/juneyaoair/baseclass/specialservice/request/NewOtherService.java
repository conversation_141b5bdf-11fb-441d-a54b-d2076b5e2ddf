package com.juneyaoair.baseclass.specialservice.request;

import com.juneyaoair.baseclass.common.base.UserInfoMust;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.Valid;

/**
 * Created by guanshiyin on 2018/11/15.
 *
 * <AUTHOR>
 * 其他服务：孕妇、用氧、患病、活体器官及血液制品运输服务申请
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class NewOtherService extends UserInfoMust {
    /**
     * 乘机人信息
     */
    @Valid
    private NewPassengerInfo passengerInfo;
    /**
     * 航班信息（票号，航班号，出行日期，始发站，到达站）
     */
    @Valid
    private NewFlightInfo flightInfo;
    /**
     * 预订人信息（姓名、手机、邮箱）
     */
    @Valid
    private NewYdPersonInfo ydPersonInfo;
    /**
     * 附件
     */
    @Valid
    private String attachment;
    /**
     * 服务类别
     */
    @NotBlank(message = "服务类别不能为空")
    private String serviceType;


    /**
     * 五类残疾种类
     */
    private String disability;


    /**
     *  有无适宜乘机医疗证明（1：是，0：否）
     */
    private  int  isMedicalCertificate;

}

package com.juneyaoair.baseclass.response.av;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.juneyaoair.baseclass.av.common.*;
import com.juneyaoair.baseclass.basicsys.response.PictureDto;
import com.juneyaoair.baseclass.change.ChangeAndRefundRule;
import com.juneyaoair.baseclass.request.av.ThemeModel;
import com.juneyaoair.thirdentity.av.comm.*;
import com.juneyaoair.thirdentity.response.tax.InternatTaxInfo;
import com.juneyaoair.util.BaseStringUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/*
 * 舱位运价
 *
 * */
@XmlRootElement(name = "CabinFare")
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(propOrder = {"ID", "PriceShowType", "CabinCode", "CabinNumber", "CabinClass",
        "PriceProductType", "PriceRouteType", "PassengerType", "FareBasis", "TourCode"
        , "IntDiscount", "Discount", "EI", "Comment", "Baggage"
        , "ValidityPeriod", "MinStay", "PriceValue", "RSP", "RefundedFlag"
        , "RefundedComment", "ChangedComment", "RescheduledFlag", "FreeChangeTimes", "ChangeAirLineFlag"
        , "UpgradeFlag", "YPrice", "FareID", "YQTax", "CNTax", "DynamicCabin", "DynamicFareID", "CombineId"
        , "FareSign", "CombineRuleInfo", "ScoreUseInfoList", "ScoreGiftInfo",
        "InsurenceAmount", "ScoreAvailable", "CouponAvailable","ShippingRulesLabel"})
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CabinFare {
    private String ID;        //ID
    private String PriceShowType;    //官网显示价格标题列：FirstClass-头等舱、Eonomic-经济舱、EconomicMin-经济舱最低价、WebSpecial-官网专享价
    private String CabinCode;    //舱位代码
    private String CabinNumber;    //可利用座位情况
    private String CabinClass;    //舱位等级
    private String PriceProductType;    //运价产品类型
    private String PriceRouteType;    //运价航程类型
    private String PassengerType;    //旅客类型
    private String PassengerIdentity;    //旅客特殊身份标识 STU-留学生
    private String FareBasis;    //运价基础
    private String TourCode;    //旅行代码
    private int IntDiscount;    //国际票折扣率
    private String Discount;    //折扣率
    private String EI;        //限制条件
    private String Comment;    //备注项
    private String Baggage;    //行李重量
    private String ValidityPeriod;    //客票有效期(最长停留时间)
    private String MinStay;    //最短停留时间
    private Double PriceValue;    //票价
    private BigDecimal  PreferentialPrice;    //优惠价格  拥军优属
    private  BigDecimal  DiscountedPriceValue; //拥军优惠后价格
    private double discountPriceValue;    //票价优惠金额
    private Double RSP;    //销售参考价
    private Boolean RefundedFlag;    //是否可退票
    private String RefundedComment;    //退票政策描述  国际使用
    private String ChangedComment;    //变更政策描述 国际使用
    private Boolean RescheduledFlag;    //是否可免费改期
    private int FreeChangeTimes;    //免费改期次数
    private Boolean ChangeAirLineFlag;    //是否可改签
    private Boolean UpgradeFlag;    //是否可升舱
    private Double YPrice;    //经济舱公布运价全价金额
    private String FareID;    //运价编号
    private Double YQTax;    //燃油费
    private Double CNTax;    //建设税
    private String DynamicCabin;
    private String DynamicFareID;
    private String CombineId;
    private String FareSign;    //运价验证串
    private CombineRule CombineRuleInfo;    //组合限制条件
    private ScoreUse[] ScoreUseInfoList;    //积分使用信息
    private ScoreGift ScoreGiftInfo;    //积分赠送信息
    //2018-03-29 16:55:13新增  国内使用
    private Double RefundFee;        //起飞前两小时前退票费
    private Double RefundFeeAfter;    //起飞前两小时后退票费
    private Double ChangeFee;        //起飞前两小时前变更费
    private Double ChangeFeeAfter;    //起飞前两小时后变更费
    private int InsurenceAmount;//绑定保险最低金额
    private Boolean ScoreAvailable;/// 是否可用积分
    private Boolean CouponAvailable;/// 是否可用优惠券
    //退改规则列表
    private List<PtRefundRuleInfo> RefundedRules; //退票规则信息  RefundedFlag为false时该字段信息为空
    private List<PtChangeRuleInfo> ChangeRules; //更改规则信息 UpgradeFlag为false并且RescheduledFlag为true时该字段信息为空
    //2019-07-12新增
    private String CabinComb;//舱位组合信息
    private String priceValueComb;//组合票价  往返/隔开
    private String PriceValueCombDiff;
    private Double TotalTax;//总税费，国际有
    private FareTaxInfo TaxInfo; //税费汇总  国际有，国际税cn、yq、q、other税
    private List<InternatTaxInfo> OtherTaxList;//税费明细  国际税费，国际有
    //2019-09-04新增
    private String HandBaggeage;//手提行李 1件，23KG
    private String HandBaggeageRemark; // 手提行李详情  单件三边之和不超过115CM
    private String CheckBaggeage; // 托运行李 1件，23KG
    private String CheckBaggeageRemark; // 托运行李详情  单件三边之和不超过158CM
    private String SpecialRemark; // 特殊行李信息 可免费托运婴儿手推车一辆

    //以下为自定义字段
    private String showDisCount;//页面折扣显示
    private String activityAvailable;//是否活动提示
    private String personalizedDisplay;//个性化展示 特价经济 特惠经济。。。
    public FlightPriceComb flightPriceComb;//不是接口返回字段，是解析priceValueComb字段后用于摊开往返价格
    private Double CouponAmount; //使用优惠券
    //改期升舱手续费
    private double changeServiceCharge;

    /**
     * 原改期费
     */
    private BigDecimal originalChangeFee;

    /**
     * 改期政策备注
     */
    private String changeRemark;

    /**
     * 改期政策提示
     */
    private String rescheduleReminder;

    //改期升舱票面差价
    private double ticketPriceDiff;
    //改期升舱税费差价
    private double taxDiff;
    //税费金额
    private double xtax;
    //燃油差价
    private double yqTaxDiff;
    //机建差价
    private double cnTaxDiff;
    //改期升舱总差价   票面差价+税费差价+机建差价+燃油差价+手续费
    private double totalDiff;
    //婴儿免费行李额 兼容5.6之前的老版本
    private BaggerRule infFreeBaggage;
    private BaggerRule adtBaggage;//成人免费行李额5.6及之后的版本
    private BaggerRule chdBaggage;//儿童免费行李额5.6及之后的版本
    private int scoreGive;////赠送累积积分
    private int additionalScoreGive;//额外赠送积分
    private String cabinClassName;//舱位等级名称  公务舱(R-R/R-R)
    private String cabinCombClassName;//公务舱-公务舱/公务舱-公务舱
    private String cabinLabel;//舱位名称说明
    private List<ChangeAndRefundRule> changeRuleList; //改期规则
    private List<ChangeAndRefundRule> refundRuleList; //退票规则
    //儿童的退改说明
    private ChildRule childRule;
    //婴儿的退改说明
    private InfRule infRule;
    /**
     * 舱位类型
     *
     * @see com.juneyaoair.appenum.av.PackageTypeEnum
     */
    private String cabinType;
    //舱位往返标签 如：往返特惠
    private LabelInfo cabinLabelRound;
    //舱位标签 吉祥品牌运价
    private LabelInfo cabinLabelBrand;
    //舱位标签列表 最上层 //100元优惠券
    private List<LabelInfo> cabinLabelList;
    //舱位标签列表 最下层  如“手提行李+托运行李、餐食、快速出票”
    private List<LabelInfo> cabinLabelList2;
    //改签说明 不可自愿签转，非自愿签转请联系客服
    private String changeDesc;
    private String weightLimit;//重量上线
    private BrandPriceRule brandPriceRule; // 品牌运价细则
    @ApiModelProperty(value = "活动运价标签(展示在价格下)，如：学生特惠")
    private LabelAndRuleInfo activityLabel;
    @ApiModelProperty(value = "活动运价标签 如：LCPREFERENCE：无免费托运行李 ")
    private Map<String, LabelAndRuleInfo> activityLabelMap;
    private List<Fare> fares; // 子运价，国内中转或往返时使用
    private List<SegmentCabinInfo> segmentCabinInfos;// 子航线运价信息
    private List<PictureDto> advertisements;// 广告信息
    private String interFlag;// 国内国际标记，前端根据此参数判断行李额及退改规则样式
    private String flightNo;// 航班号，前端根据此参数判断展示样式
    private List<TravelPrivilege> travelPrivilegeList; //出行尊享
    @JsonIgnore
    private int sortPriority;// 排序优先级  10 成人畅飞卡  20 儿童畅飞卡 50高舱高返 100 其他舱位
    private List<InterBrandRightInfo> interBrandRightInfos; //2021-05-10 国际品牌运价的权益列表 spa航班支持
    private List<LabelInfo> cabinLabelInterBrand;
    private String brandCode; //2021-05-12 国际品牌运价的号
    //2021-07-09 品牌运价积分返回配置
    private Map<String, Double> scaleScoreList;
    //运价产品类型 2021-08-05
    private String priceProductSign;

    private String SpecialFareCode;

    //wifi产品信息
    private List<TravelPrivilege> wifiTravelPrivilege;
    /**
     * 主题卡类型
     */
    private String themeCardType;
    /**
     * 主题卡名称
     */
    private String themeCardName;

    private String flightFareType;

    @ApiModelProperty(value = "规则标签 ShippingRulesLabelEnum")
    private Set<String> shippingRulesLabel;

    @ApiModelProperty(value = "前端BUG兼容处理无实际价值不使用 航线类型 I:国际 D:国内 R:地区（港澳台）")
    private String segmentType;

    /**
     *九元运价需要
     */
    private String ProductNo;

    /**
     * 退改费高低
     */
    private String returnFeeDesc;

    private Boolean  alternateButton; //候补按钮

    private  List<TravelPrivilege> privilegeList;

    @Override
    public String toString() {
        return BaseStringUtil.toJson(this);
    }

    public void setThemeCabinInfo(String code, Map<String, ThemeModel> map) {
        String themeCabinName = "免票兑换专享舱位";
        String themeName = "吉祥套卡产品";
        if (map == null) {
            cabinLabel = themeCabinName;
            themeCardName = themeName;
        } else {
            ThemeModel themeModel = map.get(code);
            if (themeModel == null) {
                cabinLabel = themeCabinName;
                themeCardName = themeName;
            } else {
                cabinLabel = themeModel.getRemark();
                themeCardName = themeModel.getThemeName();
            }
        }
    }

    public void putActivityLabelMap(String key, LabelAndRuleInfo labelAndRuleInfo){
        if (null == activityLabelMap){
            activityLabelMap = new HashMap<>();
        }
        activityLabelMap.put(key, labelAndRuleInfo);
    }
}

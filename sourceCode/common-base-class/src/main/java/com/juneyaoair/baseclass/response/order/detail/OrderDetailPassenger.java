package com.juneyaoair.baseclass.response.order.detail;

import com.juneyaoair.appenum.member.CertificateTypeEnum;
import com.juneyaoair.appenum.order.PassengerTypeEnum;
import com.juneyaoair.baseclass.response.order.comm.TaxInfo;
import com.juneyaoair.baseclass.response.order.query.InsuranceInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * 订单详情
 * <AUTHOR>
 * @Description
 * @create 2020-07-01 14:11
 */
@Data
@NoArgsConstructor
public class OrderDetailPassenger {

    /**
     * 旅客姓名
     */
    private String passengerName;

    /**
     * 旅客类型
     * @see PassengerTypeEnum#getPassType()
     */
    private String passengerType;


    private String passengerIdentity; //乘客身份VIP，教师，警殘，革伤

    /**
     * 证件类型
     * @see CertificateTypeEnum#getShowCode()
     */
    private String certType;

    /**
     * 证件号
     */
    private String certNo;

    /**
     * 客票号码
     */
    private List<String> ticketNos;

    /**
     * 前端显示的乘客退票状态标签
     * @see com.juneyaoair.appenum.order.RefundLabelEnum
     */
    private String refundLabel;

    private double InsuranceAmount; //保险金额保险金额 = 份数 * 单价 * 航段数

    private List<InsuranceInfo> InsuranceList;

    private double parPrice;

    private double YQTax;

    private double CNTax;

    private double otherTax;

    private double QFee;

    private double upgradeFee;

    /** 是否使用儿童免票券 */
    private String UseFreePass;
    /** 儿童免票券券号 */
    private String UseFreePassCouponNo;
    //儿童票号
    private String birthdate;
    /**
     * 快速退单标识
     */
    private Boolean isSpeedRefund;
    //退单编号
    private String  refundNo;

    private BigDecimal originalChangeFee;
    @ApiModelProperty(value = "税费信息列表")
    private List<TaxInfo> TaxInfoList;
}

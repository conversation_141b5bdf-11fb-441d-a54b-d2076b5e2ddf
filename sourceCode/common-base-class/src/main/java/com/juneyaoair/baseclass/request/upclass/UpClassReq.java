package com.juneyaoair.baseclass.request.upclass;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.NotBlank;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * Created by yaocf on 2018/1/10.
 * 升舱查询请求
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@XmlRootElement(name = "UpClassReq")
@XmlAccessorType(XmlAccessType.FIELD)
public class UpClassReq {
    @NotBlank(message="渠道用户号不能为空")
    private String channelCode; //渠道用户号	B2C,CC等
    @NotBlank(message="常客ID不能为空")
    private String ffpId; //预订人常客ID
    @NotBlank(message="卡号不能为空")
    private String ffpCardNo; //预订人常客卡号HO+卡号
    @NotBlank(message="验证信息不能为空")
    private String loginKeyInfo;
    @NotBlank(message="证件号不能为空")//包含票号和身份证号
    private String ticketNo;
    private String ip;
    private String ipToken;
}

package com.juneyaoair.baseclass.response.order.refund.query;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.juneyaoair.baseclass.response.order.query.OrderTotalBriefInfo;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

/**
 * Created by qinxiaoming on 2016-5-30.
 */

@XmlRootElement(name = "TotalRefundBriefResp")
@XmlAccessorType(XmlAccessType.FIELD)
public class TotalRefundBriefResp {
 
    private String ResultCode;//结果代码
 
    private String ErrorInfo;//错误信息
 
    private List<RefundTotalBriefInfo> TotalRefundBriefInfoList;
 
    private int PageNo;
 
    private int PageSize;
 
    private int PageCount;
 
    private String Version;
 
    private String ChannelCode;
 
    private String UserNo;

    public TotalRefundBriefResp() {
    }

    public String getResultCode() {
        return ResultCode;
    }

    public void setResultCode(String resultCode) {
        ResultCode = resultCode;
    }

    public String getErrorInfo() {
        return ErrorInfo;
    }

    public void setErrorInfo(String errorInfo) {
        ErrorInfo = errorInfo;
    }

    public List<RefundTotalBriefInfo> getTotalRefundBriefInfoList() {
        return TotalRefundBriefInfoList;
    }

    public void setTotalRefundBriefInfoList(List<RefundTotalBriefInfo> totalRefundBriefInfoList) {
        TotalRefundBriefInfoList = totalRefundBriefInfoList;
    }

    public int getPageNo() {
        return PageNo;
    }

    public void setPageNo(int pageNo) {
        PageNo = pageNo;
    }

    public int getPageSize() {
        return PageSize;
    }

    public void setPageSize(int pageSize) {
        PageSize = pageSize;
    }

    public int getPageCount() {
        return PageCount;
    }

    public void setPageCount(int pageCount) {
        PageCount = pageCount;
    }

    public String getVersion() {
        return Version;
    }

    public void setVersion(String version) {
        Version = version;
    }

    public String getChannelCode() {
        return ChannelCode;
    }

    public void setChannelCode(String channelCode) {
        ChannelCode = channelCode;
    }

    public String getUserNo() {
        return UserNo;
    }

    public void setUserNo(String userNo) {
        UserNo = userNo;
    }
}

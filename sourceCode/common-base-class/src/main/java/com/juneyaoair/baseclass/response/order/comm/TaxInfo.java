package com.juneyaoair.baseclass.response.order.comm;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class TaxInfo {
	@ApiModelProperty(value = "税费代码")
	private String TaxCode;
	@ApiModelProperty(value = "税费名称")
	private String TaxName;
	@ApiModelProperty(value = "币种")
	private String Currency;
	@ApiModelProperty(value = "税费金额")
	private Double TaxAmount;
	@ApiModelProperty(value = "税费后缀信息")
	private String TaxSuffix;
	@ApiModelProperty(value = "税费顺序号")
	private int TaxSeq;

	public String getTaxCode(){
		return TaxCode;
	}
	public void setTaxCode(String TaxCode){
		this.TaxCode=TaxCode;
	}
	public String getCurrency(){
		return Currency;
	}
	public void setCurrency(String Currency){
		this.Currency=Currency;
	}
	public Double getTaxAmount(){
		return TaxAmount;
	}
	public void setTaxAmount(Double TaxAmount){
		this.TaxAmount=TaxAmount;
	}
	public String getTaxSuffix(){
		return TaxSuffix;
	}
	public void setTaxSuffix(String TaxSuffix){
		this.TaxSuffix=TaxSuffix;
	}
	public int getTaxSeq(){
		return TaxSeq;
	}
	public void setTaxSeq(int TaxSeq){
		this.TaxSeq=TaxSeq;
	}

	public String getTaxName() {
		return TaxName;
	}

	public void setTaxName(String taxName) {
		TaxName = taxName;
	}

	@Override
	public String toString() {
		StringBuffer sb = new StringBuffer(); 
		sb.append("TaxInfo ["); 
		sb.append("TaxCode="+TaxCode+",");
		sb.append("Currency="+Currency+",");
		sb.append("TaxAmount="+TaxAmount+",");
		sb.append("TaxSuffix="+TaxSuffix+",");
		sb.append("TaxSeq="+TaxSeq);
		sb.append("]");
		return sb.toString();
	}
}
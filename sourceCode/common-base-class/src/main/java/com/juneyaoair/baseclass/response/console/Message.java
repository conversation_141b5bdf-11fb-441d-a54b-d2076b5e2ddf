package com.juneyaoair.baseclass.response.console;

import com.juneyaoair.baseclass.request.console.FileInfo;
import com.juneyaoair.weixin.common.pageReq;

import java.sql.Date;
import java.util.List;

/**
 * Created by lzg on 2016-05-17.
 */
public class Message  extends pageReq {
    private String messageId;
    private String messageTitle;
    private String messageAuth;
    private String messageContent;
    private String messagePlainTxt;
    private Date messagePublishTime;
    private String messageType;
    private String messageTypeCode;
//    private String messageRank;
    private String messagePublishMan;
    private String messagePicUrl;
    private String messageStatus;
    private String messageSendOrNot;
    private String messageCreateTime;
    private String messageCreateTimeStr;
    private String messageStartTime;
    private String messageEndTime;
    private String messageUpdateTime;
    private String messageUpdateMan;
    private String messageUrl;
    private String messageInfoType;
    private String messageInfoTypeCode;
    //保存時文件的ID
    private String[] fileID;
    private List<FileInfo> fileInfos;
    private List<String> channel;
    private String channels;

    public String getMessageId() {
        return messageId;
    }

    public void setMessageId(String messageId) {
        this.messageId = messageId;
    }

    public String getMessageTitle() {
        return messageTitle;
    }

    public void setMessageTitle(String messageTitle) {
        this.messageTitle = messageTitle;
    }

    public String getMessageAuth() {
        return messageAuth;
    }

    public void setMessageAuth(String messageAuth) {
        this.messageAuth = messageAuth;
    }

    public String getMessageContent() {
        return messageContent;
    }

    public void setMessageContent(String messageContent) {
        this.messageContent = messageContent;
    }

    public Date getMessagePublishTime() {
        return messagePublishTime;
    }

    public void setMessagePublishTime(Date messagePublishTime) {
        this.messagePublishTime = messagePublishTime;
    }

    public String getMessageType() {
        return messageType;
    }

    public void setMessageType(String messageType) {
        this.messageType = messageType;
    }

//    public String getMessageRank() {
//        return messageRank;
//    }
//
//    public void setMessageRank(String messageRank) {
//        this.messageRank = messageRank;
//    }

    public String getMessagePublishMan() {
        return messagePublishMan;
    }

    public void setMessagePublishMan(String messagePublishMan) {
        this.messagePublishMan = messagePublishMan;
    }

    public String getMessagePicUrl() {
        return messagePicUrl;
    }

    public void setMessagePicUrl(String messagePicUrl) {
        this.messagePicUrl = messagePicUrl;
    }

    public String getMessageStatus() {
        return messageStatus;
    }

    public void setMessageStatus(String messageStatus) {
        this.messageStatus = messageStatus;
    }

    public String getMessageUpdateMan() {
        return messageUpdateMan;
    }

    public void setMessageUpdateMan(String messageUpdateMan) {
        this.messageUpdateMan = messageUpdateMan;
    }

    public String getMessageUrl() {
        return messageUrl;
    }

    public void setMessageUrl(String messageUrl) {
        this.messageUrl = messageUrl;
    }

    public String[] getFileID() {
        return fileID;
    }

    public void setFileID(String[] fileID) {
        this.fileID = fileID;
    }

    public List<FileInfo> getFileInfos() {
        return fileInfos;
    }

    public void setFileInfos(List<FileInfo> fileInfos) {
        this.fileInfos = fileInfos;
    }

    public List<String> getChannel() {
        return channel;
    }

    public void setChannel(List<String> channel) {
        this.channel = channel;
    }

    public String getChannels() {
        return channels;
    }

    public void setChannels(String channels) {
        this.channels = channels;
    }

    public String getMessageInfoType() {
        return messageInfoType;
    }

    public void setMessageInfoType(String messageInfoType) {
        this.messageInfoType = messageInfoType;
    }

    public String getMessageSendOrNot() {
        return messageSendOrNot;
    }

    public void setMessageSendOrNot(String messageSendOrNot) {
        this.messageSendOrNot = messageSendOrNot;
    }

    public String getMessageCreateTime() {
        return messageCreateTime;
    }

    public void setMessageCreateTime(String messageCreateTime) {
        this.messageCreateTime = messageCreateTime;
    }

    public String getMessageStartTime() {
        return messageStartTime;
    }

    public void setMessageStartTime(String messageStartTime) {
        this.messageStartTime = messageStartTime;
    }

    public String getMessageEndTime() {
        return messageEndTime;
    }

    public void setMessageEndTime(String messageEndTime) {
        this.messageEndTime = messageEndTime;
    }

    public String getMessageUpdateTime() {
        return messageUpdateTime;
    }

    public void setMessageUpdateTime(String messageUpdateTime) {
        this.messageUpdateTime = messageUpdateTime;
    }

    public String getMessageTypeCode() {
        return messageTypeCode;
    }

    public void setMessageTypeCode(String messageTypeCode) {
        this.messageTypeCode = messageTypeCode;
    }

    public String getMessageInfoTypeCode() {
        return messageInfoTypeCode;
    }

    public void setMessageInfoTypeCode(String messageInfoTypeCode) {
        this.messageInfoTypeCode = messageInfoTypeCode;
    }

    public String getMessageCreateTimeStr() {
        return messageCreateTimeStr;
    }

    public void setMessageCreateTimeStr(String messageCreateTimeStr) {
        this.messageCreateTimeStr = messageCreateTimeStr;
    }

    public String getMessagePlainTxt() {
        return messagePlainTxt;
    }

    public void setMessagePlainTxt(String messagePlainTxt) {
        this.messagePlainTxt = messagePlainTxt;
    }
}

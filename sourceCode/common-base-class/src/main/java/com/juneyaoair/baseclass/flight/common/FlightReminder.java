package com.juneyaoair.baseclass.flight.common;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @description 航班提醒通知
 * @date 2020/2/13  15:07.
 */
@Data
@JsonIgnoreProperties({"flightStartDate","flightEndDate","suitRoute"})
@JsonInclude(JsonInclude.Include.NON_NULL)
@Accessors(chain = true)
public class FlightReminder {
    private String title;
    private String reminderContent;//提醒内容
    private String flightStartDate;//航班开始时间
    private String flightEndDate;//航班展示时间
    private List<String> suitRoute;//适用航线
}

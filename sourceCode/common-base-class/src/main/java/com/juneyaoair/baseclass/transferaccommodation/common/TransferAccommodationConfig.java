package com.juneyaoair.baseclass.transferaccommodation.common;

import lombok.Data;

/**
 * @ClassName TransferAccommodationConfig
 * @Description 中转住宿可申请航班配置
 * <AUTHOR>
 * @Date 2019/10/29 16:46
 **/
@Data
public class TransferAccommodationConfig {

    /**
     * 中转城市编码
     */
    private String midCityCode;

    /**
     * 到达城市编码
     */
    private String arrCityCode;

    /**
     * 最短换乘时长
     * 超过此阈值的换乘航班才能申请
     */
    private int minDurHour;

}

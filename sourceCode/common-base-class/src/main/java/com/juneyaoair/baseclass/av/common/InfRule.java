package com.juneyaoair.baseclass.av.common;

import com.juneyaoair.baseclass.change.ChangeAndRefundRule;
import com.juneyaoair.thirdentity.response.order.apply.OrderDetailBaggage;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2019/5/5  15:04.
 */
@Data
public class InfRule {
    private List<ChangeAndRefundRule> changeRuleList; //改期规则
    private List<ChangeAndRefundRule> refundRuleList; //退票规则
    private OrderDetailBaggage orderDetailBaggage;//行李额
}

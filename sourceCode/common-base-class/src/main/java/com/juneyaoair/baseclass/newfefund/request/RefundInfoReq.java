package com.juneyaoair.baseclass.newfefund.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Classname RefundInfoReq
 * @Date 2019/12/3 9:50
 * @Created by yzh
 */
@Data
public class RefundInfoReq {
    private String channelCode;
    private String channelOrderNo;
    private String orderNo;
    private String customNo;
    private String loginKeyInfo;
    @ApiModelProperty(value = "订单标识ID",notes = "此信息来源于订单详情接口")
    private String orderSign;
}

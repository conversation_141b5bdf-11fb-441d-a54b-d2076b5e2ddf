package com.juneyaoair.baseclass.activity.questionnaire.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * Created by zc on 2017/3/17.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@XmlRootElement(name = "QueryAdRequest")
@XmlAccessorType(XmlAccessType.FIELD)
public class QueryAdRequest {
    private String Version;
    @NotNull(message="渠道用户号不能为空")
    private String ChannelCode;
    private String UserNo;
    @NotNull(message="会员卡号不能为空")
    private String FfpCardNo;
}

package com.juneyaoair.baseclass.coupon.response.v3;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.juneyaoair.thirdentity.salecoupon.v2.common.ResourceBuyCouponEnjoy;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @ClassName CommonCouponProductInfo
 * <AUTHOR>
 * @Description 权益券产品信息（新产品平台）
 * @Date 2020-12-17 10:54
 **/
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CommonCouponProductInfo {
    /**
     * 活动ID
     */
    private String productNum;//所属活动编号，productId+bookingId+ruleId的组合ID
    /**
     * 产品ID
     */
    private String productId;
    /**
     * 投放ID
     */
    private String bookingId;
    /**
     * 使用规则ID
     */
    private String ruleId;
    /**
     * 产品类型
     */
    private String productType;
    /**
     * 产品名称
     */
    private String productName;
    /**
     * 产品销售价
     */
    private Double salePrice;
    /**
     * 标准售价
     */
    private Double standardPrice;
    /**
     * 产品sku
     */
    private String productSku;
    /**
     * 产品已售数量
     */
    private int saleTotalCount;
    /**
     * 券有效天数
     */
    private int validityDays;
    /**
     * 券指定日期 yyyy-MM-dd
     */
    private String latestDate;
    /**
     * 总的可购张数
     */
    private int bookingCount;
    /**
     * 剩余可购张数
     */
    private int remainedCount;
    /**
     * 产品验证
     */
    private String sign;
    /**
     * 资源ID
     */
    private String resourceId;
    /**
     * 标签列表
     */
    private List<String> tags;
    /**
     * 图片集合
     */
    private List<String> imgs;
    /**
     * 其他适用航线
     */
    private List<List<String>> otherApplyRoutes;
    /**
     * 描述
     */
    private String description;
    /**
     * 使用方式
     */
    private String useMode;
    /**
     * 购买须知
     */
    private String purchaseNotes;
    /**
     * 退款规则
     */
    private String refundRules;
    /**
     * 有效期开始时间  yyyy-MM-dd
     */
    private String startDate;
    /**
     * 有效期结束时间 yyyy-MM-dd
     */
    private String endDate;

    /**
     * 休息室特有属性，服务开始时间
     */
    private String serviceStartTime;
    /**
     * 休息室特有属性，服务结束时间
     */
    private String serviceEndTime;
    /**
     * 休息室特有属性，休息室位置
     */
    private String address;
    /**
     * 休息室特有属性，所在机场 （机场三字码）2021-02-26
     */
    private String airportLocation;
    /**
     * 休息室特有属性，所在机场航站楼
     */
    private String airportTerminal;
    /**
     * 2021-04-25 贵宾休息室 买券即享配置
     */
    private List<ResourceBuyCouponEnjoy> buyCouponEnjoyList;

    /**
     * 2021-6-18 升舱券推荐,true推荐
     */
    private Boolean isRecommend;
    /**
     * 2021-6-18 券的类型
     */
    private String couponType;

    /**
     * 航班起飞几小时前可用
     */
    private String advanceHour;



    private String beginDatetime;


    private String endDatetime;

    @ApiModelProperty(value = "活动名称")
    private String activityName;



    /**
     * 创建对应的签名字段
     * @return
     */
    public String createSignField(){
        return this.resourceId+this.productNum+this.productId+this.bookingId+this.ruleId+this.productSku+this.productType+this.salePrice;
    }
}

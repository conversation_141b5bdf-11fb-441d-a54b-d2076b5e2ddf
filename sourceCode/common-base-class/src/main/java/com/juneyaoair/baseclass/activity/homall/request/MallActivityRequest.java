package com.juneyaoair.baseclass.activity.homall.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class MallActivityRequest {
    @NotNull(message = "渠道用户号不能为空")
    private String channelCode; //渠道用户号	B2C,CC等
    @NotNull(message = "用户ID号不能为空")
    private String ffpId;
    private String ffpCardNo;
    @NotNull(message = "验证码不能为空")
    private String loginKeyInfo;
    private String activityId;
    private String winningId;
    private String receiveName;
    private String mobile;
    private String address;
    private Long productId;
    private Long fashId;
    private String memberId;
}

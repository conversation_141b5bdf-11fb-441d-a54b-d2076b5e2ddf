package com.juneyaoair.baseclass.response.coupons;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName ChangeAvailableCoupon
 * @Description 可用改期券查询结果
 * <AUTHOR>
 * @Date 2019/12/10 14:49
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ChangeAvailableCoupon extends AvailCoupon {

    /**
     * 使用人和航段
     */
    List<UsePassengerSegment> usePassengerSegments;

}

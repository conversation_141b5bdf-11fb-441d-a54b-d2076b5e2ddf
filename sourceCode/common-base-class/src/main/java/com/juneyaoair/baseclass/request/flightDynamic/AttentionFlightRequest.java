package com.juneyaoair.baseclass.request.flightDynamic;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.NotBlank;

/**
 * @program: mobileAPI
 * @description
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2019-07-17 18:31
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AttentionFlightRequest {
    private String flightNo;
    private String flightDate;
    private String departureAirport;
    private String arrivalAirport;
    private String departureCity;
    private String arrivalCity;
    /**
     * 1为航班号查询，2为出发城市到达城市查询
     **/
    private String queryType;
    /**
     *会员ID
     */
    @NotBlank(message = "会员id不能为空")
    private String ffpId;
    @ApiModelProperty(value = "操作人会员卡号")
    private String ffpCardNo;
}

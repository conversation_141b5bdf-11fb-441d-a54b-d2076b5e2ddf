package com.juneyaoair.baseclass.onBoardWifi.resp;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Author:zc
 * @Description:
 * @Date:Created in 12:57 2019/4/4
 * @Modified by:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OnBoardWifiFlightInfoResponse {
    private String resultCode;
    private String errorInfo;
    private List<OnBoardWifiFlightInfo> infos;

}

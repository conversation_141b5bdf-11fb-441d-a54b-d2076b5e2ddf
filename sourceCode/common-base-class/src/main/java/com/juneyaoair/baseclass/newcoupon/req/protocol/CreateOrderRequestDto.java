package com.juneyaoair.baseclass.newcoupon.req.protocol;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName CreateOrderRequestDto
 * @Description 创建礼宾产品订单请求对象
 * <AUTHOR>
 * @Date 2020/5/26 15:57
 **/
@NoArgsConstructor
@Data
@AllArgsConstructor
public class CreateOrderRequestDto {
    /**
     * 退款规则
     */
    private String RefundRule;
    /**
     * 产品名
     */
    private String ProductName;
    /**
     * 常旅客ID
     */
    private String FfpId;
    /**
     * 常旅客ID卡号
     */
    private String FfpCardNo;
    /**
     * 客户姓名
     */
    private String PassengerName;
    /**
     * 证件类型
     */
    private String CertType;
    /**
     * 证件号
     */
    private String CertNo;
    /**
     * 手机号
     */
    private String PhoneNo;
    /**
     * 手机归属国区号  86 (前面不带+)
     */
    private String PhoneCountryCode;
    /**
     * 渠道订单号
     */
    private String ChannelOrderNo;
    /**
     * 订单金额
     */
    private String TotalAmount;
    /**
     * 票号
     */
    private String TktNo;
    /**
     * PNR信息
     */
    private String PnrNo;
    /**
     * 使用积分
     */
    private String UseScore;
    /**
     * 币种
     */
    private String Currency;
    /**
     * 出发机场三字码
     */
    private String DepAirportCode;
    /**
     * 到达机场三字码
     */
    private String ArrAirportCode;
    /**
     * 出发机场航站楼
     */
    private String DepAirportTerminal;
    /**
     * 到达机场航站楼
     */
    private String ArrAirportTerminal;
    /**
     * 航班号
     */
    private String FlightNo;
    /**
     * 航班日期
     */
    private String FlightDate;

    /**
     * 到达时间
     */
    private String ArrTime;

    /**
     * 出发时间
     */
    private String DepTime;
    /**
     * 飞机类型
     */
    private String PlaneType;
    /**
     * 仓位
     */
    private String Cabin;
    /**
     * p-接机,c-送机
     */
    private String ServiceType;
    /**
     * 是否包含专车服务 y-是,n-否
     */
    private String CarService;
    /**
     * 用车时间,使用专车必填
     */
    private String UseCarTime;
    /**
     * 预计下车时间,送机必填
     */
    private String FinishedTime;
    /**
     * 出发地详址, 送机必填
     */
    private String StartAddress;
    /**
     * 出发地纬度,送机必填
     */
    private String StartLat;
    /**
     * 出发地经度,送机必填
     */
    private String StartLng;

    /**
     * 	产品信息
     */
    private BookProductInfo[] BookProductInfo;

    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public class BookProductInfo{
        private String ProductNum;//ProductId+BookingId+RuleId 组合ID
        /**
         * 产品编号
         */
        private String ProductId;
        /**
         * 投放规则编号
         */
        private String BookingId;
        /**
         * 适用规则编号
         */
        private String RuleId;
        /**
         * 资源类型
         */
        private String ResourceType;
        /**
         * 标准售价
         */
        private String StandardPrice;
        /**
         * 销售价
         */
        private String SalePrice;
        /**
         * 购买数量
         */
        private String BookingCount;

    }

}

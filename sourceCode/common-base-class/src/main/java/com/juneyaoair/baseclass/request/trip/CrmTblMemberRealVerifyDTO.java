/*
 * Copyright ®2018 juneyaoair Group.
 *
 *
 *
 */
package com.juneyaoair.baseclass.request.trip;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 
 * 我的行程 bean
 * 
 * <AUTHOR>
 * @date 2018年3月5日 下午3:58:58
 * @version 1.0
 */
public class CrmTblMemberRealVerifyDTO {
	// 记录id
	private int recordId;
	// 会员id
	@NotNull(message = "会员ID不能为空")
	private int sid;
	// 消费密码
	private String password;
	// 验证状态 -1认证拒绝， 0待审核，1认证成功，
	@NotNull(message = "验证状态不能为空")
	private String status;
	// 认证日期
	@NotNull(message = "认证日期不能为空")
	private Date verifyDate;
	// 认证渠道
	private String verifyChannel;
	// 操作员
	@NotNull(message = "操作员不能为空")
	private String operateUserId;
	// 操作日期
	@NotNull(message = "操作日期不能为空")
	private Date operateDate;
	// 修改员ID
	private String updateUserId;
	// 修改日期
	private Date updateDate;

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getVerifyChannel() {
		return verifyChannel;
	}

	public void setVerifyChannel(String verifyChannel) {
		this.verifyChannel = verifyChannel;
	}

	public String getOperateUserId() {
		return operateUserId;
	}

	public void setOperateUserId(String operateUserId) {
		this.operateUserId = operateUserId;
	}

	public String getUpdateUserId() {
		return updateUserId;
	}

	public void setUpdateUserId(String updateUserId) {
		this.updateUserId = updateUserId;
	}

	public int getRecordId() {
		return recordId;
	}

	public void setRecordId(int recordId) {
		this.recordId = recordId;
	}

	public int getSid() {
		return sid;
	}

	public void setSid(int sid) {
		this.sid = sid;
	}

	public Date getOperateDate() {
		return operateDate;
	}

	public void setOperateDate(Date operateDate) {
		this.operateDate = operateDate;
	}

	public Date getUpdateDate() {
		return updateDate;
	}

	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}

	public Date getVerifyDate() {
		return verifyDate;
	}

	public void setVerifyDate(Date verifyDate) {
		this.verifyDate = verifyDate;
	}
}

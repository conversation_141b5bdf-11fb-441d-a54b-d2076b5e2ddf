package com.juneyaoair.baseclass.request.booking;

import com.juneyaoair.baseclass.response.insure.PriceRule;
import lombok.Data;

import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAccessType;
import java.util.List;

@XmlRootElement(name = "InsuranceInfo")
@XmlAccessorType(XmlAccessType.FIELD)
@Data
public class InsuranceInfo {
	private String insuranceCode; //保险种类代码
	private int insuranceNumber; //保险份数
	private double insuranceAmount; //保险金额保险金额 = 份数 * 单价 * 航段数
	private String insuranceSpec; //保险规格名称
	private List<PriceRule> priceRule;//价格规则，IsFlexiblePrice为Y时启用
}
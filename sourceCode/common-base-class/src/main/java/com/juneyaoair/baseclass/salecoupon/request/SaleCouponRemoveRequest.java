package com.juneyaoair.baseclass.salecoupon.request;

import com.juneyaoair.baseclass.common.base.UserInfoMust;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.NotBlank;

/**
 * <AUTHOR>
 * @description 权益订单删除请求
 * @date 2018/8/17  18:13.
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class SaleCouponRemoveRequest extends UserInfoMust {
    @NotBlank(message="订单编号不能为空")
    private String orderNo;
    @NotBlank(message = "渠道订单编号不能为空")
    private String channelOrderNo;
}

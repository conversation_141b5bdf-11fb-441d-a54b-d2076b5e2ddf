package com.juneyaoair.baseclass.request.crm;

import javax.validation.constraints.NotNull;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * Created by <PERSON><PERSON><PERSON> on 2017/5/11.
 */
@XmlRootElement(name = "VerifyCodeReq")
@XmlAccessorType(XmlAccessType.FIELD)
public class VerifyCodeReq {
    private String channelCode;
    @NotNull(message = "手机号码 不能为空")
    private String mobiles;
    @NotNull(message = "短信类型 不能为空")
    private String type;
    // 验证码
    @NotNull(message = "短信验证码 不能为空")
    private String verifyCode;

    public String getChannelCode() {
        return channelCode;
    }

    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }

    public String getMobiles() {
        return mobiles;
    }

    public void setMobiles(String mobiles) {
        this.mobiles = mobiles;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getVerifyCode() {
        return verifyCode;
    }

    public void setVerifyCode(String verifyCode) {
        this.verifyCode = verifyCode;
    }
}

package com.juneyaoair.baseclass.captcha;

import com.juneyaoair.baseclass.member.request.Captcha;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 发送验证码时间间隔控制类
 * redis持久化对象
 * <AUTHOR>
 * @Description
 * @create 2020-11-02 10:43
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CaptchaInterval extends Captcha {

    /**
     * 最近一次访问时间
     */
    private Date lastVisitDate;

    /**
     * 访问次数
     */
    private int visitTimes;

    /**
     * 上次发送验证码时间
     */
    private Date lastSendDate;

    /**
     * 成功发送验证码次数
     */
    private int sendCaptchaTimes;

    /**
     * 当前计算得出的时间间隔
     * 单位：分钟
     * 下次可发送验证码时间 = lastVisitTime + currentInterval
     */
    private int currentInterval;
}

package com.juneyaoair.baseclass.request.ibe;

import com.juneyaoair.baseclass.response.ibe.TaxInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author:zc
 * @Description:
 * @Date:Created in 10:11 2018/5/30
 * @Modified by:
 */
@Data
public class FlightItineraryInfo {
    @ApiModelProperty(value = "货币类型")
    private String currencyType;
    @ApiModelProperty(value = "票面总价的货币类型")
    private String currencyTypeTotal;
    private String ticketNo;//票号
    private int eticketType;//电子客票类型
    private String issueAirline;//出票航空公司
    private String issueCity;//出票城市
    @ApiModelProperty(value = "出票日期")
    private String issueDate;
    private String dstCity;//终点城市
    private String orgCity;//始发城市
    private String passengerName;//旅客姓名
    private int passengerType;//旅客类型
    @ApiModelProperty(value = "支付方式")
    private String payMethod;
    @ApiModelProperty(value = "票价")
    private Double fare;
    @ApiModelProperty(value = "客票总金额")
    private Double totalAmount;
    @ApiModelProperty(value = "航协记录编号")
    private String iataNo;
    @ApiModelProperty(value = "证件类型")
    private String idType;
    @ApiModelProperty(value = "证件号码")
    private String idNo;
    private  String email;//邮箱
    @ApiModelProperty(value = "签注信息")
    private  String signingInfo;
    @ApiModelProperty(value = "旅行代码")
    private String tourCode;
    @ApiModelProperty(value = "连续票号")
    private String followTicketNo;
    @ApiModelProperty(value = "航段数")
    private int segmentCount;
    @ApiModelProperty(value = "税费数量")
    private int taxLength;
    @ApiModelProperty(value = "税款总金额")
    private double tax;
    @ApiModelProperty(value = "IT客票标记")
    private boolean iT;
    @ApiModelProperty(value = "行李件数")
    private int baggagePiece;
    @ApiModelProperty(value = "行李重量")
    private int baggageWeight;
    @ApiModelProperty(value = "行李重量单位")
    private String baggageWeightUnit;
    private List<DetrSegmentDetail> detrSegmentDetailList;
    @ApiModelProperty(value = "转换后的税费列表")
    private List<TaxInfo> taxInfoList;
}

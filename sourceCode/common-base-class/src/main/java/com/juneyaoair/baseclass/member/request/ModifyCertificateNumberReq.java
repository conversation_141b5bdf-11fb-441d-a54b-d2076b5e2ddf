package com.juneyaoair.baseclass.member.request;

import com.juneyaoair.baseclass.common.base.UserInfoNoMust;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> by jiang<PERSON><PERSON>
 * @date 2019/3/13 13:09
 */
@Data
@NoArgsConstructor
public class ModifyCertificateNumberReq extends UserInfoNoMust{
    @NotNull(message = "身份证件信息不能为空")
    private CertificateInfo certificateInfo;
    private CustomerInfo customerInfo;
    private String token;
}

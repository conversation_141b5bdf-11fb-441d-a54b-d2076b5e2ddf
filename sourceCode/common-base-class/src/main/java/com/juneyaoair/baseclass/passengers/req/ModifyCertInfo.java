package com.juneyaoair.baseclass.passengers.req;

import com.juneyaoair.baseclass.common.base.UserInfoMust;
import com.juneyaoair.thirdentity.passengers.common.GeneralContactCertInfo;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @description
 * @date 2019/8/7  16:58.
 */
@Data
public class ModifyCertInfo {
    /**
     * 用户信息
     */
    @NotNull(message = "用户信息不能为空")
    private UserInfoMust userInfo;
    /**
     * 证件信息
     */
    @NotNull(message = "证件信息不能为空")
    private GeneralContactCertInfo generalContactCertInfo;
    private Boolean isRemove;//是否删除
}

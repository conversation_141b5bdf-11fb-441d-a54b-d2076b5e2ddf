package com.juneyaoair.baseclass.specialservice.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Created by guanshiyin on 2018/11/29.
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class ResultInfo {
    /**
     *审核状态:1.预约成功、2.申请成功、3.受理中、4.受理失败、5.已取消
     */
    private String applyStatus;
    /**
     * true.操作成功  false.操作失败
     */
    private String success;
    /**
     * 返回表单id
     */
    private String id;
}

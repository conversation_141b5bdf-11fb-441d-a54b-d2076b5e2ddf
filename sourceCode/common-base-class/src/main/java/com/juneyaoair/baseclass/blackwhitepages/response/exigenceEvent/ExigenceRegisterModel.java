package com.juneyaoair.baseclass.blackwhitepages.response.exigenceEvent;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;
@AllArgsConstructor
@Data
@NoArgsConstructor
public class ExigenceRegisterModel {
    private Integer Id;
    private Integer ExigenceEventId;
    private String RegisterTitle;
    private String RegisterIntro;
    private String RegisterDescriptCh;
    private String RegisterDescriptEn;
    private String DelFlag;
    private String RegisterBdate;
    private String RegisterEdate;
    private String RegisterSwitch;
    private String RegisterTableTitleCh;
    private String RegisterTableTitleEn;
    private List<ExigenceRegisterQuestionModel> registerQuestionList;
}

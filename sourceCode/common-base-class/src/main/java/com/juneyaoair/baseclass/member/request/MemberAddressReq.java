package com.juneyaoair.baseclass.member.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 修改会员地址信息
 *
 * <AUTHOR>
 * @project mobile
 * @create 2018-12-12 13:13
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class MemberAddressReq {
    private String recoredID;
    private String postCode; // 邮政编码
    private String countryCode; // 国家代码
    private String provinceCode; // 省代码
    private String cityCode; // 市代码
    private String address; // 详细地址
    private boolean isNormal;//是否常用地址
    private String record;
    private String receiver;
    private String receiverMobile;
    private Integer addressType;
}

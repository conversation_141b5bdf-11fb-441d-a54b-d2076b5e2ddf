package com.juneyaoair.baseclass.request.airtransfer;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

/**
 * <AUTHOR>
 * @Description 接送机航班查询
 * @created 2024/3/21 9:32
 */
@Data
public class AirTransferFlightQuery {

    @NotBlank(message = "航班日期不能为空")
    @ApiModelProperty(value = "航班日期", required = true)
    private String flightDate;

    @ApiModelProperty(value = "航班号")
    private String flightNo;

    @ApiModelProperty(value = "出发城市三字码")
    private String depCity;

    @ApiModelProperty(value = "到达城市三字码")
    private String arrCity;
}

package com.juneyaoair.baseclass.electroniccompensation.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description
 * @create 2020-09-21 14:23
 */
@Data
public class ECompensationApplyRequest {

    private String id;  //业务id
    private String flightNo;//	航班号
    private String flightDate;//	航班日期
    private String isApproval;//	是否走审批 0否，1是
    private String relationship;//	旅客补偿关系
    /**
     * @see com.juneyaoair.appenum.ecompensate.CompensateReceiptWayEnum
     */
    private String compensateWay;//	补偿方式
    private String relevantProofPath;//	相关证明附件 逗号分隔
    private String receiptAccount;//	收款账号

    /**
     * 支付方式{1:支付宝,2:微信,3:银行卡}
     */
    private String payWay;

    /**
     * 补偿状态{0:申请,1:金额审批,2:旅客信息补充,3:资料审核,4:待补偿,5:已补偿,6:补偿失败}
     */
    private String compensationStatus;

    /**
     *  收款人姓名
     */
    private String receiptName;
    /**
     * 收款人证件类型
     */
    private String receiptIdcardType;
    /**
     * 收款人证件号
     */
    private String receiptIdcard;

    /**
     * 收款金额
     */
    private double receiptAmount;

    /**
     * 收款人电话号码
     */
    private String phoneNum;
    //收款人电话 由于新的国际电子支付 旅服平台改了入参字段 故有了下面这个新的收款人电话字段 为了兼容老的接口 上面的phoneNum字段不去除
    private String receiptPhone;
    /**
     * 收款人账号所属银行名称   银行卡收款专用
     */
    private String receiptAccountBank;

    /**
     * 认证code  支付宝支付的时候传给旅客服务网
     */
    private String authCode;

    /**
     * 微信支付的openId
     */
    private String openId;

    private String ffpId;

    /**
     *  是否代领{1:是,0否}
     */
    private String takeReceiveFlag;

    /**
     * 加密字符串，如果有值，说明旅服网不用校验isApproval字段
     */
    private String signature;

    //凭证附件地址
    private String credentialPath;

    @ApiModelProperty(value = "收款⼈邮箱")
    private String receiptEmail;

    @ApiModelProperty(value = "银⾏标识码 格式:ICBK")
    private String receiptSwift;

    @ApiModelProperty(value = "收款人住址")
    private String receiptAddress;

    //todo 收款账户 收款人银行卡对应的姓名
    @ApiModelProperty(value = "（银行卡）账户名称")
    private String receiptAccountName;

    @ApiModelProperty(value = "银行地址")
    private String receiptBankAddress;

}

package com.juneyaoair.baseclass.response.province;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2022/10/31
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CityThemeResp {
    @ApiModelProperty(value="主题名称",name="themeName")
    private String themeName;
    @ApiModelProperty(value="城市三字码",name="cityCodes")
    private String [] cityCodes;
}

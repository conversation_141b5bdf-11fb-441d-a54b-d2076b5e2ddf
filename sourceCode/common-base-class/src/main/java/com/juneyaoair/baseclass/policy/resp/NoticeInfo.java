package com.juneyaoair.baseclass.policy.resp;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName NoticeInfo
 * @Description 条款信息
 * <AUTHOR>
 * @Date 2019/9/3 14:39
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class NoticeInfo {
    private String ntInfoId;//条款id
    private String noticeId;//父模块id
    private String ntInfoName;//条款名称
    private String ntInfoUrl;//条款地址
    private String createTime;//创建时间
    private String modifyTime;//修改时间
    private String person;//修改人
    private String ntInfoCode;//条款序号
    private String ntInfoDescription;//条款描述
    private String canInfoShow;//是否展示
}

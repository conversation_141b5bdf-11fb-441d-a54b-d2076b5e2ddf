package com.juneyaoair.baseclass.av.common;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description 中转信息
 * @date 2019/7/15  11:41.
 */
@Data
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties({"preFlightInfo","nextFlightInfo"})
public class TransferInfo {
    private String transferType;//转机
    private String transferCityName;//中转城市
    /**
     * 中转机场三字码 此字段是前序航段的到达机场
     */
    private String transferAirPort;
    /**
     * 中转机场三字码 此字段是后续航段的出发机场
     */
    private String transferAirPortFrom;
    private long transferTime;//中转时长
    private String transferDesc;//中转描述 如不同机场转机
    private boolean sameAirport; // 是否是同机场转机
    private long crossDays; //跨天数
    /**
     * 上一程航班简要信息
     */
    private FlightSimpleInfo preFlightInfo;
    /**
     *国际转机流程图
     */
    private String interTransferProcessUrl;
    /**
     *国内转机流程图
     */
    private String transferProcessUrl;
    /**
     *转机流程页面
     */
    private String transferProcessPageUrl;
    /**
     * 转机流程文案
     */
    private String transferProcessDesc;

    /**
     * 下一程航班简要信息
     */
    private FlightSimpleInfo nextFlightInfo;

    public TransferInfo(String transferType) {
        this.transferType = transferType;
    }
}

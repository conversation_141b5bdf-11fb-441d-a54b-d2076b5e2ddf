package com.juneyaoair.webbaseclass.response.refund.order;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;

@XmlRootElement(name = "WebOrderRefundResp")
@XmlAccessorType(XmlAccessType.FIELD)
public class WebOrderRefundResp {
	private String version; //接口版本号10
	private String channelCode; //渠道用户号B2C,CC等
	private String userNo; //渠道用户人员号分配给渠道用户的工作人员号
	private int refundId; //退票信息ID
	private String refundApplyDatetime; //退票申请时间yyyy-MM-dd hh:mm:ss
	private Double refundableAmountSum; //应退金额合计
	private String resultCode; //结果代码1001 － 成功，其它失败
	private String errorInfo; //错误信息

	public String getVersion() {
		return version;
	}

	public void setVersion(String version) {
		this.version = version;
	}

	public String getChannelCode() {
		return channelCode;
	}

	public void setChannelCode(String channelCode) {
		this.channelCode = channelCode;
	}

	public String getUserNo() {
		return userNo;
	}

	public void setUserNo(String userNo) {
		this.userNo = userNo;
	}

	public int getRefundId() {
		return refundId;
	}

	public void setRefundId(int refundId) {
		this.refundId = refundId;
	}

	public String getRefundApplyDatetime() {
		return refundApplyDatetime;
	}

	public void setRefundApplyDatetime(String refundApplyDatetime) {
		this.refundApplyDatetime = refundApplyDatetime;
	}

	public Double getRefundableAmountSum() {
		return refundableAmountSum;
	}

	public void setRefundableAmountSum(Double refundableAmountSum) {
		this.refundableAmountSum = refundableAmountSum;
	}

	public String getResultCode() {
		return resultCode;
	}

	public void setResultCode(String resultCode) {
		this.resultCode = resultCode;
	}

	public String getErrorInfo() {
		return errorInfo;
	}

	public void setErrorInfo(String errorInfo) {
		this.errorInfo = errorInfo;
	}
}
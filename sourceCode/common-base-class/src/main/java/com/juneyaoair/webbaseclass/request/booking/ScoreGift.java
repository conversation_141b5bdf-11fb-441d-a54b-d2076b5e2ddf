package com.juneyaoair.webbaseclass.request.booking;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * Created by yaocf on 2016/12/20.
 */
@XmlRootElement(name = "ScoreGift")
@XmlAccessorType(XmlAccessType.FIELD)
public class ScoreGift {
    private String scoreGiftRuleID; //赠送规则ID
    private int giftPercentage; //赠送比例计算结果进行四舍五入
    private int giftScore; //赠送积分"在航班查询时为0，赠送需要根据实际支付价来计算，如果使用了积分需要扣除积分抵扣金额后再计算，
    private String remark; //赠送说明

    public String getScoreGiftRuleID() {
        return scoreGiftRuleID;
    }

    public void setScoreGiftRuleID(String scoreGiftRuleID) {
        this.scoreGiftRuleID = scoreGiftRuleID;
    }

    public int getGiftPercentage() {
        return giftPercentage;
    }

    public void setGiftPercentage(int giftPercentage) {
        this.giftPercentage = giftPercentage;
    }

    public int getGiftScore() {
        return giftScore;
    }

    public void setGiftScore(int giftScore) {
        this.giftScore = giftScore;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}

package com.juneyaoair.webbaseclass.request.insure;

import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;
import java.util.List;

/**
 * Created by yaocf on 2016/12/19.
 */
@XmlRootElement(name = "QueryFlightInsureReq_M")
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(propOrder = {"channelCode","flightType","tripType","insureSegList"})
@AllArgsConstructor
@NoArgsConstructor
public class QueryFlightInsureReq_M {
    @NotNull(message="渠道用户号不能为空")
    private String channelCode; //渠道用户号	B2C,CC等
    @NotNull(message="航班类型 不能为空")
    @Pattern(regexp="[OW|RT]{2}", message = "航班类型只能为单程：OW；往返：RT！")
    private String flightType;	//航班类型: 单程：OW；往返：RT
    @NotNull(message="国内国际标识 不能为空")
    @Pattern(regexp="[DI]{1}", message = "国内国际标识只能为国内：D；国际：I！")
    private String tripType;	//旅行类别: D-国内,I-国际
    private String isSingleBuy;//是否单独购保
    private List<InsureSegment_M> insureSegList;

    public String getChannelCode() {
        return channelCode;
    }

    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }

    public String getFlightType() {
        return flightType;
    }

    public void setFlightType(String flightType) {
        this.flightType = flightType;
    }

    public String getTripType() {
        return tripType;
    }

    public void setTripType(String tripType) {
        this.tripType = tripType;
    }

    public String getIsSingleBuy() {
        return isSingleBuy;
    }

    public void setIsSingleBuy(String isSingleBuy) {
        this.isSingleBuy = isSingleBuy;
    }

    public List<InsureSegment_M> getInsureSegList() {
        return insureSegList;
    }

    public void setInsureSegList(List<InsureSegment_M> insureSegList) {
        this.insureSegList = insureSegList;
    }
}

package com.juneyaoair.webbaseclass.request.order;

import com.juneyaoair.baseclass.response.order.comm.WebTaxInfo;
import com.juneyaoair.baseclass.response.order.query.WebInsuranceInfo;
import com.juneyaoair.webbaseclass.request.order.WebSegmentPriceInfo;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

@XmlRootElement(name = "WebOrderPassengerInfo")
@XmlAccessorType(XmlAccessType.FIELD)
public class WebOrderPassengerInfo {
	private int passengerID; //乘客ID保存在统一订单数据库中的ID
	private int passengerNO;
	private String passengerName; //乘客姓名
	private String passengerType; //乘客类型ADT － 成人，CHD － 儿童，INF － 婴儿
	private String passengerIdentity; //乘客身份VIP，教师，警殘，革伤
	private String certType; //证件类型身份证:NI,护照:PP,其它证件:CC
	private String certNo; //证件号码
	private String ffCardNo; //常客卡号HO+卡号
	private String handphoneNo; //手机号
	private Double ticketPrice; //票面价
	private Double pricePaid; //实际支付票价
	private Double yqtax; //燃油费
	private Double cntax; //建设税
	private Double otherTax; //其它税费
	private Double qfee; //Q费
	private String adtNameToInf; //婴儿绑定的成人姓名如果乘客类型为婴儿，则必填
	private String birthdate; //出生日期yyyy-MM-dd儿童、婴儿和国际票必填
	private String sex; //性别国际票必填
	private String nationality; //国籍国际票必填
	private String belongCountry; //发证国国际票必填
	private String certValidity; //证件有效期yyyy-MM-dd国际票必填
	private String isBuyInsurance; //是否购买保险Y,N
	private Double insuranceAmount; //保险金额保险金额 = 份数 * 单价 * 航段数
	private List<WebInsuranceInfo> insuranceList;
	private Double upgradeFee;
	private String redeemId;
	private String approvalCode;

	private String eticketNo; //票号电子客票号(xxx-xxxxxxxxxx形式)
	private List<WebSegmentPriceInfo> segmentPriceInfoList; //航段票价信息列表
	private List<WebTaxInfo> taxInfoList; //税费信息列表
	private String ticketOrderNo; //机票订单号
	private Double deductionTotal; //退票手续费总金额应退款 = 票价合计 - 已使用航段票价总金额 - 退票手续费总金额 + 应退票税费合计 + 应退其它费用合计
	private Double useFlowdPriceTotal; //已使用航段票价总金额
	private Double refundXTaxTotal; //应退税费合计
	private Double refundOtherTotal; //应退其它费用合计
	private String refundSign; //退票费验证串退票信息数组 + 该退票人不含税票价合计 + 退票手续费总金额 + 运价系统退票费计算私钥做SHA1

	public int getPassengerID() {
		return passengerID;
	}

	public void setPassengerID(int passengerID) {
		this.passengerID = passengerID;
	}

	public int getPassengerNO() {
		return passengerNO;
	}

	public void setPassengerNO(int passengerNO) {
		this.passengerNO = passengerNO;
	}

	public String getPassengerName() {
		return passengerName;
	}

	public void setPassengerName(String passengerName) {
		this.passengerName = passengerName;
	}

	public String getPassengerType() {
		return passengerType;
	}

	public void setPassengerType(String passengerType) {
		this.passengerType = passengerType;
	}

	public String getPassengerIdentity() {
		return passengerIdentity;
	}

	public void setPassengerIdentity(String passengerIdentity) {
		this.passengerIdentity = passengerIdentity;
	}

	public String getCertType() {
		return certType;
	}

	public void setCertType(String certType) {
		this.certType = certType;
	}

	public String getCertNo() {
		return certNo;
	}

	public void setCertNo(String certNo) {
		this.certNo = certNo;
	}

	public String getFfCardNo() {
		return ffCardNo;
	}

	public void setFfCardNo(String ffCardNo) {
		this.ffCardNo = ffCardNo;
	}

	public String getHandphoneNo() {
		return handphoneNo;
	}

	public void setHandphoneNo(String handphoneNo) {
		this.handphoneNo = handphoneNo;
	}

	public Double getTicketPrice() {
		return ticketPrice;
	}

	public void setTicketPrice(Double ticketPrice) {
		this.ticketPrice = ticketPrice;
	}

	public Double getPricePaid() {
		return pricePaid;
	}

	public void setPricePaid(Double pricePaid) {
		this.pricePaid = pricePaid;
	}

	public Double getYqtax() {
		return yqtax;
	}

	public void setYqtax(Double yqtax) {
		this.yqtax = yqtax;
	}

	public Double getCntax() {
		return cntax;
	}

	public void setCntax(Double cntax) {
		this.cntax = cntax;
	}

	public Double getOtherTax() {
		return otherTax;
	}

	public void setOtherTax(Double otherTax) {
		this.otherTax = otherTax;
	}

	public Double getQfee() {
		return qfee;
	}

	public void setQfee(Double qfee) {
		this.qfee = qfee;
	}

	public String getEticketNo() {
		return eticketNo;
	}

	public void setEticketNo(String eticketNo) {
		this.eticketNo = eticketNo;
	}

	public String getAdtNameToInf() {
		return adtNameToInf;
	}

	public void setAdtNameToInf(String adtNameToInf) {
		this.adtNameToInf = adtNameToInf;
	}

	public String getBirthdate() {
		return birthdate;
	}

	public void setBirthdate(String birthdate) {
		this.birthdate = birthdate;
	}

	public String getSex() {
		return sex;
	}

	public void setSex(String sex) {
		this.sex = sex;
	}

	public String getNationality() {
		return nationality;
	}

	public void setNationality(String nationality) {
		this.nationality = nationality;
	}

	public String getBelongCountry() {
		return belongCountry;
	}

	public void setBelongCountry(String belongCountry) {
		this.belongCountry = belongCountry;
	}

	public String getCertValidity() {
		return certValidity;
	}

	public void setCertValidity(String certValidity) {
		this.certValidity = certValidity;
	}

	public String getIsBuyInsurance() {
		return isBuyInsurance;
	}

	public void setIsBuyInsurance(String isBuyInsurance) {
		this.isBuyInsurance = isBuyInsurance;
	}

	public Double getInsuranceAmount() {
		return insuranceAmount;
	}

	public void setInsuranceAmount(Double insuranceAmount) {
		this.insuranceAmount = insuranceAmount;
	}

	public List<WebInsuranceInfo> getInsuranceList() {
		return insuranceList;
	}

	public void setInsuranceList(List<WebInsuranceInfo> insuranceList) {
		this.insuranceList = insuranceList;
	}

	public Double getUpgradeFee() {
		return upgradeFee;
	}

	public void setUpgradeFee(Double upgradeFee) {
		this.upgradeFee = upgradeFee;
	}

	public String getRedeemId() {
		return redeemId;
	}

	public void setRedeemId(String redeemId) {
		this.redeemId = redeemId;
	}

	public String getApprovalCode() {
		return approvalCode;
	}

	public void setApprovalCode(String approvalCode) {
		this.approvalCode = approvalCode;
	}



	public List<WebSegmentPriceInfo> getSegmentPriceInfoList() {
		return segmentPriceInfoList;
	}

	public void setSegmentPriceInfoList(List<WebSegmentPriceInfo> segmentPriceInfoList) {
		this.segmentPriceInfoList = segmentPriceInfoList;
	}

	public List<WebTaxInfo> getTaxInfoList() {
		return taxInfoList;
	}

	public void setTaxInfoList(List<WebTaxInfo> taxInfoList) {
		this.taxInfoList = taxInfoList;
	}

	public String getTicketOrderNo() {
		return ticketOrderNo;
	}

	public void setTicketOrderNo(String ticketOrderNo) {
		this.ticketOrderNo = ticketOrderNo;
	}

	public Double getDeductionTotal() {
		return deductionTotal;
	}

	public void setDeductionTotal(Double deductionTotal) {
		this.deductionTotal = deductionTotal;
	}

	public Double getUseFlowdPriceTotal() {
		return useFlowdPriceTotal;
	}

	public void setUseFlowdPriceTotal(Double useFlowdPriceTotal) {
		this.useFlowdPriceTotal = useFlowdPriceTotal;
	}

	public Double getRefundXTaxTotal() {
		return refundXTaxTotal;
	}

	public void setRefundXTaxTotal(Double refundXTaxTotal) {
		this.refundXTaxTotal = refundXTaxTotal;
	}

	public Double getRefundOtherTotal() {
		return refundOtherTotal;
	}

	public void setRefundOtherTotal(Double refundOtherTotal) {
		this.refundOtherTotal = refundOtherTotal;
	}

	public String getRefundSign() {
		return refundSign;
	}

	public void setRefundSign(String refundSign) {
		this.refundSign = refundSign;
	}
}
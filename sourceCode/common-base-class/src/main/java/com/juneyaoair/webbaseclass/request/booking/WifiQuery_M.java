package com.juneyaoair.webbaseclass.request.booking;

import javax.validation.constraints.Min;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * Created by yaocf on 2016/12/16.
 */
@XmlRootElement(name = "WifiQuery_M")
@XmlAccessorType(XmlAccessType.FIELD)
public class WifiQuery_M {
    private String wifiProductId;/// Wifi产品ID
    private String wifiProductName;/// Wifi产品ID
    private Double wifiOrgAmount;/// 原始金额
    private Double wifiAmount;/// 销售金额
    private Double wifiPerDayAmount;/// 每日销售金额
    private Double wifiAmountSingle;/// 单独购买销售金额
    private int maxUseScore;/// 可使用积分数
    private int useScore;/// 积分数
    private int prePurchaseDay;/// 提前预定天数
    private int minRentDay; /// 最短租赁天数
    private String wifiFetchAddress;/// 取件地址
    private String wifiReturnAddress; /// 还件地址
    @Min(value=0, message="wifi购买金额需大于0")
    private Double orderAmount;/// 购买金额
    @Min(value=1, message="wifi购买数量需大于0")
    private int purchaseQuantity;/// 购买数量
    private String takeDate;/// 领取时间
    private String returnDate;/// 归还时间
    private String handphoneNo;/// 手机号
    private String linker;/// 联系人姓名
    private String countryNo;/// 国家编号
    private String flightNo;/// 航班号
    private String certNo;/// 证件号
    private String wifiState;/// 订单状态
    private String wifiSideId;
    private String depAirport;//起始机场
    public WifiQuery_M() {
    }

    public String getDepAirport() {
        return depAirport;
    }

    public void setDepAirport(String depAirport) {
        this.depAirport = depAirport;
    }

    public String getWifiProductId() {
        return wifiProductId;
    }

    public void setWifiProductId(String wifiProductId) {
        this.wifiProductId = wifiProductId;
    }

    public String getWifiProductName() {
        return wifiProductName;
    }

    public void setWifiProductName(String wifiProductName) {
        this.wifiProductName = wifiProductName;
    }

    public Double getWifiOrgAmount() {
        return wifiOrgAmount;
    }

    public void setWifiOrgAmount(Double wifiOrgAmount) {
        this.wifiOrgAmount = wifiOrgAmount;
    }

    public Double getWifiPerDayAmount() {
        return wifiPerDayAmount;
    }

    public void setWifiPerDayAmount(Double wifiPerDayAmount) {
        this.wifiPerDayAmount = wifiPerDayAmount;
    }

    public Double getWifiAmountSingle() {
        return wifiAmountSingle;
    }

    public void setWifiAmountSingle(Double wifiAmountSingle) {
        this.wifiAmountSingle = wifiAmountSingle;
    }

    public int getMaxUseScore() {
        return maxUseScore;
    }

    public void setMaxUseScore(int maxUseScore) {
        this.maxUseScore = maxUseScore;
    }

    public int getUseScore() {
        return useScore;
    }

    public void setUseScore(int useScore) {
        this.useScore = useScore;
    }

    public int getPrePurchaseDay() {
        return prePurchaseDay;
    }

    public void setPrePurchaseDay(int prePurchaseDay) {
        this.prePurchaseDay = prePurchaseDay;
    }

    public int getMinRentDay() {
        return minRentDay;
    }

    public void setMinRentDay(int minRentDay) {
        this.minRentDay = minRentDay;
    }

    public String getWifiFetchAddress() {
        return wifiFetchAddress;
    }

    public void setWifiFetchAddress(String wifiFetchAddress) {
        this.wifiFetchAddress = wifiFetchAddress;
    }

    public String getWifiReturnAddress() {
        return wifiReturnAddress;
    }

    public void setWifiReturnAddress(String wifiReturnAddress) {
        this.wifiReturnAddress = wifiReturnAddress;
    }

    public Double getOrderAmount() {
        return orderAmount;
    }

    public void setOrderAmount(Double orderAmount) {
        this.orderAmount = orderAmount;
    }

    public int getPurchaseQuantity() {
        return purchaseQuantity;
    }

    public void setPurchaseQuantity(int purchaseQuantity) {
        this.purchaseQuantity = purchaseQuantity;
    }

    public String getTakeDate() {
        return takeDate;
    }

    public void setTakeDate(String takeDate) {
        this.takeDate = takeDate;
    }

    public String getReturnDate() {
        return returnDate;
    }

    public void setReturnDate(String returnDate) {
        this.returnDate = returnDate;
    }

    public String getHandphoneNo() {
        return handphoneNo;
    }

    public void setHandphoneNo(String handphoneNo) {
        this.handphoneNo = handphoneNo;
    }

    public String getLinker() {
        return linker;
    }

    public void setLinker(String linker) {
        this.linker = linker;
    }

    public String getCountryNo() {
        return countryNo;
    }

    public void setCountryNo(String countryNo) {
        this.countryNo = countryNo;
    }

    public String getFlightNo() {
        return flightNo;
    }

    public void setFlightNo(String flightNo) {
        this.flightNo = flightNo;
    }

    public String getCertNo() {
        return certNo;
    }

    public void setCertNo(String certNo) {
        this.certNo = certNo;
    }

    public String getWifiState() {
        return wifiState;
    }

    public void setWifiState(String wifiState) {
        this.wifiState = wifiState;
    }

    public Double getWifiAmount() {
        return wifiAmount;
    }

    public void setWifiAmount(Double wifiAmount) {
        this.wifiAmount = wifiAmount;
    }

    public String getWifiSideId() {
        return wifiSideId;
    }

    public void setWifiSideId(String wifiSideId) {
        this.wifiSideId = wifiSideId;
    }
}

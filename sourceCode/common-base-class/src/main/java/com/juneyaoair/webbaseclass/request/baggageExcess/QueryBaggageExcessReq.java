package com.juneyaoair.webbaseclass.request.baggageExcess;

import com.juneyaoair.baseclass.request.baggageExcess.BaggageExcessSegmentInfo;
import com.juneyaoair.webbaseclass.request.booking.FlightInfo;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

/**
 * @Author:zc
 * @Description:
 * @Date:Created in 10:14 2018/1/7
 * @Modified by:
 */
@XmlRootElement(name = "QueryBaggageExcessReq")
@XmlAccessorType(XmlAccessType.FIELD)
@NoArgsConstructor
@AllArgsConstructor
public class QueryBaggageExcessReq {
    @NotNull(message="航段信息不能为空")
    private List<BaggageExcessSegmentInfo> segmentInfoList;
    @NotNull(message = "航班信息不能为空")
    private List<FlightInfo> flightInfoList;
    @NotNull(message="渠道用户号不能为空")
    private String channelCode; //渠道用户号	B2C,CC等
    @NotNull(message="用户ID号不能为空")
    private String ffpId;
    @NotNull(message="用户卡号不能为空")
    private String ffpCardNo;
    @NotNull(message="验证码不能为空")
    private String loginKeyInfo;
    private String clientVersion;//客户端版本号

    public String getClientVersion() {
        return clientVersion;
    }

    public void setClientVersion(String clientVersion) {
        this.clientVersion = clientVersion;
    }

    public List<FlightInfo> getFlightInfoList() {
        return flightInfoList;
    }

    public void setFlightInfoList(List<FlightInfo> flightInfoList) {
        this.flightInfoList = flightInfoList;
    }

    public String getChannelCode() {
        return channelCode;
    }

    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }

    public List<BaggageExcessSegmentInfo> getSegmentInfoList() {
        return segmentInfoList;
    }

    public void setSegmentInfoList(List<BaggageExcessSegmentInfo> segmentInfoList) {
        this.segmentInfoList = segmentInfoList;
    }

    public String getFfpId() {
        return ffpId;
    }

    public void setFfpId(String ffpId) {
        this.ffpId = ffpId;
    }

    public String getFfpCardNo() {
        return ffpCardNo;
    }

    public void setFfpCardNo(String ffpCardNo) {
        this.ffpCardNo = ffpCardNo;
    }

    public String getLoginKeyInfo() {
        return loginKeyInfo;
    }

    public void setLoginKeyInfo(String loginKeyInfo) {
        this.loginKeyInfo = loginKeyInfo;
    }
}

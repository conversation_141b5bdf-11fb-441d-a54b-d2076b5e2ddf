package com.juneyaoair.webbaseclass.request.booking;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

/**
 * Created by yaocf on 2016/12/20.
 */
@XmlRootElement(name = "FlightInfo")
@XmlAccessorType(XmlAccessType.FIELD)
public class FlightInfo {
    private String id;
    private String flightDirection; //飞行方向去程为G,回程为B
    private String flightNo; //航班号
    private String depDateTime; //航班起飞时间yyyy-MM-dd HH:mm
    private String arrDateTime; //航班到达时间yyyy-MM-dd HH:mm
    private String flightDate; //
    private String depCity; //起飞城市三字码
    private String arrCity; //到达城市三字码
    private String depCityName; //起飞城市
    private String arrCityName; //到达城市
    private String depAirport; //起飞机场三字码
    private String arrAirport; //到达机场三字码
    private String depAirportName; //起飞机场
    private String arrAirportName; //到达机场
    private boolean codeShare; //是否共享航班
    private String carrierNo; //航班号
    private String ftype; //机型
    private String mealCode; //餐食代码
    private boolean asr;
    private int stopNumber; //经停次数
    private String depTerm; //起飞航站楼
    private String arrTerm; //到达航站楼
    private boolean etkt; //是否电子客票
    private double yqtax; //燃油费国内航程时该字段有效,国际时该字段无效值为-1
    private double cntax; //建设税国内航程时该字段有效,国际时该字段无效值为-1
    private double duration;
    private List<CabinFare> cabinFareList;
    private List<CabinFare> cabinCHDINFFareList;
    private List<CabinFare> cabinGMJCFareList;
    private String stopAirport;
    private String stopAirportName;
    private String stopArrTime;
    private String stopDepTime;
    private String countryNo;//目的国家代码
    private Double	minPrice;	//最低票价


    public List<CabinFare> getCabinGMJCFareList() {
        return cabinGMJCFareList;
    }

    public void setCabinGMJCFareList(List<CabinFare> cabinGMJCFareList) {
        this.cabinGMJCFareList = cabinGMJCFareList;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getFlightDirection() {
        return flightDirection;
    }

    public void setFlightDirection(String flightDirection) {
        this.flightDirection = flightDirection;
    }

    public String getFlightNo() {
        return flightNo;
    }

    public void setFlightNo(String flightNo) {
        this.flightNo = flightNo;
    }

    public String getDepDateTime() {
        return depDateTime;
    }

    public void setDepDateTime(String depDateTime) {
        this.depDateTime = depDateTime;
    }

    public String getArrDateTime() {
        return arrDateTime;
    }

    public void setArrDateTime(String arrDateTime) {
        this.arrDateTime = arrDateTime;
    }

    public String getFlightDate() {
        return flightDate;
    }

    public void setFlightDate(String flightDate) {
        this.flightDate = flightDate;
    }

    public String getDepCity() {
        return depCity;
    }

    public void setDepCity(String depCity) {
        this.depCity = depCity;
    }

    public String getArrCity() {
        return arrCity;
    }

    public void setArrCity(String arrCity) {
        this.arrCity = arrCity;
    }

    public String getDepCityName() {
        return depCityName;
    }

    public void setDepCityName(String depCityName) {
        this.depCityName = depCityName;
    }

    public String getArrCityName() {
        return arrCityName;
    }

    public void setArrCityName(String arrCityName) {
        this.arrCityName = arrCityName;
    }

    public String getDepAirport() {
        return depAirport;
    }

    public void setDepAirport(String depAirport) {
        this.depAirport = depAirport;
    }

    public String getArrAirport() {
        return arrAirport;
    }

    public void setArrAirport(String arrAirport) {
        this.arrAirport = arrAirport;
    }

    public String getDepAirportName() {
        return depAirportName;
    }

    public void setDepAirportName(String depAirportName) {
        this.depAirportName = depAirportName;
    }

    public String getArrAirportName() {
        return arrAirportName;
    }

    public void setArrAirportName(String arrAirportName) {
        this.arrAirportName = arrAirportName;
    }

    public boolean isCodeShare() {
        return codeShare;
    }

    public void setCodeShare(boolean codeShare) {
        this.codeShare = codeShare;
    }

    public String getCarrierNo() {
        return carrierNo;
    }

    public void setCarrierNo(String carrierNo) {
        this.carrierNo = carrierNo;
    }

    public String getFtype() {
        return ftype;
    }

    public void setFtype(String ftype) {
        this.ftype = ftype;
    }

    public String getMealCode() {
        return mealCode;
    }

    public void setMealCode(String mealCode) {
        this.mealCode = mealCode;
    }

    public boolean isAsr() {
        return asr;
    }

    public void setAsr(boolean asr) {
        this.asr = asr;
    }

    public int getStopNumber() {
        return stopNumber;
    }

    public void setStopNumber(int stopNumber) {
        this.stopNumber = stopNumber;
    }

    public String getDepTerm() {
        return depTerm;
    }

    public void setDepTerm(String depTerm) {
        this.depTerm = depTerm;
    }

    public String getArrTerm() {
        return arrTerm;
    }

    public void setArrTerm(String arrTerm) {
        this.arrTerm = arrTerm;
    }

    public boolean isEtkt() {
        return etkt;
    }

    public void setEtkt(boolean etkt) {
        this.etkt = etkt;
    }

    public double getYqtax() {
        return yqtax;
    }

    public void setYqtax(double yqtax) {
        this.yqtax = yqtax;
    }

    public double getCntax() {
        return cntax;
    }

    public void setCntax(double cntax) {
        this.cntax = cntax;
    }

    public double getDuration() {
        return duration;
    }

    public void setDuration(double duration) {
        this.duration = duration;
    }

    public List<CabinFare> getCabinFareList() {
        return cabinFareList;
    }

    public void setCabinFareList(List<CabinFare> cabinFareList) {
        this.cabinFareList = cabinFareList;
    }

    public List<CabinFare> getCabinCHDINFFareList() {
        return cabinCHDINFFareList;
    }

    public void setCabinCHDINFFareList(List<CabinFare> cabinCHDINFFareList) {
        this.cabinCHDINFFareList = cabinCHDINFFareList;
    }

    public String getStopAirport() {
        return stopAirport;
    }

    public void setStopAirport(String stopAirport) {
        this.stopAirport = stopAirport;
    }

    public String getStopAirportName() {
        return stopAirportName;
    }

    public void setStopAirportName(String stopAirportName) {
        this.stopAirportName = stopAirportName;
    }

    public String getStopArrTime() {
        return stopArrTime;
    }

    public void setStopArrTime(String stopArrTime) {
        this.stopArrTime = stopArrTime;
    }

    public String getStopDepTime() {
        return stopDepTime;
    }

    public void setStopDepTime(String stopDepTime) {
        this.stopDepTime = stopDepTime;
    }

    public String getCountryNo() {
        return countryNo;
    }

    public void setCountryNo(String countryNo) {
        this.countryNo = countryNo;
    }

    public Double getMinPrice() {
        return minPrice;
    }

    public void setMinPrice(Double minPrice) {
        this.minPrice = minPrice;
    }
}

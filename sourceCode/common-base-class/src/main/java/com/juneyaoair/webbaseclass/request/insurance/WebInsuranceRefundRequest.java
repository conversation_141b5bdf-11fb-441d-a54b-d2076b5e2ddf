package com.juneyaoair.webbaseclass.request.insurance;

import com.juneyaoair.webbaseclass.request.order.WebOrderPassengerInfo;

import javax.validation.constraints.NotNull;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;


@XmlRootElement(name = "InsuranceRefundRequest")
@XmlAccessorType(XmlAccessType.FIELD)
public class WebInsuranceRefundRequest {
	@NotNull(message="渠道用户号不能为空")
	private String channelCode; //渠道用户号	B2C,CC等
	@NotNull(message="预定渠道用户号不能为空")
	private String bookChannelCode;
	@NotNull(message="订单编号不能为空")
	private String ticketOrderNo; //机票订单编号
	@NotNull(message="按人分组退票信息不能为空")
	private List<WebOrderPassengerInfo> orderPassengerInfoList; //按人分组退票信息数组
	@NotNull(message="客户ID号不能为空")
	private String customerNo;
	@NotNull(message="验证码不能为空")
	private String loginKeyInfo;
	@NotNull(message="退保类型不能为空")//A 申请 R确认退保
	private String insuranceRefundType;
	private String proposer; //退保申请人
	private String linkTelphone; //退保人联系电话

	public String getChannelCode() {
		return channelCode;
	}

	public void setChannelCode(String channelCode) {
		this.channelCode = channelCode;
	}

	public String getBookChannelCode() {
		return bookChannelCode;
	}

	public void setBookChannelCode(String bookChannelCode) {
		this.bookChannelCode = bookChannelCode;
	}

	public String getTicketOrderNo() {
		return ticketOrderNo;
	}

	public void setTicketOrderNo(String ticketOrderNo) {
		this.ticketOrderNo = ticketOrderNo;
	}

	public List<WebOrderPassengerInfo> getOrderPassengerInfoList() {
		return orderPassengerInfoList;
	}

	public void setOrderPassengerInfoList(List<WebOrderPassengerInfo> orderPassengerInfoList) {
		this.orderPassengerInfoList = orderPassengerInfoList;
	}

	public String getCustomerNo() {
		return customerNo;
	}

	public void setCustomerNo(String customerNo) {
		this.customerNo = customerNo;
	}

	public String getLoginKeyInfo() {
		return loginKeyInfo;
	}

	public void setLoginKeyInfo(String loginKeyInfo) {
		this.loginKeyInfo = loginKeyInfo;
	}

	public String getProposer() {
		return proposer;
	}

	public void setProposer(String proposer) {
		this.proposer = proposer;
	}

	public String getLinkTelphone() {
		return linkTelphone;
	}

	public void setLinkTelphone(String linkTelphone) {
		this.linkTelphone = linkTelphone;
	}

	public String getInsuranceRefundType() {
		return insuranceRefundType;
	}

	public void setInsuranceRefundType(String insuranceRefundType) {
		this.insuranceRefundType = insuranceRefundType;
	}
}
package com.juneyaoair.webbaseclass.request.insure;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

/**
 * Created by yaocf on 2016/12/19.
 */
@XmlRootElement(name = "InsureSegment")
@XmlAccessorType(XmlAccessType.FIELD)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class InsureSegment_M {
    private int  segNO;
    private String flightDate;
    private String depDateTime; //yyyy-mm-dd hh:mm
    private String depAirport;
    private String arrAirport;
    private String cabin;
    private String ticketStatus;//客票状态
    private List<InsuranceBind_M> insuranceBindList;
}

package com.juneyaoair.webbaseclass.request.booking;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * Created by yaocf on 2016/12/19.
 */
@XmlRootElement(name = "SegmentInfo_M")
@XmlAccessorType(XmlAccessType.FIELD)
public class SegmentInfo_M {
    private String arrAirportTerminal; //到达航站楼
    private String depAirportTerminal; //起飞航站楼
    private String arrAirportCode; //到达机场代码
    private String arrAirportName;
    private String arrCityName;
    private String baggageWeight; //允许携带的行李重量
    private String baggagePiece; //允许携带的行李件数
    private String cabin; //预订舱位
    private String depAirportCode; //起飞机场代码
    private String depAirportName;
    private String depCityName;
    private String startValidityDate; //有效期起始时间(null表示无起始日期或情况不明)yyyy-MM-dd HH:mi
    private String endValidityDate; //有效期终止时间(null表示无终止日期或情况不明)yyyy-MM-dd HH:mi
    private String depTime; //起飞时间yyyy-MM-dd HH:mi
    private String flightNo; //航班编号
    private String pnrNo; //PNR编号(ICS编号)
    private String crsPnrNo; //代理人系统PNR编号
    private String crsType; //代理人系统代码
    private String rate; //适用的运价类型（如YB为Y舱B类运价）FireBase
    private String ticketStatus; //客票状态
    private String airline; //
    private String boardingNo; //旅客在已飞行航段中的登机牌号
    private String segmentStatus; //航段状态
    private String stopType; //停留原因停留原因 O 正常 X 中转联程
    private String type; //航段类型
    private boolean isFPC; //
    private String operationAirline; //承运方航空公司代码
    private String marketingAirline; //市场方航空公司代码
    private String arrTime; //到达时间yyyy-MM-dd HH:mi
    private int segmentIndex; //电子票票面航段序号
    private String mcoNumber; //MCO单号
    private String baggageWeightUnit; //行李重量单位

    public String getArrAirportTerminal() {
        return arrAirportTerminal;
    }

    public void setArrAirportTerminal(String arrAirportTerminal) {
        this.arrAirportTerminal = arrAirportTerminal;
    }

    public String getDepAirportTerminal() {
        return depAirportTerminal;
    }

    public void setDepAirportTerminal(String depAirportTerminal) {
        this.depAirportTerminal = depAirportTerminal;
    }

    public String getArrAirportCode() {
        return arrAirportCode;
    }

    public void setArrAirportCode(String arrAirportCode) {
        this.arrAirportCode = arrAirportCode;
    }

    public String getArrAirportName() {
        return arrAirportName;
    }

    public void setArrAirportName(String arrAirportName) {
        this.arrAirportName = arrAirportName;
    }

    public String getArrCityName() {
        return arrCityName;
    }

    public void setArrCityName(String arrCityName) {
        this.arrCityName = arrCityName;
    }

    public String getBaggageWeight() {
        return baggageWeight;
    }

    public void setBaggageWeight(String baggageWeight) {
        this.baggageWeight = baggageWeight;
    }

    public String getBaggagePiece() {
        return baggagePiece;
    }

    public void setBaggagePiece(String baggagePiece) {
        this.baggagePiece = baggagePiece;
    }

    public String getCabin() {
        return cabin;
    }

    public void setCabin(String cabin) {
        this.cabin = cabin;
    }

    public String getDepAirportCode() {
        return depAirportCode;
    }

    public void setDepAirportCode(String depAirportCode) {
        this.depAirportCode = depAirportCode;
    }

    public String getDepAirportName() {
        return depAirportName;
    }

    public void setDepAirportName(String depAirportName) {
        this.depAirportName = depAirportName;
    }

    public String getDepCityName() {
        return depCityName;
    }

    public void setDepCityName(String depCityName) {
        this.depCityName = depCityName;
    }

    public String getStartValidityDate() {
        return startValidityDate;
    }

    public void setStartValidityDate(String startValidityDate) {
        this.startValidityDate = startValidityDate;
    }

    public String getEndValidityDate() {
        return endValidityDate;
    }

    public void setEndValidityDate(String endValidityDate) {
        this.endValidityDate = endValidityDate;
    }

    public String getDepTime() {
        return depTime;
    }

    public void setDepTime(String depTime) {
        this.depTime = depTime;
    }

    public String getFlightNo() {
        return flightNo;
    }

    public void setFlightNo(String flightNo) {
        this.flightNo = flightNo;
    }

    public String getPnrNo() {
        return pnrNo;
    }

    public void setPnrNo(String pnrNo) {
        this.pnrNo = pnrNo;
    }

    public String getCrsPnrNo() {
        return crsPnrNo;
    }

    public void setCrsPnrNo(String crsPnrNo) {
        this.crsPnrNo = crsPnrNo;
    }

    public String getCrsType() {
        return crsType;
    }

    public void setCrsType(String crsType) {
        this.crsType = crsType;
    }

    public String getRate() {
        return rate;
    }

    public void setRate(String rate) {
        this.rate = rate;
    }

    public String getTicketStatus() {
        return ticketStatus;
    }

    public void setTicketStatus(String ticketStatus) {
        this.ticketStatus = ticketStatus;
    }

    public String getAirline() {
        return airline;
    }

    public void setAirline(String airline) {
        this.airline = airline;
    }

    public String getBoardingNo() {
        return boardingNo;
    }

    public void setBoardingNo(String boardingNo) {
        this.boardingNo = boardingNo;
    }

    public String getSegmentStatus() {
        return segmentStatus;
    }

    public void setSegmentStatus(String segmentStatus) {
        this.segmentStatus = segmentStatus;
    }

    public String getStopType() {
        return stopType;
    }

    public void setStopType(String stopType) {
        this.stopType = stopType;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public boolean isFPC() {
        return isFPC;
    }

    public void setFPC(boolean FPC) {
        isFPC = FPC;
    }

    public String getOperationAirline() {
        return operationAirline;
    }

    public void setOperationAirline(String operationAirline) {
        this.operationAirline = operationAirline;
    }

    public String getMarketingAirline() {
        return marketingAirline;
    }

    public void setMarketingAirline(String marketingAirline) {
        this.marketingAirline = marketingAirline;
    }

    public String getArrTime() {
        return arrTime;
    }

    public void setArrTime(String arrTime) {
        this.arrTime = arrTime;
    }

    public int getSegmentIndex() {
        return segmentIndex;
    }

    public void setSegmentIndex(int segmentIndex) {
        this.segmentIndex = segmentIndex;
    }

    public String getMcoNumber() {
        return mcoNumber;
    }

    public void setMcoNumber(String mcoNumber) {
        this.mcoNumber = mcoNumber;
    }

    public String getBaggageWeightUnit() {
        return baggageWeightUnit;
    }

    public void setBaggageWeightUnit(String baggageWeightUnit) {
        this.baggageWeightUnit = baggageWeightUnit;
    }
}

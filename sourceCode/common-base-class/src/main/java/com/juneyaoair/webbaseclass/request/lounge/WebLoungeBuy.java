package com.juneyaoair.webbaseclass.request.lounge;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * Created by qinxiaoming on 2016-6-2.
 */
@XmlRootElement(name = "WebLoungeBuy")
@XmlAccessorType(XmlAccessType.FIELD)
public class WebLoungeBuy {


    private String loungeNo;

    private String loungeName;

    private String depAirport;

    private String depAirportNm;

    private String arrAirport;

    private String flightNo;

    private String flightDate;

    private String cabin;

    private String passengerName;

    private String certNo;

    private String ticketNo;

    private String handphoneNo;

    private Double loungeAmount;

    private int useScore;

    private String loungeState;

    private String loungeTitle;

    private String loungeCountent;

    private String loungePicUrl;

    private String address;

    private String loungeCoupon;

    private String validDateBegin;

    private String validDateEnd;

    private int loungeCount;

    private Double loungeOrderAmount;

    private String orderDatetime;

    private String remark;

    private String isSingleOrder;

    private String isUnitedOrder;

    public WebLoungeBuy() {
    }

    public String getLoungeNo() {
        return loungeNo;
    }

    public void setLoungeNo(String loungeNo) {
        this.loungeNo = loungeNo;
    }

    public String getLoungeName() {
        return loungeName;
    }

    public void setLoungeName(String loungeName) {
        this.loungeName = loungeName;
    }

    public String getDepAirport() {
        return depAirport;
    }

    public void setDepAirport(String depAirport) {
        this.depAirport = depAirport;
    }

    public String getDepAirportNm() {
        return depAirportNm;
    }

    public void setDepAirportNm(String depAirportNm) {
        this.depAirportNm = depAirportNm;
    }

    public String getArrAirport() {
        return arrAirport;
    }

    public void setArrAirport(String arrAirport) {
        this.arrAirport = arrAirport;
    }

    public String getFlightNo() {
        return flightNo;
    }

    public void setFlightNo(String flightNo) {
        this.flightNo = flightNo;
    }

    public String getFlightDate() {
        return flightDate;
    }

    public void setFlightDate(String flightDate) {
        this.flightDate = flightDate;
    }

    public String getCabin() {
        return cabin;
    }

    public void setCabin(String cabin) {
        this.cabin = cabin;
    }

    public String getPassengerName() {
        return passengerName;
    }

    public void setPassengerName(String passengerName) {
        this.passengerName = passengerName;
    }

    public String getCertNo() {
        return certNo;
    }

    public void setCertNo(String certNo) {
        this.certNo = certNo;
    }

    public String getTicketNo() {
        return ticketNo;
    }

    public void setTicketNo(String ticketNo) {
        this.ticketNo = ticketNo;
    }

    public String getHandphoneNo() {
        return handphoneNo;
    }

    public void setHandphoneNo(String handphoneNo) {
        this.handphoneNo = handphoneNo;
    }

    public Double getLoungeAmount() {
        return loungeAmount;
    }

    public void setLoungeAmount(Double loungeAmount) {
        this.loungeAmount = loungeAmount;
    }

    public int getUseScore() {
        return useScore;
    }

    public void setUseScore(int useScore) {
        this.useScore = useScore;
    }

    public String getLoungeState() {
        return loungeState;
    }

    public void setLoungeState(String loungeState) {
        this.loungeState = loungeState;
    }

    public String getLoungeTitle() {
        return loungeTitle;
    }

    public void setLoungeTitle(String loungeTitle) {
        this.loungeTitle = loungeTitle;
    }

    public String getLoungeCountent() {
        return loungeCountent;
    }

    public void setLoungeCountent(String loungeCountent) {
        this.loungeCountent = loungeCountent;
    }

    public String getLoungePicUrl() {
        return loungePicUrl;
    }

    public void setLoungePicUrl(String loungePicUrl) {
        this.loungePicUrl = loungePicUrl;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getLoungeCoupon() {
        return loungeCoupon;
    }

    public void setLoungeCoupon(String loungeCoupon) {
        this.loungeCoupon = loungeCoupon;
    }

    public String getValidDateBegin() {
        return validDateBegin;
    }

    public void setValidDateBegin(String validDateBegin) {
        this.validDateBegin = validDateBegin;
    }

    public String getValidDateEnd() {
        return validDateEnd;
    }

    public void setValidDateEnd(String validDateEnd) {
        this.validDateEnd = validDateEnd;
    }

    public int getLoungeCount() {
        return loungeCount;
    }

    public void setLoungeCount(int loungeCount) {
        this.loungeCount = loungeCount;
    }

    public Double getLoungeOrderAmount() {
        return loungeOrderAmount;
    }

    public void setLoungeOrderAmount(Double loungeOrderAmount) {
        this.loungeOrderAmount = loungeOrderAmount;
    }

    public String getOrderDatetime() {
        return orderDatetime;
    }

    public void setOrderDatetime(String orderDatetime) {
        this.orderDatetime = orderDatetime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getIsSingleOrder() {
        return isSingleOrder;
    }

    public void setIsSingleOrder(String isSingleOrder) {
        this.isSingleOrder = isSingleOrder;
    }

    public String getIsUnitedOrder() {
        return isUnitedOrder;
    }

    public void setIsUnitedOrder(String isUnitedOrder) {
        this.isUnitedOrder = isUnitedOrder;
    }
}

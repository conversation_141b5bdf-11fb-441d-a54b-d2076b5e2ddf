package com.juneyaoair.webbaseclass.response.refund.insurance;

import com.juneyaoair.webbaseclass.request.order.WebOrderPassengerInfo;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

@XmlRootElement(name = "WebInsuranceRefundResp")
@XmlAccessorType(XmlAccessType.FIELD)
public class WebInsuranceRefundResp {
	private String version; //接口版本号10
	private String channelCode; //渠道用户号B2C,CC等
	private String userNo; //渠道用户人员号分配给渠道用户的工作人员号
	private String ticketOrderNo; //机票订单编号
	private String resultCode; //结果代码1001 － 成功，其它失败
	private String errorInfo; //错误信息
	private String insuranceCodeList;
	private List<WebOrderPassengerInfo> listOrderPassengerInfo;

	public String getVersion() {
		return version;
	}

	public void setVersion(String version) {
		this.version = version;
	}

	public String getChannelCode() {
		return channelCode;
	}

	public void setChannelCode(String channelCode) {
		this.channelCode = channelCode;
	}

	public String getUserNo() {
		return userNo;
	}

	public void setUserNo(String userNo) {
		this.userNo = userNo;
	}

	public String getTicketOrderNo() {
		return ticketOrderNo;
	}

	public void setTicketOrderNo(String ticketOrderNo) {
		this.ticketOrderNo = ticketOrderNo;
	}

	public String getResultCode() {
		return resultCode;
	}

	public void setResultCode(String resultCode) {
		this.resultCode = resultCode;
	}

	public String getErrorInfo() {
		return errorInfo;
	}

	public void setErrorInfo(String errorInfo) {
		this.errorInfo = errorInfo;
	}

	public String getInsuranceCodeList() {
		return insuranceCodeList;
	}

	public void setInsuranceCodeList(String insuranceCodeList) {
		this.insuranceCodeList = insuranceCodeList;
	}

	public List<WebOrderPassengerInfo> getListOrderPassengerInfo() {
		return listOrderPassengerInfo;
	}

	public void setListOrderPassengerInfo(List<WebOrderPassengerInfo> listOrderPassengerInfo) {
		this.listOrderPassengerInfo = listOrderPassengerInfo;
	}
}
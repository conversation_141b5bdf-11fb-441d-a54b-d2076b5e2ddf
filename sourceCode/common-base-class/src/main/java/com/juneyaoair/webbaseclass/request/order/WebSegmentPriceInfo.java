package com.juneyaoair.webbaseclass.request.order;

import com.juneyaoair.baseclass.response.order.query.WebInsuranceInfo;
import com.juneyaoair.baseclass.response.order.query.WebSegmentInfo;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

@XmlRootElement(name = "WebSegmentPriceInfo")
@XmlAccessorType(XmlAccessType.FIELD)
public class WebSegmentPriceInfo {
	private int segmentID; //航段ID保存在统一订单数据库中的ID
	private int segNO; //旅行顺序第一段为从0开始，第二段为1，依次增加
	private String flightDirection; //飞行方向去程为G,回程为B
	private String flightNo; //航班号
	private String depDateTime; //航班起飞时间yyyy-MM-dd HH:mm
	private String arrDateTime; //航班到达时间yyyy-MM-dd HH:mm
	private String depCity; //起飞城市三字码
	private String arrCity; //到达城市三字码
	private String depAirport; //起飞机场三字码
	private String arrAirport; //到达机场三字码
	private String depCityName; //起飞城市名称
	private String arrCityName; //到达城市名称
	private String depAirportName; //起飞机场名称
	private String arrAirportName; //到达机场名称
	private String cabin; //舱位
	private String cabinClass; //舱位等级
	private Boolean isCodeShare; //是否共享航班
	private String carrierFlightNo; //承运航班号
	private String mealCode; //餐食代码
	private Boolean isSeatedOnPlane; //是否可以机上订位
	private String planeStyle; //机型
	private String depTerm; //起飞航站楼
	private String arrTerm; //到达航站楼
	private String stopNumber; //经停次数
	private Boolean isBuyInsurance; //是否购买保险Y,N
	private Double insuranceAmount; //保险金额保险金额 = 份数 * 单价 * 航段数
	private Double pricePaid; //实际支付票价机票价格
	private Double yqtax; //燃油费
	private Double cntax; //建设税
	private Double amount; //票面总额
	private Double useScore; //使用积分
	private Double couponAmount; //使用优惠券
	private Double giftScore; //赠送积分
	private String ticketState; //机票状态
	private int id; //人航段标识PassengerSegmentID
	private String eticketNo; //票号电子客票号(xxx-xxxxxxxxxx形式)
	private String tktstatus; //客票状态电子客票状态(OPEN FOR USE,USED/FLOWN)非OPEN FOR USE状态都视为已使用
	private String fareID; //运价编号运价系统中该条运价的主键，计算退票费时有用
	private int segmentSeq;// 票面航段序号

	private Double upgradeTicketPrice;
	private Double deduction; //退票手续费打包运价不允许单独退时，退票手续费只有按退票人合计的退票手续费总金额
	private Double useFlowdPrice; //已使用航段票价已使用航段票价和退票手续费两者只会有其一大于0
	private Double refundXTax; //应退税费已使用段或退票规则规定不退税的不退
	private Double refundOther; //应退其它费用已使用段或退票规则规定不退其它费用的不退
	private Double xtax; //航段税费该段各项税费合计
	private Double other; //航段其它费用

	private String comment;/// 备注项	用于前端显示说明信息，如：解释签注信息或备注中转联程提供住宿
	private String baggage;/// 行李重量
	private String validityPeriod;/// 客票有效期	1Y1M1D
	private String minStay;/// 最短停留时间	1Y1M1D
	private Boolean refundedFlag;/// 是否可退票
	private String refundedComment;/// 退票政策描述 说明退票费收取或计算方法
	private String changedComment;/// 变更政策描述 说明改期、签转和升舱政策
	private Boolean rescheduledFlag;/// 是否可免费改期
	private int freeChangeTimes;/// 免费改期次数 免费改期不限次数时，目前运价系统会给出99次
	private Boolean changeAirLineFlag;/// 是否可改签
	private Boolean upgradeFlag;/// 是否可升舱
	private Double deductibls;/// 积分抵用金额
	private Double upgradeFee;/// 升舱手续费
	private Double gifiScore;/// 赠送积分
	private List<WebInsuranceInfo> insuranceList;
	private WebSegmentInfo segmentInfoOTO;/// 改期前航段信息

	public int getSegmentID() {
		return segmentID;
	}

	public void setSegmentID(int segmentID) {
		this.segmentID = segmentID;
	}

	public int getSegNO() {
		return segNO;
	}

	public void setSegNO(int segNO) {
		this.segNO = segNO;
	}

	public String getFlightDirection() {
		return flightDirection;
	}

	public void setFlightDirection(String flightDirection) {
		this.flightDirection = flightDirection;
	}

	public String getFlightNo() {
		return flightNo;
	}

	public void setFlightNo(String flightNo) {
		this.flightNo = flightNo;
	}

	public String getDepDateTime() {
		return depDateTime;
	}

	public void setDepDateTime(String depDateTime) {
		this.depDateTime = depDateTime;
	}

	public String getArrDateTime() {
		return arrDateTime;
	}

	public void setArrDateTime(String arrDateTime) {
		this.arrDateTime = arrDateTime;
	}

	public String getDepCity() {
		return depCity;
	}

	public void setDepCity(String depCity) {
		this.depCity = depCity;
	}

	public String getArrCity() {
		return arrCity;
	}

	public void setArrCity(String arrCity) {
		this.arrCity = arrCity;
	}

	public String getDepAirport() {
		return depAirport;
	}

	public void setDepAirport(String depAirport) {
		this.depAirport = depAirport;
	}

	public String getArrAirport() {
		return arrAirport;
	}

	public void setArrAirport(String arrAirport) {
		this.arrAirport = arrAirport;
	}

	public String getDepCityName() {
		return depCityName;
	}

	public void setDepCityName(String depCityName) {
		this.depCityName = depCityName;
	}

	public String getArrCityName() {
		return arrCityName;
	}

	public void setArrCityName(String arrCityName) {
		this.arrCityName = arrCityName;
	}

	public String getDepAirportName() {
		return depAirportName;
	}

	public void setDepAirportName(String depAirportName) {
		this.depAirportName = depAirportName;
	}

	public String getArrAirportName() {
		return arrAirportName;
	}

	public void setArrAirportName(String arrAirportName) {
		this.arrAirportName = arrAirportName;
	}

	public String getCabin() {
		return cabin;
	}

	public void setCabin(String cabin) {
		this.cabin = cabin;
	}

	public String getCabinClass() {
		return cabinClass;
	}

	public void setCabinClass(String cabinClass) {
		this.cabinClass = cabinClass;
	}

	public Boolean getCodeShare() {
		return isCodeShare;
	}

	public void setCodeShare(Boolean codeShare) {
		isCodeShare = codeShare;
	}

	public String getCarrierFlightNo() {
		return carrierFlightNo;
	}

	public void setCarrierFlightNo(String carrierFlightNo) {
		this.carrierFlightNo = carrierFlightNo;
	}

	public String getMealCode() {
		return mealCode;
	}

	public void setMealCode(String mealCode) {
		this.mealCode = mealCode;
	}

	public Boolean getSeatedOnPlane() {
		return isSeatedOnPlane;
	}

	public void setSeatedOnPlane(Boolean seatedOnPlane) {
		isSeatedOnPlane = seatedOnPlane;
	}

	public String getPlaneStyle() {
		return planeStyle;
	}

	public void setPlaneStyle(String planeStyle) {
		this.planeStyle = planeStyle;
	}

	public String getDepTerm() {
		return depTerm;
	}

	public void setDepTerm(String depTerm) {
		this.depTerm = depTerm;
	}

	public String getArrTerm() {
		return arrTerm;
	}

	public void setArrTerm(String arrTerm) {
		this.arrTerm = arrTerm;
	}

	public String getStopNumber() {
		return stopNumber;
	}

	public void setStopNumber(String stopNumber) {
		this.stopNumber = stopNumber;
	}

	public Boolean getBuyInsurance() {
		return isBuyInsurance;
	}

	public void setBuyInsurance(Boolean buyInsurance) {
		isBuyInsurance = buyInsurance;
	}

	public Double getInsuranceAmount() {
		return insuranceAmount;
	}

	public void setInsuranceAmount(Double insuranceAmount) {
		this.insuranceAmount = insuranceAmount;
	}

	public Double getPricePaid() {
		return pricePaid;
	}

	public void setPricePaid(Double pricePaid) {
		this.pricePaid = pricePaid;
	}

	public Double getAmount() {
		return amount;
	}

	public void setAmount(Double amount) {
		this.amount = amount;
	}

	public Double getUseScore() {
		return useScore;
	}

	public void setUseScore(Double useScore) {
		this.useScore = useScore;
	}

	public Double getCouponAmount() {
		return couponAmount;
	}

	public void setCouponAmount(Double couponAmount) {
		this.couponAmount = couponAmount;
	}

	public Double getGiftScore() {
		return giftScore;
	}

	public void setGiftScore(Double giftScore) {
		this.giftScore = giftScore;
	}

	public String getTicketState() {
		return ticketState;
	}

	public void setTicketState(String ticketState) {
		this.ticketState = ticketState;
	}

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public String getFareID() {
		return fareID;
	}

	public void setFareID(String fareID) {
		this.fareID = fareID;
	}

	public int getSegmentSeq() {
		return segmentSeq;
	}

	public void setSegmentSeq(int segmentSeq) {
		this.segmentSeq = segmentSeq;
	}

	public Double getUpgradeTicketPrice() {
		return upgradeTicketPrice;
	}

	public void setUpgradeTicketPrice(Double upgradeTicketPrice) {
		this.upgradeTicketPrice = upgradeTicketPrice;
	}

	public Double getDeduction() {
		return deduction;
	}

	public void setDeduction(Double deduction) {
		this.deduction = deduction;
	}

	public Double getUseFlowdPrice() {
		return useFlowdPrice;
	}

	public void setUseFlowdPrice(Double useFlowdPrice) {
		this.useFlowdPrice = useFlowdPrice;
	}

	public Double getRefundXTax() {
		return refundXTax;
	}

	public void setRefundXTax(Double refundXTax) {
		this.refundXTax = refundXTax;
	}

	public Double getRefundOther() {
		return refundOther;
	}

	public void setRefundOther(Double refundOther) {
		this.refundOther = refundOther;
	}

	public Double getYqtax() {
		return yqtax;
	}

	public void setYqtax(Double yqtax) {
		this.yqtax = yqtax;
	}

	public Double getCntax() {
		return cntax;
	}

	public void setCntax(Double cntax) {
		this.cntax = cntax;
	}

	public String getEticketNo() {
		return eticketNo;
	}

	public void setEticketNo(String eticketNo) {
		this.eticketNo = eticketNo;
	}

	public String getTktstatus() {
		return tktstatus;
	}

	public void setTktstatus(String tktstatus) {
		this.tktstatus = tktstatus;
	}

	public Double getXtax() {
		return xtax;
	}

	public void setXtax(Double xtax) {
		this.xtax = xtax;
	}

	public Double getOther() {
		return other;
	}

	public void setOther(Double other) {
		this.other = other;
	}

	public String getComment() {
		return comment;
	}

	public void setComment(String comment) {
		this.comment = comment;
	}

	public String getBaggage() {
		return baggage;
	}

	public void setBaggage(String baggage) {
		this.baggage = baggage;
	}

	public String getValidityPeriod() {
		return validityPeriod;
	}

	public void setValidityPeriod(String validityPeriod) {
		this.validityPeriod = validityPeriod;
	}

	public String getMinStay() {
		return minStay;
	}

	public void setMinStay(String minStay) {
		this.minStay = minStay;
	}

	public Boolean getRefundedFlag() {
		return refundedFlag;
	}

	public void setRefundedFlag(Boolean refundedFlag) {
		this.refundedFlag = refundedFlag;
	}

	public String getRefundedComment() {
		return refundedComment;
	}

	public void setRefundedComment(String refundedComment) {
		this.refundedComment = refundedComment;
	}

	public String getChangedComment() {
		return changedComment;
	}

	public void setChangedComment(String changedComment) {
		this.changedComment = changedComment;
	}

	public Boolean getRescheduledFlag() {
		return rescheduledFlag;
	}

	public void setRescheduledFlag(Boolean rescheduledFlag) {
		this.rescheduledFlag = rescheduledFlag;
	}

	public int getFreeChangeTimes() {
		return freeChangeTimes;
	}

	public void setFreeChangeTimes(int freeChangeTimes) {
		this.freeChangeTimes = freeChangeTimes;
	}

	public Boolean getChangeAirLineFlag() {
		return changeAirLineFlag;
	}

	public void setChangeAirLineFlag(Boolean changeAirLineFlag) {
		this.changeAirLineFlag = changeAirLineFlag;
	}

	public Boolean getUpgradeFlag() {
		return upgradeFlag;
	}

	public void setUpgradeFlag(Boolean upgradeFlag) {
		this.upgradeFlag = upgradeFlag;
	}

	public Double getDeductibls() {
		return deductibls;
	}

	public void setDeductibls(Double deductibls) {
		this.deductibls = deductibls;
	}

	public Double getUpgradeFee() {
		return upgradeFee;
	}

	public void setUpgradeFee(Double upgradeFee) {
		this.upgradeFee = upgradeFee;
	}

	public Double getGifiScore() {
		return gifiScore;
	}

	public void setGifiScore(Double gifiScore) {
		this.gifiScore = gifiScore;
	}

	public List<WebInsuranceInfo> getInsuranceList() {
		return insuranceList;
	}

	public void setInsuranceList(List<WebInsuranceInfo> insuranceList) {
		this.insuranceList = insuranceList;
	}

	public WebSegmentInfo getSegmentInfoOTO() {
		return segmentInfoOTO;
	}

	public void setSegmentInfoOTO(WebSegmentInfo segmentInfoOTO) {
		this.segmentInfoOTO = segmentInfoOTO;
	}
}
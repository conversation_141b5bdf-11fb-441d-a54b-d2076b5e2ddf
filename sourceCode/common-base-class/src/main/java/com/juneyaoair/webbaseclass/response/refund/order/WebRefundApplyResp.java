package com.juneyaoair.webbaseclass.response.refund.order;

import com.juneyaoair.webbaseclass.request.order.WebOrderPassengerInfo;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

@XmlRootElement(name = "WebRefundApplyResp")
@XmlAccessorType(XmlAccessType.FIELD)
public class WebRefundApplyResp {
	private String resultCode; //结果代码1001 － 成功，其它失败
	private String errorInfo; //错误信息
	private Double ticketAmount; //可退机票总金额
	private Double taxFee; //可退税费
	private Double qOther; //可退其他税费
	private Double insAmount; //可退保险
	private Double deduction; //手续费
	private Double refundAmount; //应退总金额
	private List<WebOrderPassengerInfo> orderPassengerInfoList; //按人分组退票信息数组

	public String getResultCode() {
		return resultCode;
	}

	public void setResultCode(String resultCode) {
		this.resultCode = resultCode;
	}

	public String getErrorInfo() {
		return errorInfo;
	}

	public void setErrorInfo(String errorInfo) {
		this.errorInfo = errorInfo;
	}

	public Double getTicketAmount() {
		return ticketAmount;
	}

	public void setTicketAmount(Double ticketAmount) {
		this.ticketAmount = ticketAmount;
	}

	public Double getTaxFee() {
		return taxFee;
	}

	public void setTaxFee(Double taxFee) {
		this.taxFee = taxFee;
	}

	public Double getqOther() {
		return qOther;
	}

	public void setqOther(Double qOther) {
		this.qOther = qOther;
	}

	public Double getInsAmount() {
		return insAmount;
	}

	public void setInsAmount(Double insAmount) {
		this.insAmount = insAmount;
	}

	public Double getDeduction() {
		return deduction;
	}

	public void setDeduction(Double deduction) {
		this.deduction = deduction;
	}

	public Double getRefundAmount() {
		return refundAmount;
	}

	public void setRefundAmount(Double refundAmount) {
		this.refundAmount = refundAmount;
	}

	public List<WebOrderPassengerInfo> getOrderPassengerInfoList() {
		return orderPassengerInfoList;
	}

	public void setOrderPassengerInfoList(List<WebOrderPassengerInfo> orderPassengerInfoList) {
		this.orderPassengerInfoList = orderPassengerInfoList;
	}
}
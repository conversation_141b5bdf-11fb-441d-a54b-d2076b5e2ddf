package com.juneyaoair.webbaseclass.request.booking;

import com.juneyaoair.baseclass.request.geetest.Geetest;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

/**
 * Created by yaocf on 2016/12/20.
 * 属性小写字母开头
 */
@XmlRootElement(name = "TicketBookingRequest")
@XmlAccessorType(XmlAccessType.FIELD)
@AllArgsConstructor
@NoArgsConstructor
public class TicketBookingRequest extends Geetest {
    @NotNull(message="渠道用户号不能为空")
    private String channelCode; //渠道用户号	B2C,CC等
    @NotNull(message="订单号不能为空")
    private String channelOrderNo; //渠道订单号渠道系统中的唯一订单号，不能重复
    @NotNull(message="客户编号不能为空")
    private String channelCustomerNo; //渠道客户编号通过渠道下订单的购买者，在渠道自己系统识别该客户的编号,该值为-1时表示匿名用户
    //@NotNull(message="预订人IP地址不能为空")
    private String orderRequestIp; //预订人IP地址下订单购买者的IP
    @NotNull(message="航程类型不能为空")
    private String routeType; //航程类型单程：OW；往返：RT（RT时请注意飞行方向）
    @NotNull(message="国内国际标识不能为空")
    private String interFlag; //国内国际标识国内：D；国际：I

    private String channelPrivateInfo; //渠道私有信息字段在结果中原样返回
    @NotNull(message="联系人不能为空")
    private String linker; //联系人
    @NotNull(message="手机号国际区号不能为空")
    private String countryTelCode; //手机号国际区号默认值86
    private String linkerTelphone; //联系人电话
    @NotNull(message="联系人手机不能为空")
    @Size(min=6,max=15,message="手机号码 格式错误")
    private String linkerHandphone; //联系人手机
    private String meetName; //接机人姓名
    private String meetPhone; //接机人手机
    private String linkerEMail; //联系人Email英文版必须，需要把乘机信息发邮件给联系人
    @NotNull(message="预定人卡号不能为空")
    private String sfCardNo; //预订人常客卡号HO+卡号
    @NotNull(message="预定人常客ID不能为空")
    private String ffpId; //预订人常客ID
    private String certNo;
    private String ffpCardType; //常客卡类型（Company集团，其它Member）
    private String ffpLevel;
    @NotNull(message="乘客信息不能为空")
    @Size(min=1,message="乘客信息不能少于1人")
    private List<PassengerInfo> passengerInfoList; //乘客信息列表
    @NotNull(message="航线不能为空")
    @Size(min=1,message="航线信息不能少于1段")
    private List<FlightInfo> flightInfoList; //航班航线列表
    //@NotNull(message="设备号不能为空")
    private String deviceId;//设备号
    private Integer useScoreTotal;
    private String couponCode;/// 优惠券
    private int scoreUseMin;/// 积分使用下限
    private int scoreUseMax;/// 积分使用上限
    private int scoreUseMinChd;/// 积分儿童使用下限
    private int scoreUseMaxChd;/// 积分儿童使用上限
    private Boolean buyLounge;/// 是否购买休息室
    private List<LoungeInfo_M> loungeQueryList;/// 休息室
    private Boolean postTripCert;/// 是否邮寄行程单
    private TripCertSendInfo_M tripCertSendInfo;/// 邮寄信息
    private Boolean buyWifi;/// 是否购买wifi
    private List<WifiQuery_M> wifiQueryList;/// wifi购买信息
    //报销用行程单配送信息
    private String loginKeyInfo;
    private String ip;
    private String ipToken;//防止IP注入

    private String webChannelCode;
    private String webChannelCodeToken;

    public String getWebChannelCode() {
        return webChannelCode;
    }

    public void setWebChannelCode(String webChannelCode) {
        this.webChannelCode = webChannelCode;
    }

    public String getWebChannelCodeToken() {
        return webChannelCodeToken;
    }

    public void setWebChannelCodeToken(String webChannelCodeToken) {
        this.webChannelCodeToken = webChannelCodeToken;
    }

    public String getChannelCode() {
        return channelCode;
    }

    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }

    public String getChannelOrderNo() {
        return channelOrderNo;
    }

    public void setChannelOrderNo(String channelOrderNo) {
        this.channelOrderNo = channelOrderNo;
    }

    public String getChannelCustomerNo() {
        return channelCustomerNo;
    }

    public void setChannelCustomerNo(String channelCustomerNo) {
        this.channelCustomerNo = channelCustomerNo;
    }

    public String getOrderRequestIp() {
        return orderRequestIp;
    }

    public void setOrderRequestIp(String orderRequestIp) {
        this.orderRequestIp = orderRequestIp;
    }

    public String getRouteType() {
        return routeType;
    }

    public void setRouteType(String routeType) {
        this.routeType = routeType;
    }

    public String getInterFlag() {
        return interFlag;
    }

    public void setInterFlag(String interFlag) {
        this.interFlag = interFlag;
    }

    public String getChannelPrivateInfo() {
        return channelPrivateInfo;
    }

    public void setChannelPrivateInfo(String channelPrivateInfo) {
        this.channelPrivateInfo = channelPrivateInfo;
    }

    public String getLinker() {
        return linker;
    }

    public void setLinker(String linker) {
        this.linker = linker;
    }

    public String getCountryTelCode() {
        return countryTelCode;
    }

    public void setCountryTelCode(String countryTelCode) {
        this.countryTelCode = countryTelCode;
    }

    public String getLinkerTelphone() {
        return linkerTelphone;
    }

    public void setLinkerTelphone(String linkerTelphone) {
        this.linkerTelphone = linkerTelphone;
    }

    public String getLinkerHandphone() {
        return linkerHandphone;
    }

    public void setLinkerHandphone(String linkerHandphone) {
        this.linkerHandphone = linkerHandphone;
    }

    public String getMeetName() {
        return meetName;
    }

    public void setMeetName(String meetName) {
        this.meetName = meetName;
    }

    public String getMeetPhone() {
        return meetPhone;
    }

    public void setMeetPhone(String meetPhone) {
        this.meetPhone = meetPhone;
    }

    public String getLinkerEMail() {
        return linkerEMail;
    }

    public void setLinkerEMail(String linkerEMail) {
        this.linkerEMail = linkerEMail;
    }

    public String getSfCardNo() {
        return sfCardNo;
    }

    public void setSfCardNo(String sfCardNo) {
        this.sfCardNo = sfCardNo;
    }

    public String getFfpId() {
        return ffpId;
    }

    public void setFfpId(String ffpId) {
        this.ffpId = ffpId;
    }

    public String getCertNo() {
        return certNo;
    }

    public void setCertNo(String certNo) {
        this.certNo = certNo;
    }

    public String getFfpCardType() {
        return ffpCardType;
    }

    public void setFfpCardType(String ffpCardType) {
        this.ffpCardType = ffpCardType;
    }

    public String getFfpLevel() {
        return ffpLevel;
    }

    public void setFfpLevel(String ffpLevel) {
        this.ffpLevel = ffpLevel;
    }

    public List<PassengerInfo> getPassengerInfoList() {
        return passengerInfoList;
    }

    public void setPassengerInfoList(List<PassengerInfo> passengerInfoList) {
        this.passengerInfoList = passengerInfoList;
    }

    public List<FlightInfo> getFlightInfoList() {
        return flightInfoList;
    }

    public void setFlightInfoList(List<FlightInfo> flightInfoList) {
        this.flightInfoList = flightInfoList;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public Integer getUseScoreTotal() {
        return useScoreTotal;
    }

    public void setUseScoreTotal(Integer useScoreTotal) {
        this.useScoreTotal = useScoreTotal;
    }

    public String getCouponCode() {
        return couponCode;
    }

    public void setCouponCode(String couponCode) {
        this.couponCode = couponCode;
    }

    public int getScoreUseMin() {
        return scoreUseMin;
    }

    public void setScoreUseMin(int scoreUseMin) {
        this.scoreUseMin = scoreUseMin;
    }

    public int getScoreUseMax() {
        return scoreUseMax;
    }

    public void setScoreUseMax(int scoreUseMax) {
        this.scoreUseMax = scoreUseMax;
    }

    public int getScoreUseMinChd() {
        return scoreUseMinChd;
    }

    public void setScoreUseMinChd(int scoreUseMinChd) {
        this.scoreUseMinChd = scoreUseMinChd;
    }

    public int getScoreUseMaxChd() {
        return scoreUseMaxChd;
    }

    public void setScoreUseMaxChd(int scoreUseMaxChd) {
        this.scoreUseMaxChd = scoreUseMaxChd;
    }


    public List<LoungeInfo_M> getLoungeQueryList() {
        return loungeQueryList;
    }

    public void setLoungeQueryList(List<LoungeInfo_M> loungeQueryList) {
        this.loungeQueryList = loungeQueryList;
    }

    public Boolean getPostTripCert() {
        return postTripCert;
    }

    public void setPostTripCert(Boolean postTripCert) {
        this.postTripCert = postTripCert;
    }

    public TripCertSendInfo_M getTripCertSendInfo() {
        return tripCertSendInfo;
    }

    public void setTripCertSendInfo(TripCertSendInfo_M tripCertSendInfo) {
        this.tripCertSendInfo = tripCertSendInfo;
    }


    public List<WifiQuery_M> getWifiQueryList() {
        return wifiQueryList;
    }

    public void setWifiQueryList(List<WifiQuery_M> wifiQueryList) {
        this.wifiQueryList = wifiQueryList;
    }

    public String getLoginKeyInfo() {
        return loginKeyInfo;
    }

    public void setLoginKeyInfo(String loginKeyInfo) {
        this.loginKeyInfo = loginKeyInfo;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public Boolean getBuyLounge() {
        return buyLounge;
    }

    public void setBuyLounge(Boolean buyLounge) {
        this.buyLounge = buyLounge;
    }

    public Boolean getBuyWifi() {
        return buyWifi;
    }

    public void setBuyWifi(Boolean buyWifi) {
        this.buyWifi = buyWifi;
    }

    public String getIpToken() {
        return ipToken;
    }

    public void setIpToken(String ipToken) {
        this.ipToken = ipToken;
    }
}

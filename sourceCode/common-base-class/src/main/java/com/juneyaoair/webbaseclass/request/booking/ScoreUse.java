package com.juneyaoair.webbaseclass.request.booking;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * Created by yaocf on 2016/12/20.
 */
@XmlRootElement(name = "ScoreUse")
@XmlAccessorType(XmlAccessType.FIELD)
public class ScoreUse {
    private String scoreUseRuleID; //使用规则ID
    private int useScore; //扣减积分
    private int deductibls; //抵扣金额
    private double pricePaid; //实际支付票价PricePaid = PriceValue - Deductibls
    private double priceValue; //票价
    private String remark; //使用说明

    public String getScoreUseRuleID() {
        return scoreUseRuleID;
    }

    public void setScoreUseRuleID(String scoreUseRuleID) {
        this.scoreUseRuleID = scoreUseRuleID;
    }

    public int getUseScore() {
        return useScore;
    }

    public void setUseScore(int useScore) {
        this.useScore = useScore;
    }

    public int getDeductibls() {
        return deductibls;
    }

    public void setDeductibls(int deductibls) {
        this.deductibls = deductibls;
    }

    public double getPricePaid() {
        return pricePaid;
    }

    public void setPricePaid(double pricePaid) {
        this.pricePaid = pricePaid;
    }

    public double getPriceValue() {
        return priceValue;
    }

    public void setPriceValue(double priceValue) {
        this.priceValue = priceValue;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}

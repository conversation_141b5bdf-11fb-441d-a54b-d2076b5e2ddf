package com.juneyaoair.webbaseclass.request.refund.lounge;

import com.juneyaoair.webbaseclass.request.lounge.WebLoungeBuy;

import javax.validation.constraints.NotNull;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;


@XmlRootElement(name = "WebLoungeRefundRequest")
@XmlAccessorType(XmlAccessType.FIELD)
public class WebLoungeRefundRequest {
    @NotNull(message = "渠道用户号不能为空")
    private String channelCode; //渠道用户号	B2C,CC等
    @NotNull(message = "预定渠道用户号不能为空")
    private String bookChannelCode;
    @NotNull(message = "渠道订单编号不能为空")
    private String channelOrderNo;
    @NotNull(message = "订单编号不能为空")
    private String orderNo;
    @NotNull(message = "客户卡号不能为空")
    private String ffpCardNo; //预订人常客卡号HO+卡号
    @NotNull(message = "客户ID不能为空")
    private String ffpId; //预订人常客ID
    @NotNull(message = "休息室退票信息不能为空")
    private List<WebLoungeBuy> loungeBuyList;
    @NotNull(message = "验证码不能为空")
    private String loginKeyInfo;

    public String getChannelCode() {
        return channelCode;
    }

    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }

    public String getBookChannelCode() {
        return bookChannelCode;
    }

    public void setBookChannelCode(String bookChannelCode) {
        this.bookChannelCode = bookChannelCode;
    }

    public String getChannelOrderNo() {
        return channelOrderNo;
    }

    public void setChannelOrderNo(String channelOrderNo) {
        this.channelOrderNo = channelOrderNo;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getFfpCardNo() {
        return ffpCardNo;
    }

    public void setFfpCardNo(String ffpCardNo) {
        this.ffpCardNo = ffpCardNo;
    }

    public String getFfpId() {
        return ffpId;
    }

    public void setFfpId(String ffpId) {
        this.ffpId = ffpId;
    }

    public List<WebLoungeBuy> getLoungeBuyList() {
        return loungeBuyList;
    }

    public void setLoungeBuyList(List<WebLoungeBuy> loungeBuyList) {
        this.loungeBuyList = loungeBuyList;
    }

    public String getLoginKeyInfo() {
        return loginKeyInfo;
    }

    public void setLoginKeyInfo(String loginKeyInfo) {
        this.loginKeyInfo = loginKeyInfo;
    }
}
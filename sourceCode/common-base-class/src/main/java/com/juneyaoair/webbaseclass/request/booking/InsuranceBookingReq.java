package com.juneyaoair.webbaseclass.request.booking;

import com.juneyaoair.baseclass.request.booking.InsuranceInfo;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

/**
 * Created by yaocf on 2016/12/19.
 */
@XmlRootElement(name = "InsuranceBookingReq")
@XmlAccessorType(XmlAccessType.FIELD)
@AllArgsConstructor
@NoArgsConstructor
public class InsuranceBookingReq {
    @NotNull(message="渠道用户号不能为空")
    private String channelCode; //渠道用户号	B2C,CC等
    @NotNull(message="订单号不能为空")
    private String channelOrderNo; //渠道订单号渠道系统中的唯一订单号，不能重复
    @NotNull(message="客户编号不能为空")
    private String channelCustomerNo; //渠道客户编号通过渠道下订单的购买者，在渠道自己系统识别该客户的编号,该值为-1时表示匿名用户
    @NotNull(message="联系人不能为空")
    private String linker; //联系人
    @NotNull(message="联系人手机不能为空")
    @Size(min=6,max=15,message="手机号码 格式错误")
    private String linkerHandphone; //联系人手机
    @NotNull(message="预定人卡号不能为空")
    private String ffpCardNo; //预订人常客卡号HO+卡号
    @NotNull(message="预定人常客ID不能为空")
    private String ffpId; //预订人常客ID
    @NotNull(message="验证信息不能为空")
    private String loginKeyInfo;
    @NotNull(message="航程类型不能为空")
    private String routeType; //航程类型单程：OW；往返：RT（RT时请注意飞行方向）
    @NotNull(message="国内国际标识不能为空")
    private String interFlag; //国内国际标识国内：D；国际：I
    @NotNull(message="机票信息不能为空")
    private InsuranceTicketInfo_M ticketInfo;
    @NotNull(message="保险信息不能为空")
    private List<InsuranceInfo> insuranceList;/// wifi购买信息
    private String ip;

    public String getChannelCode() {
        return channelCode;
    }

    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }

    public String getChannelOrderNo() {
        return channelOrderNo;
    }

    public void setChannelOrderNo(String channelOrderNo) {
        this.channelOrderNo = channelOrderNo;
    }

    public String getChannelCustomerNo() {
        return channelCustomerNo;
    }

    public void setChannelCustomerNo(String channelCustomerNo) {
        this.channelCustomerNo = channelCustomerNo;
    }

    public String getLinker() {
        return linker;
    }

    public void setLinker(String linker) {
        this.linker = linker;
    }

    public String getLinkerHandphone() {
        return linkerHandphone;
    }

    public void setLinkerHandphone(String linkerHandphone) {
        this.linkerHandphone = linkerHandphone;
    }

    public String getFfpCardNo() {
        return ffpCardNo;
    }

    public void setFfpCardNo(String ffpCardNo) {
        this.ffpCardNo = ffpCardNo;
    }

    public String getFfpId() {
        return ffpId;
    }

    public void setFfpId(String ffpId) {
        this.ffpId = ffpId;
    }

    public String getLoginKeyInfo() {
        return loginKeyInfo;
    }

    public void setLoginKeyInfo(String loginKeyInfo) {
        this.loginKeyInfo = loginKeyInfo;
    }

    public String getRouteType() {
        return routeType;
    }

    public void setRouteType(String routeType) {
        this.routeType = routeType;
    }

    public String getInterFlag() {
        return interFlag;
    }

    public void setInterFlag(String interFlag) {
        this.interFlag = interFlag;
    }

    public InsuranceTicketInfo_M getTicketInfo() {
        return ticketInfo;
    }

    public void setTicketInfo(InsuranceTicketInfo_M ticketInfo) {
        this.ticketInfo = ticketInfo;
    }

    public List<InsuranceInfo> getInsuranceList() {
        return insuranceList;
    }

    public void setInsuranceList(List<InsuranceInfo> insuranceList) {
        this.insuranceList = insuranceList;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }
}

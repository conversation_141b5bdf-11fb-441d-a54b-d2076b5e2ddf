package com.juneyaoair.webbaseclass.request.multiDiscount;

import com.juneyaoair.webbaseclass.request.booking.PassengerInfo;
import com.juneyaoair.webbaseclass.request.booking.FlightInfo;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

/**
 * @Author:zc
 * @Description:
 * @Date:Created in 10:31 2017/10/18
 * @Modified by:
 */
@XmlRootElement(name = "QueryMultiDiscountReq")
@XmlAccessorType(XmlAccessType.FIELD)
public class QueryMultiDiscountReq {
    @NotNull(message = "访问渠道不能为空")
    private String channelCode;
    @NotNull(message = "用户标识不能为空")
    private String ffpId;
    @NotNull(message = "用户卡号不能为空")
    private String ffpCardNo;
    @NotNull(message = "验证信息不能为空")
    private String loginKeyInfo;
    @NotNull(message = "乘客数不能为空")
    private int passengerCount;//乘客数
    @NotNull(message = "机票金额不能为空")
    private int ticketAmount;//机票金额
    @NotNull(message="国内国际标识不能为空")
    private String interFlag; //国内国际标识国内：D；国际：I
    @NotNull(message="乘客信息不能为空")
    @Size(min=1,message="乘客信息不能少于1人")
    private List<PassengerInfo> passengerInfoList; //乘客信息列表
    @NotNull(message="航线不能为空")
    @Size(min=1,message="航线信息不能少于1段")
    private List<FlightInfo> flightInfoList; //航班航线列表

    public int getPassengerCount() {
        return passengerCount;
    }

    public void setPassengerCount(int passengerCount) {
        this.passengerCount = passengerCount;
    }

    public int getTicketAmount() {
        return ticketAmount;
    }

    public void setTicketAmount(int ticketAmount) {
        this.ticketAmount = ticketAmount;
    }

    public String getChannelCode() {
        return channelCode;
    }

    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }

    public String getFfpId() {
        return ffpId;
    }

    public void setFfpId(String ffpId) {
        this.ffpId = ffpId;
    }

    public String getFfpCardNo() {
        return ffpCardNo;
    }

    public void setFfpCardNo(String ffpCardNo) {
        this.ffpCardNo = ffpCardNo;
    }

    public String getLoginKeyInfo() {
        return loginKeyInfo;
    }

    public void setLoginKeyInfo(String loginKeyInfo) {
        this.loginKeyInfo = loginKeyInfo;
    }

    public String getInterFlag() {
        return interFlag;
    }

    public void setInterFlag(String interFlag) {
        this.interFlag = interFlag;
    }

    public List<PassengerInfo> getPassengerInfoList() {
        return passengerInfoList;
    }

    public void setPassengerInfoList(List<PassengerInfo> passengerInfoList) {
        this.passengerInfoList = passengerInfoList;
    }

    public List<FlightInfo> getFlightInfoList() {
        return flightInfoList;
    }

    public void setFlightInfoList(List<FlightInfo> flightInfoList) {
        this.flightInfoList = flightInfoList;
    }
}

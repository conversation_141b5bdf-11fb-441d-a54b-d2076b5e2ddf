package com.juneyaoair.webbaseclass.request.coupon;

import com.juneyaoair.webbaseclass.request.booking.FlightInfo;

import javax.validation.constraints.NotNull;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

/**
 * Created by yaocf on 2016/12/20.
 */
@XmlRootElement(name = "QueryCouponReq")
@XmlAccessorType(XmlAccessType.FIELD)
public class QueryCouponReq {
    @NotNull(message="渠道用户号不能为空")
    private String channelCode; //渠道用户号	B2C,CC等
    @NotNull(message="用户ID号不能为空")
    private String ffpId;
    @NotNull(message="用户卡号不能为空")
    private String ffpCardNo;
    @NotNull(message="验证码不能为空")
    private String loginKeyInfo;
    private  String couponState;//R-已领取;U-已使用; E-过期;
    private  String couponNo;
    private List<FlightInfo> flightInfoList;
    private  String startDate;//2014-04-01
    private  String endDate;//2014-04-01
    private  Double amount;
    private  String clientVersionNo;//当前客户端访问版本

    public String getChannelCode() {
        return channelCode;
    }

    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }

    public String getFfpId() {
        return ffpId;
    }

    public void setFfpId(String ffpId) {
        this.ffpId = ffpId;
    }

    public String getFfpCardNo() {
        return ffpCardNo;
    }

    public void setFfpCardNo(String ffpCardNo) {
        this.ffpCardNo = ffpCardNo;
    }

    public String getLoginKeyInfo() {
        return loginKeyInfo;
    }

    public void setLoginKeyInfo(String loginKeyInfo) {
        this.loginKeyInfo = loginKeyInfo;
    }

    public String getCouponState() {
        return couponState;
    }

    public void setCouponState(String couponState) {
        this.couponState = couponState;
    }

    public String getCouponNo() {
        return couponNo;
    }

    public void setCouponNo(String couponNo) {
        this.couponNo = couponNo;
    }

    public List<FlightInfo> getFlightInfoList() {
        return flightInfoList;
    }

    public void setFlightInfoList(List<FlightInfo> flightInfoList) {
        this.flightInfoList = flightInfoList;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    public String getClientVersionNo() {
        return clientVersionNo;
    }

    public void setClientVersionNo(String clientVersionNo) {
        this.clientVersionNo = clientVersionNo;
    }
}

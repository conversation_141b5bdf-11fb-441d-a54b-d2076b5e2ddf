package com.juneyaoair.webbaseclass.request.booking;

import com.juneyaoair.baseclass.request.booking.LoungeInfo;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

@XmlRootElement(name = "GuestBuyLoungeReq")
@XmlAccessorType(XmlAccessType.FIELD)
public class GuestBuyLoungeReq {
    @NotNull(message = "渠道用户号不能为空")
    private String channelCode;
    @NotNull(message = "预定人常客ID不能为空")
    private String ffpId;
    @NotNull(message = "票号不能为空")
    private String tktNo;
    @NotNull(message = "订单号不能为空")
    private String channelOrderNo;
    private String flightDate;
    @NotNull(message = "客户编号不能为空")
    private String channelCustomerNo;
    @Min(value = 0, message = "wifi金额需大于0")
    private Double payAmount;
    @NotNull(message = "联系人不能为空")
    private String linker;
    @NotNull(message = "联系人手机不能为空")
    @Size(min = 6, max = 15, message = "手机号码 格式错误")
    private String linkerHandphone;
    @NotNull(message = "预定人卡号不能为空")
    private String ffpCardNo;
    @NotNull(message = "验证信息不能为空")
    private String loginKeyInfo;
    @NotNull(message = "休息室信息不能为空")
    private List<LoungeInfo_M> loungeList;

    public String getChannelCode() {
        return channelCode;
    }

    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }

    public String getFfpId() {
        return ffpId;
    }

    public void setFfpId(String ffpId) {
        this.ffpId = ffpId;
    }

    public String getTktNo() {
        return tktNo;
    }

    public void setTktNo(String tktNo) {
        this.tktNo = tktNo;
    }

    public String getChannelOrderNo() {
        return channelOrderNo;
    }

    public void setChannelOrderNo(String channelOrderNo) {
        this.channelOrderNo = channelOrderNo;
    }

    public String getFlightDate() {
        return flightDate;
    }

    public void setFlightDate(String flightDate) {
        this.flightDate = flightDate;
    }

    public String getChannelCustomerNo() {
        return channelCustomerNo;
    }

    public void setChannelCustomerNo(String channelCustomerNo) {
        this.channelCustomerNo = channelCustomerNo;
    }

    public Double getPayAmount() {
        return payAmount;
    }

    public void setPayAmount(Double payAmount) {
        this.payAmount = payAmount;
    }

    public String getLinker() {
        return linker;
    }

    public void setLinker(String linker) {
        this.linker = linker;
    }

    public String getLinkerHandphone() {
        return linkerHandphone;
    }

    public void setLinkerHandphone(String linkerHandphone) {
        this.linkerHandphone = linkerHandphone;
    }

    public String getFfpCardNo() {
        return ffpCardNo;
    }

    public void setFfpCardNo(String ffpCardNo) {
        this.ffpCardNo = ffpCardNo;
    }

    public String getLoginKeyInfo() {
        return loginKeyInfo;
    }

    public void setLoginKeyInfo(String loginKeyInfo) {
        this.loginKeyInfo = loginKeyInfo;
    }

    public List<LoungeInfo_M> getLoungeList() {
        return loungeList;
    }

    public void setLoungeList(List<LoungeInfo_M> loungeList) {
        this.loungeList = loungeList;
    }
}

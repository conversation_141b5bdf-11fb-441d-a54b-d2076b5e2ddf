package com.juneyaoair.webbaseclass.request.booking;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import com.juneyaoair.baseclass.request.booking.InsuranceInfo;
import com.juneyaoair.baseclass.request.booking.MealProduct;
import com.juneyaoair.baseclass.request.booking.WeightProduct;

import java.util.List;

@XmlRootElement(name = "passengerInfo")
@XmlAccessorType(XmlAccessType.FIELD)
public class PassengerInfo {
    private int passengerNO; //乘客序号第一位乘客为0开始，第二位为1，依次增加1
    private String passengerName; //乘客姓名
    private String passengerType; //乘客类型ADT － 成人，CHD － 儿童，INF － 婴儿
    private String passengerIdentity; //乘客身份VIP，教师，警殘，革伤
    private String certType; //证件类型身份证:NI,护照:PP,其它证件:CC
    private String certNo; //证件号码
    private String ffCardNo; //常客卡号HO+卡号
    private String countryTelCode; //手机号国际区号默认值86
    private String handphoneNo; //手机号购买保险必填
    private double ticketPrice; //票面价为Price.RSP销售参考价加总
    private double pricePaid; //实际支付票价当有积分支付时的实际支付票价是Price.PriceValue - 积分抵用金额
    private double yQTax; //燃油费
    private double cNTax; //建设税
    private double otherTax; //其它税费
    private double qFee; //Q费
    private String adtNameToInf; //婴儿绑定的成人姓名
    private String birthdate; //出生日期yyyy-MM-dd儿童、婴儿和国际票必填
    private String sex; //性别国际票必填
    private String nationality; //国籍国际票必填
    private String belongCountry; //发证国国际票必填
    private String certValidity; //证件有效期yyyy-MM-dd国际票必填
    private String isBuyInsurance; //是否购买保险Y - 购买，N - 未购买
    private double insuranceAmount;
    private List<InsuranceInfo> insuranceList; //保险信息列表保险购买时只能按人购买
    //private ScoreUse[] scoreUseInfoList; //积分使用信息列表
    private ScoreGift scoreGiftInfo; //积分赠送信息
    private String isSaveCommon;
    private String saCardNo;//星盟卡号 XX+卡号
    //2017-12-15 新增
    private String gjCertType;//军警证件类型  //选择军警证件类时使用 NI
    private String gjCertNo;//军警证件号码
    private String isMeal;//是否选择餐食  Y-购买   N-未购买
    private List<MealProduct> mealProductList;//餐食信息列表  选择时只能按人航段，如果两个说明，按顺序为两个航段
    private List<WeightProduct> weightProductList;//逾重行李列表 选择时只能按人航段，如果两个说明，按顺序为两个航段

    public PassengerInfo() {
    }

    public String getSaCardNo() {
        return saCardNo;
    }

    public void setSaCardNo(String saCardNo) {
        this.saCardNo = saCardNo;
    }

    public int getPassengerNO() {
        return passengerNO;
    }

    public void setPassengerNO(int passengerNO) {
        this.passengerNO = passengerNO;
    }

    public String getPassengerName() {
        return passengerName;
    }

    public void setPassengerName(String passengerName) {
        this.passengerName = passengerName;
    }

    public String getPassengerType() {
        return passengerType;
    }

    public void setPassengerType(String passengerType) {
        this.passengerType = passengerType;
    }

    public String getPassengerIdentity() {
        return passengerIdentity;
    }

    public void setPassengerIdentity(String passengerIdentity) {
        this.passengerIdentity = passengerIdentity;
    }

    public String getCertType() {
        return certType;
    }

    public void setCertType(String certType) {
        this.certType = certType;
    }

    public String getCertNo() {
        return certNo;
    }

    public void setCertNo(String certNo) {
        this.certNo = certNo;
    }

    public String getFfCardNo() {
        return ffCardNo;
    }

    public void setFfCardNo(String ffCardNo) {
        this.ffCardNo = ffCardNo;
    }

    public String getCountryTelCode() {
        return countryTelCode;
    }

    public void setCountryTelCode(String countryTelCode) {
        this.countryTelCode = countryTelCode;
    }

    public String getHandphoneNo() {
        return handphoneNo;
    }

    public void setHandphoneNo(String handphoneNo) {
        this.handphoneNo = handphoneNo;
    }

    public double getTicketPrice() {
        return ticketPrice;
    }

    public void setTicketPrice(double ticketPrice) {
        this.ticketPrice = ticketPrice;
    }

    public double getPricePaid() {
        return pricePaid;
    }

    public void setPricePaid(double pricePaid) {
        this.pricePaid = pricePaid;
    }

    public double getyQTax() {
        return yQTax;
    }

    public void setyQTax(double yQTax) {
        this.yQTax = yQTax;
    }

    public double getcNTax() {
        return cNTax;
    }

    public void setcNTax(double cNTax) {
        this.cNTax = cNTax;
    }

    public double getOtherTax() {
        return otherTax;
    }

    public void setOtherTax(double otherTax) {
        this.otherTax = otherTax;
    }

    public double getqFee() {
        return qFee;
    }

    public void setqFee(double qFee) {
        this.qFee = qFee;
    }

    public String getAdtNameToInf() {
        return adtNameToInf;
    }

    public void setAdtNameToInf(String adtNameToInf) {
        this.adtNameToInf = adtNameToInf;
    }

    public String getBirthdate() {
        return birthdate;
    }

    public void setBirthdate(String birthdate) {
        this.birthdate = birthdate;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getNationality() {
        return nationality;
    }

    public void setNationality(String nationality) {
        this.nationality = nationality;
    }

    public String getBelongCountry() {
        return belongCountry;
    }

    public void setBelongCountry(String belongCountry) {
        this.belongCountry = belongCountry;
    }

    public String getCertValidity() {
        return certValidity;
    }

    public void setCertValidity(String certValidity) {
        this.certValidity = certValidity;
    }

    public String getIsBuyInsurance() {
        return isBuyInsurance;
    }

    public void setIsBuyInsurance(String isBuyInsurance) {
        this.isBuyInsurance = isBuyInsurance;
    }

    public double getInsuranceAmount() {
        return insuranceAmount;
    }

    public void setInsuranceAmount(double insuranceAmount) {
        this.insuranceAmount = insuranceAmount;
    }

    public List<InsuranceInfo> getInsuranceList() {
        return insuranceList;
    }

    public void setInsuranceList(List<InsuranceInfo> insuranceList) {
        this.insuranceList = insuranceList;
    }

    public ScoreGift getScoreGiftInfo() {
        return scoreGiftInfo;
    }

    public void setScoreGiftInfo(ScoreGift scoreGiftInfo) {
        this.scoreGiftInfo = scoreGiftInfo;
    }

    public String getIsSaveCommon() {
        return isSaveCommon;
    }

    public void setIsSaveCommon(String isSaveCommon) {
        this.isSaveCommon = isSaveCommon;
    }

    public String getGjCertType() {
        return gjCertType;
    }

    public void setGjCertType(String gjCertType) {
        this.gjCertType = gjCertType;
    }

    public String getGjCertNo() {
        return gjCertNo;
    }

    public void setGjCertNo(String gjCertNo) {
        this.gjCertNo = gjCertNo;
    }

    public String getIsMeal() {
        return isMeal;
    }

    public void setIsMeal(String isMeal) {
        this.isMeal = isMeal;
    }

    public List<MealProduct> getMealProductList() {
        return mealProductList;
    }

    public void setMealProductList(List<MealProduct> mealProductList) {
        this.mealProductList = mealProductList;
    }

    public List<WeightProduct> getWeightProductList() {
        return weightProductList;
    }

    public void setWeightProductList(List<WeightProduct> weightProductList) {
        this.weightProductList = weightProductList;
    }
}
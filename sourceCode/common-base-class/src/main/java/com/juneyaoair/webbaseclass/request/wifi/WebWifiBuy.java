package com.juneyaoair.webbaseclass.request.wifi;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * Created by qinxiaoming on 2016-6-2.
 */
@XmlRootElement(name = "WebWifiBuy")
@XmlAccessorType(XmlAccessType.FIELD)
public class WebWifiBuy {

    private String wifiProductId;

    private String wifiProductName;

    private String flightNo;

    private String takeDate;

    private String returnDate;

    private String handphoneNo;

    private String passengerName;

    private String remark;

    private String certNo;

    private Double wifiOrgAmount;

    private Double wifiAmount;

    private Double wifiAmountSingle;

    private int maxUseScore;

    private int prePurchaseDay;

    private int minRentDay;

    private String wifiFetchAddress;

    private String wifiReturnAddress;

    private Double orderAmount;

    private int purchaseQuantity;

    private String wifiState;

    private String orderDatetime;

    private int useScore;

    private Double wifiPerDayAmount;

    public WebWifiBuy() {
    }

    public String getWifiProductId() {
        return wifiProductId;
    }

    public void setWifiProductId(String wifiProductId) {
        this.wifiProductId = wifiProductId;
    }

    public String getWifiProductName() {
        return wifiProductName;
    }

    public void setWifiProductName(String wifiProductName) {
        this.wifiProductName = wifiProductName;
    }

    public String getFlightNo() {
        return flightNo;
    }

    public void setFlightNo(String flightNo) {
        this.flightNo = flightNo;
    }

    public String getTakeDate() {
        return takeDate;
    }

    public void setTakeDate(String takeDate) {
        this.takeDate = takeDate;
    }

    public String getReturnDate() {
        return returnDate;
    }

    public void setReturnDate(String returnDate) {
        this.returnDate = returnDate;
    }

    public String getHandphoneNo() {
        return handphoneNo;
    }

    public void setHandphoneNo(String handphoneNo) {
        this.handphoneNo = handphoneNo;
    }

    public String getPassengerName() {
        return passengerName;
    }

    public void setPassengerName(String passengerName) {
        this.passengerName = passengerName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCertNo() {
        return certNo;
    }

    public void setCertNo(String certNo) {
        this.certNo = certNo;
    }

    public Double getWifiOrgAmount() {
        return wifiOrgAmount;
    }

    public void setWifiOrgAmount(Double wifiOrgAmount) {
        this.wifiOrgAmount = wifiOrgAmount;
    }

    public Double getWifiAmount() {
        return wifiAmount;
    }

    public void setWifiAmount(Double wifiAmount) {
        this.wifiAmount = wifiAmount;
    }

    public Double getWifiAmountSingle() {
        return wifiAmountSingle;
    }

    public void setWifiAmountSingle(Double wifiAmountSingle) {
        this.wifiAmountSingle = wifiAmountSingle;
    }

    public int getMaxUseScore() {
        return maxUseScore;
    }

    public void setMaxUseScore(int maxUseScore) {
        this.maxUseScore = maxUseScore;
    }

    public int getPrePurchaseDay() {
        return prePurchaseDay;
    }

    public void setPrePurchaseDay(int prePurchaseDay) {
        this.prePurchaseDay = prePurchaseDay;
    }

    public int getMinRentDay() {
        return minRentDay;
    }

    public void setMinRentDay(int minRentDay) {
        this.minRentDay = minRentDay;
    }

    public String getWifiFetchAddress() {
        return wifiFetchAddress;
    }

    public void setWifiFetchAddress(String wifiFetchAddress) {
        this.wifiFetchAddress = wifiFetchAddress;
    }

    public String getWifiReturnAddress() {
        return wifiReturnAddress;
    }

    public void setWifiReturnAddress(String wifiReturnAddress) {
        this.wifiReturnAddress = wifiReturnAddress;
    }

    public Double getOrderAmount() {
        return orderAmount;
    }

    public void setOrderAmount(Double orderAmount) {
        this.orderAmount = orderAmount;
    }

    public int getPurchaseQuantity() {
        return purchaseQuantity;
    }

    public void setPurchaseQuantity(int purchaseQuantity) {
        this.purchaseQuantity = purchaseQuantity;
    }

    public String getWifiState() {
        return wifiState;
    }

    public void setWifiState(String wifiState) {
        this.wifiState = wifiState;
    }

    public String getOrderDatetime() {
        return orderDatetime;
    }

    public void setOrderDatetime(String orderDatetime) {
        this.orderDatetime = orderDatetime;
    }

    public int getUseScore() {
        return useScore;
    }

    public void setUseScore(int useScore) {
        this.useScore = useScore;
    }

    public Double getWifiPerDayAmount() {
        return wifiPerDayAmount;
    }

    public void setWifiPerDayAmount(Double wifiPerDayAmount) {
        this.wifiPerDayAmount = wifiPerDayAmount;
    }
}

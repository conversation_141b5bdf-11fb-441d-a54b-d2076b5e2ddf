package com.juneyaoair.webbaseclass.request.booking;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * Created by yaocf on 2016/12/20.
 */
@XmlRootElement(name = "CabinFare")
@XmlAccessorType(XmlAccessType.FIELD)
public class CabinFare {
    private String priceShowType;
    private String id;
    private String cabinCode; //舱位
    private String cabinClass; //舱位等级
    private String cabinNumber; //舱位数量
    private String priceProductType; //运价产品类型1 - 公布,2 - 私有,3 - 多程惠达，4 - 中转联程
    private String priceRouteType; //运价航程类型OW - 单程,RT - 往返
    private String passengerType; //乘客类型ADT － 成人，CHD － 儿童，INF － 婴儿
    private String fareBasis; //运价基础
    private String tourCode; //旅行代码
    private String discount; //折扣率与Y公布运价的比率,如:值为10表示10%
    private String ei; //限制条件写入PNR的签注信息
    private String comment; //备注项用于前端显示说明信息，如：解释签注信息或备注中转联程提供住宿
    private String baggage; //行李重量计重制:20KG 计件制:2PC
    private String validityPeriod; //客票有效期(最长停留时间)有效取值范围:1Y或1M或1D 该项值为空时表示没有限制，有值时需要与航班日期进行计算
    private String minStay; //最短停留时间1Y1M1D 该项值为空时表示没有限制，有值时需要与航班日期进行计算
    private double priceValue; //票价票价金额为-1时是无效票价不能进行销售,使用积分时此价格为PriceValue - Deductibls
    private double rsp; //销售参考价用于前端宣传打折效果使用,当值为-1时表示找不到有效的销售参考价
    private boolean  refundedFlag; //是否可退票
    private String refundedComment; //退票政策描述说明退票费收取或计算方法
    private String changedComment; //变更政策描述说明改期、签转和升舱政策
    private boolean  rescheduledFlag; //是否可免费改期
    private int freeChangeTimes; //免费改期次数免费改期不限次数时，目前运价系统会给出99次
    private boolean changeAirLineFlag; //是否可改签
    private boolean upgradeFlag; //是否可升舱
    private double yprice; //经济舱公布运价全价金额
    private String fareID; //运价编号运价系统中该条运价的主键，计算退票费时有用
    private double yqtax; //燃油费国内航程时该字段有效,国际时该字段无效值为-1
    private double cntax; //建设税国内航程时该字段有效,国际时该字段无效值为-1
    private	String	dynamicCabin;
    private	String	dynamicFareID;
    private	String  combineId;
    private String fareSign; //运价验证串航班查询结果中Price.FareSign字段
    private CombineRule combineRuleInfo;
    private ScoreUse[] scoreUseInfoList; //积分使用信息列表
    private ScoreGift scoreGiftInfo; //积分赠送信息
    private int intDiscount;

    public String getPriceShowType() {
        return priceShowType;
    }

    public void setPriceShowType(String priceShowType) {
        this.priceShowType = priceShowType;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCabinCode() {
        return cabinCode;
    }

    public void setCabinCode(String cabinCode) {
        this.cabinCode = cabinCode;
    }

    public String getCabinClass() {
        return cabinClass;
    }

    public void setCabinClass(String cabinClass) {
        this.cabinClass = cabinClass;
    }

    public String getCabinNumber() {
        return cabinNumber;
    }

    public void setCabinNumber(String cabinNumber) {
        this.cabinNumber = cabinNumber;
    }

    public String getPriceProductType() {
        return priceProductType;
    }

    public void setPriceProductType(String priceProductType) {
        this.priceProductType = priceProductType;
    }

    public String getPriceRouteType() {
        return priceRouteType;
    }

    public void setPriceRouteType(String priceRouteType) {
        this.priceRouteType = priceRouteType;
    }

    public String getPassengerType() {
        return passengerType;
    }

    public void setPassengerType(String passengerType) {
        this.passengerType = passengerType;
    }

    public String getFareBasis() {
        return fareBasis;
    }

    public void setFareBasis(String fareBasis) {
        this.fareBasis = fareBasis;
    }

    public String getTourCode() {
        return tourCode;
    }

    public void setTourCode(String tourCode) {
        this.tourCode = tourCode;
    }

    public String getDiscount() {
        return discount;
    }

    public void setDiscount(String discount) {
        this.discount = discount;
    }

    public String getEi() {
        return ei;
    }

    public void setEi(String ei) {
        this.ei = ei;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public String getBaggage() {
        return baggage;
    }

    public void setBaggage(String baggage) {
        this.baggage = baggage;
    }

    public String getValidityPeriod() {
        return validityPeriod;
    }

    public void setValidityPeriod(String validityPeriod) {
        this.validityPeriod = validityPeriod;
    }

    public String getMinStay() {
        return minStay;
    }

    public void setMinStay(String minStay) {
        this.minStay = minStay;
    }

    public double getPriceValue() {
        return priceValue;
    }

    public void setPriceValue(double priceValue) {
        this.priceValue = priceValue;
    }

    public double getRsp() {
        return rsp;
    }

    public void setRsp(double rsp) {
        this.rsp = rsp;
    }

    public boolean isRefundedFlag() {
        return refundedFlag;
    }

    public void setRefundedFlag(boolean refundedFlag) {
        this.refundedFlag = refundedFlag;
    }

    public String getRefundedComment() {
        return refundedComment;
    }

    public void setRefundedComment(String refundedComment) {
        this.refundedComment = refundedComment;
    }

    public String getChangedComment() {
        return changedComment;
    }

    public void setChangedComment(String changedComment) {
        this.changedComment = changedComment;
    }

    public boolean isRescheduledFlag() {
        return rescheduledFlag;
    }

    public void setRescheduledFlag(boolean rescheduledFlag) {
        this.rescheduledFlag = rescheduledFlag;
    }

    public int getFreeChangeTimes() {
        return freeChangeTimes;
    }

    public void setFreeChangeTimes(int freeChangeTimes) {
        this.freeChangeTimes = freeChangeTimes;
    }

    public boolean isChangeAirLineFlag() {
        return changeAirLineFlag;
    }

    public void setChangeAirLineFlag(boolean changeAirLineFlag) {
        this.changeAirLineFlag = changeAirLineFlag;
    }

    public boolean isUpgradeFlag() {
        return upgradeFlag;
    }

    public void setUpgradeFlag(boolean upgradeFlag) {
        this.upgradeFlag = upgradeFlag;
    }

    public double getYprice() {
        return yprice;
    }

    public void setYprice(double yprice) {
        this.yprice = yprice;
    }

    public String getFareID() {
        return fareID;
    }

    public void setFareID(String fareID) {
        this.fareID = fareID;
    }

    public double getYqtax() {
        return yqtax;
    }

    public void setYqtax(double yqtax) {
        this.yqtax = yqtax;
    }

    public double getCntax() {
        return cntax;
    }

    public void setCntax(double cntax) {
        this.cntax = cntax;
    }

    public String getDynamicCabin() {
        return dynamicCabin;
    }

    public void setDynamicCabin(String dynamicCabin) {
        this.dynamicCabin = dynamicCabin;
    }

    public String getDynamicFareID() {
        return dynamicFareID;
    }

    public void setDynamicFareID(String dynamicFareID) {
        this.dynamicFareID = dynamicFareID;
    }

    public String getCombineId() {
        return combineId;
    }

    public void setCombineId(String combineId) {
        this.combineId = combineId;
    }

    public String getFareSign() {
        return fareSign;
    }

    public void setFareSign(String fareSign) {
        this.fareSign = fareSign;
    }

    public CombineRule getCombineRuleInfo() {
        return combineRuleInfo;
    }

    public void setCombineRuleInfo(CombineRule combineRuleInfo) {
        this.combineRuleInfo = combineRuleInfo;
    }

    public ScoreUse[] getScoreUseInfoList() {
        return scoreUseInfoList;
    }

    public void setScoreUseInfoList(ScoreUse[] scoreUseInfoList) {
        this.scoreUseInfoList = scoreUseInfoList;
    }

    public ScoreGift getScoreGiftInfo() {
        return scoreGiftInfo;
    }

    public void setScoreGiftInfo(ScoreGift scoreGiftInfo) {
        this.scoreGiftInfo = scoreGiftInfo;
    }

    public int getIntDiscount() {
        return intDiscount;
    }

    public void setIntDiscount(int intDiscount) {
        this.intDiscount = intDiscount;
    }
}

package com.juneyaoair.webbaseclass.request.booking;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

/**
 * Created by yaocf on 2016/12/19.
 */
@XmlRootElement(name = "InsuranceTicketInfo_M")
@XmlAccessorType(XmlAccessType.FIELD)
public class InsuranceTicketInfo_M {
    private boolean isHasMoreTax; //是否有更多税需要DETR:X提取的税项
    private boolean isIT; //是否IT票
    private String eqviuCurrencyType; //等值支付货币
    private String eqviuFare; //等值支付金额
    private String eTicketType; //电子客票类型
    private String currencyType; //货币类型
    private String dstCity; //终点城市
    private String exchangeInfo; //改签信息
    private Double fare; //票价
    private String fareCompute; //票价计算信息
    private String followTicketNo; //后续票号
    private String iSI; //ISI信息
    private String issueAirline; //出票航空公司
    private String orgCity; //始发城市
    private String originalIssue; //OI信息
    private String passengerName; //旅客姓名
    private String payMethod; //支付方式
    private String signingInfo; //签注信息
    private Double tax; //税款金额
    private String ticketNo; //票号
    private Double totalAmount; //客票总金额
    private String tourCode; //旅游代码
    private String currencyTypeTotal; //票面总价的货币类型
    private String isReceiptPrinted; //是否已打印T4（发票）联
    private String remark; //原始主机返回信息
    private String infantBirthday; //无人陪伴儿童年龄yyyy-MM-dd
    private String passengerType; //旅客类型
    private String unaccompaniedChildAge; //无人陪伴儿童年龄
    private String iataNo; //出票的Iata号
    private String issueDate; //出票时间yyyy-MM-dd HH:mi
    private String passengerID; //
    private String isFromDomestic; /// 是否国内起飞
    private String interFlag ;// 是否国际航班
    private List<SegmentInfo_M> segmentInfoList; //航段信息列表
    private List<IdentityInfo_M> identityInfoList; //证件信息列表
    private List<TaxInfo_M> taxInfoList; //税费信息列表
    private String resultCode; //结果代码1001 － 成功，其它失败
    private String errorInfo; //错误信息

    public InsuranceTicketInfo_M() {
    }

    public boolean isHasMoreTax() {
        return isHasMoreTax;
    }

    public void setHasMoreTax(boolean hasMoreTax) {
        isHasMoreTax = hasMoreTax;
    }

    public boolean isIT() {
        return isIT;
    }

    public void setIT(boolean IT) {
        isIT = IT;
    }

    public String getEqviuCurrencyType() {
        return eqviuCurrencyType;
    }

    public void setEqviuCurrencyType(String eqviuCurrencyType) {
        this.eqviuCurrencyType = eqviuCurrencyType;
    }

    public String getEqviuFare() {
        return eqviuFare;
    }

    public void setEqviuFare(String eqviuFare) {
        this.eqviuFare = eqviuFare;
    }

    public String geteTicketType() {
        return eTicketType;
    }

    public void seteTicketType(String eTicketType) {
        this.eTicketType = eTicketType;
    }

    public String getCurrencyType() {
        return currencyType;
    }

    public void setCurrencyType(String currencyType) {
        this.currencyType = currencyType;
    }

    public String getDstCity() {
        return dstCity;
    }

    public void setDstCity(String dstCity) {
        this.dstCity = dstCity;
    }

    public String getExchangeInfo() {
        return exchangeInfo;
    }

    public void setExchangeInfo(String exchangeInfo) {
        this.exchangeInfo = exchangeInfo;
    }

    public Double getFare() {
        return fare;
    }

    public void setFare(Double fare) {
        this.fare = fare;
    }

    public String getFareCompute() {
        return fareCompute;
    }

    public void setFareCompute(String fareCompute) {
        this.fareCompute = fareCompute;
    }

    public String getFollowTicketNo() {
        return followTicketNo;
    }

    public void setFollowTicketNo(String followTicketNo) {
        this.followTicketNo = followTicketNo;
    }

    public String getiSI() {
        return iSI;
    }

    public void setiSI(String iSI) {
        this.iSI = iSI;
    }

    public String getIssueAirline() {
        return issueAirline;
    }

    public void setIssueAirline(String issueAirline) {
        this.issueAirline = issueAirline;
    }

    public String getOrgCity() {
        return orgCity;
    }

    public void setOrgCity(String orgCity) {
        this.orgCity = orgCity;
    }

    public String getOriginalIssue() {
        return originalIssue;
    }

    public void setOriginalIssue(String originalIssue) {
        this.originalIssue = originalIssue;
    }

    public String getPassengerName() {
        return passengerName;
    }

    public void setPassengerName(String passengerName) {
        this.passengerName = passengerName;
    }

    public String getPayMethod() {
        return payMethod;
    }

    public void setPayMethod(String payMethod) {
        this.payMethod = payMethod;
    }

    public String getSigningInfo() {
        return signingInfo;
    }

    public void setSigningInfo(String signingInfo) {
        this.signingInfo = signingInfo;
    }

    public Double getTax() {
        return tax;
    }

    public void setTax(Double tax) {
        this.tax = tax;
    }

    public String getTicketNo() {
        return ticketNo;
    }

    public void setTicketNo(String ticketNo) {
        this.ticketNo = ticketNo;
    }

    public Double getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(Double totalAmount) {
        this.totalAmount = totalAmount;
    }

    public String getTourCode() {
        return tourCode;
    }

    public void setTourCode(String tourCode) {
        this.tourCode = tourCode;
    }

    public String getCurrencyTypeTotal() {
        return currencyTypeTotal;
    }

    public void setCurrencyTypeTotal(String currencyTypeTotal) {
        this.currencyTypeTotal = currencyTypeTotal;
    }

    public String getIsReceiptPrinted() {
        return isReceiptPrinted;
    }

    public void setIsReceiptPrinted(String isReceiptPrinted) {
        this.isReceiptPrinted = isReceiptPrinted;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getInfantBirthday() {
        return infantBirthday;
    }

    public void setInfantBirthday(String infantBirthday) {
        this.infantBirthday = infantBirthday;
    }

    public String getPassengerType() {
        return passengerType;
    }

    public void setPassengerType(String passengerType) {
        this.passengerType = passengerType;
    }

    public String getUnaccompaniedChildAge() {
        return unaccompaniedChildAge;
    }

    public void setUnaccompaniedChildAge(String unaccompaniedChildAge) {
        this.unaccompaniedChildAge = unaccompaniedChildAge;
    }

    public String getIataNo() {
        return iataNo;
    }

    public void setIataNo(String iataNo) {
        this.iataNo = iataNo;
    }

    public String getIssueDate() {
        return issueDate;
    }

    public void setIssueDate(String issueDate) {
        this.issueDate = issueDate;
    }

    public String getPassengerID() {
        return passengerID;
    }

    public void setPassengerID(String passengerID) {
        this.passengerID = passengerID;
    }

    public String getIsFromDomestic() {
        return isFromDomestic;
    }

    public void setIsFromDomestic(String isFromDomestic) {
        this.isFromDomestic = isFromDomestic;
    }

    public String getInterFlag() {
        return interFlag;
    }

    public void setInterFlag(String interFlag) {
        this.interFlag = interFlag;
    }

    public List<SegmentInfo_M> getSegmentInfoList() {
        return segmentInfoList;
    }

    public void setSegmentInfoList(List<SegmentInfo_M> segmentInfoList) {
        this.segmentInfoList = segmentInfoList;
    }

    public List<IdentityInfo_M> getIdentityInfoList() {
        return identityInfoList;
    }

    public void setIdentityInfoList(List<IdentityInfo_M> identityInfoList) {
        this.identityInfoList = identityInfoList;
    }

    public List<TaxInfo_M> getTaxInfoList() {
        return taxInfoList;
    }

    public void setTaxInfoList(List<TaxInfo_M> taxInfoList) {
        this.taxInfoList = taxInfoList;
    }

    public String getResultCode() {
        return resultCode;
    }

    public void setResultCode(String resultCode) {
        this.resultCode = resultCode;
    }

    public String getErrorInfo() {
        return errorInfo;
    }

    public void setErrorInfo(String errorInfo) {
        this.errorInfo = errorInfo;
    }
}

package com.juneyaoair.webbaseclass.request.insure;

import javax.xml.bind.annotation.XmlRootElement;

/**
 * Created by yaocf on 2016/12/19.
 */
@XmlRootElement(name = "InsuranceBind_M")
public class InsuranceBind_M {

    /// <summary>
    /// 协议产品ID
    /// </summary>
    private String insId ;
    /// <summary>
    /// 保险产品名称
    /// </summary>
    private String insNm ;
    /// <summary>
    /// 保险产品代码
    /// </summary>
    private String insCd ;
    /// <summary>
    /// 保险描述
    /// </summary>
    private String insDesc ;
    /// <summary>
    /// 保险描述URL
    /// </summary>
    private String insDescURL ;
    /// <summary>
    /// 保额
    /// </summary>
    private Double insAmt ;
    /// <summary>
    /// 保费
    /// </summary>
    private Double insTax ;
    /// <summary>
    /// 适用成人
    /// </summary>
    private String isAdt ;
    /// <summary>
    /// 适用儿童
    /// </summary>
    private String isChd ;
    /// <summary>
    /// 适用婴儿
    /// </summary>
    private String isInf ;
    /// <summary>
    /// 适用成人必选
    /// </summary>
    private String isAdtMust ;
    /// <summary>
    /// 适用儿童必选
    /// </summary>
    private String isChdMust ;
    /// <summary>
    /// 适用婴儿必选
    /// </summary>
    private String isInfMust ;
    /// <summary>
    /// 舱位
    /// </summary>
    private String cabin ;

    public String getInsId() {
        return insId;
    }

    public void setInsId(String insId) {
        this.insId = insId;
    }

    public String getInsNm() {
        return insNm;
    }

    public void setInsNm(String insNm) {
        this.insNm = insNm;
    }

    public String getInsCd() {
        return insCd;
    }

    public void setInsCd(String insCd) {
        this.insCd = insCd;
    }

    public String getInsDesc() {
        return insDesc;
    }

    public void setInsDesc(String insDesc) {
        this.insDesc = insDesc;
    }

    public String getInsDescURL() {
        return insDescURL;
    }

    public void setInsDescURL(String insDescURL) {
        this.insDescURL = insDescURL;
    }

    public Double getInsAmt() {
        return insAmt;
    }

    public void setInsAmt(Double insAmt) {
        this.insAmt = insAmt;
    }

    public Double getInsTax() {
        return insTax;
    }

    public void setInsTax(Double insTax) {
        this.insTax = insTax;
    }

    public String getIsAdt() {
        return isAdt;
    }

    public void setIsAdt(String isAdt) {
        this.isAdt = isAdt;
    }

    public String getIsChd() {
        return isChd;
    }

    public void setIsChd(String isChd) {
        this.isChd = isChd;
    }

    public String getIsInf() {
        return isInf;
    }

    public void setIsInf(String isInf) {
        this.isInf = isInf;
    }

    public String getIsAdtMust() {
        return isAdtMust;
    }

    public void setIsAdtMust(String isAdtMust) {
        this.isAdtMust = isAdtMust;
    }

    public String getIsChdMust() {
        return isChdMust;
    }

    public void setIsChdMust(String isChdMust) {
        this.isChdMust = isChdMust;
    }

    public String getIsInfMust() {
        return isInfMust;
    }

    public void setIsInfMust(String isInfMust) {
        this.isInfMust = isInfMust;
    }

    public String getCabin() {
        return cabin;
    }

    public void setCabin(String cabin) {
        this.cabin = cabin;
    }
}

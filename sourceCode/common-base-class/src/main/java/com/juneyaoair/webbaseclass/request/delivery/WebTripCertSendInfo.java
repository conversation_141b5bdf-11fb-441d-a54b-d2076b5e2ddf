package com.juneyaoair.webbaseclass.request.delivery;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;

@XmlRootElement(name = "WebTripCertSendInfo")
@XmlAccessorType(XmlAccessType.FIELD)
public class WebTripCertSendInfo {
	private String deliveryType; //配送类型
	private int deliveryFee;
	private String deliveryDatetime; //配送日期格式:yyyy-MM-dd
	private String linker; //收件人
	private String deliverToProvince; //收件人详细地址
	private String deliverToCity; //收件地址邮编
	private String deliveryAddress; //收件人联系电话
	private String remark; //备注
	private String handphoneNumber;
	private String deliveryState;//状态

	public WebTripCertSendInfo() {
	}

	public String getDeliveryType() {
		return deliveryType;
	}

	public void setDeliveryType(String deliveryType) {
		this.deliveryType = deliveryType;
	}

	public int getDeliveryFee() {
		return deliveryFee;
	}

	public void setDeliveryFee(int deliveryFee) {
		this.deliveryFee = deliveryFee;
	}

	public String getDeliveryDatetime() {
		return deliveryDatetime;
	}

	public void setDeliveryDatetime(String deliveryDatetime) {
		this.deliveryDatetime = deliveryDatetime;
	}

	public String getLinker() {
		return linker;
	}

	public void setLinker(String linker) {
		this.linker = linker;
	}

	public String getDeliverToProvince() {
		return deliverToProvince;
	}

	public void setDeliverToProvince(String deliverToProvince) {
		this.deliverToProvince = deliverToProvince;
	}

	public String getDeliverToCity() {
		return deliverToCity;
	}

	public void setDeliverToCity(String deliverToCity) {
		this.deliverToCity = deliverToCity;
	}

	public String getDeliveryAddress() {
		return deliveryAddress;
	}

	public void setDeliveryAddress(String deliveryAddress) {
		this.deliveryAddress = deliveryAddress;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getHandphoneNumber() {
		return handphoneNumber;
	}

	public void setHandphoneNumber(String handphoneNumber) {
		this.handphoneNumber = handphoneNumber;
	}

	public String getDeliveryState() {
		return deliveryState;
	}

	public void setDeliveryState(String deliveryState) {
		this.deliveryState = deliveryState;
	}
}
package com.juneyaoair.webbaseclass.request.refund.order;

import com.juneyaoair.webbaseclass.request.order.WebOrderPassengerInfo;

import javax.validation.constraints.NotNull;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;


@XmlRootElement(name = "WebOrderRefundRequest")
@XmlAccessorType(XmlAccessType.FIELD)
public class WebOrderRefundRequest {
	private String userNo; //渠道用户人员号	分配给渠道用户的工作人员号
	@NotNull(message="渠道用户号不能为空")
	private String channelCode; //渠道用户号	B2C,CC等
	@NotNull(message="预定渠道用户号不能为空")
	private String bookChannelCode;
	@NotNull(message="退票申请人不能为空")
	private String proposer; //退票申请人
	@NotNull(message="退票联系电话不能为空")
	private String linkTelphone; //退票联系电话
	@NotNull(message="是否自愿退票不能为空")
	private Boolean isVoluntaryRefund; //是否自愿退票
	private String refundReason; //退票原因
	@NotNull(message="是否退保险不能为空")
	private Boolean isRefundInsurance; //是否退保险因为保险不能部分退，所以系统根据购保情况自动处理退保
	@NotNull(message="按人分组退票信息不能为空")
	private List<WebOrderPassengerInfo> orderPassengerInfoList; //按人分组退票信息数组
	@NotNull(message="客户ID号不能为空")
	private String customerNo;
	@NotNull(message="验证码不能为空")
	private String loginKeyInfo;

	public String getUserNo() {
		return userNo;
	}

	public void setUserNo(String userNo) {
		this.userNo = userNo;
	}

	public String getChannelCode() {
		return channelCode;
	}

	public void setChannelCode(String channelCode) {
		this.channelCode = channelCode;
	}

	public String getBookChannelCode() {
		return bookChannelCode;
	}

	public void setBookChannelCode(String bookChannelCode) {
		this.bookChannelCode = bookChannelCode;
	}

	public String getProposer() {
		return proposer;
	}

	public void setProposer(String proposer) {
		this.proposer = proposer;
	}

	public String getLinkTelphone() {
		return linkTelphone;
	}

	public void setLinkTelphone(String linkTelphone) {
		this.linkTelphone = linkTelphone;
	}

	public Boolean getIsVoluntaryRefund(){
		return isVoluntaryRefund;
	}
	public void setIsVoluntaryRefund(Boolean isVoluntaryRefund){
		this.isVoluntaryRefund=isVoluntaryRefund;
	}

	public Boolean getIsRefundInsurance(){
		return isRefundInsurance;
	}
	public void setIsRefundInsurance(Boolean isRefundInsurance){
		this.isRefundInsurance=isRefundInsurance;
	}

	public String getRefundReason() {
		return refundReason;
	}

	public void setRefundReason(String refundReason) {
		this.refundReason = refundReason;
	}


	public List<WebOrderPassengerInfo> getOrderPassengerInfoList() {
		return orderPassengerInfoList;
	}

	public void setOrderPassengerInfoList(List<WebOrderPassengerInfo> orderPassengerInfoList) {
		this.orderPassengerInfoList = orderPassengerInfoList;
	}

	public String getCustomerNo() {
		return customerNo;
	}

	public void setCustomerNo(String customerNo) {
		this.customerNo = customerNo;
	}

	public String getLoginKeyInfo() {
		return loginKeyInfo;
	}

	public void setLoginKeyInfo(String loginKeyInfo) {
		this.loginKeyInfo = loginKeyInfo;
	}
}
package com.juneyaoair.webbaseclass.request.booking;

import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * Created by yaocf on 2016/12/19.
 */
@XmlRootElement(name = "DeliveryBookingReq")
@XmlAccessorType(XmlAccessType.FIELD)
@AllArgsConstructor
@NoArgsConstructor
public class DeliveryBookingReq {
    @NotNull(message="渠道用户号不能为空")
    private String channelCode; //渠道用户号	B2C,CC等
    @NotNull(message="订单号不能为空")
    private String channelOrderNo; //渠道订单号渠道系统中的唯一订单号，不能重复
    @NotNull(message="客户编号不能为空")
    private String channelCustomerNo; //渠道客户编号通过渠道下订单的购买者，在渠道自己系统识别该客户的编号,该值为-1时表示匿名用户
    @NotNull(message="预定人卡号不能为空")
    private String ffpCardNo; //预订人常客卡号HO+卡号
    @NotNull(message="预定人常客ID不能为空")
    private String ffpId; //预订人常客ID
    @NotNull(message="配送地址信息不能为空")
    private TripCertSendInfo_M tripCertSendInfo;
    @NotNull(message="验证信息不能为空")
    private String loginKeyInfo;
    private String ip;

    public String getChannelCode() {
        return channelCode;
    }

    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }

    public String getChannelOrderNo() {
        return channelOrderNo;
    }

    public void setChannelOrderNo(String channelOrderNo) {
        this.channelOrderNo = channelOrderNo;
    }

    public String getChannelCustomerNo() {
        return channelCustomerNo;
    }

    public void setChannelCustomerNo(String channelCustomerNo) {
        this.channelCustomerNo = channelCustomerNo;
    }

    public String getFfpCardNo() {
        return ffpCardNo;
    }

    public void setFfpCardNo(String ffpCardNo) {
        this.ffpCardNo = ffpCardNo;
    }

    public String getFfpId() {
        return ffpId;
    }

    public void setFfpId(String ffpId) {
        this.ffpId = ffpId;
    }

    public TripCertSendInfo_M getTripCertSendInfo() {
        return tripCertSendInfo;
    }

    public void setTripCertSendInfo(TripCertSendInfo_M tripCertSendInfo) {
        this.tripCertSendInfo = tripCertSendInfo;
    }

    public String getLoginKeyInfo() {
        return loginKeyInfo;
    }

    public void setLoginKeyInfo(String loginKeyInfo) {
        this.loginKeyInfo = loginKeyInfo;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }
}

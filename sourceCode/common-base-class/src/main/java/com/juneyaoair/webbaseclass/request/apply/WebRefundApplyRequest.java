package com.juneyaoair.webbaseclass.request.apply;

import com.juneyaoair.webbaseclass.request.order.WebOrderPassengerInfo;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;


@XmlRootElement(name = "WebRefundApplyRequest")
@XmlAccessorType(XmlAccessType.FIELD)
@AllArgsConstructor
@NoArgsConstructor
public class WebRefundApplyRequest {
	@NotNull(message="渠道用户号不能为空")
	private String channelCode; //渠道用户号	B2C,CC等
	@NotNull(message="预定渠道用户号不能为空")
	private String bookChannelCode;
	@NotNull(message="按人分组退票信息不能为空")
	private List<WebOrderPassengerInfo> orderPassengerInfoList; //按人分组退票信息数组
	@NotNull(message="航程类型不能为空")
	@Pattern(regexp="[OW|RT]{2}", message = "航班类型只能为单程：OW；往返：RT！")
	private String routeType; //航程类型单程：OW；往返：RT（RT时请注意飞行方向）
	@NotNull(message="出票时间不能为空")
	@Pattern(regexp="(((20[0-3][0-9]-(0[13578]|1[02])-(0[1-9]|[12][0-9]|3[01]))|(20[0-3][0-9]-(0[2469]|11)-(0[1-9]|[12][0-9]|30))) (20|21|22|23|[0-1][0-9]):[0-5][0-9])", message = "出票时间格式错误，应为yyyy-MM-dd hh:mi")
	private String ticketingDate; //出票时间yyyy-MM-dd hh:mi
	@NotNull(message="国内国际标识不能为空")
	@Pattern(regexp="[DI]{1}", message = "国内国际标识只能为国内：D；国际：I！")
	private String interFlag; //国内国际标识国内：D；国际：I

	@NotNull(message="客户ID号不能为空")
	private String customerNo;
	@NotNull(message="验证码不能为空")
	private String loginKeyInfo;

	public List<WebOrderPassengerInfo> getOrderPassengerInfoList() {
		return orderPassengerInfoList;
	}

	public void setOrderPassengerInfoList(List<WebOrderPassengerInfo> orderPassengerInfoList) {
		this.orderPassengerInfoList = orderPassengerInfoList;
	}

	public String getChannelCode() {
		return channelCode;
	}

	public void setChannelCode(String channelCode) {
		this.channelCode = channelCode;
	}

	public String getBookChannelCode() {
		return bookChannelCode;
	}

	public void setBookChannelCode(String bookChannelCode) {
		this.bookChannelCode = bookChannelCode;
	}

	public String getRouteType() {
		return routeType;
	}

	public void setRouteType(String routeType) {
		this.routeType = routeType;
	}

	public String getTicketingDate() {
		return ticketingDate;
	}

	public void setTicketingDate(String ticketingDate) {
		this.ticketingDate = ticketingDate;
	}

	public String getInterFlag() {
		return interFlag;
	}

	public void setInterFlag(String interFlag) {
		this.interFlag = interFlag;
	}

	public String getCustomerNo() {
		return customerNo;
	}

	public void setCustomerNo(String customerNo) {
		this.customerNo = customerNo;
	}

	public String getLoginKeyInfo() {
		return loginKeyInfo;
	}

	public void setLoginKeyInfo(String loginKeyInfo) {
		this.loginKeyInfo = loginKeyInfo;
	}
}
package com.juneyaoair.webbaseclass.request.booking;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * Created by yaocf on 2016/12/19.
 */
@XmlRootElement(name = "TripCertSendInfo_M")
@XmlAccessorType(XmlAccessType.FIELD)
public class TripCertSendInfo_M {
    private String deliveryType;/// 邮寄方式
    private int deliveryFee;/// 费用
    private String linker; /// 收件人
    private String handphoneNumber;/// 收件人手机
    private String ticketNo;  //电子票号
    private String deliverToProvince;/// 省份代码
    private String deliverToCity;/// 城市代码
    private String deliveryAddress;/// 详细地址
    private String remark;/// 备注
    private String provinceCityAddress;/// 详细地址(带省市)
    private String deliveryState;

    public TripCertSendInfo_M() {
        super();
    }

    public String getDeliveryType() {
        return deliveryType;
    }

    public void setDeliveryType(String deliveryType) {
        this.deliveryType = deliveryType;
    }

    public int getDeliveryFee() {
        return deliveryFee;
    }

    public void setDeliveryFee(int deliveryFee) {
        this.deliveryFee = deliveryFee;
    }

    public String getLinker() {
        return linker;
    }

    public void setLinker(String linker) {
        this.linker = linker;
    }

    public String getHandphoneNumber() {
        return handphoneNumber;
    }

    public void setHandphoneNumber(String handphoneNumber) {
        this.handphoneNumber = handphoneNumber;
    }

    public String getTicketNo() {
        return ticketNo;
    }

    public void setTicketNo(String ticketNo) {
        this.ticketNo = ticketNo;
    }

    public String getDeliverToProvince() {
        return deliverToProvince;
    }

    public void setDeliverToProvince(String deliverToProvince) {
        this.deliverToProvince = deliverToProvince;
    }

    public String getDeliverToCity() {
        return deliverToCity;
    }

    public void setDeliverToCity(String deliverToCity) {
        this.deliverToCity = deliverToCity;
    }

    public String getDeliveryAddress() {
        return deliveryAddress;
    }

    public void setDeliveryAddress(String deliveryAddress) {
        this.deliveryAddress = deliveryAddress;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getProvinceCityAddress() {
        return provinceCityAddress;
    }

    public void setProvinceCityAddress(String provinceCityAddress) {
        this.provinceCityAddress = provinceCityAddress;
    }

    public String getDeliveryState() {
        return deliveryState;
    }

    public void setDeliveryState(String deliveryState) {
        this.deliveryState = deliveryState;
    }
}

package com.juneyaoair.webbaseclass.request.booking;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * Created by yaocf on 2016/12/19.
 */
@XmlRootElement(name = "IdentityInfo_M")
@XmlAccessorType(XmlAccessType.FIELD)
public class IdentityInfo_M {
    private String idNo; //证件号DETR:,F提取到的信息，有证件信息、行程单和常客等等信息都在这里
    private String idType; //证件类型

    public String getIdNo() {
        return idNo;
    }

    public void setIdNo(String idNo) {
        this.idNo = idNo;
    }

    public String getIdType() {
        return idType;
    }

    public void setIdType(String idType) {
        this.idType = idType;
    }
}

package com.juneyaoair.webbaseclass.request.booking;

/**
 * Created by yaocf on 2016/12/19.
 */
public class LoungeInfo_M {
    private String loungeNo;/// 休息室编号
    private String name;/// 名称
    private String address;/// 地址
    private String airportCode;/// 机场三字码
    private String loungeTitle;/// 休息室概要
    private String loungeContent;/// 休息室介绍
    private String loungePicUrl;/// 图片url集合
    private Double loungeOrgAmount;/// 原金额
    private Double loungeAmount;/// 销售金额
    private Double maxUseScore;/// 可使用积分数
    private String flightDate;/// 起飞时间  即使用日期
    private int loungeCount;/// 购买休息室份数
    private Double orderAmount;/// 购买金额
    private String ticketNo;//票号

    public LoungeInfo_M() {
    }

    public String getLoungeNo() {
        return loungeNo;
    }

    public void setLoungeNo(String loungeNo) {
        this.loungeNo = loungeNo;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getAirportCode() {
        return airportCode;
    }

    public void setAirportCode(String airportCode) {
        this.airportCode = airportCode;
    }

    public String getLoungeTitle() {
        return loungeTitle;
    }

    public void setLoungeTitle(String loungeTitle) {
        this.loungeTitle = loungeTitle;
    }

    public String getLoungeContent() {
        return loungeContent;
    }

    public void setLoungeContent(String loungeContent) {
        this.loungeContent = loungeContent;
    }

    public String getLoungePicUrl() {
        return loungePicUrl;
    }

    public void setLoungePicUrl(String loungePicUrl) {
        this.loungePicUrl = loungePicUrl;
    }

    public Double getLoungeOrgAmount() {
        return loungeOrgAmount;
    }

    public void setLoungeOrgAmount(Double loungeOrgAmount) {
        this.loungeOrgAmount = loungeOrgAmount;
    }

    public Double getLoungeAmount() {
        return loungeAmount;
    }

    public void setLoungeAmount(Double loungeAmount) {
        this.loungeAmount = loungeAmount;
    }

    public Double getMaxUseScore() {
        return maxUseScore;
    }

    public void setMaxUseScore(Double maxUseScore) {
        this.maxUseScore = maxUseScore;
    }

    public int getLoungeCount() {
        return loungeCount;
    }

    public void setLoungeCount(int loungeCount) {
        this.loungeCount = loungeCount;
    }

    public Double getOrderAmount() {
        return orderAmount;
    }

    public void setOrderAmount(Double orderAmount) {
        this.orderAmount = orderAmount;
    }

    public String getFlightDate() {
        return flightDate;
    }

    public void setFlightDate(String flightDate) {
        this.flightDate = flightDate;
    }

    public String getTicketNo() {
        return ticketNo;
    }

    public void setTicketNo(String ticketNo) {
        this.ticketNo = ticketNo;
    }
}

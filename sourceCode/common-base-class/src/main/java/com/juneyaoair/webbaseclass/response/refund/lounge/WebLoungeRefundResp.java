package com.juneyaoair.webbaseclass.response.refund.lounge;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;

@XmlRootElement(name = "WebLoungeRefundResp")
@XmlAccessorType(XmlAccessType.FIELD)
public class WebLoungeRefundResp {
	private String version; //接口版本号10
	private String channelCode; //渠道用户号B2C,CC等
	private String userNo; //渠道用户人员号分配给渠道用户的工作人员号
	private String ffpId;
	private String ffpCardNo;
	private String channelOrderNo;
	private String orderNo;
	private String resultCode; //结果代码10001 － 成功，其它失败
	private String errorInfo; //错误信息

	public String getVersion() {
		return version;
	}

	public void setVersion(String version) {
		this.version = version;
	}

	public String getChannelCode() {
		return channelCode;
	}

	public void setChannelCode(String channelCode) {
		this.channelCode = channelCode;
	}

	public String getUserNo() {
		return userNo;
	}

	public void setUserNo(String userNo) {
		this.userNo = userNo;
	}

	public String getFfpId() {
		return ffpId;
	}

	public void setFfpId(String ffpId) {
		this.ffpId = ffpId;
	}

	public String getFfpCardNo() {
		return ffpCardNo;
	}

	public void setFfpCardNo(String ffpCardNo) {
		this.ffpCardNo = ffpCardNo;
	}

	public String getChannelOrderNo() {
		return channelOrderNo;
	}

	public void setChannelOrderNo(String channelOrderNo) {
		this.channelOrderNo = channelOrderNo;
	}

	public String getOrderNo() {
		return orderNo;
	}

	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}

	public String getResultCode() {
		return resultCode;
	}

	public void setResultCode(String resultCode) {
		this.resultCode = resultCode;
	}

	public String getErrorInfo() {
		return errorInfo;
	}

	public void setErrorInfo(String errorInfo) {
		this.errorInfo = errorInfo;
	}
}
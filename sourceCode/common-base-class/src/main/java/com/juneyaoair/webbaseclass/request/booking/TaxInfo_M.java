package com.juneyaoair.webbaseclass.request.booking;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * Created by yaocf on 2016/12/19.
 */
@XmlRootElement(name = "TaxInfo_M")
@XmlAccessorType(XmlAccessType.FIELD)
public class TaxInfo_M {
    private Double taxAmount; //税项金额
    private String taxCode; //税项代码
    private String taxCurrencyType; //税项货币类型

    public Double getTaxAmount() {
        return taxAmount;
    }

    public void setTaxAmount(Double taxAmount) {
        this.taxAmount = taxAmount;
    }

    public String getTaxCode() {
        return taxCode;
    }

    public void setTaxCode(String taxCode) {
        this.taxCode = taxCode;
    }

    public String getTaxCurrencyType() {
        return taxCurrencyType;
    }

    public void setTaxCurrencyType(String taxCurrencyType) {
        this.taxCurrencyType = taxCurrencyType;
    }
}

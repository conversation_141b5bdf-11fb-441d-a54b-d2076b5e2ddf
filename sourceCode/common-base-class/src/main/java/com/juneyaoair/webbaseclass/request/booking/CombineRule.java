package com.juneyaoair.webbaseclass.request.booking;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * Created by yaocf on 2016/12/20.
 */
@XmlRootElement(name = "CombineRule")
@XmlAccessorType(XmlAccessType.FIELD)
public class CombineRule {
    private String[] combineSegList; // 为空没有限制。有值时表示可以与该条运价组合的后续起飞到达机场对列表。数组中每一行一条起飞到达机场对
    // PVGPEK
    private String[] cabins; // 为空没有限制。有值时表示可以和该条运价组合的舱位代码（CabinFare.CabinCode）。数组中每一行一个舱位代码（多程惠达会限制组合舱位，如：头等与头等，经济与经济）
    private String combineId; // 组合Id,为空没有限制,有值时相同值的CombineId的运价才能组合

    public String[] getCombineSegList() {
        return combineSegList;
    }

    public void setCombineSegList(String[] combineSegList) {
        this.combineSegList = combineSegList;
    }

    public String[] getCabins() {
        return cabins;
    }

    public void setCabins(String[] cabins) {
        this.cabins = cabins;
    }

    public String getCombineId() {
        return combineId;
    }

    public void setCombineId(String combineId) {
        this.combineId = combineId;
    }
}

package com.juneyaoair.webbaseclass.request.order;

import javax.validation.constraints.NotNull;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;

@XmlRootElement(name = "WebCancelOrderRequest")
@XmlAccessorType(XmlAccessType.FIELD)
public class WebCancelOrderRequest {
	
	@NotNull(message="渠道用户号不能为空")
	private String channelCode; //渠道用户号	B2C,CC等
	@NotNull(message="预定渠道用户号不能为空")
	private String bookChannelCode;
	@NotNull(message="渠道订单编号不能为空")
	private String channelOrderNo; //渠道订单编号同一渠道不能有相同的渠道订单编号
	@NotNull(message="订单编号不能为空")
	private String orderNo; // 订单编号
	@NotNull(message="客户ID号不能为空")
	private String customerNo;
	@NotNull(message="验证码不能为空")
	private String loginKeyInfo;
	private String orderType;//机票类别  机票 休息室等等

	public String getChannelCode() {
		return channelCode;
	}

	public void setChannelCode(String channelCode) {
		this.channelCode = channelCode;
	}

	public String getBookChannelCode() {
		return bookChannelCode;
	}

	public void setBookChannelCode(String bookChannelCode) {
		this.bookChannelCode = bookChannelCode;
	}

	public String getChannelOrderNo() {
		return channelOrderNo;
	}

	public void setChannelOrderNo(String channelOrderNo) {
		this.channelOrderNo = channelOrderNo;
	}

	public String getOrderNo() {
		return orderNo;
	}

	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}

	public String getCustomerNo() {
		return customerNo;
	}

	public void setCustomerNo(String customerNo) {
		this.customerNo = customerNo;
	}

	public String getLoginKeyInfo() {
		return loginKeyInfo;
	}

	public void setLoginKeyInfo(String loginKeyInfo) {
		this.loginKeyInfo = loginKeyInfo;
	}

	public String getOrderType() {
		return orderType;
	}

	public void setOrderType(String orderType) {
		this.orderType = orderType;
	}
}

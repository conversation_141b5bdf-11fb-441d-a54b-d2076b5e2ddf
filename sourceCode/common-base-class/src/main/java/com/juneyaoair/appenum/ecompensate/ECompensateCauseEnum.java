package com.juneyaoair.appenum.ecompensate;

public enum ECompensateCauseEnum {

    Delay(1, "航班延误"),
    Cancel(2, "航班取消"),
    OverSale(3, "航班超售"),
    DamagedLuggage(4, "行李破损"),
    Undercharged<PERSON>uggage(5, "行李少收"),
    <PERSON><PERSON><PERSON><PERSON><PERSON>(6, "行李遗失"),
    Other(7, "其他原因"),
    FlightAlternate(8, "航班备降")
    ;

    private ECompensateCauseEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    private int value;

    private String desc;

    public int getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    //校验类型
    public static ECompensateCauseEnum checkEnum(int v){
        for (ECompensateCauseEnum c: ECompensateCauseEnum.values()) {
            if (c.getValue() == v) {
                return c;
            }
        }
        return null;
    }
}

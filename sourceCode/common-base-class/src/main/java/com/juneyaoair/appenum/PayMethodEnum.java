package com.juneyaoair.appenum;

/**
 * Created by yaocf on 2017/6/8.
 */
public enum PayMethodEnum {
    ALIPAY("ALIPAY"),
    WEIXINPAY("WEIXINPAY"),
    CARDPAY("CARDPAY"),
    SHOUFUPAY("SHOUFUPAY"),
    HUARUIPAY("HUARUIPAY"),
    CMBCHINAPAY("CMBCHINAPAY"),//招商银行
    CMBCHINAPAYV2("CMBCHINAPAYV2"),//招商银行升级
    CCBCHINAPAY("CCBCHINAPAY"),//中国建设银行
    CHINAPAY("CHINAPAY"),//银联云闪付
    XIAOMIPAY("XIAOMIPAY"),//小米支付
    HUAWEIPAY("HUAWEIPAY"),//华为支付
    APPLEPAY("APPLEPAY"),//APPLEPAY
    SAMSUNGPAY("SAMSUNGPAY"),//三星支付
    CASHIER("CASHIER"),//收银台
    YUFUPAY("YUFUPAY"),//裕福支付
    SCORE("score"),//积分付
    CASH("cash"),
    HOWALLET("HOWALLET"), //电子钱包
    HUIFU("HUIFU"),
    DCEPAY("DCEPAY"),//数字人民币支付
    PAYAFTERFLY("PAYAFTERFLY"),//华瑞先飞后付
    YBALIPAY("YBALIPAY"),//易宝聚合-支付宝 支付
    YBWEIXINPAY("YBWEIXINPAY"),//易宝聚合-微信 支付
    CCBPAY("CCBPAY"),//建行支付
    YLWEIXINPAY("YLWEIXINPAY")//银联微信支付
    ;
    public final String value;
    PayMethodEnum(String pay){
        this.value=pay;
    }
    //校验类型
    public static PayMethodEnum checkType(String v){
        for (PayMethodEnum c: PayMethodEnum.values()) {
            if (c.value.equals(v)) {
                return c;
            }
        }
        return null;
    }
}

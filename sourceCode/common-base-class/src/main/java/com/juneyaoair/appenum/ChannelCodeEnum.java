package com.juneyaoair.appenum;

/**
 * Created by yaocf on 2018/2/6.
 * 渠道枚举类
 */
public enum ChannelCodeEnum {
    /** channelCode desc */
    MOBILE("MOBILE",""),
    MWEB("MWEB",""),
    WEIXIN("WEIXIN",""),
    WXAPP("WXAPP","微信小程序"),
    CHECKIN("CHECKIN","值机小程序"),
    B2C("B2C",""),
    CALLCENTER("CallCenter","呼叫中心"),
    TAOLX("TAOLX",""),
    DSJ("DSJ",""),
    CUSS("CUSS",""),
    HW5G("HW5G",""),
    OTHER("OTHER",""),
    MP_ALIPAY("MP_ALIPAY","支付宝小程序"),
    ALIPAY_PLUG("ALIPAY_PLUG","支付宝插件"),
    ALIPAY_WINDOW("ALIPAY_WINDOW","支付宝橱窗"),
    ;

    private String channelCode;
    private String desc;

    ChannelCodeEnum(String channelCode,String desc){
        this.channelCode=channelCode;
        this.desc = desc;
    }

    //校验类型
    public static ChannelCodeEnum checkEnum(String v){
        for (ChannelCodeEnum c: ChannelCodeEnum.values()) {
            if (c.channelCode.equals(v)) {
                return c;
            }
        }
        return null;
    }

    public String getChannelCode() {
        return channelCode;
    }
}

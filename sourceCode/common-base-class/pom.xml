<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>mobile</artifactId>
        <groupId>com.juneyaoair</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>common-base-class</artifactId>
    <packaging>jar</packaging>

    <name>common-base-class</name>
    <url>http://maven.apache.org</url>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <java-version>1.8</java-version>
        <releases.repo>http://nexus.juneyaoair.com:8081/repository/maven-releases/</releases.repo>
        <snapshots.repo>http://nexus.juneyaoair.com:8081/repository/maven-snapshots/</snapshots.repo>
    </properties>
    <!-- 配置私服仓库 -->
    <repositories>
        <repository>
            <id>nexus</id>
            <name>nexus</name>
            <url>http://nexus.juneyaoair.com:8081/repository/maven-public/</url>
            <!-- true表示开启该远程仓库中release版本的下载，下同 -->
            <releases>
                <enabled>true</enabled>
            </releases>
            <!-- true表示开启该远程仓库中snapshots版本的下载，下同 -->
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
    </repositories>
    <distributionManagement>
        <repository>
            <!-- 注意：这里的id一定要和setting.xml文件中server下的id保持一致，下同 -->
            <id>releases</id>
            <url>${releases.repo}</url>
        </repository>
        <snapshotRepository>
            <id>snapshots</id>
            <url>${snapshots.repo}</url>
        </snapshotRepository>
    </distributionManagement>

    <dependencies>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>3.8.1</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>javax</groupId>
            <artifactId>javaee-api</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>javax.mail</artifactId>
                    <groupId>com.sun.mail</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
        <dependency>
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-validator</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.jpush.api</groupId>
            <artifactId>jiguang-common</artifactId>
            <version>0.1.3</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.sun.xml.bind</groupId>
            <artifactId>jaxb-core</artifactId>
            <version>2.2.11</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>
        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-annotations</artifactId>
        </dependency>
        <!--maven cuss-dto 契约-->
        <dependency>
            <groupId>com.juneyaoair</groupId>
            <artifactId>common-dto-base</artifactId>
        </dependency>
        <dependency>
            <groupId>com.juneyaoair</groupId>
            <artifactId>cuss-dto-booking</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.hibernate</groupId>
                    <artifactId>hibernate-validator</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>javax.validation</groupId>
                    <artifactId>validation-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven.compiler.plugin.version}</version>
                <configuration>
                    <source>${java-version}</source>
                    <target>${java-version}</target>
                    <encoding>UTF-8</encoding>
                    <compilerArguments>
                        <verbose/>
                        <!--<bootclasspath>${java.home}\lib\rt.jar;${java.home}\lib\jce.jar</bootclasspath>-->
                    </compilerArguments>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>

<?xml version="1.0" encoding="UTF-8"?>
<!--status 用于控制log4j2日志框架本身的日志级别-->
<Configuration status="DEBUG">
    <!--设置日志存储目录-->
    <Properties>
        <Property name="logPath">/home/<USER>/app/log</Property>
    </Properties>
    <Appenders>
        <Console name="Console" target="SYSTEM_OUT">
            <PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss.SSS}{GMT+8} [%t][%X{REQUEST_ID}] %-5level %logger{36} - %msg%n" charset="UTF-8"/>
        </Console>
        <RollingFile name="DailyDebug" fileName="${logPath}/dailydebug.log"
                     filePattern="${logPath}/debug-%d{yyyy-MM-dd}_%i.log">
            <PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss.SSS}{GMT+8} [%t][%X{REQUEST_ID}] %p (%F\:%L) - %m%n" charset="UTF-8"/>
            <Policies>
                <TimeBasedTriggeringPolicy modulate="true" interval="1"/>
                <SizeBasedTriggeringPolicy size="30 MB"/>
            </Policies>
            <Filters>
                <!--先拒绝级别对应的上一个级别的,再允许对应级别的,同时拒绝的onMismatch要设置成 NEUTRAL(中立)
                <ThresholdFilter level="info" onMatch="DENY" onMismatch="NEUTRAL"/>-->
                <ThresholdFilter level="debug" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
            <DefaultRolloverStrategy max="90"/>
        </RollingFile>
        <RollingFile name="DailyInfo" fileName="${logPath}/dailyinfo.log"
                     filePattern="${logPath}/info-%d{yyyy-MM-dd}_%i.log">
            <PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss.SSS}{GMT+8} [%t][%X{REQUEST_ID}] %p (%F\:%L) - %m%n" charset="UTF-8"/>
            <Policies>
                <TimeBasedTriggeringPolicy modulate="true" interval="1"/>
                <SizeBasedTriggeringPolicy size="30 MB"/>
            </Policies>
            <Filters>
                <!--先拒绝级别对应的上一个级别的,再允许对应级别的,同时拒绝的onMismatch要设置成 NEUTRAL(中立)
                <ThresholdFilter level="error" onMatch="DENY" onMismatch="NEUTRAL"/>-->
                <ThresholdFilter level="info" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
            <DefaultRolloverStrategy max="90"/>
        </RollingFile>
        <RollingFile name="DailyError" fileName="${logPath}/dailyerror.log"
                     filePattern="${logPath}/error-%d{yyyy-MM-dd}_%i.log">
            <PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss.SSS}{GMT+8} [%t][%X{REQUEST_ID}] %p (%F\:%L) - %m%n" charset="UTF-8"/>
            <Policies>
                <TimeBasedTriggeringPolicy modulate="true" interval="1"/>
                <SizeBasedTriggeringPolicy size="30 MB"/>
            </Policies>
            <Filters>
                <ThresholdFilter level="error" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
            <DefaultRolloverStrategy max="90"/>
        </RollingFile>
        <!--度量日志-->
        <RollingFile name="MetricInfo" fileName="${logPath}/metric/metricinfo.log"
                     filePattern="${logPath}/metric/metric-%d{yyyy-MM-dd}_%i.log">
            <PatternLayout pattern="%m%n" charset="UTF-8"/>
            <Policies>
                <TimeBasedTriggeringPolicy modulate="true" interval="1"/>
                <SizeBasedTriggeringPolicy size="150 MB"/>
            </Policies>
            <Filters>
                <!--先拒绝级别对应的上一个级别的,再允许对应级别的,同时拒绝的onMismatch要设置成 NEUTRAL(中立)-->
                <ThresholdFilter level="warn" onMatch="DENY" onMismatch="NEUTRAL"/>
                <ThresholdFilter level="info" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
            <DefaultRolloverStrategy max="90"/>
        </RollingFile>
    </Appenders>
    <Loggers>
        <!--排除一些日志信息，开发测试时可注释-->
        <Logger name="org.mongodb.driver" level="ERROR"/>
        <Logger name="org.apache.zookeeper" level="ERROR"/>
        <Logger name="org.springframework.beans.factory.support.DefaultListableBeanFactory" level="ERROR"/>
        <Logger name="org.springframework.web.servlet" level="INFO"/>
        <Logger name="org.springframework.jdbc.datasource" level="INFO"/>
        <Logger name="org.apache.http" level="INFO"/>
        <logger name="org.apache.curator" level="WARN"/>
        <logger name="org.springframework.data.redis" level="WARN"/>
        <logger name="org.mybatis" level="INFO"/>
        <logger name="org.hibernate.validator" level="INFO"/>
        <logger name="com.ctrip.framework.apollo.internals.RemoteConfigLongPollService" level="INFO"/>
        <logger name="com.ctrip.framework.apollo.internals.RemoteConfigRepository" level="INFO"/>
        <logger name="org.apache.commons.httpclient" level="INFO"/>
        <logger name="httpclient.wire" level="INFO"/>
        <logger name="springfox.documentation" level="ERROR"/>
        <logger name="com.alibaba.dubbo.remoting.exchange.support.header" level="ERROR"/>
        <logger name="com.ctrip.framework.apollo.internals.ConfigServiceLocator" level="ERROR"/>
        <logger name="com.alibaba.dubbo.remoting.exchange.support.header.HeartbeatHandler" level="ERROR"/>
        <logger name="com.alibaba.dubbo.remoting.exchange.support.header.HeartBeatTask" level="ERROR"/>
        <Logger name="com.elk.metric" level="INFO" additivity="false">
            <AppenderRef ref="MetricInfo" />
        </Logger>
        <!-- 优先级从高到低依次为：OFF、FATAL、ERROR、WARN、INFO、DEBUG、TRACE、 ALL -->
        <Logger name="com.elk.metric" level="INFO" additivity="false">
            <AppenderRef ref="MetricInfo" />
        </Logger>
        <Root level="DEBUG">
            <AppenderRef ref="DailyDebug" />
            <!--<AppenderRef ref="DailyInfo" />-->
            <AppenderRef ref="DailyError" />
            <AppenderRef ref="Console" />
        </Root>
    </Loggers>
</Configuration>

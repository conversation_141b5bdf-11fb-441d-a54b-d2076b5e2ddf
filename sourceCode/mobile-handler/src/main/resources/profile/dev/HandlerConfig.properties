#DEV
env=test
#统一订单Restful api地址
#测试
#used
UnitOrder.WebAPI.URL=http://uniteorderapi.test.juneyaoair.com
#*************************新一代订单服务地址TEST START****************************
#TEST
UnitOrder.NewAPI.URL = https://horder.hoair.cn
#TEST open api
UnitOrder.OpenAPI.URL = https://horder.hoair.cn
#*************************新一代订单服务地址TEST END******************************

#*************************crm java微服务 http START****************************
crm.openapi.url= https://horder.hoair.cn
##crm会员服务
crmmember.openapi.url = https://horder.hoair.cn/CrmMember
#企业会员服务
#单机地址
#crmcompany.openapi.url = http://172.22.20.131:8015
crmcompany.openapi.url = https://horder.hoair.cn/CrmCompany
##crm积分航段
crmmileage.openapi.url = https://horder.hoair.cn/CrmMileage
#*************************crm java微服务 http END******************************

#*************************cc微服务 http START****************************
#呼叫中心cc服务
callcenter.openapi.url= https://horder.hoair.cn/ho-cc
#*************************cc微服务 http END******************************

#==========================旅客服务网配置===========================
#TEST
UnitOrder.PassagerAPI.URL=http://172.22.20.84:8000
#UnitOrder.PassagerAPI.URL=http://172.22.3.11:8000
#航班动态
#大数据地址
bigdata_api_url = http://old-marketing.juneyaoair.com/traveller-service-gateway
weixin.login=https://api.weixin.qq.com/sns/oauth2/access_token
#发布appid
weixin.app.id=wxdb4ebfc1902d4ae9
weixin.secret=a2215b316bab882271b6c6984f651247
#企业appid
#weixin.app.id=wx27cf92844305d3b7
#weixin.secret=cf51822e494017990aa07726130a3947




UnitOrder.WebAPI.VERSION=10
UnitOrder.WebAPI.CURRENCY_CODE=CNY
UnitOrder.WebAPI.LANG_CODE = CN

UnitOrder.WebAPI.M.USER_NO=10007
UnitOrder.WebAPI.M.CHANNEL_CODE=MOBILE
UnitOrder.WebAPI.M.CHANNEL_CODE_INT=MOBILEINT

UnitOrder.WebAPI.MWEB.CHANNEL_CODE=MWEB
#test
UnitOrder.WebAPI.M.USER_KEY=jklasdfIJLoII213@#DFIDFSDREdRsdf
#pro
#UnitOrder.WebAPI.M.USER_KEY=50ab8^eef9610c130895305b71be5#ef
UnitOrder.WebAPI.M.CPS_KEY=8DEA7D1FEBF2D4368E579A2A12BE1DDA
#UnitOrder.WebAPI.M.CPS_KEY=123456
#UnitOrder.WebAPI.M.CPS_KEY=5A37FB8319D9AE9B7AEA932628F32BFC

UnitOrder.WebAPI.W.USER_NO=10009
UnitOrder.WebAPI.W.CHANNEL_CODE=WEIXIN
UnitOrder.WebAPI.W.CHANNEL_CODE_INT=WEIXININT
UnitOrder.WebAPI.W.USER_KEY=ffffsdfIJLoII213@#DFIDFSDREddddd
UnitOrder.WebAPI.W.CPS_KEY=8057E7E0D3308350F70B0AE0FDA7CC98

UnitOrder.WebAPI.B2C.USER_NO=10001
UnitOrder.WebAPI.B2C.CHANNEL_CODE=B2C

weixin.client.code=WEIXIN
weixin.client.passwd=WEIXIN2014
mobile.client.code=MOBILE
mobile.client.passwd=MOBILE2014
wxapp.client.passwd=WXAPP20200521
mweb.client.passwd=MWEB20200521
hw5g.client.passwd=HW5G2021
mpalipay.client.passwd=ALIPAYAPP20230505
hw5g.aes.secret=nj66E9lxMI8nnk+9
#网上值机接口
weixin.checkin.code=20000
mobile.checkin.code=30000
pda.checkin.code=80000
#测试渠道
#mobile.checkin.code=40000
#mobile.checkin.code=10000


crm.client.language.code=CN
#</editor-fold>

#支付网关查询地址
#test
queryGatway.url=http://netpay.test.juneyaoair.com/HOQueryGateway.aspx
#订单支付系统
#test
payment.url=http://netpay.test.juneyaoair.com/Pay/HOSyncPay.aspx
payMethods.url=http://***********:86/DigitalCurrencyBC/QueryDCList.aspx
payment.async.url=http://netpay.test.juneyaoair.com/Pay/HOASyncPay.aspx
#****************支付请求地址**************

#*******************异步通知地址START*******************
payment.back.url = http://127.0.0.1
#正式地址  首付游
payment.shoufuyou.limit.url=http://epay.juneyaoair.com:8080/HOXyfCredit.aspx
mweb.payment.shoufuyou.back.url=http://m.juneyaoair.com/m/xinYongFei/mweb/showPayResult.html
mweb.pay.return.url = http://172.19.66.75:8083/paymentResult/payResult
#*******************异步通知地址END*******************
#华瑞银行
payment.hrbank.init.url=http://epay.juneyaoair.com:8080/HOHRBPayVerifyDeveloper.aspx
#*************虚拟支付配置开始************************
#0元付
#virtual.pay.gatewayNo=95200
virtual.pay.gatewayNo=JX200
virtual.pay.cardInfo=**********|111111|||1|||
#*************虚拟支付配置结束************************
#首付游
#测试地址
#payment.shoufuyou.url=http://172.20.24.64:88/PaymentWebsite/Pay/HOAsyncPay.aspx
#首付游同步返回地址
#payment.shoufuyou.back.url=http://172.19.66.218:8022/app/xinYongFei/showPayResult

#payment.back.url = http://172.20.24.64:88
#payment.back.url = http://172.20.38.11

#支付系统地址
epay.url = http://epay.juneyaoair.com:8080

multi.uploadImage.path = D\:/tempjpg

#*********************新的短信平台配置开始***********************
Juneyao.Sms.userId=10141
Juneyao.Sms.NPwd=gCSPQQ8m
Juneyao.Sms.Type=Verification
#*********************新的短信平台配置结束***********************

Juneyao.Visit.Type.Av=av
Juneyao.Visit.Cnt.Av=1000
Juneyao.Visit.Cnt.Forever.Av=1500

Juneyao.Visit.Type.Reg=reg
Juneyao.Visit.Cnt.Reg=10
Juneyao.Visit.Cnt.Forever.Reg=20
#注册验证码获取次数
Juneyao.Visit.Type.Sms=verycode
Juneyao.Visit.Cnt.Sms=5
Juneyao.Visit.Cnt.Forever.Sms=10
#短信发送通用限制
Juneyao.Visit.Type.Sms.Common=sms_common
Juneyao.Visit.Cnt.Sms.Common=6
Juneyao.Visit.Cnt.Forever.Sms.Common=15
#邮箱验证码次数
Juneyao.Visit.Type.Email=emailverycode
Juneyao.Visit.Cnt.Email=5
Juneyao.Visit.Cnt.Forever.Email=10

Juneyao.Visit.Type.comm=comm
Juneyao.Visit.Cnt.comm=1000
Juneyao.Visit.Cnt.Forever.comm=5000

#修改密码
Juneyao.Visit.Type.UpdatePwd=upwd
Juneyao.Visit.Cnt.UpdatePwd=5
Juneyao.Visit.Cnt.Forever.UpdatePwd=10

#忘记密码
Juneyao.Visit.Type.ForgetPwd=fpwd
Juneyao.Visit.Cnt.ForgetPwd=5
Juneyao.Visit.Cnt.Forever.ForgetPwd=10

#修改消费密码
Juneyao.Visit.Type.Consume=xiaofei
Juneyao.Visit.Cnt.Consume=5
Juneyao.Visit.Cnt.Forever.Consume=10

#修改手机号码
Juneyao.Visit.Type.Mobile=mb
Juneyao.Visit.Cnt.Mobile=5
Juneyao.Visit.Cnt.Forever.Mobile=10

Juneyao.Visit.Type.AddMobile=addMobile
Juneyao.Visit.Cnt.AddMobile=5
Juneyao.Visit.Cnt.Forever.AddMobile=10

#总次数
Juneyao.Visit.Type.Total=all
Juneyao.Visit.Cnt.Total=10

#每日登录接口调用频次
Juneyao.Visit.Type.Login=LOGIN
Juneyao.Visit.Cnt.Login=3000
#每日登录出错次数
Juneyao.Visit.Type.LoginErr=loginErr
Juneyao.Visit.Cnt.LoginErr=5
#每日登录验证码获取次数限制 ip100次
Juneyao.Visit.Type.LoginCodeIP=logincodeip
Juneyao.Visit.Cnt.LoginCode_IP=100
#每日登录验证码获取次数限制 手机五次
Juneyao.Visit.Type.LoginCodeMobile=logincodemobile
Juneyao.Visit.Cnt.LoginCode_MOBILE=5
#银联实名认证
Juneyao.Visit.Type.NameAuth=nameauth
Juneyao.Visit.Cnt.NameAuth=10
Juneyao.Visit.Cnt.Forever.NameAuth=20
Juneyao.Visit.Type.NameAuthIP=nameauthip
Juneyao.Visit.Cnt.NameAuthIP=8
Juneyao.Visit.Cnt.Forever.NameAuthIP=100
Juneyao.Visit.Type.NameAuthErr=nameautherr
Juneyao.Visit.Cnt.NameAuthErr=5
#照片实名认证
Juneyao.Visit.Type.PhotoAuth=photoauth
Juneyao.Visit.Cnt.PhotoAuth=10
Juneyao.Visit.Cnt.Forever.PhotoAuth=60
Juneyao.Visit.Type.PhotoAuthIP=photoauthip
Juneyao.Visit.Cnt.PhotoAuthIP=6
Juneyao.Visit.Cnt.Forever.PhotoAuthIP=50
Juneyao.Visit.Type.PhotoAuthDevice=photoauthdevice
Juneyao.Visit.Cnt.PhotoAuthDevice=6
Juneyao.Visit.Cnt.Forever.PhotoAuthDevice=50
#值机
Juneyao.Visit.Type.CheckIn=CheckIn
Juneyao.Visit.Cnt.CheckIn=5
Juneyao.Visit.Cnt.Forever.CheckIn=30

#活动验证码
Juneyao.Visit.Type.Activity=Activity
Juneyao.Visit.Cnt.Activity=5
Juneyao.Visit.Cnt.Forever.Activity=10
#赠送优惠券
Juneyao.Visit.Type.GiveCoupon=giveCoupon
Juneyao.Visit.Cnt.GiveCoupon=15
Juneyao.Visit.Cnt.Forever.GiveCoupon=50
#选座控制
Juneyao.Visit.Type.CheckIn_Select=CheckInSeat
Juneyao.Visit.Cnt.CheckIn_Select=40
Juneyao.Visit.Cnt.Forever.CheckIn_Select=80

Juneyao.Visit.Type.CheckIn_Select_Not_Login=CheckInSeatNotLogin
Juneyao.Visit.Cnt.CheckIn_Select_Not_Login_Cnt=10
Juneyao.Visit.Cnt.Forever.CheckIn_Select_Not_Login_Cnt=30
#客票验真次数
Juneyao.Visit.Type.Ticket_Verify=TicketVerify
Juneyao.Visit.Cnt.Ticket_Verify=10
Juneyao.Visit.Cnt.Forever.Ticket_Verify=30
#遗失物品
Juneyao.Visit.Type.Goods_Lost=GoodsLostVerify
Juneyao.Visit.Cnt.Goods_Lost=3
UnitOrder.WebAPI.CRM.USER_NO=11001
UnitOrder.WebAPI.CRM.CHANNEL_CODE=CRM

#验证KEY
user.info.key =poidfjfjeui8939eidrf9893i29edo9rikriw
user.info.key.m =poidfjfjeui8939eidrf9893i29edo9rimomo

#test
#协议产品ID,保险产品名称,保险产品代码,保额,保费,适用成人,描述URL,是否默认选中,当日是否可购买,保险描述
#insure.info.D=115100099&众安综合险&7000001&600000&30&Y&/clause/service_insure6.html&N&含航意、延误取消、托运行李、证件遗失等多重保障,115000006&华泰航意险&600000&600000&20&Y&/clause/service_insure5.html&N&可享最高100万元保障,215060034&众安延误险&5000001&600000&20&Y&/clause/service_insure4.html&N&延误3小时赔付300元
#insure.info.I.OW=415010099&华泰境外航意险&2000000&600000&30&Y&/clause/service_insure2.html&N&可享最高100万元保障
#insure.info.I.RT=415010006&华泰境外综合险&2000000&400000&48&Y&/clause/service_insure3.html&N&最高赔付260万元
#pro
#insure.info.D=115100099&众安综合险&7000001&600000&30&Y&/clause/service_insure6.html&N&N&含航意、延误取消、托运行李、证件遗失等多重保障,215060034&众安延误险&5000001&600000&20&Y&/clause/service_insure4.html&N&N&延误3小时赔付300元,115000006&华泰航意险&600000&600000&20&Y&/clause/service_insure5.html&N&Y&可享最高100万元保障,115000016&太平洋航意险&1000000&1100000&20&Y&/clause/service_insure7.html&N&Y&可享最高100万元保障
#insure.info.I.OW=115160306&华泰境外航意险&1000000&2600000&30&Y&/clause/service_insure2.html&Y&N&最高赔付260万元
#insure.info.I.RT=415010006&华泰境外综合险&2000000&400000&48&Y&/clause/service_insure3.html&Y&N


#console.url = http://172.19.66.75:8086
console.url = http://172.22.0.13:9997
#基础服务地址
#basic.info.url = http://172.22.31.155:6000
basic.info.url = http://***********:6000
basic.provider.url = http://***********:6000/b2cbasic

#正式地址
cms.url = http://csm.juneyaoair.com:8080
#goods.lost.url=http://csm.juneyaoair.com:8080/bsmLstSvr!addWxBsmLst.action
#unaccompanied.minorWeb.url=http\://172.20.38.15\:91
#测试地址
#遗失物品
goods.lost.url=http://172.20.25.47/bsmLstSvr!addWxBsmLst.action
#无陪儿童
unaccompanied.minorWeb.url=http\://172.20.24.64\:88

#双十一
#正式
interfaceReqUrlPre =  http\://172.20.38.17:88/B2CManageHandler
interfaceLuckyDraw = http\://172.20.38.17:95/B2CAcitivityHandler
#测试
#interfaceReqUrlPre = http\://172.19.66.9\:8005/B2CManageHandler
#interfaceLuckyDraw = http\://172.19.66.9\:848/B2CAcitivityHandler

priceCacheReqUrl =http\://172.20.38.21
priceCacheMonthlyReqUrl = http\://www.juneyaoair.com

#企业号
#weixin.app.id=wx27cf92844305d3b7
#weixin.secret=cf51822e494017990aa07726130a3947
#weixin.login=https://api.weixin.qq.com/sns/oauth2/access_token
#发布号
#weixin.app.id=wxdb4ebfc1902d4ae9
#weixin.secret=a2215b316bab882271b6c6984f651247

#模板路径配置
newgolden.templet.url=/templet/golden.png
newsilver.templet.url=/templet/silver.png
newblack.templet.url=/templet/black.png
newcommon.templet.url=/templet/common.png
doctorsilver.template.url=/templet/doctor_silver.png
doctorgolden.template.url=/templet/doctor_golden.png
#图片输出文件夹
out.ecard.url=/uploadImage/ecard


#web服务器端口
ftp_tomcate_port_01=http://*************:8080
#ftp服务器1的IP
ftp_address_01=**************
#ftp服务器1的端口
ftp_port_01=21
#ftp服务器1的登录名称
ftp_username_01=<EMAIL>
#ftp服务器1的密码
ftp_password_01=geyao1992
#ftp服务器1的存放上传文件的目录
ftp_pathname_01=geyao/admin
ftp_img_path_01=img/admin

#web服务器端口  测试用
#sftp_tomcate_port_01=https://t-img.hoair.cn
sftp_tomcate_port_01=https://mediaws.hoair.cn
#sftp服务器的IP
//sftp_address_01=***********
sftp_address_01=***********
#sftp服务器端口
sftp_port_01=2222
#sftp用户名
sftp_username_01=juneyaoair
#sftp用户密码
#sftp_password_01=XtJrXjVu
sftp_password_01=admin@vm2018
#sftp服务器的存放文件的目录
#sftp_pathname_01=/data/sftp/juneyaoair/upload
sftp_pathname_01=/home/<USER>/data
sftp_img_path_01=data

#是否启用设备验证   本地可设为N 发布生产时为Y
is_checkDevice=N
#领取优惠券ip控制
receive.coupons.pass.ip = *************,*************,*************,*************
#支付宝实名认证appid
alipay_app_id=2017082508372340

#淘旅行请求地址
taolx_url=https://static.juneyaoair.com/routeapi
#**************************公司内网IP正则表达式************************************
ip.company.pattern=((^(172)\\.(19|20)\\.(\\d|[1-9]\\d|1\\d{2}|2[0-4]\\d||25[0-5])\\.(\\d|[1-9]\\d|1\\d{2}|2[0-4]\\d||25[0-5]))||127.0.0.1)
#**************************第三方调用接口时约定的密钥**********************************
access.secret=juneyaoair
#**************************客户端支持版本开始********************************
server.surport.client=MWEB,4.1.02,,4.1.03,,4.1.04,4.1.06
#**************************客户端支持版本结束********************************

#**************************极验相关ID设置开始********************************
#微信绑定 登录
bind.geetest.id=138b31f59e96ef0e95e5a1772852c0ed
bind.geetest.key=5491a32388f87eec4549b521644f6ee4
bind.geetest.newfailback=Y
#APP订单生成
orderBook.geetest.id=ba018b4ad831db46b7ef16de49bcc605
orderBook.geetest.key=560184ee68c3fbaaeaebe17be82a5331
orderBook.geetest.newfailback=Y
#APP5.1登录使用
login.geetest.id=c61063edd0fbd95fc38442c7979d7375
login.geetest.key=46ce2416ef47426fd0fa3a0fb041b927
login.geetest.newfailback=Y
#获取手机验证码
sms.geetest.id=7512c775632bf1ea7f2fcb52846f8018
sms.geetest.key=34147c5d2dfd802238fc7e16fca983b1
sms.geetest.newfailback=Y
#客票查询
initialize.geetest.id=d5b00cce10dfcc57417ac2c84cd38145
initialize.geetest.key=0669eb3f44bad1ccea8d466cb5426b1a
initialize.geetest.newfailback=Y
#行程确认单短信验证码
electronicsItinerary.geetest.id=66e32423cc4238af833f5beb66152485
electronicsItinerary.geetest.key=6e01208582ef6dd31eba9c18d9bc1e64
#test
geetest.onelogin.url=https://onelogin.geetest.com/check_phone
geetest.onelogin.appid=145d65c1014ad4ee46954c63f69b69e5
geetest.onelogin.appkey=b366bcf722de9098cd1bbbedb873fd9c

#pro
#geetest.onelogin.appid=4d4bf4b3c3d2ee1bb64cc3853e97dfca
#geetest.onelogin.appkey=2c63fd2065e4b9d393be26bd0f56c7c1
#**************************极验相关ID设置结束********************************

#=======================M站相关地址 START==============================
#mweb.url=http://172.19.66.75:9080
mweb.url=http://172.22.0.13:8088
#新版M站 V2
mweb.url.new=https://t-m.hoair.cn
#富文本条款地址
#test
policy.provisions.url=https://t-m.hoair.cn/service/clause/index.html?id=
#pre
#policy.provisions.url=https://mp.juneyaoair.com/service/clause/index.html?id=
#pro
#policy.provisions.url=https://m.juneyaoair.com/service/clause/index.html?id=
#=======================M站相关地址 END================================

#checkin_select_url=http://172.19.66.123:7002
#checkin_select_url=http://172.22.0.11:7001
checkin_select_url=http://*************:7001
checkin_mobile_no=30000
checkin_weixin_no=20000
#**************************相关地址配置开始**************************
#handle地址
handle.url=http://172.22.0.11:9000
##问卷
#pro
juneyaoair.question.url = http://172.20.94.10:84
#市场活动增加积分接口
#test
#juneyaoair.score.url = http://mileage.juneyaoair.com:804/Market/Func065
juneyaoair.score.url = http://172.22.3.17:9113/Market/Func065
#**************************相关地址配置结束**************************

#新值机选座系统地址
new_checkin_seat_version=30
cuss_server_url=http://***********:9210
new_checkin_select_url=http://***********:9210/cuss
new_checkin_select_customer_url=http://***********:9210/customer
#微信会员记录保存 小程序
#pro
#weixin.member.save = http://weixin.juneyaoair.com
#test
weixin.member.save = http://***********/HoApp

#crm接口地址V3
#pro
#crm_api_domain_url=http://mileage.juneyaoair.com:804
#test
crm_api_domain_url=http://172.22.3.17:9113
#测试环境
#id_name_check_url=https://114.55.36.16:8090/CreditFunc/v2.1/IdNamePhotoCheck
#正式环境
id_name_check_url=https://www.miniscores.net:8313/CreditFunc/v2.1/IdNamePhotoCheck
#==========================静默活体人脸识别配置===========================
#生产测试统一地址
livebody.checkface.url=https://www.miniscores.cn:8315/CreditFunc/v2.1/LiveBodyV2Customer
livebody.user.name=jixiang2018
livebody.user.pwd=jixiang201811
pdf_path = D:/juneyaoair/pdfFiles

UnitOrder.JunYaoAir.URL = http://www.juneyaoair.com
#**************************TEST同盾配置START*******************************
#test
#tongdun_partner_code = jxhk
#android_secret_key = 87b3d532c1824f26a2382373471950c7
#ios_secret_key = 873af5da7e4340d2bad06865991f9163
#web_secret_key = 8d9d174d3eea450a9714f50deff9219a
#xcx_secret_key = a573ae0e80064f20b618587aa55d4b8e
#tongdun_api_url = https://apitest.tongdun.cn/antifraud/v1

#tongdun_android_login_event_id = login_professional_android
#tongdun_android_register_event_id = register_professional_android
#tongdun_android_trade_event_id = trade_professional_android
#tongdun_android_checkin_lookup_event_id = Checkin_android_20200817
#tongdun_android_lookup_event_id = lookup_android_20191211
#tongdun_android_marketing_event_id = Marketing_android_20200331
#tongdun_android_reserve_event_id = Reserve_android_20200707
#tongdun_android_payment_event_id = Payment_android_20200707
#tongdun_android_sms_event_id = SMS_android_20200929
#
#tongdun_ios_login_event_id = login_professional_ios
#tongdun_ios_register_event_id = register_professional_ios
#tongdun_ios_trade_event_id = trade_professional_ios
#tongdun_ios_checkin_lookup_event_id = Checkin_ios_20200817
#tongdun_ios_lookup_event_id = lookup_ios_20191211
#tongdun_ios_marketing_event_id = Marketing_ios_20200331
#tongdun_ios_reserve_event_id = Reserve_IOS_20200707
#tongdun_ios_payment_event_id = Payment_IOS_20200707
#tongdun_ios_sms_event_id = SMS_ios_20200929
#
#tongdun_web_login_event_id = Login_web_20190425
#tongdun_web_register_event_id = Register_web_20190425
#tongdun_web_trade_event_id = Trade_web_20190425
#tongdun_h5_checkin_lookup_event_id = Checkin_h5_20200817
#tongdun_h5_lookup_event_id = Lookup_h5_20191206
#tongdun_h5_marketing_event_id = Marketing_web_20200331
#tongdun_h5_reserve_event_id = Reserve_H5_20200707
#tongdun_h5_payment_event_id = Payment_H5_20200707
#tongdun_h5_sms_event_id = SMS_web_20200929
#
#tongdun_xcx_login_event_id = Login_web_20191029
#tongdun_xcx_trade_event_id = Trade_web_20191029
#tongdun_xcx_checkin_lookup_event_id = Checkin_xcx_20200817
#tongdun_xcx_lookup_event_id = lookup_web_20191216
#tongdun_xcx_marketing_event_id = Marketing_web_20200331
#tongdun_xcx_reserve_event_id = Reserve_xcx_20200707
#tongdun_xcx_payment_event_id = Payment_xcx_20200707
#tongdun_xcx_sms_event_id = SMS_web_20200929
#**************************TEST同盾配置END*********************************

#**************************PRO同盾配置START*******************************
android_secret_key = 8574f22aac7048ceb0ceb22853fa2128
ios_secret_key = df2484a20f79408fab0a10b9d66f9b4b
harmony_secret_key = f75e85fe7e354c3ea8cdc1f6aa7d67b9
web_secret_key = f8a137abc05440c1b3aa7ca34d5885a3
xcx_secret_key = 55afbe078196461c8f1aadc75d377987
zfb_secret_key = 3c3ea7666e9e4c378d8da19b4aa60830

tongdun_partner_code = jxhk
tongdun_api_url = https://api.tongdun.cn/antifraud/v1
#安卓渠道
tongdun_android_login_event_id = Login_android_20190116
tongdun_android_register_event_id = Register_android_20190116
tongdun_android_trade_event_id = Trade_android_20190116
tongdun_android_lookup_event_id = lookup_android_20191211
tongdun_android_checkin_lookup_event_id = Checkin_android_20200817
tongdun_android_marketing_event_id = Marketing_android_20200331
tongdun_android_reserve_event_id = Reserve_android_20200707
tongdun_android_payment_event_id = Payment_android_20200707
tongdun_android_sms_event_id = SMS_android_20200929
tongdun_android_modify_salepwd_event_id = Modify_android_20231120
tongdun_android_use_score_event_id = Click_android_20231120
#IOS渠道
tongdun_ios_login_event_id = Login_ios_20190116
tongdun_ios_register_event_id = Register_ios_20190116
tongdun_ios_trade_event_id = Trade_ios_20190116
tongdun_ios_lookup_event_id = lookup_ios_20191211
tongdun_ios_checkin_lookup_event_id = Checkin_ios_20200817
tongdun_ios_marketing_event_id = Marketing_ios_20200331
tongdun_ios_reserve_event_id = Reserve_IOS_20200707
tongdun_ios_payment_event_id = Payment_IOS_20200707
tongdun_ios_sms_event_id = SMS_ios_20200929
tongdun_ios_modify_salepwd_event_id = Modify_ios_20231120
tongdun_ios_use_score_event_id = Click_ios_20231120
#鸿蒙渠道
tongdun_harmony_login_event_id = Login_hm_20241011
tongdun_harmony_register_event_id = Register_hm_20241011
tongdun_harmony_trade_event_id = Trade_hm_20241011
tongdun_harmony_lookup_event_id = lookup_hm_20241011
tongdun_harmony_checkin_lookup_event_id = lookup_hm_20241011zj
tongdun_harmony_marketing_event_id = Marketing_hm_20241011
tongdun_harmony_reserve_event_id = Booking_hm_20241011
tongdun_harmony_payment_event_id = Payment_hm_20241011
tongdun_harmony_sms_event_id = SMS_hm_20241011
tongdun_harmony_modify_salepwd_event_id = Modify_hm_20241011
tongdun_harmony_use_score_event_id = Click_hm_20241011
#h5页面
tongdun_web_login_event_id = Login_web_20190425
tongdun_web_register_event_id = Register_web_20190425
tongdun_web_trade_event_id = Trade_web_20190425
tongdun_h5_lookup_event_id = Lookup_h5_20191206
tongdun_h5_checkin_lookup_event_id = Checkin_h5_20200817
tongdun_h5_marketing_event_id = Marketing_web_20200331
tongdun_h5_reserve_event_id = Reserve_H5_20200707
tongdun_h5_payment_event_id = Payment_H5_20200707
tongdun_h5_sms_event_id = SMS_web_20200929
tongdun_h5_modify_salepwd_event_id = Modify_h5_20231202
tongdun_h5_use_score_event_id = Click_h5_20231202
#微信小程序
tongdun_xcx_login_event_id = Login_web_20191029
tongdun_xcx_trade_event_id = Trade_web_20191029
tongdun_xcx_lookup_event_id = lookup_web_20191216
tongdun_xcx_checkin_lookup_event_id = Checkin_xcx_20200817
tongdun_xcx_marketing_event_id = Marketing_web_20200331
tongdun_xcx_reserve_event_id = Reserve_xcx_20200707
tongdun_xcx_payment_event_id = Payment_xcx_20200707
tongdun_xcx_sms_event_id = SMS_web_20200929
tongdun_xcx_modify_salepwd_event_id = Modify_xcx_20231202
tongdun_xcx_use_score_event_id = Click_xcx_20231202
#支付宝小程序
tongdun_zfb_checkin_lookup_event_id = lookup_zfb_20240820
tongdun_zfb_modify_salepwd_event_id = Modify_zfb_20231202
tongdun_zfb_use_score_event_id = Click_zfb_20231202
tongdun_zfb_sms_event_id = SMS_zfb_20240821
#**************************PRO同盾配置 END*********************************

#==========================电子发票发送邮件配置===========================
email.hostname=mail.juneyaoair.com
email.username=<EMAIL>
email.password=2fjiath3al

invoice.email.hostName=mail.juneyaoair.com
invoice.email.username=<EMAIL>
invoice.email.password=Psm20190101
#==========================电子发票发送邮件配置===========================

#*************************产品管理TEST START****************************
#TEST 权益券查询地址  2019-12-12
Product.Coupon.URL=https://horder.hoair.cn/Pdm
#预发布 权益券查询地址
#Product.Coupon.URL=http://***********:8201
#生产 权益券查询地址
#Product.Coupon.URL=http://*************:9009/product
#*************************产品管理TEST END******************************


#==========================在线客服 START===========================
#Live800Key  pro
url_Live800=https://v2.live800.com/live800/chatClient/chatbox.jsp
key_Live800=juneyaoair123
companyID=897425
configID=146255
jid=**********
#==========================在线客服 END=============================

#==========================腾讯云服务 START=========================
cloud.tencent.server = https://idasc.webank.com
#==========================腾讯云服务 END===========================

#============================wallet相关配置 START===================
#test
wallet.apple.wwdrca = /passbook/test/AWDRCA.pem
wallet.keystore.path = /passbook/test/pass_test.p12
wallet.keystore.password = 111111
wallete.member.template = /passbook/test/member/member.json
#pro
#wallet.apple.wwdrca = /passbook/pro/AWDRCA.pem
#wallet.keystore.path = /passbook/pro/pass_disttibution.p12
#wallet.keystore.password = 111111
#wallete.member.template = /passbook/pro/member/member.json
#============================wallet相关配置 END====================
#============================电子补偿==============================
compensate.fw.gatewayNo = A0111
compensate.fw.key = A80FCCE#1C884130B42E8&AC45A1#C9A
compensate.fw.appId = ****************
compensate.api.redirectUrl = https://mp.juneyaoair.com/server/compensate/redirect2Epay
#============================电子补偿==============================
# �����½ӿڵĶ�������
newOrder.couponSource = PaySeat,ExtraBaggage,INNNCustomServe,INNN,ServiceButler

#============================PDI产品==============================
pdi.url = https://dev.kdlink.net:8443/kdn-api/kdn/openapi
pdi.corpId = D6iOQZYZWUp1R7Ng
pdi.secretKey = oPi2TmhjPRloC9pPvRpxukWBp8oE3QBl
#============================PDI产品==============================

#��������
flightbasic.url = http://***********:6000/flightbasic
#============================微信API==============================
weixin.api.url = https://api.weixin.qq.com
#============================微信API==============================
cuss.salty.sms=db1de9eb16624903a3d3de1614eac2a8fmsms
# ֧���������Ϣ
alipay.plug.appId = 2021003165611972
alipay.plug.appPrivateKey = MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQDjX/Fag0n3ae+fdbzzplT0Do2ZzSxY0btN3KR5e/TtOIBI+/mKs151K51EaNSznJ15qWrCPAQorvUm6wxqjtJgfja6R5KyDiepAgpmJdc/H/8eUMkz4UEpJeR7FYtzN/omIAC6582tCD17gR5c2torYx/OyVJu03wf1D15ljPNDfdlykl/xeHpZFqqu6abEmaWYVldoLJ5mDxCS2VgNwjnYagGPTOnlv3rbIAPdhANc//TGaqi8NJtufwrbPLrqTCf51NhXfiewIaLFC5WyxWFTISfUnXYpjQCfpdeTCTXzDEyUwLsJet2ZF/NTlPIxwZ/Ytw1YQ0hXebEJ0sL3kHnAgMBAAECggEBANSmjYa2lT3lAOvk5KQHYxqiZcJ7RkeHV6MZgWXxDX+zQR+BMcCLwwLYmIw7cvrMtebsEOG52bXm83bDi+g4zP7b6/lKKSOH44XIV8tzBp2OIj7LFXIE8Lk4LDkFTFek+bogYi5rz0tOEppohuoW8MZ1PXFbjlICTutBx4Dofmf+LaBVlfg3WizDGs48EiHIUppQIAgXEBjqkmKj81EfUHvS2fWwzg9lbZAvvm5ookGqMajTy4xDcyyAgLzpMvKp9+g6qhPFP8JFFA8ZZeiLXC7TXMRM8ZHzmQcO2J1Nh/UTeJwyV3sK6VGlugF8rj39d6CtAa8RFis0WvW6v7ourFECgYEA8uHd2cTE57y3kI3MMm0HMd/6QD2/YA7G87tFSQRo1xt2Xrp2xT/fQohv9zswrGWvUJKuZ460+rLWip68RyliM1FEsOz58OKfPGES7SqFWqyfEPJjf39d1QNz1BQLyMq5SwPM6qlXlO+qkQRB/pth8tolEEomZRcbuUGJFv/1U7kCgYEA76eqotZAQZUXHMN2ppjQYmgcxNSNwBivVHnZaqEAb+x90BMVBkaoS5Mt/e33MOqBWNyMikL4eKpQCSPDq9BS4dlW+0HI8+ssJLpaPyOfE/8VdfLzwfUJubb5bDf2EXhYaaB0dlavbeAqI/WFxj/Ndh9Fuw/CV7JWbxM4Jh4eUp8CgYAcOLBtgFac0qnFqyRB67c9TVgnpMUa5Tyqz2TCkjeYXwGHCkWIr95CPdY8tOz36yzOwOxw7f0LcEJLgxw/bIiTi+reX/q+yaZU/raTRjCOk6Y78t4qz2LYwHjx+I5R+3RCSiPCVIDvtlVsWkFM00MzU7Ogy+zgLCo1gW7rdDxOSQKBgEkcNEES7W1UZ0WeF59BNSkgK86cLVYx8aLqvTn7Pd/nHIaSm7JbcdIEZ1507xkcITOTrMgtIBHnmxz+rR3IItDFdfSWoBBRaRkXRJrIaio2gjyGTniYIUxnlnW+KLIxrX8jtQO49DGn1hM8PoA5TJvBdItZhmcsf5PJIDL4szVvAoGBAONJSy/245De2qVZb8zpEGEyygx8Cso+fy0ZVVlB6fUE/u5xPgXu+Ot6ZBpzmQLkJqR8hpBd+TsI2bjR7F93L5EPmyHBDXfaplql/Zdg/tjBjITRx3dbdmT+934+XDrvbyb7URYK542oX11jGlWjqI5IKbiI8GchgBriBaT5mQvE
alipay.plug.appPublicKey = MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA41/xWoNJ92nvn3W886ZU9A6Nmc0sWNG7TdykeXv07TiASPv5irNedSudRGjUs5ydealqwjwEKK71JusMao7SYH42ukeSsg4nqQIKZiXXPx//HlDJM+FBKSXkexWLczf6JiAAuufNrQg9e4EeXNraK2MfzslSbtN8H9Q9eZYzzQ33ZcpJf8Xh6WRaqrummxJmlmFZXaCyeZg8QktlYDcI52GoBj0zp5b962yAD3YQDXP/0xmqovDSbbn8K2zy66kwn+dTYV34nsCGixQuVssVhUyEn1J12KY0An6XXkwk18wxMlMC7CXrdmRfzU5TyMcGf2LcNWENIV3mxCdLC95B5wIDAQAB
# 支付宝橱窗
alipay.window.appId = 2021004123647841
alipay.window.privateKey = MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCZjYx8Oxt9hJn8Q/hrW8mywjNK49ppfxfPGPGapaNamowLnaq+UcZX0ae2/v0Xn9eG7Mh9COt4bFK86r6lr8gh61WolxTY4NgTQsbKRGFxebfYzcKkYyX+60hv1ZLSHoL+hkFsC9BnFUWyDl4gJQ3xrN1Y2p3pBAcrzurME8UdTIe+3wGmgYRFFSclpLAOEZ4HE0oc+hA4f+6jyu6SRXLzQlR6+/N8qsxg+SeDM/aoXbYLC0iMpWZddW5rJLpdzt2RpKABYKpz3AW83wilMSzBBR3Lcsy9Qf8jY4du6eUBlG1PxEsNUBAUL9XIbyci9tVJI3GYUWYQ9DBiRE5MwpxjAgMBAAECggEAPE+hh0X8BS/o/3F/CEP0E/c9CEQ4jEk0A87LTq+SWsrrCMDzyG78FZAubKz4oafRkpg8Iwg23ITHdpZJLXC+jbwOV/qHYKvnkqEezWFQWK6zLD+dHM4opViqiep8EXWUg/4dgh+dym9LgaUtCTkkofEAu56aeWRwoJ9UD7YQLPI+ISLr9eOU1JSMbtPB8j4mZ/w4gTOQhB+syWcnvU743zbO94rEv8Tcg1Y41uil2y/aXuTq9PYobnqN0rGc47ZwSv632EJXdYRdrtBy465Gxynkhhwh2cBYiEcfePGmGamMAltG4lDfpHe3MIH3YDtOizuimlN823+ejJgrNbsJIQKBgQDiLMnNzM9/JD/JOgnbnHLDvyyn6D8suJYBU4xfN2PAh/KRcI1PCEQq/YmrRboNihqiXEyfbEZGfdydUM00tRRUuz+6k8DhhTsBH6sNxVubeqrC9ypiKkzo73pfZcyYBX3tySpbkmZfdwlrDcAZwd3kurenR2PiiBYMTMO+kasJUQKBgQCtzS/Qo4IoDeTchPA4x/vfsx030+H8dJB7Baz4bn8ZJDx57g+kifI1dfxoSemv9GfMjo8k04sodbNJud5u5rQ6rHR7Ye09uGY/7PPWE2N9v3qmabQNyUlCob/Of4LZ2IRvmDEKchgGbYTSBKKK1hifqBFo5KIjGxuFu+iSDxtdcwKBgQCoX1dMEbBWohGqJ7628lytRGBmDwsYq8Ff82aIa5+i8JGvm+5Wa3zz1BLsodfDxI/XVobkF6KpT4Ewy7o4BqW//jWPG/d268vlJkIc2lM8B7Gfuoie04a10bFz20DX2x7IKf2SdgYoA2HLhE0B38TkRev6Z7cAKPbg6TS/ZYnCQQKBgC03RotHHqOBCdt9EmoTTSnjlWRi0VY2lSgMT/XJ+XL3BGzMTHGIGeVirqoLIKrQMPj81azFBF0gUeaKXp4Hkzf4ic1Xqrr18FQNN1qobKfYWvZUZa14goC9SANXFRnLxJirxYZsLGfvsjBZWFg6Xumadf0ODkiH0t82BcmJ+JqVAoGBALDZ2MB211iN2cDz4g1lSBl2W9ODxxRkAXAB1bSDIO+owbK0pXOGdBd5rdWbP5k/dWajpaL6f71jONa4gpxQxtfBfstll5pwV4W/JCUIMoJozBTLWtPVU8ojkbecoCbJualiaA1A0fkvFLxOAbkjLPVSJYE5VS9Ww/2vmFc1Vugx
alipay.window.publicKey = MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAmY2MfDsbfYSZ/EP4a1vJssIzSuPaaX8XzxjxmqWjWpqMC52qvlHGV9Gntv79F5/XhuzIfQjreGxSvOq+pa/IIetVqJcU2ODYE0LGykRhcXm32M3CpGMl/utIb9WS0h6C/oZBbAvQZxVFsg5eICUN8azdWNqd6QQHK87qzBPFHUyHvt8BpoGERRUnJaSwDhGeBxNKHPoQOH/uo8rukkVy80JUevvzfKrMYPkngzP2qF22CwtIjKVmXXVuayS6Xc7dkaSgAWCqc9wFvN8IpTEswQUdy3LMvUH/I2OHbunlAZRtT8RLDVAQFC/VyG8nIvbVSSNxmFFmEPQwYkROTMKcYwIDAQAB
# 订单产品服务地址
oneorder.product.url = http://openapi-test.juneyaoair.com:9031/Product
# 哈喽租车
hellobike.url = https://m.hellobike.com/AppRentCarH5/fat/latest/pr_index_home.html#/home?adSource=duanwai_jxhk&c1=Aj11&c3=170&title=租车&titlestyle=6&authCode=
hellobike.hb.publicKey = MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAiytaxg4VZCrt8L/ivUr4ykiQURlANmdOX0rZUHTT++UUiLcVAd1ZQ561ZevJm33CZ8lPU27yCsFj1+rwabsTM1tXE3N60mI/FHIUaeCgE6t84J4zZqzzzp2NjFYAI38JDQyqsCXSNmooHQvpnPSYcn0m51JDgi49JNLQEuRD/mxh83EeQyhN0RpGVwBkZgzeresnWaP39GQzE2WAitw2QYbqWkYhYjyf7+n/J5m7OEAK2AJPwAQW3N9M1kzCINdmk52Mzr5bXcf0AWrmYJDvAyq0urqIFbFIueXXQ+/tAFfFEJxQhfp1aUDYPa0XsJHenytLEs9w4qWEVdnD54ujsQIDAQAB
hellobike.ho.privateKey = MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCoEVwLhDIkP8t5PpxX3IsER/C9dEnkpdN98Kf0U+Vw+YlD+RDFrumX+OoehE/VRxdWJAhlY4YqiX1AG+yc92pP+OC9UaOVpvoPLSazpeW83B3AJvn/88yGy/wPFDPGUtLv3tFuH6Why1ZrF+jXYEZk57V7HjzDokYu2XM4baGuYqBQutt177SL8hQTOmyia5qtBDFZTS3gPnWN4MgLf68rUgVOFSOCQEhz6l0LF8BOib/Xa+7S9bT4oBcOGqZviLRFglWs/YyTB72G2cJkJschBF5REkKJj7feUOTzHQWNuNlTn3bb1urVBPZBnWD+6NdTCMaIhc8BxQgB57DZ2lQ/AgMBAAECggEAJpsGN6cAFY+J36ngI9aHIE6eIAasSpdEw3MaQWdcFt1bCIAXuDD7e2sz7Bqcc/NGiwTv4MpBsVevq6A+c7fpdcFHbnVoTCH04rOOZkm/QeiV9ON/HaoU2wnUNmp/khjIV00pclBWrZmVmjf9xitZQChxaXyCnLWTxYMxkk6siu/BRGuUVycTY1wKPquSfIxXP+6vMQCaa06OEHklU2KlQGsvYn8ptVnHkod0Y6fCP+UIp3jkPTU6Ezh4KUXaXYVrUI4D5jq//rpoPCUm+Uj33k3xwKtfXVrmQSc74pFvM2WBMhKzkXD0/IYbpbNXCTSKIMQUzXWDGmBDeDairne6wQKBgQDc14ofcBifABrLA1Uf5jvvsyn6aTBHsdyKLLJxyWbQ5uKPOS84VT4ADXMqqJ9BRAPTjufDAS9aVmbhW8mn18LfCZmiUznt5iJhkpBmodyyKgKVsBFeoaSUGUVHCcdYeHxc5g1Ww8TBl4voYzn32cXvjUAlHU0iSTLP4gxX38o8sQKBgQDC0v+thY+eOofXuciBwYu9IhaiAnLAonqn89ly/QWBVNrwY+VnrlO5JCB8yBe8pAbH6sGIH5Ea7HXCeNZ1m405f6LVoSRXzjhC6a+CFO/U1StYE5Ep9LxzDFdF/yWwz12rrSOP4qo3bnbFxNRQeldCbGZHQ4Z7aDbVp6BkNzUb7wKBgAQ4Ced32MCQ2/Is6h9l4pc9sPubrtRvzWrGV9Jcqp4rCwD5crF8z2Zo215hQkmY5wnPnRXYm/L525VKj5upP4vLPygetuiYkOeUSTXsua6dptb9Ohe4ttAte2lUAn7qLlYH/xE9RDD+mCmkW6MkgeeFp0gOOz8AbvwRsXKsT8zBAoGBALamnrIN36GWejjTopmT3PCKfvGQngjfy2KeiST29ixUY5av8SkNFM0kg5i49xgSJ5FSE/MMtTuqqJSUOWu1U+euM/lYw/vWCIUjFTo9asP8vlAquBO/sNWjokareKiVINqMziRdevvRAmVTrUFuEVTiz9jMA866233fU83gvFfdAoGBALCnUccifSCIt5tZi+kfjteXlX4Dc1btqexIEfyF++rgQx3A6TMxN/4E9sEPvjuJHgShElYA86O2LXdBqD6CkMVa9Cu1uBb63XgeSAej2S8sG88YX3EpMnTZJm9+QGi379sGYV2JVj5RBrAZf7B1TzIVxy0o9z5TIW+rkItPlL1F
hellobike.ho.publicKey = MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAqBFcC4QyJD/LeT6cV9yLBEfwvXRJ5KXTffCn9FPlcPmJQ/kQxa7pl/jqHoRP1UcXViQIZWOGKol9QBvsnPdqT/jgvVGjlab6Dy0ms6XlvNwdwCb5//PMhsv8DxQzxlLS797Rbh+loctWaxfo12BGZOe1ex48w6JGLtlzOG2hrmKgULrbde+0i/IUEzpsomuarQQxWU0t4D51jeDIC3+vK1IFThUjgkBIc+pdCxfATom/12vu0vW0+KAXDhqmb4i0RYJVrP2Mkwe9htnCZCbHIQReURJCiY+33lDk8x0FjbjZU59229bq1QT2QZ1g/ujXUwjGiIXPAcUIAeew2dpUPwIDAQAB
# token加密SM4 key
hoTokenSm4Key = EnkpdN98Kf0UscrA
# BFF-SSO服务
bff.sso.url = http://openapi-test.juneyaoair.com:9031/mobilesso

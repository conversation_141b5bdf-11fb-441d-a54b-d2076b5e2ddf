<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:tx="http://www.springframework.org/schema/tx"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:mvc="http://www.springframework.org/schema/mvc"
       xmlns:task="http://www.springframework.org/schema/task"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
    http://www.springframework.org/schema/beans/spring-beans-4.0.xsd
    http://www.springframework.org/schema/tx
    http://www.springframework.org/schema/tx/spring-tx-4.0.xsd
    http://www.springframework.org/schema/context
    http://www.springframework.org/schema/context/spring-context-4.0.xsd
    http://www.springframework.org/schema/aop
    http://www.springframework.org/schema/aop/spring-aop-4.0.xsd
    http://www.springframework.org/schema/mvc
    http://www.springframework.org/schema/mvc/spring-mvc-4.0.xsd
    http://www.springframework.org/schema/task
    http://www.springframework.org/schema/task/spring-task-4.0.xsd">

    <!-- 启用spring mvc 注解 -->
    <context:component-scan base-package="com.geetest.geeguard.controller"/>
    <context:component-scan base-package="com.juneyaoair" use-default-filters="false">
        <context:include-filter type="annotation" expression="org.springframework.stereotype.Controller"/>
    </context:component-scan>
    <!--相当于注册了DefaultAnnotationHandlerMapping和AnnotationMethodHandlerAdapter-->
    <mvc:annotation-driven validator="validator">
        <mvc:message-converters>
            <ref bean="mappingJackson2HttpMessageConverter"/>
        </mvc:message-converters>
    </mvc:annotation-driven>
    <!-- 配置JSON视图 -->
    <bean id="mappingJackson2HttpMessageConverter"
          class="org.springframework.http.converter.json.MappingJackson2HttpMessageConverter">
        <property name="supportedMediaTypes">
            <list>
                <value>text/json;charset=UTF-8</value>
                <value>application/json;charset=UTF-8</value>
                <value>text/html;charset=UTF-8</value>
            </list>
        </property>
    </bean>
    <!--通过处理器适配器AnnotationMethodHandlerAdapter来开启支持@RequestMapping注解-->
    <bean
            class="org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter">
        <property name="messageConverters">
            <list>
                <ref bean="mappingJackson2HttpMessageConverter"/>
            </list>
        </property>
    </bean>
    <!--视图导航设置，根据前端请求的accept设置返回需要的类型-->
    <bean id="contentNegotiationManager"
          class="org.springframework.web.accept.ContentNegotiationManagerFactoryBean">
        <property name="favorPathExtension" value="false"/>
        <property name="favorParameter" value="true"/>
        <property name="mediaTypes">
            <value>
                json=application/json
                xml=application/xml
            </value>
        </property>
    </bean>

    <bean id="multipartResolver" class="org.springframework.web.multipart.commons.CommonsMultipartResolver">
        <!-- 设定默认编码 -->
        <property name="defaultEncoding" value="UTF-8"/>
        <!-- 设定文件上传时写入内存的最大值，如果小于这个参数不会生成临时文件，默认为10240 -->
        <property name="maxInMemorySize">
            <value>1638400</value>
        </property>
        <!--设定文件上传的最大值20M  n*1024*1024 单位bytes-->
        <property name="maxUploadSize">
            <value>20971520</value>
        </property>
        <!-- 延迟文件解析 -->
        <!--<property name="resolveLazily" value="true" />-->
    </bean>

    <!--校验工厂-->
    <!-- validated 校验配置start -->
    <bean id="validator" class="org.springframework.validation.beanvalidation.LocalValidatorFactoryBean" >
        <!-- 提供检验机制的类： 此处用的而是Hibernate校验器，但是跟Hibernate没有关系 -->
        <property name="providerClass" value="org.hibernate.validator.HibernateValidator" />
        <!-- 指定校验的使用的资源文件, 如果不需要额外的错误配置文件，就不用配置该属性，
        在配置文件中配置k-v的错误提示信息，如果不指定则使用默认配置：classpath下的ValidationMessages.properties -->
        <!--<property name="validationMessageSource" ref="messoreSources" />-->
    </bean>
    <!-- 额外错误信息文件配置 -->
    <!-- <bean id="messoreSources" class="org.springframework.context.support.ReloadableResourceBundleMessageSource">
         &lt;!&ndash; 错误信息资源文件， 不需要写后缀， 默认后缀为.properties &ndash;&gt;
         <property name="basenames">
             <list>
                 <value>classpath:testErrMessages</value>
             </list>
         </property>
         &lt;!&ndash; 错误信息文件编码方式 &ndash;&gt;
         <property name="fileEncodings" value="utf-8" />
         &lt;!&ndash; 错误信息文件缓存时间 &ndash;&gt;
         <property name="cacheSeconds" value="120" />
     </bean>-->
    <!-- validated 校验配置end -->

    <!--相当于注册了DefaultAnnotationHandlerMapping和AnnotationMethodHandlerAdapter-->
    <!--<mvc:annotation-driven validator="validator"/>-->
    <mvc:default-servlet-handler/>

    <aop:aspectj-autoproxy />

    <!--Token拦截器配置-->
    <mvc:interceptors>
        <!--url拦截处理-->
        <mvc:interceptor>
            <mvc:mapping path="/**"/>
            <bean class="com.juneyaoair.mobile.interceptor.UrlInterceptor">
                <property name="handConfig" ref="handConfig"/>
            </bean>
        </mvc:interceptor>
        <!--拦截所有请求,token验证-->
        <mvc:interceptor>
            <!--需要进行登录拦截的地址配置-->
            <mvc:mapping path="/**"/>
            <!--不需要进行拦截地址-->
            <mvc:exclude-mapping path="/favicon.ico"/>
            <mvc:exclude-mapping path="/error/**"/>
            <mvc:exclude-mapping path="/version/**"/>
            <mvc:exclude-mapping path="/swagger-ui.html"/>
            <mvc:exclude-mapping path="/captcha/**"/>
            <mvc:exclude-mapping path="/CityMapReserve/**"/>
            <!--基础数据查询-->
            <mvc:exclude-mapping path="/data/**"/>
            <mvc:exclude-mapping path="/data/index/**"/>
            <!--航线基础数据服务-->
            <mvc:exclude-mapping path="/flight/flightLine"/>
            <mvc:exclude-mapping path="/flight/new/flightLine"/>
            <mvc:exclude-mapping path="/flight/periodMinPrice"/>
            <mvc:exclude-mapping path="/flight/toGainHalfYearPrice"/>
            <mvc:exclude-mapping path="/flight/getFlightLineAirport"/>
            <mvc:exclude-mapping path="/flight/searchCityAirportInfo"/>
            <mvc:exclude-mapping path="/flight/cfcModule"/>
            <!-- 航班运价查询 -->
            <mvc:exclude-mapping path="/v2/flight/AvFare"/>
            <mvc:exclude-mapping path="/v2/flight/queryMultipleFlightFare"/>
            <mvc:exclude-mapping path="/v2/flight/flightAdvertisementQuery"/>
            <mvc:exclude-mapping path="/v2/flight/query"/>
            <mvc:exclude-mapping path="/v3/flight/queryCabinInfoFare"/>
            <mvc:exclude-mapping path="/member/login"/>
            <mvc:exclude-mapping path="/member/memberEncryptLogin"/>
            <mvc:exclude-mapping path="/orderService/queryShareOrderDetail"/>
            <mvc:exclude-mapping path="/flightDynamic/queryShareFlightDynamic"/>
            <mvc:exclude-mapping path="/member/captchalogin"/>
            <mvc:exclude-mapping path="/member/register"/>
            <mvc:exclude-mapping path="/member/registerBasic"/>
            <mvc:exclude-mapping path="/member/authMember"/>
            <mvc:exclude-mapping path="/member/thirdlogin"/>
            <mvc:exclude-mapping path="/member/thirdloginqq"/>
            <mvc:exclude-mapping path="/member/bindthirdlogin"/>
            <mvc:exclude-mapping path="/member/resetLoginPwd"/>
            <mvc:exclude-mapping path="/member/checkMobile"/>
            <mvc:exclude-mapping path="/member/hwlogin"/>
            <mvc:exclude-mapping path="/geetest/**"/>
            <mvc:exclude-mapping path="/trip/queryTripMethod"/>
            <mvc:exclude-mapping path="/checkInSeatService/getBoardingPass"/>
            <mvc:exclude-mapping path="/checkInSeatService/getBoardingPassStatus"/>
            <mvc:exclude-mapping path="/checkInSeatService/queryMyCheckInInfo"/>
            <mvc:exclude-mapping path="/checkInSeatService/queryCheckInCount"/>
            <mvc:exclude-mapping path="/checkInSeatService/checkInSeatAgreements"/>
            <mvc:exclude-mapping path="/checkInSeatService/checkIn/sendVerifyCode"/>
            <mvc:exclude-mapping path="/push/**"/>
            <mvc:exclude-mapping path="/paymentService/**"/>
            <mvc:exclude-mapping path="/nemPaymentService/**"/>
            <mvc:exclude-mapping path="/excessBaggage/**"/>
            <mvc:exclude-mapping path="/v2/coupon/queryUpCouponPro"/>
            <mvc:exclude-mapping path="/flightDynamic/queryNewFlightDynamic"/>

            <mvc:exclude-mapping path="/newinterest/queryFlightInfo"/>
            <mvc:exclude-mapping path="/lounge/queryLoungeInfo"/>
            <mvc:exclude-mapping path="/holiday/**"/>
            <mvc:exclude-mapping path="/anwreqsys/file/**"/>
            <mvc:exclude-mapping path="/package/queryBaggageWifi"/>
            <mvc:exclude-mapping path="/package/newBuyBaggageWifi"/>
            <mvc:exclude-mapping path="/applyVisa/queryType"/>
            <mvc:exclude-mapping path="/applyVisa/queryVisaProduct"/>
            <mvc:exclude-mapping path="/memberAccount/queryMobileNumber"/>
            <mvc:exclude-mapping path="/smsOrderService/queryOrderDetailM"/>
            <mvc:exclude-mapping path="/taolxService/queryOrderList"/>
            <mvc:exclude-mapping path="/taolxService/getSpecialGoods"/>
            <mvc:exclude-mapping path="/Airport/qAirportCheckInETime"/>
            <!--航班延误证明-->
            <mvc:exclude-mapping path="/electrionicproof/queryFlightChangeProof"/>
            <mvc:exclude-mapping path="/electrionicproof/sendFlightChangeProof"/>
            <mvc:exclude-mapping path="/wifiPhoneCard/queryPro"/>
            <mvc:exclude-mapping path="/airportCar/queryAirportCarProduct"/>
            <mvc:exclude-mapping path="/policy/queryPolicy"/>
            <mvc:exclude-mapping path="/policy/queryPolicyRichText"/>
            <!--短信服务-->
            <mvc:exclude-mapping path="/sendSms/sendVerifyCode"/>
            <mvc:exclude-mapping path="/sendSms/sendCouponVerifyCode"/>
            <!--预留登机牌-->
            <mvc:exclude-mapping path="/boardingPass/queryBoardingPassProduct"/>
            <!-- 登机口升舱订单查询相关无需登陆 -->
            <mvc:exclude-mapping path="/gateUpgrade/queryUpgradeOrder*"/>
            <mvc:exclude-mapping path="/gateUpgrade/queryUpgradeRules"/>
            <mvc:exclude-mapping path="/gateUpgrade/decryptTktNoAndPsgName"/>
            <!--个性化餐食无需拦截地址-->
            <mvc:exclude-mapping path="/individualizationNew/queryMealList"/>
            <mvc:exclude-mapping path="/individualizationNew/queryImgList"/>
            <!--单独购保查询无需拦截地址-->
            <mvc:exclude-mapping path="/insurance/queryInsureList"/>
            <!--企业认证查询认证结果-->
            <mvc:exclude-mapping path="/companyMemberVerify/toCatchApplyResult"/>
            <!--微信小程序接口过滤-->
            <mvc:exclude-mapping path="/wechat/miniprogram/member/bindingByPhoneNo"/>
            <mvc:exclude-mapping path="/wechat/miniprogram/member/bindingByCaptcha"/>
            <mvc:exclude-mapping path="/wechat/miniprogram/member/bindingByPassword"/>
            <mvc:exclude-mapping path="/wechat/miniprogram/member/wxAuth"/>
            <mvc:exclude-mapping path="/wechat/miniprogram/member/wxminiLogin"/>
            <mvc:exclude-mapping path="/wechat/miniprogram/member/autoLogin"/>
            <!-- 领取分享的权益券不需要验证登陆 -->
            <mvc:exclude-mapping path="/v2/rightCoupon/receiveRightCoupon"/>
            <mvc:exclude-mapping path="/couponService/receiveChangeCoupon"/>
            <!-- 查询可购买改期券-->
            <mvc:exclude-mapping path="/v2/coupon/queryChangeCouponPro"/>
            <mvc:exclude-mapping path="/v2/coupon/queryCouponByCode"/>
            <mvc:exclude-mapping path="/v2/coupon/queryRightCouponByCode"/>
            <!--优惠券默认城市对-->
            <mvc:exclude-mapping path="/v2/coupon/upCouponDefCity"/>
            <!-- 航班预订申请 -->
            <mvc:exclude-mapping path="/flightReservation/applyReservation"/>
            <!-- 文案查询接口无需登录 -->
            <mvc:exclude-mapping path="/document/**"/>
            <!-- 会员注销文案查询 -->
            <mvc:exclude-mapping path="/member/logoutPageInfo"/>
            <!-- 升舱产品航线查询 -->
            <mvc:exclude-mapping path="/v2/coupon/upProflightLine"/>
            <!-- 获取航班信息（礼宾航班查询使用） -->
            <mvc:exclude-mapping path="/airportCar/queryNewFlightInfo"/>
            <!-- 查询礼宾产品 -->
            <mvc:exclude-mapping path="/airportCar/queryProtocolProduct"/>
            <!-- 企业会员绑定 -->
            <mvc:exclude-mapping path="/companyAuth/memberBind"/>

            <!--快速注册绑定服务-->
            <mvc:exclude-mapping path="/wxCustom/**"/>
            <!--黑白屏-->
            <mvc:exclude-mapping path="/theme/themeSwitch"/>
            <!-- 电子补偿 -->
            <mvc:exclude-mapping path="/compensate/selectAblgById"/>
            <mvc:exclude-mapping path="/compensate/queryCompensateRecord"/>
            <mvc:exclude-mapping path="/compensate/queryCompensateRecordForMsg"/>
            <mvc:exclude-mapping path="/compensate/addCompensation"/>
            <mvc:exclude-mapping path="/compensate/addNewCompensation"/>
            <mvc:exclude-mapping path="/compensate/getToken"/>
            <mvc:exclude-mapping path="/compensate/toCatchCompensationFlightInfo"/>
            <mvc:exclude-mapping path="/compensate/toPreserveSupplementaryInformation"/>
            <mvc:exclude-mapping path="/elCompensate/toCacheCompensateInformation"/>
            <mvc:exclude-mapping path="/elCompensate/toCreateOrderThenPayForIt"/>
            <!-- 补偿腾讯验证 -->
            <mvc:exclude-mapping path="/compensate/getDetectInfo"/>
            <mvc:exclude-mapping path="/compensate/detectAuth"/>
            <mvc:exclude-mapping path="/compensate/redirect2Epay"/>
            <mvc:exclude-mapping path="/compensate/redirect2B2C"/>
            <!--快速注册绑定服务-->
            <mvc:exclude-mapping path="/wxCustom/**"/>
            <!-- 查询改期券产品-->
            <mvc:exclude-mapping path="/new/coupon/queryChangeCouponProduct"/>
            <!-- 查询升舱券产品-->
            <mvc:exclude-mapping path="/new/coupon/queryUpgradeCouponProduct"/>
            <!-- 查询行李券产品-->
            <mvc:exclude-mapping path="/new/coupon/queryBaggageCouponProduct"/>
            <!-- 查询休息室券产品-->
            <mvc:exclude-mapping path="/new/coupon/queryLoungeCouponProduct"/>
            <!--文件上传-->
            <mvc:exclude-mapping path="/anwreqsys/file/filesUploadV2"/>
            <!-- 易宝用，验证加密串 -->
            <mvc:exclude-mapping path="/member/checkLoginKeyInfo"/>
            <!-- 手机号一键登录接口 -->
            <mvc:exclude-mapping path="/oneClick/geetestLogin"/>
            <!-- 支付宝登录接口 -->
            <mvc:exclude-mapping path="/member/thirdPartyGrant"/>
            <!-- 会员服务获取联名卡信息 -->
            <mvc:exclude-mapping path="/member/getCoBrandedCardInfo"/>
            <!-- 口令分享,查询口令 -->
            <mvc:exclude-mapping path="/shortsentence/queryShortSentence*"/>
            <!-- 中文处理接口 -->
            <mvc:exclude-mapping path="/chinese/**"/>
            <!-- 预付费行李查询价格接口 -->
            <mvc:exclude-mapping path="/new/coupon/queryPrePaymentBaggagePrice"/>
            <!-- 预付费行李查询文案接口 -->
            <mvc:exclude-mapping path="/new/coupon/queryPrepaymentBaggageDocument"/>
            <!-- 设备信息保存 -->
            <mvc:exclude-mapping path="/device/ds"/>
            <!--广告位查询-->
            <mvc:exclude-mapping path="/v2/flight/queryAdvertisements"/>
            <!--会员权益-->
            <mvc:exclude-mapping path="/member/searchMemberRight"/>
            <!-- 支付宝登录链接排除 -->
            <mvc:exclude-mapping path="/aliMember/loginByCellPhoneNo"/>
            <mvc:exclude-mapping path="/aliMember/autoLogin"/>
            <mvc:exclude-mapping path="/themeFlightInfo/searchThemeFlightByDate"/>
            <mvc:exclude-mapping path="/themeFlightInfo/selectThemeCalendar"/>
            <mvc:exclude-mapping path="/Airport/qAirportCheckInETime"/>
            <mvc:exclude-mapping path="/memberDevelop/toCatchCurrentInfo"/>
            <mvc:exclude-mapping path="/customerService/artificialCustomerService"/>

            <bean class="com.juneyaoair.mobile.interceptor.TokenInterceptor">
                <property name="authService" ref="authService"/>
            </bean>
        </mvc:interceptor>
        <!--限流拦截器-->
        <mvc:interceptor>
            <!--需要进行限流拦截的地址配置-->
            <mvc:mapping path="/**"/>
            <bean class="com.juneyaoair.mobile.limiter.interceptor.RateLimiterInterceptor">
                <property name="handConfig" ref="handConfig"/>
            </bean>
        </mvc:interceptor>
    </mvc:interceptors>
</beans>

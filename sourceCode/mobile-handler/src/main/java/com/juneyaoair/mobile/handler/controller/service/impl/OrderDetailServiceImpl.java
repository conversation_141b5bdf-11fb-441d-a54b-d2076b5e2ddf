package com.juneyaoair.mobile.handler.controller.service.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.google.gson.reflect.TypeToken;
import com.juneyaoair.appenum.ChannelCodeEnum;
import com.juneyaoair.appenum.PayMethodEnum;
import com.juneyaoair.appenum.WSEnum;
import com.juneyaoair.appenum.av.FareBasisEnum;
import com.juneyaoair.appenum.av.ShippingRulesLabelEnum;
import com.juneyaoair.appenum.flight.FlightDirection;
import com.juneyaoair.appenum.order.*;
import com.juneyaoair.appenum.premium.PremiumEnum;
import com.juneyaoair.appenum.rightcoupon.VoucherTypesEnum;
import com.juneyaoair.baseclass.av.common.*;
import com.juneyaoair.baseclass.basicsys.response.AirPortInfoDto;
import com.juneyaoair.baseclass.basicsys.response.CityInfoDto;
import com.juneyaoair.baseclass.newcoupon.req.CouponProductOrderGetRequestDto;
import com.juneyaoair.baseclass.request.booking.TicketOrderExt;
import com.juneyaoair.baseclass.request.premium.OrderDetailPremium;
import com.juneyaoair.baseclass.response.av.BrandRightDto;
import com.juneyaoair.baseclass.response.order.comm.*;
import com.juneyaoair.baseclass.response.order.detail.*;
import com.juneyaoair.baseclass.response.order.query.InsuranceInfo;
import com.juneyaoair.baseclass.response.order.query.OrderBase;
import com.juneyaoair.baseclass.response.order.query.SubOrderResp;
import com.juneyaoair.baseclass.response.payment.PayMethod;
import com.juneyaoair.baseclass.response.province.CityInfo;
import com.juneyaoair.baseclass.response.province.ProvinceCity;
import com.juneyaoair.mobile.comm.service.IRedisService;
import com.juneyaoair.mobile.constants.MobileCoreBeanNames;
import com.juneyaoair.mobile.handler.HandlerConstants;
import com.juneyaoair.mobile.handler.comm.RedisKeyConfig;
import com.juneyaoair.mobile.handler.config.bean.HandConfig;
import com.juneyaoair.mobile.handler.controller.service.OrderDetailService;
import com.juneyaoair.mobile.handler.controller.service.bean.OrderDetailRequest;
import com.juneyaoair.mobile.handler.controller.util.OrderDetailConvert;
import com.juneyaoair.mobile.handler.controller.util.OrderPayStateConvert;
import com.juneyaoair.mobile.handler.controller.util.TicketOrderConvert;
import com.juneyaoair.mobile.handler.controller.v2.util.AVObjectConvertV2;
import com.juneyaoair.mobile.handler.controller.v2.util.CommonUtil;
import com.juneyaoair.mobile.handler.manage.OrderManage;
import com.juneyaoair.mobile.handler.service.IBasicService;
import com.juneyaoair.mobile.handler.service.IOrderService;
import com.juneyaoair.mobile.handler.service.LocalCacheService;
import com.juneyaoair.mobile.handler.util.ChannelUtils;
import com.juneyaoair.mobile.handler.util.FlightUtil;
import com.juneyaoair.mobile.mongo.entity.FlightInfo;
import com.juneyaoair.thirdentity.brandright.BrandRightDetailVo;
import com.juneyaoair.thirdentity.brandright.request.BrandRightQueryRequest;
import com.juneyaoair.thirdentity.brandright.response.BrandRightQueryResponse;
import com.juneyaoair.thirdentity.comm.request.PtPdmRequest;
import com.juneyaoair.thirdentity.comm.response.PtPdmResponse;
import com.juneyaoair.thirdentity.request.order.query.PtLoungeGetReq;
import com.juneyaoair.thirdentity.request.order.query.PtTicketDeliveryGetReq;
import com.juneyaoair.thirdentity.request.order.query.PtWeightProReq;
import com.juneyaoair.thirdentity.request.order.query.PtWifiGetReq;
import com.juneyaoair.thirdentity.request.order.refund.apply.PtRefundApplyReq;
import com.juneyaoair.thirdentity.response.order.apply.OrderDetailBaggage;
import com.juneyaoair.thirdentity.response.order.apply.PtOrderBriefInfo;
import com.juneyaoair.thirdentity.response.order.apply.PtPassengerSegment;
import com.juneyaoair.thirdentity.response.order.apply.PtRefundApplyResp;
import com.juneyaoair.thirdentity.response.order.query.BaggageBuy;
import com.juneyaoair.thirdentity.response.order.query.PtWeightProResp;
import com.juneyaoair.thirdentity.salecoupon.response.PtSaleCouponOrderGetResponse;
import com.juneyaoair.utils.StringUtil;
import com.juneyaoair.utils.http.HttpResult;
import com.juneyaoair.utils.http.HttpUtil;
import com.juneyaoair.utils.json.JsonUtil;
import com.juneyaoair.utils.util.BeanUtils;
import com.juneyaoair.utils.util.DateUtils;
import com.juneyaoair.utils.util.SensitiveInfoHider;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <AUTHOR> by jiangmingming
 * @date 2019/6/21 17:00
 */
@Service
public class OrderDetailServiceImpl implements OrderDetailService {
    private static Logger log = LoggerFactory.getLogger(OrderDetailService.class);
    private static final String PROVINCE_CITY_KEY = RedisKeyConfig.PROVINCE_CITY_KEY;
    private static final String LOG_INFO_ONE = "查询结果:";
    private static final String LOG_INFO_TWO = "查询网络出错";
    @Resource(name = MobileCoreBeanNames.API_REDIS_SERVICE)
    private IRedisService apiRedisService;
    @Autowired
    private HandConfig handConfig;
    @Autowired
    private IBasicService basicService;
    @Autowired
    private IOrderService orderService;
    @Autowired
    private LocalCacheService localCacheService;

    @Autowired
    private OrderManage orderManage;

    @Override
    public OrderDetailResp getOrderResult(SubOrderResp subOrderResp, List<OrderBase> subTiccetList,
                                          String ip, String channelCode, String userNo, OrderDetailRequest detailReq,String source) {
        //呼叫中心订单暂不处理
        AtomicReference<OrderDetailResp> resp = new AtomicReference<>(new OrderDetailResp());
        if (!ChannelCodeEnum.CALLCENTER.getChannelCode().equals(subOrderResp.getOriginChannelCode()) && subTiccetList.size() > 1) {
            subTiccetList.removeIf(a -> a.getItem3() != null);
        }
        //调用统一订单接口，查询订单详情
        String path = handConfig.getOneOrderUrl() + HandlerConstants.SUB_QUERY_REFUND_APPLY;
        int ticketOrderIdx = 0;
        for (int i = 0; i < subTiccetList.size(); i++) {
            OrderBase orderBase = subTiccetList.get(i);
            PtRefundApplyReq refundApply = new PtRefundApplyReq(HandlerConstants.VERSION,
                    channelCode, HandlerConstants.B2C_USER_NO, orderBase.getItem2(), "");
            Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
            // 查询 /Refund/RefundApply
            HttpResult serviceDetailResult = HttpUtil.doPostClient(refundApply, path, headMap, handConfig.getReadTimeout(), handConfig.getConnectTimeout());
            //
            if (serviceDetailResult.isResult() && StringUtils.isNotBlank(serviceDetailResult.getResponse())) {
                PtRefundApplyResp reRefundResp = (PtRefundApplyResp) JsonUtil.jsonToBean(serviceDetailResult.getResponse(), PtRefundApplyResp.class);
                //如果是短链接进来的，无需处理改期升舱
                if("smsLink".equals(source)&&subTiccetList.size()>1){
                    if(OrderSortEnum.Upgrade.getOrderSort().equals(reRefundResp.getOrderSort())||OrderSortEnum.Change.getOrderSort().equals(reRefundResp.getOrderSort())){
                        //如果是原人航段为空，表示是原始订单不做处理
                        if(reRefundResp.getPassengerSegmentList().stream().anyMatch(ptPassengerSegment -> ptPassengerSegment.getFormerPassengerSegmentId()==0)){
                            continue;
                        }
                    }
                }
                //获取订单中第一个机票子订单信息，其余的子机票订单只保留其中的乘客信息
                getOrderDetailResult(subOrderResp, resp, ticketOrderIdx, reRefundResp);
                //处理同一订单中不同机票子订单的是否可退状态
                List<OrderPassengerInfo> orderPassengerInfoList = resp.get().getOrderPassengerInfoList();
                if (CollectionUtils.isNotEmpty(orderPassengerInfoList)) {
                    List<OrderPassengerInfo> orderPassengerInfos = orderPassengerInfoList.stream().filter(orderPassenger -> StringUtils.isNotEmpty(orderPassenger.getTicketOrderNo())
                            && StringUtils.isNotEmpty(orderBase.getItem2()) && orderPassenger.getTicketOrderNo().equals(orderBase.getItem2())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(orderPassengerInfos)) {
                        orderPassengerInfos.forEach(
                                e -> {
                                    e.setEnableRefund(reRefundResp.isEnableRefund());
                                    if (!e.isEnableRefund()) {
                                        e.setRejectText("超过有效期客票暂不支持线上提交退票，请致电吉祥航空客服热线95520办理");
                                    }
                                }

                        );
                    }
                }

                //处理wifi订单
                handlerWifi(subOrderResp, resp, reRefundResp);
                ticketOrderIdx++;
            }
        }
        //计算实际价格
        if (CollectionUtils.isNotEmpty(subTiccetList)) {
            //计算结果
            calculateAmount(resp);
        } else {
            //其他订单
            createOrderBaseInfo(resp.get(), subOrderResp);
        }
        //计算跨天数和飞行时间,查询经停城市
        calculateCityInfo(resp, ip, channelCode);
        // 处理前端展示旅客及航段信息
        resp.get().setPassengerList(genOrderDetailPassengerList(resp.get().getOrderPassengerInfoList(), resp));
        resp.get().setSegmentList(genOrderDetailSegmentList(resp.get().getTicketRangeType(), resp.get().getOrderPassengerInfoList(), channelCode, ip));
        //是否为免票订单
        Boolean freeTicket  =resp.get().getSegmentList().stream().anyMatch(segment->"X".equals(segment.getCabin())
                ||"I".equals(segment.getCabin())||"N".equals(segment.getCabin()));
        resp.get().setFreeTicket(freeTicket);
        // 提取退改规则行李额
        dealChdInfRuleInfo(resp.get());
        //休息室订单
        handlerLounge(subOrderResp, channelCode, userNo, detailReq, resp);
        //Wifi订单
        //handlerWifi(subOrderResp, channelCode,userNo, detailReq, resp);
        //行程单邮寄订单
        handlerTripCert(subOrderResp, ip, channelCode, userNo, detailReq, resp);
        //处理行李信息
        handlerWeightOrder(subOrderResp, channelCode, userNo, detailReq, resp);
        //处理优选服务
        handlerPremium(subOrderResp, ip, channelCode, userNo, detailReq, resp);
        //迪士尼门票信息
        if (CollectionUtils.isNotEmpty(subOrderResp.getDisneyTicketList())){
            resp.get().setDisneyTicketList(subOrderResp.getDisneyTicketList());
        }
        resp.get().setCustomerNo(detailReq.getCustomerNo());
        resp.get().setCardNo(detailReq.getCardNo());
        String key = StringUtil.newGUID() + detailReq.getCustomerNo();
        resp.get().setOrderSign(key);
        //订单金额 实付金额
        resp.get().setOrderAmount(subOrderResp.getAmount());
        List<PayMethod>   payMethods= handConfig.getPayMethod();
        PayMethod  pay =  payMethods.stream().filter(payMethod -> payMethod.getPayMethodName()
                .equals(resp.get().getPayMethodName())).findFirst().orElse(null);
       if (pay!=null){
           resp.get().setDescribtion(pay.getDescribtion());
       }else {
           resp.get().setActualAmount(subOrderResp.getAmount());
       }
        //先飞后付立减金需要特殊处理
        if (handConfig.getVerticalReduction().equals(resp.get().getPayMethodName())) {
            resp.get().setActualAmount(resp.get().getActualAmount());
            if (resp.get().isUseDeduct()) {
                resp.get().setDescribtion(handConfig.getPAFDesc());
            }
        }
        resp.get().setCurrentDatetime(DateUtils.getDateStringAllDate(new Date()));//当前数据返回时间
        dealOrderCouponList(resp.get());
        //计算订单还剩余多少时间
        Date bookingTime = DateUtils.toDate(resp.get().getBookingDatetime(), "yyyy-MM-dd HH:mm:ss");
        Date currentTime = DateUtils.toDate(resp.get().getCurrentDatetime(), "yyyy-MM-dd HH:mm:ss");
        long remainTime = handConfig.getOrderRemainTime() - (currentTime.getTime() - bookingTime.getTime());
        if (remainTime <= 0) {
            remainTime = 0;
        }
        resp.get().setOrderRemainTime(remainTime);
        return resp.get();
    }

    @Override
    public ShareOrderResp getShareOrderResult(SubOrderResp subOrderResp, List<OrderBase> subTiccetList, String ip, String channelCode) {
        ShareOrderResp shareOrderResp =new ShareOrderResp();
        //呼叫中心订单暂不处理
        AtomicReference<OrderDetailResp> resp = new AtomicReference<>(new OrderDetailResp());
        if (!ChannelCodeEnum.CALLCENTER.getChannelCode().equals(subOrderResp.getOriginChannelCode()) && subTiccetList.size() > 1) {
            subTiccetList.removeIf(a -> a.getItem3() != null);
        }
        //调用统一订单接口，查询订单详情
        String path = handConfig.getOneOrderUrl() + HandlerConstants.SUB_QUERY_REFUND_APPLY;
        int ticketOrderIdx = 0;
        for (OrderBase orderBase : subTiccetList) {
            PtRefundApplyReq refundApply = new PtRefundApplyReq(HandlerConstants.VERSION,
                    channelCode, HandlerConstants.B2C_USER_NO, orderBase.getItem2(), "");
            Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
            // 查询 /Refund/RefundApply
            HttpResult serviceDetailResult = HttpUtil.doPostClient(refundApply, path, headMap, handConfig.getReadTimeout(), handConfig.getConnectTimeout());
            //
            if (serviceDetailResult.isResult() && StringUtils.isNotBlank(serviceDetailResult.getResponse())) {
                PtRefundApplyResp reRefundResp = (PtRefundApplyResp) JsonUtil.jsonToBean(serviceDetailResult.getResponse(), PtRefundApplyResp.class);
                //获取订单中第一个机票子订单信息，其余的子机票订单只保留其中的乘客信息
                getOrderDetailResult(subOrderResp, resp, ticketOrderIdx, reRefundResp);
                ticketOrderIdx++;
            }
        }
        //计算跨天数和飞行时间,查询经停城市
        calculateCityInfo(resp, ip, channelCode);
        // 处理前端展示旅客及航段信息
        shareOrderResp.setPassengerList(genOrderDetailPassengerList(resp.get().getOrderPassengerInfoList(), resp));
        shareOrderResp.setSegmentList(genOrderDetailSegmentList(resp.get().getTicketRangeType(), resp.get().getOrderPassengerInfoList(), channelCode, ip));
        resp.get().setSegmentList(shareOrderResp.getSegmentList());
        dealChdInfRuleInfo(resp.get());
        shareOrderResp.setSegmentRuleInfos(resp.get().getSegmentRuleInfos());
        shareOrderResp.setBrandCode(resp.get().getBrandCode());
        shareOrderResp.setInterFlag(resp.get().getInterFlag());
        shareOrderResp.setRouteType(resp.get().getRouteType());
        return shareOrderResp;
    }

    /**
     * 处理优选服务
     *
     * @param subOrderResp
     * @param ip
     * @param channelCode
     * @param userNo
     * @param detailReq
     * @param resp
     */
    private void handlerPremium(SubOrderResp subOrderResp, String ip, String channelCode, String userNo, OrderDetailRequest detailReq, AtomicReference<OrderDetailResp> resp) {
        OrderDetailResp orderDetailResp = resp.get();
        //********如果subTiccetList有内容就查询远程接口*********
        List<OrderBase> subtOrderBaseInfoList = subOrderResp.getSubtOrderBaseInfoList();
        Optional<OrderBase> couponOrderOptional = subtOrderBaseInfoList.stream().filter(orderBase -> orderBase.getItem1().equals("CouponOrder")).findFirst();
        if (!couponOrderOptional.isPresent()) {
            return;
        }
        //查询
        CouponProductOrderGetRequestDto couponProductOrderGetRequestDto = new CouponProductOrderGetRequestDto();
        couponProductOrderGetRequestDto.setChannelCode(channelCode);
        couponProductOrderGetRequestDto.setUserNo(ChannelUtils.getChannelInfo(channelCode, "10"));
        couponProductOrderGetRequestDto.setChannelOrderNo(detailReq.getChannelOrderNo());
        couponProductOrderGetRequestDto.setOrderNo(detailReq.getOrderNo());
        couponProductOrderGetRequestDto.setFfpId(detailReq.getCustomerNo());
        couponProductOrderGetRequestDto.setIsRemoved("0");
        couponProductOrderGetRequestDto.setPageNo(1);
        couponProductOrderGetRequestDto.setPageSize(20);
        Map<String, String> orderManageHeadMap = HttpUtil.getHeaderMap(ip, "");
        //查询远程接口,获取权益券信息
        PtSaleCouponOrderGetResponse ptSaleCouponOrderGetResponse = orderManage.queryCouponList(couponProductOrderGetRequestDto, orderManageHeadMap);
        List<PtSaleCouponOrderGetResponse.CouponOrder> couponOrderList = ptSaleCouponOrderGetResponse.getCouponOrderList();
        if (StringUtil.isNullOrEmpty(couponOrderList)) {
            return;
        }
        for (PtSaleCouponOrderGetResponse.CouponOrder couponOrder : couponOrderList) {
            List<PtSaleCouponOrderGetResponse.CouponOrder.SaleCoupon> saleCouponList = couponOrder.getSaleCouponList();
            Map<String, List<PtSaleCouponOrderGetResponse.CouponOrder.SaleCoupon>> sourceTypeMap = saleCouponList.stream()
                    .collect(Collectors.groupingBy(PtSaleCouponOrderGetResponse.CouponOrder.SaleCoupon::getCouponSource));
            List<OrderDetailPremium> orderDetailPremiumList = new ArrayList<>();
            //只要对应的4个,可优化,暂不优化
            OrderDetailPremium rescheduleDetail = buildOrderDetailPremium(sourceTypeMap, VoucherTypesEnum.RESCHEDULE.getCode());
            if (rescheduleDetail != null) {
                orderDetailPremiumList.add(rescheduleDetail);
            }
            OrderDetailPremium upgradeDetail = buildOrderDetailPremium(sourceTypeMap, VoucherTypesEnum.UPGRADECOUPON.getCode());
            if (upgradeDetail != null) {
                orderDetailPremiumList.add(upgradeDetail);
            }
            OrderDetailPremium loungeDetail = buildOrderDetailPremium(sourceTypeMap, VoucherTypesEnum.LOUNGECOUPON.getCode());
            if (loungeDetail != null) {
                orderDetailPremiumList.add(loungeDetail);
            }
            OrderDetailPremium baggageDetail = buildOrderDetailPremium(sourceTypeMap, VoucherTypesEnum.BAGGAGECOUPON.getCode());
            if (baggageDetail != null) {
                orderDetailPremiumList.add(baggageDetail);
            }
            OrderDetailPremium disneyDetail = buildOrderDetailPremium(sourceTypeMap, VoucherTypesEnum.DISNEY.getCode());
            if (disneyDetail != null) {
                orderDetailPremiumList.add(disneyDetail);
            }
            OrderDetailPremium innnDetail = buildOrderDetailPremium(sourceTypeMap, VoucherTypesEnum.INNN.getCode());
            if (innnDetail != null) {
                innnDetail.setRemark(couponOrder.getRemark());
                orderDetailPremiumList.add(innnDetail);
            }
            orderDetailResp.setPremiumList(orderDetailPremiumList);
            for (OrderDetailPremium orderDetailPremium : orderDetailPremiumList) {
                PtSaleCouponOrderGetResponse.CouponOrder.SaleCoupon saleCoupon = orderDetailPremium.getOrderDetailPremiumProductList().get(0);
                double productOnePrice = saleCoupon.getPrice();
                int count = Integer.parseInt(orderDetailPremium.getBookingCount());
                double allPrice = productOnePrice * count;
                //添加价格
                resp.get().setActualAmount(resp.get().getActualAmount() + allPrice);
            }
        }
    }

    /**
     * 优选服务构建
     *
     * @param sourceTypeMap
     * @param code
     * @return
     */
    private OrderDetailPremium buildOrderDetailPremium(Map<String, List<PtSaleCouponOrderGetResponse.CouponOrder.SaleCoupon>> sourceTypeMap, String code) {
        List<PtSaleCouponOrderGetResponse.CouponOrder.SaleCoupon> saleCouponList = sourceTypeMap.get(code);
        if (StringUtil.isNullOrEmpty(saleCouponList)) {
            return null;
        }
        OrderDetailPremium orderDetailPremium = new OrderDetailPremium();

        PtSaleCouponOrderGetResponse.CouponOrder.SaleCoupon saleCouponFirst = saleCouponList.get(0);
        String name = getPremiumNameBySource(saleCouponFirst.getCouponSource());
        orderDetailPremium.setPremiumName(name);
        orderDetailPremium.setOrderNo(saleCouponFirst.getThirdSvcOrderNo());
        orderDetailPremium.setChannelOrderNo(saleCouponFirst.getThirdSvcChannelOrderNo());
        orderDetailPremium.setBookingCount(String.valueOf(saleCouponList.size()));
        orderDetailPremium.setCouponState(saleCouponFirst.getThirdSvcCouponState());
        orderDetailPremium.setEquityUse(saleCouponFirst.isIsThirdSvcEquityUse());
        if(VoucherTypesEnum.DISNEY.getCode().equals(code)){
            String  productName  =  StringUtils.substringBefore(saleCouponFirst.getActivityName(), "—");
            orderDetailPremium.setProductName(productName);
        }

        orderDetailPremium.setProductType(code);

        Double allPremiumPrice = 0.0;
        for (PtSaleCouponOrderGetResponse.CouponOrder.SaleCoupon saleCoupon : saleCouponList) {
            allPremiumPrice += saleCoupon.getPrice();
        }
        orderDetailPremium.setAllPremiumPrice(allPremiumPrice);

        //退款申请中,或者已退款,都设置为true,前端不显示
        boolean refundOrApplyRefund = saleCouponList.stream()
                .anyMatch(saleCoupon -> OrderCouponStateEnum.ApplyRefund.getStateCode().equals(saleCoupon.getCouponState())
                        || OrderCouponStateEnum.Refund.getStateCode().equals(saleCoupon.getCouponState()));
        orderDetailPremium.setRefundOrApplyRefund(refundOrApplyRefund);

        //只要有一个不为Not类型则显示不可退款
        boolean refundedFlag = saleCouponList.stream()
                .allMatch(saleCoupon -> saleCoupon.getCouponState().equals(OrderCouponStateEnum.Not.getStateCode()));
        orderDetailPremium.setRefundedFlag(refundedFlag);
        //第一个是已取消则显示已取消
        //第一显示已退款则显示已退款
        //refundedFlag为true则显示已使用,false显示未使用
        //根据这个进行终止
        boolean flag = true;
        flag = premiumSetStatus(orderDetailPremium, saleCouponFirst, flag, OrderCouponStateEnum.Cancel, false);
        flag = premiumSetStatus(orderDetailPremium, saleCouponFirst, flag, OrderCouponStateEnum.Refund, false);
        flag = premiumOneSetStatus(orderDetailPremium, saleCouponList, flag, OrderCouponStateEnum.Giving, OrderCouponStateEnum.Used.getStateCode(), OrderCouponStateEnum.Used.getDesc());
        flag = premiumOneSetStatus(orderDetailPremium, saleCouponList, flag, OrderCouponStateEnum.GiveAway, OrderCouponStateEnum.Used.getStateCode(), OrderCouponStateEnum.Used.getDesc());
        flag = premiumOneSetStatus(orderDetailPremium, saleCouponList, flag, OrderCouponStateEnum.Used, OrderCouponStateEnum.Used.getStateCode(), OrderCouponStateEnum.Used.getDesc());
        if (refundedFlag) {
            orderDetailPremium.setPremiumStatus(OrderCouponStateEnum.Not.getStateCode());
            orderDetailPremium.setPremiumStatusName(OrderCouponStateEnum.Not.getDesc());
        } else {
            orderDetailPremium.setPremiumStatus("");
            orderDetailPremium.setPremiumStatus("");
        }
        orderDetailPremium.setOrderDetailPremiumProductList(saleCouponList);
        return orderDetailPremium;
    }

    private String getPremiumNameBySource(String couponSource) {
        PremiumEnum[] values = PremiumEnum.values();
        for (PremiumEnum value : values) {
            if (value.getCode().equals(couponSource)) {
                return value.getName();
            }
        }
        return "";
    }

    /**
     * 只要有一个满足则设置
     *
     * @param orderDetailPremium
     * @param saleCouponList
     * @param flag
     * @param apply
     * @param code
     * @param name
     * @return
     */
    private boolean premiumOneSetStatus(OrderDetailPremium orderDetailPremium,
                                        List<PtSaleCouponOrderGetResponse.CouponOrder.SaleCoupon> saleCouponList,
                                        boolean flag, OrderCouponStateEnum apply,
                                        String code, String name) {
        if (flag) {
            //有一个满足,就返回true
            boolean anyMatch = saleCouponList.stream().anyMatch(saleCoupon -> apply.getStateCode().equals(saleCoupon.getCouponState()));
            if (anyMatch) {
                orderDetailPremium.setPremiumStatus(code);
                orderDetailPremium.setPremiumStatusName(name);
                flag = false;
            }
        }
        return flag;
    }

    /**
     * 优选服务处理状态
     *
     * @param orderDetailPremium
     * @param saleCouponFirst
     * @param flag               是否结束
     * @param cancel
     * @return 是否结束
     */
    private boolean premiumSetStatus(OrderDetailPremium orderDetailPremium, PtSaleCouponOrderGetResponse.CouponOrder.SaleCoupon saleCouponFirst,
                                     boolean flag,
                                     OrderCouponStateEnum cancel,
                                     boolean isEmpty) {
        if (flag) {
            if (saleCouponFirst.getCouponState().equals(cancel.getStateCode())) {
                if (isEmpty) {
                    orderDetailPremium.setPremiumStatus("");
                    orderDetailPremium.setPremiumStatusName("");
                } else {
                    orderDetailPremium.setPremiumStatus(cancel.getStateCode());
                    orderDetailPremium.setPremiumStatusName(cancel.getDesc());
                }
                flag = false;
            }
        }
        return flag;
    }

    /**
     * 提取退改规则行李额
     *
     * @param resp
     */
    private void dealChdInfRuleInfo(OrderDetailResp resp) {
        List<SegmentRuleInfo> segmentRuleInfoList = resp.getSegmentList().stream()
                .map(OrderDetailSegment::getSegmentRuleInfo).collect(Collectors.toList());
        resp.setSegmentRuleInfos(segmentRuleInfoList);
        resp.setBrandCode("");
        if (CollectionUtils.isNotEmpty(segmentRuleInfoList)) {
            resp.setBrandCode(StringUtils.isNotBlank(segmentRuleInfoList.get(0).getBrandCode()) ? segmentRuleInfoList.get(0).getBrandCode() : "");
        }
    }

    /**
     * 处理使用的券信息
     *
     * @param response
     */
    private void dealOrderCouponList(OrderDetailResp response) {
        response.getOrderPassengerInfoList().stream().filter(orderPassengerInfo -> "Y".equals(orderPassengerInfo.getUseUnlimitedFlyFlag())
                && orderPassengerInfo.getUseUnlimitedFlyValue().intValue() > 0).forEach(orderPassengerInfo -> {
            Optional<OrderCouponInfo> optional = response.getUnlimitedCouponList().stream().filter(couponInfo ->
                    couponInfo.getCouponNo().equals(orderPassengerInfo.getUseUnlimitedFlyCardNo())).findFirst();
            // 券信息存在则添加
            if (!optional.isPresent()) {
                OrderCouponInfo orderCouponInfo = new OrderCouponInfo();
                orderCouponInfo.setNumber(1);
                orderCouponInfo.setCouponPrice(orderPassengerInfo.getUseUnlimitedFlyValue());
                orderCouponInfo.setCouponNo(orderPassengerInfo.getUseUnlimitedFlyCardNo());
                if (PassengerTypeEnum.CHD.getPassType().equals(orderPassengerInfo.getPassengerType())) {
                    orderCouponInfo.setCouponName(VoucherTypesEnum.CHILD_UNLIMITED_FLY.getName());
                    orderCouponInfo.setCouponSource(VoucherTypesEnum.CHILD_UNLIMITED_FLY.getCode());
                } else {
                    orderCouponInfo.setCouponName(VoucherTypesEnum.ADT_UNLIMITED_FLY.getName());
                    orderCouponInfo.setCouponSource(VoucherTypesEnum.ADT_UNLIMITED_FLY.getCode());
                }
                response.getUnlimitedCouponList().add(orderCouponInfo);
            }
        });
    }

    /**
     * 将航段换乘信息整合
     *
     * @param resp 需要整合的结果
     */
    private void combineTransferSegment(AtomicReference<OrderDetailResp> resp) {
        if (CollectionUtils.isEmpty(resp.get().getOrderPassengerInfoList())) {
            return;
        }
        resp.get().getOrderPassengerInfoList().forEach(orderPassengerInfo -> {
            if (CollectionUtils.isNotEmpty(orderPassengerInfo.getSegmentPriceInfoList()) && orderPassengerInfo.getSegmentPriceInfoList().size() > 1) {
                //往返方向分组处理
                List<SegmentPriceInfo> goInfos = new ArrayList<>();
                List<SegmentPriceInfo> backInfos = new ArrayList<>();
                for (int i = 0; i < orderPassengerInfo.getSegmentPriceInfoList().size(); i++) {
                    if (FlightDirection.GO.getCode().equals(orderPassengerInfo.getSegmentPriceInfoList().get(i).getFlightDirection())) {
                        goInfos.add(orderPassengerInfo.getSegmentPriceInfoList().get(i));
                    } else if (FlightDirection.BACK.getCode().equals(orderPassengerInfo.getSegmentPriceInfoList().get(i).getFlightDirection())) {
                        backInfos.add(orderPassengerInfo.getSegmentPriceInfoList().get(i));
                    }
                }
                //当去程多于一段以上时判断其为联程航班
                if (goInfos.size() > 1) {
                    // 当有返程票时，默认隐藏
                    combineSegmentPriceInfos(goInfos);
                }
                if (backInfos.size() > 1) {
                    combineSegmentPriceInfos(backInfos);
                }
            }
        });
    }

    /**
     * 将多航段换乘信息转化成具有换乘信息的集合
     * 例： 入参：[{北京—青岛}, {青岛—上海}, {上海—赫尔辛基}]
     *
     * @param priceInfos 航段信息 在前一段中加入中转信息
     * @return
     */
    private void combineSegmentPriceInfos(List<SegmentPriceInfo> priceInfos) {
        // 从第二个航段开始
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(DateUtils.YYYY_MM_DD_HH_MM_PATTERN);
        for (int i = 1; i < priceInfos.size(); i++) {
            // 中转前航段
            SegmentPriceInfo before = priceInfos.get(i - 1);
            // 中转后航段
            SegmentPriceInfo after = priceInfos.get(i);
            TransferInfo transferInfo = new TransferInfo("中转");
            transferInfo.setTransferCityName(before.getArrCityName());
            transferInfo.setTransferAirPort(before.getArrAirport());
            try {
                long interval = simpleDateFormat.parse(after.getDepDateTime()).getTime() - simpleDateFormat.parse(before.getArrDateTime()).getTime();
                transferInfo.setTransferTime(interval);
            } catch (ParseException e) {
                log.error("计算换乘间隔时间异常", e);
            }
            transferInfo.setTransferDesc(before.getArrAirport().equals(after.getDepAirport()) ? "相同机场中转" : "不同机场中转");
            before.setTransferInfo(transferInfo);
        }
    }

    /**
     * 计算跨天数和飞行时间,查询经停城市
     */
    private void calculateCityInfo(AtomicReference<OrderDetailResp> resp, String ip, String channelCode) {
        for (OrderPassengerInfo orderPassengerInfo : resp.get().getOrderPassengerInfoList()) {
            orderPassengerInfo.getSegmentPriceInfoList().forEach(segmentPriceInfo -> {
                //计算飞行时间
                //出发城市
                AirPortInfoDto depAirPort = localCacheService.getLocalAirport(segmentPriceInfo.getDepAirport());
                //到达城市
                AirPortInfoDto arrAirPort = localCacheService.getLocalAirport(segmentPriceInfo.getArrAirport());
                if (depAirPort != null && arrAirPort != null) {
                    String depCityTimeZone = depAirPort.getCityTimeZone();
                    String arrCityTimeZone = arrAirPort.getCityTimeZone();
                    //飞行时长
                    if ((!StringUtil.isNullOrEmpty(arrCityTimeZone)) &&
                            (!StringUtil.isNullOrEmpty(depCityTimeZone)) &&
                            (!StringUtil.isNullOrEmpty(segmentPriceInfo.getDepDateTime())) &&
                            (!StringUtil.isNullOrEmpty(segmentPriceInfo.getArrDateTime()))) {
                        //添加夏、冬令时处理
                        if (!depCityTimeZone.equals(arrCityTimeZone)) {
                            depCityTimeZone = FlightUtil.convertSummerOrWinterTime(depCityTimeZone, segmentPriceInfo.getDepDateTime(), depAirPort);
                            arrCityTimeZone = FlightUtil.convertSummerOrWinterTime(arrCityTimeZone, segmentPriceInfo.getArrDateTime(), arrAirPort);
                        }
                        long flightTime = DateUtils.calDuration(segmentPriceInfo.getDepDateTime(), depCityTimeZone, segmentPriceInfo.getArrDateTime(), arrCityTimeZone);
                        segmentPriceInfo.setFlightTime(flightTime);
                        int day = DateUtils.diffDays(segmentPriceInfo.getDepDateTime().substring(0, 10), segmentPriceInfo.getArrDateTime().substring(0, 10), DateUtils.YYYY_MM_DD_PATTERN);
                        segmentPriceInfo.setDay(day < 0 ? 0 : day);
                    }
                }
                //获取经停城市
                if (segmentPriceInfo.getStopNumber() != null && Integer.parseInt(segmentPriceInfo.getStopNumber()) > 0) {
                    FlightInfo flightInfo = new FlightInfo();
                    flightInfo.setFlightNo(segmentPriceInfo.getFlightNo());
                    flightInfo.setFlightDate(DateUtils.dateToString(DateUtils.toDate(segmentPriceInfo.getDepDateTime(), DateUtils.YYYY_MM_DD_PATTERN), DateUtils.YYYY_MM_DD_PATTERN));
                    flightInfo.setDepAirport(segmentPriceInfo.getDepAirport());
                    flightInfo.setArrAirport(segmentPriceInfo.getArrAirport());
                    List<FlightInfo> flightInfos = basicService.queryFlightInfo(flightInfo);
                    if (CollectionUtils.isNotEmpty(flightInfos)) {
                        String stopAirport = flightInfos.get(0).getStopAirport();
                        String flightDate = flightInfos.get(0).getFlightDate();
                        AirPortInfoDto info = localCacheService.getLocalAirport(stopAirport, flightDate);
                        if (info != null) {
                            segmentPriceInfo.setStopCityName(info.getCityName());
                        }
                    }
                }
            });
        }
    }

    /**
     * 处理行李信息
     *
     * @param subOrderResp
     * @param channelCode
     * @param detailReq
     * @param resp
     */
    private void handlerWeightOrder(SubOrderResp subOrderResp, String channelCode, String userNo, OrderDetailRequest detailReq, AtomicReference<OrderDetailResp> resp) {
        List<OrderBase> subWeightOrderList = TicketOrderConvert.getSubOrderBaseInfoList(subOrderResp.getSubtOrderBaseInfoList(), "WeightOrder");
        if (!StringUtil.isNullOrEmpty(subWeightOrderList)) {
            PtWeightProResp weightProResp = queryWeightInfo(userNo, channelCode, detailReq.getChannelOrderNo(), detailReq.getOrderNo(), detailReq.getCustomerNo());
            if (!StringUtil.isNullOrEmpty(weightProResp.getBaggageBuyList())) {
                Double weightAmount = 0.0;
                for (BaggageBuy baggageBuy : weightProResp.getBaggageBuyList()) {//遍历处理
                    weightAmount = weightAmount + baggageBuy.getWeightOrderAmount();
                }
                resp.get().setBaggageBuyList(weightProResp.getBaggageBuyList());
                resp.get().setBaggageBuyAmount(weightAmount);
                resp.get().setActualAmount(resp.get().getActualAmount() + weightAmount);
            }
        }
    }

    /**
     * 处理邮寄行程单
     *
     * @param subOrderResp
     * @param ip
     * @param channelCode
     * @param detailReq
     * @param resp
     */
    private void handlerTripCert(SubOrderResp subOrderResp, String ip, String channelCode, String userNo, OrderDetailRequest detailReq, AtomicReference<OrderDetailResp> resp) {
        List<OrderBase> subTicketDeliveryList = TicketOrderConvert.getSubOrderBaseInfoList(subOrderResp.getSubtOrderBaseInfoList(), "TicketDelivery");
        if (CollectionUtils.isNotEmpty(subTicketDeliveryList)) {
            TicketDeliveryGetResp deliveryGetResp = queryDeliveryInfo(userNo, channelCode, detailReq.getChannelOrderNo(), detailReq.getOrderNo(), detailReq.getCustomerNo(), ip);
            if (deliveryGetResp.getTripCertSendInfo() != null) {
                TripCertSendInfo tripCertSendInfo = deliveryGetResp.getTripCertSendInfo();
                deliveryGetResp.getTripCertSendInfo().setDeliveryState(deliveryGetResp.getDeliveryState());
                //详细的地址
                //转化省市代码
                String key = PROVINCE_CITY_KEY + ":" + tripCertSendInfo.getDeliverToProvince();
                String data = apiRedisService.getData(key);
                ProvinceCity provinceCity;
                if (StringUtil.isNullOrEmpty(data)) {
                    String json = com.juneyaoair.mobile.handler.util.FileUtils.readJson("/province.json");
                    Map<String, ProvinceCity> map = (HashMap<String, ProvinceCity>) JsonUtil.jsonToMap(json, new TypeToken<HashMap<String, ProvinceCity>>() {
                    }.getType());
                    provinceCity = map.get(tripCertSendInfo.getDeliverToProvince());
                    //存到redis中。存储30天
                    apiRedisService.replaceData(key, JsonUtil.objectToJson(provinceCity), 24 * 3600L * 30);
                } else {
                    provinceCity = (ProvinceCity) JsonUtil.jsonToBean(data, ProvinceCity.class);
                }
                String provinceNm = provinceCity.getProvinceNm();
                CityInfo cityInfo = provinceCity.getCityList().stream().filter(city -> tripCertSendInfo.getDeliverToCity().equals(city.getCityCode())).findFirst().orElse(null);
                String cityNm = cityInfo == null ? "" : cityInfo.getCityNm();
                tripCertSendInfo.setProvinceCityAddress(provinceNm + cityNm + tripCertSendInfo.getDeliveryAddress());
                resp.get().setTripCertSendInfo(tripCertSendInfo);
                resp.get().setActualAmount(resp.get().getActualAmount() + deliveryGetResp.getTripCertSendInfo().getDeliveryFee());
            }
        }
    }


    /**
     * 处理wifi订单
     *
     * @param subOrderResp
     * @param resp
     * @param reRefundResp
     */
    private void handlerWifi(SubOrderResp subOrderResp, AtomicReference<OrderDetailResp> resp, PtRefundApplyResp reRefundResp) {
        List<PtPassengerSegment> passengerSegmentList = reRefundResp.getPassengerSegmentList();
        int count = 0;
        if (passengerSegmentList == null) {
            return;
        }
        for (PtPassengerSegment ptPassengerSegment : passengerSegmentList) {
            if (!StringUtil.isNullOrEmpty(ptPassengerSegment.getFareBasis()) && ptPassengerSegment.getFareBasis().endsWith(FareBasisEnum.WIFI_FAREBASIS.getFareBasisCode())) {
                count++;
            }
        }
        if (count > 0) {
            List<WifiOrderInfo> wifiOrderInfoList = new ArrayList<>();
            WifiOrderInfo wifiOrderInfo = new WifiOrderInfo();
            wifiOrderInfo.setName(handConfig.getWifiTravelPrivilege().getName());
            wifiOrderInfo.setDescription(handConfig.getWifiTravelPrivilege().getDescription());
            wifiOrderInfo.setCount(count);
            wifiOrderInfoList.add(wifiOrderInfo);
            resp.get().setWifiOrderInfo(wifiOrderInfoList);
        }
    }

    /**
     * 处理休息室订单
     *
     * @param subOrderResp
     * @param channelCode
     * @param detailReq
     * @param resp
     */
    private void handlerLounge(SubOrderResp subOrderResp, String channelCode, String userNo, OrderDetailRequest detailReq, AtomicReference<OrderDetailResp> resp) {
        List<OrderBase> subLoungeOrderList = TicketOrderConvert.getSubOrderBaseInfoList(subOrderResp.getSubtOrderBaseInfoList(), "LoungeOrder");
        if (CollectionUtils.isNotEmpty(subLoungeOrderList)) {
            LoungeGetResp loungeResp = queryLoungeInfo(userNo, channelCode, detailReq.getChannelOrderNo(), detailReq.getOrderNo(), detailReq.getCustomerNo());
            if (CollectionUtils.isNotEmpty(loungeResp.getLoungeBuyList())) {
                resp.get().setLoungeBuyList(loungeResp.getLoungeBuyList());
                Double loungeAmount = 0.0;
                for (LoungeBuy loungeBuy : loungeResp.getLoungeBuyList()) {
                    loungeAmount += loungeBuy.getLoungeAmount();
                }
                resp.get().setLoungeAmount(loungeAmount);
                resp.get().setActualAmount(resp.get().getActualAmount() + loungeAmount);
            }
        }
    }

    //计算各种价格
    private void calculateAmount(AtomicReference<OrderDetailResp> resp) {
        //处理个别信息
        //机票总额 //机建、燃油总额 //保险总额 //票面优惠 //使用积分 //使用优惠券 //应付总额
        Double totalAmount = 0.0;
        Double taxAmount = 0.0;
        Double insuranceAmount = 0.0;
        Double redeemAmount = 0.0;
        Double useScore = 0.0;
        Double couponAmount = 0.0;
        Double actualAmount = 0.0;
        Double giftScore = 0.0;
        for (OrderPassengerInfo orderPass : resp.get().getOrderPassengerInfoList()) {
            //票面价
            Double parPrice = 0.0;
            Double passTotalTax = orderPass.getCNTax() + orderPass.getYQTax() + orderPass.getOtherTax() + orderPass.getQFee();
            taxAmount += passTotalTax;
            //处理已退的保险,状态为N，且存在保险,只针对保险订单和机票订单
            if ("N".equals(orderPass.getIsBuyInsurance()) &&
                    (!StringUtil.isNullOrEmpty(orderPass.getInsuranceList())) &&
                    (OrderSortEnum.Insurance.getOrderSort().equals(resp.get().getOrderSort())
                            || OrderSortEnum.Normal.getOrderSort().equals(resp.get().getOrderSort()))) {
                double passAmount = 0.00;//乘客的保险金额
                for (SegmentPriceInfo segmentPriceInfo : orderPass.getSegmentPriceInfoList()) {//累计乘客的保险金额
                    double segAmount = 0.00;//每段的保险金额
                    if ((!segmentPriceInfo.getIsBuyInsurance()) && (!StringUtil.isNullOrEmpty(segmentPriceInfo.getInsuranceList()))) {
                        for (InsuranceInfo insuranceInfo : segmentPriceInfo.getInsuranceList()) {//累计航段中的保险金额
                            segAmount += insuranceInfo.getInsuranceAmount() * insuranceInfo.getInsuranceNumber();
                        }
                    }
                    segmentPriceInfo.setInsuranceAmount(segAmount);
                    passAmount += segAmount;
                }
                orderPass.setInsuranceAmount(passAmount);
            }
            insuranceAmount += orderPass.getInsuranceAmount();
            Double useScoreOther = 0.0;
            Double useCouponAmountOther = 0.0;
            totalAmount += orderPass.getTicketPrice();
            useCouponAmountOther += orderPass.getCouponAmount();
            //订单实际支付金额
            actualAmount += orderPass.getPricePaid() + passTotalTax + orderPass.getInsuranceAmount();
            for (SegmentPriceInfo segPrc : orderPass.getSegmentPriceInfoList()) {
                useScoreOther += segPrc.getUseScore();
                giftScore += segPrc.getGiftScore();
            }
            redeemAmount += orderPass.getTicketPrice() - (orderPass.getPricePaid() == null ? 0.0 : orderPass.getPricePaid()) - useScoreOther - useCouponAmountOther;
            useScore += useScoreOther;
            couponAmount += useCouponAmountOther;
            parPrice = orderPass.getPricePaid() + useScoreOther + useCouponAmountOther +(resp.get().getDiscountAmountTotal()==null?0.0:resp.get().getDiscountAmountTotal().doubleValue());
            orderPass.setParPrice(parPrice);//乘客票面价
        }
        String payMethodName = resp.get().getPayMethodName();
        Double payDeductAmt = resp.get().getPayDeductAmt();

        if (handConfig.getVerticalReduction().equals(payMethodName) && null != payDeductAmt) {
            if (actualAmount - payDeductAmt >= taxAmount) {
                resp.get().setUseDeduct(true);
            }
            actualAmount = Math.max(actualAmount - payDeductAmt, taxAmount);
        }
        resp.get().setTotalAmount(totalAmount);
        resp.get().setTaxAmount(taxAmount);
        resp.get().setInsuranceAmount(insuranceAmount);
        resp.get().setRedeemAmount(redeemAmount);
        //积分抵扣金额
        resp.get().setUseScore(useScore);
        //优惠券处理 2017-02-06 10:26:18
        resp.get().setCouponAmount(couponAmount);
        //应付金额
        resp.get().setActualAmount(actualAmount);
        resp.get().setGiftScore(giftScore);
    }

    /**
     * 查询机票订单
     *
     * @param subOrderResp
     * @param resp
     * @param subOrderIndex
     * @param reRefundResp
     */
    private void getOrderDetailResult(SubOrderResp subOrderResp, AtomicReference<OrderDetailResp> resp, int subOrderIndex, PtRefundApplyResp reRefundResp) {
        // 改期订单使用改期券时，手续费为券的金额
        if (OrderSortEnum.Change.getOrderSort().equals(reRefundResp.getOrderSort())) {
            reRefundResp.getPassengerSegmentList().forEach(ptPassengerSegment ->
                    ptPassengerSegment.setUpgradeFee(ptPassengerSegment.getCouponAmount() + ptPassengerSegment.getUpgradeFee()));
        }
        if (0 == subOrderIndex) {
            resp.set(OrderDetailConvert.toOrderDetailReponse(subOrderResp, reRefundResp, localCacheService, handConfig));
        } else {
            List<OrderPassengerInfo> passList = OrderDetailConvert.toPassengerInfoList(reRefundResp, localCacheService, handConfig, null);
            resp.get().getOrderPassengerInfoList().addAll(passList);
            if (!StringUtil.isNullOrEmpty(reRefundResp.getOrderBriefInfoList())) {
                List<OrderBriefInfo> orderBriefInfoList = new ArrayList<>();
                for (PtOrderBriefInfo ptOrderBriefInfo : reRefundResp.getOrderBriefInfoList()) {
                    OrderBriefInfo orderBriefInfo = new OrderBriefInfo();
                    BeanUtils.copyProperties(ptOrderBriefInfo, orderBriefInfo);
                    orderBriefInfoList.add(orderBriefInfo);
                }
                resp.get().setOrderBriefInfoList(orderBriefInfoList);
            }
        }
        //对人航段数据按照航段序号重新排序
        resp.get().getOrderPassengerInfoList().forEach(orderPassengerInfo -> orderPassengerInfo.getSegmentPriceInfoList().sort(Comparator.comparing(SegmentPriceInfo::getSegNO)));
        //重新处理舱位名称显示
        resp.get().getOrderPassengerInfoList().forEach(orderPassengerInfo -> orderPassengerInfo.getSegmentPriceInfoList().forEach(segmentPriceInfo -> segmentPriceInfo.setCabinClassName(CommonUtil.showCabinClassName(segmentPriceInfo.getCabinClass(),null,reRefundResp.getOrderSort()))));
        //联程航班处理中转信息
        combineTransferSegment(resp);
        String dateStr = DateUtils.timeStampToDateStr(DateUtils.toDate(subOrderResp.getCreateDatetime(), DateUtils.YYYY_MM_DD_HH_MM_SS_PATTERN).getTime() + 20 * 60 * 1000);
        OrderPayStateEnum orderPayState = OrderPayStateConvert.convertState(subOrderResp.getState(), subOrderResp.getPayState(), resp.get().getOrderSort(),
                DateUtils.toDate(dateStr, DateUtils.YYYY_MM_DD_HH_MM_SS_PATTERN), DateUtils.toDate(DateUtils.getDateStringAllDate(new Date()), DateUtils.YYYY_MM_DD_HH_MM_SS_PATTERN),
                resp.get().getOrderPassengerInfoList());
        resp.get().setOrderPayState(orderPayState.getStateCode());
        resp.get().setOrderPayStateName(orderPayState.getStateDesc());
        // 取消状态和已出票状态的机票订单显示删除按钮
        List<OrderPayStateEnum> deletableStates = Arrays.asList(OrderPayStateEnum.Cancel, OrderPayStateEnum.TicketOut, OrderPayStateEnum.RefundApplying,
                OrderPayStateEnum.RefundApproved, OrderPayStateEnum.Refunding, OrderPayStateEnum.Refund, OrderPayStateEnum.RefundFailed);
        resp.get().setShowDeleteButton(deletableStates.contains(orderPayState));
        //退票按钮设置
        resp.get().setRefundBtn(showRefundBtn(resp.get().getOrderPayState(), resp.get().getFareType(), resp.get().getOrderPassengerInfoList()));
        resp.get().setTicketRangeType(reRefundResp.getTicketRangeType());
        resp.get().setChildrenOrderList(reRefundResp.getChildrenOrderList());
        //改期，升舱退改规则处理
        if (OrderSortEnum.Change.getOrderSort().equalsIgnoreCase(reRefundResp.getOrderSort())) {
            resp.get().setRefundRule(handConfig.getChangeOrderRefundRule());
        } else if (OrderSortEnum.Upgrade.getOrderSort().equalsIgnoreCase(reRefundResp.getOrderSort())) {
            resp.get().setRefundRule(handConfig.getUpdateOrderRefundRule());
        }
        resp.get().setAdultTicketNo(reRefundResp.getAdultTicketNo());
        resp.get().setAdultName(reRefundResp.getAdultName());
        resp.get().setEnableRefund(reRefundResp.isEnableRefund());
        resp.get().setDiscountAmountTotal(reRefundResp.getDiscountAmountTotal());
        resp.get().setUseScores(reRefundResp.getUseScores());
        resp.get().setRouteType(reRefundResp.getRouteType());
        resp.get().setInterFlag(reRefundResp.getInterFlag());
        if (CollectionUtils.isNotEmpty(reRefundResp.getTicketOrderExtList())){
            TicketOrderExt  ticketOrderExt=reRefundResp.getTicketOrderExtList().get(0);
            resp.get().setDeductionType(ticketOrderExt.getDeductionType());
        }
    }

    /**
     * 筛选乘机人列表
     *
     * @param orderPassengerInfos
     * @return
     */
    private List<OrderDetailPassenger> genOrderDetailPassengerList(List<OrderPassengerInfo> orderPassengerInfos, AtomicReference<OrderDetailResp> resp) {
        List<OrderDetailPassenger> passengers = Lists.newArrayList();
        // 乘机人Map 用于筛选不同的乘机人，相同证件视为同一乘客
        Map<String, List<OrderPassengerInfo>> orderPassengerInfoMap = orderPassengerInfos.stream()
                .collect(Collectors.groupingBy(OrderPassengerInfo::getCertNo));
        Map<String, TravelPrivilege> travelPrivilegeMap = new HashMap<>();
        for (List<OrderPassengerInfo> passengerInfos : orderPassengerInfoMap.values()) {
            OrderPassengerInfo orderPassengerInfo = passengerInfos.get(0);
            if (CollectionUtils.isNotEmpty(orderPassengerInfo.getPrivilegeList())) {
                orderPassengerInfo.getPrivilegeList().forEach(travelPrivilege -> {
                    // 不存在保存，存在则数量相加
                    if (!travelPrivilegeMap.containsKey(travelPrivilege.getPrivilegeId())) {
                        travelPrivilegeMap.put(travelPrivilege.getPrivilegeId(), travelPrivilege);
                    } else {
                        travelPrivilegeMap.get(travelPrivilege.getPrivilegeId())
                                .setNumber(travelPrivilege.getNumber() + travelPrivilegeMap.get(travelPrivilege.getPrivilegeId()).getNumber());
                    }
                });
            }
            OrderDetailPassenger orderDetailPassenger = new OrderDetailPassenger();
            orderDetailPassenger.setPassengerName(orderPassengerInfo.getPassengerName());
            orderDetailPassenger.setPassengerType(orderPassengerInfo.getPassengerType());
            orderDetailPassenger.setPassengerIdentity(HandlerConstants.PASSENGER_TYPE_OLD.equalsIgnoreCase(orderPassengerInfo.getPassengerIdentity())
                    ?orderPassengerInfo.getPassengerIdentity():"");
            orderDetailPassenger.setCertNo(SensitiveInfoHider.hideSensitiveInfo(orderPassengerInfo.getCertNo()));
            orderDetailPassenger.setCertType(orderPassengerInfo.getCertType());
            orderDetailPassenger.setUseFreePass(orderPassengerInfo.getUseFreePass());
            orderDetailPassenger.setUseFreePassCouponNo(orderPassengerInfo.getUseFreePassCouponNo());
            orderDetailPassenger.setBirthdate(orderPassengerInfo.getBirthdate());
            List<String> refundLabelList = passengerInfos.stream().filter(passenger -> StringUtils.isNotBlank(passenger.getRefundLabel()))
                    .map(OrderPassengerInfo::getRefundLabel).distinct().collect(Collectors.toList());
            Set<String> ticketNoSet = new HashSet<>();
            passengerInfos.stream().filter(passengerInfo -> StringUtils.isNotBlank(passengerInfo.getETicketNo())).forEach(passengerInfo -> {
                String[] ticketNos = passengerInfo.getETicketNo().split("/");
                ticketNoSet.addAll(Arrays.asList(ticketNos));
            });
            // 计算价格及保险
            passengerInfos.forEach(passengerInfo -> {
                if (null != passengerInfo.getYQTax()) {
                    orderDetailPassenger.setYQTax(orderDetailPassenger.getYQTax() + passengerInfo.getYQTax());
                }
                if (null != passengerInfo.getCNTax()) {
                    orderDetailPassenger.setCNTax(orderDetailPassenger.getCNTax() + passengerInfo.getCNTax());
                }
                if (null != passengerInfo.getParPrice()) {
                    orderDetailPassenger.setParPrice(orderDetailPassenger.getParPrice() + passengerInfo.getParPrice());
                }
                if (null != passengerInfo.getOtherTax()) {
                    orderDetailPassenger.setOtherTax(orderDetailPassenger.getOtherTax() + passengerInfo.getOtherTax());
                }
//                if (null != passengerInfo.getOriginalChangeFee()) {
//                    orderDetailPassenger.setOriginalChangeFee(passengerInfo.getOriginalChangeFee());
//                }
                if (null != passengerInfo.getQFee()) {
                    orderDetailPassenger.setQFee(orderDetailPassenger.getQFee() + passengerInfo.getQFee());
                }
                if (null != passengerInfo.getInsuranceAmount()) {
                    orderDetailPassenger.setInsuranceAmount(orderDetailPassenger.getInsuranceAmount() + passengerInfo.getInsuranceAmount());
                }
                if (null != passengerInfo.getUpgradeFee()) {
                    orderDetailPassenger.setUpgradeFee(orderDetailPassenger.getUpgradeFee() + passengerInfo.getUpgradeFee());
                }
                if (null == orderDetailPassenger.getInsuranceList()) {
                    orderDetailPassenger.setInsuranceList(Lists.newArrayList());
                }
                if (CollectionUtils.isNotEmpty(passengerInfo.getInsuranceList())) {
                    orderDetailPassenger.getInsuranceList().addAll(passengerInfo.getInsuranceList());
                }
            });
            orderDetailPassenger.setTicketNos(Lists.newArrayList(ticketNoSet));
            // 是否包含未退票
            boolean containNotRefund = passengerInfos.stream().anyMatch(passengerInfo -> StringUtils.isBlank(passengerInfo.getRefundLabel()));
            if (CollectionUtils.isNotEmpty(refundLabelList)) {
                if (containNotRefund) {
                    orderDetailPassenger.setRefundLabel(RefundLabelEnum.PartlyRefunded.getCode());
                } else if (refundLabelList.contains(RefundLabelEnum.Refunded.getCode())) {
                    orderDetailPassenger.setRefundLabel(RefundLabelEnum.Refunded.getCode());
                } else {
                    orderDetailPassenger.setRefundLabel(RefundLabelEnum.Refunding.getCode());
                }
            }
            orderDetailPassenger.setTaxInfoList(orderPassengerInfo.getTaxInfoList());
            passengers.add(orderDetailPassenger);
        }
        List<TravelPrivilege> privilegeList = Lists.newArrayList(travelPrivilegeMap.values());
        privilegeList.sort(Comparator.comparing(TravelPrivilege::getSortPriority));
        resp.get().setTravelPrivilegeList(privilegeList);
        return passengers;
    }

    /**
     * 筛选不重复的航段信息
     *
     * @param orderPassengerInfos
     * @return
     */
    private List<OrderDetailSegment> genOrderDetailSegmentList(String ticketRangeType, List<OrderPassengerInfo> orderPassengerInfos, String channelCode, String ip) {
        // 筛选不重复的航段信息 key : 出发城市+到达城市+航班号+航班日期
        Map<String, OrderDetailSegment> segmentMap = new HashMap<>();
        Map<Integer, Boolean> interFlagMap = new HashMap<>(); // 航段是否是国际航段Map， key为多程订单中的第几段，value为是否为国际航班
        int priority = 0; // 航段顺序
        int travelNo = -1; // 多程订单中的第几段
        for (OrderPassengerInfo orderPassengerInfo : orderPassengerInfos) {
            boolean travelNoCounted = false; // 当次是否已经计算第几程
            for (SegmentPriceInfo segmentPriceInfo : orderPassengerInfo.getSegmentPriceInfoList()) {
                String key = segmentPriceInfo.getDepCity() + segmentPriceInfo.getArrCity() + segmentPriceInfo.getFlightNo() + segmentPriceInfo.getDepDateTime();
                CityInfoDto deptCity = localCacheService.getLocalCity(segmentPriceInfo.getDepCity());
                CityInfoDto arrCity = localCacheService.getLocalCity(segmentPriceInfo.getArrCity());
                if (null == segmentMap.get(key)) {
                    OrderDetailSegment segment = new OrderDetailSegment();
                    SegmentRuleInfo segmentRuleInfo = new SegmentRuleInfo();
                    // 多程订单
                    if (HandlerConstants.ORDER_TICKET_RANGE_TYPE_MULTIPLE.equals(ticketRangeType)) {
                        if (!travelNoCounted) {
                            travelNoCounted = true;
                            travelNo++;
                            segmentRuleInfo.setFlightDirection(FlightDirection.GO.getCode());
                        }
                    } else {
                        segmentRuleInfo.setFlightDirection(segmentPriceInfo.getFlightDirection());
                        // 为方便前端展示，去程定义为第一程，返程为第二程
                        travelNo = FlightDirection.GO.getCode().equals(segmentRuleInfo.getFlightDirection()) ? 0 : 1;
                    }
                    Boolean isInternational = HandlerConstants.TRIP_TYPE_I.equals(deptCity.getIsInternational())
                            || HandlerConstants.TRIP_TYPE_I.equals(arrCity.getIsInternational());
                    Boolean travelInterFlag = null == interFlagMap.get(travelNo) ? false : interFlagMap.get(travelNo);
                    interFlagMap.put(travelNo, travelInterFlag || isInternational);
                    BeanUtils.copyNotNullProperties(segmentPriceInfo, segment, new String[]{"shippingRulesLabel"});
                    segment.setTravelNo(travelNo);
                    segment.setPriority(priority++);
                    if (HandlerConstants.BUS_PLUS.equals(segmentPriceInfo.getTourCode())){
                        if ("HKG".equals(segment.getArrCity())){
                            segment.setArrCityName("中国香港全境");
                            segment.setArrAirportName("中国香港全境");
                        }else {
                            segmentRuleInfo.setArrCityName(segment.getArrCityName());
                        }
                        if ("HKG".equals(segment.getDepCity())){
                            segment.setDepCityName("中国香港全境");
                            segment.setDepAirportName("中国香港全境");
                        }else {
                            segmentRuleInfo.setDepCityName(segment.getDepCityName());
                        }
                    }else {
                        segmentRuleInfo.setDepCityName(segment.getDepCityName());
                        segmentRuleInfo.setArrCityName(segment.getArrCityName());
                     }
                    segmentRuleInfo.setDepCity(segment.getDepCity());
                    segmentRuleInfo.setArrCity(segment.getArrCity());
                    segmentRuleInfo.setTravelNo(travelNo);
                    segmentRuleInfo.setCabin(segment.getCabin());
                    segmentRuleInfo.setCabinClassName(segmentPriceInfo.getCabinClassName());
                    segmentRuleInfo.setRefundedComment(segmentPriceInfo.getRefundedComment());
                    segmentRuleInfo.setChangedComment(segmentPriceInfo.getChangedComment());
                    segmentRuleInfo.setOrderDetailBaggage(segmentPriceInfo.getOrderDetailBaggage());
                    segment.setSegmentRuleInfo(segmentRuleInfo);
                    segment.setShippingRulesLabel(Sets.newHashSet());
                    segmentMap.put(key, segment);
                }
                // 设置行程标记信息
                if (CollectionUtils.isNotEmpty(segmentPriceInfo.getShippingRulesLabel())) {
                    segmentMap.get(key).getShippingRulesLabel().addAll(segmentPriceInfo.getShippingRulesLabel());
                }
                OrderDetailBaggage orderDetailBaggage = segmentPriceInfo.getOrderDetailBaggage();
                if (null != orderDetailBaggage && StringUtils.isNotBlank(orderDetailBaggage.getHandBaggeage()) && orderDetailBaggage.getHandBaggeage().contains(",")) {
                    String[] handBaggage = orderDetailBaggage.getHandBaggeage().split(",");
                    orderDetailBaggage.setCountLimit(handBaggage[0]);
                    orderDetailBaggage.setHandBaggeage(handBaggage[1]);
                }
                // 设置退改规则和行李额
                if (PassengerTypeEnum.ADT.getPassType().equals(orderPassengerInfo.getPassengerType())
                        &&!PassengerTypeEnum.OLD.getPassType().equals(orderPassengerInfo.getPassengerIdentity())) {
                    segmentMap.get(key).getSegmentRuleInfo().setRefundRuleList(segmentPriceInfo.getRefundRuleList());
                    segmentMap.get(key).getSegmentRuleInfo().setChangeRuleList(segmentPriceInfo.getChangeRuleList());
                    segmentMap.get(key).getSegmentRuleInfo().setOrderDetailBaggage(segmentPriceInfo.getOrderDetailBaggage());
                    //2021-05-13 订单详情国际品牌运价权益列表处理
                    String brandCode = segmentPriceInfo.getBrandCode();
                    if (StringUtils.isNotBlank(brandCode)) {
                        segmentMap.get(key).getSegmentRuleInfo().setBrandCode(brandCode);
                        String userNo = ChannelUtils.getChannelInfo(channelCode, "10");
                        PtPdmRequest ptPdmRequest = new PtPdmRequest(HandlerConstants.VERSION, channelCode, userNo);
                        String convertBrandCode = brandCode;
                        if (StringUtils.isNotBlank(segmentPriceInfo.getCabinClass())) {
                            String cabinClass = "C".equalsIgnoreCase(segmentPriceInfo.getCabinClass().substring(0, 1)) ? "J" : segmentPriceInfo.getCabinClass().substring(0, 1);
                            convertBrandCode = brandCode + cabinClass;
                        }
                        StringBuilder stringBuilder = new StringBuilder(convertBrandCode);
                        if (StringUtils.isNotBlank(segmentPriceInfo.getTourCode())&&segmentPriceInfo.getTourCode().contains("STUDENT")) {
                            convertBrandCode = stringBuilder.append("_").append(segmentPriceInfo.getTourCode()).toString();
                        }
                        List<InterBrandRightInfo> interBrandRightInfos = genInterBrandRightInfos(convertBrandCode, segmentPriceInfo.getCabin(), ptPdmRequest);
                        segmentMap.get(key).getSegmentRuleInfo().setInterBrandRightInfos(interBrandRightInfos);
                    }
                } else if (PassengerTypeEnum.CHD.getPassType().equals(orderPassengerInfo.getPassengerType())) {
                    if (null == segmentMap.get(key).getSegmentRuleInfo().getChildRule()) {
                        ChildRule childRule = new ChildRule();
                        childRule.setChangeRuleList(segmentPriceInfo.getChangeRuleList());
                        childRule.setRefundRuleList(segmentPriceInfo.getRefundRuleList());
                        childRule.setOrderDetailBaggage(segmentPriceInfo.getOrderDetailBaggage());
                        segmentMap.get(key).getSegmentRuleInfo().setChildRule(childRule);
                        segmentMap.get(key).getSegmentRuleInfo().setOrderDetailBaggage(segmentPriceInfo.getOrderDetailBaggage());
                    }
                } else if (PassengerTypeEnum.INF.getPassType().equals(orderPassengerInfo.getPassengerType())) {
                    if (null == segmentMap.get(key).getSegmentRuleInfo().getInfRule()) {
                        InfRule infRule = new InfRule();
                        infRule.setChangeRuleList(segmentPriceInfo.getChangeRuleList());
                        infRule.setRefundRuleList(segmentPriceInfo.getRefundRuleList());
                        infRule.setOrderDetailBaggage(segmentPriceInfo.getOrderDetailBaggage());
                        if (infRule.getOrderDetailBaggage()==null){
                            OrderDetailBaggage infBaggage = new OrderDetailBaggage();
                            infBaggage.setCheckBaggeage("0KG");
                            // 不存在使用默认值 否则使用订单返回
                            infBaggage.setSpecialRemark(AVObjectConvertV2.INF_SPECIAL_REMARK);
                            infRule.setOrderDetailBaggage(infBaggage);
                        }
                        segmentMap.get(key).getSegmentRuleInfo().setInfRule(infRule);
                    }
                }
            }
        }

        segmentMap.values().forEach(segment -> {
            segment.getSegmentRuleInfo().setInterFlag(interFlagMap.get(segment.getTravelNo()) ? HandlerConstants.FLIGHT_INTER_I : HandlerConstants.FLIGHT_INTER_D);
            // 检查儿童行李信息是否为空 为空默认使用成人行李信息 不存在默认0KG
            if (null == segment.getSegmentRuleInfo().getChildRule()) {
                ChildRule childRule = new ChildRule();
                OrderDetailBaggage orderDetailBaggage = new OrderDetailBaggage();
                OrderDetailBaggage atdOrderDetailBaggage = null == segment.getSegmentRuleInfo() ? null : segment.getSegmentRuleInfo().getOrderDetailBaggage();
                String checkBaggage = null != atdOrderDetailBaggage ? atdOrderDetailBaggage.getCheckBaggeage() : "0KG";
                orderDetailBaggage.setCheckBaggeage(checkBaggage);
                childRule.setOrderDetailBaggage(orderDetailBaggage);
                segment.getSegmentRuleInfo().setChildRule(childRule);
            }
            // 检查婴儿行李信息是否为空 为空默认0KG
            if (null == segment.getSegmentRuleInfo().getInfRule()) {
                InfRule infRule = new InfRule();
                OrderDetailBaggage orderDetailBaggage = new OrderDetailBaggage();
                orderDetailBaggage.setCheckBaggeage("0KG");
                // 不存在使用默认值 否则使用订单返回
                orderDetailBaggage.setSpecialRemark(AVObjectConvertV2.INF_SPECIAL_REMARK);
                infRule.setOrderDetailBaggage(orderDetailBaggage);
                segment.getSegmentRuleInfo().setInfRule(infRule);
            }
            if (segment.getOrderDetailBaggage()==null){
                OrderDetailBaggage   orderDetailBaggage = new OrderDetailBaggage();
                orderDetailBaggage.setCheckBaggeage("0KG");
                segment.setOrderDetailBaggage(orderDetailBaggage);
            }
            // 带低碳与不带低碳同行，删除"LCPREFERENCE"低碳行李标签
            this.handlePeerShippingLCLabel(orderPassengerInfos, segment);
        });
        List<OrderDetailSegment> orderDetailSegmentList = Lists.newArrayList(segmentMap.values());
        orderDetailSegmentList.sort(Comparator.comparing(OrderDetailSegment::getPriority));
        return orderDetailSegmentList;
    }

    /**
     * 处理同行人低碳标签问题
     * <p>
     * 带低碳与不带低碳同行，删除"LCPREFERENCE"低碳行李标签
     */
    private void handlePeerShippingLCLabel(List<OrderPassengerInfo> orderPassengerInfos, OrderDetailSegment segment) {
        // 校验有同行人时的 "LCPREFERENCE"低碳标签
        if (segment.getShippingRulesLabel() != null && segment.getShippingRulesLabel().contains(ShippingRulesLabelEnum.LCPREFERENCE.name())) {
            if (orderPassengerInfos.size() > 1) {
                // 带低碳与不带低碳同行，有一个乘客不带低碳标签时，删除"LCPREFERENCE"低碳行李标签,前端不展示“无免费托运行李”
                boolean isRemoveLabel = false;
                for (OrderPassengerInfo orderPassengerInfo : orderPassengerInfos) {
                    for (SegmentPriceInfo segmentPriceInfo : orderPassengerInfo.getSegmentPriceInfoList()) {
                        if (segmentPriceInfo.getSegNO() == (segment.getSegNO())) {
                            if (orderPassengerInfo.getShippingRulesLabel() == null || !orderPassengerInfo.getShippingRulesLabel().contains(ShippingRulesLabelEnum.LCPREFERENCE.name())) {
                                isRemoveLabel = true;
                                break;
                            }
                        }
                    }
                }
                if (isRemoveLabel) {
                    segment.getShippingRulesLabel().remove(ShippingRulesLabelEnum.LCPREFERENCE.name());
                }
            }
        }
    }

    /**
     * 国际品牌运价权益列表处理
     *
     * @param
     */
    private List<InterBrandRightInfo> genInterBrandRightInfos(String brandCode, String cabinCode, PtPdmRequest ptPdmRequest) {
        List<InterBrandRightInfo> interBrandRightInfos = new ArrayList<>();
        if (StringUtils.isNotBlank(brandCode) && ptPdmRequest != null) {
            BrandRightQueryRequest brandRightQueryRequest = new BrandRightQueryRequest();
            brandRightQueryRequest.setBrandRightCode(brandCode);
            brandRightQueryRequest.setCabin(cabinCode);
            ptPdmRequest.setRequest(brandRightQueryRequest);
            PtPdmResponse<List<BrandRightQueryResponse>> response = orderService.queryBrandRight(ptPdmRequest, brandCode);
            if (response != null) {
                if ("10001".equals(response.getResultCode())) {
                    //2021-05-10 国际品牌运价权益处理 查询统一订单获取国际品牌运价权益列表
                    List<BrandRightQueryResponse> brandRightQueryResponses = response.getResult();
                    if (CollectionUtils.isNotEmpty(brandRightQueryResponses)) {
                        BrandRightQueryResponse brandRightQueryResponse = brandRightQueryResponses.get(0);
                        if (brandRightQueryResponse != null && CollectionUtils.isNotEmpty(brandRightQueryResponse.getBrandRightDetail())) {
                            InterBrandRightInfo head = new InterBrandRightInfo();
                            head.setRightName("服务");
                            head.setState("是否包含");
                            interBrandRightInfos.add(head);
                            Map<String, BrandRightDto> brandRightMap = handConfig.getBrandRightDtoList().stream().collect(
                                    Collectors.toMap(x -> x.getRightCode()+":"+x.getServiceState(), x -> x));
                            for (BrandRightDetailVo detailVo : brandRightQueryResponse.getBrandRightDetail()) {
                                if (BrandRightStateEnum.E.getState().equals(detailVo.getServiceState())){
                                    continue;
                                }
                                InterBrandRightInfo brandRightInfo = new InterBrandRightInfo();
                                brandRightInfo.setRightName(detailVo.getRightName());
                                BrandRightDto    brandRightDto=brandRightMap.get(detailVo.getRightCode()+":"+detailVo.getServiceState());
                                if (StringUtils.isNotBlank(detailVo.getServiceState())) {
                                    if (brandRightDto!=null){
                                        brandRightInfo.setState(brandRightDto.getDescription());
                                    }else {
                                        brandRightInfo.setState("不享受");
                                    }
                                } else {
                                    if (detailVo.getQuantity() != null) {
                                        DecimalFormat decimalFormat = new DecimalFormat("###################.###########");
                                        StringBuilder state = new StringBuilder();
                                        state.append(decimalFormat.format(detailVo.getQuantity()))
                                                .append(StringUtils.isNotBlank(detailVo.getSpec()) ? detailVo.getSpec() : "")
                                                .append(StringUtils.isNotBlank(detailVo.getServiceStateCon()) ? " " + detailVo.getServiceStateCon() + " " : "")
                                                .append(StringUtils.isNotBlank(detailVo.getUnit()) ? detailVo.getUnit() : "");
                                        brandRightInfo.setState(state.toString());
                                    } else {
                                        brandRightInfo.setState("");
                                    }
                                }
                                interBrandRightInfos.add(brandRightInfo);
                            }
                        }
                    }
                }
            }
        }
        return interBrandRightInfos;
    }

    /**
     * 其它订单产生订单基本信息
     *
     * @param resp
     * @param subOrderResp
     */
    private void createOrderBaseInfo(OrderDetailResp resp, SubOrderResp subOrderResp) {
        resp.setChannelOrderNo(subOrderResp.getChannelOrderNo());
        resp.setRouteType("");
        resp.setInterFlag("D");
        resp.setOrderSort("");
        resp.setOrderNO(subOrderResp.getOrderNo());
        resp.setLinker(subOrderResp.getLinker());
        resp.setLinkerHandphone(subOrderResp.getLinkerHandphone());
        resp.setOrderState(subOrderResp.getState());
        resp.setBookingDatetime(subOrderResp.getCreateDatetime());
        resp.setPaidDatetime(subOrderResp.getPaidDatetime());
        resp.setTicketDatetime(subOrderResp.getPaidDatetime());
        resp.setIsBooking(OrderStateEnum.Cancel.getStateCode().equals(subOrderResp.getState()));
        resp.setIsPaid(PayEnum.Pay.getStateCode().equals(subOrderResp.getPayState()));
    }

    //取休息室订单
    private LoungeGetResp queryLoungeInfo(String userNo, String channelCode, String channelOrderNo, String orderNo, String customerNo) {
        LoungeGetResp loungeGetResp = new LoungeGetResp();
        String path = HandlerConstants.URL_FARE + HandlerConstants.SUB_GET_LOUNGE;
        PtLoungeGetReq requObj = createLoungeGetRequest(userNo, channelCode, channelOrderNo, orderNo, customerNo);
        HttpResult serviceResult = HttpUtil.doPost(requObj, path, false);
        if (null != serviceResult && serviceResult.isResult()) {
            loungeGetResp = (LoungeGetResp) JsonUtil.jsonToBean(serviceResult.getResponse(), LoungeGetResp.class);
            if (!loungeGetResp.getResultCode().equals("1001")) {
                loungeGetResp.setResultCode(WSEnum.ERROR.getResultCode());
                loungeGetResp.setErrorInfo(LOG_INFO_ONE + loungeGetResp.getErrorInfo());
            } else {
                int loungeBuyCnt = 0;
                Double loungeBuyAmt = 0.0;
                int useScore = 0;//积分抵扣
                for (LoungeBuy loungeBuy : loungeGetResp.getLoungeBuyList()) {
                    loungeBuyCnt += loungeBuy.getLoungeCount();
                    loungeBuyAmt += loungeBuy.getLoungeOrderAmount();
                    useScore += loungeBuy.getUseScore();
                    loungeBuy.setDepAirportNm(localCacheService.getLocalAirport(loungeBuy.getDepAirport()).getAirPortName());
                }
                loungeGetResp.setPayType(PayMethodEnum.CASH.value);
                loungeGetResp.setUseScore(useScore);
                loungeGetResp.setLoungeCountTotal(loungeBuyCnt);
                loungeGetResp.setLoungeOrderAmountPayableTotal(loungeBuyAmt);//订单应付金额
                loungeGetResp.setLoungeOrderAmountTotal(loungeBuyAmt - useScore);//订单实付金额
                if (useScore > 0 && useScore == loungeBuyAmt) {
                    loungeGetResp.setPayType(PayMethodEnum.SCORE.value);
                }
                loungeGetResp.setResultCode(WSEnum.SUCCESS.getResultCode());
                loungeGetResp.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
            }
        } else {
            loungeGetResp.setResultCode(WSEnum.ERROR.getResultCode());
            loungeGetResp.setErrorInfo(LOG_INFO_TWO);
        }
        return loungeGetResp;
    }

    //查询休息室
    private PtLoungeGetReq createLoungeGetRequest(String userNo, String channelCode, String channelOrderNo, String orderNo, String customerNo) {
        return new PtLoungeGetReq(
                HandlerConstants.VERSION,
                channelCode,
                userNo,
                channelOrderNo,
                orderNo,
                customerNo
        );
    }

    //查询wifi
    private PtWifiGetReq createWifiGetRequest(String userNo, String channelCode, String channelOrderNo, String orderNo, String customerNo) {
        return new PtWifiGetReq(
                HandlerConstants.VERSION,
                channelCode,
                userNo,
                channelOrderNo,
                orderNo,
                customerNo
        );
    }

    //查询行程单
    private PtTicketDeliveryGetReq createDeliveryGetRequest(String userNo, String channelCode, String channelOrderNo, String orderNo, String customerNo) {
        return new PtTicketDeliveryGetReq(
                HandlerConstants.VERSION,
                channelCode,
                userNo,
                channelOrderNo,
                orderNo,
                customerNo
        );
    }

    //取行程单订单
    private TicketDeliveryGetResp queryDeliveryInfo(String userNo, String channelCode, String channelOrderNo, String orderNo, String customerNo, String clientIp) {
        TicketDeliveryGetResp deliveryGetResp = new TicketDeliveryGetResp();
        Map<String, String> headMap = HttpUtil.getHeaderMap(clientIp, "");
        String path = HandlerConstants.URL_FARE_API + HandlerConstants.SUB_QUERY_DELIVERY;
        PtTicketDeliveryGetReq requObj = createDeliveryGetRequest(userNo, channelCode, channelOrderNo, orderNo, customerNo);
        HttpResult serviceResult = HttpUtil.doPostClient(requObj, path, headMap, handConfig.getReadTimeout(), handConfig.getConnectTimeout());
        if (serviceResult.isResult() && !StringUtil.isNullOrEmpty(serviceResult.getResponse())) {
            deliveryGetResp = (TicketDeliveryGetResp) JsonUtil.jsonToBean(serviceResult.getResponse(), TicketDeliveryGetResp.class);
            if (!deliveryGetResp.getResultCode().equals("1001")) {
                deliveryGetResp.setResultCode(WSEnum.ERROR.getResultCode());
                deliveryGetResp.setErrorInfo(LOG_INFO_ONE + deliveryGetResp.getErrorInfo());
            } else {
                deliveryGetResp.setDeliveryFee(deliveryGetResp.getTripCertSendInfo().getDeliveryFee());
                deliveryGetResp.setResultCode(WSEnum.SUCCESS.getResultCode());
                deliveryGetResp.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
            }
        } else {
            deliveryGetResp.setResultCode(WSEnum.ERROR.getResultCode());
            deliveryGetResp.setErrorInfo(LOG_INFO_TWO);
        }
        return deliveryGetResp;
    }

    private PtWeightProResp queryWeightInfo(String userNo, String channelCode, String channelOrderNo, String orderNo, String customerNo) {
        PtWeightProResp weightProResp = new PtWeightProResp();
        String weightPath = HandlerConstants.URL_FARE + HandlerConstants.SUB_GETBAGGAGE;
        PtWeightProReq ptWeightProReq = new PtWeightProReq(
                customerNo,
                channelOrderNo,
                orderNo,
                HandlerConstants.VERSION,
                channelCode,
                userNo
        );
        Double weightAmount = 0.0;//逾重行李金额
        HttpResult weightProResult = HttpUtil.doPost(ptWeightProReq, weightPath, false);
        if (null != weightProResult && weightProResult.isResult()) {
            weightProResp = (PtWeightProResp) JsonUtil.jsonToBean(weightProResult.getResponse(), PtWeightProResp.class);
            if (!weightProResp.getResultCode().equals("1001")) {
                weightProResp.setResultCode(WSEnum.ERROR.getResultCode());
                weightProResp.setErrorInfo(LOG_INFO_ONE + weightProResp.getErrorInfo());
            } else {
                if (!StringUtil.isNullOrEmpty(weightProResp.getBaggageBuyList())) {
                    for (BaggageBuy baggageBuy : weightProResp.getBaggageBuyList()) {//遍历处理
                        weightAmount = weightAmount + baggageBuy.getWeightOrderAmount();
                    }
                    weightProResp.setBaggageAmount(weightAmount);
                }
                weightProResp.setResultCode(WSEnum.SUCCESS.getResultCode());
                weightProResp.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
            }
        } else {
            weightProResp.setResultCode(WSEnum.ERROR.getResultCode());
            weightProResp.setErrorInfo(LOG_INFO_TWO);
        }
        return weightProResp;
    }

    /**
     * 订单退款按钮显示逻辑
     *
     * @param payState
     * @param fareType
     * @return
     */
    private static boolean showRefundBtn(String payState, String fareType, List<OrderPassengerInfo> orderPassengerInfos) {
//        if(FareTypeEnum.SPA.getFare().equals(fareType)){
//            return false;
//        }
        List<String> showRefundBtnStatus = Lists.newArrayList(OrderPayStateEnum.Pay.getStateCode(), OrderPayStateEnum.RefundApplying.getStateCode(),
                OrderPayStateEnum.RefundApproved.getStateCode(), OrderPayStateEnum.Refunding.getStateCode(), OrderPayStateEnum.Refund.getStateCode(),
                OrderPayStateEnum.RefundFailed.getStateCode());
        Optional<OrderPassengerInfo> containRefundablePassenger =
                orderPassengerInfos.stream().filter(orderPassengerInfo -> StringUtils.isBlank(orderPassengerInfo.getRefundLabel())).findFirst();
        if (showRefundBtnStatus.contains(payState) && containRefundablePassenger.isPresent()) {
            return true;
        }
        return false;
    }
}

package com.juneyaoair.mobile.handler.controller;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.juneyaoair.appenum.HorderResultEnum;
import com.juneyaoair.appenum.WSEnum;
import com.juneyaoair.appenum.av.FareTypeEnum;
import com.juneyaoair.appenum.order.OrderSortEnum;
import com.juneyaoair.baseclass.basicsys.response.AirPortInfoDto;
import com.juneyaoair.baseclass.common.base.CheckDayLicense;
import com.juneyaoair.baseclass.common.base.DayLicenseEnum;
import com.juneyaoair.baseclass.common.request.BaseReq;
import com.juneyaoair.baseclass.common.response.BaseResp;
import com.juneyaoair.baseclass.cuss.response.TicketBasicInfo;
import com.juneyaoair.baseclass.newfefund.request.RefundInfoReq;
import com.juneyaoair.baseclass.request.order.refund.Lounge.LoungeRefundRequest;
import com.juneyaoair.baseclass.request.order.refund.apply.RefundApplyRequest;
import com.juneyaoair.baseclass.request.order.refund.change.OrderChangedRequest;
import com.juneyaoair.baseclass.request.order.refund.confirm.OrderRefundRequest;
import com.juneyaoair.baseclass.request.order.refund.delivery.DeliveryRefundRequest;
import com.juneyaoair.baseclass.request.order.refund.insurance.InsuranceRefundRequest;
import com.juneyaoair.baseclass.request.order.refund.insurance.InsureRefundQueryRequest;
import com.juneyaoair.baseclass.request.order.refund.query.RefundDetailRequest;
import com.juneyaoair.baseclass.request.order.refund.query.RefundType;
import com.juneyaoair.baseclass.request.order.refund.query.TotalRefundBriefRequest;
import com.juneyaoair.baseclass.request.order.refund.wifi.WifiRefundRequest;
import com.juneyaoair.baseclass.response.order.comm.OrderPassengerInfo;
import com.juneyaoair.baseclass.response.order.comm.SegmentPriceInfo;
import com.juneyaoair.baseclass.response.order.detail.*;
import com.juneyaoair.baseclass.response.order.refund.apply.RefundApplyResp;
import com.juneyaoair.baseclass.response.order.refund.change.OrderChangedResp;
import com.juneyaoair.baseclass.response.order.refund.confirm.OrderRefundResp;
import com.juneyaoair.baseclass.response.order.refund.delivery.DeliveryRefundResp;
import com.juneyaoair.baseclass.response.order.refund.detail.RefundOrderDetailResp;
import com.juneyaoair.baseclass.response.order.refund.insurance.InsuranceRefundResp;
import com.juneyaoair.baseclass.response.order.refund.insurance.InsureSegmentInfo;
import com.juneyaoair.baseclass.response.order.refund.insurance.RefundInsureDetailResp;
import com.juneyaoair.baseclass.response.order.refund.lounge.LoungeRefundResp;
import com.juneyaoair.baseclass.response.order.refund.query.RefundTotalBriefInfo;
import com.juneyaoair.baseclass.response.order.refund.query.TotalRefundBriefResp;
import com.juneyaoair.baseclass.response.order.refund.wifi.WifiRefundResp;
import com.juneyaoair.mobile.aop.annotation.InterfaceLog;
import com.juneyaoair.mobile.aop.annotation.NotDuplicate;
import com.juneyaoair.mobile.comm.service.IRedisService;
import com.juneyaoair.mobile.constants.MobileCoreBeanNames;
import com.juneyaoair.mobile.core.bean.weiChart.FileInfo;
import com.juneyaoair.mobile.handler.HandlerConstants;
import com.juneyaoair.mobile.handler.config.bean.HandConfig;
import com.juneyaoair.mobile.handler.controller.service.InsuranceService;
import com.juneyaoair.mobile.handler.controller.util.OrderObjectConvert;
import com.juneyaoair.mobile.handler.manage.OrderManage;
import com.juneyaoair.mobile.handler.service.CheckInSeatService;
import com.juneyaoair.mobile.handler.service.CheckLicenseService;
import com.juneyaoair.mobile.handler.service.IBasicService;
import com.juneyaoair.mobile.handler.service.LocalCacheService;
import com.juneyaoair.mobile.handler.util.FavFTPUtil;
import com.juneyaoair.mobile.handler.util.FileUtils;
import com.juneyaoair.thirdentity.comm.RefundGroup;
import com.juneyaoair.thirdentity.comm.RefundInfo;
import com.juneyaoair.thirdentity.request.order.refund.Lounge.PtLoungeRefundReq;
import com.juneyaoair.thirdentity.request.order.refund.calculate.PtRefundAmtReq;
import com.juneyaoair.thirdentity.request.order.refund.change.PtOrderChangedRequest;
import com.juneyaoair.thirdentity.request.order.refund.confirm.BatchTicketRefundRequest;
import com.juneyaoair.thirdentity.request.order.refund.confirm.BatchTicketRefundResponse;
import com.juneyaoair.thirdentity.request.order.refund.confirm.Enclosure;
import com.juneyaoair.thirdentity.request.order.refund.confirm.PtRefundReq;
import com.juneyaoair.thirdentity.request.order.refund.delivery.PtDeliveryRefundReq;
import com.juneyaoair.thirdentity.request.order.refund.insurance.PtInsuranceRefundReq;
import com.juneyaoair.thirdentity.request.order.refund.insurance.PtRefundInsureDetailReq;
import com.juneyaoair.thirdentity.request.order.refund.query.PtRefundOrderDetailReq;
import com.juneyaoair.thirdentity.request.order.refund.query.PtTotalRefundBriefReq;
import com.juneyaoair.thirdentity.request.order.refund.wifi.PtWifiRefundReq;
import com.juneyaoair.thirdentity.request.speedRefund.TicketInfoRequest;
import com.juneyaoair.thirdentity.response.order.refund.calculate.RefundResponse;
import com.juneyaoair.thirdentity.response.order.refund.change.PtOrderChangedResp;
import com.juneyaoair.thirdentity.response.order.refund.confirm.PtRefundResp;
import com.juneyaoair.thirdentity.response.detr.PtIBETicketInfo;
import com.juneyaoair.thirdentity.response.detr.PtSegmentInfo;
import com.juneyaoair.thirdentity.response.speedRefund.TicketListInfoResponse;
import com.juneyaoair.utils.StringUtil;
import com.juneyaoair.utils.http.HttpResult;
import com.juneyaoair.utils.http.HttpUtil;
import com.juneyaoair.utils.json.JsonUtil;
import com.juneyaoair.utils.util.BeanUtils;
import com.juneyaoair.utils.util.DateUtils;
import com.juneyaoair.utils.util.MdcUtils;
import com.juneyaoair.utils.util.MetricLogUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import java.io.File;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Created by qinxiaoming on 2016-6-12.
 */

@RequestMapping("/refundService")
@RestController
@Api(value = "退票管理", tags = "退票管理")
public class OrderRefundController extends BassController {
    private static final int CONNECT_TIMEOUT = 15000;
    private static final int READ_TIMEOUT = 15000;

    @Autowired
    private HandConfig handConfig;

    @Autowired
    private LocalCacheService localCacheService;
    @Autowired
    private CheckInSeatService checkInSeatService;
    @Autowired
    private CheckLicenseService checkLicenseService;
    @Autowired
    private OrderManage orderManage;

    /*退票申请*/
    @InterfaceLog
    @RequestMapping(value = "/refundApply", method = RequestMethod.POST)
    @ApiOperation(value = "退票相关费用查询",notes = "退票相关费用查询")
    public RefundApplyResp OrderRtnApply(@RequestBody @Validated RefundApplyRequest req, BindingResult bindingResult, HttpServletRequest request) {
        RefundApplyResp result = new RefundApplyResp();
        String headChannelCode = request.getHeader(HandlerConstants.TOKEN_CHANNELCODE);
        //检查输入参数
        if (bindingResult.hasErrors()) {
            result.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            result.setErrorInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
            return result;
        }
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(req.getCustomerNo(), req.getLoginKeyInfo(), req.getChannelCode());
        if (!flag) {
            result.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            result.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return result;
        }
        String channelCode = req.getChannelCode();
        String ip = getClientIP(request);
        String userNo = getChannelInfo(channelCode, "10");
        RefundApplyResp response = new RefundApplyResp();
        response.setOrderPassengerInfoList(req.getOrderPassengerInfoList());
//        if (FareTypeEnum.SPA.getFare().equals(req.getFareType())) {
//            req.getOrderPassengerInfoList().forEach(orderPassengerInfo -> {
//                orderPassengerInfo.getSegmentPriceInfoList().forEach(segmentPriceInfo -> segmentPriceInfo.setCantRefundReason("此票不支持在线退票，请联系客服处理"));
//                orderPassengerInfo.setRefundable(false);
//            });
//            response.setResultCode(WSEnum.ERROR.getResultCode());
//            response.setErrorInfo("此票不支持在线退票，请联系客服处理");
//            return response;
//        }
        //1、DETR 接口查询,乘客航段状态DETR设置；
        JSONObject tags = new JSONObject();
        tags.put("IP",ip);
        tags.put("FfpCardNo",req.getFfpCardNo());
        tags.put("ChannelCode",headChannelCode);
        MetricLogUtil.saveMetricLog("退票-客票提取",tags,new BigDecimal(1));
        CheckDayLicense ipCheckDayLicense = new CheckDayLicense(ip, DayLicenseEnum.REFUND_APPLY_IP, "退票申请达到上限");
        CheckDayLicense ffpCheckDayLicense = new CheckDayLicense(req.getCustomerNo(), DayLicenseEnum.REFUND_APPLY_FFP, "退票申请达到上限");
        checkLicenseService.checkDayLicense(true, ipCheckDayLicense, ffpCheckDayLicense);
        for (OrderPassengerInfo orderPass : req.getOrderPassengerInfoList()) {
            // 未选中不查DETR
            if ("N".equals(orderPass.getSelectRefund())) {
                continue;
            }
            String[] tickets = orderPass.getETicketNo().split("/");
            // 基于ADA校验客票状态
            if ("Y".equalsIgnoreCase(handConfig.getRefundApplyAdaFlag())) {
                for (String ticket : tickets) {
                    List<TicketBasicInfo> ticketBasicInfoList = checkInSeatService.getTicketBasicInfo(ticket);
                    if (CollectionUtils.isNotEmpty(ticketBasicInfoList)) {
                        // 状态校验标识 默认不通过
                        boolean statusCheckFlag = false;
                        for (TicketBasicInfo ticketBasicInfo : ticketBasicInfoList) {
                            // 客票为值机状态
                            if ("C".equals(ticketBasicInfo.getStatus())) {
                                result.setResultCode(WSEnum.ERROR.getResultCode());
                                result.setErrorInfo("票号：" + ticket + "处于已值机状态，请先取消值机再申请退票。");
                                return result;
                            }
                            if (!statusCheckFlag) {
                                // 已退票 未使用 无控制权 标示为通过
                                statusCheckFlag = "E".equals(ticketBasicInfo.getStatus()) || "O".equals(ticketBasicInfo.getStatus()) || "A".equals(ticketBasicInfo.getStatus());
                            }
                        }
                        if (!statusCheckFlag) {
                            result.setResultCode(WSEnum.ERROR.getResultCode());
                            result.setErrorInfo("票号：" + ticket + "当前客票状态不允许退票。");
                            return result;
                        }
                    }
                }
            }
            if (!"AQ".equals(req.getDeductionType())){
                for (String ticket : tickets) {
                    TicketInfoRequest ticketInfoRequest = new TicketInfoRequest(HandlerConstants.VERSION, channelCode, userNo);
                    ticketInfoRequest.setCertType("TN");
                    ticketInfoRequest.setTicketNo(ticket);
                    ticketInfoRequest.setPassengerName(orderPass.getPassengerName());
                    Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
                    TicketListInfoResponse ticketListInfoResponse = orderManage.getTicketInfo(ticketInfoRequest,headMap,false);
                    if (!HorderResultEnum.isSucc(ticketListInfoResponse.getResultCode()) || CollectionUtils.isEmpty(ticketListInfoResponse.getIBETicketInfoList())) {
                        result.setResultCode(WSEnum.ERROR.getResultCode());
                        result.setErrorInfo("获取票状态出错!");
                        return result;
                    } else {
                        //此处是按照票号查询的，查询后取一个
                        PtIBETicketInfo ptIBETicketInfo = ticketListInfoResponse.getIBETicketInfoList().get(0);
                        if (CollectionUtils.isEmpty(ptIBETicketInfo.getSegmentInfoList())) {
                            result.setResultCode(WSEnum.ERROR.getResultCode());
                            result.setErrorInfo("获取航段信息有误");
                            return result;
                        }
                        for (SegmentPriceInfo segPrc : orderPass.getSegmentPriceInfoList()) {
                            int segId = segPrc.getSegmentSeq();
                            for (PtSegmentInfo segmentInfo : ptIBETicketInfo.getSegmentInfoList()) {
                                if (segmentInfo.getSegmentIndex() == segId &&
                                        (segmentInfo.getType().equals("AIRSEG_NORMAL") || segmentInfo.getType().equals("AIRSEG_OPEN"))
                                        && segPrc.getDepAirport().equals(segmentInfo.getDepAirportCode())) {
                                    segPrc.setTicketState(segmentInfo.getTicketStatus());
                                    break;
                                }
                            }
                        }
                    }
                }
            }else {

            }

        }
        //某个乘客的航段全部为非OPEN_FOR_USE状态，提示无法退票。
        int INFRefund = 0;
        int checkedInCnt = 0;
        int notOpenCount = 0;//非OPEN 状态的客票状态
        String  ticketUsage="0";//客票使用情况

        String checkedInTktNos = "";
        for (OrderPassengerInfo orderPass : req.getOrderPassengerInfoList()) {
            // 未选中不做校验
            if ("N".equals(orderPass.getSelectRefund())) {
                continue;
            }
            int segRefund = 0;
            for (SegmentPriceInfo segPrc : orderPass.getSegmentPriceInfoList()) {
                if (!HandlerConstants.OPEN_FOR_USE.equals(segPrc.getTicketState()) && !segPrc.getTicketState().startsWith(HandlerConstants.AIRP_CNTL) /*此状态视为未使用*/) {
                    notOpenCount++;
                }
                if (HandlerConstants.OPEN_FOR_USE.equals(segPrc.getTicketState()) || (HandlerConstants.EXCHANGED.equals(segPrc.getTicketState()) && HandlerConstants.EXCHANGED.equals(segPrc.getTKTStatus()))) {
                    segRefund++;
                    segPrc.setRefundable(true);
                    orderPass.setRefundable(true);
                    if ((FareTypeEnum.ADDON.getFare().equals(req.getFareType()) || FareTypeEnum.SPA.getFare().equals(req.getFareType())) && notOpenCount > 0) {
                        segPrc.setCantRefundReason("部分航段已使用或已退改，无法退票申请。");
                        segPrc.setRefundable(false);
                        orderPass.setRefundable(false);
                    }
                    if (notOpenCount > 0&&HandlerConstants.TRIP_TYPE_I.equals(req.getInterFlag())){
                       ticketUsage="1";
                    }
                }
                if (HandlerConstants.CHECKED_IN.equals(segPrc.getTicketState())) {
                    checkedInCnt++;
                    checkedInTktNos = !StringUtil.isNullOrEmpty(checkedInTktNos) ? (checkedInTktNos + "," + orderPass.getETicketNo()) : orderPass.getETicketNo();
                    segPrc.setCantRefundReason("已办理过值机，请先取消值机后再申请退款");
                }
            }
            if ((FareTypeEnum.ADDON.getFare().equals(req.getFareType()) || FareTypeEnum.SPA.getFare().equals(req.getFareType())) && notOpenCount > 0) {
                result.setResultCode(WSEnum.ERROR.getResultCode());
                result.setErrorInfo("乘客：" + orderPass.getPassengerName() + "的部分航段已使用或已退改，无法退票申请。");
                return result;
            }
            if ("INF".equals(orderPass.getPassengerType())) {
                INFRefund++;
            }
            if (checkedInCnt > 0) {
                result.setResultCode(WSEnum.ERROR.getResultCode());
                result.setErrorInfo("票号：" + checkedInTktNos + "处于已值机状态，请先取消值机再申请退票。");
                return result;
            }
            if (segRefund == 0) {
                orderPass.setRefundable(false);
                result.setResultCode(WSEnum.ERROR.getResultCode());
                result.setErrorInfo("乘客：" + orderPass.getPassengerName() + "的所有航段已使用或已退改，无法退票申请。");
                return result;
            }
        }
        if (INFRefund == req.getOrderPassengerInfoList().stream().filter(orderPassengerInfo -> !"N".equals(orderPassengerInfo.getSelectRefund())).count()) {
            result.setResultCode(WSEnum.ERROR.getResultCode());
            result.setErrorInfo("不可单独退婴儿票");
            return result;
        }
        BeanUtils.copyProperties(req, response);
        //退票费request
        PtRefundAmtReq refReq = new PtRefundAmtReq(HandlerConstants.VERSION, req.getBookChannelCode(), userNo
                , HandlerConstants.CURRENCY_CODE, req.getRouteType(), req.getTicketingDate(), req.getInterFlag());
        //人员分组 拼装选择的人
        refReq.setRefundGroupList(OrderObjectConvert.ToRefundGroupList(req.getOrderPassengerInfoList().stream().filter(orderPassengerInfo ->
                !"N".equals(orderPassengerInfo.getSelectRefund())).collect(Collectors.toList()), false));
        //计算退票费
        Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
        HttpResult httpResult = HttpUtil.doPostClient(refReq, handConfig.getOneOrderUrl() + HandlerConstants.CALCULATE_REFUND, headMap, READ_TIMEOUT, CONNECT_TIMEOUT);
        if (httpResult.isResult()) {
            RefundResponse refundResponse = (RefundResponse) JsonUtil.jsonToBean(httpResult.getResponse(), RefundResponse.class);
            if (!HorderResultEnum.isSucc((refundResponse.getResultCode()))) {
                result.setResultCode(WSEnum.ERROR.getResultCode());
                result.setErrorInfo(refundResponse.getErrorInfo());
                return result;
            }
            for (RefundGroup refundGroup : refundResponse.getRefundGroupList()) {
                for (OrderPassengerInfo orderPassengerInfo : response.getOrderPassengerInfoList()) {
                    if (String.valueOf(orderPassengerInfo.getPassengerID()).equals(refundGroup.getPassengerID())) {
                        orderPassengerInfo.setDeductionTotal(refundGroup.getDeductionTotal());//退票手续费
                        orderPassengerInfo.setUseFlowdPriceTotal(refundGroup.getUseFlowdPriceTotal());//已使用航段票价总金额
                        orderPassengerInfo.setRefundXTaxTotal(refundGroup.getRefundXTaxTotal());//应退税费合计
                        orderPassengerInfo.setRefundOtherTotal(refundGroup.getRefundOtherTotal());//应退其它费用合计
                        orderPassengerInfo.setRefundSign(refundGroup.getRefundSign());
                        for (SegmentPriceInfo segmentPriceInfo : orderPassengerInfo.getSegmentPriceInfoList()) {
                            for (RefundInfo refundInfo : refundGroup.getRefundInfoList()) {
                                if (String.valueOf(segmentPriceInfo.getID()).equals(refundInfo.getID())) {
                                    segmentPriceInfo.setDeduction(refundInfo.getDeduction());
                                    segmentPriceInfo.setUseFlowdPrice(refundInfo.getUseFlowdPrice());
                                    segmentPriceInfo.setRefundXTax(refundInfo.getRefundXTax());
                                    segmentPriceInfo.setRefundOther(refundInfo.getRefundOther());
                                    segmentPriceInfo.setXTax(refundInfo.getXTax());
                                    segmentPriceInfo.setOther(refundInfo.getOther());
                                    segmentPriceInfo.setMachineTravelCouponPrice(refundInfo.getMachineTravelCouponPrice());
                                    break;
                                }
                            }
                        }
                        break;
                    }
                }
            }
            //计算价格
            Double TicketAmount = 0.0; //可退机票总金额
            Double TaxFee = 0.0; //可退税费
            Double QOther = 0.0; //可退其他税费
            Double InsAmount = 0.0; //可退保险
            Double Deduction = 0.0; //手续费
            Double RefundAmount = 0.0;//应退总金额
            Double machineTravelCouponPrice=0.0;//接送机券可退金额
            Double OBFee = 0.0;//升舱改期手续费
            BigDecimal   totalScore = BigDecimal.ZERO;//积分
            BigDecimal coupon = BigDecimal.ZERO;//优惠券
            /* 2020-01-07 此特殊处理的原因是：查询订单详情金额明细的时候UpgradeFee字段加上了改期券抵扣金额，此处应该还原*/
            double changeCouponAmount = 0.0;
            for (OrderPassengerInfo orderPassengerInfo : response.getOrderPassengerInfoList()) {
                // 计算选中的退票费
                if ("N".equals(orderPassengerInfo.getSelectRefund())) {
                    continue;
                }
                Double unTaxFee = 0.0;
                for (SegmentPriceInfo segprc : orderPassengerInfo.getSegmentPriceInfoList()) {
                    if (HandlerConstants.OPEN_FOR_USE.equals(segprc.getTicketState())
                            || HandlerConstants.EXCHANGED.equals(segprc.getTicketState())
                            || segprc.getTicketState().contains(HandlerConstants.AIRP_CNTL)) {
                        TicketAmount += segprc.getPricePaid();
                        totalScore = totalScore.add(BigDecimal.valueOf(segprc.getUseScore()));
                        coupon = coupon.add(BigDecimal.valueOf(segprc.getCouponAmount()));
                        if (segprc.getIsBuyInsurance()) {
                            InsAmount += segprc.getInsuranceAmount();
                        }
                    } else {//不可使用的
                        unTaxFee += segprc.getCNTax() + segprc.getYQTax();
                    }
                    // 如果退款手续费大于积分和现金，则手续费改为使用现金和积分总额
                    if (segprc.getDeduction() > segprc.getPricePaid() + segprc.getUseScore()) {
                        Deduction += segprc.getPricePaid() + segprc.getUseScore();
                    } else {
                        Deduction += segprc.getDeduction();
                    }
                    OBFee += segprc.getUpgradeFee();
                    machineTravelCouponPrice +=(segprc.getMachineTravelCouponPrice()==null?0:segprc.getMachineTravelCouponPrice().doubleValue());
                }
                TaxFee += orderPassengerInfo.getCNTax() + orderPassengerInfo.getYQTax() - unTaxFee;
                QOther += orderPassengerInfo.getOtherTax() + orderPassengerInfo.getQFee();
                changeCouponAmount += orderPassengerInfo.getChangeCouponAmount();
            }
            response.setTicketAmount(TicketAmount);
            response.setTaxFee(TaxFee);
            response.setQOther(QOther);
            response.setInsAmount(InsAmount);
            response.setMachineTravelCouponPrice(machineTravelCouponPrice);
            // 不含税的可退总额    可退机票总金额 + 可退保险 - 手续费 - 升舱改期手续费 +改期券抵扣金额 + 积分 - 接送机券可退金额
            RefundAmount = TicketAmount + InsAmount - Deduction - OBFee + changeCouponAmount + totalScore.doubleValue()-machineTravelCouponPrice;
            if (RefundAmount<0){
                RefundAmount=0.0;
            }
            BigDecimal refundScore; // 自愿退票退还积分
            if (totalScore.doubleValue() >= RefundAmount) {
                refundScore = BigDecimal.valueOf(RefundAmount);
            } else {//可退总额大于总积分时,退还所有积分
                refundScore = totalScore;
            }
            RefundAmount += TaxFee + QOther;// 税费全退
            response.setDeduction(Deduction);
            response.setTotalScore(totalScore.doubleValue());
            response.setRefundScore(refundScore.doubleValue());
            response.setTicketUsage(ticketUsage);
            BigDecimal bg = BigDecimal.valueOf(RefundAmount);
            double f1 = bg.setScale(1, BigDecimal.ROUND_HALF_UP).doubleValue();
            response.setRefundAmount(f1);
            response.setResultCode(WSEnum.SUCCESS.getResultCode());
            String   involuntary  =refundResponse.getInvoluntary()!=null?refundResponse.getInvoluntary():"N";
            response.setVoluntaryRefund("Y".equals(involuntary));
        } else {
            response.setResultCode(WSEnum.NETWORK_BUSY.getResultCode());
            response.setErrorInfo(WSEnum.NETWORK_BUSY.getResultInfo());
        }
        return response;
    }

    /*确认退票*/
    @RequestMapping(value = "/refundConfirm", method = RequestMethod.POST)
    @ApiOperation(value = "退票确认",notes = "提交退票请求")
    @InterfaceLog
    @NotDuplicate
    public OrderRefundResp orderRefund(@RequestBody @Validated OrderRefundRequest req, BindingResult bindingResult, HttpServletRequest request) {
        OrderRefundResp target = new OrderRefundResp();
        String realChannelCode = request.getHeader(HandlerConstants.TOKEN_CHANNELCODE);
        String reqId = MdcUtils.getRequestId(realChannelCode);
        if (bindingResult.hasErrors()) {
            target.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            target.setErrorInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
            return target;
        }
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(req.getCustomerNo(), req.getLoginKeyInfo(), req.getChannelCode());
        if (!flag) {
            target.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            target.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return target;
        }
        String channelCode = req.getChannelCode();
        String userNo = getChannelInfo(channelCode, "10");
        req.setUserNo(userNo);
        // 仅退选中的人
        req.setOrderPassengerInfoList(req.getOrderPassengerInfoList().stream().filter(orderPassengerInfo -> !"N".equals(orderPassengerInfo.getSelectRefund())).collect(Collectors.toList()));
        List<PtRefundReq> ptRefundReqList = OrderObjectConvert.ToTicketRefundRequestList(req);
        if (null == ptRefundReqList || ptRefundReqList.size() <= 0) {
            target.setResultCode(WSEnum.ERROR.getResultCode());
            target.setErrorInfo("请求参数无效！");
            return target;
        }

//        if (req.getRefundType().isSpecialSituationRefund()){
//            if (req.getOrderPassengerInfoList().size() !=1) {
//                target.setResultCode(WSEnum.ERROR.getResultCode());
//                target.setErrorInfo("特情申退一次只能申请一个乘机人，请分开申请");
//                return target;
//            }
//        }

        boolean isVoluntaryRefund = req.getRefundType() == null ? req.getIsVoluntaryRefund() : req.getRefundType().isVoluntaryRefund();
//        boolean isMURefundType = false;
//        if (!isVoluntaryRefund) {
//            for (OrderPassengerInfo orderPassengerInfo : req.getOrderPassengerInfoList()) {
//                for (SegmentPriceInfo segmentPriceInfo : orderPassengerInfo.getSegmentPriceInfoList()) {
//                    if (segmentPriceInfo.getFlightNo().startsWith("MU")) {
//                        isMURefundType = true;
//                    }
//                }
//            }
//            if (isMURefundType) {
//                target.setResultCode(WSEnum.ERROR.getResultCode());
//                target.setErrorInfo("东航票暂不支持非自愿退票！");
//                return target;
//            }
//        }
        log.info("{}退票确认请求json:{}", reqId, JsonUtil.objectToJson(ptRefundReqList));
        List<Enclosure> enclosureInfoList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(req.getUploadFiles())) {
            req.getUploadFiles().forEach(fileInfo -> {
                FileInfo info = FavFTPUtil.moveTempFile(fileInfo.getFilePath() + File.separator + fileInfo.getRealName());
                Enclosure enclosure = new Enclosure(FileUtils.getFileSuffix(info.getImgServiceUrl()), info.getImgServiceUrl(),fileInfo.getFileName());
                enclosureInfoList.add(enclosure);
            });
        }
        StringBuilder successMessage = new StringBuilder("您好！我们将尽快完成退票审核，您可查看手机端退票进度；" +
                "您提交退票申请后原定座位将被取消，实际退还金额将以审核结果为准。");
//        // 畅飞卡退款noshow提示
//        boolean noShowMessage = false;
//        if (req.getOrderPassengerInfoList().stream().anyMatch(passenger -> StringUtils.isNotBlank(passenger.getUseUnlimitedFlyCardNo()))) {
//            for (OrderPassengerInfo orderPassengerInfo : req.getOrderPassengerInfoList()) {
//                Date now = new Date();
//                noShowMessage = orderPassengerInfo.getSegmentPriceInfoList().stream().anyMatch(segmentPriceInfo -> {
//                    Date depDate = DateUtils.toDate(segmentPriceInfo.getDepDateTime(), DateUtils.YYYY_MM_DD_HH_MM_PATTERN);
//                    return null != depDate && DateUtils.durDays(now, depDate) < handConfig.getUnlimitedCard2Config().getNoShowDay();
//                });
//                if (noShowMessage) {
//                    successMessage.setLength(0);
//                    successMessage.append(handConfig.getSuccessMessage());
//                    break;
//                }
//            }
//        }
        // 封装退票接口请求参数
        BatchTicketRefundRequest refundConfirmRequest = new BatchTicketRefundRequest();
        refundConfirmRequest.setChannelCode(req.getBookChannelCode());
        refundConfirmRequest.setVersion(HandlerConstants.VERSION);
        refundConfirmRequest.setBatchTicketRefundRequest(ptRefundReqList);
        for (PtRefundReq ptRefundReq : ptRefundReqList) {
            ptRefundReq.setEnclosureInfoList(enclosureInfoList);
            ptRefundReq.setEnclosure(CollectionUtils.isNotEmpty(enclosureInfoList));
            //非自愿退票调整手续费
            if (!isVoluntaryRefund) {
                for (RefundGroup refundGroup : ptRefundReq.getRefundGroupList()) {
                    for (RefundInfo refundInfo : refundGroup.getRefundInfoList()) {
                        //可用客票手续费调整
                        if (HandlerConstants.OPEN_FOR_USE.equals(refundInfo.getTKTStatus())
                                || HandlerConstants.EXCHANGED.equals(refundInfo.getTKTStatus())) {
                            refundInfo.setDeduction(0);
                        }
                    }
                    refundGroup.setDeductionTotal(0);
                }
            }
        }
        // 调用退票接口
        String ip = getClientIP(request);
        Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
        HttpResult result = this.doPostClient(refundConfirmRequest, HandlerConstants.URL_FARE_API + HandlerConstants.BATCH_REFUND_CONFIRM, headMap, READ_TIMEOUT, CONNECT_TIMEOUT);
        if (result.isResult() && !StringUtil.isNullOrEmpty(result.getResponse())) {
            // 解析返回数据
            BatchTicketRefundResponse response = (BatchTicketRefundResponse) JsonUtil.jsonToBean(result.getResponse(), BatchTicketRefundResponse.class);
            // 记录所有异常描述
            Set<String> errorMessage = Sets.newHashSet();
            // 标记退票订单是否存在异常 默认不存在
            boolean refundFail = false;
            for (PtRefundResp ptRefundResp : response.getBatchTicketRefundInfo()) {
                if (!HorderResultEnum.isSucc(response.getResultCode())) {
                    refundFail = true;
                    if ("2209".equals(response.getResultCode())) {
                        errorMessage.add("非自愿改期订单请直接退原订单");
                    } else {
                        errorMessage.add(StringUtils.isNotBlank(response.getErrorInfo()) ? response.getErrorInfo() : "退票请求失败！");
                    }
                } else {
                    BeanUtils.copyProperties(ptRefundResp, target);
                    // 非直营渠道的原票在直营渠道做了客票改期，需要提示客人去原渠道退原票
                    if (ptRefundResp.isHoChannel() && StringUtils.isNotBlank(ptRefundResp.getChangeFormerTicketNo())) {
                        // 客票改期订单只会有一个原票号和一个现票号
                        successMessage.append("\n票号").append(req.getOrderPassengerInfoList().get(0).getETicketNo()).append("已提交退款，票号")
                                .append(ptRefundResp.getChangeFormerTicketNo()).append("请至原渠道办理退款。");
                    }
                }
            }
            // 存在异常设置返回编码为错误，设置异常描述
            if (refundFail) {
                target.setResultCode(WSEnum.ERROR.getResultCode());
                target.setErrorInfo(Joiner.on("\n").join(errorMessage));
            }
        } else {
            target.setResultCode(WSEnum.ERROR.getResultCode());
            target.setErrorInfo("退票请求失败!");
            return target;
        }
        target.setSuccessMessage(successMessage.toString());
        return target;
    }

    //<editor-fold desc="退增值产品">
    //退wifi
    @RequestMapping(value = "/refundWifiConfirm", method = RequestMethod.POST)
    public WifiRefundResp wifiRefund(@RequestBody WifiRefundRequest req, HttpServletRequest request) {
        //检查条件是否满足
        WifiRefundResp response = new WifiRefundResp();
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(req.getFfpId(), req.getLoginKeyInfo(), req.getChannelCode());
        if (!flag) {
            response.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            response.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return response;
        }
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<WifiRefundRequest>> violations = validator.validate(req);
        if (null != violations && violations.size() > 0) {
            response.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            response.setErrorInfo(violations.iterator().next().getMessage());
            return response;
        }
        String channelCode = req.getChannelCode();
        String userNo = getChannelInfo(channelCode, "10");
        PtWifiRefundReq target = new PtWifiRefundReq(HandlerConstants.VERSION, req.getBookChannelCode(), userNo);
        BeanUtils.copyProperties(req, target);
        target.setChannelCode(req.getBookChannelCode());
        //调用接口返回数据
        String wifiRtnPath = HandlerConstants.URL_FARE + HandlerConstants.SUB_REFUND_WIFI;
        HttpResult serviceDetailResult = doPost(target, wifiRtnPath);
        if (null != serviceDetailResult && serviceDetailResult.isResult()) {
            response = (WifiRefundResp) JsonUtil.jsonToBean(serviceDetailResult.getResponse(), WifiRefundResp.class);
            if (response.getResultCode().equals("1001")) {
                response.setResultCode(WSEnum.SUCCESS.getResultCode());
                response.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
            } else {
                response.setResultCode(WSEnum.ERROR.getResultCode());
                response.setErrorInfo(response.getErrorInfo());
            }
            return response;
        } else {
            response.setResultCode(WSEnum.ERROR.getResultCode());
            response.setErrorInfo("调用退wifi时网络出错");
            return response;
        }

    }

    //退休息室
    @RequestMapping(value = "/refundLoungeConfirm", method = RequestMethod.POST)
    public LoungeRefundResp loungeRefund(@RequestBody LoungeRefundRequest req, HttpServletRequest request) {
        //检查条件是否满足
        LoungeRefundResp response = new LoungeRefundResp();
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(req.getFfpId(), req.getLoginKeyInfo(), req.getChannelCode());
        if (!flag) {
            response.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            response.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return response;
        }
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<LoungeRefundRequest>> violations = validator.validate(req);
        if (null != violations && violations.size() > 0) {
            response.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            response.setErrorInfo(violations.iterator().next().getMessage());
            return response;
        }
        String channelCode = req.getChannelCode();
        String userNo = getChannelInfo(channelCode, "10");
        PtLoungeRefundReq target = new PtLoungeRefundReq(HandlerConstants.VERSION, req.getBookChannelCode(), userNo);
        BeanUtils.copyProperties(req, target);
        target.setChannelCustomerNo(req.getFfpId());
        target.setChannelCode(req.getBookChannelCode());
        //调用接口返回数据
        String loungeRtnPath = HandlerConstants.URL_FARE + HandlerConstants.SUB_REFUND_LOUNGE;
        HttpResult serviceDetailResult = doPost(target, loungeRtnPath);
        if (null != serviceDetailResult && serviceDetailResult.isResult()) {
            response = (LoungeRefundResp) JsonUtil.jsonToBean(serviceDetailResult.getResponse(), LoungeRefundResp.class);
            if (response.getResultCode().equals("1001")) {
                response.setResultCode(WSEnum.SUCCESS.getResultCode());
                response.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
            } else {
                response.setResultCode(WSEnum.ERROR.getResultCode());
                response.setErrorInfo(response.getErrorInfo());
            }
            return response;
        } else {
            response.setResultCode(WSEnum.ERROR.getResultCode());
            response.setErrorInfo("调用退休息室时网络出错");
            return response;
        }

    }

    //退行程单
    @RequestMapping(value = "/refundDeliveryConfirm", method = RequestMethod.POST)
    public DeliveryRefundResp deliveryRefund(@RequestBody DeliveryRefundRequest req, HttpServletRequest request) {
        //检查条件是否满足
        DeliveryRefundResp response = new DeliveryRefundResp();
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(req.getFfpId(), req.getLoginKeyInfo(), req.getChannelCode());
        if (!flag) {
            response.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            response.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return response;
        }
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<DeliveryRefundRequest>> violations = validator.validate(req);
        if (null != violations && violations.size() > 0) {
            response.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            response.setErrorInfo(violations.iterator().next().getMessage());
            return response;
        }
        String channelCode = req.getChannelCode();
        String userNo = getChannelInfo(channelCode, "10");
        PtDeliveryRefundReq target = new PtDeliveryRefundReq(HandlerConstants.VERSION, req.getBookChannelCode(), userNo);
        BeanUtils.copyProperties(req, target);
        target.setChannelCode(req.getBookChannelCode());
        //调用接口返回数据
        String loungeRtnPath = HandlerConstants.URL_FARE + HandlerConstants.SUB_REFUND_TICKET_DELIVERY;
        HttpResult serviceDetailResult = doPost(target, loungeRtnPath);
        if (null != serviceDetailResult && serviceDetailResult.isResult()) {
            response = (DeliveryRefundResp) JsonUtil.jsonToBean(serviceDetailResult.getResponse(), DeliveryRefundResp.class);
            if (response.getResultCode().equals("1001")) {
                response.setResultCode(WSEnum.SUCCESS.getResultCode());
                response.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
            } else {
                response.setResultCode(WSEnum.ERROR.getResultCode());
                response.setErrorInfo(response.getErrorInfo());
            }
            return response;
        } else {
            response.setResultCode(WSEnum.ERROR.getResultCode());
            response.setErrorInfo("调用退行程单时网络出错");
            return response;
        }
    }
//</editor-fold>

    /*单独退保*/
    @ApiOperation(value = "单独退保")
    @RequestMapping(value = "/refundInsure", method = RequestMethod.POST)
    public InsuranceRefundResp insuranceRtnApply(@RequestBody InsuranceRefundRequest req, HttpServletRequest request) {
        InsuranceRefundResp resp = new InsuranceRefundResp();
        int suc = 0, fal = 0;
        String errorMessage = null;
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(req.getCustomerNo(), req.getLoginKeyInfo(), req.getChannelCode());
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<InsuranceRefundRequest>> violations = validator.validate(req);
        if (null != violations && violations.size() > 0) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(violations.iterator().next().getMessage());
            return resp;
        }
        String channelCode = req.getChannelCode();
        String userNo = getChannelInfo(channelCode, "10");
        PtInsuranceRefundReq target = new PtInsuranceRefundReq(HandlerConstants.VERSION,
                req.getBookChannelCode(), userNo, HandlerConstants.CURRENCY_CODE);
        BeanUtils.copyProperties(req, target);
        target.setChannelCode(req.getChannelCode());
        for (OrderPassengerInfo orderPass : req.getOrderPassengerInfoList()) {
            //可退的保险航段
            int insureCount = 0;
            List segIdList = new ArrayList();
            for (SegmentPriceInfo seg : orderPass.getSegmentPriceInfoList()) {
                if (seg.getIsBuyInsurance()) {
                    insureCount++;
                    segIdList.add(seg.getID());//保存有保险的航段ID
                }
            }
            if (insureCount > 0) {
                //拼装航段ID
                int[] ids = new int[insureCount];
                for (int i = 0; i < insureCount; i++) {
                    ids[i] = (int) segIdList.get(i);
                }
                String[] arrCodes = new String[orderPass.getInsuranceList().size()];
                //创建新的保险id集合
                ArrayList<String> insCodeList = new ArrayList<>();
                //拼装保险代码
                for (int i = 0; i < orderPass.getInsuranceList().size(); i++) {
                    arrCodes[i] = orderPass.getInsuranceList().get(i).getInsuranceCode();
                }
                //不可退保险id集合
                List<String> cannotRefundIndIdList = insuranceService.getCannotRefundIndIdList();
                //不可退保险个数，默认为零
                int cannotRefundCount = 0;
                for (String arrCode : arrCodes) {
                    //如果id数组不合法 ，将flag置为false
                    if (cannotRefundIndIdList.contains(arrCode)) {
                        cannotRefundCount++;
                    }
                }
                //如果保险id数组包含不可退id
                if (0 != cannotRefundCount) {
                    int insIdSize = orderPass.getInsuranceList().size() - cannotRefundCount;
                    if (insIdSize <= 0) {
                        //如果全部不可退
                        resp.setResultCode(WSEnum.ERROR.getResultCode());
                        resp.setErrorInfo("当前订单无可退保险");
                        return resp;
                    }
                    //拼装保险代码
                    for (int i = 0; i < orderPass.getInsuranceList().size(); i++) {
                        //如果id可退
                        if (!cannotRefundIndIdList.contains(orderPass.getInsuranceList().get(i).getInsuranceCode())) {
                            insCodeList.add(orderPass.getInsuranceList().get(i).getInsuranceCode());
                        }
                    }
                    //将新的保险id集合转换成数组
                    arrCodes = new String[insCodeList.size()];
                    for (int i = 0; i < insCodeList.size(); i++) {
                        arrCodes[i] = insCodeList.get(i);
                    }
                }
                target.setPassengerSegmentIDList(ids);
                target.setInsuranceCodeList(arrCodes);
                //调用接口返回数据
                String insureRtnPath = HandlerConstants.New_URL_FARE + HandlerConstants.SUB_REFUND_INSURE;
                HttpResult serviceDetailResult = HttpUtil.doPostClient(target, insureRtnPath);
                if (serviceDetailResult.isResult()) {
                    resp = (InsuranceRefundResp) JsonUtil.jsonToBean(serviceDetailResult.getResponse(), InsuranceRefundResp.class);
                    if (resp.getResultCode().equals("1001")) {
                        suc++;
                    } else {
                        fal++;
                        errorMessage = resp.getErrorInfo();
                    }
                } else {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setErrorInfo("调用退保时网络出错");
                    return resp;
                }
            }
        }
        if (suc > 0 && fal == 0) {
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            resp.setErrorInfo("退保成功");
        } else if (suc == 0 && fal > 0 && StringUtils.isNotEmpty(errorMessage)) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo(errorMessage);
        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo("退保失败,发生其它错误");
        }
        return resp;
    }

    //退单列表查询
    @RequestMapping(value = "/queryRefundBrief", method = RequestMethod.POST)
    public TotalRefundBriefResp QueryRefundOrderBrief(@RequestBody TotalRefundBriefRequest refundBriefRequest, HttpServletRequest request) {
        TotalRefundBriefResp resp = new TotalRefundBriefResp();
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(refundBriefRequest.getCustomerNo(), refundBriefRequest.getLoginKeyInfo(), refundBriefRequest.getChannelCode());
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<TotalRefundBriefRequest>> violations = validator.validate(refundBriefRequest);
        if (null != violations && violations.size() > 0) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(violations.iterator().next().getMessage());
            return resp;
        }
        String userNo = getChannelInfo(refundBriefRequest.getChannelCode(), "10");
        PtTotalRefundBriefReq ptBrief = new PtTotalRefundBriefReq(HandlerConstants.VERSION, refundBriefRequest.getChannelCode(), userNo);
        BeanUtils.copyProperties(refundBriefRequest, ptBrief);
        ptBrief.setCreateDateBegin(refundBriefRequest.getDateBegin());
        ptBrief.setCreateDateEnd(refundBriefRequest.getDateEnd());
        if ("Finish".equalsIgnoreCase(refundBriefRequest.getState())) {
            ptBrief.setRefundState("2");
        } else if ("ToBeChecked".equalsIgnoreCase(refundBriefRequest.getState())) {
            ptBrief.setRefundState("1");
        } else {//全部
            ptBrief.setRefundState("0");
        }
        HttpResult serviceResult = doPost(ptBrief, HandlerConstants.URL_FARE + HandlerConstants.SUB_QUERY_TOTAL_REFUND);
        if (serviceResult.isResult() && StringUtils.isNotBlank(serviceResult.getResponse())) {
            try {
                resp = (TotalRefundBriefResp) JsonUtil.jsonToBean(serviceResult.getResponse(), TotalRefundBriefResp.class);
                if (resp.getResultCode().equals("1001")) {
                    if (!StringUtil.isNullOrEmpty(resp.getTotalRefundBriefInfoList())) {
                        for (RefundTotalBriefInfo refundTotalBriefInfo : resp.getTotalRefundBriefInfoList()) {
                            if ("Surrender".equals(refundTotalBriefInfo.getRefundType())) {//保险订单名称临时处理
                                changeInsureName(refundTotalBriefInfo);
                            }
                            if (!StringUtil.isNullOrEmpty(refundTotalBriefInfo.getSegmentInfoList())) {
                                for (com.juneyaoair.baseclass.response.order.query.SegmentInfo segmentInfo : refundTotalBriefInfo.getSegmentInfoList()) {
                                    AirPortInfoDto depAirPortInfoDto = localCacheService.getLocalAirport(segmentInfo.getDepAirport(), DateUtils.toDate(segmentInfo.getDepDateTime(), "yyyy-MM-dd HH:mm"));
                                    AirPortInfoDto arrAirPortInfoDto = localCacheService.getLocalAirport(segmentInfo.getArrAirport(), DateUtils.toDate(segmentInfo.getDepDateTime(), "yyyy-MM-dd HH:mm"));
                                    segmentInfo.setDepCityName(depAirPortInfoDto.getCityName());
                                    segmentInfo.setArrCityName(arrAirPortInfoDto.getCityName());
                                    segmentInfo.setDepAirportName(depAirPortInfoDto.getAirPortName());
                                    segmentInfo.setArrAirportName(arrAirPortInfoDto.getAirPortName());
                                }
                            }
                        }
                    } else {
                        resp.setTotalRefundBriefInfoList(new ArrayList<RefundTotalBriefInfo>());
                    }
                    resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                    resp.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
                } else {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setErrorInfo(resp.getErrorInfo());
                }
                return resp;
            } catch (Exception e) {
                log.error("查询订单列表出错" + e.getMessage());
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo("查询订单列表返回结果空");
                return resp;
            }
        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo("查询网络出错");
            return resp;
        }
    }

    //临时处理保险名称
    private void changeInsureName(RefundTotalBriefInfo refundTotalBriefInfo) {
        String insureName = "";
        String insureStr = "";
        if (refundTotalBriefInfo.getInsuranceInfo() != null) {
            if (!StringUtil.isNullOrEmpty(refundTotalBriefInfo.getInsuranceInfo().getInsuranceCode())) {
                insureStr = HandlerConstants.INSURE_INFO_D + "," + HandlerConstants.INSURE_INFO_I_OW + "," + HandlerConstants.INSURE_INFO_I_RT;//所有在用的保险集合
                if (!StringUtil.isNullOrEmpty(insureStr)) {
                    String[] insureArray = insureStr.split(",");
                    for (String str : insureArray) {
                        String[] curInsureStr = str.split("&");
                        if (curInsureStr[0].equals(refundTotalBriefInfo.getInsuranceInfo().getInsuranceCode())) {
                            insureName = curInsureStr[1];
                            refundTotalBriefInfo.getInsuranceInfo().setInsuranceName(insureName);
                            break;
                        }
                    }
                }
            }
        }
    }

    //<editor-fold desc="退单详情">
    //机票退单详情查询
    @RequestMapping(value = "/queryRefundDetail", method = RequestMethod.POST)
    public RefundOrderDetailResp QueryRefundOrderDetail(@RequestBody RefundDetailRequest refundDetailRequest, HttpServletRequest request) {
        RefundOrderDetailResp resp = new RefundOrderDetailResp();
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(refundDetailRequest.getCustomerNo(), refundDetailRequest.getLoginKeyInfo(), refundDetailRequest.getChannelCode());
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<RefundDetailRequest>> violations = validator.validate(refundDetailRequest);
        if (null != violations && violations.size() > 0) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(violations.iterator().next().getMessage());
            return resp;
        }

        String userNo = getChannelInfo(refundDetailRequest.getChannelCode(), "10");
        PtRefundOrderDetailReq ptBrief = new PtRefundOrderDetailReq(HandlerConstants.VERSION, refundDetailRequest.getChannelCode(), userNo);
        BeanUtils.copyProperties(refundDetailRequest, ptBrief);
        Map<String, String> headMap = HttpUtil.getHeaderMap(this.getClientIP(request), "");
        String serviceResult = invokePost(ptBrief, HandlerConstants.URL_FARE_API + HandlerConstants.SUB_QUERY_REFUND_DETAIL, headMap, READ_TIMEOUT, CONNECT_TIMEOUT);
        if (StringUtils.isNotBlank(serviceResult)) {
            try {
                resp = (RefundOrderDetailResp) JsonUtil.jsonToBean(serviceResult, RefundOrderDetailResp.class);
                if (resp.getResultCode().equals("1001")) {
                    resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                    resp.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
                } else {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setErrorInfo(resp.getErrorInfo());
                }
                return resp;
            } catch (Exception e) {
                log.error("查询退单详情出错" + e.getMessage());
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo("查询退单详情返回结果空");
                return resp;
            }
        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo("查询退单详情网络出错");
            return resp;
        }
    }

    //保险退单详情查询
    @RequestMapping(value = "/queryInsureRefundDetail", method = RequestMethod.POST)
    public RefundInsureDetailResp QueryInsureRefundOrderDetail(@RequestBody InsureRefundQueryRequest refundDetailRequest, HttpServletRequest request) {
        RefundInsureDetailResp resp = new RefundInsureDetailResp();
        String ip = this.getClientIP(request);
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(refundDetailRequest.getCustomerNo(), refundDetailRequest.getLoginKeyInfo(), refundDetailRequest.getChannelCode());
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<InsureRefundQueryRequest>> violations = validator.validate(refundDetailRequest);
        if (null != violations && violations.size() > 0) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(violations.iterator().next().getMessage());
            return resp;
        }
        String userNo = getChannelInfo(refundDetailRequest.getChannelCode(), "10");
        PtRefundInsureDetailReq ptBrief = new PtRefundInsureDetailReq(HandlerConstants.VERSION, refundDetailRequest.getChannelCode(), userNo);
        BeanUtils.copyProperties(refundDetailRequest, ptBrief);
        HttpResult serviceResult = doPost(ptBrief, HandlerConstants.URL_FARE + HandlerConstants.SUB_QUERY_INSURE_DETAIL);
        if (null != serviceResult && serviceResult.isResult()) {
            try {
                resp = (RefundInsureDetailResp) JsonUtil.jsonToBean(serviceResult.getResponse(), RefundInsureDetailResp.class);
                if (resp.getResultCode().equals("1001")) {
                    Map<String, AirPortInfoDto> airportMap = basicService.queryAllAirportMap(refundDetailRequest.getChannelCode(), ip);
                    if (resp.getInsuranceRefund().getSegmentInfo() != null) {
                        InsureSegmentInfo segInfo = resp.getInsuranceRefund().getSegmentInfo();
                        segInfo.setDepCityNm(airportMap.get(segInfo.getDepAirport()).getCityName() + airportMap.get(segInfo.getDepAirport()).getAirPortName());
                        segInfo.setArrCityNm(airportMap.get(segInfo.getArrAirport()).getCityName() + airportMap.get(segInfo.getArrAirport()).getAirPortName());
                    }
                    resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                    resp.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
                } else {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setErrorInfo(resp.getErrorInfo());
                }
                return resp;
            } catch (Exception e) {
                log.error("查询保险退单详情出错" + e.getMessage());
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo("查询保险退单详情返回结果空");
                return resp;
            }
        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo("查询保险退单详情网络出错");
            return resp;
        }

    }


    /*校验是否为改期退票，如果是的话，改期或升舱票是否已经退*/
    @RequestMapping(value = "/checkChangeRefund", method = RequestMethod.POST)
    public OrderChangedResp checkChangeRefund(@RequestBody @Validated OrderChangedRequest req, BindingResult bindingResult, HttpServletRequest request) {
        //校验是否为改期退票
        OrderChangedResp response = new OrderChangedResp();
        if (bindingResult.hasErrors()) {
            response.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            response.setErrorInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
            return response;
        }
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(req.getCustomerNo(), req.getLoginKeyInfo(), req.getChannelCode());
        if (!flag) {
            response.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            response.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return response;
        }
        if (req.getPassengerSegmentIds() == null || req.getPassengerSegmentIds().isEmpty()) {
            response.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            response.setErrorInfo("人航段不能为空");
            return response;
        }
        String channelCode = req.getChannelCode();
        String userNo = getChannelInfo(channelCode, "10");

        PtOrderChangedRequest refReq = new PtOrderChangedRequest();
        refReq.setVersion(HandlerConstants.VERSION);
        refReq.setChannelCode(channelCode);
        refReq.setUserNo(userNo);
        refReq.setOrderNo(req.getOrderNo());
        refReq.setChannelOrderNo(req.getChannelOrderNo());
        refReq.setPassengerSegmentIds(req.getPassengerSegmentIds());
        try {
            String ip = getClientIP(request);
            Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
            String str = this.invokePost(refReq, HandlerConstants.URL_FARE_API + HandlerConstants.SUB_QUERY_CHANGED_REFUND, headMap, READ_TIMEOUT, CONNECT_TIMEOUT);
            PtOrderChangedResp resp = (PtOrderChangedResp) JsonUtil.jsonToBean(str, PtOrderChangedResp.class);

            if (!"1001".equals(resp.getResultCode())) {
                response.setResultCode(WSEnum.ERROR.getResultCode());
                response.setErrorInfo("校验是否为改期退票出错！");
                return response;
            }
            response.setResultCode(WSEnum.SUCCESS.getResultCode());
            response.setOrderNo(resp.getOrderNo());
            response.setChannelOrderNo(resp.getChannelOrderNo());
            response.setHasChangeRefund(resp.getHasChangeRefund());
        } catch (Exception e) {
            log.error("校验是否为改期退票出错", e);
            response.setHasChangeRefund(true);
            response.setResultCode(WSEnum.ERROR.getResultCode());
            response.setErrorInfo("校验是否为改期退票结果空");

        }
        return response;
    }

    //redis订单详情信息
    @InterfaceLog
    @RequestMapping(value = "/orderRefundDetail", method = RequestMethod.POST)
    @ApiOperation(value = "退票订单信息", notes = "退票订单信息")
    public OrderDetailResp refundOrder(@RequestBody RefundInfoReq req, HttpServletRequest request) {
        OrderDetailResp orderDetails = new OrderDetailResp();
        try {
            boolean flag = checkKeyInfo(req.getCustomNo(), req.getLoginKeyInfo(), req.getChannelCode());
            if (flag) {
                String orderStr = apiRedisService.getData(req.getOrderSign());
                if (StringUtil.isNullOrEmpty(orderStr)) {
                    orderDetails.setResultCode(WSEnum.NO_DATA.getResultCode());
                    orderDetails.setErrorInfo("网络异常，请重新再试！");
                } else {
                    orderDetails = (OrderDetailResp) JsonUtil.jsonToBean(orderStr, OrderDetailResp.class);
                    if (orderDetails.getResultCode().equals(WSEnum.SUCCESS.getResultCode())) {
                        if (!orderDetails.getOrderNO().equals(req.getOrderNo()) || !orderDetails.getCustomerNo().equals(req.getCustomNo()) || !orderDetails.getChannelOrderNo().equals(req.getChannelOrderNo())) {
                            orderDetails.setResultCode(WSEnum.ERROR.getResultCode());
                            orderDetails.setErrorInfo(WSEnum.ERROR.getResultInfo());
                        } else {
                            //隐藏，防止赋值给js时被用户看到
                            orderDetails.setOrderSign(null);
                            orderDetails.setVersion(null);
                            orderDetails.setUserNo(null);
                            //LoungePicUrl中可能存在引号导致转换json异常
                            if (orderDetails.getLoungeBuyList() != null && CollectionUtils.isNotEmpty(orderDetails.getLoungeBuyList())) {
                                for (LoungeBuy loungeBuy : orderDetails.getLoungeBuyList()) {
                                    loungeBuy.setLoungePicUrl(null);
                                }
                            }
                        }
                    } else {
                        orderDetails.setResultCode(WSEnum.ERROR.getResultCode());
                    }
                    for (OrderPassengerInfo orderPass : orderDetails.getOrderPassengerInfoList()) {
                        for (SegmentPriceInfo segPrc : orderPass.getSegmentPriceInfoList()) {
                            if (HandlerConstants.OPEN_FOR_USE.equals(segPrc.getTicketState()) || (HandlerConstants.EXCHANGED.equals(segPrc.getTicketState()) && HandlerConstants.EXCHANGED.equals(segPrc.getTKTStatus()))) {
                                orderPass.setRefundable(true);
                                segPrc.setRefundable(true);
                            }
                            if (HandlerConstants.CHECKED_IN.equals(segPrc.getTicketState())) {
                                segPrc.setCantRefundReason("票号：" + segPrc.getETicketNo() + "处于已值机状态，请先取消值机再申请退票。");
                            }
                            if (FareTypeEnum.ADDON.getFare().equals(orderDetails.getFareType()) && !HandlerConstants.OPEN_FOR_USE.equals(segPrc.getTicketState())) {
                                segPrc.setCantRefundReason("部分航段已使用或已退改，无法退票申请。");
                            }
                        }
                    }
                    if (CollectionUtils.isNotEmpty(orderDetails.getUnlimitedCouponList())) {
                        orderDetails.setRefundMessage(handConfig.getRefundMessage());
                    } else if (OrderSortEnum.Upgrade.getOrderSort().equals(orderDetails.getOrderSort())) {
                        orderDetails.setRefundPageConfirmMessage("本次退票仅退升舱部分，同时原机票也将无法使用；原机票部分，" +
                                "请至原订单中提交申请；如有问题，请联系官方客服95520。<br/>确认要申请退票吗?");
                    } else if (OrderSortEnum.Change.getOrderSort().equals(orderDetails.getOrderSort())) {
                        orderDetails.setRefundPageConfirmMessage("本次退票仅退改期舱位差价部分，同时原机票也将无法使用；原机票部分，" +
                                "请至原订单中提交申请；如有问题，请联系官方客服95520。<br/>确认要申请退票吗?");
                    }
                }
            } else {
                orderDetails.setResultCode(WSEnum.ERROR.getResultCode());
                orderDetails.setErrorInfo("信息检验异常!");
            }
        } catch (Exception e) {
            orderDetails.setResultCode(WSEnum.ERROR.getResultCode());
            orderDetails.setErrorInfo("网络异常");
            log.error("查询订单详情出错：网络异常", e);
            return orderDetails;
        }
        return orderDetails;
    }

    //redis单独退wifi
    @RequestMapping(value = "/refundWifi", method = RequestMethod.POST)
    @ApiOperation(value = "单独退WiFi", notes = "单独退WiFi")
    public WifiGetResp refundWifi(@RequestBody RefundInfoReq req, HttpServletRequest request) {
        WifiGetResp wifiOrder = new WifiGetResp();
        try {
            boolean flag = checkKeyInfo(req.getCustomNo(), req.getLoginKeyInfo(), req.getChannelCode());
            if (flag) {
                wifiOrder = (WifiGetResp) JsonUtil.jsonToBean(apiRedisService.getData(req.getOrderSign()), WifiGetResp.class);
                if (wifiOrder.getResultCode().equals(WSEnum.SUCCESS.getResultCode())) {
                    if (!wifiOrder.getOrderNo().equals(req.getOrderNo()) || !wifiOrder.getCustomerNo().equals(req.getCustomNo())) {
                        wifiOrder.setResultCode(WSEnum.ERROR.getResultCode());
                    }
                } else {
                    wifiOrder.setResultCode(WSEnum.ERROR.getResultCode());
                }
            } else {
                wifiOrder.setResultCode(WSEnum.ERROR.getResultCode());
            }

        } catch (Exception e) {
            wifiOrder.setResultCode(WSEnum.ERROR.getResultCode());
            return wifiOrder;
        }
        return wifiOrder;
    }

    //Redis单独退休息室
    @RequestMapping(value = "/refundLounge", method = RequestMethod.POST)
    @ApiOperation(value = "单独退休息室", notes = "单独退休息室")
    public LoungeGetResp refundLounge(@RequestBody RefundInfoReq req, HttpServletRequest request) {
        LoungeGetResp loungeOrder = new LoungeGetResp();
        try {
            boolean flag = checkKeyInfo(req.getCustomNo(), req.getLoginKeyInfo(), req.getChannelCode());
            if (flag) {
                loungeOrder = (LoungeGetResp) JsonUtil.jsonToBean(apiRedisService.getData(req.getOrderSign()), LoungeGetResp.class);
                if (loungeOrder.getResultCode().equals(WSEnum.SUCCESS.getResultCode())) {
                    if (!loungeOrder.getOrderNo().equals(req.getOrderNo()) || !loungeOrder.getCustomerNo().equals(req.getCustomNo())) {
                        loungeOrder.setResultCode(WSEnum.ERROR.getResultCode());
                    } else {
                        //LoungePicUrl中可能存在引号导致转换json异常
                        if (loungeOrder.getLoungeBuyList() != null && loungeOrder.getLoungeBuyList().size() > 0) {
                            for (LoungeBuy loungeBuy : loungeOrder.getLoungeBuyList()) {
                                loungeBuy.setLoungePicUrl(null);
                            }
                        }
                    }
                } else {
                    loungeOrder.setResultCode(WSEnum.ERROR.getResultCode());
                }
            } else {
                loungeOrder.setResultCode(WSEnum.ERROR.getResultCode());
            }

        } catch (Exception e) {
            loungeOrder.setResultCode(WSEnum.ERROR.getResultCode());
            return loungeOrder;
        }
        return loungeOrder;
    }

    //redis单独退行程单
    @RequestMapping(value = "/refundDelivery", method = RequestMethod.POST)
    @ApiOperation(value = "单独退行程单", notes = "单独退行程单")
    public TicketDeliveryGetResp refundDelivery(@RequestBody RefundInfoReq req, HttpServletRequest request) {
        TicketDeliveryGetResp deliveryOrder = new TicketDeliveryGetResp();
        try {
            boolean flag = checkKeyInfo(req.getCustomNo(), req.getLoginKeyInfo(), req.getChannelCode());
            if (flag) {
                deliveryOrder = (TicketDeliveryGetResp) JsonUtil.jsonToBean(apiRedisService.getData(req.getOrderSign()), TicketDeliveryGetResp.class);
                if (deliveryOrder.getResultCode().equals(WSEnum.SUCCESS.getResultCode())) {
                    if (!deliveryOrder.getOrderNo().equals(req.getOrderNo()) || !deliveryOrder.getCustomerNo().equals(req.getCustomNo())) {
                        deliveryOrder.setResultCode(WSEnum.ERROR.getResultCode());
                    }
                } else {
                    deliveryOrder.setResultCode(WSEnum.ERROR.getResultCode());
                }
            } else {
                deliveryOrder.setResultCode(WSEnum.ERROR.getResultCode());
            }

        } catch (Exception e) {
            deliveryOrder.setResultCode(WSEnum.ERROR.getResultCode());
            return deliveryOrder;
        }
        return deliveryOrder;
    }

    //redis单独退保
    @RequestMapping(value = "/refundInsurance", method = RequestMethod.POST)
    @ApiOperation(value = "单独退保", notes = "单独退保")
    public OrderDetailResp refundInsurance(@RequestBody RefundInfoReq req, HttpServletRequest request) {
        OrderDetailResp orderDetails = new OrderDetailResp();
        try {
            boolean flag = checkKeyInfo(req.getCustomNo(), req.getLoginKeyInfo(), req.getChannelCode());

            if (flag) {
                orderDetails = (OrderDetailResp) JsonUtil.jsonToBean(apiRedisService.getData(req.getOrderSign()), OrderDetailResp.class);
                if (orderDetails.getResultCode().equals(WSEnum.SUCCESS.getResultCode())) {
                    if (!orderDetails.getOrderNO().equals(req.getOrderNo()) || !orderDetails.getCustomerNo().equals(req.getCustomNo())) {
                        orderDetails.setResultCode(WSEnum.ERROR.getResultCode());
                    } else {
                        //LoungePicUrl中可能存在引号导致转换json异常
                        if (orderDetails.getLoungeBuyList() != null && CollectionUtils.isNotEmpty(orderDetails.getLoungeBuyList())) {
                            for (LoungeBuy loungeBuy : orderDetails.getLoungeBuyList()) {
                                loungeBuy.setLoungePicUrl(null);
                            }
                        }
                    }
                } else {
                    orderDetails.setResultCode(WSEnum.ERROR.getResultCode());
                }
            } else {
                orderDetails.setResultCode(WSEnum.ERROR.getResultCode());
            }
        } catch (Exception e) {
            orderDetails.setResultCode(WSEnum.ERROR.getResultCode());
            return orderDetails;
        }
        return orderDetails;
    }

    @RequestMapping(value = "queryRefundTypes", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @ApiOperation(value = "查询退票类型", notes = "查询退票类型")
    public BaseResp<List<RefundType>> recordQuery(@RequestBody BaseReq<String> req, HttpServletRequest request) {
        BaseResp<List<RefundType>> resp = new BaseResp<>();
        String ip = this.getClientIP(request);
        String reqId = StringUtil.newGUID() + request.getRequestURI();
        try {
            //校验参数
            this.checkRequest(req);
            List<RefundType> refundTypes = handConfig.getRefundTypes();
            List<RefundType> fileRefundList;
            String errorBuyOrderRefundRequest = req.getRequest();
            if ("errorBuyOrderRefund".equals(errorBuyOrderRefundRequest)) {
                fileRefundList = refundTypes.stream().filter(refundType -> refundType.isErrorBuyRefund()).collect(Collectors.toList());
            } else {
                fileRefundList = refundTypes.stream().filter(refundType -> !refundType.isErrorBuyRefund()).collect(Collectors.toList());
            }
            resp.setObjData(fileRefundList);
            resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
        } catch (Exception e) {
            this.logError(resp, reqId, ip, req, e);
        }
        return resp;
    }

    @Autowired
    private IBasicService basicService;
    @Autowired
    private InsuranceService insuranceService;
    @Resource(name = MobileCoreBeanNames.API_REDIS_SERVICE)
    private IRedisService apiRedisService;
}

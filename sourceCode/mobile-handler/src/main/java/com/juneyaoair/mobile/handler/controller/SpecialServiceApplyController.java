package com.juneyaoair.mobile.handler.controller;

import cn.hutool.core.util.ObjectUtil;
import com.juneyaoair.CommonBaseConstants;
import com.juneyaoair.appenum.NewSpecialPassengerTypeEnum;
import com.juneyaoair.appenum.NewSpecialServiceTypeEnum;
import com.juneyaoair.appenum.PsmResultCodeEnum;
import com.juneyaoair.appenum.WSEnum;
import com.juneyaoair.appenum.order.UnifiedOrderResultEnum;
import com.juneyaoair.appenum.ticket.TicketQueryTypeEnum;
import com.juneyaoair.baseclass.common.request.BaseReq;
import com.juneyaoair.baseclass.common.response.BaseResp;
import com.juneyaoair.baseclass.specialservice.request.*;
import com.juneyaoair.baseclass.specialservice.response.ResultInfo;
import com.juneyaoair.baseclass.specialservice.response.TfSpecialServiceApplyResponseInfo;
import com.juneyaoair.exception.CommonException;
import com.juneyaoair.mobile.aop.annotation.InterfaceLog;
import com.juneyaoair.mobile.aop.annotation.NotDuplicate;
import com.juneyaoair.mobile.handler.HandlerConstants;
import com.juneyaoair.mobile.handler.config.bean.HandConfig;
import com.juneyaoair.mobile.handler.manage.OrderManage;
import com.juneyaoair.mobile.handler.util.CertNoUtil;
import com.juneyaoair.pattern.PatternCommon;
import com.juneyaoair.thirdentity.request.speedRefund.TicketInfoRequest;
import com.juneyaoair.thirdentity.response.detr.PtIBETicketInfo;
import com.juneyaoair.thirdentity.response.speedRefund.TicketListInfoResponse;
import com.juneyaoair.thirdentity.specialservice.TfSpecialService;
import com.juneyaoair.thirdentity.specialservice.request.TfSpecialServiceApplyRequest;
import com.juneyaoair.thirdentity.specialservice.response.TfSpecialServiceApplyResponse;
import com.juneyaoair.utils.EncoderHandler;
import com.juneyaoair.utils.StringUtil;
import com.juneyaoair.utils.http.HttpResult;
import com.juneyaoair.utils.http.HttpUtil;
import com.juneyaoair.utils.json.JsonUtil;
import com.juneyaoair.utils.util.BeanUtils;
import com.juneyaoair.utils.util.DateUtils;
import com.juneyaoair.utils.util.MdcUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * Created by guanshiyin on 2018/11/15.
 *
 * <AUTHOR>
 * 特殊旅客服务
 */
@RequestMapping("/specialService")
@RestController
@Api(value = "SpecialServiceApplyController", tags = "特殊旅客服务")
public class SpecialServiceApplyController extends BassController {
    private static final String FROM = "B2C";
    private static final String SYSTEM_CODE = "B2C001";
    private static final String MD_5_PASS_WORD = "e23b4f33c231302aa437733de354b14d";
    private static final String APPLY = "apply";
    private static final String URL = HandlerConstants.URL_PASSAGER_API + HandlerConstants.SPE_SERVICE_APPLY;
    private static final String REQUEST_LOG = "请求号:{}，客户端提交参数：{}";
    private static final String RESPONSE_LOG = "请求号:{}，服务端响应结果：{}";
    private static final String ERROR_LOG = "请求号:{}，错误信息:";
    private static final String ERROR_ID_CARD = "请输入正确的证件号！";
    private static final String ERROR_TIME = "0000-00-00";
    private static final String ERROR_GENDER = "性别只能为F或M！";

    @Autowired
    private OrderManage orderManage;

    @Autowired
    private HandConfig handConfig;

    //婴儿摇篮服务申请
    @InterfaceLog
    @ApiOperation(value = "applyBabyCradle", notes = "婴儿摇篮服务申请")
    @RequestMapping(value = "/applyBabyCradle", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @NotDuplicate
    public BaseResp applyBabyCradle(@RequestBody BaseReq<NewBabyCradleInfo> requestDTO, HttpServletRequest request) {
        BaseResp resp = new BaseResp();
        try {
            ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
            Validator validator = factory.getValidator();
            Set<ConstraintViolation<BaseReq>> violations = validator.validate(requestDTO);
            if (null != violations && violations.size() > 0) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo(violations.iterator().next().getMessage());
                return resp;
            }
            NewBabyCradleInfo babyCradle = requestDTO.getRequest();
            //验证用户查询是否正常
            boolean flag = this.checkKeyInfo(babyCradle.getFfpId(), babyCradle.getLoginKeyInfo(), requestDTO.getChannelCode());
            if (!flag) {
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                return resp;
            }
            NewBabyCradleInfo babyCradleInfo = requestDTO.getRequest();
            NewPassengerInfo passengerInfo = babyCradleInfo.getPassengerInfo();
            NewFlightInfo newFlightInfo = babyCradleInfo.getFlightInfo();
            int days = DateUtils.dateDiff(passengerInfo.getPassBirthDate(), newFlightInfo.getFlightDate(), DateUtils.YYYY_MM_DD_PATTERN);
            int age = DateUtils.getAgeByBirth(passengerInfo.getPassBirthDate(), newFlightInfo.getFlightDate(), DateUtils.YYYY_MM_DD_PATTERN);
            //出生日期  婴儿需满14天，小于2周岁
            if (days - 1 < 14 || age == -1 || age >= 2 || age == -2) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo("出生满14天且不超过2周岁的婴儿才可申请此服务");
                return resp;
            }
            //身高体重
            if (Integer.valueOf(passengerInfo.getPassHeight()) > 75 || Integer.valueOf(passengerInfo.getPassWeight()) > 11) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo("身高低于75cm,且体重低于11KG的婴儿才能使用婴儿摇篮");
                return resp;
            }
            //封装请求头
            NewReqHeader header = builderHeader(MdcUtils.getRequestId());
            //封装请求体
            TfSpecialServiceApplyRequest requestEntity = buildApplyBabyCradle(requestDTO.getRequest());
            //向旅客服务网发送请求 请求头，请求体，地址
            HttpResult result = doPost2PassengerService(requestEntity, URL, header);
            //校验返回数据
            BaseResp checkResponse = checkResponse(result);
            return checkResponse;
        } catch (Exception e) {
            log.error(ERROR_LOG, MdcUtils.getRequestId(), e.getMessage());
            resp.setResultInfo(WSEnum.ERROR.getResultInfo());
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            return resp;
        }
    }

    //无陪服务申请
    @ApiOperation(value = "applyNoCompany", notes = "无陪服务申请")
    @RequestMapping(value = "/applyNoCompany", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @NotDuplicate
    @InterfaceLog
    public BaseResp applyNoCompany(@RequestBody @Validated BaseReq<NoAccompanyInfo> requestDTO, BindingResult bindingResult, HttpServletRequest request) {
        BaseResp resp = new BaseResp();
        String reqId = MdcUtils.getRequestId();

        if (bindingResult.hasErrors()) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
            return resp;
        }
        // 验证用户查询是否正常
        NoAccompanyInfo noAccompany = requestDTO.getRequest();
        boolean flag = this.checkKeyInfo(noAccompany.getFfpId(), noAccompany.getLoginKeyInfo(), requestDTO.getChannelCode());
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        String ip = this.getClientIP(request);
        //数据有效性检验
        checkNoCompanyReq(requestDTO, resp);
        if (!WSEnum.SUCCESS.getResultCode().equals(resp.getResultCode())) {
            return resp;
        }
        NoAccompanyInfo noAccompanyInfo=  requestDTO.getRequest();
        String serviceType = noAccompanyInfo.getServiceType();
        if (ObjectUtil.isNotEmpty(requestDTO.getRequest().getFlightInfo())
                &&NewSpecialServiceTypeEnum.NOACCOMPANYCHILDREN.serviceType == Integer.valueOf(serviceType)) {
            String depDateTime = requestDTO.getRequest().getFlightInfo().getDepDateTime();
            if (StringUtils.isNotBlank(depDateTime)) {
                int minute = DateUtils.dateminuteDiff(DateUtils.toAllDate(depDateTime), new Date());
                if (minute < 12 * 60) {
                    resp.setResultInfo("无法申请，无陪儿童服务的旅客应在对应航班起飞时间12小时前完成预订，现已超时");
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    return resp;
                }
            }
            //对票号真实性做校验，再确认是否为儿童票（根据预订系统中乘客年龄、证件号码、儿童“CHD”标识等信息判别
            String ticketNo = requestDTO.getRequest().getFlightInfo().getTicketNo();
            if (ObjectUtil.isNotEmpty(requestDTO.getRequest().getPassengerInfo())&&StringUtils.isNotBlank(ticketNo)) {
                String passName = requestDTO.getRequest().getPassengerInfo().getPassName();
                BaseResp baseResp = queryTicket(requestDTO.getChannelCode(), ticketNo, passName, ip);
                if (!baseResp.getResultCode().equals(WSEnum.SUCCESS.getResultCode())) {
                    resp.setResultInfo(baseResp.getResultInfo());
                    resp.setResultCode(WSEnum.ALERT_ERROR_MESSAGE.getResultCode());
                    return resp;
                }
            }
        }
        try {

            //封装请求头
            NewReqHeader header = builderHeader(reqId);
            //封装请求体
            TfSpecialServiceApplyRequest requestEntity = buildApplyNoCompany(noAccompanyInfo);
            //向旅客服务网发送请求 请求头，请求体，地址
            HttpResult result = doPost2PassengerService(requestEntity, URL, header);
            //校验返回数据
            BaseResp checkResponse = checkResponse(result);
            return checkResponse;

        } catch (Exception e) {
            log.error(ERROR_LOG, reqId, e.getMessage());
            resp.setResultInfo(WSEnum.ERROR.getResultInfo());
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            return resp;
        }
    }

    //轮椅服务申请
    @ApiOperation(value = "applyWheelchair", notes = "轮椅服务申请")
    @RequestMapping(value = "/applyWheelchair", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @NotDuplicate
    public BaseResp applyWheelchair(@RequestBody BaseReq<NewWheelChairPassengersInfo> requestDTO, HttpServletRequest request) {
        String reqId = StringUtil.newGUID() + "_applyWheelchair";
        BaseResp resp = new BaseResp();
        try {
            String logStr = JsonUtil.objectToJson(requestDTO);
            log.info(REQUEST_LOG, reqId, logStr);
            ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
            Validator validator = factory.getValidator();
            Set<ConstraintViolation<BaseReq>> violations = validator.validate(requestDTO);
            if (null != violations && violations.size() > 0) {
                resp.setResultInfo(violations.iterator().next().getMessage());
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                String logStr2 = JsonUtil.objectToJson(resp);
                log.info(RESPONSE_LOG, reqId, logStr2);
                return resp;
            }
            NewWheelChairPassengersInfo wheelChairPassengersInfo = requestDTO.getRequest();
            //验证用户查询是否正常
            boolean flag = this.checkKeyInfo(wheelChairPassengersInfo.getFfpId(), wheelChairPassengersInfo.getLoginKeyInfo(), requestDTO.getChannelCode());
            if (!flag) {
                resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                String logStr2 = JsonUtil.objectToJson(resp);
                log.info(RESPONSE_LOG, reqId, logStr2);
                return resp;
            }
            //参数判错
            checkWheelReq(requestDTO, resp);
            if (!WSEnum.SUCCESS.getResultCode().equals(resp.getResultCode())) {
                String logStr2 = JsonUtil.objectToJson(resp);
                log.info(RESPONSE_LOG, reqId, logStr2);
                return resp;
            }
            //封装请求头
            NewReqHeader header = builderHeader(reqId);
            //封装请求体
            TfSpecialServiceApplyRequest requestEntity = buildApplyWheelChair(requestDTO.getRequest());
            //向旅客服务网发送请求 请求头，请求体，地址
            HttpResult result = doPost2PassengerService(requestEntity, URL, header);
            //校验返回数据
            BaseResp checkResponse = checkResponse(result);
            return checkResponse;
        } catch (Exception e) {
            log.error(ERROR_LOG, reqId, e.getMessage());
            resp.setResultInfo(WSEnum.ERROR.getResultInfo());
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            log.info(RESPONSE_LOG, reqId, JsonUtil.objectToJson(resp));
            return resp;
        }
    }

    //担架旅客服务申请
    @ApiOperation(value = "applyStretcher", notes = "担架旅客服务申请")
    @RequestMapping(value = "/applyStretcher", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @NotDuplicate
    public BaseResp applyStretcher(@RequestBody BaseReq<NewStretcherPassengersInfo> requestDTO, HttpServletRequest request) {
        String reqId = StringUtil.newGUID() + "_applyStretcher";
        BaseResp resp = new BaseResp();
        try {
            String logStr = JsonUtil.objectToJson(requestDTO);
            log.info(REQUEST_LOG, reqId, logStr);
            ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
            Validator validator = factory.getValidator();
            Set<ConstraintViolation<BaseReq>> violations = validator.validate(requestDTO);
            if (null != violations && violations.size() > 0) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo(violations.iterator().next().getMessage());
                String logStr2 = JsonUtil.objectToJson(resp);
                log.info(RESPONSE_LOG, reqId, logStr2);
                return resp;
            }
            NewStretcherPassengersInfo stretcherPassengersInfo = requestDTO.getRequest();
            //验证用户查询是否正常
            boolean flag = this.checkKeyInfo(stretcherPassengersInfo.getFfpId(), stretcherPassengersInfo.getLoginKeyInfo(), requestDTO.getChannelCode());
            if (!flag) {
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                String logStr2 = JsonUtil.objectToJson(resp);
                log.info(RESPONSE_LOG, reqId, logStr2);
                return resp;
            }
            //参数判错
            checkStretcherReq(requestDTO, resp);
            if (!WSEnum.SUCCESS.getResultCode().equals(resp.getResultCode())) {
                String logStr2 = JsonUtil.objectToJson(resp);
                log.info(RESPONSE_LOG, reqId, logStr2);
                return resp;
            }
            //封装请求头
            NewReqHeader header = builderHeader(reqId);
            //封装请求体
            TfSpecialServiceApplyRequest requestEntity = buildApplyStretcher(requestDTO.getRequest());
            //向旅客服务网发送请求 请求头，请求体，地址
            HttpResult result = doPost2PassengerService(requestEntity, URL, header);

            //校验返回数据
            BaseResp checkResponse = checkResponse(result);
            return checkResponse;
        } catch (Exception e) {
            log.error(ERROR_LOG, reqId, e.getMessage());
            resp.setResultInfo(WSEnum.ERROR.getResultInfo());
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            log.info(RESPONSE_LOG, reqId, JsonUtil.objectToJson(resp));
            return resp;
        }
    }

    //孕妇、用氧、患病、活体器官及血液制品运输服务申请
    @ApiOperation(value = "applyOtherService", notes = "其他服务：孕妇、用氧、患病、活体器官及血液制品运输服务申请")
    @RequestMapping(value = "/applyOtherService", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @NotDuplicate
    public BaseResp applyOtherService(@RequestBody BaseReq<NewOtherService> requestDTO, HttpServletRequest request) {
        String reqId = StringUtil.newGUID() + "_applyOtherService";
        BaseResp resp = new BaseResp();
        try {
            String logStr = JsonUtil.objectToJson(requestDTO);
            log.info(REQUEST_LOG, reqId, logStr);
            ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
            Validator validator = factory.getValidator();
            Set<ConstraintViolation<BaseReq>> violations = validator.validate(requestDTO);
            if (null != violations && violations.size() > 0) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo(violations.iterator().next().getMessage());
                String logStr2 = JsonUtil.objectToJson(resp);
                log.info(RESPONSE_LOG, reqId, logStr2);
                return resp;
            }

            //验证用户查询是否正常
            NewOtherService otherService = requestDTO.getRequest();
            boolean flag = this.checkKeyInfo(otherService.getFfpId(), otherService.getLoginKeyInfo(), requestDTO.getChannelCode());
            if (!flag) {
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                String logStr2 = JsonUtil.objectToJson(resp);
                log.info(RESPONSE_LOG, reqId, logStr2);
                return resp;
            }
            //数据有效性检验
            checkOtherServiceReq(requestDTO, resp);
            if (!WSEnum.SUCCESS.getResultCode().equals(resp.getResultCode())) {
                String logStr2 = JsonUtil.objectToJson(resp);
                log.info(RESPONSE_LOG, reqId, logStr2);
                return resp;
            }
            //封装请求头
            NewReqHeader header = builderHeader(reqId);
            //封装请求体
            TfSpecialServiceApplyRequest requestEntity = buildApplyOtherService(requestDTO.getRequest());
            //向旅客服务网发送请求 请求头，请求体，地址
            HttpResult result = doPost2PassengerService(requestEntity, URL, header);

            //校验返回数据
            BaseResp checkResponse = checkResponse(result);
            return checkResponse;
        } catch (Exception e) {
            log.error(ERROR_LOG, reqId, e.getMessage());
            resp.setResultInfo(WSEnum.ERROR.getResultInfo());
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            log.info(RESPONSE_LOG, reqId, JsonUtil.objectToJson(resp));
            return resp;
        }
    }

    //盲人、聋哑、哺乳旅客服务申请
    @ApiOperation(value = "applyAnOtherService", notes = "其他服务：孕妇、用氧、患病、活体器官及血液制品运输服务申请")
    @RequestMapping(value = "/applyAnOtherService", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @NotDuplicate
    public BaseResp applyAnOtherService(@RequestBody BaseReq<OldOtherService> requestDTO, HttpServletRequest request) {
        String reqId = StringUtil.newGUID() + "_applyAnOtherService";
        BaseResp resp = new BaseResp();
        try {
            String logStr = JsonUtil.objectToJson(requestDTO);
            log.info(REQUEST_LOG, reqId, logStr);
            ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
            Validator validator = factory.getValidator();
            Set<ConstraintViolation<BaseReq>> violations = validator.validate(requestDTO);
            if (null != violations && violations.size() > 0) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo(violations.iterator().next().getMessage());
                String logStr2 = JsonUtil.objectToJson(resp);
                log.info(RESPONSE_LOG, reqId, logStr2);
                return resp;
            }
            //验证用户查询是否正常
            OldOtherService oldOtherService = requestDTO.getRequest();
            boolean flag = this.checkKeyInfo(oldOtherService.getFfpId(), oldOtherService.getLoginKeyInfo(), requestDTO.getChannelCode());
            if (!flag) {
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                String logStr2 = JsonUtil.objectToJson(resp);
                log.info(RESPONSE_LOG, reqId, logStr2);
                return resp;
            }
            //数据有效性检验
            checkAnOtherServiceReq(requestDTO, resp);
            if (!WSEnum.SUCCESS.getResultCode().equals(resp.getResultCode())) {
                String logStr2 = JsonUtil.objectToJson(resp);
                log.info(RESPONSE_LOG, reqId, logStr2);
                return resp;
            }
            //封装请求头
            NewReqHeader header = builderHeader(reqId);
            //封装请求体
            TfSpecialServiceApplyRequest requestEntity = buildApplyAnOtherService(requestDTO.getRequest());
            //向旅客服务网发送请求 请求头，请求体，地址
            HttpResult result = doPost2PassengerService(requestEntity, URL, header);

            //校验返回数据
            BaseResp checkResponse = checkResponse(result);
            return checkResponse;
        } catch (Exception e) {
            log.error(ERROR_LOG, reqId, e.getMessage());
            resp.setResultInfo(WSEnum.ERROR.getResultInfo());
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            log.info(RESPONSE_LOG, reqId, JsonUtil.objectToJson(resp));
            return resp;
        }
    }

    //残疾人团队服务申请
    @ApiOperation(value = "applyDisabilityGroup", notes = "残疾人团队服务申请")
    @RequestMapping(value = "/applyDisabilityGroup", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @NotDuplicate
    public BaseResp applyDisabilityGroup(@RequestBody BaseReq<NewDisabilityGroupInfo> requestDTO, HttpServletRequest request) {
        String reqId = StringUtil.newGUID() + "_applyDisabilityGroup";
        BaseResp resp = new BaseResp();
        try {
            String logStr = JsonUtil.objectToJson(requestDTO);
            log.info(REQUEST_LOG, reqId, logStr);
            ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
            Validator validator = factory.getValidator();
            Set<ConstraintViolation<BaseReq>> violations = validator.validate(requestDTO);
            if (null != violations && violations.size() > 0) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo(violations.iterator().next().getMessage());
                String logStr2 = JsonUtil.objectToJson(resp);
                log.info(RESPONSE_LOG, reqId, logStr2);
                return resp;
            }
            //验证用户查询是否正常
            NewDisabilityGroupInfo disabilityGroupInfo = requestDTO.getRequest();
            boolean flag = this.checkKeyInfo(disabilityGroupInfo.getFfpId(), disabilityGroupInfo.getLoginKeyInfo(), requestDTO.getChannelCode());
            if (!flag) {
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                String logStr2 = JsonUtil.objectToJson(resp);
                log.info(RESPONSE_LOG, reqId, logStr2);
                return resp;
            }
            //封装请求头
            NewReqHeader header = builderHeader(reqId);
            //封装请求体
            TfSpecialServiceApplyRequest requestEntity = buildApplyDisabilityGroup(requestDTO.getRequest());
            //向旅客服务网发送请求 请求头，请求体，地址
            HttpResult result = doPost2PassengerService(requestEntity, URL, header);

            //校验返回数据
            BaseResp checkResponse = checkResponse(result);
            return checkResponse;
        } catch (Exception e) {
            log.error(ERROR_LOG, reqId, e.getMessage());
            resp.setResultInfo(WSEnum.ERROR.getResultInfo());
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            log.info(RESPONSE_LOG, reqId, JsonUtil.objectToJson(resp));
            return resp;
        }
    }

    //残障旅客服务申请
    @ApiOperation(value = "applyDisabledPassenger", notes = "残障旅客服务申请")
    @RequestMapping(value = "/applyDisabledPassenger", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @NotDuplicate
    public BaseResp applyDisabledPassenger(@RequestBody BaseReq<NewDisabledPassengerInfo> requestDTO, HttpServletRequest request) {
        String reqId = StringUtil.newGUID() + "_applyDisabledPassenger";
        BaseResp resp = new BaseResp();
        try {
            String logStr = JsonUtil.objectToJson(requestDTO);
            log.info(REQUEST_LOG, reqId, logStr);
            ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
            Validator validator = factory.getValidator();
            Set<ConstraintViolation<BaseReq>> violations = validator.validate(requestDTO);
            if (null != violations && violations.size() > 0) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo(violations.iterator().next().getMessage());
                String logStr2 = JsonUtil.objectToJson(resp);
                log.info(RESPONSE_LOG, reqId, logStr2);
                return resp;
            }
            //验证用户查询是否正常
            NewDisabledPassengerInfo disabledPassengerInfo = requestDTO.getRequest();
            boolean flag = this.checkKeyInfo(disabledPassengerInfo.getFfpId(), disabledPassengerInfo.getLoginKeyInfo(), requestDTO.getChannelCode());
            if (!flag) {
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                String logStr2 = JsonUtil.objectToJson(resp);
                log.info(RESPONSE_LOG, reqId, logStr2);
                return resp;
            }
            //数据有效性检验
            checkDisabledPassengerReq(requestDTO, resp);
            if (!WSEnum.SUCCESS.getResultCode().equals(resp.getResultCode())) {
                String logStr2 = JsonUtil.objectToJson(resp);
                log.info(RESPONSE_LOG, reqId, logStr2);
                return resp;
            }
            //封装请求头
            NewReqHeader header = builderHeader(reqId);
            //封装请求体
            TfSpecialServiceApplyRequest requestEntity = buildDisabledPassenger(requestDTO.getRequest());
            //向旅客服务网发送请求 请求头，请求体，地址
            HttpResult result = doPost2PassengerService(requestEntity, URL, header);

            //校验返回数据
            BaseResp checkResponse = checkResponse(result);
            return checkResponse;
        } catch (Exception e) {
            log.error(ERROR_LOG, reqId, e.getMessage());
            resp.setResultInfo(WSEnum.ERROR.getResultInfo());
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            log.info(RESPONSE_LOG, reqId, JsonUtil.objectToJson(resp));
            return resp;
        }
    }

    //服务犬服务申请
    @ApiOperation(value = "applyServiceDogs", notes = "服务犬服务申请")
    @RequestMapping(value = "/applyServiceDogs", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @NotDuplicate
    public BaseResp applyServiceDogs(@RequestBody BaseReq<NewNeedServiceDogsPassengersInfo> requestDTO, HttpServletRequest request) {
        String reqId = StringUtil.newGUID() + "_applyServiceDogs";
        BaseResp resp = new BaseResp();
        try {
            String logStr = JsonUtil.objectToJson(requestDTO);
            log.info(REQUEST_LOG, reqId, logStr);
            ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
            Validator validator = factory.getValidator();
            Set<ConstraintViolation<BaseReq>> violations = validator.validate(requestDTO);
            if (null != violations && violations.size() > 0) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo(violations.iterator().next().getMessage());
                String logStr2 = JsonUtil.objectToJson(resp);
                log.info(RESPONSE_LOG, reqId, logStr2);
                return resp;
            }
            //验证用户查询是否正常
            NewNeedServiceDogsPassengersInfo needServiceDogsPassengersInfo = requestDTO.getRequest();
            boolean flag = this.checkKeyInfo(needServiceDogsPassengersInfo.getFfpId(), needServiceDogsPassengersInfo.getLoginKeyInfo(), requestDTO.getChannelCode());
            if (!flag) {
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                String logStr2 = JsonUtil.objectToJson(resp);
                log.info(RESPONSE_LOG, reqId, logStr2);
                return resp;
            }
            //数据有效性检验
            checkServiceDogsReq(requestDTO, resp);
            if (!WSEnum.SUCCESS.getResultCode().equals(resp.getResultCode())) {
                String logStr2 = JsonUtil.objectToJson(resp);
                log.info(RESPONSE_LOG, reqId, logStr2);
                return resp;
            }
            //封装请求头
            NewReqHeader header = builderHeader(reqId);
            //封装请求体
            TfSpecialServiceApplyRequest requestEntity = buildApplyServiceDogs(requestDTO.getRequest());
            //向旅客服务网发送请求 请求头，请求体，地址
            HttpResult result = doPost2PassengerService(requestEntity, URL, header);

            //校验返回数据
            BaseResp checkResponse = checkResponse(result);
            return checkResponse;
        } catch (Exception e) {
            log.error(ERROR_LOG, reqId, e.getMessage());
            resp.setResultInfo(WSEnum.ERROR.getResultInfo());
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            log.info(RESPONSE_LOG, reqId, JsonUtil.objectToJson(resp));
            return resp;
        }
    }

    //电动轮椅服务申请
    @ApiOperation(value = "applyDdWheelchair", notes = "电动轮椅服务申请")
    @RequestMapping(value = "/applyDdWheelchair", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @NotDuplicate
    public BaseResp applyDdWheelchair(@RequestBody BaseReq<NewWheelChairPassengersInfo> requestDTO, HttpServletRequest request) {
        String reqId = StringUtil.newGUID() + "_applyDdWheelchair";
        BaseResp resp = new BaseResp();
        try {
            String logStr = JsonUtil.objectToJson(requestDTO);
            log.info(REQUEST_LOG, reqId, logStr);
            ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
            Validator validator = factory.getValidator();
            Set<ConstraintViolation<BaseReq>> violations = validator.validate(requestDTO);
            if (null != violations && violations.size() > 0) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo(violations.iterator().next().getMessage());
                String logStr2 = JsonUtil.objectToJson(resp);
                log.info(RESPONSE_LOG, reqId, logStr2);
                return resp;
            }
            NewWheelChairPassengersInfo wheelChairPassengersInfo = requestDTO.getRequest();
            //验证用户查询是否正常
            boolean flag = this.checkKeyInfo(wheelChairPassengersInfo.getFfpId(), wheelChairPassengersInfo.getLoginKeyInfo(), requestDTO.getChannelCode());
            if (!flag) {
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                String logStr2 = JsonUtil.objectToJson(resp);
                log.info(RESPONSE_LOG, reqId, logStr2);
                return resp;
            }
            //参数判错
            checkWheelReq(requestDTO, resp);
            if (!WSEnum.SUCCESS.getResultCode().equals(resp.getResultCode())) {
                String logStr2 = JsonUtil.objectToJson(resp);
                log.info(RESPONSE_LOG, reqId, logStr2);
                return resp;
            }
            //封装请求头
            NewReqHeader header = builderHeader(reqId);
            //封装请求体
            TfSpecialServiceApplyRequest requestEntity = buildApplyDdWheelChair(requestDTO.getRequest());
            //向旅客服务网发送请求 请求头，请求体，地址
            HttpResult result = doPost2PassengerService(requestEntity, URL, header);
            //校验返回数据
            BaseResp checkResponse = checkResponse(result);
            return checkResponse;
        } catch (Exception e) {
            log.error(ERROR_LOG, reqId, e.getMessage());
            resp.setResultInfo(WSEnum.ERROR.getResultInfo());
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            log.info(RESPONSE_LOG, reqId, JsonUtil.objectToJson(resp));
            return resp;
        }
    }


    //服务犬服务申请参数判错
    private BaseResp checkServiceDogsReq(BaseReq<NewNeedServiceDogsPassengersInfo> req, BaseResp resp) {
        NewNeedServiceDogsPassengersInfo needServiceDogsPassengersInfo = req.getRequest();
        NewFlightInfo flightInfo = needServiceDogsPassengersInfo.getFlightInfo();
        NewPassengerInfo passengerInfo = needServiceDogsPassengersInfo.getPassengerInfo();
        Pattern pattern;
        Matcher matcher;
        if ("NI".equals(passengerInfo.getPassCertType())) {
            //18位
            pattern = Pattern.compile(PatternCommon.ID_NUMBER);
            matcher = pattern.matcher(passengerInfo.getPassCertNo());
            if (!matcher.matches()) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo(ERROR_ID_CARD);
                return resp;
            } else {
                String birthDate = certNoToDate(passengerInfo.getPassCertNo());
                if (ERROR_TIME.equals(birthDate)) {
                    resp.setResultInfo(ERROR_ID_CARD);
                    resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                    return resp;
                }
                if (passengerInfo.getPassCertNo().length() == 18) {
                    passengerInfo.setPassSex(checkSex(passengerInfo.getPassCertNo().substring(16, 17)));
                } else {
                    passengerInfo.setPassSex(checkSex(passengerInfo.getPassCertNo().substring(14, 15)));
                }
                int age = DateUtils.getAgeByBirth(birthDate, flightInfo.getFlightDate(), DateUtils.YYYY_MM_DD_PATTERN);
                passengerInfo.setPassAge(age);
            }
        } else {
            pattern = Pattern.compile(PatternCommon.SEX);
            matcher = pattern.matcher(passengerInfo.getPassSex());
            if (!matcher.matches()) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo(ERROR_GENDER);
                return resp;
            }
        }
        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
        return resp;
    }

    //残障旅客服务申请参数判错
    private BaseResp checkDisabledPassengerReq(BaseReq<NewDisabledPassengerInfo> req, BaseResp resp) {
        NewDisabledPassengerInfo disabledPassengerInfo = req.getRequest();
        NewFlightInfo flightInfo = disabledPassengerInfo.getFlightInfo();
        NewPassengerInfo passengerInfo = disabledPassengerInfo.getPassengerInfo();
        Pattern pattern;
        Matcher matcher;
        if ("NI".equals(passengerInfo.getPassCertType())) {
            //18位
            pattern = Pattern.compile(PatternCommon.ID_NUMBER);
            matcher = pattern.matcher(passengerInfo.getPassCertNo());
            if (!matcher.matches()) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo(ERROR_ID_CARD);
                return resp;
            } else {
                String birthDate = certNoToDate(passengerInfo.getPassCertNo());
                if (ERROR_TIME.equals(birthDate)) {
                    resp.setResultInfo(ERROR_ID_CARD);
                    resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                    return resp;
                }
                if (passengerInfo.getPassCertNo().length() == 18) {
                    passengerInfo.setPassSex(checkSex(passengerInfo.getPassCertNo().substring(16, 17)));
                } else {
                    passengerInfo.setPassSex(checkSex(passengerInfo.getPassCertNo().substring(14, 15)));
                }
                int age = DateUtils.getAgeByBirth(birthDate, flightInfo.getFlightDate(), DateUtils.YYYY_MM_DD_PATTERN);
                passengerInfo.setPassAge(age);
            }
        } else {
            pattern = Pattern.compile(PatternCommon.SEX);
            matcher = pattern.matcher(passengerInfo.getPassSex());
            if (!matcher.matches()) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo(ERROR_GENDER);
                return resp;
            }
        }
        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
        return resp;
    }

    //盲人、聋哑、哺乳旅客服务申请参数判错
    private BaseResp checkAnOtherServiceReq(BaseReq<OldOtherService> req, BaseResp resp) {
        OldOtherService oldOtherService = req.getRequest();
        NewFlightInfo flightInfo = oldOtherService.getFlightInfo();
        NewPassengerInfo passengerInfo = oldOtherService.getPassengerInfo();
        Pattern pattern;
        Matcher matcher;
        if ("NI".equals(passengerInfo.getPassCertType())) {
            //18位
            pattern = Pattern.compile(PatternCommon.ID_NUMBER);
            matcher = pattern.matcher(passengerInfo.getPassCertNo());
            if (!matcher.matches()) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo(ERROR_ID_CARD);
                return resp;
            } else {
                String birthDate = certNoToDate(passengerInfo.getPassCertNo());
                if (ERROR_TIME.equals(birthDate)) {
                    resp.setResultInfo(ERROR_ID_CARD);
                    resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                    return resp;
                }
                if (passengerInfo.getPassCertNo().length() == 18) {
                    passengerInfo.setPassSex(checkSex(passengerInfo.getPassCertNo().substring(16, 17)));
                } else {
                    passengerInfo.setPassSex(checkSex(passengerInfo.getPassCertNo().substring(14, 15)));
                }
                int age = DateUtils.getAgeByBirth(birthDate, flightInfo.getFlightDate(), DateUtils.YYYY_MM_DD_PATTERN);
                passengerInfo.setPassAge(age);
            }
        } else {
            pattern = Pattern.compile(PatternCommon.SEX);
            matcher = pattern.matcher(passengerInfo.getPassSex());
            if (!matcher.matches()) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo(ERROR_GENDER);
                return resp;
            }
        }
        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
        return resp;
    }

    //孕妇、用氧、患病、活体器官及血液制品运输参数判错
    private BaseResp checkOtherServiceReq(BaseReq<NewOtherService> req, BaseResp resp) {
        NewOtherService otherService = req.getRequest();
        NewFlightInfo flightInfo = otherService.getFlightInfo();
        NewPassengerInfo passengerInfo = otherService.getPassengerInfo();
        Pattern pattern;
        Matcher matcher;
        if ("NI".equals(passengerInfo.getPassCertType())) {
            //18位
            pattern = Pattern.compile(PatternCommon.ID_NUMBER);
            matcher = pattern.matcher(passengerInfo.getPassCertNo());
            if (!matcher.matches()) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo(ERROR_ID_CARD);
                return resp;
            } else {
                String birthDate = certNoToDate(passengerInfo.getPassCertNo());
                if (ERROR_TIME.equals(birthDate)) {
                    resp.setResultInfo(ERROR_ID_CARD);
                    resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                    return resp;
                }
                if (passengerInfo.getPassCertNo().length() == 18) {
                    passengerInfo.setPassSex(checkSex(passengerInfo.getPassCertNo().substring(16, 17)));
                } else {
                    passengerInfo.setPassSex(checkSex(passengerInfo.getPassCertNo().substring(14, 15)));
                }
                int age = DateUtils.getAgeByBirth(birthDate, flightInfo.getFlightDate(), DateUtils.YYYY_MM_DD_PATTERN);
                passengerInfo.setPassAge(age);
            }
        } else {
            pattern = Pattern.compile(PatternCommon.SEX);
            matcher = pattern.matcher(passengerInfo.getPassSex());
            if (!matcher.matches()) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo(ERROR_GENDER);
                return resp;
            }
        }
        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
        return resp;
    }

    //担架旅客参数判错
    private BaseResp checkStretcherReq(BaseReq<NewStretcherPassengersInfo> req, BaseResp resp) {
        NewStretcherPassengersInfo stretcherPassengersInfo = req.getRequest();
        NewPassengerInfo passengerInfo = stretcherPassengersInfo.getPsgInfo();
        NewFlightInfo newFlightInfo = stretcherPassengersInfo.getFlightInfo();

        Pattern pattern;
        Matcher matcher;
        if ("NI".equals(passengerInfo.getPassCertType())) {
            //18位
            pattern = Pattern.compile(PatternCommon.ID_NUMBER);
            matcher = pattern.matcher(passengerInfo.getPassCertNo());
            if (!matcher.matches()) {
                resp.setResultInfo(ERROR_ID_CARD);
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                return resp;
            } else {
                String birthDate = certNoToDate(passengerInfo.getPassCertNo());
                if (ERROR_TIME.equals(birthDate)) {
                    resp.setResultInfo(ERROR_ID_CARD);
                    resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                    return resp;
                }
                if (passengerInfo.getPassCertNo().length() == 18) {
                    passengerInfo.setPassSex(checkSex(passengerInfo.getPassCertNo().substring(16, 17)));
                } else {
                    passengerInfo.setPassSex(checkSex(passengerInfo.getPassCertNo().substring(14, 15)));
                }
                int age = DateUtils.getAgeByBirth(birthDate, newFlightInfo.getFlightDate(), DateUtils.YYYY_MM_DD_PATTERN);
                passengerInfo.setPassAge(age);
            }
        } else {
            pattern = Pattern.compile(PatternCommon.SEX);
            matcher = pattern.matcher(passengerInfo.getPassSex());
            if (!matcher.matches()) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo(ERROR_GENDER);
                return resp;
            }
        }
        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
        return resp;
    }

    //轮椅服务参数判错
    private BaseResp checkWheelReq(BaseReq<NewWheelChairPassengersInfo> req, BaseResp resp) {
        NewWheelChairPassengersInfo wheelChairPassengersInfo = req.getRequest();
        NewFlightInfo newFlightInfo = wheelChairPassengersInfo.getFlightInfo();
        NewPassengerInfo passengerInfo = wheelChairPassengersInfo.getPassengerInfo();

        Pattern pattern;
        Matcher matcher;
        if ("NI".equals(passengerInfo.getPassCertType())) {
            //18位
            pattern = Pattern.compile(PatternCommon.ID_NUMBER);
            matcher = pattern.matcher(passengerInfo.getPassCertNo());
            if (!matcher.matches()) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo(ERROR_ID_CARD);
                return resp;
            } else {
                String birthDate = certNoToDate(passengerInfo.getPassCertNo());
                if (ERROR_TIME.equals(birthDate)) {
                    resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                    resp.setResultInfo(ERROR_ID_CARD);
                    return resp;
                }
                if (passengerInfo.getPassCertNo().length() == 18) {
                    passengerInfo.setPassSex(checkSex(passengerInfo.getPassCertNo().substring(16, 17)));
                } else {
                    passengerInfo.setPassSex(checkSex(passengerInfo.getPassCertNo().substring(14, 15)));
                }
                int age = DateUtils.getAgeByBirth(birthDate, newFlightInfo.getFlightDate(), DateUtils.YYYY_MM_DD_PATTERN);
                passengerInfo.setPassAge(age);
            }
        } else {
            pattern = Pattern.compile(PatternCommon.SEX);
            matcher = pattern.matcher(passengerInfo.getPassSex());
            if (!matcher.matches()) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo(ERROR_GENDER);
                return resp;
            }
        }

        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
        return resp;
    }

    //无陪参数判错
    private BaseResp checkNoCompanyReq(BaseReq<NoAccompanyInfo> req, BaseResp resp) {
        NoAccompanyInfo noAccompanyInfo = req.getRequest();
        NewFlightInfo flightInfo = noAccompanyInfo.getFlightInfo();
        String serviceType = noAccompanyInfo.getServiceType();
        //乘机人信息判错
        NewPassengerInfo passengerInfo = noAccompanyInfo.getPassengerInfo();
        Pattern pattern;
        Matcher matcher;
        int age = 0;
        //非身份证的处理
        if (!"NI".equals(passengerInfo.getPassCertType())) {
            //如果传的不是年龄
            if (passengerInfo.getPassBirthDate() != null) {
                //传的出生日期，根据出生日期计算年龄
                age = DateUtils.getAgeByBirth(passengerInfo.getPassBirthDate(), flightInfo.getFlightDate(), DateUtils.YYYY_MM_DD_PATTERN);
            } else {
                //传的年龄
                age = passengerInfo.getPassAge();
            }
            pattern = Pattern.compile(PatternCommon.SEX);
            matcher = pattern.matcher(passengerInfo.getPassSex());
            if (!matcher.matches()) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo(ERROR_GENDER);
                return resp;
            }
        } else {
            //身份证
            //18位
            pattern = Pattern.compile(PatternCommon.ID_NUMBER);
            matcher = pattern.matcher(passengerInfo.getPassCertNo());
            if (matcher.matches()) {
                String birthDate = certNoToDate(passengerInfo.getPassCertNo());
                if (ERROR_TIME.equals(birthDate)) {
                    resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                    resp.setResultInfo(ERROR_ID_CARD);
                    return resp;
                }
                if (passengerInfo.getPassCertNo().length() == 18) {
                    passengerInfo.setPassSex(checkSex(passengerInfo.getPassCertNo().substring(16, 17)));
                } else {
                    passengerInfo.setPassSex(checkSex(passengerInfo.getPassCertNo().substring(14, 15)));
                }
                age = DateUtils.getAgeByBirth(birthDate, flightInfo.getFlightDate(), DateUtils.YYYY_MM_DD_PATTERN);
                passengerInfo.setPassAge(age);
            } else {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo(ERROR_ID_CARD);
                return resp;
            }
        }
        if (NewSpecialServiceTypeEnum.NOACCOMPANYCHILDREN.serviceType == Integer.valueOf(serviceType)) {
            //无陪儿童   年龄为5-12周岁的儿童才可申请此服务
            if (age < 5 || age > 12) {
                resp.setResultInfo("年龄为5-12周岁的儿童才可申请此服务！");
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                return resp;
            }
        } else if (NewSpecialServiceTypeEnum.NOACCOMPANYTEENAGERS.serviceType == Integer.valueOf(serviceType)) {
            //无陪青少年
            if (age < 12 || age >= 18) {
                resp.setResultInfo("年龄为12-18（不含）周岁的青少年才可申请此服务！");
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                return resp;
            }
        } else if (NewSpecialServiceTypeEnum.NOACCOMPANYOLD.serviceType == Integer.valueOf(serviceType)) {
            //无陪老人
            if (age < 65) {
                resp.setResultInfo("年龄为65周岁及以上的老年人才可申请此服务！");
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                return resp;
            }
        } else {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo("不存在的服务类型！");
            return resp;
        }
        //送机人信息判错
        NewSjPersonInfo sjPersonInfo = noAccompanyInfo.getSjPersonInfo();
        int feedAge = 0;
        if ("NI".equals(sjPersonInfo.getSjPersonCertType())) {
            sjPersonInfo.setSjPersonSex("");
            //18位
            pattern = Pattern.compile(PatternCommon.ID_NUMBER);
            matcher = pattern.matcher(sjPersonInfo.getSjPersonCertNo());
            if (matcher.matches()) {
                String birthDate = certNoToDate(sjPersonInfo.getSjPersonCertNo());
                if (ERROR_TIME.equals(birthDate)) {
                    resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                    resp.setResultInfo(ERROR_ID_CARD);
                    return resp;
                }
                feedAge = DateUtils.getAgeByBirth(birthDate, flightInfo.getFlightDate(), DateUtils.YYYY_MM_DD_PATTERN);
                sjPersonInfo.setSjPersonAge(Integer.toString(feedAge));
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo(ERROR_ID_CARD);
                return resp;
            }
            if (feedAge < 18) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("送机人必须为年满18周岁的成人");
                return resp;
            }
        }
        //接机人信息判错
        NewJjPersonInfo jjPersonInfo = noAccompanyInfo.getJjPersonInfo();
        int operatorAge = 0;
        if ("NI".equals(jjPersonInfo.getJjPersonCertType())) {
            jjPersonInfo.setJjPersonSex("");
            //18位
            pattern = Pattern.compile(PatternCommon.ID_NUMBER);
            matcher = pattern.matcher(jjPersonInfo.getJjPersonCertNo());
            if (matcher.matches()) {
                String birthDate = certNoToDate(jjPersonInfo.getJjPersonCertNo());
                if (ERROR_TIME.equals(birthDate)) {
                    resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                    resp.setResultInfo(ERROR_ID_CARD);
                    return resp;
                }
                operatorAge = DateUtils.getAgeByBirth(birthDate, flightInfo.getFlightDate(), DateUtils.YYYY_MM_DD_PATTERN);
                jjPersonInfo.setJjPersonAge(Integer.toString(operatorAge));
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo(ERROR_ID_CARD);
                return resp;
            }
            if (operatorAge < 18) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("接机人必须为年满18周岁的成人");
                return resp;
            }
        }
        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
        return resp;
    }

    //身份证获取生日
    private String certNoToDate(String certNo) {
        String birthDate = "";
        String day = "";
        String month = "";
        String year = "";
        if (certNo.length() == 18) {
            day = certNo.substring(12, 14);
            month = certNo.substring(10, 12);
            year = certNo.substring(6, 10);
            birthDate = year + "-" + month + "-" + day;
        } else if (certNo.length() == 15) {
            //320981910226321
            day = certNo.substring(10, 12);
            month = certNo.substring(8, 10);
            year = "19" + certNo.substring(6, 8);
            birthDate = year + "-" + month + "-" + day;
        } else {
            birthDate = ERROR_TIME;
        }
        return birthDate;
    }

    //身份证判别性别
    private String checkSex(String num) {
        int sex = Integer.parseInt(num);
        //偶数的为女性
        if (sex % 2 == 0) {
            return "F";
        } else {  //男性
            return "M";
        }

    }

    //构建电动轮椅服务请求类
    private TfSpecialServiceApplyRequest buildApplyDdWheelChair(NewWheelChairPassengersInfo wheelChairPassengersInfo) {
        TfSpecialServiceApplyRequest request = new TfSpecialServiceApplyRequest();
        //基础信息
        request.setMemberId(wheelChairPassengersInfo.getFfpId());
        request.setInftCode(APPLY);
        //乘机人 航班信息 预定人
        TfSpecialService tfSpecialService = buildSpecialBaseService(wheelChairPassengersInfo.getPassengerInfo(), wheelChairPassengersInfo.getFlightInfo(), wheelChairPassengersInfo.getYdPersonInfo());

        tfSpecialService.setIsPt(wheelChairPassengersInfo.getIsPt());
        tfSpecialService.setDisability(wheelChairPassengersInfo.getDisability());
        //特殊旅客类型、特殊服务类型
        if (NewSpecialServiceTypeEnum.DDWHEELCHAIR.serviceType == Integer.valueOf(wheelChairPassengersInfo.getServiceType())) {
            tfSpecialService.setPassengerType(NewSpecialPassengerTypeEnum.ECHA.getPassengerType());
            tfSpecialService.setSpeServiceType(NewSpecialServiceTypeEnum.DDWHEELCHAIR.serviceType);
        }

        List<TfSpecialService> tfSpecialServiceList = new ArrayList<>();
        tfSpecialServiceList.add(tfSpecialService);
        request.setData(tfSpecialServiceList);
        return request;
    }

    //构建服务犬服务申请请求类
    private TfSpecialServiceApplyRequest buildApplyServiceDogs(NewNeedServiceDogsPassengersInfo needServiceDogsPassengersInfo) {
        TfSpecialServiceApplyRequest request = new TfSpecialServiceApplyRequest();
        //基础信息
        request.setMemberId(needServiceDogsPassengersInfo.getFfpId());
        request.setInftCode(APPLY);
        //乘机人 航班信息 预定人
        TfSpecialService tfSpecialService = buildSpecialBaseService(needServiceDogsPassengersInfo.getPassengerInfo(), needServiceDogsPassengersInfo.getFlightInfo(), needServiceDogsPassengersInfo.getYdPersonInfo());
        //特殊旅客类型、特殊服务类型
        if (NewSpecialServiceTypeEnum.NEEDCABINSERVICEDOGPASSENGERS.serviceType == Integer.valueOf(needServiceDogsPassengersInfo.getServiceType())) {
            tfSpecialService.setPassengerType(NewSpecialPassengerTypeEnum.PETC.getPassengerType());
            tfSpecialService.setSpeServiceType(NewSpecialServiceTypeEnum.NEEDCABINSERVICEDOGPASSENGERS.serviceType);
        }
        //服务犬信息  导听犬：1	导盲犬：2
        tfSpecialService.setServiceDogType(needServiceDogsPassengersInfo.getServiceDogType());
        //附件路径
        tfSpecialService.setAttachment(needServiceDogsPassengersInfo.getAttachment());

        List<TfSpecialService> tfSpecialServiceList = new ArrayList<>();
        tfSpecialServiceList.add(tfSpecialService);
        request.setData(tfSpecialServiceList);
        request.setInftCode(APPLY);
        return request;
    }

    //构建残障旅客服务申请类
    private TfSpecialServiceApplyRequest buildDisabledPassenger(NewDisabledPassengerInfo disabledPassengerInfo) {
        TfSpecialServiceApplyRequest request = new TfSpecialServiceApplyRequest();
        //基础信息
        request.setMemberId(disabledPassengerInfo.getFfpId());
        request.setInftCode(APPLY);

        //乘机人 航班信息 预定人
        TfSpecialService tfSpecialService = buildSpecialBaseService(disabledPassengerInfo.getPassengerInfo(), disabledPassengerInfo.getFlightInfo(), disabledPassengerInfo.getYdPersonInfo());
        tfSpecialService.setDisability(disabledPassengerInfo.getDisability());
        //特殊旅客类型、特殊服务类型
        if (NewSpecialServiceTypeEnum.MENTALDISABILITIESPASSENGERS.serviceType == Integer.valueOf(disabledPassengerInfo.getServiceType())) {
            tfSpecialService.setPassengerType(NewSpecialPassengerTypeEnum.DISA.getPassengerType());
            tfSpecialService.setSpeServiceType(NewSpecialServiceTypeEnum.MENTALDISABILITIESPASSENGERS.serviceType);
        }
        //残疾类型
        tfSpecialService.setDisabilityType(disabledPassengerInfo.getDisabilityType());
        //陪同人
        NewPtPersonInfo ptPersonInfo = disabledPassengerInfo.getPtPersonInfo();
        if (ptPersonInfo != null) {
            tfSpecialService.setPtPersonName(ptPersonInfo.getPtPersonName());
            tfSpecialService.setPtPersonContact(ptPersonInfo.getPtPersonPhone());
        }
        List<TfSpecialService> tfSpecialServiceList = new ArrayList<>();
        tfSpecialServiceList.add(tfSpecialService);
        request.setData(tfSpecialServiceList);
        request.setInftCode(APPLY);
        return request;
    }

    //构建残疾人团队服务申请类
    private TfSpecialServiceApplyRequest buildApplyDisabilityGroup(NewDisabilityGroupInfo disabilityGroupInfo) {
        TfSpecialServiceApplyRequest request = new TfSpecialServiceApplyRequest();
        //基础信息
        request.setMemberId(disabilityGroupInfo.getFfpId());
        request.setInftCode(APPLY);

        TfSpecialService tfSpecialService = new TfSpecialService();
        tfSpecialService.setApplicationSource("B2C");
        //特殊旅客类型、特殊服务类型
        String serviceType = disabilityGroupInfo.getServiceType();
        if (Integer.valueOf(serviceType) == NewSpecialServiceTypeEnum.DISABILITYGROUP.serviceType) {
            tfSpecialService.setPassengerType(NewSpecialPassengerTypeEnum.DISA.getPassengerType());
            tfSpecialService.setSpeServiceType(NewSpecialServiceTypeEnum.DISABILITYGROUP.serviceType);
        }
        tfSpecialService.setDisability(disabilityGroupInfo.getDisability());
        //残疾人团队人数
        tfSpecialService.setDisabilityNum(disabilityGroupInfo.getDisabilityNum());
        //残疾类型  可以使用,隔开  智力残疾：1	肢体残疾：2  精神残疾：3
        tfSpecialService.setDisabilityType(disabilityGroupInfo.getDisabilityType());
        //是否携带轮椅 1是 0 否
        tfSpecialService.setIsCarryWheelchair(disabilityGroupInfo.getIsCarryWheelchair());
        //航班信息
        NewFlightInfo flightInfo = disabilityGroupInfo.getFlightInfo();
        tfSpecialService.setTicketNo(flightInfo.getTicketNo());
        tfSpecialService.setFlightNo(flightInfo.getFlightNo());
        tfSpecialService.setFlightDate(flightInfo.getFlightDate());
        tfSpecialService.setDepatureStation(flightInfo.getDepAirCode());
        tfSpecialService.setDestinationStation(flightInfo.getArrAirCode());
        //预订人信息
        NewYdPersonInfo ydPersonInfo = disabilityGroupInfo.getYdPersonInfo();
        tfSpecialService.setYdPersonName(ydPersonInfo.getYdPersonName());
        tfSpecialService.setYdPersonPhone(ydPersonInfo.getYdPersonPhone());
        tfSpecialService.setYdPersonEmail(ydPersonInfo.getYdPersonEmail());

        List<TfSpecialService> tfSpecialServiceList = new ArrayList<>();
        tfSpecialServiceList.add(tfSpecialService);
        request.setData(tfSpecialServiceList);
        return request;
    }

    //构建盲人、聋哑、哺乳旅客服务申请类
    private TfSpecialServiceApplyRequest buildApplyAnOtherService(OldOtherService otherService) {
        TfSpecialServiceApplyRequest request = new TfSpecialServiceApplyRequest();
        //基础信息
        request.setMemberId(otherService.getFfpId());
        request.setInftCode(APPLY);

        //乘机人 航班信息 预定人
        TfSpecialService tfSpecialService = buildSpecialBaseService(otherService.getPassengerInfo(), otherService.getFlightInfo(), otherService.getYdPersonInfo());
        //特殊旅客类型、特殊服务类型
        String serviceType = otherService.getServiceType();
        tfSpecialService.setDisability(otherService.getDisability());
        tfSpecialService.setIsPt(otherService.getIsPt());
        //盲人
        if (NewSpecialServiceTypeEnum.NOACCOMPANYBLINDPASSENGERS.serviceType == Integer.valueOf(serviceType)) {
            tfSpecialService.setPassengerType(NewSpecialPassengerTypeEnum.DISA.getPassengerType());
            tfSpecialService.setSpeServiceType(NewSpecialServiceTypeEnum.NOACCOMPANYBLINDPASSENGERS.serviceType);
        }
        //聋哑
        if (NewSpecialServiceTypeEnum.NOACCOMPANYDEAFPASSENGERS.serviceType == Integer.valueOf(serviceType)) {
            tfSpecialService.setPassengerType(NewSpecialPassengerTypeEnum.DISA.getPassengerType());
            tfSpecialService.setSpeServiceType(NewSpecialServiceTypeEnum.NOACCOMPANYDEAFPASSENGERS.serviceType);
        }
        //哺乳
        if (NewSpecialServiceTypeEnum.LACTATIONPASSENGERS.serviceType == Integer.valueOf(serviceType)) {
            tfSpecialService.setPassengerType(NewSpecialPassengerTypeEnum.WOME.getPassengerType());
            tfSpecialService.setSpeServiceType(NewSpecialServiceTypeEnum.LACTATIONPASSENGERS.serviceType);
        }
        List<TfSpecialService> tfSpecialServiceList = new ArrayList<>();
        tfSpecialServiceList.add(tfSpecialService);
        request.setData(tfSpecialServiceList);
        return request;
    }

    //构建孕妇旅客 用氧旅客 患病旅客 活体器官及血液制品运输服务申请类
    private TfSpecialServiceApplyRequest buildApplyOtherService(NewOtherService otherService) {
        TfSpecialServiceApplyRequest request = new TfSpecialServiceApplyRequest();
        //基础信息
        request.setMemberId(otherService.getFfpId());
        request.setInftCode(APPLY);

        //乘机人 航班信息 预定人
        TfSpecialService tfSpecialService = buildSpecialBaseService(otherService.getPassengerInfo(), otherService.getFlightInfo(), otherService.getYdPersonInfo());
        tfSpecialService.setDisability(otherService.getDisability());
        //特殊旅客类型、特殊服务类型
        String serviceType = otherService.getServiceType();
        //孕妇
        if (NewSpecialServiceTypeEnum.PREGNANTPASSENGERS.serviceType == Integer.valueOf(serviceType)) {
            tfSpecialService.setPassengerType(NewSpecialPassengerTypeEnum.WOME.getPassengerType());
            tfSpecialService.setSpeServiceType(NewSpecialServiceTypeEnum.PREGNANTPASSENGERS.serviceType);
            tfSpecialService.setIsMedicalCertificate(otherService.getIsMedicalCertificate());
        }
        //用氧旅客
        if (NewSpecialServiceTypeEnum.PASSENGERSWITHOXYGEN.serviceType == Integer.valueOf(serviceType)) {
            tfSpecialService.setPassengerType(NewSpecialPassengerTypeEnum.ILL.getPassengerType());
            tfSpecialService.setSpeServiceType(NewSpecialServiceTypeEnum.PASSENGERSWITHOXYGEN.serviceType);
        }
        //患病.
        if (NewSpecialServiceTypeEnum.SICKPASSENGERS.serviceType == Integer.valueOf(serviceType)) {
            tfSpecialService.setPassengerType(NewSpecialPassengerTypeEnum.ILL.getPassengerType());
            tfSpecialService.setSpeServiceType(NewSpecialServiceTypeEnum.SICKPASSENGERS.serviceType);
        }
        //活体器官及血液制品运输
        if (NewSpecialServiceTypeEnum.HUMANORGANDONATION.serviceType == Integer.valueOf(serviceType)) {
            tfSpecialService.setPassengerType(NewSpecialPassengerTypeEnum.TRAN.getPassengerType());
            tfSpecialService.setSpeServiceType(NewSpecialServiceTypeEnum.HUMANORGANDONATION.serviceType);
        }
        //附件
        tfSpecialService.setAttachment(otherService.getAttachment());

        List<TfSpecialService> tfSpecialServiceList = new ArrayList<>();
        tfSpecialServiceList.add(tfSpecialService);
        request.setData(tfSpecialServiceList);
        return request;
    }

    //构建担架旅客申请类
    private TfSpecialServiceApplyRequest buildApplyStretcher(NewStretcherPassengersInfo stretcherPassengersInfo) {
        TfSpecialServiceApplyRequest request = new TfSpecialServiceApplyRequest();
        //基础信息
        request.setMemberId(stretcherPassengersInfo.getFfpId());
        request.setInftCode(APPLY);
        //乘机人 航班信息 预定人
        TfSpecialService tfSpecialService = buildSpecialBaseService(stretcherPassengersInfo.getPsgInfo(), stretcherPassengersInfo.getFlightInfo(), stretcherPassengersInfo.getYdPersonInfo());
        tfSpecialService.setDisability(stretcherPassengersInfo.getDisability());
        tfSpecialService.setIsPt(stretcherPassengersInfo.getIsPt());
        //特殊旅客类型
        tfSpecialService.setPassengerType(NewSpecialPassengerTypeEnum.DISA.getPassengerType());
        //特殊服务类型
        tfSpecialService.setSpeServiceType(NewSpecialServiceTypeEnum.STRETCHERPASSENGERS.serviceType);
        //陪同人:姓名、手机
        NewPtPersonInfo ptPersonInfo = stretcherPassengersInfo.getPtPersonInfo();
        tfSpecialService.setPtPersonName(ptPersonInfo.getPtPersonName());
        tfSpecialService.setPtPersonContact(ptPersonInfo.getPtPersonPhone());
        //附件地址
        tfSpecialService.setAttachment(stretcherPassengersInfo.getAttachment());

        List<TfSpecialService> tfSpecialServiceList = new ArrayList<>();
        tfSpecialServiceList.add(tfSpecialService);
        request.setData(tfSpecialServiceList);
        return request;
    }

    //构建轮椅申请类
    private TfSpecialServiceApplyRequest buildApplyWheelChair(NewWheelChairPassengersInfo wheelChairPassengersInfo) {
        TfSpecialServiceApplyRequest request = new TfSpecialServiceApplyRequest();
        //基础信息
        request.setMemberId(wheelChairPassengersInfo.getFfpId());
        request.setInftCode(APPLY);
        //乘机人 航班信息 预定人
        TfSpecialService tfSpecialService = buildSpecialBaseService(wheelChairPassengersInfo.getPassengerInfo(), wheelChairPassengersInfo.getFlightInfo(), wheelChairPassengersInfo.getYdPersonInfo());
        tfSpecialService.setDisability(wheelChairPassengersInfo.getDisability());
        tfSpecialService.setIsPt(wheelChairPassengersInfo.getIsPt());
        //选择的服务类型:5:地面轮椅、6:登机轮椅、7:机上轮椅
        String speServiceType = wheelChairPassengersInfo.getServiceType();
        if (NewSpecialServiceTypeEnum.GROUNDWHEELCHAIR.serviceType == Integer.valueOf(speServiceType)) {
            //特殊旅客类型
            tfSpecialService.setPassengerType(NewSpecialPassengerTypeEnum.WCHR.getPassengerType());
            //特殊服务类型
            tfSpecialService.setSpeServiceType(NewSpecialServiceTypeEnum.GROUNDWHEELCHAIR.serviceType);
        }
        if (NewSpecialServiceTypeEnum.BOARDWHEELCHAIR.serviceType == Integer.valueOf(speServiceType)) {
            //特殊旅客类型
            tfSpecialService.setPassengerType(NewSpecialPassengerTypeEnum.DISA.getPassengerType());
            //特殊服务类型
            tfSpecialService.setSpeServiceType(NewSpecialServiceTypeEnum.BOARDWHEELCHAIR.serviceType);
        }
        if (NewSpecialServiceTypeEnum.ONBOARDWHEELCHAIR.serviceType == Integer.valueOf(speServiceType)) {
            //特殊旅客类型
            tfSpecialService.setPassengerType(NewSpecialPassengerTypeEnum.DISA.getPassengerType());
            //特殊服务类型
            tfSpecialService.setSpeServiceType(NewSpecialServiceTypeEnum.ONBOARDWHEELCHAIR.serviceType);
        }
        //陪同人:姓名，手机
        NewPtPersonInfo ptPersonInfo = wheelChairPassengersInfo.getPtPersonInfo();
        if (ptPersonInfo != null) {
            if (ptPersonInfo.getPtPersonName() != null) {
                tfSpecialService.setPtPersonName(ptPersonInfo.getPtPersonName());
            }
            if (ptPersonInfo.getPtPersonPhone() != null) {
                tfSpecialService.setPtPersonContact(ptPersonInfo.getPtPersonPhone());
            }
        }
        //接机人姓名，手机
        NewJjPersonInfo jjPersonInfo = wheelChairPassengersInfo.getJjPersonInfo();
        if (jjPersonInfo != null) {
            if (jjPersonInfo.getJjPersonName() != null) {
                tfSpecialService.setJjPersonName(jjPersonInfo.getJjPersonName());
            }
            if (jjPersonInfo.getJjPersonPhone() != null) {
                tfSpecialService.setJjPersonContact(jjPersonInfo.getJjPersonPhone());
            }
        }
        //送机人姓名，手机
        NewSjPersonInfo sjPersonInfo = wheelChairPassengersInfo.getSjPersonInfo();
        if (sjPersonInfo != null) {
            if (sjPersonInfo.getSjPersonName() != null) {
                tfSpecialService.setSjPersonName(sjPersonInfo.getSjPersonName());
            }
            if (sjPersonInfo.getSjPersonPhone() != null) {
                tfSpecialService.setSjPersonContact(sjPersonInfo.getSjPersonPhone());
            }
        }
        //附件地址
        tfSpecialService.setAttachment(wheelChairPassengersInfo.getAttachment());

        List<TfSpecialService> tfSpecialServiceList = new ArrayList<>();
        tfSpecialServiceList.add(tfSpecialService);
        request.setData(tfSpecialServiceList);
        return request;
    }

    //构建无陪申请类
    private TfSpecialServiceApplyRequest buildApplyNoCompany(NoAccompanyInfo noAccompanyInfo) {
        TfSpecialServiceApplyRequest request = new TfSpecialServiceApplyRequest();
        //基础信息
        request.setMemberId(noAccompanyInfo.getFfpId());
        request.setInftCode(APPLY);
        //乘机人 航班信息 预定人
        TfSpecialService tfSpecialService = buildSpecialBaseService(noAccompanyInfo.getPassengerInfo(), noAccompanyInfo.getFlightInfo(), noAccompanyInfo.getYdPersonInfo());

        String serviceType = noAccompanyInfo.getServiceType();

        tfSpecialService.setDisability(noAccompanyInfo.getDisability());
        tfSpecialService.setIsPt(noAccompanyInfo.getIsPt());
        //无陪儿童
        if (NewSpecialServiceTypeEnum.NOACCOMPANYCHILDREN.serviceType == Integer.valueOf(serviceType)) {
            //特殊旅客类型
            tfSpecialService.setPassengerType(NewSpecialPassengerTypeEnum.CHD.getPassengerType());
            //特殊服务类型
            tfSpecialService.setSpeServiceType(NewSpecialServiceTypeEnum.NOACCOMPANYCHILDREN.serviceType);
        }
        //无陪青少年
        if (NewSpecialServiceTypeEnum.NOACCOMPANYTEENAGERS.serviceType == Integer.valueOf(serviceType)) {
            //特殊旅客类型
            tfSpecialService.setPassengerType(NewSpecialPassengerTypeEnum.TEEN.getPassengerType());
            //特殊服务类型
            tfSpecialService.setSpeServiceType(NewSpecialServiceTypeEnum.NOACCOMPANYTEENAGERS.serviceType);
        }
        //无陪老人
        if (NewSpecialServiceTypeEnum.NOACCOMPANYOLD.serviceType == Integer.valueOf(serviceType)) {
            //特殊旅客类型
            tfSpecialService.setPassengerType(NewSpecialPassengerTypeEnum.OLD.getPassengerType());
            //特殊服务类型
            tfSpecialService.setSpeServiceType(NewSpecialServiceTypeEnum.NOACCOMPANYOLD.serviceType);
        }
        //送机人:姓名、手机、证件类型、证件号
        NewSjPersonInfo sjPersonInfo = noAccompanyInfo.getSjPersonInfo();
        tfSpecialService.setSjPersonName(sjPersonInfo.getSjPersonName());
        tfSpecialService.setSjPersonContact(sjPersonInfo.getSjPersonPhone());
        if ("PP".equals(sjPersonInfo.getSjPersonCertType())) {
            tfSpecialService.setSjPersionCertType("PSPT");
        } else {
            tfSpecialService.setSjPersionCertType(sjPersonInfo.getSjPersonCertType());
        }
        tfSpecialService.setSjPersonCert(sjPersonInfo.getSjPersonCertNo());
        //接机人:姓名、手机、证件类型、证件号
        NewJjPersonInfo jjPersonInfo = noAccompanyInfo.getJjPersonInfo();
        tfSpecialService.setJjPersonName(jjPersonInfo.getJjPersonName());
        tfSpecialService.setJjPersonContact(jjPersonInfo.getJjPersonPhone());
        if ("PP".equals(jjPersonInfo.getJjPersonCertType())) {
            tfSpecialService.setJjPersonCertType("PSPT");
        } else {
            tfSpecialService.setJjPersonCertType(jjPersonInfo.getJjPersonCertType());
        }
        tfSpecialService.setJjPersonCert(jjPersonInfo.getJjPersonCertNo());

        List<TfSpecialService> tfSpecialServiceList = new ArrayList<>();
        tfSpecialServiceList.add(tfSpecialService);
        request.setData(tfSpecialServiceList);
        return request;
    }

    //构建婴儿摇篮请求类
    private TfSpecialServiceApplyRequest buildApplyBabyCradle(NewBabyCradleInfo babyCradleInfo) {
        TfSpecialServiceApplyRequest request = new TfSpecialServiceApplyRequest();
        //基础信息
        request.setMemberId(babyCradleInfo.getFfpId());
        request.setInftCode(APPLY);
        //乘机人 航班信息 预定人
        TfSpecialService tfSpecialService = buildSpecialBaseService(babyCradleInfo.getPassengerInfo(), babyCradleInfo.getFlightInfo(), babyCradleInfo.getYdPersonInfo());
        // 校验成人选座
        tfSpecialService.setCheckPtPerson("1");
        //特殊旅客类型
        tfSpecialService.setPassengerType(NewSpecialPassengerTypeEnum.INF.getPassengerType());
        //特殊服务类型speServiceType
        tfSpecialService.setSpeServiceType(NewSpecialServiceTypeEnum.BABYCRADLE.serviceType);
        //陪同人
        NewPtPersonInfo ptPersonInfo = babyCradleInfo.getPtPersonInfo();
        tfSpecialService.setPtPersonName(ptPersonInfo.getPtPersonName());
        if (ptPersonInfo.getPtPersonCertType() != null && ptPersonInfo.getPtPersonCertNo() != null) {
            if ("PP".equals(ptPersonInfo.getPtPersonCertType())) {
                tfSpecialService.setPtPersonCertType("PSPT");
                tfSpecialService.setPtPersonCert(ptPersonInfo.getPtPersonCertNo());
            } else {
                tfSpecialService.setPtPersonCertType(ptPersonInfo.getPtPersonCertType());
                tfSpecialService.setPtPersonCert(ptPersonInfo.getPtPersonCertNo());
            }
        }
        if (ptPersonInfo.getPtPersonBirthDate() != null) {
            tfSpecialService.setPtPersonBirthDate(ptPersonInfo.getPtPersonBirthDate());
        }
        if (ptPersonInfo.getPtPersonSex() != null) {
            tfSpecialService.setPtPersonSex(ptPersonInfo.getPtPersonSex());
        }

        List<TfSpecialService> tfSpecialServiceList = new ArrayList<>();
        tfSpecialServiceList.add(tfSpecialService);
        request.setData(tfSpecialServiceList);
        return request;
    }

    //构建基本的信息  乘机人  航班 预定人
    private TfSpecialService buildSpecialBaseService(NewPassengerInfo passengerInfo, NewFlightInfo flightInfo, NewYdPersonInfo ydPersonInfo) {
        TfSpecialService tfSpecialService = new TfSpecialService();
        //乘机人信息:姓名、年龄、出生日期、身高、体重、证件类型、证件号、出生日期、性别、孕周、用氧量
        tfSpecialService.setPassName(passengerInfo.getPassName());
        tfSpecialService.setApplicationSource("B2C");
        if (String.valueOf(passengerInfo.getPassAge()) != null) {
            tfSpecialService.setPassAge(passengerInfo.getPassAge());
        }
        if (passengerInfo.getPassBirthDate() != null) {
            tfSpecialService.setPassBirthDate(passengerInfo.getPassBirthDate());
        }
        if (passengerInfo.getPassHeight() != null) {
            tfSpecialService.setPassHeight(Integer.valueOf(passengerInfo.getPassHeight()));
        }
        if (passengerInfo.getPassWeight() != null) {
            tfSpecialService.setPassWeight(Integer.valueOf(passengerInfo.getPassWeight()));
        }
        if (passengerInfo.getPassCertType() != null && passengerInfo.getPassCertNo() != null) {
            if ("PP".equals(passengerInfo.getPassCertType())) {
                tfSpecialService.setPassCertType("PSPT");
            } else {
                tfSpecialService.setPassCertType(passengerInfo.getPassCertType());
            }
            tfSpecialService.setPassCertification(passengerInfo.getPassCertNo());
        }
        if (passengerInfo.getPassBirthDate() != null) {
            tfSpecialService.setPassBirthDate(passengerInfo.getPassBirthDate());
        }
        if (passengerInfo.getPassSex() != null) {
            tfSpecialService.setPassSex(passengerInfo.getPassSex());
        }
        if (String.valueOf(passengerInfo.getPregnancyWeek()) != null) {
            tfSpecialService.setPregnancyWeek(passengerInfo.getPregnancyWeek());
        }
        if (String.valueOf(passengerInfo.getOxygenUse()) != null) {
            tfSpecialService.setUserOxygen(passengerInfo.getOxygenUse());
        }
        //航班信息：票号、航班号、出行日期、始发站、到达站
        if (flightInfo.getTicketNo() != null) {
            tfSpecialService.setTicketNo(flightInfo.getTicketNo());
        }
        if (flightInfo.getFlightNo() != null) {
            tfSpecialService.setFlightNo(flightInfo.getFlightNo().toUpperCase());
        }
        tfSpecialService.setFlightDate(flightInfo.getFlightDate());
        tfSpecialService.setDepatureStation(flightInfo.getDepAirCode());
        tfSpecialService.setDestinationStation(flightInfo.getArrAirCode());

        //预订人信息：姓名、手机、邮箱
        tfSpecialService.setYdPersonName(ydPersonInfo.getYdPersonName());
        tfSpecialService.setYdPersonPhone(ydPersonInfo.getYdPersonPhone());
        tfSpecialService.setYdPersonEmail(ydPersonInfo.getYdPersonEmail());
        return tfSpecialService;
    }

    //封装请求头
    private NewReqHeader builderHeader(String reqId) {
        NewReqHeader header = new NewReqHeader();
        String timestamp = String.valueOf(System.currentTimeMillis());
        header.setFrom(FROM);
        header.setSystemCode(SYSTEM_CODE);
        header.setMsgId(reqId);
        header.setTimestamp(timestamp);
        header.setVerifyCode(EncoderHandler.encodeByMD5(SYSTEM_CODE + MD_5_PASS_WORD + timestamp + reqId));
        return header;
    }

    //校验返回数据
    private BaseResp checkResponse(HttpResult result) {
        BaseResp resp = new BaseResp();
        if (null != result && result.isResult()) {
            //真数据
            TfSpecialServiceApplyResponse response = (TfSpecialServiceApplyResponse) JsonUtil.jsonToBean(result.getResponse(), TfSpecialServiceApplyResponse.class);
            TfSpecialServiceApplyResponseInfo tfSpecialServiceApplyResponseInfo = new TfSpecialServiceApplyResponseInfo();
            if (response.getCode() == 0) {
                //操作成功
                BeanUtils.copyProperties(response, tfSpecialServiceApplyResponseInfo);
                if (!response.getData().getApplyStatus().equals("4")) {
                    ResultInfo resultInfo = new ResultInfo();
                    resultInfo.setApplyStatus(response.getData().getApplyStatus());
                    if (response.getData().getSuccess() != null) {
                        resultInfo.setSuccess(response.getData().getSuccess());
                    }
                    if (response.getData().getIds() != null) {
                        resultInfo.setId(response.getData().getIds().get(0));
                    }
                    tfSpecialServiceApplyResponseInfo.setData(resultInfo);
                    resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                    resp.setResultInfo(tfSpecialServiceApplyResponseInfo.getMsg());
                    resp.setObjData(tfSpecialServiceApplyResponseInfo.getData());
                } else {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo(tfSpecialServiceApplyResponseInfo.getMsg());
                }
            } else if (PsmResultCodeEnum.P5000042.getResultCode() == response.getCode()) {
                resp.setResultCode(WSEnum.HAND_MESSAGE.getResultCode());
                resp.setResultInfo("办理婴儿摇篮服务将会为您自动选择座位。检测到您已办理值机选座服务，请先去原渠道取消值机选座服务后再提交申请。");
            } else {
                log.error("旅服系统 错误信息 {}",response.getMsg());
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("服务申请失败!");
            }
        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("网络出错了，请重新提交申请！");
        }
        return resp;
    }


    /**
     * 获取客票信息
     *
     * @param channelCode
     * @param ticketNo
     * @return
     */
    private BaseResp queryTicket(String channelCode, String ticketNo, String passName, String clientIp) {
        String userNo = getChannelInfo(channelCode, "10");
        TicketInfoRequest ticketInfoRequest = new TicketInfoRequest(HandlerConstants.VERSION, channelCode, userNo);
        String certType = CertNoUtil.getCertTypeByCertNo(ticketNo);
        ticketInfoRequest.setCertType(certType);
        ticketInfoRequest.setPassengerName(passName);
        if ("TN".equals(certType)) {
            ticketInfoRequest.setTicketNo(ticketNo);
        } else {
            ticketInfoRequest.setCertNo(ticketNo);
            throw new CommonException(WSEnum.NOT_SUPPORT.getResultCode(), WSEnum.NOT_SUPPORT.getResultInfo());
        }
        ticketInfoRequest.setQueryType(TicketQueryTypeEnum.CHANGE.getCode());
        Map<String, String> headMap = HttpUtil.getHeaderMap(clientIp, "");
        return getTicketInfo(ticketInfoRequest, headMap);
    }



    /**
     * 客票提取
     *
     * @param ticketInfoRequest
     * @param headMap
     * @param useCache          是否放入redis缓存
     * @return
     */
    public BaseResp getTicketInfo(TicketInfoRequest ticketInfoRequest, Map<String, String> headMap) {
        BaseResp resp = new BaseResp();
        String url = HandlerConstants.URL_FARE_API + HandlerConstants.QUERY_TICKET_INFO;
        HttpResult result = HttpUtil.doPostClient(ticketInfoRequest, url, headMap, handConfig.getReadTimeout(), handConfig.getConnectTimeout());
        if (result.isResult()) {
            if (StringUtils.isBlank(result.getResponse())) {
                resp.setResultCode(WSEnum.ALERT_ERROR_MESSAGE.getResultCode());
                resp.setResultInfo("您填写的票号不存在，请您填写真实、准确的票号之后重新提交!");
                return resp;
            } else {
                TicketListInfoResponse ticketListInfoResponse = (TicketListInfoResponse) JsonUtil.jsonToBean(result.getResponse(), TicketListInfoResponse.class);
                if (!UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(ticketListInfoResponse.getResultCode())) {
                    resp.setResultCode(WSEnum.ALERT_ERROR_MESSAGE.getResultCode());
                    resp.setResultInfo("您填写的票号不存在，请您填写真实、准确的票号之后重新提交!");
                    return resp;
                }
                List<PtIBETicketInfo> iBETicketInfoList = ticketListInfoResponse.getIBETicketInfoList();
                if (ObjectUtil.isNotEmpty(iBETicketInfoList)) {
                    List<PtIBETicketInfo> chdTicketInfoList = iBETicketInfoList.stream()
                            .filter(ibeTicketInfo -> CommonBaseConstants.IBE_PASSENGER_TYPE_CHD.equals(ibeTicketInfo.getPassengerType()))
                            .collect(Collectors.toList());
                    if (ObjectUtil.isEmpty(chdTicketInfoList)) {
                        resp.setResultInfo("您输入的票号对应的客票非儿童票，请核对票号信息后重新填写并提交！");
                        resp.setResultCode(WSEnum.ALERT_ERROR_MESSAGE.getResultCode());
                        return resp;
                    }
                }
            }
        } else {
            resp.setResultCode(WSEnum.ALERT_ERROR_MESSAGE.getResultCode());
            resp.setResultInfo("您填写的票号不存在，请您填写真实、准确的票号之后重新提交!");
            return resp;
        }
        resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
        return resp;
    }
}

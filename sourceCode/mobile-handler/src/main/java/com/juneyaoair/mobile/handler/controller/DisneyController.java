package com.juneyaoair.mobile.handler.controller;


import com.google.gson.reflect.TypeToken;
import com.juneyaoair.appenum.ChannelCodeEnum;
import com.juneyaoair.appenum.WSEnum;
import com.juneyaoair.appenum.coupon.CouponTypeEnum;
import com.juneyaoair.appenum.order.UnifiedOrderResultEnum;
import com.juneyaoair.baseclass.common.request.BaseReq;
import com.juneyaoair.baseclass.common.response.BaseResp;
import com.juneyaoair.baseclass.request.disney.*;
import com.juneyaoair.baseclass.response.av.FlightInfo;
import com.juneyaoair.baseclass.response.coupons.AvailCoupon;
import com.juneyaoair.baseclass.response.coupons.AvailCouponsResponse;
import com.juneyaoair.baseclass.response.coupons.Coupon;
import com.juneyaoair.baseclass.response.disney.DisneyCalendarResponse;
import com.juneyaoair.baseclass.response.disney.DisneyTicketChangeResponse;
import com.juneyaoair.mobile.handler.HandlerConstants;
import com.juneyaoair.mobile.handler.bean.coupon.CouponStyle;
import com.juneyaoair.mobile.handler.controller.util.CouponUtil;
import com.juneyaoair.mobile.handler.util.ChannelUtils;
import com.juneyaoair.thirdentity.request.coupon.CouponQueryRequest;
import com.juneyaoair.utils.http.HttpResult;
import com.juneyaoair.utils.http.HttpUtil;
import com.juneyaoair.utils.json.JsonUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("disney")
@Api(value = "迪士尼相关接口", description = "迪士尼相关接口")
public class DisneyController extends BassController{



    @ApiOperation(value = "迪士尼运价日历", notes = "迪士尼运价日历")
    @RequestMapping(value = "disneyPriceCalendar", method = RequestMethod.POST)
    public BaseResp queryBoardingPassProduct(@RequestBody BaseReq<QueryDisneyPriceReq> req, HttpServletRequest request, BindingResult bindingResult) {
        BaseResp baseResp = new BaseResp();
        if (bindingResult.hasErrors()) {
            baseResp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            baseResp.setErrorInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
            return baseResp;
        }
        QueryDisneyPriceRequest  queryDisneyPriceRequest = new QueryDisneyPriceRequest();
        BeanUtils.copyProperties(req.getRequest(),queryDisneyPriceRequest);
        queryDisneyPriceRequest.setVersion("10");
        queryDisneyPriceRequest.setChannelCode(ChannelCodeEnum.B2C.getChannelCode());
        queryDisneyPriceRequest.setPassengerType(req.getRequest().getPassengerType());
        queryDisneyPriceRequest.setQueryDays(req.getRequest().getQueryDays());
        queryDisneyPriceRequest.setQueryDate(req.getRequest().getQueryDate());
        HttpResult httpResult = HttpUtil.doPostClient(queryDisneyPriceRequest, HandlerConstants.URL_FARE_API + HandlerConstants.COUPON_DISNEY_PRICE_CALENDAR);
        DisneyCalendarResponse  disneyCalendarResponse;
        if (httpResult.isResult() && StringUtils.isNotEmpty(httpResult.getResponse())) {
            disneyCalendarResponse = (DisneyCalendarResponse) JsonUtil.jsonToBean(httpResult.getResponse(), new TypeToken<DisneyCalendarResponse>() {
            }.getType());
           if (UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(disneyCalendarResponse.getResultCode())) {
               baseResp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
               baseResp.setResultCode(WSEnum.SUCCESS.getResultCode());
               baseResp.setObjData(disneyCalendarResponse.getDisneyCalendarDayList());
           }else {
               baseResp.setResultInfo(disneyCalendarResponse.getErrorInfo());
               baseResp.setResultCode(WSEnum.NO_DATA.getResultCode());
           }
        }else {
            baseResp.setResultInfo(WSEnum.SERVICE_BUSY_ERROR.getResultInfo());
            baseResp.setResultCode(WSEnum.SERVICE_BUSY_ERROR.getResultCode());
        }
        return  baseResp;
    }

    @ApiOperation(value = "迪士尼优选服务改期", notes = "迪士尼优选服务改期")
    @RequestMapping(value = "disneyTicketChange", method = RequestMethod.POST)
    public BaseResp disneyTicketChange(@RequestBody BaseReq<DisneyTicketChangeReq> req, HttpServletRequest request, BindingResult bindingResult) {
        BaseResp baseResp = new BaseResp();
        if (bindingResult.hasErrors()) {
            baseResp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            baseResp.setErrorInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
            return baseResp;
        }
        String ip = this.getClientIP(request);
        DisneyTicketChangeRequest disneyTicketChangeRequest = new DisneyTicketChangeRequest();
        BeanUtils.copyProperties(req.getRequest(),disneyTicketChangeRequest);
        disneyTicketChangeRequest.setChannelNo(ChannelCodeEnum.MOBILE.getChannelCode());
        disneyTicketChangeRequest.setCurrencyCode("CNY");
        disneyTicketChangeRequest.setOrderRequestIp(ip);
        disneyTicketChangeRequest.setPayAmount("0");
        HttpResult httpResult = HttpUtil.doPostClient(disneyTicketChangeRequest, HandlerConstants.URL_FARE_API + HandlerConstants.DISNEY_TICKET_CHANGE);
        DisneyTicketChangeResponse disneyTicketChangeResponse;
        if (httpResult.isResult() && StringUtils.isNotEmpty(httpResult.getResponse())) {
            disneyTicketChangeResponse = (DisneyTicketChangeResponse) JsonUtil.jsonToBean(httpResult.getResponse(), new TypeToken<DisneyTicketChangeResponse>() {
            }.getType());
            if (UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(disneyTicketChangeResponse.getResultCode())) {
                baseResp.setResultInfo("改期成功");
                baseResp.setResultCode(WSEnum.SUCCESS.getResultCode());
            }else {
                baseResp.setResultInfo(disneyTicketChangeResponse.getErrorInfo());
                baseResp.setResultCode(WSEnum.ERROR.getResultCode());
            }
        }else {
            baseResp.setResultInfo(WSEnum.SERVICE_BUSY_ERROR.getResultInfo());
            baseResp.setResultCode(WSEnum.SERVICE_BUSY_ERROR.getResultCode());
        }
        return  baseResp;
    }


    @ApiOperation(value = "迪士尼单售可用优惠券", notes = "迪士尼单售可用优惠券")
    @RequestMapping(value = "queryDisneyCoupon", method = RequestMethod.POST)
    public AvailCouponsResponse queryDisneyCoupon(@RequestBody BaseReq<DisneyProductInfoReq> req, HttpServletRequest request, BindingResult bindingResult) {
        AvailCouponsResponse baseResp = new AvailCouponsResponse();
        if (bindingResult.hasErrors()) {
            baseResp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            baseResp.setErrorInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
            return baseResp;
        }
        String   channelCode =req.getChannelCode();
        String userNo = ChannelUtils.getChannelInfo(channelCode, "10");
        CouponQueryRequest couponQueryRequest = getCouponQueryRequest(req, channelCode, userNo);
        HttpResult httpResult = HttpUtil.doPostClient(couponQueryRequest, HandlerConstants.New_URL_FARE + HandlerConstants.SUB_QUERY_AVAIL_COUPON);
        AvailCouponsResponse availCouponsResponse;
        if (httpResult.isResult() && StringUtils.isNotEmpty(httpResult.getResponse())) {
            availCouponsResponse = (AvailCouponsResponse) JsonUtil.jsonToBean(httpResult.getResponse(), new TypeToken<AvailCouponsResponse>() {
            }.getType());
            if (availCouponsResponse.getResultCode().equals("1001")) {
                baseResp.setResultCode(WSEnum.SUCCESS.getResultCode());
                baseResp.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
                for (AvailCoupon availCoupon : availCouponsResponse.getAvailCouponList()) {
                    String flightType = availCoupon.getBookingLimit() == null ? "" : availCoupon.getBookingLimit().getFlightType();
                    CouponStyle couponStyle= CouponUtil .creatCouponStyle(availCoupon);
                    couponStyle.setFlightType(flightType);
                    Coupon coupon = CouponUtil.initCouponStyle(couponStyle);
                    availCoupon.setCouponStyle(coupon.getCouponCss());
                    availCoupon.setCouponName(coupon.getCouponName());
                    availCoupon.setCouponSourceName(coupon.getCouponTypeName());
                    availCoupon.setPassengerInfoSuitList(availCoupon.getBookingLimit() == null ? null : availCoupon.getBookingLimit().getPassengerInfoSuitList());
                }
                Map<Boolean, List<AvailCoupon>> partitionedCoupons = availCouponsResponse.getAvailCouponList()
                        .stream()
                        .collect(Collectors.partitioningBy(AvailCoupon::getIsAvailable));

                List<AvailCoupon> availableCoupons = partitionedCoupons.get(true);
                List<AvailCoupon> unavailableCoupons = partitionedCoupons.get(false);
                List<AvailCoupon> sortedByEndDateAndCouponPrice = availableCoupons.stream()
                        .sorted(Comparator.comparing(AvailCoupon::getFlightEndDate, Comparator.nullsFirst(Comparator.naturalOrder()))
                                .thenComparing(AvailCoupon::getCouponPrice, Comparator.nullsFirst(Comparator.naturalOrder())))
                        .collect(Collectors.toList());
                // 仅返回可用优惠券
                baseResp.setAvailCouponList(sortedByEndDateAndCouponPrice);
                baseResp.setUnusableCouponList(unavailableCoupons);
            } else if (availCouponsResponse.getResultCode().equals("1003")) {//没有可用优惠券
                baseResp.setResultCode(WSEnum.SUCCESS.getResultCode());
                baseResp.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
                baseResp.setAvailCouponList(new ArrayList<>());
            } else {
                baseResp.setResultCode(WSEnum.ERROR.getResultCode());
                baseResp.setErrorInfo("查询结果:" + baseResp.getErrorInfo());
            }
            return baseResp;
        }else {
            baseResp.setErrorInfo(WSEnum.SERVICE_BUSY_ERROR.getResultInfo());
            baseResp.setResultCode(WSEnum.SERVICE_BUSY_ERROR.getResultCode());
        }
        return  baseResp;
    }

    private static @NotNull CouponQueryRequest getCouponQueryRequest(BaseReq<DisneyProductInfoReq> req, String channelCode, String userNo) {
        DisneyProductInfoReq  disneyProductInfoReq  =  req.getRequest();
        CouponQueryRequest couponQueryRequest = new CouponQueryRequest();
        couponQueryRequest.setChannelCode(channelCode);
        couponQueryRequest.setUserNo(userNo);
        couponQueryRequest.setVersion(HandlerConstants.VERSION);
        couponQueryRequest.setFfpCardNo(disneyProductInfoReq.getFfpCardNo());
        couponQueryRequest.setFfpId(disneyProductInfoReq.getFfpId());
        couponQueryRequest.setCouponState("R");
        couponQueryRequest.setPassengerInfoList(disneyProductInfoReq.getPassengerInfoList());
        couponQueryRequest.setCouponSource("Disney");
        couponQueryRequest.setDisneyProductInfo(disneyProductInfoReq.getDisneyProductInfo());
        return couponQueryRequest;
    }

}

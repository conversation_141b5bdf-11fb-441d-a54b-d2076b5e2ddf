package com.juneyaoair.mobile.handler.controller.util;

import com.juneyaoair.appenum.WSEnum;
import com.juneyaoair.baseclass.basicsys.response.AirPortInfoDto;
import com.juneyaoair.baseclass.request.speedRefund.*;
import com.juneyaoair.baseclass.response.order.refund.detail.RefundOrderDetailResponse;
import com.juneyaoair.baseclass.response.order.refund.detail.RefundPassengerInfo;
import com.juneyaoair.baseclass.response.order.refund.detail.RefundTicketDetail;
import com.juneyaoair.baseclass.response.order.refund.detail.RefundTicketPassenger;
import com.juneyaoair.baseclass.response.order.refund.query.RefundTicketBriefInfo;
import com.juneyaoair.baseclass.response.order.refund.query.RefundTotalBriefInfo;
import com.juneyaoair.baseclass.response.speedRefund.*;
import com.juneyaoair.baseclass.ticket.resp.PassengerInfoDto;
import com.juneyaoair.baseclass.ticket.resp.QueryPassengerInfoByTicketResp;
import com.juneyaoair.baseclass.ticketInfo.Segment;
import com.juneyaoair.exception.CommonException;
import com.juneyaoair.exception.OperationFailedException;
import com.juneyaoair.mobile.core.bean.flight.AirPortInfo;
import com.juneyaoair.mobile.handler.HandlerConstants;
import com.juneyaoair.mobile.handler.config.bean.HandConfig;
import com.juneyaoair.mobile.handler.controller.v2.util.CommonUtil;
import com.juneyaoair.mobile.handler.util.FlightUtil;
import com.juneyaoair.thirdentity.request.speedRefund.*;
import com.juneyaoair.thirdentity.response.speedRefund.*;
import com.juneyaoair.thirdentity.response.detr.PtIBETicketInfo;
import com.juneyaoair.thirdentity.response.detr.PtSegmentInfo;
import com.juneyaoair.utils.StringUtil;
import com.juneyaoair.utils.util.BeanUtils;
import com.juneyaoair.utils.util.DateUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by yaocf on 2017/1/16.
 * 快速退票参数封装
 */
public class SpeedTicketConvert {
    //客票直退查询请求类
    public static TicketInfoRequest formatTicketInfoRequest(TicketInfoReq ticketInfoReq, String userNo) {
        TicketInfoRequest ticketInfoRequest = new TicketInfoRequest(HandlerConstants.VERSION, ticketInfoReq
                .getChannelCode(), userNo);
        String certNO = ticketInfoReq.getCertificateNo();
        //解析凭证号码
        if (certNO.length() >= 15) {
            ticketInfoRequest.setCertType("NI");
            ticketInfoRequest.setCertNo(ticketInfoReq.getCertificateNo());
            throw new CommonException(WSEnum.NOT_SUPPORT.getResultCode(), WSEnum.NOT_SUPPORT.getResultInfo());
        } else {
            ticketInfoRequest.setCertType("TN");
            ticketInfoRequest.setTicketNo(ticketInfoReq.getCertificateNo());
        }
        ticketInfoRequest.setPassengerName(ticketInfoReq.getPassengerName());
        return ticketInfoRequest;
    }

    //客票直退请求
    public static TicketRefundRequest formatTicketRefundRequest(TicketRefundReq ticketRefundReq, String userNo) {
        TicketRefundRequest ticketRefundRequest = new TicketRefundRequest(HandlerConstants.VERSION, ticketRefundReq
                .getChannelCode(), userNo);
        BeanUtils.copyProperties(ticketRefundReq, ticketRefundRequest);
        List<PtRefundSegmentInfo> ptRefundSegmentInfoList = new ArrayList<>();
        List<PtPassengerInfo> ptPassengerInfoList = new ArrayList<>();
        //复制可退航段
        for (RefundSegmentInfo refundSegmentInfo : ticketRefundReq.getRefundSegmentInfoList()) {
            PtRefundSegmentInfo ptRefundSegmentInfo = new PtRefundSegmentInfo();
            BeanUtils.copyProperties(refundSegmentInfo, ptRefundSegmentInfo);
            ptRefundSegmentInfoList.add(ptRefundSegmentInfo);
        }
        //复制乘机人
        for (PassengerInfo passengerInfo : ticketRefundReq.getPassengerInfoList()) {
            PtPassengerInfo ptPassengerInfo = new PtPassengerInfo();
            BeanUtils.copyProperties(passengerInfo, ptPassengerInfo);
            ptPassengerInfo.setPassengerType("ADT");//暂时固定为成人
            ptPassengerInfoList.add(ptPassengerInfo);
        }
        ticketRefundRequest.setOrderRequestIp(ticketRefundReq.getIp());
        ticketRefundRequest.setPNR(ticketRefundReq.getpNR());
        ticketRefundRequest.setChannelCustomerNo(ticketRefundReq.getFfpId());
        ticketRefundRequest.setRefundSegmentInfoList(ptRefundSegmentInfoList);
        ticketRefundRequest.setPassengerInfoList(ptPassengerInfoList);
        return ticketRefundRequest;
    }

    //客票直退查询请求
    public static QueryTicketPayoutRequest formatQueryTicketPayoutRequest(QueryTicketPayoutReq req, String userNo) {
        QueryTicketPayoutRequest request = new QueryTicketPayoutRequest(HandlerConstants.VERSION, req.getChannelCode(),
                userNo, req.getPageSize(), req.getPageNo(), req.getCreateDateEnd(), req.getCreateDateBegin());
        request.setChannelCustomerNo(req.getFfpId());
        return request;
    }

    /**
     * 票号查询快速退票，处理IBE
     *
     * @param ibeTicketInfo
     * @param airportMap
     * @param handConfig
     * @return
     */
    public static IBETicketInfo convertIBEByTicketNo(PtIBETicketInfo ibeTicketInfo, Map<String, AirPortInfoDto> airportMap, HandConfig handConfig) {
        int refundAble = 0;//可退航段数
        IBETicketInfo ibe = new IBETicketInfo();
        ibe.setHOFlight(true);  //默认为吉祥航班
        BeanUtils.copyProperties(ibeTicketInfo, ibe);
        if ("PASSENGER_INFANT".equals(ibe.getPassengerType())) {
            ibe.setIsRefundValid("N");
            ibe.setUnValidReson("婴儿客票暂不支持极速退款，请联系原订票渠道办理退票");
            throw new OperationFailedException("婴儿客票暂不支持极速退款，请联系原订票渠道办理退票");
        }
        if ("PASSENGER_CHILD".equals(ibe.getPassengerType())) {
            ibe.setIsRefundValid("N");
            ibe.setUnValidReson("儿童客票暂不支持极速退款，请联系原订票渠道办理退票");
            throw new OperationFailedException("儿童客票暂不支持极速退款，请联系原订票渠道办理退票");
        }
        // 联程航班
        if ((!ibeTicketInfo.getOrgCity().equals(ibeTicketInfo.getDstCity())) && ibeTicketInfo.getSegmentInfoList().size() > 1) {
            ibe.setIsRefundValid("N");
            ibe.setUnValidReson("联程客票暂不支持极速退款，请联系原订票渠道办理退票");
            throw new OperationFailedException("联程客票暂不支持极速退款，请联系原订票渠道办理退票");
        }
        //多客票联程
        if (!StringUtil.isNullOrEmpty(ibeTicketInfo.getFollowTicketNo())) {
            ibe.setIsRefundValid("N");
            ibe.setUnValidReson("联程客票暂不支持极速退款，请联系原订票渠道办理退票");
            throw new OperationFailedException("联程客票暂不支持极速退款，请联系原订票渠道办理退票");
        }
        // 团队票
        if (FlightUtil.judgeTeamTicket(ibeTicketInfo.getSigningInfo())) {
            ibe.setIsRefundValid("N");
            ibe.setUnValidReson("团队客票暂不支持极速退款，请联系原订票渠道办理退票");
            throw new OperationFailedException("团队客票暂不支持极速退款，请联系原订票渠道办理退票");
        }
        // 公务票
        if (StringUtils.isNotBlank(ibeTicketInfo.getFareCompute()) && ibeTicketInfo.getFareCompute().contains("GP")) {
            ibe.setIsRefundValid("N");
            ibe.setUnValidReson("公务客票暂不支持极速退款，请联系原订票渠道办理退票");
            throw new OperationFailedException("公务客票暂不支持极速退款，请联系原订票渠道办理退票");
        }
        if (StringUtils.isNotBlank(ibeTicketInfo.getTicketNoInf())) {
            ibe.setIsRefundValid("N");
            ibe.setUnValidReson("与婴儿、儿童乘客同行的成人客票暂不支持极速退款，请联系原订票渠道办理退票");
            throw new OperationFailedException("与婴儿、儿童乘客同行的成人客票暂不支持极速退款，请联系原订票渠道办理退票");
        }
        if (CollectionUtils.isNotEmpty(ibeTicketInfo.getSegmentInfoList())) {
            int segNum = ibeTicketInfo.getSegmentInfoList().size();//航段数
            Double cn = ibe.getcN() / segNum;//建设
            Double yq = ibe.getyQ() / segNum;//燃油
            Double ob = ibe.getoB() / segNum;//改期
            Double totalRefundAmount = 0.00;//票号的总可退金额
            //航段处理
            List<SegmentInfo> segmentInfoList = new ArrayList<>();
            boolean isZiYuan = true;//自愿
            //遍历是否有不正常航班，延误取消  自有航班数
            int hoCount = 0;
            for (PtSegmentInfo seg : ibeTicketInfo.getSegmentInfoList()) {
                if (("C".equals(seg.getSegmentStatus())
                        || "D".equals(seg.getSegmentStatus())
                        || "X".equals(seg.getSegmentStatus())) && "OPEN FOR USE".equals(seg.getTicketStatus())) {//延误，取消航班 票号可用
                    isZiYuan = false;
                }
                if (true == seg.getIrrFlag() && "OPEN FOR USE".equals(seg.getTicketStatus())) {
                    isZiYuan = false;
                }
                if ("HO".equals(seg.getAirline())) {
                    hoCount++;
                }
            }
            /*
            客票状态类
            （1）无IRR标记/FOC无延误/取消标识：非航班变动原因如需申请退票，请联系原订票渠道办理，如有疑问，欢迎致电吉祥航空客服热线95520咨询
            （2）CHECK IN状态：已值机客票暂不可办理极速退款，请取消值机后再次尝试提交
             */
            //当一张机票中含多个航段，只有多个航段同时具有以上（1）中包含的标记时，才可以退票
            //过滤机票多个航段（假如有）中的可退票航段（客票状态类）
            long segRefundAble = ibeTicketInfo.getSegmentInfoList().stream().filter(e ->
                    ("C".equals(e.getSegmentStatus()) || "D".equals(e.getSegmentStatus()) || e.getIrrFlag() || "X".equals(e.getSegmentStatus()))
                            && (HandlerConstants.OPEN_FOR_USE.equals(e.getTicketStatus()) || HandlerConstants.CHECKED_IN.equals(e.getTicketStatus()))).count();

            //(1) 无IRR标记/FOC无延误/取消标识
            if (segRefundAble == 0) {
                ibe.setIsRefundValid("N");
                ibe.setUnValidReson("非航班变动原因如需申请退票，请联系原订票渠道办理，如有疑问，欢迎致电吉祥航空客服热线95520咨询");
                throw new OperationFailedException("非航班变动原因如需申请退票，请联系原订票渠道办理，如有疑问，欢迎致电吉祥航空客服热线95520咨询");
            }

            //(2) 机票对应的航段信息中包含CHECK IN状态（即已经值过机的状态）
            long segStatusOfCheckIn = ibeTicketInfo.getSegmentInfoList().stream().filter(e -> HandlerConstants.CHECKED_IN.equals(e.getTicketStatus())).count();
            if (segStatusOfCheckIn > 0) {
                ibe.setIsRefundValid("N");
                ibe.setUnValidReson("已值机客票暂不可办理极速退款，请取消值机后再次尝试提交");
                throw new OperationFailedException("已值机客票暂不可办理极速退款，请取消值机后再次尝试提交");
            }

            //非自有航班
            if (hoCount != segNum) {
                ibe.setHOFlight(false);
                ibe.setIsRefundValid("N");
                ibe.setUnValidReson("非吉祥航空实际承运的客票暂不支持极速退款，请联系原订票渠道办理退票");
                throw new OperationFailedException("非吉祥航空实际承运的客票暂不支持极速退款，请联系原订票渠道办理退票");
            }

            if (isZiYuan) {
                //ibe.setRefundType("自愿退票");
                ibe.setIsRefundValid("N");
                ibe.setUnValidReson("，非航班变动原因如需申请退票，请联系原订票渠道办理，如有疑问，欢迎致电吉祥航空客服热线95520咨询");
                throw new OperationFailedException("非航班变动原因如需申请退票，请联系原订票渠道办理，如有疑问，欢迎致电吉祥航空客服热线95520咨询");
            } else {//存在不正常航班
                ibe.setRefundType("非自愿退票");
                ibe.setVoluntary(isZiYuan);
            }
            for (PtSegmentInfo seg : ibeTicketInfo.getSegmentInfoList()) {
                double segRefundAmount = 0.00;//每段可退金额
                //String depTime = seg.getDepTime();//起飞时间
                //Date depDate = DateUtils.toDate(depTime, "yyyy-MM-dd HH:mm");
                //depDate = DateUtils.dateAddOrLessSecond(depDate, -2 * 60 * 60);//起飞时间两小时之前
                //航班状态判断,处理手续费
                /*if (isZiYuan) {
                    if ("N".equals(seg.getSegmentStatus()) || "".equals(seg.getSegmentStatus())) {//正常航班  自愿退票
                        if (DateUtils.compareDate(curDate, depDate)) {//起飞时间两小时（含）外
                            seg.setRefundDeduction(seg.getRefundDeductionB());
                        } else {//起飞时间两小时之后
                            seg.setRefundDeduction(seg.getRefundDeductionA());
                        }
                    } else if ("C".equals(seg.getSegmentStatus()) || "D".equals(seg.getSegmentStatus())) {//延误，取消航班  非自愿退票
                        seg.setRefundDeduction(0.00);
                    }
                } else {
                    seg.setRefundDeduction(0.00);
                }*/
                seg.setRefundDeduction(0.00);
                SegmentInfo segmentInfo = new SegmentInfo();
                BeanUtils.copyProperties(seg, segmentInfo);
                AirPortInfoDto dep = airportMap.get(segmentInfo.getDepAirportCode());
                AirPortInfoDto arr = airportMap.get(segmentInfo.getArrAirportCode());
                segmentInfo.setDepAirportName(dep.getAirPortName());
                segmentInfo.setArrAirportName(arr.getAirPortName());
                segmentInfo.setDepCityName(dep.getCityName());
                segmentInfo.setArrCityName(arr.getCityName());
                segmentInfo.setDepCity(dep.getCityCode());
                segmentInfo.setArrCity(arr.getCityCode());
                segmentInfo.setcNTax(cn);//燃油
                segmentInfo.setyQTax(yq);//建设
                segmentInfo.setoB(ob);//改期
                //设置飞行天数和时间
                FlightDaysAndTimeConvert flightDaysAndTimeResult = calFlightDaysAndTime(airportMap, segmentInfo);
                segmentInfo.setDay(flightDaysAndTimeResult.getDay());
                segmentInfo.setFlightTime(flightDaysAndTimeResult.getTime());

                //计算每个可用航段可退金额
                if ("OPEN FOR USE".equals(segmentInfo.getTicketStatus())) {
                    refundAble++;
                    segRefundAmount = segmentInfo.getTicketPrice() + segmentInfo.getcNTax() + segmentInfo.getyQTax() - segmentInfo
                            .getRefundDeduction();
                } else if ("REFUNDED".equals(segmentInfo.getTicketStatus())) {//已退航段
                    segRefundAmount = 0.00;
                    segmentInfo.setDepTime("--");
                    segmentInfo.setFlightNo("--");
                } else if ("USED/FLOWN".equals(segmentInfo.getTicketStatus())) {//已使用的
                    segmentInfo.setRefundDeduction(0.00);
                    segRefundAmount = 0.00;
                } else {
                    segRefundAmount = 0.00;
                }
                segmentInfo.setRefundAmount(segRefundAmount);
//                if(segmentInfo.getRefundAmount() == 0.0){
//                    segmentInfo.setcNTax(0.0);//燃油
//                    segmentInfo.setyQTax(0.0);//建设
//                    segmentInfo.setTicketPrice(0.0);
//                    segmentInfo.setRefundDeduction(0.0);
//                }
                totalRefundAmount += segRefundAmount;//可退金额累加
                segmentInfoList.add(segmentInfo);
            }
            ibe.setTicketNo(ibeTicketInfo.getTicketNo());
            ibe.setRefundAble(refundAble);
            ibe.setSegmentInfoList(segmentInfoList);
            //总的实退金额
            ibe.setRefundAmount(totalRefundAmount);
        }

        // 免票兑换舱位
        if (ibeTicketInfo.getSegmentInfoList().stream().anyMatch(ptSegmentInfo -> ptSegmentInfo.getCabin().equals(handConfig.getFreeTicketCabin()) || "N".equals(ptSegmentInfo.getCabin()))){
            String message = "免票机票暂不支持极速退款，请联系吉祥航空客服热线95520办理";
            ibe.setIsRefundValid("N");
            ibe.setUnValidReson(message);
            throw new OperationFailedException(message);
        }
        // 奖励飞免票兑换舱位
        if (ibeTicketInfo.getSegmentInfoList().stream().anyMatch(ptSegmentInfo -> handConfig.getAwardFlyFreeTicketCabin().contains(ptSegmentInfo.getCabin())|| "N".equals(ptSegmentInfo.getCabin()))){
            String message = "免票机票暂不支持极速退款，请联系吉祥航空客服热线95520办理";
            ibe.setIsRefundValid("N");
            ibe.setUnValidReson(message);
            throw new OperationFailedException(message);
        }
        //公务舱限制不允许快速退票
        if (ibeTicketInfo.getSegmentInfoList().stream().anyMatch(segmentInfo -> "J".equals(CommonUtil.getCabinClassByCabinCode(segmentInfo.getCabin(), handConfig.getCabinClass())))) {
            String message = "公务舱客票暂不支持快速退票，请联系原订票渠道办理，如有疑问，欢迎致电吉祥航空客服热线95520咨询";
            ibe.setIsRefundValid("N");
            ibe.setUnValidReson(message);
            throw new OperationFailedException(message);
        }
        return ibe;
    }

    /**
     * 计算飞行天数和时间
     *
     * @param airportMap
     * @param segmentInfo
     * @return
     */
    public static FlightDaysAndTimeConvert calFlightDaysAndTime(Map<String, AirPortInfoDto> airportMap, SegmentInfo segmentInfo) {
        //计算飞行时间
        AirPortInfoDto airPortInfoDep = airportMap.get(segmentInfo.getDepAirportCode());
        AirPortInfoDto airPortInfoArr = airportMap.get(segmentInfo.getArrAirportCode());
        //出发城市
        String depCityTimeZone = airPortInfoDep.getCityTimeZone();
        //到达城市
        String arrCityTimeZone = airPortInfoArr.getCityTimeZone();
        //获取出发时间
        String depTime = segmentInfo.getDepTime();
        //获取到达时间,航班号
        String arrTime = segmentInfo.getArrTime();
        long time = DateUtils.calDuration(depTime, depCityTimeZone, arrTime, arrCityTimeZone);
        int day = DateUtils.diffDays(depTime, depCityTimeZone, arrTime, arrCityTimeZone);
        FlightDaysAndTimeConvert flightDaysAndTimeResult = new FlightDaysAndTimeConvert();
        flightDaysAndTimeResult.setDay(day);
        flightDaysAndTimeResult.setTime(time);
        return flightDaysAndTimeResult;
    }

    //银行名称请求类
    public static PtBankInfoReq fortmatPtRequest(String channelCode, String userNo, String bankNo) {
        PtBankInfoReq ptRequest = new PtBankInfoReq(HandlerConstants.VERSION, channelCode, userNo);
        ptRequest.setGatewayNo("A0107");
        ptRequest.setBankNo(bankNo);
        return ptRequest;
    }

    //暂时废弃
    public static void formatTicketPayout(QueryTicketPayoutResponse response, QueryTicketPayoutResp resp, Map<String, AirPortInfo> airportMap) {
        List<TicketInfo> list = new ArrayList<>();
        if (!StringUtil.isNullOrEmpty(response.getTicketPayoutInfoList())) {//
            Map<String, Object> map = new HashMap<>();
            for (PtTicketPayoutInfo ptTicketPayoutInfo : response.getTicketPayoutInfoList()) {//处理航段信息
                String ticketNo = ptTicketPayoutInfo.getTicketNo();
                TicketPayoutInfo ticketPayoutInfo = new TicketPayoutInfo();
                AirPortInfo dep = airportMap.get(ptTicketPayoutInfo.getDepAirport());
                AirPortInfo arr = airportMap.get(ptTicketPayoutInfo.getArrAirport());
                BeanUtils.copyProperties(ptTicketPayoutInfo, ticketPayoutInfo);
                ticketPayoutInfo.setDepAirportName(dep.getAirPortName());
                ticketPayoutInfo.setArrAirportName(arr.getAirPortName());
                ticketPayoutInfo.setDepCityName(dep.getCityName());
                ticketPayoutInfo.setArrCityName(arr.getCityName());
                List<TicketPayoutInfo> temp = null;
                if (map.containsKey(ticketNo)) {//已有票号List追加
                    temp = (List<TicketPayoutInfo>) map.get(ticketNo);
                } else {
                    temp = new ArrayList<>();
                }
                temp.add(ticketPayoutInfo);
                map.put(ticketNo, temp);
            }
            if (map != null) {//遍历map
                for (Map.Entry<String, Object> entry : map.entrySet()) {
                    TicketInfo ticketInfo = new TicketInfo();
                    ticketInfo.setTicketNo(entry.getKey());
                    ticketInfo.setTicketPayoutInfoList((List<TicketPayoutInfo>) entry.getValue());
                    list.add(ticketInfo);
                }
            }
            resp.setTicketInfoList(list);
        }

    }

    public static void formatTicketPayoutResp(QueryTicketPayoutResponse response, QueryTicketPayoutResp resp, Map<String,
            AirPortInfoDto> airportMap) {
        List<TicketPayoutInfo> list = new ArrayList<>();
        if (!StringUtil.isNullOrEmpty(response.getTicketPayoutInfoList())) {
            for (PtTicketPayoutInfo ptTicketPayoutInfo : response.getTicketPayoutInfoList()) {//处理航段信息
                TicketPayoutInfo ticketPayoutInfo = new TicketPayoutInfo();
                AirPortInfoDto dep = airportMap.get(ptTicketPayoutInfo.getDepAirport());
                AirPortInfoDto arr = airportMap.get(ptTicketPayoutInfo.getArrAirport());
                BeanUtils.copyProperties(ptTicketPayoutInfo, ticketPayoutInfo);
                ticketPayoutInfo.setMaskBankAccountNO(ticketPayoutInfo.getMaskBankAccountNO());   //银行账号全名
                ticketPayoutInfo.setMaskBankAccount(createAsterisk(ticketPayoutInfo.getMaskBankAccountNO()));
                ticketPayoutInfo.setDepAirportName(dep == null ? "" : dep.getAirPortName());
                ticketPayoutInfo.setArrAirportName(arr == null ? "" : arr.getAirPortName());
                ticketPayoutInfo.setDepCityName(dep == null ? "" : dep.getCityName());
                ticketPayoutInfo.setArrCityName(arr == null ? "" : arr.getCityName());
                ticketPayoutInfo.setRefundType("自愿退票");
                if ("N".equals(ptTicketPayoutInfo.getIsVoluntaryRefund())) {
                    ticketPayoutInfo.setRefundType("非自愿退票");
                }
                ticketPayoutInfo.setTicketPayoutNo(ptTicketPayoutInfo.getTicketPayoutNO());
                if (StringUtil.isNullOrEmpty(ticketPayoutInfo.getPayoutErrorInfo())) {
                    ticketPayoutInfo.setPayoutErrorInfo("");
                }
                list.add(ticketPayoutInfo);
            }
            resp.setTicketPayoutInfoList(list);
        }

    }

    public static RefundTotalBriefInfo convertToRefundDetail(QueryTicketPayoutResponse response, Map<String,
            AirPortInfoDto> airportMap, String refundNo) {
        RefundTotalBriefInfo quickRefundDetailResp = new RefundTotalBriefInfo();
        if (!StringUtil.isNullOrEmpty(response.getTicketPayoutInfoList())) {
            for (PtTicketPayoutInfo e : response.getTicketPayoutInfoList()
            ) {
                if (refundNo.equals(e.getChannelPayoutNo())) {
                    quickRefundDetailResp.setRefundNo(e.getChannelPayoutNo());
                    quickRefundDetailResp.setCreditCard(e.getMaskBankCertNo());
                    quickRefundDetailResp.setQuickRefund(true);

                    com.juneyaoair.baseclass.response.order.refund.detail.SegmentInfo segmentInfo = new com.juneyaoair.baseclass.response.order.refund.detail.SegmentInfo();
                    com.juneyaoair.baseclass.response.order.refund.detail.PassengerInfo passengerInfo = new com.juneyaoair.baseclass.response.order.refund.detail.PassengerInfo();
                    segmentInfo.setDepDateTime(e.getDepDateTime());
                    segmentInfo.setDepCity(e.getDepCity());
                    segmentInfo.setArrCity(e.getArrCity());
                    passengerInfo.setPassengerName(e.getPassengerName());
                    //todo 乘客类型待添加
                    //passengerInfo.setPassengerType(e.);
                    RefundTicketDetail refundTicketDetail = new RefundTicketDetail();
                    refundTicketDetail.setSegmentInfo(segmentInfo);
                    refundTicketDetail.setPassengerInfo(passengerInfo);
                    refundTicketDetail.setTicketNo(e.getTicketNo());
                    refundTicketDetail.setRefundablePrice(e.getPriceAmount());
                    refundTicketDetail.setYQTax(e.getYQTax());
                    refundTicketDetail.setCNTax(e.getCNTax());
                    refundTicketDetail.setRefundableOtherTax(0.0);
                    refundTicketDetail.setRefundableQfee(0.0);
                    refundTicketDetail.setRefundDeduction(0.0);

                    ArrayList<RefundTicketDetail> refundTicketDetails = new ArrayList<>();
                    refundTicketDetails.add(refundTicketDetail);
                    RefundPassengerInfo refundPassengerInfo = new RefundPassengerInfo();
                    refundPassengerInfo.setRefundDetailList(refundTicketDetails);

                    ArrayList<RefundPassengerInfo> refundPassengerInfos = new ArrayList<>();
                    refundPassengerInfos.add(refundPassengerInfo);
                    RefundTicketPassenger refundTicketPassenger = new RefundTicketPassenger();
                    refundTicketPassenger.setRefundPassengerInfoList(refundPassengerInfos);
                    refundTicketPassenger.setRefundApplyDatetime(e.getApplyDate());
                    refundTicketPassenger.setRefundableAmountSum(e.getRefundAmount());
                    refundTicketPassenger.setRefundState(e.getTicketPayoutState());
                    refundTicketPassenger.setProposer(e.getPassengerName());
                    refundTicketPassenger.setLinkTelphone(e.getHandphoneNo());
                    RefundOrderDetail refundOrderDetail = new RefundOrderDetail();
                    refundOrderDetail.setRefundTicketPassenger(refundTicketPassenger);
                    quickRefundDetailResp.setRefundOrderDetail(refundOrderDetail);
                    return quickRefundDetailResp;
                }
            }
        }
        return quickRefundDetailResp;
    }

    //生成很多个*号
    public static String createAsterisk(String str) {
        StringBuffer stringBuffer = new StringBuffer();
        if (!StringUtil.isNullOrEmpty(str)) {
            if (str.length() > 8) {
                for (int i = 0; i < str.length() - 8; i++) {
                    stringBuffer.append("*");
                }
                str = str.replaceAll("([0-9]{4})([0-9]*)([0-9]{4})", "$1" + stringBuffer.toString() + "$3");
            }
        } else {
            str = "";
        }
        return str;
    }

    /**
     * 快速退单详情参数转换
     *
     * @param queryRefundDetailResponse 调用订单系统返回的快速退单详情
     * @param airportMap                机场信息
     * @param refundNo                  退单号（POUT************）
     * @param responseNew
     */
    public static RefundOrderDetailResponse formChargebackDetail(QueryRefundDetailResponse queryRefundDetailResponse, Map<String, AirPortInfoDto> airportMap, String refundNo, QueryPassengerInfoByTicketResp responseNew) {
        RefundOrderDetailResponse response = new RefundOrderDetailResponse();
        if (null != queryRefundDetailResponse.getTicketPayoutDetailInfo()) {
            //乘机人信息

            TicketPayoutDetailInfo ticketPayoutDetailInfo = queryRefundDetailResponse.getTicketPayoutDetailInfo();
            List<PtSpeedSegmentInfo> ticketPayoutDetailList = ticketPayoutDetailInfo.getTicketPayoutDetailList();
            RefundTicketPassenger refundTicketPassenger = new RefundTicketPassenger();

            double ticketPayoutAmount = 0.0;
            for (PtSpeedSegmentInfo elem : ticketPayoutDetailList
            ) {
                ticketPayoutAmount = ticketPayoutAmount + elem.getTicketPayoutAmount();
            }
            refundTicketPassenger.setRefundableAmountSum(ticketPayoutAmount); //退票总金额
            /**
             * 转换申请时间格式
             */
            String applyDateStr = handleTImePattern(ticketPayoutDetailInfo.getCreateAuditDate());
            refundTicketPassenger.setRefundApplyDatetime(applyDateStr); //退票申请时间
            refundTicketPassenger.setRefundState(ticketPayoutDetailInfo.getTicketPayoutState()); //退票状态
            //联系人需要明确，目前以乘机人姓名为准
            refundTicketPassenger.setProposer(ticketPayoutDetailInfo.getPassengerName()); //联系人
            refundTicketPassenger.setLinkTelphone(ticketPayoutDetailInfo.getHandphoneNo());// 联系电话
            ArrayList<RefundPassengerInfo> refundPassengerInfos = new ArrayList<>();
            RefundPassengerInfo refundPassengerInfo = new RefundPassengerInfo();
            ArrayList<RefundTicketDetail> refundTicketDetails = new ArrayList<>();
            for (PtSpeedSegmentInfo e : ticketPayoutDetailList
            ) {
                if (null != e) {

                    RefundTicketDetail refundTicketDetail = new RefundTicketDetail();
                    com.juneyaoair.baseclass.response.order.refund.detail.PassengerInfo passengerInfo = new com.juneyaoair.baseclass.response.order.refund.detail.PassengerInfo();
                    com.juneyaoair.baseclass.response.order.refund.detail.SegmentInfo segmentInfo = new com.juneyaoair.baseclass.response.order.refund.detail.SegmentInfo();
                    //乘客信息设置
                    passengerInfo.setPassengerName(ticketPayoutDetailInfo.getPassengerName());
                    passengerInfo.setPassengerType(ticketPayoutDetailInfo.getPassengerType());
                    // 航段信息设置
                    /**
                     * 起飞时间设置
                     */
                    String flightDateStr = handleTImePattern(e.getFlightDate());
                    segmentInfo.setDepDateTime(flightDateStr); //起飞时间
                    segmentInfo.setDepAirport(e.getDepAirport());
                    segmentInfo.setArrAirport(e.getArrAirport());
                    AirPortInfoDto depAirPort = airportMap.get(segmentInfo.getDepAirport());
                    AirPortInfoDto arrAirPort = airportMap.get(segmentInfo.getArrAirport());
                    segmentInfo.setDepCityName(depAirPort == null ? segmentInfo.getDepAirport() : depAirPort.getCityName());
                    segmentInfo.setArrCityName(arrAirPort == null ? segmentInfo.getArrAirport() : arrAirPort.getCityName());

                    refundTicketDetail.setPassengerInfo(passengerInfo);//乘客信息设置
                    refundTicketDetail.setSegmentInfo(segmentInfo); //航段信息设置
                    refundTicketDetail.setTicketNo(e.getTicketNo()); //票号
                    refundTicketDetail.setRefundablePrice(e.getTichetAmount()); //可退票面
                    refundTicketDetail.setRefundableYQTax(e.getYqTax()); // 燃油费
                    refundTicketDetail.setRefundableCNTax(e.getCnTax());//机建费
                    refundTicketDetail.setRefundableOtherTax(e.getOtherTax()); //其他税费
                    refundTicketDetail.setRefundableQfee(e.getQFee());//Q费
                    refundTicketDetail.setRefundDeduction(e.getRefundDeduction());//退票手续费

                    refundTicketDetails.add(refundTicketDetail);
                }
            }
            refundPassengerInfo.setRefundDetailList(refundTicketDetails);
            refundPassengerInfos.add(refundPassengerInfo);
            refundTicketPassenger.setRefundPassengerInfoList(refundPassengerInfos);
            if (null != responseNew.getPassengerInfoDtos()) {
                for (PassengerInfoDto el : responseNew.getPassengerInfoDtos()
                ) {
                    if (el.getPassengerName().equals(queryRefundDetailResponse.getTicketPayoutDetailInfo().getPassengerName())) {
                        response.setChannelOrderNo(el.getChannelOrderNo());
                        response.setOrderNo(el.getOrderNo());
                    }
                }
            }
            if (!StringUtils.isEmpty(response.getOrderNo())) {
                response.setAble2SeeOrderDetail(true);
            } else {
                response.setAble2SeeOrderDetail(false);
            }
            response.setRefundTicketPassenger(refundTicketPassenger);
            response.setBankCertNum(createAsterisk(ticketPayoutDetailInfo.getBankAccountNo()));
            response.setRefundNo(refundNo);
        }
        return response;
    }

    /**
     * 处理时间格式
     *
     * @param dateStr
     */
    private static String handleTImePattern(String dateStr) {
        if (StringUtils.isEmpty(dateStr)) {
            return "";
        }
        StringBuffer sb = new StringBuffer();
        String strFront = dateStr.substring(0, 10);
        sb.append(strFront);
        sb.append(" ");
        String strBehind = dateStr.substring(11, 19);
        sb.append(strBehind);
        return sb.toString();
    }

    public static List<RefundTicketBriefInfo> formatTicketRefundResp(List<RefundTicketBriefInfo> totalRefundBriefInfoList,
                                                                     QueryTicketPayoutResponse queryTicketPayoutResponse,
                                                                     Map<String, AirPortInfoDto> airportMap) {
        List<PtTicketPayoutInfo> ticketPayoutInfoList = queryTicketPayoutResponse.getTicketPayoutInfoList();
        if (CollectionUtils.isNotEmpty(ticketPayoutInfoList)) {
            for (PtTicketPayoutInfo e : ticketPayoutInfoList) {
                RefundTicketBriefInfo refundTicketBriefInfo = new RefundTicketBriefInfo();
                refundTicketBriefInfo.setRefundNo(e.getTicketPayoutNO()); //客票退款单号(快速退单)
                refundTicketBriefInfo.setRefundState(e.getTicketPayoutState()); // 退款状态
                refundTicketBriefInfo.setRefundFee(String.valueOf(Math.round(e.getRefundAmount()))); // 实退金额
                refundTicketBriefInfo.setIsSpeedRefund(true); //是否快速退票
                ArrayList<com.juneyaoair.baseclass.response.order.query.SegmentInfo> segmentInfos = new ArrayList<>();
                for (Segment segment : e.getSegmentList()
                ) {
                    com.juneyaoair.baseclass.response.order.query.SegmentInfo segmentInfo = new com.juneyaoair.baseclass.response.order.query.SegmentInfo();
                    AirPortInfoDto dep = airportMap.get(segment.getDepAirport());
                    AirPortInfoDto arr = airportMap.get(segment.getArrAirport());
                    //将UTC时间转换为东八区时间
                    String depDateTime = DateUtils.UTCToCST(segment.getFlightDate());

                    segmentInfo.setDepDateTime(depDateTime);
                    segmentInfo.setDepAirportName(dep == null ? "" : dep.getAirPortName());
                    segmentInfo.setArrAirportName(arr == null ? "" : arr.getAirPortName());
                    segmentInfo.setDepCityName(dep == null ? "" : dep.getCityName());
                    segmentInfo.setArrCityName(arr == null ? "" : arr.getCityName());
                    segmentInfo.setFlightDirection(segment.getFligthDirection());
                    segmentInfos.add(segmentInfo);
                }

                refundTicketBriefInfo.setSegmentInfoList(segmentInfos); // 航段信息
                totalRefundBriefInfoList.add(refundTicketBriefInfo);
            }
        }
        return totalRefundBriefInfoList;
    }
}

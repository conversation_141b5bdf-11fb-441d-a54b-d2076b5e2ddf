package com.juneyaoair.mobile.interceptor.utils;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTCreator;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.InvalidClaimException;
import com.auth0.jwt.exceptions.TokenExpiredException;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.google.gson.reflect.TypeToken;
import com.juneyaoair.appenum.ChannelCodeEnum;
import com.juneyaoair.appenum.WSEnum;
import com.juneyaoair.appenum.member.MemberDetailRequestItemsEnum;
import com.juneyaoair.crm.IMemberService;
import com.juneyaoair.exception.CommonException;
import com.juneyaoair.mobile.comm.service.IRedisService;
import com.juneyaoair.mobile.constants.MobileCoreBeanNames;
import com.juneyaoair.mobile.filter.JsonParameterRequestWrapper;
import com.juneyaoair.mobile.handler.HandlerConstants;
import com.juneyaoair.mobile.handler.comm.RedisKeyConfig;
import com.juneyaoair.mobile.handler.config.bean.HandConfig;
import com.juneyaoair.mobile.handler.controller.util.CRMReqUtil;
import com.juneyaoair.mobile.interceptor.bean.*;
import com.juneyaoair.mobile.interceptor.service.MobileLoginVerifyService;
import com.juneyaoair.thirdentity.comm.request.PtApiCRMRequest;
import com.juneyaoair.thirdentity.comm.response.PtCRMResponse;
import com.juneyaoair.thirdentity.member.request.PtMemberDetailRequest;
import com.juneyaoair.thirdentity.member.response.MemberStateInfoSoaModel;
import com.juneyaoair.thirdentity.member.response.PtMemberDetail;
import com.juneyaoair.utils.StringUtil;
import com.juneyaoair.utils.http.ReqUtil;
import com.juneyaoair.utils.json.JsonMapper;
import com.juneyaoair.utils.json.JsonUtil;
import com.juneyaoair.utils.util.MdcUtils;
import com.juneyaoair.utils.util.SM4Util;
import com.juneyaoair.utils.util.VersionNoUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Type;
import java.security.SecureRandom;
import java.util.*;

/**
 * @Author:zc
 * @Description:
 * @Date:Created in 16:39 2019/1/21
 * @Modified by:
 */
@Component
@Slf4j
public class TokenUtils {
    @Autowired
    private HandConfig handconfig;

    public static final String MEMBER_CLAIM_INFO = "memberClaimInfo";
    private final static Type TOKEN_TYPE = new TypeToken<Map<String, String>>() {}.getType();

    /** 单位：毫秒 */
    private static final long EXPIRE_TIME = 3 * 60 * 1000L;

    /** 获取随机时间 */
    private static Random random = new SecureRandom();

    /**
     * 当jwt中token过期，会生成新的token，同时老的token在很短一段时间也可使用
     * 保障新老token都可使用，平滑过渡业务
     * 单位：秒
     */
    private static final long REDIS_OLD_TOKEN_EXPIRE_TIME = 5 * 60L;
    private static final String TOKEN_SECRET = "juneyaoair";

    @Resource(name = MobileCoreBeanNames.API_REDIS_SERVICE)
    private IRedisService redisService;
    @Resource
    private MobileLoginVerifyService mobileLoginVerifyService;
    private static TokenUtils tokenUtils;

    @Autowired
    private IMemberService memberService;

    /**
     * 是否使用SSO方法生产及解析token
     * @param channelCode
     * @param clientVersion
     * @return
     */
    public static boolean getSsoTokenFlag(String channelCode, String clientVersion) {
        // 非MOBILE 使用SSO-TOKEN
        if (!ChannelCodeEnum.MOBILE.getChannelCode().equals(channelCode)) {
            return true;
        }
        // 不存在版本 使用老逻辑
        if (StringUtils.isBlank(tokenUtils.handconfig.getMinSsoTokenVersion())) {
            return false;
        }
        // 版本号>=最低使用SSO-TOKEN版本 使用SSO-TOKEN
        return VersionNoUtil.compareVersions(clientVersion, tokenUtils.handconfig.getMinSsoTokenVersion()) >= 0;
    }

    @PostConstruct
    public void init() {
        tokenUtils = this;
        tokenUtils.redisService = this.redisService;
        tokenUtils.handconfig = this.handconfig;
        tokenUtils.mobileLoginVerifyService = this.mobileLoginVerifyService;
    }

    /**
     * 默认有效期15天
     */
    private static long getTokenExpiress() {
        return tokenUtils.handconfig.getTokenExpiress();
    }

    /**
     * 生成签名，15min后过期
     *
     * @param map
     * @return
     */
    public static String sign(Map<String, String> map) {
        return sign(map, EXPIRE_TIME);
    }

    /**
     * 生成签名
     *
     * @param map        签名中参数
     * @param expireTime 过期时间
     * @return
     */
    public static String sign(Map<String, String> map, Long expireTime) {
        try {
            //设置过期时间
            long now = System.currentTimeMillis();
            Date date = new Date(now + expireTime);
            //私钥及加密算法
            Algorithm algorithm = Algorithm.HMAC256(TOKEN_SECRET);
            //设置头部信息
            Map<String, Object> header = new HashMap<>();
            header.put("typ", "JWT");
            header.put("alg", "HS256");
            //附带username,userId信息，生成签名
            JWTCreator.Builder builder = JWT.create();
            builder.withHeader(header);
            String mapString = JsonUtil.objectToJson(map);
            String encrypted = SM4Util.encryptEcb2Base64(mapString, HandlerConstants.HO_TOKEN_SM4_KEY);
            log.debug("【TokenUtils】token签名信息：{} 加密后信息：{}", mapString, encrypted);
            builder.withClaim(MEMBER_CLAIM_INFO, encrypted);
            String newToken = builder.withExpiresAt(date).sign(algorithm);
            return newToken;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 签名并缓存
     *
     * @param map
     * @return
     */
    public static String signAndCache(Map<String, String> map, String memberCardNo) {
        String channelCode = map.get("channelCode");
        String token = sign(map);
        String key = RedisKeyConfig.createTokenKey(channelCode, memberCardNo);
        tokenUtils.redisService.putData(key, token, getTokenExpiress());
        return token;
    }

    /**
     * 校验token是否正确
     *
     * @param token
     * @return
     */
    public static int verifyToken(String token) {
        if (StringUtil.isNullOrEmpty(token)) {
            return VerifyTokenEnum.TOKEN_EXCEPTION.getCode();
        }
        try {
            Algorithm algorithm = Algorithm.HMAC256(TOKEN_SECRET);
            JWTVerifier verifier = JWT.require(algorithm).build();
            verifier.verify(token);
            return VerifyTokenEnum.TOKEN_SUCCESS.getCode();
        } catch (TokenExpiredException e) {
            //token过期
            return VerifyTokenEnum.TOKEN_EXPIRED.getCode();
        } catch (InvalidClaimException e) {
            //错误claim参数
            return VerifyTokenEnum.TOKEN_VALID.getCode();
        } catch (Exception e) {
            //非法token
            return VerifyTokenEnum.TOKEN_EXCEPTION.getCode();
        }
    }

    /**
     * 解析token
     *
     * @param token 请求token
     */
    public static Map<String, String> parserToken(String token) {
        try {
            // 获取参数
            DecodedJWT jwt = JWT.decode(token);
            String str = jwt.getClaim(MEMBER_CLAIM_INFO).asString();
            if (StringUtils.isBlank(str)) {
                log.error("1.token解析失败，请求ID：{}， token：{} 解析后数据为空", MdcUtils.getRequestId(), token);
                throw new CommonException(WSEnum.INVALID_TOKEN.getResultCode(), "token失效，重新登录！");
            }
            String decrypt = SM4Util.base64decryptEcb(str, HandlerConstants.HO_TOKEN_SM4_KEY);
            Map<String, String> tokenMap = (Map<String, String>) JsonUtil.jsonToMap(decrypt, TOKEN_TYPE);
            if (null == tokenMap || tokenMap.isEmpty()) {
                log.error("2.token解析失败，请求ID：{}， token：{} 解析后数据为空", MdcUtils.getRequestId(), token);
                throw new CommonException(WSEnum.INVALID_TOKEN.getResultCode(), "token失效，重新登录！");
            }
            return tokenMap;
        } catch (CommonException ce) {
            throw ce;
        } catch (Exception e) {
            log.error("3.token解析失败，请求ID：{}， token：{}", MdcUtils.getRequestId(), token);
            throw new CommonException(WSEnum.INVALID_TOKEN.getResultCode(), "token失效，重新登录！");
        }
    }

    /**
     * 1.登陆有效期以redis中数据为准
     * 2.如果jwttoken失效，判断redis中token，不存在则重新登陆，否则重新生成token缓存同时返回给前端
     * 3.如果jwttoken有效，判断redis中token，不存在则重新登陆，否则增加缓存时间
     *
     * @param token
     * @return
     */
    public static VerifyTokenResp verifyTokenAndRenewal(String channelCode, String token) {
        int code = verifyToken(token);
        if (code == VerifyTokenEnum.TOKEN_EXCEPTION.getCode() || code == VerifyTokenEnum.TOKEN_VALID.getCode()) {
            return VerifyTokenResult.forFailInfo(token);
        }
        Map<String, String> paramMap;
        try {
            paramMap = parserToken(token);
        } catch (CommonException ce) {
            return VerifyTokenResult.forFailInfo(token);
        }
        String memberCardNo = paramMap.get(HandlerConstants.TOKEN_MEMBERCARDNO);
        // 上一次签名在token中的渠道号，此渠道号可能与这次header中的渠道号不同的原因是跨渠道访问
        String tokenChannelCode = paramMap.get(HandlerConstants.TOKEN_CHANNELCODE);
        // 从redis中查询原渠道token的签名信息，这么做是因为允许多夸渠道续签token
        // 例如WXAPP渠道和CHECKIN渠道可以互相跳转续签
        String queryKey = RedisKeyConfig.createTokenKey(tokenChannelCode, memberCardNo);
        String queryOldKey = RedisKeyConfig.createOldTokenKey(tokenChannelCode, memberCardNo);
        // 签名后的新key按当前渠道放入redis
        String maintainKey = RedisKeyConfig.createTokenKey(channelCode, memberCardNo);
        String maintainOldKey = RedisKeyConfig.createOldTokenKey(channelCode, memberCardNo);
        //新token
        String redisToken = tokenUtils.redisService.getData(queryKey);
        //旧token,保持短时间有效，用于平滑过渡
        String oldRedisToken = tokenUtils.redisService.getData(queryOldKey);
        if (StringUtil.isNullOrEmpty(redisToken) && StringUtil.isNullOrEmpty(oldRedisToken)) {
            //redis中不存在，表示用户信息失效，需重新登陆
            return VerifyTokenResult.forFailInfo(token);
        }
        //过期token只验证redis中是否存在
        if (token.equals(oldRedisToken)) {
            return VerifyTokenResult.forSuccessInfo(redisToken);
        } else if (token.equals(redisToken)) {
            if (code == VerifyTokenEnum.TOKEN_SUCCESS.getCode()) {
                //jwtToken认证通过，缓存时间续加
                tokenUtils.redisService.putData(maintainKey, token, getTokenExpiress());
            } else if (code == VerifyTokenEnum.TOKEN_EXPIRED.getCode()) {
                Map<String, String> newMap = new HashMap<>();
                paramMap.forEach((k, v) -> newMap.put(k, v));
                // 更新token中的渠道号
                newMap.put(HandlerConstants.TOKEN_CHANNELCODE, channelCode);
                // 微信小程序或者值机小程序夸系统时，将openId置空，因为原Token中的openId在新系统中不可用
                if ((ChannelCodeEnum.WXAPP.getChannelCode().equals(channelCode) || ChannelCodeEnum.CHECKIN.getChannelCode().equals(channelCode))
                        && !queryKey.equals(maintainKey)) {
                    newMap.remove(HandlerConstants.TOKEN_CHANNELCODE);
                }
                //redis中存在，生成新的token，缓存到redis中
                tokenUtils.redisService.putData(maintainOldKey, redisToken, REDIS_OLD_TOKEN_EXPIRE_TIME);
                redisToken = sign(newMap);
                tokenUtils.redisService.putData(maintainKey, redisToken, getTokenExpiress());
            }
        } else {
            return VerifyTokenResult.forFailInfo(token);
        }
        return VerifyTokenResult.forSuccessInfo(redisToken);
    }

    /**
     * 从token中获取指定参数
     * @param token
     * @param key
     * @return
     */
    public static String getString(String token, String key) {
        try {
            Map<String, String> map = parserToken(token);
            return map.get(key);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 退出登录
     */
    public static String logout(String token) {
        try {
            String channelCode = getString(token, HandlerConstants.TOKEN_CHANNELCODE);
            String memberCardNo = getString(token, HandlerConstants.TOKEN_MEMBERCARDNO);
            String key = RedisKeyConfig.createTokenKey(channelCode, memberCardNo);
            tokenUtils.redisService.removeData(key);
            return "ok";
        } catch (Exception e) {
            return "fail";
        }
    }

    /**
     * 根据卡号注销登录状态
     * @param ffpCardNo
     * @return
     */
    public static void logoutAll(String ffpCardNo){
        String key = "";
        for(ChannelCodeEnum channelCode:ChannelCodeEnum.values()){
            if(ChannelCodeEnum.MOBILE.getChannelCode().equals(channelCode.getChannelCode())){
                key = RedisKeyConfig.createMobileTokenKey(ffpCardNo);
            }else{
                key = RedisKeyConfig.createTokenKey(channelCode.getChannelCode(),ffpCardNo);
            }
            tokenUtils.redisService.removeData(key);
        }
    }

    /**
     * 根据用户id，会员卡号，设备id保存到生成token，并根据用户id和设备id保存到redis中
     *
     * @param mobileTokenUserInfo token
     * @return token
     */
    public static String mobileCreateTokenAndSaveRedis(String channelCode,MobileTokenUserInfo mobileTokenUserInfo) {
        //校验参数
        if (mobileTokenUserInfo == null) {
            return null;
        }
        //1.生成token
        Map<String, String> map = new HashMap<>();
        map.put(HandlerConstants.TOKEN_CHANNELCODE,channelCode);
        map.put(HandlerConstants.MOBILE_TOKEN_USER_INFO, JsonUtil.objectToJson(mobileTokenUserInfo));
        long mobileTokenExpireTimeMilli = TokenUtils.tokenUtils.handconfig.getMobileTokenExpireTimeMilli();
        String token = sign(map, mobileTokenExpireTimeMilli);
        if (StringUtils.isBlank(token)) {
            return null;
        }
        //2.保存到redis key：前缀+id，value：token
        String key = RedisKeyConfig.createMobileTokenKey(mobileTokenUserInfo.getMemberId()) ;
        long mobileRedisExpireTimeSeconds = TokenUtils.tokenUtils.handconfig.getMobileRedisExpireTimeSeconds();
        tokenUtils.redisService.putData(key, token, mobileRedisExpireTimeSeconds);
        return token;
    }

    /**
     * 移动端根据token进行校验
     *
     * @param requestToken 请求token
     * @param request
     */
    public static MobileVerifyTokenResp mobileVerifyTokenAndRenewal(String requestToken, HttpServletRequest request) {
        //1.对token进行校验
        int tokenStatus = verifyToken(requestToken);
        //1.1 token错误，拦截请求
        if (VerifyTokenEnum.TOKEN_SUCCESS.getCode() != tokenStatus && VerifyTokenEnum.TOKEN_EXPIRED.getCode() != tokenStatus) {
            return MobileVerifyTokenResp.error(WSEnum.INVALID_TOKEN);
        }
        //2.根据token到redis进行校验
        //获取参数
        Map<String, String> paramMap;
        try {
            paramMap = parserToken(requestToken);
        } catch (CommonException ce) {
            return MobileVerifyTokenResp.error(WSEnum.INVALID_TOKEN);
        }
        String str = paramMap.get(HandlerConstants.MOBILE_TOKEN_USER_INFO);
        MobileTokenUserInfo mobileTokenUserInfo = (MobileTokenUserInfo) JsonUtil.jsonToBean(str, MobileTokenUserInfo.class);
        //校验token中的用户id和请求体中的用户id是否相同
        if (!verifyUserId(mobileTokenUserInfo.getUserId(), request)) {
            return MobileVerifyTokenResp.error(WSEnum.INVALID_TOKEN.getResultCode(), "校验用户信息错误");
        }
        //校验会员账号是否被封禁
        MobileVerifyTokenResp verifyIsClosedResp = verifyIsClosed(String.valueOf(mobileTokenUserInfo.getUserId()), mobileTokenUserInfo.getMemberId()
                , request);
        if (verifyIsClosedResp.getCode().equals("fail")) {
            return verifyIsClosedResp;
        }

        String key = RedisKeyConfig.createMobileTokenKey(mobileTokenUserInfo.getMemberId()) ;
        String redisToken = tokenUtils.redisService.getData(key);
        //2.1 token对应redis不为空
        if (StringUtils.isNotBlank(redisToken)) {
            //2.1.1 token相同，校验成功，刷新redis缓存
            if (redisToken.equals(requestToken)) {
                long mobileRedisExpireTimeSeconds = TokenUtils.tokenUtils.handconfig.getMobileRedisExpireTimeSeconds();
                //更新redis缓存
                tokenUtils.redisService.expire(key, mobileRedisExpireTimeSeconds);
                return MobileVerifyTokenResp.success();
            }
            //2.1.2 token不同，校验失败，拦截请求 已在其他设备登录
            else {
                return MobileVerifyTokenResp.error(WSEnum.ACCOUNT_LOGIN_ON_OTHER_MOBILE);
            }
        }
        //2.2 token对应redis为空，查询数据库
        else {
            //2.2.0 判断token状态是否为过期的，如果过期就返回token失效错误
            if (VerifyTokenEnum.TOKEN_EXPIRED.getCode() == tokenStatus) {
                return MobileVerifyTokenResp.error(WSEnum.ACCOUNT_LOGIN_ON_OTHER_MOBILE);
            }

            //查询数据库，根据用户id
            String deviceId = "";
            if ("Y".equals(TokenUtils.tokenUtils.handconfig.getSaveDeviceInfoFlag())) {
                deviceId = tokenUtils.mobileLoginVerifyService.getDeviceIdByUserIdAndMemberId(mobileTokenUserInfo.getUserId(), mobileTokenUserInfo.getMemberId());
            }
            //2.2.1 设备id为空或者不同，校验失败，拦截请求 已在其他设备登录
            if (StringUtils.isNotBlank(deviceId) && StringUtils.isNotBlank(mobileTokenUserInfo.getDeviceId()) && !deviceId.equals(mobileTokenUserInfo.getDeviceId())) {
                return MobileVerifyTokenResp.error(WSEnum.ACCOUNT_LOGIN_ON_OTHER_MOBILE);
            }
            //2.2.2 设备id相同，将token保存到redis,返回成功
            else {
                long mobileRedisExpireTimeSeconds = TokenUtils.tokenUtils.handconfig.getMobileRedisExpireTimeSeconds();
                tokenUtils.redisService.putData(key, requestToken, mobileRedisExpireTimeSeconds);
                return MobileVerifyTokenResp.success();
            }
        }
    }

    /**
     * 根据request和userId判断请求体中token是否正确。
     * 如果不是json请求：true，如果没有ffpId：true，如果相同true，只有不相同才返回false。
     *
     * @param userId
     * @param request
     * @return false 错误,不相同
     */
    private static Boolean verifyUserId(Long userId, HttpServletRequest request) {
        if (!ReqUtil.isJsonReq(request)) {
            return true;
        }
        JsonParameterRequestWrapper requestWrapper = (JsonParameterRequestWrapper) request;
        String bodyMessage = requestWrapper.getBodyMessage();
        if (StringUtils.isBlank(bodyMessage)) {
            return true;
        }
        Map baseMap = JsonMapper.buildNormalMapper().fromJson(bodyMessage, Map.class);
        //判断请求体 外层的用户id
        Object ffpIdInOut = baseMap.get("ffpId");
        if (ffpIdInOut != null) {
            String ffpIdStr = String.valueOf(ffpIdInOut);
            Long ffpId = Long.valueOf(ffpIdStr);
            //如果用户id不相同,则报错
            if (!userId.equals(ffpId)) {
                return false;
            }
            //如果正常,则处理下面的逻辑
        }

        Object requestObj = baseMap.get("request");


        if (Objects.isNull(requestObj)) {
            //没有request代表没有用户信息，可以放行
            return true;
        }
        //判断类型是否为map,不是直接跳出,返回true
        if (!(requestObj instanceof Map)) {
            return true;
        }

        Map requestMap = (Map) requestObj;
        Object ffpIdObj = requestMap.get("ffpId");
        if (Objects.isNull(ffpIdObj)) {
            //没有用户id，可以放行
            return true;
        }
        Long ffpId = Long.valueOf(String.valueOf(ffpIdObj));
        //如果用户id不同则返回false，相同返回true
        return userId.equals(ffpId);
    }

    /**
     * 校验手机是否封号,根据request请求体内容
     */
    public static Boolean verifyIsClosedByRequest(HttpServletRequest request) {
        if (!ReqUtil.isJsonReq(request)) {
            return true;
        }
        if (!(request instanceof JsonParameterRequestWrapper)) {
            return true;
        }
        JsonParameterRequestWrapper requestWrapper = (JsonParameterRequestWrapper) request;
        String bodyMessage = requestWrapper.getBodyMessage();
        if (StringUtils.isBlank(bodyMessage)) {
            return true;
        }
        Map baseMap = JsonMapper.buildNormalMapper().fromJson(bodyMessage, Map.class);
        if (baseMap == null) {
            return true;
        }
        Object requestObj = baseMap.get("request");
        if (Objects.isNull(requestObj)) {
            //没有request代表没有用户信息，可以放行
            return true;
        }
        //判断类型是否为map,不是直接跳出,返回true
        if (!(requestObj instanceof Map)) {
            return true;
        }

        Map requestMap = (Map) requestObj;
        Object ffpIdObj = requestMap.get("ffpId");
        Object ffpCardNo = requestMap.get("ffpCardNo");
        if (Objects.isNull(ffpIdObj) || Objects.isNull(ffpCardNo)) {
            //没有用户id，或卡号id 可以放行
            return true;
        }
        String ffpIdStr = String.valueOf(ffpIdObj);
        String ffpCardNoStr = String.valueOf(ffpCardNo);

        MobileVerifyTokenResp mobileVerifyTokenResp = verifyIsClosed(ffpIdStr, ffpCardNoStr, request);
        //校验相同才可以使用
        return mobileVerifyTokenResp.getCode().equals("success");
    }

    /**
     * 校验会员信息,不使用缓存
     *
     * @param cardNo
     * @param ffpId
     * @param request
     * @return
     */
    public static MobileVerifyTokenResp verifyIsClosedNewest(String ffpId, String cardNo, HttpServletRequest request) {
        String key = RedisKeyConfig.createAccountStateKey(ffpId, cardNo);
        tokenUtils.redisService.removeData(key);
        return verifyIsClosed(ffpId, cardNo, request);
    }

    /**
     * 校验会员信息
     *
     * @param cardNo
     * @param ffpId
     * @param request
     * @return
     */
    public static MobileVerifyTokenResp verifyIsClosed(String ffpId, String cardNo, HttpServletRequest request) {
        if (StringUtil.isNullOrEmpty(ffpId) || StringUtil.isNullOrEmpty(cardNo)) {
            return MobileVerifyTokenResp.success();
        }
        //先查询缓存，如果缓存为空则调用远程接口
        String key = RedisKeyConfig.createAccountStateKey(ffpId, cardNo);
        String memberStatus = tokenUtils.redisService.getData(key);
        if (Objects.nonNull(memberStatus)) {
            if ("2".equals(memberStatus)) {
                return MobileVerifyTokenResp.success();
            } else {
                return MobileVerifyTokenResp.error(WSEnum.INVALID_TOKEN.getResultCode(), "该账号已封禁");
            }
        }
        //如果缓存中没有则查询远程接口
        String[] items = {MemberDetailRequestItemsEnum.STATEINFO.eName};
        PtApiCRMRequest<PtMemberDetailRequest> ptApiCRMRequest = CRMReqUtil.buildMemberDetailReq(cardNo, ffpId, request, ChannelCodeEnum.MOBILE.getChannelCode(), items);
        PtCRMResponse<PtMemberDetail> ptCRMResponse = tokenUtils.memberService.memberDetail(ptApiCRMRequest);
        if (ptCRMResponse.getCode() != 0) {
            return MobileVerifyTokenResp.error(WSEnum.SERVICE_BUSY_ERROR.getResultCode(), ptCRMResponse.getMsg());
        }
        MemberStateInfoSoaModel stateInfo = ptCRMResponse.getData().getStateInfo();

        Long randomTime = getNowToTomorrowOneClockSecond() + random.nextInt(60 * 60);
        tokenUtils.redisService.putData(key, String.valueOf(stateInfo.getIsClosed()), randomTime);
        if (stateInfo.getIsClosed() == 2) {
            return MobileVerifyTokenResp.success();
        } else {
            return MobileVerifyTokenResp.error(WSEnum.INVALID_TOKEN.getResultCode(), "该账号已封禁");
        }
    }

    //获取当前时间距离明天1点的时间
    private static Long getNowToTomorrowOneClockSecond() {
        Calendar instance = Calendar.getInstance();
        //明天的时间
        instance.add(Calendar.DAY_OF_MONTH, 1);
        //早上1点钟
        instance.set(Calendar.HOUR_OF_DAY, 1);
        instance.set(Calendar.MINUTE, 0);
        long tomorrow = instance.getTimeInMillis();
        return (tomorrow - System.currentTimeMillis()) / 1000;

    }

}

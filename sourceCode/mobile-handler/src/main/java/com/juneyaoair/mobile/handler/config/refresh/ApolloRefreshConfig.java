package com.juneyaoair.mobile.handler.config.refresh;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.model.ConfigChangeEvent;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfig;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfigChangeListener;
import com.google.gson.reflect.TypeToken;
import com.juneyaoair.baseclass.DCEPayment.common.DCEPayConfig;
import com.juneyaoair.baseclass.activity.ActivityConfig;
import com.juneyaoair.baseclass.activity.MaoTaiConfig;
import com.juneyaoair.baseclass.activity.SmsTemplate;
import com.juneyaoair.baseclass.antCloud.config.AntConfig;
import com.juneyaoair.baseclass.av.common.*;
import com.juneyaoair.baseclass.baggagestandard.common.BaggageStandardConfig;
import com.juneyaoair.baseclass.captcha.CaptchaSendIntervalConfig;
import com.juneyaoair.baseclass.common.BookCert;
import com.juneyaoair.baseclass.common.HeadTitleInfo;
import com.juneyaoair.baseclass.common.base.*;
import com.juneyaoair.baseclass.common.response.ImageResponse;
import com.juneyaoair.baseclass.common.response.ProModule;
import com.juneyaoair.baseclass.flight.common.FlightReminderV2;
import com.juneyaoair.baseclass.individualizationSetMeal.response.MealImage;
import com.juneyaoair.baseclass.insurance.apollo.InsuranceConfig;
import com.juneyaoair.baseclass.member.comm.AssociateMemberInfo;
import com.juneyaoair.baseclass.member.comm.CoBrandedCardInfo;
import com.juneyaoair.baseclass.member.comm.LogoutPageInfoConfig;
import com.juneyaoair.baseclass.member.comm.VerifyChannel;
import com.juneyaoair.baseclass.member.response.rightsDescriptionResponse;
import com.juneyaoair.baseclass.request.order.refund.query.RefundType;
import com.juneyaoair.baseclass.reservation.ReservationConfig;
import com.juneyaoair.baseclass.response.av.BrandRightDto;
import com.juneyaoair.baseclass.response.av.FlightQueryTypeDto;
import com.juneyaoair.baseclass.response.av.MultipleFlightTransferConfig;
import com.juneyaoair.baseclass.response.av.ThemeFlightDetails;
import com.juneyaoair.baseclass.response.console.ThemeSwitchDate;
import com.juneyaoair.baseclass.response.coupons.CouponDefCity;
import com.juneyaoair.baseclass.response.newCheckinSeat.CheckInPolicyConfig;
import com.juneyaoair.baseclass.response.payment.PayMethod;
import com.juneyaoair.baseclass.response.payment.paysetting.Bank;
import com.juneyaoair.baseclass.response.prepaymentBaggage.PrepaymentBaggageDocumentResp;
import com.juneyaoair.baseclass.response.taolx.ParentRegion;
import com.juneyaoair.baseclass.response.taolx.TourDest;
import com.juneyaoair.baseclass.response.taolx.VersionMember;
import com.juneyaoair.baseclass.response.wallet.WalletTextVo;
import com.juneyaoair.baseclass.risk.FraudEscapeConfig;
import com.juneyaoair.baseclass.studentVerify.common.StudentVerifyConfig;
import com.juneyaoair.baseclass.tencentcloud.common.TencentCloudAccount;
import com.juneyaoair.baseclass.transferaccommodation.common.TransferAccommodationConfig;
import com.juneyaoair.baseclass.unlimit.UnlimitedCard2Config;
import com.juneyaoair.baseclass.unlimit.UpgradeCardV2Config;
import com.juneyaoair.mobile.handler.config.JjscConfig;
import com.juneyaoair.mobile.handler.config.bean.HandConfig;
import com.juneyaoair.mobile.handler.config.bean.TempleteConfig;
import com.juneyaoair.mobile.handler.service.IProductCacheService;
import com.juneyaoair.mobile.limiter.RateLimiterInfo;
import com.juneyaoair.thirdentity.passengers.common.CertType;
import com.juneyaoair.utils.StringUtil;
import com.juneyaoair.utils.json.JsonUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.lang.reflect.Type;
import java.util.*;

/**
 * Created by yaocf on 2018/4/17  18:29.
 */
@Component
public class ApolloRefreshConfig {
    private static final Logger logger = LoggerFactory.getLogger(ApolloRefreshConfig.class);
    @ApolloConfig
    private Config config;
    @ApolloConfig("hand.templete")
    private Config tconfig;
    @Autowired
    private HandConfig handConfig;
    @Autowired
    private TempleteConfig templeteConfig;
    @Autowired
    private IProductCacheService productCacheService;

    @ApolloConfigChangeListener
    private void onChange(ConfigChangeEvent changeEvent) {
        logger.info("HandConfig更新之前的配置：{}", handConfig.toString());
        if (changeEvent.isChanged("support_airPort_QR")) {
            handConfig.setSupport_airPort_QR(config.getProperty("support_airPort_QR", ""));
        }
        if (changeEvent.isChanged("aircraftModel")) {
            handConfig.setAircraftModel(config.getProperty("aircraftModel", ""));
        }
        if (changeEvent.isChanged("airCompany")) {
            handConfig.setAirCompany(config.getProperty("airCompany", ""));
        }
        if (changeEvent.isChanged("visaMaterial")) {
            handConfig.setVisaMaterial(config.getProperty("visaMaterial", ""));
        }
        if (changeEvent.isChanged("overWrite")) {
            handConfig.setOverWrite(config.getProperty("overWrite", ""));
        }
        if (changeEvent.isChanged("bankNameList")) {
            handConfig.setBankNameList(config.getProperty("bankNameList", "中国银行"));
        }
        if (changeEvent.isChanged("authPhotoCheckDevice")) {
            handConfig.setAuthPhotoCheckDevice(config.getProperty("authPhotoCheckDevice", "Y"));
        }
        if (changeEvent.isChanged("accessDiffLimit")) {
            handConfig.setAccessDiffLimit(Integer.parseInt(config.getProperty("accessDiffLimit", "180")));
        }
        if (changeEvent.isChanged("timeLimit")) {
            handConfig.setTimeLimit(Integer.parseInt(config.getProperty("timeLimit", "20")));
        }
        if (changeEvent.isChanged("cabinClass")) {
            handConfig.setCabinClass(config.getProperty("cabinClass", ""));
        }
        if (changeEvent.isChanged("connectTimeout")) {
            handConfig.setConnectTimeout(Integer.parseInt(config.getProperty("connectTimeout", "30000")));
        }
        if (changeEvent.isChanged("readTimeout")) {
            handConfig.setReadTimeout(Integer.parseInt(config.getProperty("readTimeout", "80000")));
        }
        if (changeEvent.isChanged("avReadRedis")) {
            handConfig.setAvReadRedis(config.getProperty("avReadRedis", "Y"));
        }
        if (changeEvent.isChanged("useWaitingLimit")) {
            handConfig.setUseWaitingLimit(config.getProperty("useWaitingLimit", "Y"));
        }
        if (changeEvent.isChanged("useGeetestLogin")) {
            handConfig.setUseGeetestLogin(config.getProperty("useGeetestLogin", "Y"));
        }
        if (changeEvent.isChanged("useGeetesOnline")) {
            handConfig.setUseGeetesOnline(config.getProperty("useGeetesOnline", "Y"));
        }
        if (changeEvent.isChanged("byPassStatusUrl")) {
            handConfig.setByPassStatusUrl(config.getProperty("byPassStatusUrl", ""));
        }
        if (changeEvent.isChanged("useTongDun")) {
            handConfig.setUseTongDun(config.getProperty("useTongDun", "Y"));
        }
        if (changeEvent.isChanged("wxSmallAppId")) {
            handConfig.setWxSmallAppId(config.getProperty("wxSmallAppId", "wx534a6f359f266888"));
        }
        if (changeEvent.isChanged("wxSmallSecret")) {
            handConfig.setWxSmallSecret(config.getProperty("wxSmallSecret", "9524accccad6985f67163c97a2f5be11"));
        }
        if (changeEvent.isChanged("appId")) {
            handConfig.setAppId(config.getProperty("appId", "b8025e81-95db-48fd-8e7a-ad2c73136b4e"));
        }
        if (changeEvent.isChanged("ciSmallAppId")) {
            handConfig.setCiSmallAppId(config.getProperty("ciSmallAppId", "wx534a6f359f266888"));
        }
        if (changeEvent.isChanged("ciSmallSecret")) {
            handConfig.setCiSmallSecret(config.getProperty("ciSmallSecret", "be602d6a74bd1a3a75e5c7cf298a20ae"));
        }
        if (changeEvent.isChanged("wxPublicAppId")) {
            handConfig.setWxPublicAppId(config.getProperty("wxPublicAppId", "wx42f49ced634e312b"));
        }
        if (changeEvent.isChanged("wxPublicSecret")) {
            handConfig.setWxPublicSecret(config.getProperty("wxPublicSecret", "7830f9555363c2e338f893048c008448"));
        }
        if (changeEvent.isChanged("ycabinLimit")) {
            handConfig.setYcabinLimit(Integer.parseInt(config.getProperty("ycabinLimit", "5")));
        }
        if (changeEvent.isChanged("jcabinLimit")) {
            handConfig.setJcabinLimit(Integer.parseInt(config.getProperty("jcabinLimit", "2")));
        }
        if (changeEvent.isChanged("cabinSequence")) {
            handConfig.setCabinSequence(config.getProperty("cabinSequence", "J,Y"));
        }
        if (changeEvent.isChanged("restLogExclude")) {
            handConfig.setRestLogExclude(config.getProperty("restLogExclude", "orderServiceOrderBooking"));
        }
        if (changeEvent.isChanged("supportUpCouponCabin")) {
            handConfig.setSupportUpCouponCabin(config.getProperty("supportUpCouponCabin", "A,R"));
        }
        if (changeEvent.isChanged("supportUpCouponCabinI")) {
            handConfig.setSupportUpCouponCabinI(config.getProperty("supportUpCouponCabinI", "R"));
        }
        if (changeEvent.isChanged("tokenExpiress")) {
            handConfig.setTokenExpiress(Long.valueOf(config.getProperty("tokenExpiress", "1296000")));
        }
        if (changeEvent.isChanged("mobileRedisExpireTimeSeconds")) {
            handConfig.setMobileRedisExpireTimeSeconds(Long.valueOf(config.getProperty("mobileRedisExpireTimeSeconds", "86400")));
        }
        if (changeEvent.isChanged("mobileTokenExpireTimeMilli")) {
            handConfig.setMobileTokenExpireTimeMilli(Long.valueOf(config.getProperty("mobileTokenExpireTimeMilli", "1296000000")));
        }
        if (changeEvent.isChanged("memberStarRuleId")) {
            handConfig.setAliPayArgument(config.getProperty("memberStarRuleId", ""));
        }
        if (changeEvent.isChanged("aliPayArgument")) {
            handConfig.setAliPayArgument(config.getProperty("aliPayArgument", ""));
        }
        if (changeEvent.isChanged("cussFlightChangeSecret")) {
            handConfig.setCussFlightChangeSecret(config.getProperty("cussFlightChangeSecret", "profile/test"));
        }
        if (changeEvent.isChanged("authPhotoLimit")) {
            try {
                String s = config.getProperty("authPhotoLimit", "");
                AccessLimit accessLimit = (AccessLimit) JsonUtil.jsonToBean(s, AccessLimit.class);
                if (accessLimit != null) {
                    handConfig.setAuthPhotoLimit(accessLimit);
                }
            } catch (Exception e) {
                logger.error("authPhotoLimit config update error!", e);
            }
        }
        if (changeEvent.isChanged("bookPersonCert")) {
            try {
                String s = config.getProperty("bookPersonCert", "");
                BookCert bookCert = (BookCert) JsonUtil.jsonToBean(s, BookCert.class);
                if (bookCert != null) {
                    handConfig.setBookPersonCert(bookCert);
                }
            } catch (Exception e) {
                logger.error("bookCert config update error!", e);
            }
        }

        if (changeEvent.isChanged("transferProcessConfig")) {
            try {
                String s = config.getProperty("transferProcessConfig", "");
                TransferProcess transferProcess = (TransferProcess) JsonUtil.jsonToBean(s, TransferProcess.class);
                if (transferProcess != null) {
                    handConfig.setTransferProcessConfig(transferProcess);
                }
            } catch (Exception e) {
                logger.error("transferProcessConfig config update error!", e);
            }
        }

        if (changeEvent.isChanged("wifiLimitDay")) {
            handConfig.setWifiLimitDay(Integer.parseInt(config.getProperty("wifiLimitDay", "10")));
        }
        if (changeEvent.isChanged("supportVirtualPayment")) {
            handConfig.setSupportVirtualPayment(config.getProperty("supportVirtualPayment", "N"));
        }
        if (changeEvent.isChanged("hotDest")) {
            try {
                String s = config.getProperty("hotDest", "");
                Type type = new TypeToken<ProModule<TourDest>>() {
                }.getType();
                ProModule<TourDest> proModule = (ProModule<TourDest>) JsonUtil.jsonToBean(s, type);
                if (proModule != null) {
                    handConfig.setHotDest(proModule);
                }
            } catch (Exception e) {
                logger.error("hotDest config update error!", e);
            }
        }
        if (changeEvent.isChanged("hotRegion")) {
            try {
                String s = config.getProperty("hotRegion", "");
                ParentRegion parentRegion = (ParentRegion) JsonUtil.jsonToBean(s, ParentRegion.class);
                if (parentRegion != null) {
                    handConfig.setHotRegion(parentRegion);
                }
            } catch (Exception e) {
                logger.error("hotRegion config update error!", e);
            }
        }
        if (changeEvent.isChanged("tripTicketIpLimit")) {
            try {
                String s = config.getProperty("tripTicketIpLimit", "{\"limitDay\":{\"frequency\":86400,\"accessLimit\":30},\"limitForever\":{\"frequency\":0,\"accessLimit\":60},\"limitMin\":{\"frequency\":180,\"accessLimit\":10}}");
                LimitCount limitCount = (LimitCount) JsonUtil.jsonToBean(s, LimitCount.class);
                if (limitCount != null) {
                    handConfig.setTripTicketIpLimit(limitCount);
                }
            } catch (Exception e) {
                logger.error("hotRegion config update error!", e);
            }
        }
        if (changeEvent.isChanged("insureQueryRules")) {
            handConfig.setInsureQueryRules(config.getProperty("insureQueryRules", "[{\"tripTypes\":[\"D\"],\"excludeFareTypes\":[\"Addon\"],\"insureList\":[{\"InsId\":\"*********\",\"InsNm\":\"国内航班延误险\",\"InsCd\":\"5000001\",\"InsDesc\":\"延误3小时赔付600元\",\"InsDescURL\":\"/clause/service_insure6.html\",\"InsAmt\":600,\"InsTax\":30,\"IsAdt\":\"Y\",\"IsChd\":\"Y\",\"IsInf\":\"N\",\"IsAdtMust\":\"N\",\"IsChdMust\":\"N\",\"IsInfMust\":\"N\",\"PriceType\":\"ONE\",\"IsDefault\":\"N\",\"IsSingleBuy\":\"N\",\"IsAvailable\":\"Y\"},{\"InsId\":\"*********\",\"InsNm\":\"国内航空综合险\",\"InsCd\":\"7000001\",\"InsDesc\":\"含航意、延误取消、托运行李、证件遗失等多重保障\",\"InsDescURL\":\"/clause/service_insure4.html\",\"InsAmt\":3103600,\"InsTax\":30,\"IsAdt\":\"Y\",\"IsChd\":\"Y\",\"IsInf\":\"N\",\"IsAdtMust\":\"N\",\"IsChdMust\":\"N\",\"IsInfMust\":\"N\",\"PriceType\":\"ONE\",\"IsDefault\":\"N\",\"IsSingleBuy\":\"N\",\"IsAvailable\":\"Y\"}]},{\"flightTypes\":[\"OW\"],\"tripTypes\":[\"I\"],\"excludeFareTypes\":[\"Addon\"],\"insureList\":[{\"InsId\":\"*********\",\"InsNm\":\"国际航班延误险\",\"InsCd\":\"5000000\",\"InsDesc\":\"延误3小时赔付600元\",\"InsDescURL\":\"/clause/service_insure2.html\",\"InsAmt\":600,\"InsTax\":35,\"IsAdt\":\"Y\",\"IsChd\":\"Y\",\"IsInf\":\"N\",\"IsAdtMust\":\"N\",\"IsChdMust\":\"N\",\"IsInfMust\":\"N\",\"PriceType\":\"ONE\",\"IsDefault\":\"N\",\"IsSingleBuy\":\"N\",\"IsAvailable\":\"Y\"},{\"InsId\":\"*********\",\"InsNm\":\"国际退票险\",\"InsCd\":\"6000000\",\"InsDesc\":\"国际退票险\",\"InsDescURL\":\"/clause/service_insure2.html\",\"InsAmt\":1500,\"InsTax\":40,\"IsAdt\":\"Y\",\"IsChd\":\"Y\",\"IsInf\":\"N\",\"IsAdtMust\":\"N\",\"IsChdMust\":\"N\",\"IsInfMust\":\"N\",\"PriceType\":\"ONE\",\"IsDefault\":\"N\",\"IsSingleBuy\":\"N\",\"IsAvailable\":\"Y\"}]}]"));
        }
        if (changeEvent.isChanged("baggageStandardConfigList")) {
            try {
                String s = config.getProperty("baggageStandardConfigList", "");
                List<BaggageStandardConfig> baggageStandardConfigList = (List<BaggageStandardConfig>) JsonUtil.jsonToBean(s, new TypeToken<List<BaggageStandardConfig>>() {
                }.getType());
                handConfig.setBaggageStandardConfigList(baggageStandardConfigList);
            } catch (Exception e) {
                logger.error("baggageStandardConfigList config update error!", e);
            }
        }

        if (changeEvent.isChanged("brandRightDtoList")) {
            try {
                String s = config.getProperty("brandRightDtoList", "");
                List<BrandRightDto> brandRightDtoList = (List<BrandRightDto>) JsonUtil.jsonToBean(s, new TypeToken<List<BrandRightDto>>() {
                }.getType());
                handConfig.setBrandRightDtoList(brandRightDtoList);
            } catch (Exception e) {
                logger.error("brandRightDtoList config update error!", e);
            }
        }


        if (changeEvent.isChanged("walletTextList")) {
            try {
                String s = config.getProperty("walletTextList", "");
                List<WalletTextVo> walletTextVos = (List<WalletTextVo>) JsonUtil.jsonToBean(s, new TypeToken<List<WalletTextVo>>() {
                }.getType());
                handConfig.setWalletTextList(walletTextVos);
            } catch (Exception e) {
                logger.error("baggageStandardConfigList config update error!", e);
            }
        }

        if (changeEvent.isChanged("vipLoungeList")) {
            try {
                String s = config.getProperty("vipLoungeList", "");
                List<VipLounges> vipLounges = (List<VipLounges>) JsonUtil.jsonToBean(s, new TypeToken<List<VipLounges>>() {
                }.getType());
                handConfig.setVipLoungeList(vipLounges);
            } catch (Exception e) {
                logger.error("vipLoungeList config update error!", e);
            }
        }
        if (changeEvent.isChanged("airportCodeList")) {
            try {
                String s = config.getProperty("airportCodeLists", "");
                List<AirportAll> airportCodeLists = (List<AirportAll>) JsonUtil.jsonToBean(s, new TypeToken<List<AirportAll>>() {
                }.getType());
                handConfig.setAirportCodeList(airportCodeLists);
            } catch (Exception e) {
                logger.error("airportCodeLists config update error!", e);
            }
        }
        if (changeEvent.isChanged("mealImageList")) {
            try {
                String s = config.getProperty("mealImageList", "");
                List<MealImage> mealImageList = (List<MealImage>) JsonUtil.jsonToBean(s, new TypeToken<List<MealImage>>() {
                }.getType());
                handConfig.setMealImageList(mealImageList);
            } catch (Exception e) {
                logger.error("mealImageList config update error!", e);
            }
        }
        if (changeEvent.isChanged("packagePro")) {
            handConfig.setPackagePro(config.getProperty("packagePro", "{\"A1\":{\"proCode\":\"A1\",\"proDesc\":\"100机票优惠券\"},\"A2\":{\"proCode\":\"A2\",\"proDesc\":\"50M机上WIFI\"}}"));
        }
        if (changeEvent.isChanged("additionRuleId")) {
            handConfig.setAdditionRuleId(config.getProperty("additionRuleId", "2"));
        }
        if (changeEvent.isChanged("refundMessage")) {
            handConfig.setRefundMessage(config.getProperty("refundMessage", "2"));
        }
        if (changeEvent.isChanged("notAllowedChangeCabin")) {
            handConfig.setNotAllowedChangeCabin(config.getProperty("notAllowedChangeCabin", "I,N,X,G"));
        }
        if (changeEvent.isChanged("saleWifiCabin")) {
            handConfig.setSaleWifiCabin(config.getProperty("saleWifiCabin", "N"));
        }
        if (changeEvent.isChanged("mwebOnlineVer")) {
            handConfig.setMwebOnlineVer(config.getProperty("mwebOnlineVer", "14000"));
        }
        if (changeEvent.isChanged("miniOnlineVer")) {
            handConfig.setMiniOnlineVer(config.getProperty("miniOnlineVer", "11000"));
        }
        if (changeEvent.isChanged("payConfigsV2")) {
            try {
                Type type = new TypeToken<Map<String, List<Bank>>>() {
                }.getType();
                handConfig.setPayConfigsV2(JsonUtil.fromJson(config.getProperty("payConfigsV2", ""), type));
            } catch (Exception e) {
                logger.error("payConfigsV2更新失败");
            }
        }
        if (changeEvent.isChanged("payJnConfigsV2")) {
            try {
                Type type = new TypeToken<Map<String, List<Bank>>>() {
                }.getType();
                handConfig.setPayJnConfigsV2(JsonUtil.fromJson(config.getProperty("payJnConfigsV2", ""), type));
            } catch (Exception e) {
                logger.error("payJnConfigsV2更新失败");
            }
        }
        if (changeEvent.isChanged("labelInfoMap")) {
            try {
                Type type = new TypeToken<Map<String, List<LabelInfo>>>() {
                }.getType();
                handConfig.setLabelInfoMap(JsonUtil.fromJson(config.getProperty("labelInfoMap", "{}"), type));
            } catch (Exception e) {
                logger.error("labelInfoMap更新失败");
            }
        }
        if (changeEvent.isChanged("intercontinentalCities")) {
            handConfig.setIntercontinentalCities(config.getProperty("intercontinentalCities", "HEL"));
        }
        if (changeEvent.isChanged("roundTripCouponCode")) {
            handConfig.setRoundTripCouponCode(config.getProperty("roundTripCouponCode", ""));
        }
        if (changeEvent.isChanged("showAvlog")) {
            handConfig.setShowAvlog(config.getProperty("showAvlog", "N"));
        }
        if (changeEvent.isChanged("scoreFreeLimit")) {
            handConfig.setScoreFreeLimit(config.getIntProperty("scoreFreeLimit", 20));
        }
        if (changeEvent.isChanged("huaRuiPublicKey")) {
            handConfig.setHuaRuiPublicKey(config.getProperty("huaRuiPublicKey", "04BB34D657EE7E8490E66EF577E6B3CEA28B739511E787FB4F71B7F38F241D87F18A5A93DF74E90FF94F4EB907F271A36B295B851F971DA5418F4915E2C1A23D6E"));
        }
        if (changeEvent.isChanged("huaRuiUrl")) {
            handConfig.setHuaRuiUrl(config.getProperty("huaRuiUrl", ""));
        }
        if (changeEvent.isChanged("channelId")) {
            handConfig.setChannelId(config.getProperty("channelId", "jixiang"));
        }
        if (changeEvent.isChanged("labelInfoConfig")) {
            try {
                String s = config.getProperty("labelInfoConfig", "{\"commonLabelInfos\":[{\"labelName\":\"手提+托运行李\",\"labelUrl\":\"\",\"labelType\":\"normal\",\"labelNum\":1,\"pictureUrl\":\"https://mediaws.juneyaoair.com/upload/icon/cabin/<EMAIL>\"},{\"labelName\":\"含餐\",\"labelUrl\":\"\",\"labelType\":\"normal\",\"labelNum\":2,\"pictureUrl\":\"https://mediaws.juneyaoair.com/upload/icon/cabin/<EMAIL>\"},{\"labelName\":\"快速出票\",\"labelUrl\":\"\",\"labelType\":\"normal\",\"labelNum\":3,\"pictureUrl\":\"https://mediaws.juneyaoair.com/upload/icon/cabin/<EMAIL>\"}],\"businessClassLabelInfoList\":[{\"labelName\":\"额外行李额\",\"labelUrl\":\"\",\"labelType\":\"normal\",\"labelNum\":1,\"pictureUrl\":\"https://mediaws.juneyaoair.com/upload/icon/cabin/<EMAIL>\"},{\"labelName\":\"优先登机\",\"labelUrl\":\"\",\"labelType\":\"normal\",\"labelNum\":2,\"pictureUrl\":\"https://mediaws.juneyaoair.com/upload/icon/cabin/<EMAIL>\"},{\"labelName\":\"宽敞座椅\",\"labelUrl\":\"\",\"labelType\":\"normal\",\"labelNum\":3,\"pictureUrl\":\"https://mediaws.juneyaoair.com/upload/icon/cabin/<EMAIL>\"},{\"labelName\":\"精美餐食\",\"labelUrl\":\"\",\"labelType\":\"normal\",\"labelNum\":4,\"pictureUrl\":\"https://mediaws.juneyaoair.com/upload/icon/cabin/<EMAIL>\"}],\"brandLabelInfos\":[{\"labelName\":\"无免费行李额\",\"labelUrl\":\"\",\"labelType\":\"normal\",\"labelNum\":1,\"pictureUrl\":\"https://mediaws.juneyaoair.com/upload/icon/cabin/<EMAIL>\"},{\"labelName\":\"不支持退改\",\"labelUrl\":\"\",\"labelType\":\"normal\",\"labelNum\":2,\"pictureUrl\":\"https://mediaws.juneyaoair.com/upload/icon/cabin/<EMAIL>\"},{\"labelName\":\"无积分\",\"labelUrl\":\"\",\"labelType\":\"normal\",\"labelNum\":3,\"pictureUrl\":\"https://mediaws.juneyaoair.com/upload/icon/cabin/<EMAIL>\"}],\"brandLabel\":{\"labelName\":\"吉祥品牌运价\",\"labelUrl\":\"\",\"labelType\":\"normal\",\"labelNum\":0,\"pictureUrl\":\"https://mediaws.juneyaoair.com/upload/icon/cabin/<EMAIL>\"}}");
                LabelInfoConfig labelInfoConfig = JsonUtil.fromJson(s, LabelInfoConfig.class);
                handConfig.setLabelInfoConfig(labelInfoConfig);
            } catch (Exception e) {
                logger.error("labelInfoConfig config update error!", e);
            }
        }
        if (changeEvent.isChanged("brandRouteCabinConfig")) {
            try {
                String s = config.getProperty("brandRouteCabinConfig", "{\"brandPriceCabins\":[\"K\"],\"internationalRoute\":{\"FI\":[],\"TH\":[\"SHABKK\"]}}");
                BrandRouteCabinConfig brandRouteCabinConfig = JsonUtil.fromJson(s, BrandRouteCabinConfig.class);
                handConfig.setBrandRouteCabinConfig(brandRouteCabinConfig);
            } catch (Exception e) {
                logger.error("brandRouteCabinConfig config update error!", e);
            }
        }
        if (changeEvent.isChanged("transferAccommodationConfigs")) {
            try {
                String s = config.getProperty("transferAccommodationConfigs", "[{\"midCityCode\":\"SHA\",\"arrCityCode\":\"HEL\", \"minDurHour\":6}]");
                List<TransferAccommodationConfig> transferAccommodationConfigs = JsonUtil.fromJson(s, new TypeToken<List<TransferAccommodationConfig>>() {
                }.getType());
                handConfig.setTransferAccommodationConfigs(transferAccommodationConfigs);
            } catch (Exception e) {
                logger.error("transferAccommodationConfigs config update error!", e);
            }
        }
        if (changeEvent.isChanged("payMethod")) {
            try {
                String s = config.getProperty("payMethod", "");
                List<PayMethod> payMethods = JsonUtil.fromJson(s, new TypeToken<List<PayMethod>>() {
                }.getType());
                handConfig.setPayMethod(payMethods);
            } catch (Exception e) {
                logger.error("payMethod config update error!", e);
            }
        }

        if (changeEvent.isChanged("brandPriceRule")) {
            handConfig.setBrandPriceRule(config.getProperty("brandPriceRule", "{\"saleChannel\":{\"title\":\"销售渠道\",\"description\":\"APP/官网/客服中心等吉祥航空直销渠道，以及携程OTA平台；\"},\"applicableRoute\":{\"title\":\"适用航线\",\"description\":\"目前仅适用上海到泰国的所有航线，包括上海\\u003d清迈/上海\\u003d曼谷/上海\\u003d普吉岛（单程及往返均适用）；\"},\"applicableCabin\":{\"title\":\"适用舱位\",\"description\":\"经济舱{cabin}舱；\"},\"freeBaggage\":{\"title\":\"免费行李额\",\"description\":\"无，即0PC；\"},\"refundRule\":{\"title\":\"退改规则\",\"description\":\"客票一旦出票，不支持退改签。非自愿情况，按正常客票非自愿退改规则处理；\"},\"memberBenefits\":{\"title\":\"会员权益\",\"description\":\"积分/航段累计规则不变，即累计1次定级航段，无积分。\"}}"));
        }
        if (changeEvent.isChanged("billAuthIp")) {
            handConfig.setBillAuthIp(config.getProperty("billAuthIp", ""));
        }
        if (changeEvent.isChanged("supportVerifyChannel")) {
            try {
                String s = config.getProperty("supportVerifyChannel", "");
                if (!StringUtil.isNullOrEmpty(s)) {
                    List<VerifyChannel> verifyChannelList = (List<VerifyChannel>) JsonUtil.jsonToBean(s, new TypeToken<List<VerifyChannel>>() {
                    }.getType());
                    handConfig.setSupportVerifyChannel(verifyChannelList);
                } else {
                    handConfig.setSupportVerifyChannel(new ArrayList<>());
                }
            } catch (Exception e) {
                logger.error("supportVerifyChannel config update error! error info is ", e);
            }
        }

        if (changeEvent.isChanged("supportLogoutVerifyChannel")) {
            try {
                String s = config.getProperty("supportLogoutVerifyChannel", "");
                if (!StringUtil.isNullOrEmpty(s)) {
                    List<VerifyChannel> verifyChannelList = (List<VerifyChannel>) JsonUtil.jsonToBean(s, new TypeToken<List<VerifyChannel>>() {
                    }.getType());
                    handConfig.setSupportVerifyChannel(verifyChannelList);
                } else {
                    handConfig.setSupportVerifyChannel(new ArrayList<>());
                }
            } catch (Exception e) {
                logger.error("supportLogoutVerifyChannel config update error! error info is ", e);
            }
        }
        if (changeEvent.isChanged("antConfig")) {
            try {
                String str = config.getProperty("antConfig", "{\"baseUrl\":\"https://prodapigw.cloud.alipay.com\",\"key\":\"LTAI4FmVAMWUoD2wXW8qvdzt\",\"secret\":\"******************************\"}");
                if (!StringUtil.isNullOrEmpty(str)) {
                    handConfig.setAntConfig(JsonUtil.fromJson(str, AntConfig.class));
                }
            } catch (Exception e) {
                logger.error("antConfig config update error! error info is ", e);
            }
        }
        if (changeEvent.isChanged("usingChannels")) {
            handConfig.setUsingChannels(config.getProperty("usingChannels", ""));
        }
        if (changeEvent.isChanged("applyRange")) {
            handConfig.setApplyRange(config.getProperty("applyRange", ""));
        }
        if (changeEvent.isChanged("useProxy")) {
            handConfig.setUseProxy(config.getProperty("useProxy", "N"));
        }
        if (changeEvent.isChanged("useDefaultIcon")) {
            handConfig.setUseDefaultIcon(config.getProperty("useDefaultIcon", "N"));
        }
        if (changeEvent.isChanged("upCouponDefCity")) {
            try {
                String s = config.getProperty("upCouponDefCity", "{arrCityCode:PEK,arrCity:北京,depCity:上海,depCityCode:SHA}");
                CouponDefCity couponDefCity = JsonUtil.fromJson(s, CouponDefCity.class);
                handConfig.setUpCouponDefCity(couponDefCity);
            } catch (Exception e) {
                logger.error("upCouponDefCity config update error!", e);
            }
        }
        if (changeEvent.isChanged("changeCouponDefCity")) {
            try {
                String s = config.getProperty("changeCouponDefCity", "{arrCityCode:PEK,arrCity:北京,depCity:上海,depCityCode:SHA}");
                CouponDefCity couponDefCity = JsonUtil.fromJson(s, CouponDefCity.class);
                handConfig.setUpCouponDefCity(couponDefCity);
            } catch (Exception e) {
                logger.error("changeCouponDefCity config update error!", e);
            }
        }
        if (changeEvent.isChanged("tencentCloudAccount")) {
            try {
                String s = config.getProperty("tencentCloudAccount", "{secretId:AKIDxlR1Rh5eMrGVZS5iGermWHsIWYP5at0x,secretKey:xlNp5A3nNn13ub7RZXLwLdljZDh195uZ}");
                TencentCloudAccount tencentCloudAccount = JsonUtil.fromJson(s, TencentCloudAccount.class);
                handConfig.setTencentCloudAccount(tencentCloudAccount);
            } catch (Exception e) {
                logger.error("tencentCloudAccount config update error!", e);
            }
        }
        if (changeEvent.isChanged("tencentCloudAppId")) {
            handConfig.setTencentCloudAppId(config.getProperty("tencentCloudAppId", "TIDAlGRU"));
        }

        if (changeEvent.isChanged("licenseMap")) {
            try {
                String s = config.getProperty("licenseMap", "");
                if (!StringUtil.isNullOrEmpty(s)) {
                    Map<String, String> licenseMap = JsonUtil.fromJson(s, new TypeToken<Map<String, String>>() {
                    }.getType());
                    handConfig.setLicenseMap(licenseMap);
                } else {
                    handConfig.setLicenseMap(new HashMap<>());
                }
            } catch (Exception e) {
                logger.error("httpLogConfig config update error!", e);
            }
        }
        if (changeEvent.isChanged("avSecret")) {
            handConfig.setAvSecret(config.getProperty("applyRange", "X8ofQWCp5xJd&2e5l@*60M%2rTEI$vtW"));
        }
        if (changeEvent.isChanged("validRang")) {
            handConfig.setValidRang(config.getProperty("validRang", ""));
        }
        if (changeEvent.isChanged("closedCheckInTips")) {
            handConfig.setClosedCheckInTips(config.getProperty("closedCheckInTips", "航班暂未开放值机选座，请您在航班起飞前90分钟至人工柜台办理"));
        }
        if (changeEvent.isChanged("gateUpgradeSeatMapCfg")) {
            handConfig.setGateUpgradeSeatMapCfg(config.getProperty("gateUpgradeSeatMapCfg", "{}"));
        }
        if (changeEvent.isChanged("changeOrderRefundRule")) {
            handConfig.setChangeOrderRefundRule(config.getProperty("changeOrderRefundRule", ""));
        }
        if (changeEvent.isChanged("updateOrderRefundRule")) {
            handConfig.setUpdateOrderRefundRule(config.getProperty("updateOrderRefundRule", ""));
        }
        if (changeEvent.isChanged("cancelOrderReasonConfig")) {
            changeApolloObjectConfig("cancelOrderReasonConfig", config.getProperty("cancelOrderReasonConfig", ""), Map.class);
        }
        if (changeEvent.isChanged("refundTypes")) {
            try {
                String s = config.getProperty("refundTypes", "");
                if (!StringUtil.isNullOrEmpty(s)) {
                    List<RefundType> refundTypes = (List<RefundType>) JsonUtil.jsonToBean(s, new TypeToken<List<RefundType>>() {
                    }.getType());
                    handConfig.setRefundTypes(refundTypes);
                } else {
                    handConfig.setRefundTypes(new ArrayList<>());
                }
            } catch (Exception e) {
                logger.error("refundTypes config update error! error info is ", e);
            }
        }
        if (changeEvent.isChanged("youthFareLabel")) {
            changeApolloObjectConfig("youthFareLabel", config.getProperty("youthFareLabel", ""), LabelInfo.class);
        }
        if (changeEvent.isChanged("reservationConfig")) {
            handConfig.setReservationConfig(JsonUtil.fromJson(config.getProperty("reservationConfig", "{}"), ReservationConfig.class));
        }
        if (changeEvent.isChanged("onTimeRate")) {
            handConfig.setOnTimeRate(config.getProperty("onTimeRate", "80.00%"));
        }
        if (changeEvent.isChanged("freeTicketCabin")) {
            handConfig.setFreeTicketCabin(config.getProperty("freeTicketCabin", "X"));
        }
        if (changeEvent.isChanged("checkRealName")) {
            handConfig.setCheckRealName(config.getProperty("checkRealName", "N"));
        }

        if (changeEvent.isChanged("freeTicketCabin")) {
            changeApolloObjectConfig("freeTicketCabin", config.getProperty("freeTicketCabin", ""), String.class);
        }
        if (changeEvent.isChanged("freeTicketDocument")) {
            changeApolloObjectConfig("freeTicketDocument", config.getProperty("freeTicketDocument", ""), String.class);
        }
        if (changeEvent.isChanged("awardFlyFreeTicketCabin")) {
            changeApolloObjectConfig("awardFlyFreeTicketCabin", config.getProperty("awardFlyFreeTicketCabin", ""), String.class);
        }
        if (changeEvent.isChanged("businessFlyFreeTicketCabin")) {
            handConfig.setBusinessFlyFreeTicketCabin(config.getProperty("businessFlyFreeTicketCabin", ""));
        }

        if (changeEvent.isChanged("openFreeTicketCabin")) {
            handConfig.setOpenFreeTicketCabin(config.getProperty("openFreeTicketCabin", ""));
        }

        if (changeEvent.isChanged("awardFlyFreeTicketDocument")) {
            changeApolloObjectConfig("awardFlyFreeTicketDocument", config.getProperty("awardFlyFreeTicketDocument", ""), String.class);
        }
        if (changeEvent.isChanged("businessFreeTicketDocument")) {
            handConfig.setBusinessFreeTicketDocument(config.getProperty("businessFreeTicketDocument", ""));
        }
        if (changeEvent.isChanged("awareFlyPeriodQueryDay")) {
            changeApolloObjectConfig("awareFlyPeriodQueryDay", config.getProperty("awareFlyPeriodQueryDay", ""), Integer.class);
        }
        if (changeEvent.isChanged("awareFlyPeriodUserRedis")) {
            changeApolloObjectConfig("awareFlyPeriodUserRedis", config.getProperty("awareFlyPeriodUserRedis", ""), String.class);
        }
        if (changeEvent.isChanged("logoutPageInfoConfig")) {
            changeApolloObjectConfig("logoutPageInfoConfig", config.getProperty("logoutPageInfoConfig", ""), LogoutPageInfoConfig.class);
        }
        if (changeEvent.isChanged("protocolTrafficCustomerServicePhone")) {
            handConfig.setProtocolTrafficCustomerServicePhone(config.getProperty("protocolTrafficCustomerServicePhone", ""));
        }
        if (changeEvent.isChanged("authIp")) {
            handConfig.setAuthIp(config.getProperty("authIp", "127.0.0.1"));
        }
        if (changeEvent.isChanged("couponUseRuleDocument")) {
            changeApolloObjectConfig("couponUseRuleDocument", config.getProperty("couponUseRuleDocument", ""), String.class);
        }
        if (changeEvent.isChanged("couponUseRuleDocumentNew")) {
            changeApolloObjectConfig("couponUseRuleDocumentNew", config.getProperty("couponUseRuleDocumentNew", ""), String.class);
        }
        if (changeEvent.isChanged("transferTips")) {
            changeApolloObjectConfig("transferTips", config.getProperty("transferTips", ""), LabelDetail.class);
        }
        if (changeEvent.isChanged("busTransferTips")) {
            changeApolloObjectConfig("busTransferTips", config.getProperty("busTransferTips", ""), LabelDetail.class);
        }

        if (changeEvent.isChanged("transferITips")) {
            changeApolloObjectConfig("transferITips", config.getProperty("transferITips", ""), LabelDetail.class);
        }
        /**
         * 2021-05-21 行李直达航班中转须知
         */
        if (changeEvent.isChanged("baggageDirectTransferTips")) {
            changeApolloObjectConfig("baggageDirectTransferTips", config.getProperty("baggageDirectTransferTips", ""), LabelAndRuleInfo.class);
        }
        if (changeEvent.isChanged("memberFareLabel")) {
            changeApolloObjectConfig("memberFareLabel", config.getProperty("memberFareLabel", ""), LabelAndRuleInfo.class);
        }
        if (changeEvent.isChanged("multiDiscountFareLabel")) {
            changeApolloObjectConfig("multiDiscountFareLabel", config.getProperty("multiDiscountFareLabel", ""), LabelAndRuleInfo.class);
        }
        if (changeEvent.isChanged("disneyFareLabel")) {
            changeApolloObjectConfig("disneyFareLabel", config.getProperty("disneyFareLabel", ""), LabelAndRuleInfo.class);
        }
        if (changeEvent.isChanged("baggageFareLabel")) {
            changeApolloObjectConfig("baggageFareLabel", config.getProperty("baggageFareLabel", ""), LabelAndRuleInfo.class);
        }
        if (changeEvent.isChanged("youthFareLabel")) {
            changeApolloObjectConfig("youthFareLabel", config.getProperty("youthFareLabel", ""), LabelAndRuleInfo.class);
        }
        if (changeEvent.isChanged("firstRideFareLabel")) {
            changeApolloObjectConfig("firstRideFareLabel", config.getProperty("firstRideFareLabel", ""), LabelAndRuleInfo.class);
        }
        /* 2021-10-20 会员秒杀 */
        if (changeEvent.isChanged("memberSecKillCabin")) {
            handConfig.setMemberSecKillCabin(config.getProperty("memberSecKillCabin", ""));
        }
        if (changeEvent.isChanged("memberSecKillTourCode")) {
            handConfig.setMemberSecKillTourCode(config.getProperty("memberSecKillTourCode", ""));
        }
        if (changeEvent.isChanged("memberSecKillFareLabel")) {
            changeApolloObjectConfig("memberSecKillFareLabel", config.getProperty("memberSecKillFareLabel", ""), LabelAndRuleInfo.class);
        }
        if (changeEvent.isChanged("noFreeBaggageFareLabel")) {
            changeApolloObjectConfig("noFreeBaggageFareLabel", config.getProperty("noFreeBaggageFareLabel", ""), LabelAndRuleInfo.class);
        }
        if (changeEvent.isChanged("multipleFlightTransferConfig")) {
            changeApolloObjectConfig("multipleFlightTransferConfig",
                    config.getProperty("multipleFlightTransferConfig", ""), MultipleFlightTransferConfig.class);
        }
        if (changeEvent.isChanged("ownerUpunlimitTip")) {
            handConfig.setOwnerUpunlimitTip(config.getProperty("ownerUpunlimitTip", "您购买的无限升舱卡尚未完成绑定，请尽快前往”我的-权益券“完成绑定"));
        }
        if (changeEvent.isChanged("givingUpunlimitTip")) {
            handConfig.setGivingUpunlimitTip(config.getProperty("givingUpunlimitTip", "好友赠送了您一张无限升舱卡，前往”我的-权益券“完成绑定后即可使用"));
        }
        if (changeEvent.isChanged("wifiLabelAndRuleInfo")) {
            changeApolloObjectConfig("wifiLabelAndRuleInfo", config.getProperty("wifiLabelAndRuleInfo", ""), LabelAndRuleInfo.class);
        }
        if (changeEvent.isChanged("wifiTravelPrivilege")) {
            changeApolloObjectConfig("wifiTravelPrivilege", config.getProperty("wifiTravelPrivilege", ""), TravelPrivilege.class);
        }

        if (changeEvent.isChanged(("activityConfigList"))) {
            try {
                String s = config.getProperty("activityConfigList", "");
                if (!StringUtil.isNullOrEmpty(s)) {
                    List<ActivityConfig> activityConfigList = JsonUtil.fromJson(s, new TypeToken<List<ActivityConfig>>() {
                    }.getType());
                    handConfig.setActivityConfigList(activityConfigList);
                } else {
                    handConfig.setActivityConfigList(new ArrayList<>());
                }
            } catch (Exception e) {
                logger.error("activityConfigList config update error! error info is ", e);
            }
        }
        if (changeEvent.isChanged(("coBrandedCardInfoList"))) {
            try {
                String s = config.getProperty("coBrandedCardInfoList", "");
                if (!StringUtil.isNullOrEmpty(s)) {
                    List<CoBrandedCardInfo> coBrandedCardInfoList = (List<CoBrandedCardInfo>) JsonUtil.jsonToBean(s, new TypeToken<List<CoBrandedCardInfo>>() {
                    }.getType());
                    handConfig.setCoBrandedCardInfoList(coBrandedCardInfoList);
                } else {
                    handConfig.setCoBrandedCardInfoList(new ArrayList<>());
                }
            } catch (Exception e) {
                logger.error("coBrandedCardInfoList config update error! error info is ", e);
            }
        }

        if (changeEvent.isChanged(("associateMemberInfoList"))) {
            try {
                String s = config.getProperty("associateMemberInfoList", "");
                if (!StringUtil.isNullOrEmpty(s)) {
                    List<AssociateMemberInfo> coBrandedCardInfoList = (List<AssociateMemberInfo>) JsonUtil.jsonToBean(s, new TypeToken<List<AssociateMemberInfo>>() {
                    }.getType());
                    handConfig.setAssociateMemberInfoList(coBrandedCardInfoList);
                } else {
                    handConfig.setAssociateMemberInfoList(new ArrayList<>());
                }
            } catch (Exception e) {
                logger.error("associateMemberInfoList config update error! error info is ", e);
            }
        }
        if (changeEvent.isChanged(("smsTempleList"))) {
            try {
                String s = config.getProperty("smsTempleList", "");
                if (!StringUtil.isNullOrEmpty(s)) {
                    List<SmsTemplate> smsTempleList = (List<SmsTemplate>) JsonUtil.jsonToBean(s, new TypeToken<List<SmsTemplate>>() {
                    }.getType());
                    handConfig.setSmsTempleList(smsTempleList);
                } else {
                    handConfig.setSmsTempleList(new ArrayList<>());
                }
            } catch (Exception e) {
                logger.error("smsTempleList config update error! error info is ", e);
            }
        }

        if (changeEvent.isChanged(("unLimitConfigList"))) {
            try {
                String s = config.getProperty("unLimitConfigList", "");
                if (!StringUtil.isNullOrEmpty(s)) {
                    List<ActivityConfig> activityConfigList = (List<ActivityConfig>) JsonUtil.jsonToBean(s, new TypeToken<List<ActivityConfig>>() {
                    }.getType());
                    handConfig.setUnLimitConfigList(activityConfigList);
                } else {
                    handConfig.setUnLimitConfigList(new ArrayList<>());
                }
            } catch (Exception e) {
                logger.error("unLimitUpgradeConfigList config update error! error info is ", e);
            }
        }

        if (changeEvent.isChanged(("bankConfigList"))) {
            try {
                String s = config.getProperty("bankConfigList", "");
                if (!StringUtil.isNullOrEmpty(s)) {
                    List<DCEPayConfig> dcePayConfigs = (List<DCEPayConfig>) JsonUtil.jsonToBean(s, new TypeToken<List<DCEPayConfig>>() {
                    }.getType());
                    handConfig.setBankConfigList(dcePayConfigs);
                } else {
                    handConfig.setBankConfigList(new ArrayList<>());
                }
            } catch (Exception e) {
                logger.error("bankConfigList config update error! error info is ", e);
            }
        }

        if (changeEvent.isChanged("unlimitedUpClassCabin")) {
            handConfig.setUnlimitedUpClassCabin(config.getProperty("unlimitedUpClassCabin", "J"));
        }
        if (changeEvent.isChanged("HKMCTWRegions")) {
            changeApolloObjectConfig("HKMCTWRegions", config.getProperty("HKMCTWRegions", ""), List.class);
        }
        if (changeEvent.isChanged("unlimitedCardTongDun")) {
            handConfig.setUnlimitedCardTongDun(config.getProperty("unlimitedCardTongDun", "Y"));
        }
        if (changeEvent.isChanged("sms4DigitPayment")) {
            /*handConfig.setUnlimitedCardTongDun(config.getProperty("sms4DigitPayment", ""));*/
            changeApolloObjectConfig("sms4DigitPayment", config.getProperty("sms4DigitPayment", ""), String.class);
        }
        if (changeEvent.isChanged("airportProtocolMessage")) {
            changeApolloObjectConfig("airportProtocolMessage", config.getProperty("airportProtocolMessage", ""), String.class);
        }
        if (changeEvent.isChanged("unlimitedUpNotAllowedCabin")) {
            handConfig.setUnlimitedUpNotAllowedCabin(config.getProperty("unlimitedUpNotAllowedCabin", ""));
        }
        if (changeEvent.isChanged("geetestOneLoginRiskLevelThreshold")) {
            handConfig.setGeetestOneLoginRiskLevelThreshold(config.getIntProperty("geetestOneLoginRiskLevelThreshold", 7));
        }
        if (changeEvent.isChanged("highRebateFareLabel")) {
            handConfig.setHighRebateFareLabel(config.getProperty("highRebateFareLabel", ""));
        }
        if (changeEvent.isChanged("unlimitedFlyDateLimit")) {
            handConfig.setUnlimitedFlyDateLimit(config.getProperty("unlimitedFlyDateLimit", "2021-01-20"));
        }
        if (changeEvent.isChanged("limitScoreUseDate")) {
            handConfig.setLimitScoreUseDate(config.getProperty("limitScoreUseDate", "2020-08-18"));
        }
        if (changeEvent.isChanged("limitScoreRiskUseDate")) {
            handConfig.setLimitScoreRiskUseDate(config.getProperty("limitScoreRiskUseDate", "2020-10-01"));
        }
        if (changeEvent.isChanged("showUnlimitedFlyTimeBegin")) {
            handConfig.setShowUnlimitedFlyTimeBegin(config.getProperty("showUnlimitedFlyTimeBegin", "2020-08-18 10:00:00"));
        }
        if (changeEvent.isChanged("freeTicket5DayLimit")) {
            handConfig.setFreeTicket5DayLimit(config.getIntProperty("freeTicket5DayLimit", 5));
        }
        if (changeEvent.isChanged("wxappSwitch")) {
            handConfig.setWxappSwitch(config.getProperty("wxappSwitch", "N"));
        }
        if (changeEvent.isChanged("useFlightRecommend")) {
            handConfig.setUseFlightRecommend(config.getProperty("useFlightRecommend", "Y"));
        }
        if (changeEvent.isChanged("checkinSeatTongDun")) {
            handConfig.setCheckinSeatTongDun(config.getProperty("checkinSeatTongDun", "N"));
        }
        if (changeEvent.isChanged("mwebOrderChannel")) {
            handConfig.setMwebOrderChannel(config.getProperty("mwebOrderChannel", "WEIXIN"));
        }
        if (changeEvent.isChanged("httpLogConfig")) {
            try {
                String s = config.getProperty("httpLogConfig", "");
                if (!StringUtil.isNullOrEmpty(s)) {
                    Map<String, String> httpLogConfig = JsonUtil.fromJson(s, new TypeToken<Map<String, String>>() {
                    }.getType());
                    handConfig.setHttpLogConfig(httpLogConfig);
                } else {
                    handConfig.setHttpLogConfig(new HashMap<>());
                }
            } catch (Exception e) {
                logger.error("httpLogConfig config update error!", e);
            }
        }
        if (changeEvent.isChanged("oneWayFareFlightNumber")) {
            handConfig.setOneWayFareFlightNumber(config.getIntProperty("oneWayFareFlightNumber", 4));
        }
        if (changeEvent.isChanged("suggestionSystem")) {
            handConfig.setSuggestionSystem(config.getProperty("suggestionSystem", "P"));
        }
        if (changeEvent.isChanged("isNewProcessInsurance")) {
            handConfig.setIsNewProcessInsurance(config.getProperty("isNewProcessInsurance", "N"));
        }
        if (changeEvent.isChanged("suggestionSystem")) {
            handConfig.setSuggestionSystem(config.getProperty("suggestionSystem", "P"));
        }
        if (changeEvent.isChanged("changeYCabinLimit")) {
            handConfig.setChangeYCabinLimit(config.getIntProperty("changeYCabinLimit", 5));
        }
        if (changeEvent.isChanged("autoCheckInTip")) {
            handConfig.setAutoCheckInTip(config.getProperty("autoCheckInTip", ""));
        }
        if (changeEvent.isChanged("smsUseTongDun")) {
            handConfig.setSmsUseTongDun(config.getProperty("smsUseTongDun", "Y"));
        }
        if (changeEvent.isChanged("foreverLimitSwitch")) {
            handConfig.setForeverLimitSwitch(config.getProperty("foreverLimitSwitch", "Y"));
        }
        if (changeEvent.isChanged("whiteListOfPAF")) {
            handConfig.setWhiteListOfPAF(config.getProperty("whiteListOfPAF", ""));
        }
        if (changeEvent.isChanged("captchaSendIntervalConfigs")) {
            handConfig.setCaptchaSendIntervalConfigs(JsonUtil.fromJson(config.getProperty("captchaSendIntervalConfigs", "[]"),
                    new TypeToken<List<CaptchaSendIntervalConfig>>() {
                    }.getType()));
        }
        if (changeEvent.isChanged("inMaoTaiConfig")) {
            changeApolloObjectConfig("inMaoTaiConfig", config.getProperty("inMaoTaiConfig", ""), MaoTaiConfig.class);
        }
        if (changeEvent.isChanged("outMaoTaiConfig")) {
            changeApolloObjectConfig("outMaoTaiConfig", config.getProperty("outMaoTaiConfig", ""), MaoTaiConfig.class);
        }
        if (changeEvent.isChanged("compensateFWRedirectB2CUrl")) {
            handConfig.setCompensateFWRedirectB2CUrl(config.getProperty("compensateFWRedirectB2CUrl", ""));
        }
        if (changeEvent.isChanged("compensateFWRedirectPayUrl")) {
            handConfig.setCompensateFWRedirectPayUrl(config.getProperty("compensateFWRedirectPayUrl", ""));
        }
        if (changeEvent.isChanged("compensateGateWayNo")) {
            handConfig.setCompensateGateWayNo(config.getProperty("compensateGateWayNo", ""));
        }
        if (changeEvent.isChanged("compensateApiRedirectUrl")) {
            handConfig.setCompensateApiRedirectUrl(config.getProperty("compensateApiRedirectUrl", ""));
        }
        if (changeEvent.isChanged("tripCertChannelCodes")) {
            handConfig.setTripCertChannelCodes(config.getProperty("tripCertChannelCodes", ""));
        }
        if (changeEvent.isChanged("unlimitedCard2Config")) {
            changeApolloObjectConfig("unlimitedCard2Config", config.getProperty("unlimitedCard2Config", ""), UnlimitedCard2Config.class);
        }
        if (changeEvent.isChanged("shoppingBoradMealType")) {
            changeApolloObjectConfig("shoppingBoradMealType", config.getProperty("shoppingBoradMealType", ""), List.class);
        }
        if (changeEvent.isChanged("shoppingBoradDepAirportCode")) {
            changeApolloObjectConfig("shoppingBoradDepAirportCode", config.getProperty("shoppingBoradDepAirportCode", ""), List.class);
        }
        if (changeEvent.isChanged("apiKey")) {
            handConfig.setApiKey(config.getProperty("apiKey", "gr9H97OanBO8RnqqrC19dfzPFFd5004k"));
        }
        if (changeEvent.isChanged("gmjcLimitLine")) {
            try {
                String s = config.getProperty("gmjcLimitLine", "");
                if (!StringUtil.isNullOrEmpty(s)) {
                    List<String> gmjcLimitLine = JsonUtil.fromJson(config.getProperty("gmjcLimitLine", ""), new TypeToken<List<String>>() {
                    }.getType());
                    handConfig.setGmjcLimitLine(gmjcLimitLine);
                }
            } catch (Exception e) {
                logger.error("gmjcLimitLine config update error!", e);
            }
        }
        if (changeEvent.isChanged("cleanProductCache")) {
            productCacheService.clearCache();
        }
        if (changeEvent.isChanged("voucherRefundChannelCodes")) {
            try {
                String s = config.getProperty("voucherRefundChannelCodes", "");
                List<String> voucherRefundChannelCodes = JsonUtil.fromJson(config.getProperty("voucherRefundChannelCodes", ""), new TypeToken<List<String>>() {
                }.getType());
                handConfig.setVoucherRefundChannelCodes(voucherRefundChannelCodes);
            } catch (Exception e) {
                logger.error("voucherRefundChannelCodes config update error!", e);
            }
        }
        if (changeEvent.isChanged("huazhuHotelRefundTimeLimit")) {
            handConfig.setHuazhuHotelRefundTimeLimit(config.getProperty("huazhuHotelRefundTimeLimit", ""));
        }
        if (changeEvent.isChanged("checkInPolicyConfig")) {
            changeApolloObjectConfig("checkInPolicyConfig", config.getProperty("checkInPolicyConfig", "{}"), CheckInPolicyConfig.class);
        }
        if (changeEvent.isChanged("preferredSeatAgreements")) {
            changeApolloObjectConfig("preferredSeatAgreements", config.getProperty("preferredSeatAgreements", "{}"), CheckInPolicyConfig.class);
        }
        if (changeEvent.isChanged("smsUseGeetest")) {
            handConfig.setSmsUseGeetest(config.getProperty("smsUseGeetest", ""));
        }
        if (changeEvent.isChanged("upgradeCardV2Config")) {
            changeApolloObjectConfig("upgradeCardV2Config", config.getProperty("upgradeCardV2Config", ""), UpgradeCardV2Config.class);
        }
        if (changeEvent.isChanged("tripCertOwnSaleCheck")) {
            handConfig.setTripCertOwnSaleCheck(config.getProperty("tripCertOwnSaleCheck", "Y"));
        }
        if (changeEvent.isChanged("payCacheTime")) {
            handConfig.setPayCacheTime(config.getLongProperty("payCacheTime", 3600L));
        }
        if (changeEvent.isChanged("baggageDirectConfig")) {
            changeApolloObjectConfig("baggageDirectConfig", config.getProperty("baggageDirectConfig", "{}"), BaggageDirectConfig.class);
        }
        if (changeEvent.isChanged("fraudEscapeConfig")) {
            changeApolloObjectConfig("fraudEscapeConfig", config.getProperty("fraudEscapeConfig", "{}"), FraudEscapeConfig.class);
        }
        if (changeEvent.isChanged("studentVerifyConfig")) {
            changeApolloObjectConfig("studentVerifyConfig", config.getProperty("studentVerifyConfig", "{}"), StudentVerifyConfig.class);
        }
        if (changeEvent.isChanged("maxDays")) {
            handConfig.setMaxDays(config.getIntProperty("maxDays", 0));
        }

        if (changeEvent.isChanged("payAfterFlyRemarks")) {
            handConfig.setPayAfterFlyRemarks(config.getProperty("payAfterFlyRemarks", ""));
        }
        if (changeEvent.isChanged("internationalChange")) {
            handConfig.setInternationalChange(config.getProperty("internationalChange", ""));
        }
        /**
         * 2021-05-24 国际品牌运价查询运价类型
         */
        if (changeEvent.isChanged("interBrandFareType")) {
            handConfig.setInterBrandFareType(config.getProperty("interBrandFareType", ""));
        }
        if (changeEvent.isChanged("versionControl")) {
            handConfig.setVersionControl(config.getProperty("versionControl", ""));
        }
        /**
         * 2021-06-24 需要国际品牌运价查询的城市三字码
         */
        if (changeEvent.isChanged("interBrandFareCountryCodes")) {
            changeApolloObjectConfig("interBrandFareCountryCodes", config.getProperty("interBrandFareCountryCodes", ""), List.class);
        }
        /**
         * 2021-07-23 打包券订单开具电子发票活动编号
         */
        if (changeEvent.isChanged("invoiceActivityNos")) {
            changeApolloObjectConfig("invoiceActivityNos", config.getProperty("invoiceActivityNos", ""), List.class);
        }

        /**
         * 2022-01-11 主题卡券类型
         */
        if (changeEvent.isChanged("themeCouponList")) {
            changeApolloObjectConfig("themeCouponList", config.getProperty("themeCouponList", ""), List.class);
        }

        /**
         * 2025-02-24 直播活动类型
         */
        if (changeEvent.isChanged("queryActivityList")) {
            changeApolloObjectConfig("queryActivityList", config.getProperty("queryActivityList", ""), List.class);
        }


        /**
         * 2022-01-11 主题卡券类型
         */
        if (changeEvent.isChanged("themeNameCouponList")) {
            changeApolloObjectConfig("themeNameCouponList", config.getProperty("themeNameCouponList", ""), List.class);
        }
        /**
         * 2021-8-11 预付费行李
         */
        if (changeEvent.isChanged("prepaymentBaggageDocumentResp")) {
            changeApolloObjectConfig("prepaymentBaggageDocumentResp", config.getProperty("prepaymentBaggageDocumentResp", ""), PrepaymentBaggageDocumentResp.class);
        }
        /**
         * 2021-08-12 发送短信次数（总发送次数非操作错误次数）
         */
        if (changeEvent.isChanged("sendSmsLimit")) {
            changeApolloObjectConfig("sendSmsLimit", config.getProperty("sendSmsLimit", "{}"), LimitCount.class);
        }
        if (changeEvent.isChanged("versionMember")) {
            changeApolloObjectConfig("versionMember", config.getProperty("versionMember", "{}"), VersionMember.class);
        }
        /**
         * 2022-5-12 控制钱包版本号
         */
        if (changeEvent.isChanged("vesionMumber")) {
            changeApolloObjectConfig("versionMember", config.getProperty("versionMember", "{}"), VersionMember.class);
        }
        /**
         * 2022-09-27 下单页保险相关配置
         */
        if (changeEvent.isChanged("insuranceConfig")) {
            changeApolloObjectConfig("insuranceConfig", config.getProperty("insuranceConfig", "{}"), InsuranceConfig.class);
        }
        //保险弹窗配置
        if (changeEvent.isChanged("insuranceConfigList")) {
            handConfig.setInsuranceConfigList(JsonUtil.fromJson(config.getProperty("insuranceConfigList", "[]"),
                    new TypeToken<List<InsuranceConfig>>() {
                    }.getType()));
        }
        /**
         * 2021-08-16 需要国际品牌运价查询的城市三字码
         */
        if (changeEvent.isChanged("flightReminderV2List")) {
            handConfig.setFlightReminderV2List(JsonUtil.fromJson(config.getProperty("flightReminderV2List", "[]"),
                    new TypeToken<List<FlightReminderV2>>() {
                    }.getType()));
        }
        if (changeEvent.isChanged("useProxyUrl")) {
            changeApolloObjectConfig("useProxyUrl", config.getProperty("useProxyUrl", "[]"), List.class);
        }

        if (changeEvent.isChanged("openAppAdvert")) {
            handConfig.setOpenAppAdvert(config.getProperty("openAppAdvert", "Y"));
        }
        if (changeEvent.isChanged("apiLimitList")) {
            handConfig.setApiLimitList(JsonUtil.fromJson(config.getProperty("apiLimitList", "[]"), new TypeToken<List<ApiLimit>>() {
            }.getType()));
        }
        if (changeEvent.isChanged("closed")) {
            handConfig.setClosed(config.getProperty("closed", "N"));
        }
        if (changeEvent.isChanged("holidaySwitch")) {
            handConfig.setHolidaySwitch(config.getProperty("holidaySwitch", "Y"));
        }
        if (changeEvent.isChanged("studentTips")) {
            handConfig.setStudentTips(config.getProperty("studentTips", ""));
        }
        if (changeEvent.isChanged("crmPublicKey")) {
            handConfig.setCrmPublicKey(config.getProperty("crmPublicKey", ""));
        }
        if (changeEvent.isChanged("crmSalt")) {
            handConfig.setCrmSalt(config.getProperty("crmSalt", "juneyaoair"));
        }
        if (changeEvent.isChanged("handLuggageList")) {
            handConfig.setHandLuggageList(JsonUtil.fromJson(config.getProperty("handLuggageList", "[]"), new TypeToken<List<HandLuggage>>() {
            }.getType()));
        }
        if (changeEvent.isChanged("crmRuleList")) {
            handConfig.setCrmRuleList(JsonUtil.fromJson(config.getProperty("crmRuleList", "[]"), new TypeToken<List<ScoreCrmRule>>() {
            }.getType()));
        }
        if (changeEvent.isChanged("themeTips")) {
            handConfig.setThemeTips(config.getProperty("themeTips", ""));
        }
        if (changeEvent.isChanged("themeCardIssueList")) {
            handConfig.setThemeCardIssueList(JsonUtil.fromJson(config.getProperty("themeCardIssueList", "[]"), new TypeToken<List<ThemeCardIssue>>() {
            }.getType()));
        }
        if (changeEvent.isChanged("themeCabin")) {
            handConfig.setThemeCabin(config.getProperty("themeCabin", "X"));
        }
        if (changeEvent.isChanged("themeBuyDayLimit")) {
            handConfig.setThemeBuyDayLimit(config.getIntProperty("themeBuyDayLimit", 3));
        }

        if (changeEvent.isChanged("queryDays")) {
            handConfig.setQueryDays(config.getIntProperty("queryDays", 30));
        }

        if (changeEvent.isChanged("themeCabinLabel")) {
            handConfig.setThemeCabinLabel(config.getProperty("themeCabinLabel", "主题卡兑换专享舱位"));
        }

        if (changeEvent.isChanged("payGatewayPageUrl")) {
            handConfig.setPayGatewayPageUrl(config.getProperty("payGatewayPageUrl", ""));
        }
        if (changeEvent.isChanged("memberStarQueryOpen")) {
            handConfig.setMemberStarQueryOpen(config.getProperty("memberStarQueryOpen", ""));
        }
        if (changeEvent.isChanged("queryPush")) {
            handConfig.setQueryPush(config.getProperty("queryPush", "N"));
        }
        if (changeEvent.isChanged("queryCountPush")) {
            handConfig.setQueryCountPush(config.getProperty("queryCountPush", "N"));
        }
        if (changeEvent.isChanged("personInternalSelfFlag")) {
            handConfig.setPersonInternalSelfFlag(config.getProperty("personInternalSelfFlag", ""));
        }
        if (changeEvent.isChanged("openMultiCouponFlag")) {
            handConfig.setOpenMultiCouponFlag(config.getProperty("openMultiCouponFlag", ""));
        }
        if (changeEvent.isChanged("isFamilyAccount")) {
            handConfig.setIsFamilyAccount(config.getProperty("isFamilyAccount", ""));
        }
        if (changeEvent.isChanged("isFamilyAccountVer")) {
            handConfig.setIsFamilyAccountVer(config.getIntProperty("isFamilyAccountVer", 79100));
        }

        if (changeEvent.isChanged("freeTicketFlag")) {
            handConfig.setFreeTicketFlag(config.getProperty("freeTicketFlag", ""));
        }
        if (changeEvent.isChanged("useCertTypeMap")) {
            String value = config.getProperty("useCertTypeMap", "{}");
            TypeReference<Map<String, List<CertType>>> typeReference = new TypeReference<Map<String, List<CertType>>>() {
            };
            handConfig.setUseCertTypeMap(JSON.parseObject(value, typeReference));
        }
        if (changeEvent.isChanged("commonPersonWarmRemindMap")) {
            String value = config.getProperty("commonPersonWarmRemindMap", "{}");
            TypeReference<Map<String, String>> typeReference = new TypeReference<Map<String, String>>() {
            };
            handConfig.setCommonPersonWarmRemindMap(JSON.parseObject(value, typeReference));
        }

        if (changeEvent.isChanged("flightQueryTypeMap")) {
            String value = config.getProperty("flightQueryTypeMap", "{}");
            TypeReference<Map<String, FlightQueryTypeDto>> typeReference = new TypeReference<Map<String, FlightQueryTypeDto>>() {
            };
            handConfig.setFlightQueryTypeMap(JSON.parseObject(value, typeReference));
        }

        if (changeEvent.isChanged("districtTipsTextMap")) {
            String value = config.getProperty("districtTipsTextMap", "{}");
            TypeReference<Map<String, String>> typeReference = new TypeReference<Map<String, String>>() {
            };
            handConfig.setDistrictTipsTextMap(JSON.parseObject(value, typeReference));
        }


        if (changeEvent.isChanged("returnFeeDescMap")) {
            String value = config.getProperty("returnFeeDescMap", "{}");
            TypeReference<Map<String, String>> typeReference = new TypeReference<Map<String, String>>() {
            };
            handConfig.setReturnFeeDescMap(JSON.parseObject(value, typeReference));
        }

        if (changeEvent.isChanged("personInternalRealNameFlag")) {
            handConfig.setPersonInternalRealNameFlag(config.getProperty("personInternalRealNameFlag", ""));
        }
        if (changeEvent.isChanged("personDomesticRealNameFlag")) {
            handConfig.setPersonDomesticRealNameFlag(config.getProperty("personDomesticRealNameFlag", ""));
        }

        if (changeEvent.isChanged("busFareLabelUrl")) {
            handConfig.setBusFareLabelUrl(config.getProperty("busFareLabelUrl", ""));
        }
        if (changeEvent.isChanged("onlineCustomerService")) {
            handConfig.setOnlineCustomerService(config.getProperty("onlineCustomerService", ""));
        }
        if (changeEvent.isChanged("newBaggageProductEnableTime")) {
            handConfig.setNewBaggageProductEnableTime(config.getProperty("newBaggageProductEnableTime", ""));
        }
        if (changeEvent.isChanged("oldProductList")) {
            handConfig.setOldProductList(JsonUtil.fromJson(config.getProperty("oldProductList", "[]"), new TypeToken<List<String>>() {
            }.getType()));
        }

        if (changeEvent.isChanged("material")) {
            handConfig.setMaterial(config.getProperty("material", ""));
        }
        if (changeEvent.isChanged("integralSwitch")) {
            handConfig.setIntegralSwitch(config.getProperty("integralSwitch", ""));
        }
        if (changeEvent.isChanged("serviceLink")) {
            handConfig.setServiceLink(config.getProperty("serviceLink", ""));
        }
        if (changeEvent.isChanged("juneyaoair.config.seatShareImgUrl")) {
            handConfig.setSeatShareImgUrl(config.getProperty("juneyaoair.config.seatShareImgUrl", ""));
        }
        if (changeEvent.isChanged("saveDeviceInfoFlag")) {
            handConfig.setSaveDeviceInfoFlag(config.getProperty("saveDeviceInfoFlag", "Y"));
        }
        if (changeEvent.isChanged("verticalReduction")) {
            handConfig.setVerticalReduction(config.getProperty("verticalReduction", "华瑞银行先飞后付"));
        }
        if (changeEvent.isChanged("CCBPay")) {
            handConfig.setCCBPay(config.getProperty("CCBPay", "建行吉祥联名卡支付"));
        }
        if (changeEvent.isChanged("CCBPayDesc")) {
            handConfig.setCCBPayDesc(config.getProperty("CCBPayDesc", "建行吉祥联名卡支付"));
        }
        if (changeEvent.isChanged("PAFDesc")) {
            handConfig.setPAFDesc(config.getProperty("PAFDesc", "先飞后付立减"));
        }
        if (changeEvent.isChanged("rightsDescriptionMap")) {
            String value = config.getProperty("rightsDescriptionMap", "{}");
            TypeReference<Map<String, rightsDescriptionResponse>> typeReference = new TypeReference<Map<String, rightsDescriptionResponse>>() {
            };
            handConfig.setRightsDescriptionMap(JSON.parseObject(value, typeReference));
        }


        if (changeEvent.isChanged("ThemeFlightDetailsMap")) {
            String value = config.getProperty("ThemeFlightDetailsMap", "{}");
            TypeReference<Map<String, ThemeFlightDetails>> typeReference = new TypeReference<Map<String, ThemeFlightDetails>>() {
            };
            handConfig.setThemeFlightDetailsMap(JSON.parseObject(value, typeReference));
        }

        if (changeEvent.isChanged("mButtonIcon")) {
            try {
                String s = config.getProperty("mButtonIcon", "");
                List<ImageResponse> imageResponse = (List<ImageResponse>) JsonUtil.jsonToBean(s, new TypeToken<List<ImageResponse>>() {
                }.getType());
                handConfig.setMButtonIcon(imageResponse);
            } catch (Exception e) {
                logger.error("imageResponse config update error!", e);
            }
        }
        if (changeEvent.isChanged("blackAndWhite")) {
            handConfig.setBlackAndWhite(config.getProperty("blackAndWhite", "N"));
        }
        if (changeEvent.isChanged("iosTechnicalDate")) {
            try {
                String s = config.getProperty("iosTechnicalDate", "");
                ThemeSwitchDate themeSwitchDate = (ThemeSwitchDate) JsonUtil.jsonToBean(s, ThemeSwitchDate.class);
                if (themeSwitchDate != null) {
                    handConfig.setIosTechnicalDate(themeSwitchDate);
                }
            } catch (Exception e) {
                logger.error("iosTechnicalDate config update error!", e);
            }
        }
        if (changeEvent.isChanged("technicalDate")) {
            try {
                String s = config.getProperty("technicalDate", "");
                ThemeSwitchDate themeSwitchDate = (ThemeSwitchDate) JsonUtil.jsonToBean(s, ThemeSwitchDate.class);
                if (themeSwitchDate != null) {
                    handConfig.setTechnicalDate(themeSwitchDate);
                }
            } catch (Exception e) {
                logger.error("technicalDate config update error!", e);
            }
        }
        if (changeEvent.isChanged("oneOrderUrl")) {
            handConfig.setOneOrderUrl(config.getProperty("oneOrderUrl", ""));
        }
        if (changeEvent.isChanged("useOpenApiAddress")) {
            try {
                List<String> pathList = (List<String>) JsonUtil.jsonToBean(config.getProperty("useOpenApiAddress", ""), new TypeToken<List<String>>() {
                }.getType());
                handConfig.setUseOpenApiAddress(pathList);
            } catch (Exception e) {
                logger.error("useOpenApiAddress config update error!", e);
            }
        }
        if (changeEvent.isChanged("downloadUrlList")) {
            try {
                List<String> pathList = (List<String>) JsonUtil.jsonToBean(config.getProperty("downloadUrlList", ""), new TypeToken<List<String>>() {
                }.getType());
                handConfig.setDownloadUrlList(pathList);
            } catch (Exception e) {
                logger.error("downloadUrlList config update error!", e);
            }
        }
        if (changeEvent.isChanged("offUrlList")) {
            try {
                Set<String> pathList = (Set<String>) JsonUtil.jsonToBean(config.getProperty("offUrlList", ""), new TypeToken<Set<String>>() {
                }.getType());
                handConfig.setOffUrlList(pathList);
            } catch (Exception e) {
                logger.error("offUrlList config update error!", e);
            }
        }
        if (changeEvent.isChanged("whiteUrlList")) {
            try {
                Set<String> pathList = (Set<String>) JsonUtil.jsonToBean(config.getProperty("whiteUrlList", ""), new TypeToken<Set<String>>() {
                }.getType());
                handConfig.setWhiteUrlList(pathList);
            } catch (Exception e) {
                logger.error("whiteUrlList config update error!", e);
            }
        }
        if (changeEvent.isChanged("smsUrl")) {
            handConfig.setSmsUrl(config.getProperty("smsUrl", ""));
        }

        if (changeEvent.isChanged("locationAddresses")) {
            try {
                String s = config.getProperty("locationAddresses", "");
                List<LocationAddress> LocationAddress = (List<LocationAddress>) JsonUtil.jsonToBean(s, new TypeToken<List<LocationAddress>>() {
                }.getType());
                handConfig.setLocationAddresses(LocationAddress);
            } catch (Exception e) {
                logger.error("locationAddresses config update error!", e);
            }
        }
        if (changeEvent.isChanged("minMobileVersion")) {
            handConfig.setMinMobileVersion(config.getProperty("minMobileVersion", "7.4.0"));
        }
        if (changeEvent.isChanged("minSsoTokenVersion")) {
            handConfig.setMinSsoTokenVersion(config.getProperty("minSsoTokenVersion", ""));
        }
        if (changeEvent.isChanged("dsopUrl")) {
            handConfig.setDsopUrl(config.getProperty("dsopUrl", ""));
        }
        if (changeEvent.isChanged("dataAppId")) {
            handConfig.setDataAppId(config.getProperty("dataAppId", ""));
        }
        if (changeEvent.isChanged("privateKeyIsv")) {
            handConfig.setPrivateKeyIsv(config.getProperty("privateKeyIsv", ""));
        }

        if (changeEvent.isChanged("rewriteFlag")) {
            handConfig.setRewriteFlag(config.getProperty("rewriteFlag", "false"));
        }
        if (changeEvent.isChanged("blackRegisterChannel")) {
            handConfig.setBlackRegisterChannel(config.getProperty("blackRegisterChannel", ""));
        }
        if (changeEvent.isChanged("ownRealChannel")) {
            handConfig.setOwnRealChannel(config.getProperty("ownRealChannel", ""));
        }
        if (changeEvent.isChanged("useTime")) {
            handConfig.setUseTime(config.getIntProperty("useTime", 180));
        }
        if (changeEvent.isChanged("photoUseTime")) {
            handConfig.setPhotoUseTime(config.getIntProperty("photoUseTime", 604800));
        }
        if (changeEvent.isChanged("headTitleInfo")) {
            changeApolloObjectConfig("headTitleInfo", config.getProperty("headTitleInfo", ""), HeadTitleInfo.class);
        }
        if (changeEvent.isChanged("com.juneyaoair.config.messageFlag")) {
            handConfig.setMessageFlag(config.getBooleanProperty("com.juneyaoair.config.messageFlag", true));
        }
        if (changeEvent.isChanged("com.juneyaoair.messagePlatform.push")) {
            handConfig.setMessagePlatformPush(config.getProperty("com.juneyaoair.messagePlatform.push", ""));
        }
        if (changeEvent.isChanged("com.juneyaoair.messagePlatform.channelCode")) {
            handConfig.setMessagePlatformChannelCode(config.getProperty("com.juneyaoair.messagePlatform.channelCode", ""));
        }
        if (changeEvent.isChanged("com.juneyaoair.messagePlatform.channelSecret")) {
            handConfig.setMessagePlatformChannelSecret(config.getProperty("com.juneyaoair.messagePlatform.channelSecret", ""));
        }
        if (changeEvent.isChanged("com.juneyaoair.messagePlatform.smsAisleNumber")) {
            handConfig.setMessagePlatformSmsAisleNumber(config.getProperty("com.juneyaoair.messagePlatform.smsAisleNumber", ""));
        }
        if (changeEvent.isChanged("com.juneyaoair.messagePlatform.wechatPublicAisleNumber")) {
            handConfig.setMessagePlatformWechatPublicAisleNumber(config.getProperty("com.juneyaoair.messagePlatform.wechatPublicAisleNumber", ""));
        }
        if (changeEvent.isChanged("ignoreAirline")) {
            handConfig.setIgnoreAirline(config.getProperty("ignoreAirline", ""));
        }
        if (changeEvent.isChanged("switchTongdunDorI")) {
            handConfig.setSwitchTongdunDorI(config.getProperty("switchTongdunDorI", ""));
        }
        if (changeEvent.isChanged("orderRemainTime")) {
            handConfig.setOrderRemainTime(config.getLongProperty("orderRemainTime", 1200000L));
        }
        if (changeEvent.isChanged("aYLeaveCountryText")) {
            handConfig.setAYLeaveCountryText(config.getProperty("aYLeaveCountryText", ""));
        }
        if (changeEvent.isChanged("flightWaitingText")) {
            handConfig.setFlightWaitingText(config.getProperty("flightWaitingText", ""));
        }
        if (changeEvent.isChanged("cabinSortJsonStr")) {
            handConfig.setCabinSortJsonStr(config.getProperty("cabinSortJsonStr", ""));
        }
        if (changeEvent.isChanged("hoAesKey")) {
            handConfig.setHoAesKey(config.getProperty("hoAesKey", ""));
        }
        if (changeEvent.isChanged("hoSm4Key")) {
            handConfig.setHoSm4Key(config.getProperty("hoSm4Key", ""));
        }
        if (changeEvent.isChanged("hoSm4KeyPrivate")) {
            handConfig.setHoSm4KeyPrivate(config.getProperty("hoSm4KeyPrivate", ""));
        }
        if (changeEvent.isChanged("sendCheckVerifyCodeByFfp")) {
            try {
                String s = config.getProperty("sendCheckVerifyCodeByFfp", "");
                TypeReference<Map<String, Integer>> typeReference = new TypeReference<Map<String, Integer>>() {
                };
                Map<String, Integer> sendCheckVerifyCodeByFfp = JSON.parseObject(s, typeReference);
                handConfig.setSendCheckVerifyCodeByFfp(sendCheckVerifyCodeByFfp);
            } catch (Exception e) {
                logger.error("sendCheckVerifyCodeByFfp config update error!", e);
            }
        }
        if (changeEvent.isChanged("useDsop")) {
            handConfig.setUseDsop(config.getProperty("useDsop", "N"));
        }
        if (changeEvent.isChanged("uploadCheckVerifyCodeByFfp")) {
            try {
                String s = config.getProperty("uploadCheckVerifyCodeByFfp", "");
                TypeReference<Map<String, Integer>> typeReference = new TypeReference<Map<String, Integer>>() {
                };
                Map<String, Integer> uploadCheckVerifyCodeByFfp = JSON.parseObject(s, typeReference);
                handConfig.setUploadCheckVerifyCodeByFfp(uploadCheckVerifyCodeByFfp);
            } catch (Exception e) {
                logger.error("uploadCheckVerifyCodeByFfp config update error!", e);
            }
        }
        if (changeEvent.isChanged("checkAccessCount")) {
            try {
                String s = config.getProperty("checkAccessCount", "");
                TypeReference<Map<String, Integer>> typeReference = new TypeReference<Map<String, Integer>>() {};
                Map<String, Integer> checkAccessCountMap = JSON.parseObject(s, typeReference);
                handConfig.setCheckAccessCount(checkAccessCountMap);
            } catch (Exception e) {
                logger.error("checkAccessCount config update error!", e);
            }
        }
        if (changeEvent.isChanged("useCheckDevice")) {
            handConfig.setUseCheckDevice(config.getProperty("useCheckDevice", "N"));
        }
        if (changeEvent.isChanged("juneyaoair.config.limiterInfoMap")) {
            TypeReference<Map<String, RateLimiterInfo>> typeReference = new TypeReference<Map<String, RateLimiterInfo>>() {
            };
            Map<String, RateLimiterInfo> limiterInfoMap = JSON.parseObject(config.getProperty("juneyaoair.config.limiterInfoMap", "{}"), typeReference);
            handConfig.setLimiterInfoMap(limiterInfoMap);
        }
        //oss相关配置刷新
        if (changeEvent.isChanged("ossMinioEndpoint")) {
            handConfig.setEndpoint(config.getProperty("ossMinioEndpoint", "https://minio.juneyaoair.com"));
        }
        if (changeEvent.isChanged("ossMinioAccessKey")) {
            handConfig.setAccessKey(config.getProperty("ossMinioAccessKey", ""));
        }
        if (changeEvent.isChanged("ossMinioSecretKey")) {
            handConfig.setSecretKey(config.getProperty("ossMinioSecretKey", ""));
        }
        if (changeEvent.isChanged("ossMiniBucket")) {
            handConfig.setBucket(config.getProperty("ossMiniBucket", "app"));
        }
        if (changeEvent.isChanged("closeSellTime")) {
            handConfig.setCloseSellTime(config.getLongProperty("closeSellTime", 40L));
        }

        if (changeEvent.isChanged("closeSellPromptTime")) {
            handConfig.setCloseSellPromptTime(config.getLongProperty("closeSellPromptTime", 65L));
        }
        if (changeEvent.isChanged("xinfeiChange")) {
            handConfig.setXinfeiChange(config.getProperty("xinfeiChange", "Y"));
        }
        if (changeEvent.isChanged("xinfeiUrl")) {
            handConfig.setXinfeiUrl(config.getProperty("xinfeiUrl", ""));
        }
        if (changeEvent.isChanged("refundApplyAdaFlag")) {
            handConfig.setRefundApplyAdaFlag(config.getProperty("refundApplyAdaFlag", ""));
        }
        if (changeEvent.isChanged("checkLicense.map")) {
            String value = config.getProperty("checkLicense.map", "{}");
            TypeReference<Map<String, Integer>> typeReference = new TypeReference<Map<String, Integer>>() {};
            handConfig.setCheckLicenseMap(JSON.parseObject(value, typeReference));
        }
        if (changeEvent.isChanged("specialFareQuery.map")) {
            String value = config.getProperty("specialFareQuery.map", "{}");
            TypeReference<Map<String, SpecialFareQuery>> typeReference = new TypeReference<Map<String, SpecialFareQuery>>() {};
            handConfig.setSpecialFareQueryMap(JSON.parseObject(value, typeReference));
        }
        if (changeEvent.isChanged("useTransferCityFlag")) {
            handConfig.setUseTransferCityFlag(config.getProperty("useTransferCityFlag", "Y"));
        }
        if (changeEvent.isChanged("b2cHandUrl")) {
            handConfig.setB2cHandUrl(config.getProperty("b2cHandUrl", ""));
        }
        if (changeEvent.isChanged("waitFareMinute")) {
            handConfig.setWaitFareMinute(config.getIntProperty("waitFareMinute", 0));
        }
        if (changeEvent.isChanged("ossUseMinioType")) {
            try {
                List<String> useMinioTypeList = (List<String>) JsonUtil.jsonToBean(config.getProperty("ossUseMinioType", ""), new TypeToken<List<String>>() {
                }.getType());
                handConfig.setUseMinioType(useMinioTypeList);
            } catch (Exception e) {
                logger.error("useMinioType config update error!", e);
            }
        }
        if (changeEvent.isChanged("clearPriceCode")) {
            try {
                List<String> clearPriceCodeList = (List<String>) JsonUtil.jsonToBean(config.getProperty("clearPriceCode", ""), new TypeToken<List<String>>() {
                }.getType());
                handConfig.setClearPriceCode(clearPriceCodeList);
            } catch (Exception e) {
                logger.error("clearPriceCode config update error!", e);
            }
        }
        if (changeEvent.isChanged("notShowCreditCardAdChannel")) {
            try {
                List<String> notShowCreditCardAdChannelList = (List<String>) JsonUtil.jsonToBean(config.getProperty("notShowCreditCardAdChannel", ""), new TypeToken<List<String>>() {
                }.getType());
                handConfig.setNotShowCreditCardAdChannel(notShowCreditCardAdChannelList);
            } catch (Exception e) {
                logger.error("notShowCreditCardAdChannel config update error!", e);
            }
        }
        if(changeEvent.isChanged("jjscConfig")){
            try {
                JjscConfig jjscConfig = JsonUtil.fromJson(config.getProperty("jjscConfig", "{}"), new TypeToken<JjscConfig>() {});
                handConfig.setJjscConfig(jjscConfig);
            } catch (Exception e) {
                logger.error("jjscConfig config update error!", e);
            }
        }
        if(changeEvent.isChanged("rsa.key.auth.apiPrivateKey")){
            handConfig.setApiPrivateKey(config.getProperty("rsa.key.auth.apiPrivateKey",""));
        }
        if(changeEvent.isChanged("rsa.key.auth.bffPublicKey")){
            handConfig.setBffPublicKey(config.getProperty("rsa.key.auth.bffPublicKey",""));
        }
        if(changeEvent.isChanged("searchTheme")){
            handConfig.setSearchTheme(config.getBooleanProperty("searchTheme",false));
        }
        if (changeEvent.isChanged("allowOriginList")) {
            try {
                Set<String> allowOriginList = (Set<String>) JsonUtil.jsonToBean(config.getProperty("allowOriginList", ""), new TypeToken<Set<String>>() {
                }.getType());
                handConfig.setAllowOriginList(allowOriginList);
            } catch (Exception e) {
                logger.error("allowOriginList config update error!", e);
            }
        }
        logger.info("HandConfig更新之后的配置{}", handConfig.toString());
    }

    /**
     * 更新阿波罗配置
     * 注：此方法有缺陷，无法正确识别泛型类型，诸如List<T>, Map<K,V>等，配置更新时泛型属性值为Set类型
     *
     * @param propertyName HandConfig 中的属性名
     * @param jsonValue    json字符串
     * @param type         HandConfig 中的属性类型
     * @param <T>
     */
    private <T> void changeApolloObjectConfig(String propertyName, String jsonValue, Class<T> type) {
        try {
            String methodName = "set" + propertyName.substring(0, 1).toUpperCase() + propertyName.substring(1);
            Method method = HandConfig.class.getMethod(methodName, type);
            T args = null;
            if (type.equals(String.class)) {
                args = (T) jsonValue;
            } else {
                args = JsonUtil.fromJson(jsonValue, type);
            }
            method.invoke(handConfig, args);
        } catch (Exception e) {
            logger.error(propertyName + " config update error!", e);
        }
    }

    //模板空间配置更新
    @ApolloConfigChangeListener("hand.templete")
    private void templeteOnChange(ConfigChangeEvent changeEvent) {
        logger.info("TempleteConfig更新之前的配置：" + templeteConfig.toString());
        if (changeEvent.isChanged("shareDesc")) {
            templeteConfig.setShareDesc(tconfig.getProperty("shareDesc", "赠送您一张#COUPONNAME#，快来领取吧~"));
        }
        if (changeEvent.isChanged("signatureConditions")) {
            templeteConfig.setSignatureConditions(tconfig.getProperty("signatureConditions", "不可自愿签转，非自愿签转请联系客服"));
        }
        if (changeEvent.isChanged("flightChangeSms")) {
            templeteConfig.setFlightChangeSms(tconfig.getProperty("flightChangeSms", "尊敬的旅客，请点击链接领取您的航延证明#SHORTURL#， 感谢您的理解和支持，如有疑问请致电吉祥客服95520咨询。"));
        }
        logger.info("TempleteConfig更新之后的配置：" + templeteConfig.toString());
    }
}

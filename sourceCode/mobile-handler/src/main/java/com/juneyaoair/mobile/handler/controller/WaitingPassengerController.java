package com.juneyaoair.mobile.handler.controller;

import com.google.gson.reflect.TypeToken;
import com.juneyaoair.appenum.WSEnum;
import com.juneyaoair.appenum.member.CertificateTypeEnum;
import com.juneyaoair.appenum.member.MemberDetailRequestItemsEnum;
import com.juneyaoair.baseclass.basicsys.response.AirPortInfoDto;
import com.juneyaoair.baseclass.common.request.BaseReq;
import com.juneyaoair.baseclass.common.response.BaseResp;
import com.juneyaoair.baseclass.waitingpassager.WaitingPassgerLimit;
import com.juneyaoair.baseclass.waitingpassager.request.*;
import com.juneyaoair.baseclass.waitingpassager.response.WaitingFlightInfo;
import com.juneyaoair.baseclass.waitingpassager.response.WaitingPersonResp;
import com.juneyaoair.crm.IMemberService;
import com.juneyaoair.mobile.comm.service.IRedisService;
import com.juneyaoair.mobile.constants.MobileCoreBeanNames;
import com.juneyaoair.mobile.core.service.cityandairport.ICityAirportService;
import com.juneyaoair.mobile.handler.HandlerConstants;
import com.juneyaoair.mobile.handler.comm.RedisKeyConfig;
import com.juneyaoair.mobile.handler.config.bean.HandConfig;
import com.juneyaoair.mobile.handler.service.IBasicService;
import com.juneyaoair.mobile.handler.service.LocalCacheService;
import com.juneyaoair.mobile.handler.util.CertNoUtil;
import com.juneyaoair.mobile.handler.util.FileUtils;
import com.juneyaoair.mobile.mongo.entity.FlightInfo;
import com.juneyaoair.pattern.PatternCommon;
import com.juneyaoair.thirdentity.bigdata.CusBaseLabelResponse;
import com.juneyaoair.thirdentity.bigdata.commmon.PtBIgDataResponse;
import com.juneyaoair.thirdentity.comm.request.Header;
import com.juneyaoair.thirdentity.comm.request.PtApiCRMRequest;
import com.juneyaoair.thirdentity.comm.response.PtCRMResponse;
import com.juneyaoair.thirdentity.member.request.PtMemberDetailRequest;
import com.juneyaoair.thirdentity.member.response.PtMemberDetail;
import com.juneyaoair.utils.EncoderHandler;
import com.juneyaoair.utils.StringUtil;
import com.juneyaoair.utils.http.HttpResult;
import com.juneyaoair.utils.http.HttpUtil;
import com.juneyaoair.utils.json.JsonUtil;
import com.juneyaoair.utils.util.BeanUtils;
import com.juneyaoair.utils.util.DateUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import java.lang.reflect.Type;
import java.util.*;

/**
 * 地面候补
 *
 * <AUTHOR>
 * @project mobile
 * @create 2018-11-16 16:23
 */
@RequestMapping("/waitingPassenger")
@RestController
@Api(value = "WaitingPassengerController")
public class WaitingPassengerController extends BassController {
    @Autowired
    private HandConfig handConfig;
    @Resource(name = MobileCoreBeanNames.API_REDIS_SERVICE)
    private IRedisService apiRedisService;
    @Autowired
    private IMemberService memberService;
    @Autowired
    private IBasicService basicService;
    @Autowired
    private LocalCacheService localCacheService;

    private static final String WAITING_LIMIT_KEY = RedisKeyConfig.WAITING_LIMIT_KEY;
    private static final String LOG_RESP_ONE = "请求号:{}，IP地址:{}，服务端响应结果：{}";
    private static final String LOG_RESP_TWO = "没有查询到任何航班信息";
    private static final String LOG_RESP_THREE = "网络请求异常！";
    private static final String LOG_RESP_FOUR = "请求号:{}，IP地址:{}，错误信息：{}";
    private static final String LOG_RESP_FIVE = "请求异常！";
    private static final String DATE_FORMAT = "yyyy-MM-dd";
    @ApiOperation(value = "查询航班信息", notes = "查询航班信息")
    @RequestMapping(value = "/queryFlightInfo", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp queryFlightInfo(@RequestBody BaseReq<FlightQuery> req, HttpServletRequest request) {
        String reqId = StringUtil.newGUID() + "_queryFlightInfo";
        BaseResp resp = new BaseResp();
        String ip = this.getClientIP(request);
        try {
            ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
            Validator validator = factory.getValidator();
            Set<ConstraintViolation<BaseReq>> violations = validator.validate(req);
            if (CollectionUtils.isNotEmpty(violations)) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo(violations.iterator().next().getMessage());
                return resp;
            }
            //验证日期是否可候補
            FlightQuery flightQuery = req.getRequest();
            String currentDateStr = DateUtils.getCurrentDateStr();
            Date date = DateUtils.toDate(currentDateStr);
            Date toAllDate = DateUtils.toDate(flightQuery.getFlightDate());
            int i = toAllDate.compareTo(date);
            if (i <= 0) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo("只可查询当日之后的航班日期");
                return resp;
            }
            // 验证用户查询是否正常
            boolean flag = this.checkKeyInfo(flightQuery.getFfpId(), flightQuery.getLoginKeyInfo(), req.getChannelCode());
            if (!flag) {
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                String respJson = JsonUtil.objectToJson(resp);
                log.info(LOG_RESP_ONE, reqId, ip, respJson);
                return resp;
            }
            // mongodb查询航班信息
            List<WaitingFlightInfo> flightInfos = queryFlightInfoList(flightQuery);
            if (CollectionUtils.isEmpty(flightInfos)) {
                resp.setResultCode(WSEnum.NO_DATA.getResultCode());
                resp.setResultInfo(WSEnum.NO_DATA.getResultInfo());
                resp.setObjData(LOG_RESP_TWO);
                return resp;
            }
            List<WaitingFlightInfo> waitingFlightInfos = new ArrayList<>();
            //特殊时间需要进行候补过滤
            if ("Y".equals(handConfig.getUseWaitingLimit())) {
                flightInfos = filterFlightNo(flightInfos, flightQuery.getFlightDate());
                if (CollectionUtils.isEmpty(flightInfos)) {
                    resp.setResultCode(WSEnum.NO_DATA.getResultCode());
                    resp.setResultInfo(WSEnum.NO_DATA.getResultInfo());
                    resp.setObjData(LOG_RESP_TWO);
                    return resp;
                }
            }
            for (WaitingFlightInfo flightInfo : flightInfos) {
                //调用旅客服务网判断mongdb航班是否可候补
                StringBuilder stringBuilder = new StringBuilder();
                stringBuilder.append("arrivalAirport=").append(flightInfo.getArrAirport())
                        .append("&").append("departureAirport=").append(flightInfo.getDepAirport())
                        .append("&").append("flightDate=").append(flightInfo.getFlightDate())
                        .append("&").append("flightNo=").append(flightInfo.getFlightNo());
                try {
                    String waitingFlightResult = passagerGet(stringBuilder.toString(), HandlerConstants.URL_PASSAGER_API + HandlerConstants.WAITINGPASSAGER_QUERYFLIGHT);
                    if (!StringUtil.isNullOrEmpty(waitingFlightResult)) {
                        WaitingPersonResp waitingPersonResp = (WaitingPersonResp) JsonUtil.jsonToBean(waitingFlightResult, WaitingPersonResp.class);
                        if ("0".equals(waitingPersonResp.getCode())) {
                            ArrayList<Map> data = (ArrayList) waitingPersonResp.getData();
                            if (CollectionUtils.isEmpty(data)) {
                                continue;
                            } else {
                                String wnum = (String) data.get(0).get("wnum");
                                if (wnum == null || "".equals(wnum)) {
                                    wnum = "0";
                                }
                                flightInfo.setWaitingPersonNum(wnum);
                                String arrTime = (String) data.get(0).get("sta");
                                if (!StringUtil.isNullOrEmpty(arrTime)) {
                                    String substringTime = arrTime.substring(11);
                                    if (!StringUtil.isNullOrEmpty(substringTime)) {
                                        flightInfo.setArrDateTime(substringTime);
                                    }

                                }
                                WaitingFlightInfo waitingFlightInfo = new WaitingFlightInfo();
                                BeanUtils.copyNotNullProperties(flightInfo, waitingFlightInfo);
                                //加入Y舱价格
                                waitingFlightInfo.setYCabinPrice(Double.parseDouble((String) data.get(0).get("adt_price")));

                                waitingFlightInfos.add(waitingFlightInfo);
                            }

                        } else {
                            resp.setResultCode(WSEnum.ERROR.getResultCode());
                            resp.setResultInfo(waitingPersonResp.getMsg());
                            return resp;
                        }
                    } else {
                        resp.setResultCode(WSEnum.ERROR.getResultCode());
                        resp.setResultInfo(LOG_RESP_THREE);
                        return resp;
                    }
                } catch (Exception e) {
                    log.error(LOG_RESP_FOUR, reqId, ip, e);
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo(LOG_RESP_FIVE);
                    return resp;
                }

            }

            if (CollectionUtils.isEmpty(waitingFlightInfos)) {
                resp.setResultCode(WSEnum.NO_DATA.getResultCode());
                resp.setResultInfo(WSEnum.NO_DATA.getResultInfo());
                resp.setObjData(LOG_RESP_TWO);
            } else {
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                resp.setObjData(waitingFlightInfos);
            }
        } catch (Exception e) {
            log.error(LOG_RESP_FOUR, reqId, ip, e);
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("数据异常！");
            return resp;
        }
        return resp;
    }


    @Autowired
    private ICityAirportService cityAirportService;
    /**
     * 根据城市及日期获取航班信息
     *
     * @param flightQuery
     * @return
     */
    private List<WaitingFlightInfo> queryFlightInfoList(FlightQuery flightQuery) {
        ArrayList<WaitingFlightInfo> waitingFlightInfos = new ArrayList<>();
        FlightInfo flightInfo = new FlightInfo();
        flightInfo.setArrCity(flightQuery.getArrCity());
        flightInfo.setDepCity(flightQuery.getDepCity());
        flightInfo.setFlightDate(flightQuery.getFlightDate());
        List<FlightInfo> flightInfos = basicService.queryFlightInfo(flightInfo);
        if (StringUtil.isNullOrEmpty(flightInfos)) {
            return waitingFlightInfos;
        } else {
            for (FlightInfo info : flightInfos) {
                WaitingFlightInfo waitingFlightInfo = new WaitingFlightInfo();
                BeanUtils.copyNotNullProperties(info, waitingFlightInfo);
                if (StringUtil.isNullOrEmpty(waitingFlightInfo.getArrAirportTerminal()) || waitingFlightInfo.getArrAirportTerminal().equals("--")) {
                    waitingFlightInfo.setArrAirportTerminal("");
                }
                if (StringUtil.isNullOrEmpty(waitingFlightInfo.getDepAirportTerminal()) || waitingFlightInfo.getDepAirportTerminal().equals("--")) {
                    waitingFlightInfo.setDepAirportTerminal("");
                }
                //将返回的到达出发时间2255转为22:55
                String arrDateTime = waitingFlightInfo.getArrDateTime();
                String arrHour = arrDateTime.substring(0, 2);
                String arrMin = arrDateTime.substring(2);
                waitingFlightInfo.setArrDateTime(arrHour + ":" + arrMin);
                String depDateTime = waitingFlightInfo.getDepDateTime();
                String depHour = depDateTime.substring(0, 2);
                String depMin = depDateTime.substring(2);
                waitingFlightInfo.setDepDateTime(depHour + ":" + depMin);
                // 根据三字码获取名称等信息
                AirPortInfoDto arrPortInfo = localCacheService.getLocalAirport(waitingFlightInfo.getArrAirport(),waitingFlightInfo.getFlightDate());
                waitingFlightInfo.setArrAirportName(arrPortInfo.getAirPortName());
                waitingFlightInfo.setArrCityName(arrPortInfo.getCityName());
                AirPortInfoDto depPortInfo = localCacheService.getLocalAirport(waitingFlightInfo.getDepAirport(),waitingFlightInfo.getFlightDate());
                waitingFlightInfo.setDepAirportName(depPortInfo.getAirPortName());
                waitingFlightInfo.setDepCityName(depPortInfo.getCityName());
                String isStop = waitingFlightInfo.getIsStop();
                if ("Y".equals(isStop)) {
                    if (StringUtil.isNullOrEmpty(waitingFlightInfo.getStopAirport())) {
                        waitingFlightInfo.setStopCityName("--");
                    } else {
                        AirPortInfoDto stopPortInfo = localCacheService.getLocalAirport(waitingFlightInfo.getStopAirport(),waitingFlightInfo.getFlightDate());
                        waitingFlightInfo.setStopCityName(stopPortInfo.getCityName());
                    }

                }
                waitingFlightInfo.setFlightTime(DateUtils.millisecondDiff(DateUtils.toAllDate(waitingFlightInfo.getDepDateChinaTime()), DateUtils.toAllDate(waitingFlightInfo.getArrDateChinaTime())));
                waitingFlightInfo.setDays(DateUtils.dateDiff(DateUtils.toAllDate(waitingFlightInfo.getDepDateChinaTime()), DateUtils.toAllDate(waitingFlightInfo.getArrDateChinaTime())));

                waitingFlightInfos.add(waitingFlightInfo);

            }
        }
        return waitingFlightInfos;
    }

    @ApiOperation(value = "applyWaitingPassenger", notes = "候补申请")
    @RequestMapping(value = "/applyWaitingPassenger", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp applyWaitingPassenger(@RequestBody BaseReq<WaitingPassager> req, HttpServletRequest request) {
        String reqId = StringUtil.newGUID() + "_applyWaitingPassenger";
        BaseResp resp = new BaseResp();
        String ip = this.getClientIP(request);
        try {
            String reqJson = JsonUtil.objectToJson(req);
            log.info("【航班候补申请】请求号:{}，IP地址:{}，客户端提交参数：{}", reqId, ip, reqJson);
            ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
            Validator validator = factory.getValidator();
            Set<ConstraintViolation<BaseReq>> violations = validator.validate(req);
            if (CollectionUtils.isNotEmpty(violations)) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo(violations.iterator().next().getMessage());
                String respJson = JsonUtil.objectToJson(resp);
                log.info("【航班候补申请】请求号:{}，IP地址:{}，服务端响应结果：{}", reqId, ip, respJson);
                return resp;
            }
            // 验证用户查询是否正常
            WaitingPassager waitingPassager = req.getRequest();
            boolean flag = this.checkKeyInfo(waitingPassager.getFfpId(), waitingPassager.getLoginKeyInfo(), req.getChannelCode());
            if (!flag) {
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                return resp;
            }
            //当天时间8点以后不接受申请
            Calendar calendar = Calendar.getInstance();
            int hour = calendar.get(Calendar.HOUR_OF_DAY);
            if (hour >= 20) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("当日20:00后暂停受理，可于明日20:00前提交候补申请");
                return resp;
            }
            // 前端请求数据转化后端需要
            WaitingPassager reqRequest = req.getRequest();
            List<PassagerInfo> passagerList = reqRequest.getPassagerList();
            if (null != passagerList && passagerList.size() > 2) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo("一次最多添加两位候补旅客");
                return resp;
            }
            String channelCode = req.getChannelCode();
            for (PassagerInfo passagerInfo : passagerList) {
                if ("NI".equals(passagerInfo.getPassengerIdcardType())) {
                    boolean matches = passagerInfo.getPassengerIdcard().matches(PatternCommon.ID_NUMBER);
                    if (!matches) {
                        resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                        resp.setResultInfo("身份证格式不正确");
                        return resp;
                    }
                } else if ("PSPT".equals(passagerInfo.getPassengerIdcardType())) {
                    boolean matches = passagerInfo.getPassengerIdcard().matches(PatternCommon.PASSPORT_NO);
                    if (!matches) {
                        resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                        resp.setResultInfo("护照格式不正确");
                        return resp;
                    }
                } else {
                    resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                    resp.setResultInfo("证件类型不合法");
                    return resp;
                }
            }
            WaitWaitingPassengerList waitPassReq = new WaitWaitingPassengerList();
            List<WaitingPassagerInfo> waitWaitingPassengerList ;
            String orderNum = "DN" + System.currentTimeMillis() + Math.round(Math.random() * 90 + 10);
            waitWaitingPassengerList = changerReq(reqRequest, orderNum, request, channelCode);
            waitPassReq.setWaitWaitingPassengerList(waitWaitingPassengerList);
            // 调用旅客服务网增加候补旅客
            String serviceResult = passagerPost(waitPassReq, HandlerConstants.URL_PASSAGER_API + HandlerConstants.WAITINGPASSAGER_APPLY);
            if (!StringUtil.isNullOrEmpty(serviceResult)) {
                WaitingPersonResp waitingPersonResp = (WaitingPersonResp) JsonUtil.jsonToBean(serviceResult, WaitingPersonResp.class);
                if ("0".equals(waitingPersonResp.getCode())) {
                    resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                    resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());

                    //查询候补人数
                    FlightNum flightNum = new FlightNum();
                    flightNum.setArrival_airport(reqRequest.getArrivalAirport());
                    flightNum.setDeparture_airport(reqRequest.getDepartureAirport());
                    flightNum.setFlight_date(reqRequest.getFlightDate());
                    flightNum.setFlight_no(reqRequest.getFlightNo());
                    flightNum.setPassenger_idcard(reqRequest.getPassagerList().get(0).getPassengerIdcard());
                    // 调用旅客服务网查询候补人数
                    String serviceNum = passagerPost(flightNum, HandlerConstants.URL_PASSAGER_API + HandlerConstants.WAITINGPASSAGER_SELECTNUM);
                    if (!StringUtil.isNullOrEmpty(serviceNum)) {
                        WaitingPersonResp waitingPersonNum = (WaitingPersonResp) JsonUtil.jsonToBean(serviceNum, WaitingPersonResp.class);
                        if ("0".equals(waitingPersonNum.getCode())) {
                            resp.setObjData(waitingPersonNum.getData());
                        } else {
                            resp.setResultCode(WSEnum.ERROR.getResultCode());
                            resp.setResultInfo(waitingPersonNum.getMsg());
                        }
                    } else {
                        resp.setResultCode(WSEnum.ERROR.getResultCode());
                        resp.setResultInfo(LOG_RESP_THREE);
                        return resp;
                    }
                } else {
                    resp.setResultCode(WSEnum.REPEAT_UBMISSION.getResultCode());
                    resp.setResultInfo(waitingPersonResp.getMsg());
                    return resp;
                }
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo(LOG_RESP_THREE);
                return resp;
            }


        } catch (Exception e) {
            log.error("【航班候补申请】请求号:{}，IP地址:{}，错误信息：", reqId, ip, e);
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(LOG_RESP_FIVE);
            log.info("【航班候补申请】请求号:{}，IP地址:{}，服务端响应结果：{}", reqId, ip, JsonUtil.objectToJson(resp));
            return resp;
        }
        return resp;
    }

    /**
     * 讲前端请求转化未后端需要的list集合
     *
     * @param req
     * @return
     */
    private List<WaitingPassagerInfo> changerReq(WaitingPassager req, String orderNum, HttpServletRequest request, String channelCode) {
        ArrayList<WaitingPassagerInfo> passagerInfos = new ArrayList<>();
        List<PassagerInfo> passagerList = req.getPassagerList();
        if (StringUtil.isNullOrEmpty(passagerList)) {
            return passagerInfos;
        } else {
            for (PassagerInfo passager : passagerList) {
                WaitingPassagerInfo waitingPassagerInfo = new WaitingPassagerInfo();
                waitingPassagerInfo.setArrTime(req.getArrTime());
                String arrAirportTerminal = req.getArrAirportTerminal();
                if (StringUtils.isNotBlank(arrAirportTerminal)) {
                    waitingPassagerInfo.setArrAirportTerminal(arrAirportTerminal);
                }
                waitingPassagerInfo.setArrivalAirport(req.getArrivalAirport());
                String depAirportTerminal = req.getDepAirportTerminal();
                if (StringUtils.isNotBlank(depAirportTerminal)) {
                    waitingPassagerInfo.setDepAirportTerminal(depAirportTerminal);
                }
                waitingPassagerInfo.setDepartureAirport(req.getDepartureAirport());
                waitingPassagerInfo.setDepTime(req.getDepTime());
                waitingPassagerInfo.setFlightDate(req.getFlightDate());
                String flightId = req.getFlightId();
                if (StringUtils.isNotBlank(flightId)) {
                    waitingPassagerInfo.setFlightId(flightId);
                }
                waitingPassagerInfo.setFlightNo(req.getFlightNo());
                String involuntary = req.getInvoluntary();
                if (StringUtils.isNotBlank(involuntary)) {
                    waitingPassagerInfo.setInvoluntary(involuntary);
                }
                waitingPassagerInfo.setLinkPersonContact(req.getLinkPersonContact());
                waitingPassagerInfo.setLinkPersonName(req.getLinkPersonName());
                waitingPassagerInfo.setPassengerIdcardType(passager.getPassengerIdcardType());
                waitingPassagerInfo.setPassengerIdcard(passager.getPassengerIdcard());
                waitingPassagerInfo.setOrderNum(orderNum);
                waitingPassagerInfo.setPassengerName(passager.getPassengerName());
                waitingPassagerInfo.setPassengerType(passager.getPassengerType());
                //会员级别
                PtMemberDetail memberRecordResponse = queryMemberRecord(waitingPassagerInfo.getPassengerIdcardType(), waitingPassagerInfo.getPassengerIdcard(), request, channelCode);
                String levelCode = "0";
                if (memberRecordResponse != null) {
                    levelCode = memberRecordResponse.getStateInfo().getMemberLevelCode();
                }
                waitingPassagerInfo.setMemberLevel(levelCode);
                //旅客高价值标记
                PtBIgDataResponse<CusBaseLabelResponse> ptBIgDataResponse = queryPassLabel(waitingPassagerInfo.getPassengerIdcard(), waitingPassagerInfo.getPassengerName(), "",request);
                if (ptBIgDataResponse != null && !StringUtil.isNullOrEmpty(ptBIgDataResponse.getData())) {
                    CusBaseLabelResponse cus = ptBIgDataResponse.getData().get(0);
                    if ("是".equals(cus.getCode_list_flag())) {
                        waitingPassagerInfo.setPassengerWorth(1);
                    }
                }
                passagerInfos.add(waitingPassagerInfo);
            }
            for (WaitingPassagerInfo info : passagerInfos) {
                if ("UM".equals(info.getPassengerType())) {
                    info.setPassengerIdcardType("CHD");
                }
            }
        }
        return passagerInfos;
    }

    //查询会员详情
    private PtMemberDetail queryMemberRecord(String certificateType, String certificateNumber, HttpServletRequest request, String channelCode) {
        int certType = CertificateTypeEnum.ID_CARD.getCode();
        PtMemberDetail memberRecordResponse;
        if ("NI".equals(certificateType)) {
            certType = CertificateTypeEnum.ID_CARD.getCode();
        } else if ("PSPT".equals(certificateType)) {
            certType = CertificateTypeEnum.PASSPORT.getCode();
        }
        String[] items = {MemberDetailRequestItemsEnum.BASICINFO.eName
                , MemberDetailRequestItemsEnum.STATEINFO.eName};
        PtApiCRMRequest<PtMemberDetailRequest> ptMemberDetailRequestPtApiCRMRequest = buildMemberDetailReq("" + certType, certificateNumber, request, channelCode, items);
        PtCRMResponse<PtMemberDetail> ptCRMResponse = memberService.memberDetail(ptMemberDetailRequestPtApiCRMRequest);
        String respJson = JsonUtil.objectToJson(ptCRMResponse);
        log.info("【航班候补查询会员等级】证件类型：{}，证件：{}，返回结果：{}", certificateType, certificateNumber, respJson);
        if (ptCRMResponse.getCode() == 0) {
            memberRecordResponse = ptCRMResponse.getData();
        } else {
            return null;
        }
        return memberRecordResponse;

    }

    //查询用户画像
    private PtBIgDataResponse<CusBaseLabelResponse> queryPassLabel(String certNum, String name, String memberCardNo,HttpServletRequest request) {
        PtBIgDataResponse<CusBaseLabelResponse> cusBaseLabelResponse;
        Map paramMap = new HashMap<>();
        paramMap.put("creditCode", certNum);
        paramMap.put("chName", name);
        paramMap.put("pkMemberId", memberCardNo);
        paramMap.put("signature", EncoderHandler.encodeByMD5(HandlerConstants.BIGDATA_CLIENTCODE + memberCardNo + HandlerConstants.BIGDATA_CLIENT_PASSWORD));
        Map<String, String> headMap = HttpUtil.getHeaderMap(getClientIP(request), "");
        HttpResult result = this.doPostClient(paramMap, HandlerConstants.BIGDATD_API_URL + HandlerConstants.BIGDATD_MEMBER_LABEL,headMap, handConfig.getReadTimeout(), handConfig.getConnectTimeout());
        log.info("【航班候补查询会员画像】证件：{}，姓名：{}，卡号：{}，返回结果：{}", certNum, name, memberCardNo, result);
        if (result.isResult() && !StringUtil.isNullOrEmpty(result.getResponse())) {
            try {
                Type type = new TypeToken<PtBIgDataResponse<CusBaseLabelResponse>>() {
                }.getType();
                cusBaseLabelResponse = (PtBIgDataResponse<CusBaseLabelResponse>) JsonUtil.jsonToBean(result.getResponse(), type);
                if ("000000".equals(cusBaseLabelResponse.getCode())) {
                    if (StringUtil.isNullOrEmpty(cusBaseLabelResponse.getData())) {
                        return null;
                    }
                    return cusBaseLabelResponse;
                } else {
                    return null;
                }
            } catch (Exception e) {
                log.error("【航班候补查询会员画像】证件：{}，姓名：{}，卡号：{}，返回结果：{},异常信息：", certNum, name, memberCardNo, result, e);
                return null;
            }
        } else {
            return null;
        }
    }

    private PtApiCRMRequest<PtMemberDetailRequest> buildMemberDetailReq(String certificateType, String certificateNumber, HttpServletRequest request, String channelCode, String[] items) {
        PtMemberDetailRequest ptMemberDetailRequest = new PtMemberDetailRequest();
        ptMemberDetailRequest.setCertificateType(certificateType);
        ptMemberDetailRequest.setCertificateNumber(certificateNumber);
        ptMemberDetailRequest.setRequestItems(items);
        Header header = buildHeader(request, "-1", "");
        PtApiCRMRequest ptApiCRMRequest = new PtApiCRMRequest();
        ptApiCRMRequest.setHeader(header);
        ptApiCRMRequest.setChannel(channelCode);
        ptApiCRMRequest.setChannelPwd(getClientPwd(channelCode));
        ptApiCRMRequest.setData(ptMemberDetailRequest);
        return ptApiCRMRequest;
    }

    //CRM密码
    private String getClientPwd(String channelCode) {
        return getChannelInfo(channelCode, "40");
    }

    @ApiOperation(value = "selectWaitWaitingPassengerNum", notes = "查询候补申请人数（前端暂时用不到的接口）")
    @RequestMapping(value = "/selectWaitWaitingPassengerNum", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp selectWaitWaitingPassengerNum(@RequestBody BaseReq<FlightNum> req, HttpServletRequest request) {
        String reqId = StringUtil.newGUID() + "_selectWaitWaitingPassengerNum";
        BaseResp resp = new BaseResp();
        String ip = this.getClientIP(request);
        try {
            String reqJson = JsonUtil.objectToJson(req);
            log.info("请求号:{}，IP地址:{}，客户端提交参数：{}", reqId, ip, reqJson);
            ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
            Validator validator = factory.getValidator();
            Set<ConstraintViolation<BaseReq>> violations = validator.validate(req);
            if (CollectionUtils.isNotEmpty(violations)) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo(violations.iterator().next().getMessage());
                String respJson = JsonUtil.objectToJson(resp);
                log.info(LOG_RESP_ONE, reqId, ip,respJson );
                return resp;
            }
            // 验证用户查询是否正常
            FlightNum flightNum = req.getRequest();
            boolean flag = this.checkKeyInfo(flightNum.getFfpId(), flightNum.getLoginKeyInfo(), req.getChannelCode());
            if (!flag) {
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                String respJson = JsonUtil.objectToJson(resp);
                log.info(LOG_RESP_ONE, reqId, ip, respJson);
                return resp;
            }

            // 调用旅客服务网查询候补人数
            String serviceResult = passagerPost(flightNum, HandlerConstants.URL_PASSAGER_API + HandlerConstants.WAITINGPASSAGER_SELECTNUM);
            if (!StringUtil.isNullOrEmpty(serviceResult)) {
                WaitingPersonResp waitingPersonResp = (WaitingPersonResp) JsonUtil.jsonToBean(serviceResult, WaitingPersonResp.class);
                if ("0".equals(waitingPersonResp.getCode())) {
                    resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                    resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                    resp.setObjData(waitingPersonResp.getData());
                } else {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo(waitingPersonResp.getMsg());
                    return resp;
                }
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo(LOG_RESP_THREE);
                return resp;
            }


        } catch (Exception e) {
            log.error(LOG_RESP_FOUR, reqId, ip, e.getMessage());
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(LOG_RESP_FIVE);
            log.info(LOG_RESP_ONE, reqId, ip, JsonUtil.objectToJson(resp));
            return resp;
        }
        return resp;
    }


    /**
     * 过滤航班
     */
    private List<WaitingFlightInfo> filterFlightNo(List<WaitingFlightInfo> flightInfos, String flightDateStr) {
        List<WaitingFlightInfo> flightInfoList = new ArrayList<>();
        //特殊时间，可查候补航班过滤
        String key = WAITING_LIMIT_KEY + ":querywaitinglimit";
        String data = apiRedisService.getData(key);
        if (StringUtil.isNullOrEmpty(data)) {
            data = FileUtils.readJson("/querywaitinglimit.json");
            //存到redis中。存储30天
            apiRedisService.replaceData(key, data, 24 * 3600L * 30);
        }
        Map<String, List<WaitingPassgerLimit>> map = (Map<String, List<WaitingPassgerLimit>>) JsonUtil.jsonToMap(data, new TypeToken<HashMap<String, List<WaitingPassgerLimit>>>() {
        }.getType());
        Set<String> keys = map.keySet();
        Date flightDate = DateUtils.toDate(flightDateStr, DATE_FORMAT);
        keys.forEach(k -> {
            String startDateStr = k.substring(0, 10);
            String endDateStr = k.substring(11);
            Date startDate = DateUtils.toDate(startDateStr, DATE_FORMAT);
            Date endDate = DateUtils.toDate(endDateStr, DATE_FORMAT);
            if (!(flightDate.before(startDate) || flightDate.after(endDate))) {
                List<WaitingPassgerLimit> waitingPassgerLimits = map.get(k);
                flightInfos.forEach(flightInfo ->
                    waitingPassgerLimits.forEach(waitingPassgerLimit -> {
                        if (flightInfo.getDepCity().equals(waitingPassgerLimit.getDepCity())
                                && flightInfo.getArrCity().equals(waitingPassgerLimit.getArrCity())
                                && waitingPassgerLimit.getFlightNos().contains(flightInfo.getFlightNo())) {
                            flightInfoList.add(flightInfo);
                        }
                    })
                );
            }
        });
        return flightInfoList;
    }

}

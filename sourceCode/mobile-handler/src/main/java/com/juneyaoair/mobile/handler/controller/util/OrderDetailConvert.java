package com.juneyaoair.mobile.handler.controller.util;


import com.google.common.collect.Lists;
import com.google.common.reflect.TypeToken;
import com.juneyaoair.appenum.av.FareBasisEnum;
import com.juneyaoair.appenum.common.AirCompanyEnum;
import com.juneyaoair.appenum.order.*;
import com.juneyaoair.appenum.rightcoupon.VoucherTypesEnum;
import com.juneyaoair.baseclass.InsureQueryRule;
import com.juneyaoair.baseclass.av.common.PackagePro;
import com.juneyaoair.baseclass.basicsys.response.AirPortInfoDto;
import com.juneyaoair.baseclass.change.ChangeAndRefundRule;
import com.juneyaoair.baseclass.request.av.AircraftModel;
import com.juneyaoair.baseclass.response.insure.InsureInfo;
import com.juneyaoair.baseclass.response.order.comm.*;
import com.juneyaoair.baseclass.response.order.detail.OrderBriefInfo;
import com.juneyaoair.baseclass.response.order.detail.OrderDetailResp;
import com.juneyaoair.baseclass.response.order.query.InsuranceInfo;
import com.juneyaoair.baseclass.response.order.query.SubOrderResp;
import com.juneyaoair.baseclass.response.payment.PayMethod;
import com.juneyaoair.mobile.handler.HandlerConstants;
import com.juneyaoair.mobile.handler.config.bean.HandConfig;
import com.juneyaoair.mobile.handler.controller.v2.util.AVObjectConvertV2;
import com.juneyaoair.mobile.handler.controller.v2.util.AVObjectConvertV3;
import com.juneyaoair.mobile.handler.controller.v2.util.AvObjectConvertCommon;
import com.juneyaoair.mobile.handler.controller.v2.util.CommonUtil;
import com.juneyaoair.mobile.handler.service.LocalCacheService;
import com.juneyaoair.mobile.handler.util.FlightUtil;
import com.juneyaoair.thirdentity.response.order.apply.*;
import com.juneyaoair.utils.StringUtil;
import com.juneyaoair.utils.json.JsonUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.lang.reflect.Type;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;


public class OrderDetailConvert {
    //转换订单详细
    public static OrderDetailResp toOrderDetailReponse(SubOrderResp orderBaseInfo, PtRefundApplyResp resp, LocalCacheService localCacheService, HandConfig config) {
        OrderDetailResp response = new OrderDetailResp();
        BeanUtils.copyProperties(resp, response);
        PtPassengerSegment ps = resp.getPassengerSegmentList().get(0);
        response.setChannelOrderNo(orderBaseInfo.getChannelOrderNo());
        response.setOrderNO(orderBaseInfo.getOrderNo());
        response.setOrderState(orderBaseInfo.getState());
        response.setOrderSort(resp.getOrderSort());
        response.setPayMethodName(orderBaseInfo.getPayMethod() == null ? "" : orderBaseInfo.getPayMethod());
        if (orderBaseInfo.getState().equals("TickedOut")) {
            orderBaseInfo.setState("Finish");
        }
        PayMethod pay =  config.getPayMethod().stream().filter(payMethod -> payMethod.getPayMethodName()
                .equals(response.getPayMethodName())).findFirst().orElse(null);
        if (pay!=null){
            response.setPayDeductAmt(orderBaseInfo.getPayDeductAmt());
        }
        response.setBookingDatetime(orderBaseInfo.getCreateDatetime());
        response.setTicketDatetime(ps.getTicketDatetime());
        response.setIsBooking(!OrderStateEnum.Cancel.getStateCode().equals(orderBaseInfo.getState()));
        response.setIsPaid(PayEnum.Pay.getStateCode().equals(orderBaseInfo.getPayState()));
        response.setIsTicket(StringUtils.isNotEmpty(ps.getTicketState()));
        List<OrderPassengerInfo> passlist = toPassengerInfoList(resp, localCacheService, config, response);
        response.setOrderPassengerInfoList(passlist);
        List<OrderBriefInfo> briefInfoList = new ArrayList<>();
        if (!StringUtil.isNullOrEmpty(resp.getOrderBriefInfoList())) {
            BeanUtils.copyProperties(resp.getOrderBriefInfoList(), briefInfoList);
        }
        //如果为B2C渠道的升舱单，暂时一律认为是改期订单
        if (resp.getOrderSort().equals(OrderSortEnum.Change.getOrderSort()) && resp.getChannelCode().equals("B2C")) {
            resp.setOrderSort(OrderSortEnum.Change.getOrderSort());
        }
        //查询出升舱前后舱位等级和舱位，并对升舱改期航段做出标记
        if (OrderSortEnum.Upgrade.getOrderSort().equals(resp.getOrderSort()) || OrderSortEnum.Change.getOrderSort().equals(resp.getOrderSort())) {
            response.getOrderPassengerInfoList().forEach(orderPassengerInfo -> {
                AtomicInteger index = new AtomicInteger();
                orderPassengerInfo.getSegmentPriceInfoList().forEach(segmentPriceInfo -> {
                    segmentPriceInfo.setCabinClassName(CommonUtil.showCabinClassName(segmentPriceInfo.getCabinClass(), null, resp.getOrderSort()));
                    if (segmentPriceInfo.getSegmentInfoOTO() != null) {
                        //原始舱位信息
                        segmentPriceInfo.setFormatCabin(segmentPriceInfo.getSegmentInfoOTO().getCabin());
                        segmentPriceInfo.setFormatCabinClass(segmentPriceInfo.getSegmentInfoOTO().getCabinClass());
                        segmentPriceInfo.setFormatCabinClassName(CommonUtil.showCabinClassName(segmentPriceInfo.getSegmentInfoOTO().getCabinClass()));
                        if ("Upgrade".equals(resp.getOrderSort())) {
                            if (!segmentPriceInfo.getCabinClass().equals(segmentPriceInfo.getSegmentInfoOTO().getCabinClass())) {
                                segmentPriceInfo.setChangeFlag(true);
                            }
                        } else {
                            if (segmentPriceInfo.getDepAirport().equals(segmentPriceInfo.getSegmentInfoOTO().getDepAirport()) &&
                                    segmentPriceInfo.getArrAirport().equals(segmentPriceInfo.getSegmentInfoOTO().getArrAirport()) &&
                                    segmentPriceInfo.getFlightNo().equals(segmentPriceInfo.getSegmentInfoOTO().getFlightNo()) &&
                                    segmentPriceInfo.getDepDateTime().equals(segmentPriceInfo.getSegmentInfoOTO().getDepDateTime()) &&
                                    segmentPriceInfo.getCabin().equals(segmentPriceInfo.getSegmentInfoOTO().getCabin()) &&
                                    segmentPriceInfo.getCabinClass().equals(segmentPriceInfo.getSegmentInfoOTO().getCabinClass())) {
                                segmentPriceInfo.setChangeFlag(false);
                            } else {
                                segmentPriceInfo.setChangeFlag(true);
                                index.getAndIncrement();
                            }
                        }
                    }
                });
                if (index.get() == 0 && resp.getOrderSort().equals("Change")) {
                    orderPassengerInfo.getSegmentPriceInfoList().forEach(segmentPriceInfo ->
                            segmentPriceInfo.setChangeFlag(true)
                    );
                }
            });
        } else {
            //非升舱改期航班处理
            response.getOrderPassengerInfoList().forEach(orderPassengerInfo ->
                    orderPassengerInfo.getSegmentPriceInfoList().forEach(segmentPriceInfo ->
                            segmentPriceInfo.setCabinClassName(CommonUtil.showCabinClassName(segmentPriceInfo.getCabinClass()))
                    )
            );
        }
        // 设置订单优惠券信息
        response.setOrderCouponList(Lists.newArrayList());
        response.setUnlimitedCouponList(Lists.newArrayList());
        if (CollectionUtils.isNotEmpty(resp.getOrderCouponList())) {
            // 畅飞卡1.0不在此处处理
            resp.getOrderCouponList().stream().filter(couponInfo -> !("UnlimitFlyCard".equals(couponInfo.getCouponType())
                    && !VoucherTypesEnum.UNLIMITED_FLY_V2.getCode().equals(couponInfo.getCouponSource())
                    && !VoucherTypesEnum.UNLIMITED_FLY_V2_SF.getCode().equals(couponInfo.getCouponSource()))).forEach(couponInfo -> {
                OrderCouponInfo orderCouponInfo = new OrderCouponInfo();
                org.springframework.beans.BeanUtils.copyProperties(couponInfo, orderCouponInfo);
                orderCouponInfo.setNumber(1);
                if ("ThemeCard".equals(couponInfo.getCouponType())) {
                    orderCouponInfo.showCouponName(couponInfo.getCouponSource(), FlightUtil.toThemeModelMap(config.getThemeCabinLabel()));
                    response.getUnlimitedCouponList().add(orderCouponInfo);
                } else {
                    if (VoucherTypesEnum.UNLIMITED_FLY_V2_SF.getCode().equals(couponInfo.getCouponSource())
                            || VoucherTypesEnum.UNLIMITED_FLY_V2.getCode().equals(couponInfo.getCouponSource())) {
                        // 畅飞卡2.0展示在畅飞卡的list中
                        orderCouponInfo.setCouponName("吉祥畅飞卡");
                        orderCouponInfo.setCouponSource(VoucherTypesEnum.UNLIMITED_FLY_V2.getName());
                        response.getUnlimitedCouponList().add(orderCouponInfo);
                    } else {
                        if (VoucherTypesEnum.UPGRADE.getCode().equalsIgnoreCase(couponInfo.getCouponType())) {
                            orderCouponInfo.setCouponName("升舱券");
                        } else if (VoucherTypesEnum.RESCHEDULECOUPON.getCode().equals(couponInfo.getCouponType())) {
                            orderCouponInfo.setCouponName("改期券");
                        } else if (VoucherTypesEnum.EXPIRING_CARD.getCode().equals(couponInfo.getCouponSource())) {
                            orderCouponInfo.setCouponName(VoucherTypesEnum.EXPIRING_CARD.getName());
                            orderCouponInfo.setCouponType(VoucherTypesEnum.EXPIRING_CARD.getCode());
                        }
                        // 前端展示优惠券使用类型
                        orderCouponInfo.setCouponType(StringUtils.isBlank(couponInfo.getCouponUseType()) ? orderCouponInfo.getCouponType() : couponInfo.getCouponUseType());
                        response.getOrderCouponList().add(orderCouponInfo);
                    }
                }
            });
        }
        return response;
    }

    //乘客信息
    public static List<OrderPassengerInfo> toPassengerInfoList(PtRefundApplyResp resp, LocalCacheService localCacheService, HandConfig config, OrderDetailResp response) {
        List<OrderPassengerInfo> passlist = new ArrayList<>();
        //giveCouponInfos为该订单所有赠送的券
        Map<String, OrderGiveCouponInfo> giveCouponInfos = new HashMap<>();
        for (PtOrderPassengerInfo ptPass : resp.getPassengerInfoList()) {
            OrderPassengerInfo orderPass = new OrderPassengerInfo();
            BeanUtils.copyProperties(ptPass, orderPass);
            orderPass.setSegmentPriceInfoList(toSegmentPriceInfoList(orderPass.getPassengerID(), resp, localCacheService, config));

            orderPass.setTicketOrderNo(resp.getTicketOrderNo());
            orderPass.setPrivilegeList(Lists.newArrayList());
            String isBuyInsur = "N";
            Double insureAmt = 0.0;
            double changeCouponAmount = 0.0; //改期券抵扣金额
            for (PtPassengerSegment ptPassengerSegment : resp.getPassengerSegmentList()) {
                if (ptPassengerSegment.getPassengerID() == ptPass.getPassengerID()) {
                    if (OrderSortEnum.Change.getOrderSort().equals(resp.getOrderSort())) {
                        changeCouponAmount += ptPassengerSegment.getCouponAmount();
                        orderPass.setUpgradeFee(orderPass.getUpgradeFee());
                        orderPass.setChangeCouponAmount(changeCouponAmount);
                    }
                    // 退票标签
                    RefundLabelEnum refundLabelEnum = null;
                    if (StringUtils.isNotBlank(ptPassengerSegment.getRefundState())) {
                        refundLabelEnum = RefundLabelEnum.Refunding;
                    }
                    if (RefundRebateStateEnum.Success.getCode().equals(ptPassengerSegment.getRefundRebateState())) {
                        refundLabelEnum = RefundLabelEnum.Refunded;
                    }
                    if (StringUtils.isBlank(orderPass.getRefundLabel()) && null != refundLabelEnum) {
                        orderPass.setRefundLabel(refundLabelEnum.getCode());
                    }
                    orderPass.setDiscountAmount(ptPassengerSegment.getDiscountAmount());
                    orderPass.setRefundState(ptPassengerSegment.getRefundState());
                    orderPass.setRefundRebateState(ptPassengerSegment.getRefundRebateState());
//                    orderPass.setOriginalChangeFee(ptPassengerSegment.getOriginalChangeFee());
                    if (CollectionUtils.isNotEmpty(ptPassengerSegment.getPrivilegeList())) {
                        for (int i = 0; i < ptPassengerSegment.getPrivilegeList().size(); i++) {
                            ptPassengerSegment.getPrivilegeList().get(i).setSortPriority(i);
                        }
                        orderPass.getPrivilegeList().addAll(ptPassengerSegment.getPrivilegeList());
                    }
                }
            }
            for (SegmentPriceInfo segPrc : orderPass.getSegmentPriceInfoList()) {
                if (segPrc.getIsBuyInsurance()) {
                    isBuyInsur = "Y";
                }
                insureAmt += (segPrc.getIsBuyInsurance() ? segPrc.getInsuranceAmount() : 0.0);
                if (StringUtils.isNotBlank(segPrc.getCarrierFlightNo()) && segPrc.getCarrierFlightNo().startsWith("MU")) {
                    segPrc.setSaleInfo("中国东方航空承运");
                }
            }
            orderPass.setIsBuyInsurance(isBuyInsur);
            orderPass.setInsuranceAmount(insureAmt);
            orderPass.setETicketNo(setETicketNo(orderPass.getSegmentPriceInfoList()));
            List<TaxInfo> taxList = new ArrayList<>();
            for (int tax = 0; tax < ptPass.getTaxInfoList().length; tax++) {
                TaxInfo taxInfo = new TaxInfo();
                PtTaxInfo ptTaxInfo = ptPass.getTaxInfoList()[tax];
                BeanUtils.copyProperties(ptTaxInfo, taxInfo);
                taxList.add(taxInfo);
            }
            orderPass.setTaxInfoList(taxList);

            //旅客保险汇总
            Map<String, List<InsuranceInfo>> insureMap = new HashMap<>();
            for (SegmentPriceInfo segmentPriceInfo : orderPass.getSegmentPriceInfoList()) {
                if (OrderSortEnum.Upgrade.getOrderSort().equals(resp.getOrderSort())) {//升舱券抵扣金额
                    segmentPriceInfo.setUpgradeCouponAmount(segmentPriceInfo.getCouponAmount());
                }
                if (StringUtil.isNullOrEmpty(segmentPriceInfo.getInsuranceList())) {
                    continue;
                }
                for (InsuranceInfo insuranceInfo : segmentPriceInfo.getInsuranceList()) {
                    InsuranceInfo curInsurInfo = new InsuranceInfo();
                    BeanUtils.copyProperties(insuranceInfo, curInsurInfo);
                    if (insureMap.containsKey(curInsurInfo.getInsuranceCode())) {
                        List<InsuranceInfo> insurList = insureMap.get(curInsurInfo.getInsuranceCode());
                        insurList.add(curInsurInfo);
                        insureMap.put(curInsurInfo.getInsuranceCode(), insurList);
                    } else {
                        List<InsuranceInfo> insurList = new ArrayList<>();
                        insurList.add(curInsurInfo);
                        insureMap.put(curInsurInfo.getInsuranceCode(), insurList);
                    }
                }
            }
            if (insureMap.size() > 0) {
                List<InsuranceInfo> insurList = new ArrayList<>();
                for (Map.Entry<String, List<InsuranceInfo>> entry : insureMap.entrySet()) {
                    Double insuranceNumber = 0.0;
                    Double insuranceAmount = 0.0;
                    String insureCode = "";
                    String insureNm = "";
                    String insuranceBillNo = "";
                    String insuranceState = "";
                    for (InsuranceInfo insuranceInfo : entry.getValue()) {
                        insureCode = insuranceInfo.getInsuranceCode();
                        insuranceNumber += insuranceInfo.getInsuranceNumber();
                        insuranceAmount += insuranceInfo.getInsuranceAmount();
                        insuranceBillNo = insuranceInfo.getInsuranceBillNo();
                        insuranceState = insuranceInfo.getInsuranceState();
                    }
                    insureNm = getInsureNm(resp.getInterFlag(), resp.getRouteType(), insureCode, config);
                    InsuranceInfo curInsuerInfo = new InsuranceInfo(insureCode, insureNm, insuranceNumber, insuranceAmount, insuranceState);
                    curInsuerInfo.setInsuranceBillNo(insuranceBillNo);
                    insurList.add(curInsuerInfo);
                }
                orderPass.setInsuranceList(insurList);
            }
            //国际税费处理
            if (HandlerConstants.TRIP_TYPE_I.equals(resp.getInterFlag())) {
                Double taxAmount = 0.0;
                for (TaxInfo taxInfo : orderPass.getTaxInfoList()) {
                    taxAmount += taxInfo.getTaxAmount();
                }
                orderPass.getSegmentPriceInfoList().get(0).setXTax(taxAmount);
            }
            //处理每人购买机票赠送的券
            Map<String, OrderGiveCouponInfo> passGiveCouponInfos = new HashMap<>();
            for (PtPassengerSegment ptPassengerSegment : resp.getPassengerSegmentList()) {
                if (ptPassengerSegment.getPassengerID() == ptPass.getPassengerID() && !StringUtil.isNullOrEmpty(ptPassengerSegment.getFareBasis())) {
                    PackagePro packagePro = getPackagePro(config, ptPassengerSegment.getFareBasis());
                    if (packagePro != null) {
                        //根据人航段的farebasis将不同优惠券进行分组，累加
                        getGiveCouponInfos(passGiveCouponInfos, packagePro);
                        getGiveCouponInfos(giveCouponInfos, packagePro);
                    }
                }
                //标记往返特惠
                if ("8".equals(ptPassengerSegment.getPriceProductType())) {
                    response.setOwDiscountFlag(true);
                }
            }
            //map的值转化为List
            List<OrderGiveCouponInfo> passGivelist = new ArrayList<>();
            passGiveCouponInfos.keySet().forEach(s ->
                    passGivelist.add(passGiveCouponInfos.get(s))
            );
            orderPass.setPassGiveCouponInfos(passGivelist);
            passlist.add(orderPass);
        }
        if (response != null) {
            //map的值转化为List
            List<OrderGiveCouponInfo> list = new ArrayList<>();
            giveCouponInfos.keySet().forEach(s ->
                    list.add(giveCouponInfos.get(s))
            );
            response.setGiveCouponInfos(list);
        }
        return passlist;
    }


    //支持多票号的处理
    private static String setETicketNo(List<SegmentPriceInfo> segmentPriceInfoList) {
        String defaultTicketNo = segmentPriceInfoList.get(0).getETicketNo() == null ? "" : segmentPriceInfoList.get(0).getETicketNo();
        if (segmentPriceInfoList.size() > 1) {
            for (int i = 1; i < segmentPriceInfoList.size(); i++) {
                String curTicketNo = segmentPriceInfoList.get(i).getETicketNo() == null ? "" : segmentPriceInfoList.get(i).getETicketNo();
                if (!defaultTicketNo.contains(curTicketNo)) {
                    defaultTicketNo = defaultTicketNo + "/" + curTicketNo;
                }
            }
        }
        return defaultTicketNo;
    }

    /**
     * 将结果put到map中
     * map key为String， price 为优惠券名称和数量的对象
     *
     * @param passGiveCouponInfos
     * @param packagePro
     */
    public static void getGiveCouponInfos(Map<String, OrderGiveCouponInfo> passGiveCouponInfos, PackagePro packagePro) {
        if (passGiveCouponInfos.isEmpty() || passGiveCouponInfos.get(packagePro.getProCode()) == null) {
            OrderGiveCouponInfo orderGiveCouponInfo = new OrderGiveCouponInfo();
            orderGiveCouponInfo.setCouponName(packagePro.getProDesc());
            orderGiveCouponInfo.setCount(1);
            passGiveCouponInfos.put(packagePro.getProCode(), orderGiveCouponInfo);
        } else {
            OrderGiveCouponInfo orderGiveCouponInfo = passGiveCouponInfos.get(packagePro.getProCode());
            orderGiveCouponInfo.setCount(orderGiveCouponInfo.getCount() + 1);
            passGiveCouponInfos.put(packagePro.getProCode(), orderGiveCouponInfo);
        }
    }

    //人下航段
    public static List<SegmentPriceInfo> toSegmentPriceInfoList(int passengerID, PtRefundApplyResp resp, LocalCacheService localCacheService, HandConfig handConfig) {
        List<SegmentPriceInfo> segPrc = new ArrayList<>();
        List<PtPassengerSegment> curSegList = new ArrayList<>();
        //人对应的航段价格信息
        //统计共有多少张优惠券或者wifi券
        for (PtPassengerSegment ptPassSeg : resp.getPassengerSegmentList()) {
            if (ptPassSeg.getPassengerID() == passengerID) {
                curSegList.add(ptPassSeg);
            }
        }
        for (PtPassengerSegment ptPassengerSegment : curSegList) {
            //航段信息
            PtSegmentPriceInfo ptSegPrc = getSegmentPrc(resp.getSegmentInfoList(), ptPassengerSegment.getSegmentID());
            PtPassengerSegment ptSeg = getSegment(resp.getPassengerSegmentList(), ptPassengerSegment.getPassengerID(),
                    ptPassengerSegment.getSegmentID());
            Boolean insureApplySuccess = HandlerConstants.INSURE_STATE_SUCCESS.equals(ptSeg == null ? null : ptSeg.getInsuranceState())
                    || HandlerConstants.INSURE_STATE_APPLY.equals(ptSeg == null ? null : ptSeg.getInsuranceState());
            SegmentPriceInfo segmentPriceInfo = new SegmentPriceInfo();
            if (ptPassengerSegment.isIsUseScore() && ptPassengerSegment.getPricePaid() == 0) {
                segmentPriceInfo.setUseUpgradeCouponFlag(true);
            }
            if (ptSegPrc != null && null != ptSeg) {
                BeanUtils.copyProperties(ptSegPrc, segmentPriceInfo);
                BeanUtils.copyProperties(ptSeg, segmentPriceInfo);
                AirPortInfoDto depAirportInfo = localCacheService.getLocalAirport(ptSegPrc.getDepAirport());
                AirPortInfoDto arrAirportInfo = localCacheService.getLocalAirport(ptSegPrc.getArrAirport());
                segmentPriceInfo.setDepCityName(depAirportInfo == null ? "" : depAirportInfo.getCityName());
                segmentPriceInfo.setArrCityName(arrAirportInfo == null ? "" : arrAirportInfo.getCityName());
                segmentPriceInfo.setDepAirportName(depAirportInfo == null ? ptSegPrc.getDepAirport() : depAirportInfo.getAirPortName());
                segmentPriceInfo.setArrAirportName(arrAirportInfo == null ? ptSegPrc.getArrAirport() : arrAirportInfo.getAirPortName());
                //应付金额 ?联程航班会重复计算
                segmentPriceInfo.setAmount(ptSeg.getPricePaid() + ptSeg.getYQTax() + ptSeg.getCNTax() + (insureApplySuccess ? ptSeg.getInsuranceAmount() : 0.0));
                segmentPriceInfo.setID(ptSeg.getPassengerSegmentID());
                segmentPriceInfo.setUseScore(ptSeg.getDeductibls());
                segmentPriceInfo.setCouponAmount(ptSeg.getCouponAmount());
                segmentPriceInfo.setGiftScore(ptSeg.getGifiScore());
                segmentPriceInfo.setETicketNo(ptSeg.getTicketNo());
                segmentPriceInfo.setTKTStatus(ptSeg.getTicketState());
                segmentPriceInfo.setStopNumber(ptSegPrc.getStopNumber() + "");
                //奖励飞退改规则修改 免票I,N处理
                String[] awardFlyFreeTicketCabin = handConfig.getAwardFlyFreeTicketCabin().split(",");
                for (String s : awardFlyFreeTicketCabin) {
                    if (StringUtils.isNotBlank(segmentPriceInfo.getCabin()) && s.equals(segmentPriceInfo.getCabin())
                            &&segmentPriceInfo.getFlightNo().startsWith(AirCompanyEnum.HO.getAirCompanyCode())) {
                        ptSeg.setRefundedRules(null);
                    }
                }

                //解析退改规则
                List<ChangeAndRefundRule> changeRuleList = AVObjectConvertV2.toChangeRules(ptSeg.getChangeRules(), resp.getInterFlag());
                List<ChangeAndRefundRule> refundRuleList = AVObjectConvertV2.toRefundRules(ptSeg.getRefundedRules(), resp.getInterFlag());
                FlightUtil.completeRule(refundRuleList, changeRuleList);
                // 免票舱处理
                if (Arrays.asList(awardFlyFreeTicketCabin).contains(segmentPriceInfo.getCabin())) {
                    refundRuleList.stream().filter(changeAndRefundRule -> -1d == changeAndRefundRule.getChangeFee()).forEach(changeAndRefundRule -> changeAndRefundRule.setRuleDesc("以兑换卡券规则为准"));
                }
                segmentPriceInfo.setChangeRuleList(changeRuleList);
                segmentPriceInfo.setRefundRuleList(refundRuleList);
                //航班图标处理
                segmentPriceInfo.setFlightNoIconList(AVObjectConvertV3.setFlightNoIconList(segmentPriceInfo.getFlightNo(), handConfig.getAirCompany()));
                segmentPriceInfo.setCarrierFlightNoIconList(AvObjectConvertCommon.setFlightNoIconList(handConfig.getAirCompany(), segmentPriceInfo.getCarrierFlightNo()));
                //机型处理
                Map<String, AircraftModel> aircraftModelMap = FlightUtil.toAircraftModelMap(handConfig.getAircraftModel());
                AircraftModel aircraftModel = FlightUtil.convertAircraftModel(aircraftModelMap, segmentPriceInfo.getPlaneStyle());
                segmentPriceInfo.setAircraftModel("机型" + segmentPriceInfo.getPlaneStyle());
                if (aircraftModel != null) {
                    segmentPriceInfo.setAircraftModel(aircraftModel.getRemark());
                }
                if (ptSeg.getSegmentInfoOTO() != null) {
                    if (ptSeg.getSegmentInfoOTO().getUpgradeTicketPrice() != null) {
                        segmentPriceInfo.setUpgradeTicketPrice(ptSeg.getSegmentInfoOTO().getUpgradeTicketPrice());
                    }
                } else {
                    segmentPriceInfo.setUpgradeTicketPrice(0.0);
                }
            }
            segmentPriceInfo.setIsBuyInsurance(insureApplySuccess);
            if (!StringUtil.isNullOrEmpty(segmentPriceInfo.getInsuranceList())) {
                for (InsuranceInfo insure : segmentPriceInfo.getInsuranceList()) {
                    insure.setInsuranceName(getInsureNm(resp.getInterFlag(), resp.getRouteType(), insure.getInsuranceCode(), handConfig));
                }
            }
            segPrc.add(segmentPriceInfo);
        }
        return segPrc;
    }

    //获取指定航线
    public static PtSegmentPriceInfo getSegmentPrc(List<PtSegmentPriceInfo> segPrcList, int segId) {
        for (PtSegmentPriceInfo segmentPriceInfo : segPrcList) {
            if (segId == segmentPriceInfo.getSegmentID()) {
                return segmentPriceInfo;
            }
        }
        return null;
    }

    //获取人航段
    public static PtPassengerSegment getSegment(List<PtPassengerSegment> passSeg, int passengerID, int segId) {
        for (PtPassengerSegment passengerSegment : passSeg) {
            if (segId == passengerSegment.getSegmentID() && passengerID == passengerSegment.getPassengerID()) {
                return passengerSegment;
            }
        }
        return null;
    }

    //获取保险名
    public static String getInsureNm(String interFlag, String routeType, String insureCode, HandConfig handConfig) {
        List<InsureQueryRule> insureQueryRules = (List<InsureQueryRule>)
                JsonUtil.jsonToBean(handConfig.getInsureQueryRules(), new TypeToken<List<InsureQueryRule>>() {
                }.getType());
        for (InsureQueryRule insureQueryRule : insureQueryRules) {
            List<InsureInfo> insureList = insureQueryRule.getInsureList();
            final int insurtListSize = insureList.size();
            for (int i = 0; i < insurtListSize; i++) {
                if ("Y".equalsIgnoreCase(insureList.get(i).getIsMultSpec())) {
                    for (com.juneyaoair.baseclass.request.booking.InsuranceInfo insuranceInfo : insureList.get(i).getInsuranceInfo()) {
                        InsureInfo copyInsure = new InsureInfo();
                        com.juneyaoair.utils.util.BeanUtils.copyNotNullProperties(insureList.get(i), copyInsure);
                        copyInsure.setInsId(insuranceInfo.getInsuranceCode());
                        copyInsure.setInsNm(insureList.get(i).getInsNm() + "(" + insuranceInfo.getInsuranceSpec() + ")");
                        insureList.add(copyInsure);
                    }
                }
            }
        }
        Set<InsureInfo> insureInfos = new HashSet<>();
        insureQueryRules.stream().filter(insureQueryRule -> doStringListFilter(insureQueryRule.getTripTypes(), interFlag))
                .filter(insureQueryRule -> doStringListFilter(insureQueryRule.getFlightTypes(), routeType))
                .forEach(insureQueryRule ->
                        insureQueryRule.getInsureList().stream()
                                .filter(insureInfo -> StringUtils.isNotBlank(insureInfo.getInsId()))
                                .forEach(insureInfo -> insureInfos.add(insureInfo)));
        Optional<InsureInfo> insureInfo = insureInfos.stream().filter(insure -> insure.getInsId().equals(insureCode)).findFirst();
        return insureInfo.isPresent() ? insureInfo.get().getInsNm() : null;
    }

    private static boolean doStringListFilter(List<String> list, String source) {
        if (CollectionUtils.isEmpty(list)) {
            return true;
        } else {
            return list.contains(source);
        }
    }

    //获取机票赠送的券名称
    public static PackagePro getPackagePro(HandConfig config, String key) {
        if (key.endsWith(FareBasisEnum.COUPON_FAREBASIS.getFareBasisCode())) {
            key = FareBasisEnum.COUPON_FAREBASIS.getFareBasisCode();
        }
        String packageProStr = config.getPackagePro();
        Type type = new TypeToken<Map<String, PackagePro>>() {
        }.getType();
        Map<String, PackagePro> map = (Map<String, PackagePro>) JsonUtil.jsonToBean(packageProStr, type);
        return map.get(key);
    }

}

package com.juneyaoair.mobile.limiter.interceptor;

import com.alibaba.fastjson.JSON;
import com.juneyaoair.appenum.WSEnum;
import com.juneyaoair.baseclass.common.response.BaseResp;
import com.juneyaoair.common.dto.base.BaseResultDTO;
import com.juneyaoair.exception.CommonException;
import com.juneyaoair.mobile.handler.config.bean.HandConfig;
import com.juneyaoair.mobile.limiter.RequestLimiter;
import io.github.resilience4j.ratelimiter.RateLimiter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.PrintWriter;

/**
 * @Author: caolei
 * @Description: 限流功能实现
 * @Date: 2022/8/11 14:13
 * @Modified by:
 */
@Slf4j
public class RateLimiterInterceptor implements HandlerInterceptor {

    @Setter
    private HandConfig handConfig;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        try {
            // 非方法不做处理
            if (!(handler instanceof HandlerMethod)) {
                return true;
            }
            HandlerMethod handlerMethod = (HandlerMethod) handler;
            // 获取RequestLimiter注解
            RequestLimiter rateLimit = handlerMethod.getMethodAnnotation(RequestLimiter.class);
            // 方法无注解 返回true
            if (null == rateLimit) {
                return true;
            }
            String typeCode = rateLimit.typeCode();
            RateLimiter rateLimiter = handConfig.getRateLimiter(typeCode);
            if (null == rateLimiter){
                return true;
            }
            // 限流器指标：等待线程数，可用令牌数
            // RateLimiter.Metrics metrics = rateLimiter.getMetrics();
            // log.info("typeCode:{} 可用令牌数:{}, 等待线程数: {}", typeCode, metrics.getAvailablePermissions(), metrics.getNumberOfWaitingThreads());
            // acquirePermission阻塞的获取给定数量的令牌，直到获取到令牌或者线程发生中断
            boolean acquire = rateLimiter.acquirePermission();
            if (acquire) {
                return true;
            }
            makeResult(response, "1.超出访问限制，请稍后再试！");
            return false;
        } catch (CommonException ce){
            makeResult(response, ce.getMessage());
            return false;
        } catch (Exception e) {
            log.error("限流RateLimiterInterceptor出现异常，异常原因：", e);
            makeResult(response, "2.超出访问限制，请稍后再试！");
            return false;
        }
    }

    private void makeResult(HttpServletResponse response, String errorMessage) {
        response.setContentType("application/json; charset=utf-8");
        response.setCharacterEncoding("UTF-8");
        try (PrintWriter out = response.getWriter()) {
            BaseResp<String> result = new BaseResp<>();
            result.setResultCode(WSEnum.ERROR.getResultCode());
            result.setResultInfo(errorMessage);
            out.append(JSON.toJSONString(result));
        } catch (Exception e) {
            log.error("限流RateLimiterInterceptor出现异常，异常原因：", e);
            throw new CommonException(WSEnum.ERROR.getResultCode(), "4.超出访问限制，请稍后再试！");
        }
    }


    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) {

    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {

    }
}
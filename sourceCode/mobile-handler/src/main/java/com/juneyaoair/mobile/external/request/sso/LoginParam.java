package com.juneyaoair.mobile.external.request.sso;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

/**
 * @author: caolei
 * @description: 请求参数
 */
@Data
public class LoginParam {

    @NotBlank(message = "业务参数不能为空")
    @ApiModelProperty(value = "业务参数", required = true)
    private String bizContent;

    @NotBlank(message = "签名不能为空")
    @ApiModelProperty(value = "签名", required = true)
    private String sign;

}
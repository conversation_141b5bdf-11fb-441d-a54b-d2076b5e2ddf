package com.juneyaoair.mobile.limiter;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * @Author: caolei
 * @Description: 令牌桶配置参数
 * @Date: 2022/8/11 14:11
 * @Modified by:
 */
@Data
public class RateLimiterInfo {

    @ApiModelProperty(value = "请求被限流时，线程等待的时间 单位：毫秒")
    private Long timeoutDuration;

    @ApiModelProperty(value = "令牌刷新的周期（period），单位：秒")
    private Long limitRefreshPeriod;

    @ApiModelProperty(value = "每个（period）周期内的令牌数")
    private Integer limitForPeriod;
}

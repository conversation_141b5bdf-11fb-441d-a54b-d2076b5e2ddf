package com.juneyaoair.mobile.handler.controller;

import com.juneyaoair.appenum.WSEnum;
import com.juneyaoair.baseclass.common.request.BaseReq;
import com.juneyaoair.baseclass.common.response.BaseResp;
import com.juneyaoair.baseclass.response.chinese.MemberName;
import com.juneyaoair.baseclass.response.chinese.NamePinyin;
import com.juneyaoair.mobile.handler.bean.chinese.NameParts;
import com.juneyaoair.mobile.handler.controller.util.ChineseUtils;
import com.juneyaoair.utils.StringUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description:
 * @Version: V1.0
 * created by Zhang<PERSON>ingS<PERSON>ng on 2021/6/11 14:21
 */
@RestController
@RequestMapping("/chinese")
@Api(value = "ChineseController", tags = {"中文处理"})
public class ChineseController extends BassController {

    @ApiOperation(value = "将姓名拆分单独的姓和名", notes = "将姓名拆分单独的姓和名")
    @RequestMapping(value = "/splitName", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp<NamePinyin> splitName(@RequestBody BaseReq<String> req, HttpServletRequest request) {
        BaseResp<NamePinyin> resp = new BaseResp<>();
        String name = req.getRequest();
        if (StringUtil.isNullOrEmpty(name)) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("内容不能为空");
            resp.setErrorInfo("内容不能为空");
            return resp;
        }
        NamePinyin namePinyin = new NamePinyin();
        namePinyin.setName(name);
        //拆分姓名
        String[] strings = ChineseUtils.splitName(name);
        namePinyin.setXing(strings[0]);
        namePinyin.setMing(strings[1]);

        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
        resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
        resp.setObjData(namePinyin);
        return resp;
    }


    @ApiOperation(value = "根据汉字获取拼音的组合", notes = "根据汉字获取拼音的组合")
    @RequestMapping(value = "getPinyinGroup", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp<List<String>> groupPinyin(@RequestBody BaseReq<String> req, HttpServletRequest request) {
        BaseResp<List<String>> resp = new BaseResp<>();
        String hanzi = req.getRequest();
        if (StringUtil.isNullOrEmpty(hanzi)) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("内容不能为空");
            resp.setErrorInfo("内容不能为空");
            return resp;
        }
        List<String> pinyinGroup = ChineseUtils.getPinyinGroup(hanzi);

        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
        resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
        resp.setObjData(pinyinGroup);
        return resp;
    }

    @ApiOperation(value = "根据姓名拆分成具体姓和名并获取拼音", notes = "根据姓名拆分成具体姓和名并获取拼音")
    @RequestMapping(value = "/splitNameGetPinyin", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp<NamePinyin> splitNameGetPinyin(@RequestBody BaseReq<String> req, HttpServletRequest request) {
        BaseResp<NamePinyin> resp = new BaseResp<>();
        String name = req.getRequest();
        if (StringUtil.isNullOrEmpty(name)) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("内容不能为空");
            resp.setErrorInfo("内容不能为空");
            return resp;
        }
        NamePinyin namePinyin = new NamePinyin();
        namePinyin.setName(name);
        //拆分姓名
        String[] strings = ChineseUtils.splitName(name);
        namePinyin.setXing(strings[0]);
        namePinyin.setMing(strings[1]);
        //获取拼音
        namePinyin.setXingPinyin(ChineseUtils.getPinyinGroup(strings[0]));
        namePinyin.setMingPinyin(ChineseUtils.getPinyinGroup(strings[1]));

        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
        resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
        resp.setObjData(namePinyin);
        return resp;
    }

    @ApiOperation(value = "根据姓名拆分多音字", notes = "根据姓名拆分成具体姓和名并获取拼音")
    @RequestMapping(value = "/splitNameGroupPinyin", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp<MemberName> splitNameGroupPinyin(@RequestBody BaseReq<String> req, HttpServletRequest request) {
        BaseResp<MemberName> resp = new BaseResp<>();
        String name = req.getRequest();
        if (StringUtil.isNullOrEmpty(name)) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("内容不能为空");
            resp.setErrorInfo("内容不能为空");
            return resp;
        }
        if (!ChineseUtils.isAllChinese(name)) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("中文姓名格式错误");
            resp.setErrorInfo("中文姓名格式错误");
            return resp;
        }
        if (name.length() > 10) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("中文姓名字符超过10个，请您手动填写”");
            resp.setErrorInfo("中文姓名字符超过10个，请您手动填写");
            return resp;
        }
        MemberName namePinyin = new MemberName();
        namePinyin.setName(name);
        Map<Integer, List<String>> namePinyinMap = new HashMap<>();

        Map<Integer, List<String>> surnamePinyinMap = new HashMap<>();
        //拆分姓名
        NameParts splitName = ChineseUtils.splitFullName(name);
        namePinyin.setLastNames(splitName.getSurnames());
        namePinyin.setFirstNames(splitName.getGivenNames());
        for (int i = 0; i < splitName.getSurnames().size(); i++) {
            surnamePinyinMap.put(i, ChineseUtils.getPinyinGroup(splitName.getSurnames().get(i)));
        }

        for (int i = 0; i < splitName.getGivenNames().size(); i++) {
            namePinyinMap.put(i, ChineseUtils.getPinyinGroup(splitName.getGivenNames().get(i)));
        }
        namePinyin.setFirstNamePinyinMap(namePinyinMap);
        namePinyin.setLastNamePinyinMap(surnamePinyinMap);
        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
        resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
        resp.setObjData(namePinyin);
        return resp;
    }


}
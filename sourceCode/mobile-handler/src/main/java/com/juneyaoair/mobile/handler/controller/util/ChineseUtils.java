package com.juneyaoair.mobile.handler.controller.util;

import com.alibaba.fastjson.JSON;
import com.juneyaoair.mobile.handler.bean.chinese.NameParts;
import com.juneyaoair.utils.StringUtil;
import net.sourceforge.pinyin4j.PinyinHelper;
import net.sourceforge.pinyin4j.format.HanyuPinyinCaseType;
import net.sourceforge.pinyin4j.format.HanyuPinyinOutputFormat;
import net.sourceforge.pinyin4j.format.HanyuPinyinToneType;
import net.sourceforge.pinyin4j.format.HanyuPinyinVCharType;
import net.sourceforge.pinyin4j.format.exception.BadHanyuPinyinOutputFormatCombination;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Version: V1.0
 * created by ZhangJingShuang on 2021/6/3 18:41
 */
public class ChineseUtils{


    //复姓map，用作校验
    private static String[] name = { "欧阳", "太史", "上官", "端木", "司马", "东方", "独孤", "南宫", "万俟", "闻人", "夏侯", "诸葛", "尉迟", "公羊", "赫连", "澹台",
            "皇甫", "宗政", "濮阳", "公冶", "太叔", "申屠", "公孙", "慕容", "仲孙", "钟离", "长孙", "宇文", "司徒", "鲜于", "司空", "闾丘", "子车",
            "亓官", "司寇", "巫马", "公西", "颛孙", "壤驷", "公良", "漆雕", "乐正", "宰父", "谷梁", "拓跋", "夹谷", "轩辕", "令狐", "段干", "百里",
            "呼延", "东郭", "南门", "羊舌", "微生", "公户", "公玉", "公仪", "梁丘", "公仲", "公上", "公门", "公山", "公坚", "左丘", "公伯", "西门",
            "公祖", "第五", "公乘", "贯丘", "公皙", "南荣", "东里", "东宫", "仲长", "子书", "子桑", "即墨", "达奚", "褚师", "吴铭","贺兰","完颜"};
    private static Set<String> twoXingSet = new HashSet<>();
    static {
        twoXingSet.addAll(Arrays.asList(name));
    }

    /**
     * 姓名转换成单独的姓和名
     * @param name
     * @return 数组，下标0为姓，下标1为名。
     */
    public static String[] splitName(String name){
        if (name.contains("/")){
            String[] split = name.split("/");
            if (split.length >= 2){
                return split;
            }else if (split.length == 1){
                split = new String[]{split[0], ""};
                return split;
            }else {
                return new String[]{"",""};
            }
        }

        String[] xingMing = new String[2];
        if (name == null || name.length()<=1){
            xingMing[0] = name;
            xingMing[1] = "";
            return xingMing;
        }
        if (name.length() == 2){
            xingMing[0] = String.valueOf(name.charAt(0));
            xingMing[1] = String.valueOf(name.charAt(1));
            return xingMing;
        }
        String twoXing = name.substring(0,2);
        if (twoXingSet.contains(twoXing)){
            xingMing[0] = twoXing;
            xingMing[1] = name.substring(2,name.length());
            return xingMing;
        }
        xingMing[0] = name.substring(0,1);
        xingMing[1] = name.substring(1,name.length());
        return xingMing;
    }

    /**
     * 字符转拼音
     * @param word
     * @return
     */
    private static List<String> charToPinyinList(Character word){
        HanyuPinyinOutputFormat t3 = new HanyuPinyinOutputFormat();
        t3.setCaseType(HanyuPinyinCaseType.UPPERCASE);
        t3.setToneType(HanyuPinyinToneType.WITHOUT_TONE);
        t3.setVCharType(HanyuPinyinVCharType.WITH_V);
        try {
            String[] strings = PinyinHelper.toHanyuPinyinStringArray(word, t3);
            if (strings.length<=0){
                List<String> list = new ArrayList<>();
                list.add(word.toString());
                return list;
            }
            List<String> collect = Arrays.stream(strings).distinct().collect(Collectors.toList());
            return collect;
        } catch (BadHanyuPinyinOutputFormatCombination badHanyuPinyinOutputFormatCombination) {
            return new ArrayList<>();
        }
    }

    /**
     * 获取拼音组合
     * @param cnStr 中文字符串
     * @return 拼音组合的集合
     */
    public static List<String> getPinyinGroup(String cnStr){
        if (Objects.isNull(cnStr)){
            return new ArrayList<>();
        }
        List<List<String>> pinyinList = stringToPinyinList(cnStr);
        System.out.println(JSON.toJSONString(pinyinList));
        List<String> pinyinBuilder = generateCombinations(pinyinList);
        List<String> collect = pinyinBuilder.stream().map(String::toString)
                .collect(Collectors.toList());
        return collect;
    }

    /**
     * string转成每个字符多音字的集合
     * @param cnStr
     * @return
     */
    private static List<List<String>> stringToPinyinList(String cnStr) {
        if (Objects.isNull(cnStr)){
            return new ArrayList<>();
        }
        char[] chars = cnStr.toCharArray();
        List<List<String>> pinyinList = new ArrayList<>();
        for (char aChar : chars) {
            pinyinList.add(charToPinyinList(aChar));
        }
        return pinyinList;
    }

    /**
     * 递归组合拼音集合
     * @param list 要组合的拼音 长度不能超过8
     * @param index 获取拼音组合的下标，从0开始
     * @param stringBuilderOut 上一级的拼音组合
     * @return 组合的拼音集合
     */
    private static List<StringBuilder> groupPinyin(List<List<String>> list, int index, StringBuilder stringBuilderOut){
        if (StringUtil.isNullOrEmpty(list)){
            return new ArrayList<>();
        }
        //如果超过只返回第一种的集合
        if (list.size() > 8 ){
            StringBuilder stringBuilder = new StringBuilder();
            for (List<String> strings : list) {
                stringBuilder.append(strings.get(0));
            }
            List<StringBuilder> builderList = new ArrayList<>();
            builderList.add(stringBuilder);
            return builderList;
        }

        List<StringBuilder> stringBuilderList = new ArrayList<>();
        for (String pinyin : list.get(index)) {
            StringBuilder stringBuilderIn = new StringBuilder();
            stringBuilderIn.append(stringBuilderOut);
            stringBuilderIn.append(pinyin);
            //最后一个，添加进集合
            if (list.size() == index+1){
                stringBuilderList.add(stringBuilderIn);
            }else {
                stringBuilderList.addAll(groupPinyin(list, index + 1, stringBuilderIn));
            }
        }
        return stringBuilderList;
    }


    // 生成所有可能的拼音组合
    private static List<String> generateCombinations(List<List<String>> pinyinList) {
        List<String> result = new ArrayList<>();
        generateCombinationsHelper(pinyinList, 0, new StringBuilder(), result);
        return result;
    }

    private static void generateCombinationsHelper(List<List<String>> pinyinList,
                                                   int index,
                                                   StringBuilder current,
                                                   List<String> result) {
        if (index == pinyinList.size()) {
            result.add(current.toString());
            return;
        }

        List<String> currentOptions = pinyinList.get(index);
        int currentLength = current.length();

        for (String option : currentOptions) {
            if (current.length() > 0) {
                current.append(" ");
            }
            current.append(option);
            generateCombinationsHelper(pinyinList, index + 1, current, result);
            current.setLength(currentLength); // 回溯
        }
    }

    public static NameParts splitFullName(String fullName) {
        List<String> surnames = new ArrayList<>();
        List<String> givenNames = new ArrayList<>();
        // 首先尝试匹配复姓
        for (String compoundSurname : name) {
            if (fullName.startsWith(compoundSurname) && fullName.length() > compoundSurname.length()) {
                surnames.add(compoundSurname);
                givenNames.add(fullName.substring(compoundSurname.length()));

                // 对于复姓，也添加单姓的可能性
                surnames.add(compoundSurname.substring(0, 1));
                givenNames.add(fullName.substring(1));
                return new NameParts(surnames, givenNames);
            }
        }
        // 如果没有复姓，按单姓处理
        if (fullName.length() > 1) {
            surnames.add(fullName.substring(0, 1));
            givenNames.add(fullName.substring(1));
        }

        return new NameParts(surnames, givenNames);
    }
    public static boolean isAllChinese(String str) {
        return str.matches("^[\\u4e00-\\u9fa5]+$");
    }
}

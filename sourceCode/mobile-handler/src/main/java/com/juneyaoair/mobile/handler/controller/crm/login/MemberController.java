package com.juneyaoair.mobile.handler.controller.crm.login;

import cn.com.antcloud.api.baasplus.v1_0_0.request.CertifyIndividualidFaceauthRequest;
import cn.com.antcloud.api.baasplus.v1_0_0.request.InitIndividualidFaceauthRequest;
import cn.com.antcloud.api.baasplus.v1_0_0.request.QueryIndividualidFaceauthRequest;
import cn.com.antcloud.api.baasplus.v1_0_0.response.CertifyIndividualidFaceauthResponse;
import cn.com.antcloud.api.baasplus.v1_0_0.response.InitIndividualidFaceauthResponse;
import cn.com.antcloud.api.baasplus.v1_0_0.response.QueryIndividualidFaceauthResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.geetest.geeguard.sdk.GeetestLib;
import com.geetest.geeguard.sdk.enums.DigestmodEnum;
import com.google.common.collect.Lists;
import com.google.gson.reflect.TypeToken;
import com.juneyaoair.appenum.*;
import com.juneyaoair.appenum.geetest.GeetestTypeEnum;
import com.juneyaoair.appenum.member.*;
import com.juneyaoair.appenum.order.UnifiedOrderResultEnum;
import com.juneyaoair.baseclass.basicsys.request.Antifraud;
import com.juneyaoair.baseclass.common.base.UserInfoMust;
import com.juneyaoair.baseclass.common.base.UserInfoNoMust;
import com.juneyaoair.baseclass.common.request.BaseReq;
import com.juneyaoair.baseclass.common.request.BaseReq1;
import com.juneyaoair.baseclass.common.response.BaseResp;
import com.juneyaoair.baseclass.common.response.MemberchangeResp;
import com.juneyaoair.baseclass.companyverify.constant.CompanyVerifiedStatusEnum;
import com.juneyaoair.baseclass.companyverify.request.CompanyVerifyApplyRequest;
import com.juneyaoair.baseclass.companyverify.response.CompanyVerifyApplyResponse;
import com.juneyaoair.baseclass.fiveg.PhoneInfo;
import com.juneyaoair.baseclass.member.comm.*;
import com.juneyaoair.baseclass.member.request.*;
import com.juneyaoair.baseclass.member.response.*;
import com.juneyaoair.baseclass.orderbrief.resp.UnpayOrderInfo;
import com.juneyaoair.baseclass.orderbrief.resp.UnpayOrderResponse;
import com.juneyaoair.baseclass.request.coupons.CouponActivityReq;
import com.juneyaoair.baseclass.request.coupons.RightsCouponReq;
import com.juneyaoair.baseclass.request.crm.CompanyMemberQueryInfoReqDto;
import com.juneyaoair.baseclass.request.crm.MemberLogoutRequest;
import com.juneyaoair.baseclass.request.geetest.Geetest;
import com.juneyaoair.baseclass.response.BaseResponse;
import com.juneyaoair.baseclass.response.coupons.CouponActivityInfo;
import com.juneyaoair.baseclass.response.coupons.CouponActivityResp;
import com.juneyaoair.baseclass.response.coupons.CouponResp;
import com.juneyaoair.baseclass.response.coupons.RightsCouponResponse;
import com.juneyaoair.baseclass.response.crm.*;
import com.juneyaoair.baseclass.response.idName.IdNameRes;
import com.juneyaoair.baseclass.risk.FraudEscapeConfig;
import com.juneyaoair.client.ClientInfo;
import com.juneyaoair.crm.IMemberService;
import com.juneyaoair.exception.*;
import com.juneyaoair.mobile.SystemConstants;
import com.juneyaoair.mobile.aop.annotation.InterfaceLog;
import com.juneyaoair.mobile.aop.annotation.NotDuplicate;
import com.juneyaoair.mobile.comm.service.IRedisService;
import com.juneyaoair.mobile.constants.MobileCoreBeanNames;
import com.juneyaoair.mobile.core.bean.device.AppCustomer;
import com.juneyaoair.mobile.core.bean.device.DeviceInfo;
import com.juneyaoair.mobile.core.bean.unitorder.request.OrderMaxFlightDateRequestDto;
import com.juneyaoair.mobile.core.bean.unitorder.response.OrderMaxFlightDateResponseDto;
import com.juneyaoair.mobile.core.service.device.mongo.IAppCustomerServiceMongo;
import com.juneyaoair.mobile.core.service.device.mongo.IDeviceServiceMongo;
import com.juneyaoair.mobile.external.request.sso.ParseTokenResult;
import com.juneyaoair.mobile.external.service.AuthService;
import com.juneyaoair.mobile.handler.HandlerConstants;
import com.juneyaoair.mobile.handler.comm.RedisKeyConfig;
import com.juneyaoair.mobile.handler.comm.SourceType;
import com.juneyaoair.mobile.handler.config.bean.HandConfig;
import com.juneyaoair.mobile.handler.controller.BassController;
import com.juneyaoair.mobile.handler.controller.crm.login.util.CrmUtil;
import com.juneyaoair.mobile.handler.controller.util.CRMReqUtil;
import com.juneyaoair.mobile.handler.controller.util.FraudApiInvoker;
import com.juneyaoair.mobile.handler.service.*;
import com.juneyaoair.mobile.handler.util.GeetestUtil;
import com.juneyaoair.mobile.interceptor.utils.TokenUtils;
import com.juneyaoair.pattern.PatternCommon;
import com.juneyaoair.thirdentity.comm.request.Header;
import com.juneyaoair.thirdentity.comm.request.PtApiCRMRequest;
import com.juneyaoair.thirdentity.comm.request.PtCrmMileageRequest;
import com.juneyaoair.thirdentity.comm.response.PtCRMResponse;
import com.juneyaoair.thirdentity.comm.response.PtCrmMileageResponse;
import com.juneyaoair.thirdentity.member.comm.MemberCertificateSoaModel;
import com.juneyaoair.thirdentity.member.comm.ThirdPartyUserInfoSoaModel;
import com.juneyaoair.thirdentity.member.request.*;
import com.juneyaoair.thirdentity.member.response.*;
import com.juneyaoair.thirdentity.tongdun.FinalDecisionEnum;
import com.juneyaoair.thirdentity.tongdun.LoginTypeEnum;
import com.juneyaoair.thirdentity.tongdun.RegisterTypeEnum;
import com.juneyaoair.thirdentity.tongdun.response.FraudApiResponse;
import com.juneyaoair.util.ValidatorUtils;
import com.juneyaoair.utils.EncoderHandler;
import com.juneyaoair.utils.StringUtil;
import com.juneyaoair.utils.apple.AppleLoginUtil;
import com.juneyaoair.utils.exception.BusinessException;
import com.juneyaoair.utils.http.HttpResult;
import com.juneyaoair.utils.http.HttpUtil;
import com.juneyaoair.utils.http.HttpsUtil;
import com.juneyaoair.utils.json.JsonMapper;
import com.juneyaoair.utils.json.JsonUtil;
import com.juneyaoair.utils.util.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import java.lang.reflect.Type;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 会员2.0服务
 * @date 2018/7/20  9:17.
 */
@RequestMapping("/member")
@RestController
@Api(value = "会员服务", tags = "会员服务")
public class MemberController extends BassController {
    @Autowired
    private IAppCustomerServiceMongo appCustomerServiceMongo;
    @Autowired
    private IDeviceServiceMongo deviceServiceMongo;
    @Autowired
    private ThreadPoolTaskExecutor taskExecutor;
    @Resource(name = MobileCoreBeanNames.API_REDIS_SERVICE)
    private IRedisService apiRedisService;
    @Autowired
    private HandConfig handConfig;
    @Autowired
    private IAntCloudService antCloudService;
    @Autowired
    private IMemberService memberService;
    @Autowired
    private IBasicService basicService;
    @Autowired
    private IMemberCacheService memberCacheService;

    @Autowired
    private IMemberTagService iMemberTagService;
    @Autowired
    private GeetestService geetestService;
    @Autowired
    private MemberAggrService memberAggrService;
    @Autowired
    private MobileMemberService mobileMemberService;
    @Autowired
    private AuthService authService;

    @Autowired
    private ICompanyMemberVerifyService companyMemberVerifyService;

    private static final String LOGIN_ERR = "loginErr";
    private static final String CHANNEL_CODE = "channelCode";
    private static final String EQUIPMENT_SAVE_ERR = "设备信息保存异常!";
    private static final String INTERNATIONAL_AREA_CODE_MUST_BE_DIGITAL = "国际区号只能为数字！";
    private static final String THIRD_PARTY_TYPE = "ThirdPartyType";
    private static final String THIRDPARTYTYPE = "thirdPartyType";
    private static final String ACCESS_TOKEN = "AccessToken";
    private static final String OPEN_ID = "OpenId";
    private static final String WECHAT = "WECHAT";
    private static final String UNKNOWN_LOGIN_TYPE = "登录类型未知";
    private static final String WRONG_IDCARD = "请输入正确的证件号！";
    private static final String TONGDUN_RESULT = "{},调用同盾接口返回结果:{}";
    private static final String MEMBER_MERGE_KEY_PREFIX = "member:merge:";
    private static final String NAME = "Name";
    private static final String IDCARD = "IDCard";
    private static final String CODE = "Code";
    private static final String ALIPAY_IDENTITY_VERIFY = "alipayIdentityVerify";
    private static final String SERVICE_NAME = "会员服务";
    private static final String LOG_RESP_TEN = "渠道:{},IP:{},手机号:{}";
    private static final String TOKEN = "token";
    private static final String DATE_FORMAT = "yyyy-MM-dd";

    private String getClientPwd(String channelCode) {
        return getChannelInfo(channelCode, "40");
    }

    @ApiOperation(value = "验证码登录", notes = "验证码登录")
    @RequestMapping(value = "/captchalogin", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @InterfaceLog
    @NotDuplicate
    public BaseResp<LoginResponse> captchalogin(@RequestBody @Validated BaseReq<LoginInfo> req, BindingResult bindingResult, HttpServletRequest request) {
        BaseResp<LoginResponse> resp = new BaseResp<>();
        try {
            String ip = this.getClientIP(request);
            String headChannelCode = request.getHeader(HandlerConstants.TOKEN_CHANNELCODE);
            String platform = request.getHeader(HandlerConstants.CLIENT_PLATFORM);
            String blackBoxFrom = request.getHeader(HandlerConstants.BLACKBOX_FROM);
            String channelCode = req.getChannelCode();
            if (bindingResult.hasErrors()) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
                return resp;
            }
            if (ChannelCodeEnum.MP_ALIPAY.getChannelCode().equals(req.getChannelCode())) {
                req.setChannelCode(ChannelCodeEnum.WEIXIN.getChannelCode());
            }
            //支付宝目前走微信渠道
            //IP控制
            if (!this.chkDayOptErr(ip, LOGIN_ERR, "")) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("登录错误次数过多，请明日再试！");
                return resp;
            }
            //账户控制
            if (!this.chkDayOptErr(req.getRequest().getMobileNum(), LOGIN_ERR, "")) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("账户已被暂时锁定，请明日再试！");
                return resp;
            }

            LoginInfo loginInfoReq = req.getRequest();
            String phoneNum = PhoneUtil.formatMobile(loginInfoReq.getCountryCode(), loginInfoReq.getMobileNum());
            // 当日验证码登录接口同一手机号\ID限制访问次数
            this.checkAccessCount(CheckAccessCountEnum.CAPTCHA_LOGIN_IP, ip);
            this.checkAccessCount(CheckAccessCountEnum.CAPTCHA_LOGIN_PHONE, phoneNum);
            //根据手机号查询用户信息，查询不到用户信息的将注册手机号
            String[] items = {MemberDetailRequestItemsEnum.BASICINFO.eName, MemberDetailRequestItemsEnum.STATEINFO.eName,
                    MemberDetailRequestItemsEnum.CERTIFICATEINFO.eName, MemberDetailRequestItemsEnum.CONTACTINFO.eName, MemberDetailRequestItemsEnum.ADDRESSINFOS.eName};
            //渠道暂时固定为MOBILE渠道
            PtMemberDetailRequest ptMemberDetailRequest = new PtMemberDetailRequest();
            ptMemberDetailRequest.setMobile(phoneNum);
            ptMemberDetailRequest.setRequestItems(items);
            Header header = buildHeader(request, "-1", "");

            PtApiCRMRequest<PtMemberDetailRequest> ptApiCRMRequest = new PtApiCRMRequest<>();
            ptApiCRMRequest.setHeader(header);
            ptApiCRMRequest.setChannel(channelCode);
            ptApiCRMRequest.setChannelPwd(getClientPwd(channelCode));
            ptApiCRMRequest.setData(ptMemberDetailRequest);
            PtCRMResponse<PtMemberDetail> memberDetailResp = memberService.memberDetail(ptApiCRMRequest);
            if (memberDetailResp.getCode() == 0) {
                if (memberDetailResp.getData() != null && memberDetailResp.getData().getStateInfo() != null) {
                    MemberStateInfoSoaModel stateInfoSoaModel = memberDetailResp.getData().getStateInfo();
                    if (2 != stateInfoSoaModel.getIsClosed()) {
                        throw new CommonException(WSEnum.ERROR.getResultCode(), "该账户已关闭");
                    }
                }
            } else if (memberDetailResp.getCode() == 100002) {
                registerMember(request, channelCode, loginInfoReq.getSid(), phoneNum);
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo(memberDetailResp.getMsg());
                return resp;
            }
            PtApiCRMRequest<CaptchaLoginRequest> ptApiRequest = buildCaptchaLogin(request, req);
            PtCRMResponse<PtLoginResponse> ptCRMResponse = memberService.captchalogin(ptApiRequest);
            if (ptCRMResponse.isIsSuccess()) {
                //登录成功
                if (ptCRMResponse.getCode() == 0) {
                    PtLoginResponse captchaLoginResponse = ptCRMResponse.getData();
                    LoginResponse loginInfo = new LoginResponse();
                    loginInfo.setToken(captchaLoginResponse.getLoginInfo().getToken());
                    loginInfo.setExpiryTime(captchaLoginResponse.getLoginInfo().getExpiryTime());
                    MemberLoginResponse memberLoginResponse = formatLoginResp(captchaLoginResponse.getMemberInfo(), request, channelCode);
                    if ("Y".equals(handConfig.getUseTongDun()) && FraudApiInvoker.needRiskControl(handConfig.getFraudEscapeConfig(), 0, ip)) {
                        //验证同盾
                        FraudApiResponse fraudApiResponse = FraudApiInvoker.loginRiskControl(headChannelCode, platform, memberLoginResponse.getMemberID(), req.getRequest().getBlackBox(), blackBoxFrom, LoginTypeEnum.MESSAGE.getCode(), req.getRequest().getMobileNum(), ip, req.getClientVersion());
                        if (fraudApiResponse.getSuccess() && fraudApiResponse.getFinal_decision().equalsIgnoreCase(FinalDecisionEnum.REJECT.getCode())) {
                            resp.setResultCode(WSEnum.TONGDUN_FAIL_LOGIN.getResultCode());
                            resp.setResultInfo(WSEnum.TONGDUN_FAIL_LOGIN.getResultInfo());
                            return resp;
                        }
                    }
                    if (WSEnum.SUCCESS.getResultCode().equals(memberLoginResponse.getResultCode())) {
                        memberLoginResponse.setToken(captchaLoginResponse.getLoginInfo().getToken());
                        memberLoginResponse.setExpiryTime(captchaLoginResponse.getLoginInfo().getExpiryTime());
                        //首次登录标记需弹窗
                        memberLoginResponse.setNewUser(captchaLoginResponse.isIsNewRegisterUser());
                        loginInfo.setMemberLoginResponse(memberLoginResponse);
                        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                        resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                        resp.setObjData(loginInfo);
                        //保存登陆人员设备信息 异步调用
                        if (ChannelCodeEnum.MOBILE.getChannelCode().equalsIgnoreCase(headChannelCode) && loginInfo.getMemberLoginResponse() != null && !StringUtil.isNullOrEmpty(req.getRequest().getDeviceId())) {
                            try {
                                AppCustomer appCustomer = createAppCustomer(loginInfo.getMemberLoginResponse(), req.getRequest().getDeviceId());
                                appCustomer.setClientVersion(req.getClientVersion());
                                DeviceInfo device = new DeviceInfo(req.getRequest().getDeviceId(), req.getRequest().getSystemInfo(), req.getRequest()
                                        .getPushNum(), "MSG_LOGIN", ip, appCustomer.getFfp_id() + "", appCustomer.getFfp_card_no());
                                device.setClientVersion(req.getClientVersion());
                                //  2017/12/22利用线程池
                                taskExecutor.execute(new DeviceThread(MdcUtils.getRequestId(), appCustomer, device));
                            } catch (Exception e) {
                                log.error(EQUIPMENT_SAVE_ERR, e.getMessage());
                            }
                        }
                    } else {
                        resp.setResultCode(WSEnum.ERROR.getResultCode());
                        resp.setResultInfo(memberLoginResponse.getErrorInfo());
                    }
                } else {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo(ptCRMResponse.getMsg());
                }
            } else {
                if (StringUtils.isNotBlank(ptCRMResponse.getInnerMsg()) &&
                        ptCRMResponse.getInnerMsg().contains("联系方式命中黑名单")) {
                    resp.setResultCode(WSEnum.BLACK_USER_REGISTER.getResultCode());
                    resp.setResultInfo(WSEnum.BLACK_USER_REGISTER.getResultInfo());
                } else {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo(ptCRMResponse.getMsg());
                }
            }
            return resp;
        } catch (CommonException ce) {
            resp.setResultCode(ce.getResultCode());
            resp.setResultInfo(ce.getErrorMsg());
            return resp;
        } catch (Exception e) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR.getResultInfo());
            log.error("{}验证码登录异常", MdcUtils.getRequestId(), e);
            return resp;
        }
    }

    private void registerMember(HttpServletRequest request, String channelCode, String sid, String mobile) {
        PtApiCRMRequest<PtRegisterRequest> ptApiCRMRequest = new PtApiCRMRequest<>();
        Header header = buildHeader(request, "-1", "");
        ptApiCRMRequest.setHeader(header);
        ptApiCRMRequest.setChannel(channelCode);
        ptApiCRMRequest.setChannelPwd(getClientPwd(channelCode));
        PtRegisterRequest target = new PtRegisterRequest();
        target.setMobile(mobile);
        target.setReferrer(sid);
        target.setSubmitDate(System.currentTimeMillis());
        target.setValidateMode(ValidateModeEnum.NO_VALID.validMethod);
        //性别未知
        target.setSex("U");
        ptApiCRMRequest.setData(target);
        PtCRMResponse<PtRegisterResponse> ptCRMResponse = memberService.register(ptApiCRMRequest);
        if (ptCRMResponse.isIsSuccess()) {
            if (ptCRMResponse.getCode() != 0) {
                throw new OperationFailedException("快速注册失败");
            }
        } else if (StringUtils.isNotBlank(ptCRMResponse.getInnerMsg()) &&
                ptCRMResponse.getInnerMsg().contains("联系方式命中黑名单")) {
            throw new CommonException(WSEnum.BLACK_USER_REGISTER.getResultCode(), WSEnum.BLACK_USER_REGISTER.getResultInfo());
        } else {
            throw new OperationFailedException("快速注册失败");
        }
    }

    @ApiOperation(value = "账号密码登录", notes = "账号密码登录")
    @RequestMapping(value = "login", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @InterfaceLog
    public BaseResp<LoginResponse> memberLogin(@RequestBody @Validated BaseReq<AccountPwd> req, BindingResult bindingResult, HttpServletRequest request) {
        BaseResp<LoginResponse> resp = new BaseResp<>();
        if (bindingResult.hasErrors()) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
            return resp;
        }
        String ip = this.getClientIP(request);
        String headChannelCode = request.getHeader(HandlerConstants.TOKEN_CHANNELCODE);
        String reqId = headChannelCode + StringUtil.newGUID() + request.getRequestURI();
        try {
            //支付宝目前走mobile通道
            if (ChannelCodeEnum.MP_ALIPAY.getChannelCode().equals(req.getChannelCode())) {
                req.setChannelCode(ChannelCodeEnum.WEIXIN.getChannelCode());
            }
            AccountPwd accountPwd = req.getRequest();
            //IP控制
            if (!this.chkDayOptErr(ip, LOGIN_ERR, "")) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("登录错误次数过多，请明日再试！");
                return resp;
            }
            //账户控制
            if (!this.chkDayOptErr(accountPwd.getUserName(), LOGIN_ERR, "")) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("账户已被暂时锁定，请明日再试！");
                return resp;
            }
            //指定IP白名单
            FraudEscapeConfig fraudEscapeConfig = handConfig.getFraudEscapeConfig();
            boolean ipCheck = true;
            if (CollectionUtils.isNotEmpty(fraudEscapeConfig.getIpWhiteList()) && fraudEscapeConfig.getIpWhiteList().contains(ip)) {
                ipCheck = false;
            }
            //IP控制
            if (ipCheck && !this.chkDayOptErr(ip, LOGIN_SOURCE, "")) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("登录频繁，请明日再试！");
                return resp;
            }
            //账户控制
            if (ipCheck && !this.chkDayOptErr(accountPwd.getUserName(), LOGIN_SOURCE, "")) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("账户登录频繁，请明日再试！");
                return resp;
            }
            //极验验证
            if ("Y".equals(handConfig.getUseGeetestLogin()) && ipCheck &&
                    !ChannelCodeEnum.WXAPP.getChannelCode().equalsIgnoreCase(headChannelCode) &&
                    !ChannelCodeEnum.CHECKIN.getChannelCode().equalsIgnoreCase(headChannelCode) &&
                    !ChannelCodeEnum.MP_ALIPAY.getChannelCode().equals(headChannelCode)) {
                GeetestLib gtSdk = GeetestUtil.initGeetest(headChannelCode, GeetestTypeEnum.LOGIN.getGeetestType());
                //极验验证
                HashMap<String, String> param = new HashMap<>();
                DigestmodEnum digestmodEnum = DigestmodEnum.MD5;
                param.put("digestmod", digestmodEnum.getName());
                param.put("user_id", ip); //网站用户id  设备号
                param.put("client_type", accountPwd.getClient_type()); //web:电脑上的浏览器；h5:手机上的浏览器，包括移动应用内完全内置的web_view；native：通过原生SDK植入APP应用的方式
                param.put("ip_address", ip); //传输用户请求验证时所携带的IP
                Geetest geetest = new Geetest(accountPwd.getGeetest_challenge(), accountPwd.getGeetest_validate(), accountPwd.getGeetest_seccode());
                geetestService.validate(gtSdk, geetest, param);
            }
            String channelCode = req.getChannelCode();
            PtApiCRMRequest<PtLoginRequest> ptApiRequest = buildLoginReq(request, channelCode, req.getRequest().getUserName(), req.getRequest().getPassword());
            memberLoginFun(request, ptApiRequest, reqId, ip, req.getClientVersion(), channelCode, accountPwd.getDeviceId(), accountPwd.getPushNum(), accountPwd.getSystemInfo(), "LOGIN", resp, req.getPlatformInfo(), accountPwd.getUserName(), accountPwd.getBlackBox());
            return resp;
        } catch (CommonException commonException) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(commonException.getErrorMsg());
            return resp;
        } catch (Exception e) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR.getResultInfo());
            return resp;
        }
    }

    @ApiOperation(value = "账号密码加密登录", notes = "账号密码加密登录")
    @RequestMapping(value = "memberEncryptLogin", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @InterfaceLog
    public BaseResp<LoginResponse> memberEncryptLogin(@RequestBody @Validated BaseReq<AccountPwd> req, BindingResult bindingResult, HttpServletRequest request) {
        BaseResp resp = new BaseResp();
        if (bindingResult.hasErrors()) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
            return resp;
        }
        String ip = this.getClientIP(request);
        String headChannelCode = request.getHeader(HandlerConstants.TOKEN_CHANNELCODE);
        String reqId = headChannelCode + StringUtil.newGUID() + request.getRequestURI();
        try {
            AccountPwd accountPwd = req.getRequest();
            //支付宝目前走mobile通道
            if (ChannelCodeEnum.MP_ALIPAY.getChannelCode().equals(req.getChannelCode())) {
                req.setChannelCode(ChannelCodeEnum.WEIXIN.getChannelCode());
            }
            //IP控制
            if (!this.chkDayOptErr(ip, LOGIN_ERR, "")) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("登录错误次数过多，请明日再试！");
                return resp;
            }
            //账户控制
            if (!this.chkDayOptErr(accountPwd.getUserName(), LOGIN_ERR, "")) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("账户已被暂时锁定，请明日再试！");
                return resp;
            }
            //指定IP白名单
            FraudEscapeConfig fraudEscapeConfig = handConfig.getFraudEscapeConfig();
            boolean ipCheck = true;
            if (CollectionUtils.isNotEmpty(fraudEscapeConfig.getIpWhiteList()) && fraudEscapeConfig.getIpWhiteList().contains(ip)) {
                ipCheck = false;
            }
            //IP控制
            if (ipCheck && !this.chkDayOptErr(ip, LOGIN_SOURCE, "")) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("登录频繁，请明日再试！");
                return resp;
            }
            //账户控制
            if (ipCheck && !this.chkDayOptErr(accountPwd.getUserName(), LOGIN_SOURCE, "")) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("账户登录频繁，请明日再试！");
                return resp;
            }
            //极验验证
            if ("Y".equals(handConfig.getUseGeetestLogin()) && ipCheck &&
                    !ChannelCodeEnum.WXAPP.getChannelCode().equalsIgnoreCase(headChannelCode) &&
                    !ChannelCodeEnum.CHECKIN.getChannelCode().equalsIgnoreCase(headChannelCode) &&
                    !ChannelCodeEnum.MP_ALIPAY.getChannelCode().equals(headChannelCode)) {
                GeetestLib gtSdk = GeetestUtil.initGeetest(headChannelCode, GeetestTypeEnum.LOGIN.getGeetestType());
                //极验验证
                HashMap<String, String> param = new HashMap<>();
                DigestmodEnum digestmodEnum = DigestmodEnum.MD5;
                param.put("digestmod", digestmodEnum.getName());
                param.put("user_id", ip); //网站用户id  设备号
                param.put("client_type", accountPwd.getClient_type()); //web:电脑上的浏览器；h5:手机上的浏览器，包括移动应用内完全内置的web_view；native：通过原生SDK植入APP应用的方式
                param.put("ip_address", ip); //传输用户请求验证时所携带的IP
                Geetest geetest = new Geetest(accountPwd.getGeetest_challenge(), accountPwd.getGeetest_validate(), accountPwd.getGeetest_seccode());
                geetestService.validate(gtSdk, geetest, param);
            }
            String channelCode = req.getChannelCode();
            String pwd = SM4Util.decryptEcb(accountPwd.getPassword(), handConfig.getHoSm4Key());
            PtApiCRMRequest<PtLoginRequest> ptApiRequest = buildLoginReq(request, channelCode, accountPwd.getUserName(), pwd);
            memberLoginFun(request, ptApiRequest, reqId, ip, req.getClientVersion(), channelCode, accountPwd.getDeviceId(), accountPwd.getPushNum(), accountPwd.getSystemInfo(), "LOGIN", resp, req.getPlatformInfo(), accountPwd.getUserName(), accountPwd.getBlackBox());
            return resp;
        } catch (CommonException commonException) {
            saveError("登录异常", MdcUtils.getRequestId(), ip, reqId, commonException);
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(commonException.getErrorMsg());
            return resp;
        } catch (Exception e) {
            saveError("登录异常", MdcUtils.getRequestId(), ip, reqId, e);
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR.getResultInfo());
            return resp;
        }
    }

    @ApiOperation(value = "5G手机登录注册", notes = "5G手机登录注册")
    @RequestMapping(value = "/hwlogin", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @InterfaceLog
    public BaseResp<LoginResponse> huaweiRegister(@RequestBody BaseReq<PhoneInfo> req, HttpServletRequest request) {
        /**
         * 1.参数验证，解密手机号
         * 2.检查是否注册会员，是会员的情况下默认返回登录态，不是会员情况下执行快速注册后返回登录态
         */
        BaseResp resp = new BaseResp();
        Set<ConstraintViolation<BaseReq<PhoneInfo>>> violations = ValidatorUtils.validate(req);
        if (CollectionUtils.isNotEmpty(violations)) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo(violations.iterator().next().getMessage());
            return resp;
        }
        ObjCheckUtil.notNull(req.getRequest(), "业务参数不能为空");
        PhoneInfo phoneInfo = req.getRequest();
        String channelCode = request.getHeader(HandlerConstants.TOKEN_CHANNELCODE);
        //解密数据
        String phone;
        try {
            phone = AESTool.decrypt(phoneInfo.getEncryptedData(), HandlerConstants.HW5G_AES_SECRET, HandlerConstants.HW5G_AES_SECRET);
        } catch (Exception e) {
            log.error("AES解密异常请求参数：{},异常信息", JsonUtil.objectToJson(req), e);
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo("快速登录失败(-2)");
            return resp;
        }
        log.info("AES解密信息,明文:{},密文:{}", phone, phoneInfo.getEncryptedData());
        Pattern pattern = Pattern.compile(PatternCommon.MOBILE_PHONE);
        Matcher matcher = pattern.matcher(phone == null ? "" : phone);
        if (!matcher.matches()) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo("手机号不符合规则！");
            return resp;
        }
        String[] items = {MemberDetailRequestItemsEnum.BASICINFO.eName};
        PtApiCRMRequest<PtMemberDetailRequest> ptApiCRMRequest = CRMReqUtil.buildMemberDetailReq(phone, request, channelCode, items);
        PtCRMResponse<PtMemberDetail> memberDetailPtCRMResponse = memberService.memberDetail(ptApiCRMRequest);
        PtMemberDetail ptMemberDetail = memberDetailPtCRMResponse.getData();
        MemberLoginResponse memberLoginResponse;
        if (memberDetailPtCRMResponse.getCode() == 0) {
            if (memberDetailPtCRMResponse.getData() != null && memberDetailPtCRMResponse.getData().getStateInfo() != null) {
                MemberStateInfoSoaModel stateInfoSoaModel = memberDetailPtCRMResponse.getData().getStateInfo();
                if (2 != stateInfoSoaModel.getIsClosed()) {
                    throw new CommonException(WSEnum.ERROR.getResultCode(), "该账户已关闭");
                }
            }
            MemberBasicInfoSoaModel memberBasicInfoSoaModel = ptMemberDetail.getBasicInfo();
            //返回登录信息
            memberLoginResponse = formatLoginResp(memberBasicInfoSoaModel.getMemberId(), memberBasicInfoSoaModel.getCardNO(), request, channelCode);
        } else if (memberDetailPtCRMResponse.getCode() == 100002) {
            PtApiCRMRequest<FastRegisterRequest> fastRegisterRequestPtApiCRMRequest = buildCommReq(request, channelCode, null);
            FastRegisterRequest registerRequest = new FastRegisterRequest();
            registerRequest.setMode(1);
            registerRequest.setModeValue(phone);
            //registerRequest.setPassword(phone);
            registerRequest.setIgnoreVerify(true);
            fastRegisterRequestPtApiCRMRequest.setData(registerRequest);
            PtCRMResponse<FastRegisterResponse> registerResponse = memberService.fastRegister(fastRegisterRequestPtApiCRMRequest);
            if (registerResponse.getCode() == 0) {
                FastRegisterResponse fastRegisterResponse = registerResponse.getData();
                memberLoginResponse = formatLoginResp(fastRegisterResponse.getMemberId(), fastRegisterResponse.getMemberCardNo(), request, channelCode);
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("快速登录失败");
                return resp;
            }
            //返回登录信息
        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(memberDetailPtCRMResponse.getMsg());
            return resp;
        }
        if (memberLoginResponse != null && WSEnum.SUCCESS.getResultCode().equals(memberLoginResponse.getResultCode())) {
            LoginResponse loginInfo = new LoginResponse();
            loginInfo.setMemberLoginResponse(memberLoginResponse);
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
            resp.setObjData(loginInfo);
        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("快速登录失败-1");
        }
        return resp;
    }

    @NotDuplicate
    @ApiOperation(value = "赫尔辛基吉祥会员注册", notes = "吉祥会员注册")
    @RequestMapping(value = "/helsinkiRegister", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp<PtRegisterResponse> helsinkiRegister(HttpServletRequest request, @RequestBody BaseReq1<HelsinkiRegisterReq> req) {
        BaseResp resp = new BaseResp();
        try {
            String reqId = StringUtil.newGUID() + "_register";
            String ip = this.getClientIP(request);
            String logStr = JsonUtil.objectToJson(req);
            log.info(COMMON_LOG_WITH_REQ_INFO, reqId, ip, logStr);
            ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
            Validator validator = factory.getValidator();
            Set<ConstraintViolation<BaseReq1>> violations = validator.validate(req);
            if (CollectionUtils.isNotEmpty(violations)) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo(violations.iterator().next().getMessage());
                String logStr2 = JsonUtil.objectToJson(resp);
                log.info(COMMON_LOG_WITH_RESP_INFO, reqId, ip, logStr2);
                return resp;
            }
            String channelCode = req.getChannelCode();
            HelsinkiRegisterReq registerInfo = req.getRequest();
            BaseReq<HelsinkiRegisterReq> registerReqBaseReq = new BaseReq<>();
            BeanUtils.copyNotNullProperties(req, registerReqBaseReq);
            PtApiCRMRequest<PtRegisterRequest> ptApiRequest = buildRegisterReq1(request, registerReqBaseReq, resp);
            if (!WSEnum.SUCCESS.getResultCode().equals(resp.getResultCode())) {
                return resp;
            }
            PtCRMResponse<PtRegisterResponse> ptCRMResponse = memberService.register(ptApiRequest);
            if (ptCRMResponse.isIsSuccess()) {
                //添加会员卡号信息
                resp.setObjData(ptCRMResponse.getData());
                if (ptCRMResponse.getCode() == 0) {  //注册成功并自动登录
                    PtApiCRMRequest<PtLoginRequest> ptApiLoginRequest = buildLoginReq(request, channelCode, registerInfo.getEmail(), registerInfo.getPassword());
                    memberLoginFun(request, ptApiLoginRequest, reqId, ip, registerReqBaseReq.getClientVersion(), channelCode, registerInfo.getDeviceId(), registerInfo.getPushNum(), registerInfo.getSystemInfo(), "REGISTER_LOGIN", resp, req.getPlatformInfo(), "", "");
                } else {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo(ptCRMResponse.getMsg());
                }
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo(ptCRMResponse.getMsg());
            }
            String logStr2 = JsonUtil.objectToJson(resp);
            log.info(COMMON_LOG_WITH_RESP_INFO, reqId, ip, logStr2);
            return resp;
        } catch (Exception e) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR.getResultInfo());
            return resp;
        }
    }

    private PtApiCRMRequest<PtRegisterRequest> buildRegisterReq1(HttpServletRequest request, BaseReq<HelsinkiRegisterReq> req, BaseResp resp) {
        PtRegisterRequest target = new PtRegisterRequest();
        HelsinkiRegisterReq source = req.getRequest();

        BeanUtils.copyNotNullProperties(source, target);
        if (StringUtils.isBlank(target.getSex())) {
            target.setSex(SexEnum.UNKNOWN.eName);
        }
        target.setELastName(source.getELastName());
        target.setEFirstName(source.getEFirstName());
        target.setSubmitDate(System.currentTimeMillis());
        target.setValidateMode(ValidateModeEnum.NO_VALID.validMethod);
        PtApiCRMRequest<PtRegisterRequest> ptApiCRMRequest = new PtApiCRMRequest();
        Header header = buildHeader(request, "-1", "");
        ptApiCRMRequest.setHeader(header);
        ptApiCRMRequest.setChannel(req.getChannelCode());
        ptApiCRMRequest.setChannelPwd(getClientPwd(req.getChannelCode()));
        ptApiCRMRequest.setData(target);
        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
        resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
        return ptApiCRMRequest;
    }

    @InterfaceLog
    @NotDuplicate
    @ApiOperation(value = "吉祥会员注册", notes = "吉祥会员注册")
    @RequestMapping(value = "/register", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp<LoginResponse> registerMember(@RequestBody @Validated BaseReq<RegisterReq> req, BindingResult bindingResult, HttpServletRequest request) {
        BaseResp resp = new BaseResp();
        try {
            //支付宝走微信渠道
            if (ChannelCodeEnum.MP_ALIPAY.getChannelCode().equals(req.getChannelCode())) {
                req.setChannelCode(ChannelCodeEnum.WEIXIN.getChannelCode());
            }
            if (bindingResult.hasErrors()) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
                return resp;
            }
            String ip = this.getClientIP(request);
            String headChannelCode = request.getHeader(HandlerConstants.TOKEN_CHANNELCODE);
            //5.3版本使用同盾
            if ("Y".equals(handConfig.getUseTongDun())) {
                String code = request.getHeader(CHANNEL_CODE);
                if (ChannelCodeEnum.MOBILE.getChannelCode().equalsIgnoreCase(code)
                        || ChannelCodeEnum.MWEB.getChannelCode().equalsIgnoreCase(code)
                        || ChannelCodeEnum.WEIXIN.getChannelCode().equalsIgnoreCase(code)) {
                    Map<String, String> params = FraudApiInvoker.registerRiskControlParam(headChannelCode, req.getPlatformInfo(), req.getRequest().getMobile(), req.getRequest().getBlackBox(), RegisterTypeEnum.NORMAL.getCode(), ip, req.getClientVersion());
                    if (params != null) {
                        Antifraud antifraud = new Antifraud("", "", ip, params);
                        FraudApiResponse fraudApiResponse = basicService.antifraud(code, antifraud);
                        if (fraudApiResponse.getSuccess()) {
                            if (FinalDecisionEnum.REJECT.getCode().equalsIgnoreCase(fraudApiResponse.getFinal_decision())) {
                                resp.setResultCode(WSEnum.TONGDUN_FAIL_REGISTER.getResultCode());
                                resp.setResultInfo(WSEnum.TONGDUN_FAIL_REGISTER.getResultInfo());
                                return resp;
                            }
                            ip = StringUtils.isNotBlank(fraudApiResponse.getVirtualIp()) ? fraudApiResponse.getVirtualIp() : ip;
                        }
                    }
                }
            }
            String channelCode = req.getChannelCode();
            RegisterReq registerInfo = req.getRequest();
            //国际代码必须传值
            if (StringUtil.isNullOrEmpty(registerInfo.getCountryCode())) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("国际区号不能为空！");
                return resp;
            }
            if (!registerInfo.getCountryCode().matches(PatternCommon.MOBILE_GLOBAL_ROAMING)) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo(INTERNATIONAL_AREA_CODE_MUST_BE_DIGITAL);
                return resp;
            }
            PtApiCRMRequest<PtRegisterRequest> ptApiRequest = buildRegisterReq(request, req, resp);
            if (ptApiRequest == null && !WSEnum.SUCCESS.getResultCode().equals(resp.getResultCode())) {
                return resp;
            }
            PtCRMResponse<PtRegisterResponse> ptCRMResponse = memberService.register(ptApiRequest);
            if (ptCRMResponse.isIsSuccess()) {
                if (ptCRMResponse.getCode() == 0) {  //注册成功并自动登录
                    //PtApiCRMRequest<PtLoginRequest> ptApiLoginRequest = buildLoginReq(request, channelCode, registerInfo.getMobile(), registerInfo.getPassword());
                    //memberLoginFun(request, ptApiLoginRequest, reqId, ip, req.getClientVersion(), channelCode, registerInfo.getDeviceId(), registerInfo.getPushNum(), registerInfo.getSystemInfo(), "REGISTER_LOGIN", resp, req.getPlatformInfo(), registerInfo.getMobile(), registerInfo.getBlackBox());
                } else {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo(ptCRMResponse.getMsg());
                }
            } else {
                if (StringUtils.isNotBlank(ptCRMResponse.getInnerMsg()) &&
                        ptCRMResponse.getInnerMsg().contains("联系方式命中黑名单")) {
                    resp.setResultCode(WSEnum.BLACK_USER_REGISTER.getResultCode());
                    resp.setResultInfo(WSEnum.BLACK_USER_REGISTER.getResultInfo());
                } else {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo(ptCRMResponse.getMsg());
                }
            }
            return resp;
        } catch (Exception e) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR.getResultInfo());
            log.error("{},会员完整注册异常", MdcUtils.getRequestId(), e);
            return resp;
        }
    }

    @InterfaceLog
    @ApiOperation(value = "第三方帐号登录", notes = "第三方帐号登录")
    @RequestMapping(value = "/thirdlogin", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp<LoginResponse> thirdPartyLogin(@RequestBody BaseReq<ThirdPartyLoginRequest> req, HttpServletRequest request) {
        String headChannelCode = request.getHeader(HandlerConstants.TOKEN_CHANNELCODE);
        String platform = request.getHeader(HandlerConstants.CLIENT_PLATFORM);
        String blackBoxFrom = request.getHeader(HandlerConstants.BLACKBOX_FROM);
        String reqId = MdcUtils.getRequestId();
        BaseResp resp = new BaseResp();
        ThirdPartyLoginRequest thirdPartyLoginRequest = req.getRequest();
        if ("QQ".equals(thirdPartyLoginRequest.getThirdPartyType())) {
            thirdPartyLoginRequest.setCode("QQ");
        } else if (ThirdPartyTypeEnum.APPLE.getEnName().equals(thirdPartyLoginRequest.getThirdPartyType())) {
            thirdPartyLoginRequest.setCode(ThirdPartyTypeEnum.APPLE.getEnName());
        }
        try {
            String ip = this.getClientIP(request);
            ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
            Validator validator = factory.getValidator();
            Set<ConstraintViolation<BaseReq>> violations = validator.validate(req);
            if (CollectionUtils.isNotEmpty(violations)) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo(violations.iterator().next().getMessage());
                return resp;
            }
            String channelCode = req.getChannelCode();
            Map<String, Object> reqMap = new HashMap<>();
            if (!StringUtil.isNullOrEmpty(thirdPartyLoginRequest.getThirdPartyType())) {
                reqMap.put(THIRD_PARTY_TYPE, thirdPartyLoginRequest.getThirdPartyType());
                reqMap.put("Code", thirdPartyLoginRequest.getCode());
                // 如果是qq登录
                if (ThirdPartyTypeEnum.QQ.getEnName().equals(thirdPartyLoginRequest.getThirdPartyType())) {
                    if (StringUtil.isNullOrEmpty(thirdPartyLoginRequest.getAccessToken()) || StringUtil.isNullOrEmpty(thirdPartyLoginRequest.getOpenId())) {
                        resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                        resp.setResultInfo(WSEnum.ERROR_LONGIN_CHK_ERROR.getResultInfo());
                        return resp;
                    }
                    reqMap.put(ACCESS_TOKEN, thirdPartyLoginRequest.getAccessToken());
                    reqMap.put(OPEN_ID, thirdPartyLoginRequest.getOpenId());
                } else if (ThirdPartyTypeEnum.APPLE.getEnName().equals(thirdPartyLoginRequest.getThirdPartyType())) {
                    try {
                        String appleUserId = AppleLoginUtil.verifyIdentityToken(thirdPartyLoginRequest.getIdentityToken());
                        reqMap.put("ThirdPartyUid", appleUserId);
                    } catch (BusinessException e) {
                        log.error("苹果登录失败, token={}", thirdPartyLoginRequest.getIdentityToken(), e);
                        resp.setResultCode(WSEnum.ERROR.getResultCode());
                        resp.setResultInfo(e.getMessage());
                        return resp;
                    } catch (Exception e) {
                        log.error("苹果登录失败, token={}", thirdPartyLoginRequest.getIdentityToken(), e);
                        resp.setResultCode(WSEnum.ERROR.getResultCode());
                        resp.setResultInfo("苹果账号登陆失败");
                        return resp;
                    }
                }
            } else {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo(UNKNOWN_LOGIN_TYPE);
                return resp;
            }
            PtApiCRMRequest<Map<String, Object>> ptApiRequest = buildCommReqNoToken(request, channelCode);
            ptApiRequest.setData(reqMap);
            PtCRMResponse<PtThirdPartyLoginResponse> ptCRMResponse = memberService.thirdPartyLogin(ptApiRequest);
            if (ptCRMResponse.isIsSuccess()) {
                //第三方授权成功
                if (ptCRMResponse.getCode() == 0) {
                    LoginResponse login = new LoginResponse();
                    PtThirdPartyLoginResponse thirdPart = ptCRMResponse.getData();
                    ThirdAccountInfo target = new ThirdAccountInfo();
                    //获取到的第三方基本信息
                    BeanUtils.copyProperties(thirdPart.getThirdPartyUserInfo(), target);
                    login.setThirdPartInfo(target);
                    login.setBindMember(thirdPart.isIsBindMember());
                    //第三方已经绑定会员
                    if (thirdPart.isIsBindMember()) {
                        login.setToken(thirdPart.getLoginInfo().getToken());
                        login.setExpiryTime(thirdPart.getLoginInfo().getExpiryTime());
                        //此处为了兼容老的数据结构
                        MemberLoginResponse memberLoginResponse = formatLoginResp(thirdPart.getMemberInfo(), request, channelCode);
                        //5.3版本使用同盾
                        if ("Y".equals(handConfig.getUseTongDun()) && FraudApiInvoker.needRiskControl(handConfig.getFraudEscapeConfig(), 0, ip)) {
                            //验证同盾风控
                            String loginType = "";
                            if (WECHAT.equals(req.getRequest().getThirdPartyType())) {
                                loginType += LoginTypeEnum.WEIXIN.getCode();
                            } else if ("ALIPAY".equals(req.getRequest().getThirdPartyType())) {
                                loginType += LoginTypeEnum.ZHIFUBAO.getCode();
                            } else if ("QQ".equals(req.getRequest().getThirdPartyType())) {
                                loginType += LoginTypeEnum.QQ.getCode();
                            } else if (ThirdPartyTypeEnum.APPLE.getEnName().equals(req.getRequest().getThirdPartyType())) {
                                loginType += LoginTypeEnum.APPLE.getCode();
                            }
                            FraudApiResponse fraudApiResponse = FraudApiInvoker.loginRiskControl(headChannelCode, platform, ptCRMResponse.getData().getMemberInfo().getMemberCardNo(), req.getRequest().getBlackBox(), blackBoxFrom, loginType, "", ip, req.getClientVersion());
                            String logStr2 = JsonUtil.objectToJson(fraudApiResponse);
                            log.info(TONGDUN_RESULT, MdcUtils.getRequestId(), logStr2);
                            if (fraudApiResponse.getSuccess() && fraudApiResponse.getFinal_decision().equalsIgnoreCase(FinalDecisionEnum.REJECT.getCode())) {
                                resp.setResultCode(WSEnum.TONGDUN_FAIL_LOGIN.getResultCode());
                                resp.setResultInfo(WSEnum.TONGDUN_FAIL_LOGIN.getResultInfo());
                                return resp;
                            }
                        }
                        if (WSEnum.SUCCESS.getResultCode().equals(memberLoginResponse.getResultCode())) {
                            memberLoginResponse.setToken(thirdPart.getLoginInfo().getToken());
                            memberLoginResponse.setExpiryTime(thirdPart.getLoginInfo().getExpiryTime());
                            login.setMemberLoginResponse(memberLoginResponse);
                            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                            resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                            resp.setObjData(login);
                            //保存登陆人员设备信息 异步调用
                            if (ChannelCodeEnum.MOBILE.getChannelCode().equalsIgnoreCase(headChannelCode) && login.getMemberLoginResponse() != null && !StringUtil.isNullOrEmpty(req.getRequest().getDeviceId())) {
                                try {
                                    AppCustomer appCustomer = createAppCustomer(login.getMemberLoginResponse(), req.getRequest().getDeviceId());
                                    appCustomer.setClientVersion(req.getClientVersion());
                                    DeviceInfo device = new DeviceInfo(req.getRequest().getDeviceId(), req.getRequest().getSystemInfo(), req.getRequest()
                                            .getPushNum(), req.getRequest().getThirdPartyType() + "_THIRDLOGIN", ip, appCustomer.getFfp_id() + "", appCustomer.getFfp_card_no());
                                    device.setClientVersion(req.getClientVersion());
                                    taskExecutor.execute(new DeviceThread(reqId, appCustomer, device));
                                } catch (Exception e) {
                                    log.error(EQUIPMENT_SAVE_ERR, e.getMessage());
                                }
                            }
                        } else {
                            resp.setResultCode(WSEnum.ERROR.getResultCode());
                            resp.setResultInfo(memberLoginResponse.getErrorInfo());
                        }
                    } else {
                        //未绑定的跳转至绑定界面
                        resp.setObjData(login);
                        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                        resp.setResultInfo("请去进行账号绑定");
                    }
                } else {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo(ptCRMResponse.getMsg());
                }
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo(ptCRMResponse.getMsg());
            }
            return resp;
        } catch (Exception e) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR.getResultInfo());
            return resp;
        }
    }

    @InterfaceLog
    @ApiOperation(value = "第三方帐号QQ登录可扩展微博", notes = "第三方帐号QQ登录")
    @RequestMapping(value = "/thirdloginqq", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp<LoginResponse> thirdPartyLoginQQ(@RequestBody BaseReq<ThirdPartyLoginQQRequest> req, HttpServletRequest request) {
        BaseResp resp = new BaseResp();
        String headChannelCode = request.getHeader(HandlerConstants.TOKEN_CHANNELCODE);
        String platform = request.getHeader(HandlerConstants.CLIENT_PLATFORM);
        String blackBoxFrom = request.getHeader(HandlerConstants.BLACKBOX_FROM);
        try {
            String ip = this.getClientIP(request);
            Map<String, String> authInfo = req.getRequest().getAuthInfo();
            String reqId = StringUtil.newGUID() + "_thirdloginqq";
            ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
            Validator validator = factory.getValidator();
            Set<ConstraintViolation<BaseReq>> violations = validator.validate(req);
            if (CollectionUtils.isNotEmpty(violations)) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo(violations.iterator().next().getMessage());
                return resp;
            }
            String channelCode = req.getChannelCode();
            Map<String, Object> reqMap = new HashMap();
            if (!StringUtil.isNullOrEmpty(authInfo.get(THIRDPARTYTYPE))) {
                reqMap.put(THIRD_PARTY_TYPE, authInfo.get(THIRDPARTYTYPE));
                // 如果是qq登录
                if (authInfo.get(THIRDPARTYTYPE).equals("QQ")) {
                    if (StringUtil.isNullOrEmpty(authInfo.get("accessToken")) || StringUtil.isNullOrEmpty(authInfo.get("openId"))) {
                        resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                        resp.setResultInfo(WSEnum.ERROR_LONGIN_CHK_ERROR.getResultInfo());
                        return resp;
                    }
                    reqMap.put(ACCESS_TOKEN, authInfo.get("accessToken"));
                    reqMap.put(OPEN_ID, authInfo.get("openId"));
                    reqMap.put("Code", "QQ");


                } else if (authInfo.get(THIRDPARTYTYPE).equals(WECHAT)) { //微信登录
                    reqMap.put("Code", authInfo.get("code"));
                }
            } else {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo(UNKNOWN_LOGIN_TYPE);
                return resp;
            }

            PtApiCRMRequest<Map<String, Object>> ptApiRequest = buildCommReqNoToken(request, channelCode);
            ptApiRequest.setData(reqMap);
            PtCRMResponse<PtThirdPartyLoginResponse> ptCRMResponse = memberService.thirdPartyLogin(ptApiRequest);
            if (ptCRMResponse.isIsSuccess()) {
                //第三方授权成功
                if (ptCRMResponse.getCode() == 0) {
                    LoginResponse login = new LoginResponse();
                    PtThirdPartyLoginResponse thirdPart = ptCRMResponse.getData();
                    ThirdAccountInfo target = new ThirdAccountInfo();
                    //获取到的第三方基本信息
                    BeanUtils.copyProperties(thirdPart.getThirdPartyUserInfo(), target);
                    login.setThirdPartInfo(target);
                    login.setBindMember(thirdPart.isIsBindMember());
                    //第三方已经绑定会员
                    if (thirdPart.isIsBindMember()) {
                        login.setToken(thirdPart.getLoginInfo().getToken());
                        login.setExpiryTime(thirdPart.getLoginInfo().getExpiryTime());
                        //此处为了兼容老的数据结构
                        MemberLoginResponse memberLoginResponse = formatLoginResp(thirdPart.getMemberInfo(), request, channelCode);
                        //5.3版本使用同盾
                        if (FraudApiInvoker.needRiskControl(handConfig.getFraudEscapeConfig(), 0, ip)) {
                            //添加同盾校验
                            FraudApiResponse fraudApiResponse = FraudApiInvoker.loginRiskControl(headChannelCode, platform, ptCRMResponse.getData().getMemberInfo().getMemberCardNo(), authInfo.get("blackBox"), blackBoxFrom, LoginTypeEnum.QQ.getCode(), memberLoginResponse.getMemberTel(), ip, req.getClientVersion());
                            String logStr2 = JsonUtil.objectToJson(fraudApiResponse);
                            log.info(TONGDUN_RESULT, MdcUtils.getRequestId(), logStr2);
                            if (fraudApiResponse.getSuccess() && fraudApiResponse.getFinal_decision().equalsIgnoreCase(FinalDecisionEnum.REJECT.getCode())) {
                                resp.setResultCode(WSEnum.ERROR.getResultCode());
                                resp.setResultInfo(fraudApiResponse.getFinal_decision());
                                return resp;
                            }
                        }
                        if (WSEnum.SUCCESS.getResultCode().equals(memberLoginResponse.getResultCode())) {
                            memberLoginResponse.setToken(thirdPart.getLoginInfo().getToken());
                            memberLoginResponse.setExpiryTime(thirdPart.getLoginInfo().getExpiryTime());
                            login.setMemberLoginResponse(memberLoginResponse);
                            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                            resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                            resp.setObjData(login);
                            //保存登陆人员设备信息 异步调用
                            if (ChannelCodeEnum.MOBILE.getChannelCode().equalsIgnoreCase(headChannelCode) && login.getMemberLoginResponse() != null && !StringUtil.isNullOrEmpty(req.getRequest().getDeviceId())) {
                                try {
                                    AppCustomer appCustomer = createAppCustomer(login.getMemberLoginResponse(), req.getRequest().getDeviceId());
                                    appCustomer.setClientVersion(req.getClientVersion());
                                    DeviceInfo device = new DeviceInfo(req.getRequest().getDeviceId(), req.getRequest().getSystemInfo(), req.getRequest()
                                            .getPushNum(), authInfo.get(THIRD_PARTY_TYPE) + "_THIRDLOGIN", ip, appCustomer.getFfp_id() + "", appCustomer.getFfp_card_no());
                                    device.setClientVersion(req.getClientVersion());
                                    taskExecutor.execute(new DeviceThread(reqId, appCustomer, device));
                                } catch (Exception e) {
                                    log.error(EQUIPMENT_SAVE_ERR, e.getMessage());
                                }
                            }
                        } else {
                            resp.setResultCode(WSEnum.ERROR.getResultCode());
                            resp.setResultInfo(memberLoginResponse.getErrorInfo());
                        }
                    } else {
                        //未绑定的跳转至绑定界面
                        resp.setObjData(login);
                        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                        resp.setResultInfo("请去进行账号绑定");
                    }
                } else {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo(ptCRMResponse.getMsg());
                }
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo(ptCRMResponse.getMsg());
            }
            String logStr2 = JsonUtil.objectToJson(resp);
            log.info(COMMON_LOG_WITH_RESP_INFO, reqId, ip, logStr2);
            return resp;
        } catch (Exception e) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR.getResultInfo());
            return resp;
        }
    }

    @InterfaceLog
    @ApiOperation(value = "第三方帐号登录请求授权参数", notes = "第三方帐号登录请求授权参数")
    @RequestMapping(value = "/thirdPartyGrant", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp thirdPartyGrant(@RequestBody BaseReq<String> req, HttpServletRequest request) {
        BaseResp resp = new BaseResp();
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<BaseReq>> violations = validator.validate(req);
        if (CollectionUtils.isNotEmpty(violations)) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo(violations.iterator().next().getMessage());
            return resp;
        }
        Map<String, Object> param = new HashMap<>();
        param.put("UseScene", MobileSdkSceneEnum.ALIPAY_MOBILE_LOGIN.eName);
        PtApiCRMRequest<Map<String, Object>> ptApiRequest = buildCommReqNoToken(request, req.getChannelCode());
        ptApiRequest.setData(param);
        PtCRMResponse<PtCreateMobileSdkParamsResponse> ptCRMResponse = memberService.createMobileSdkParams(ptApiRequest);
        if (ptCRMResponse.getCode() == 0) {
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
            resp.setObjData(ptCRMResponse.getData().getSdkParams());

        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(ptCRMResponse.getMsg());
        }
        return resp;
    }

    @InterfaceLog
    @ApiOperation(value = "第三方帐号实名请求授权参数", notes = "第三方帐号实名请求授权参数")
    @RequestMapping(value = "/thirdPartyRealGrant", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp thirdPartyRealGrant(@RequestBody @Validated BaseReq<String> req, BindingResult bindingResult, HttpServletRequest request) {
        BaseResp resp = new BaseResp();
        if (bindingResult.hasErrors()) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
            return resp;
        }
        Map<String, Object> param = new HashMap<>();
        param.put("UseScene", MobileSdkSceneEnum.ALIPAY_REAL_NAME_VERFIY.eName);
        PtApiCRMRequest<Map<String, Object>> ptApiRequest = buildCommReqNoToken(request, req.getChannelCode());
        ptApiRequest.setData(param);
        PtCRMResponse<PtCreateMobileSdkParamsResponse> ptCRMResponse = memberService.createMobileSdkParams(ptApiRequest);
        if (ptCRMResponse.getCode() == 0) {
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
            resp.setObjData(ptCRMResponse.getData().getSdkParams());

        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(ptCRMResponse.getMsg());
        }
        return resp;
    }

    @ApiOperation(value = "第三方帐号实名请求授权参数", notes = "第三方帐号实名请求授权参数")
    @RequestMapping(value = "/thirdPartyRealGrantV2", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp thirdPartyRealGrantV2(@RequestBody @Validated BaseReq<AliPayCall> req, BindingResult bindingResult, HttpServletRequest request) {
        BaseResp resp = new BaseResp();
        AliPayCall aliPayCall = req.getRequest();
        Objects.requireNonNull(aliPayCall, "业务参数不可为空");
        checkRequest(req, bindingResult);
        if (StringUtils.isNotBlank(aliPayCall.getOperation()) && !SensitiveOperationEnum.checkEnum(aliPayCall.getOperation())) {
            throw new ServiceException("参数超出规定范围值");
        }
        ClientInfo clientInfo = initClientInfo(request, req.getChannelCode(), aliPayCall.getFfpId(), aliPayCall.getFfpCardNo());
        //此类型不需要验证短信验证码
        if (!SensitiveOperationEnum.AUTH1.getOperation().equals(aliPayCall.getOperation())) {
            memberAggrService.checkSmsCode(clientInfo, aliPayCall.getOperation());
        }
        Map<String, Object> param = new HashMap<>();
        param.put("UseScene", MobileSdkSceneEnum.ALIPAY_REAL_NAME_VERFIY.eName);
        PtApiCRMRequest<Map<String, Object>> ptApiRequest = buildCommReqNoToken(request, req.getChannelCode());
        ptApiRequest.setData(param);
        PtCRMResponse<PtCreateMobileSdkParamsResponse> ptCRMResponse = memberService.createMobileSdkParams(ptApiRequest);
        if (ptCRMResponse.getCode() == 0) {
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
            resp.setObjData(ptCRMResponse.getData().getSdkParams());

        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(ptCRMResponse.getMsg());
        }
        return resp;
    }

    @InterfaceLog
    @ApiOperation(value = "第三方帐号绑定登录", notes = "第三方帐号绑定登录")
    @RequestMapping(value = "/bindthirdlogin", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp<LoginResponse> bindThirdPartyLogin(@RequestBody BaseReq<BindThirdPartyLogin> req, HttpServletRequest request) {
        BaseResp resp = new BaseResp();
        try {
            String ip = this.getClientIP(request);
            String headChannelCode = request.getHeader(HandlerConstants.TOKEN_CHANNELCODE);
            //5.3版本使用同盾
            if ("Y".equals(handConfig.getUseTongDun())) {
                if (ChannelCodeEnum.MOBILE.getChannelCode().equalsIgnoreCase(headChannelCode) || ChannelCodeEnum.MWEB.getChannelCode().equalsIgnoreCase(headChannelCode) || ChannelCodeEnum.WEIXIN.getChannelCode().equalsIgnoreCase(headChannelCode)) {
                    String thirdPartyType = req.getRequest().getThirdPartyUserInfo().getThirdPartyType();
                    String registerType = "";
                    if (WECHAT.equals(thirdPartyType)) {
                        registerType += RegisterTypeEnum.WEIXIN.getCode();
                    } else if ("QQ".equals(thirdPartyType)) {
                        registerType += RegisterTypeEnum.QQ.getCode();
                    } else if ("ALIPAY".equals(thirdPartyType)) {
                        registerType += RegisterTypeEnum.ZHIFUBAO.getCode();
                    } else if (ThirdPartyTypeEnum.APPLE.getEnName().equals(thirdPartyType)) {
                        registerType += RegisterTypeEnum.APPLE.getCode();
                        ThirdAccountInfo thirdAccountInfo = req.getRequest().getThirdPartyUserInfo();
                        try {
                            // 重新解析苹果账号ID，防止篡改
                            String appleUserId = AppleLoginUtil.verifyIdentityToken(thirdAccountInfo.getIdentityToken());
                            thirdAccountInfo.setUid(appleUserId);
                        } catch (BusinessException e) {
                            log.error("苹果登录失败, token={}", thirdAccountInfo.getIdentityToken(), e);
                            resp.setResultCode(WSEnum.ERROR.getResultCode());
                            resp.setResultInfo(e.getMessage());
                            return resp;
                        } catch (Exception e) {
                            log.error("苹果登录失败, token={}", thirdAccountInfo.getIdentityToken(), e);
                            resp.setResultCode(WSEnum.ERROR.getResultCode());
                            resp.setResultInfo("苹果账号登陆失败");
                            return resp;
                        }
                    }
                    //验证同盾
                    Map<String, String> params = FraudApiInvoker.registerRiskControlParam(headChannelCode, req.getPlatformInfo(), req.getRequest().getChannelValue(), req.getRequest().getBlackBox(), registerType, ip, req.getClientVersion());
                    if (params != null) {
                        Antifraud antifraud = new Antifraud("", "", ip, params);
                        FraudApiResponse fraudApiResponse = basicService.antifraud(headChannelCode, antifraud);
                        if (fraudApiResponse.getSuccess()) {
                            if (FinalDecisionEnum.REJECT.getCode().equalsIgnoreCase(fraudApiResponse.getFinal_decision())) {
                                resp.setResultCode(WSEnum.TONGDUN_FAIL_REGISTER.getResultCode());
                                resp.setResultInfo(WSEnum.TONGDUN_FAIL_REGISTER.getResultInfo());
                                return resp;
                            }
                            ip = StringUtils.isNotBlank(fraudApiResponse.getVirtualIp()) ? fraudApiResponse.getVirtualIp() : ip;
                        }
                    }
                }
            }
            String reqId = StringUtil.newGUID() + "_bindthirdlogin";
            String logStr = JsonUtil.objectToJson(req);
            log.info(COMMON_LOG_WITH_REQ_INFO, reqId, ip, logStr);
            ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
            Validator validator = factory.getValidator();
            Set<ConstraintViolation<BaseReq>> violations = validator.validate(req);
            if (CollectionUtils.isNotEmpty(violations)) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo(violations.iterator().next().getMessage());
                return resp;
            }
            String channelCode = req.getChannelCode();
            BindThirdPartyLogin form = req.getRequest();
            //国际代码必须传值
            if (StringUtil.isNullOrEmpty(form.getCountryCode())) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("国际区号不能为空！");
                return resp;
            }
            if (!form.getCountryCode().matches(PatternCommon.MOBILE_GLOBAL_ROAMING)) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo(INTERNATIONAL_AREA_CODE_MUST_BE_DIGITAL);
                return resp;
            }
            PtApiCRMRequest<PtBindThirdPartyLoginRequest> ptApiRequest = buildCommReqNoToken(request, channelCode);
            PtBindThirdPartyLoginRequest bind = new PtBindThirdPartyLoginRequest();
            ThirdPartyUserInfoSoaModel thirdPartyUserInfoSoaModel = new ThirdPartyUserInfoSoaModel();
            BeanUtils.copyProperties(form, bind);
            BeanUtils.copyProperties(form.getThirdPartyUserInfo(), thirdPartyUserInfoSoaModel);
            //APP默认使用手机渠道
            bind.setSendChannel(1);
            bind.setChannelValue(PhoneUtil.formatMobile(form.getCountryCode(), form.getChannelValue()));
            bind.setThirdPartyUserInfo(thirdPartyUserInfoSoaModel);
            ptApiRequest.setData(bind);
            PtCRMResponse<PtLoginResponse> ptCRMResponse = memberService.memberBindAccoutLogin(ptApiRequest);
            if (ptCRMResponse.isIsSuccess()) {
                if (ptCRMResponse.getCode() == 0) {  //登录成功
                    PtLoginResponse captchaLoginResponse = ptCRMResponse.getData();
                    LoginResponse loginInfo = new LoginResponse();
                    loginInfo.setToken(captchaLoginResponse.getLoginInfo().getToken());
                    loginInfo.setExpiryTime(captchaLoginResponse.getLoginInfo().getExpiryTime());
                    MemberLoginResponse memberLoginResponse = formatLoginResp(captchaLoginResponse.getMemberInfo(), request, channelCode);
                    if (WSEnum.SUCCESS.getResultCode().equals(memberLoginResponse.getResultCode())) {
                        memberLoginResponse.setToken(captchaLoginResponse.getLoginInfo().getToken());
                        memberLoginResponse.setExpiryTime(captchaLoginResponse.getLoginInfo().getExpiryTime());
                        loginInfo.setMemberLoginResponse(memberLoginResponse);
                        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                        resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                        resp.setObjData(loginInfo);
                        //保存登陆人员设备信息 异步调用
                        if (ChannelCodeEnum.MOBILE.getChannelCode().equalsIgnoreCase(headChannelCode) && loginInfo.getMemberLoginResponse() != null && !StringUtil.isNullOrEmpty(req.getRequest().getDeviceId())) {
                            try {
                                AppCustomer appCustomer = createAppCustomer(loginInfo.getMemberLoginResponse(), req.getRequest().getDeviceId());
                                appCustomer.setClientVersion(req.getClientVersion());
                                DeviceInfo device = new DeviceInfo(req.getRequest().getDeviceId(), req.getRequest().getSystemInfo(), req.getRequest()
                                        .getPushNum(), req.getRequest().getThirdPartyUserInfo().getThirdPartyType() + "_BINDTHIRDLOGIN", ip, appCustomer.getFfp_id() + "", appCustomer.getFfp_card_no());
                                device.setClientVersion(req.getClientVersion());
                                //  2017/12/22利用线程池
                                taskExecutor.execute(new DeviceThread(reqId, appCustomer, device));
                            } catch (Exception e) {
                                log.error(EQUIPMENT_SAVE_ERR, e.getMessage());
                            }
                        }
                    } else {
                        resp.setResultCode(WSEnum.ERROR.getResultCode());
                        resp.setResultInfo(memberLoginResponse.getErrorInfo());
                    }
                } else {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo(ptCRMResponse.getMsg());
                }
            } else {
                if (StringUtils.isNotBlank(ptCRMResponse.getInnerMsg()) &&
                        ptCRMResponse.getInnerMsg().contains("联系方式命中黑名单")) {
                    resp.setResultCode(WSEnum.BLACK_USER_REGISTER.getResultCode());
                    resp.setResultInfo(WSEnum.BLACK_USER_REGISTER.getResultInfo());
                } else {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo(ptCRMResponse.getMsg());
                }
            }
            return resp;
        } catch (Exception e) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR.getResultInfo());
            log.error("第三方帐号绑定登录异常", e);
            return resp;
        }
    }

    @InterfaceLog
    @ApiOperation(value = "第三方帐号绑定(登录态)", notes = "第三方帐号绑定(登录态)")
    @RequestMapping(value = "/bindthirdaccount", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp bindThirdPartyAccount(@RequestBody @Validated BaseReq<BindThirdPartyAccountReq> req, BindingResult bindingResult, HttpServletRequest request) {
        BaseResp resp = new BaseResp();
        BindThirdPartyAccountReq thirdAccount = req.getRequest();
        if ("QQ".equals(thirdAccount.getThirdPartyType())) {
            thirdAccount.setCode("QQ");
        }
        if (bindingResult.hasErrors()) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
            return resp;
        }
        String channelCode = req.getChannelCode();
        try {
            Map<String, Object> reqMap = new HashMap<>();
            if (!StringUtil.isNullOrEmpty(thirdAccount.getThirdPartyType())) {
                reqMap.put(THIRD_PARTY_TYPE, thirdAccount.getThirdPartyType());
                // 如果是qq登录
                if (ThirdPartyTypeEnum.QQ.getEnName().equals(thirdAccount.getThirdPartyType())) {
                    if (StringUtil.isNullOrEmpty(thirdAccount.getAccessToken()) || StringUtil.isNullOrEmpty(thirdAccount.getOpenId())) {
                        resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                        resp.setResultInfo(WSEnum.ERROR_LONGIN_CHK_ERROR.getResultInfo());
                        return resp;
                    }
                    reqMap.put(ACCESS_TOKEN, thirdAccount.getAccessToken());
                    reqMap.put(OPEN_ID, thirdAccount.getOpenId());
                    reqMap.put("Code", "QQ");
                } else if (ThirdPartyTypeEnum.APPLE.getEnName().equals(thirdAccount.getThirdPartyType())) {
                    try {
                        String appleUserId = AppleLoginUtil.verifyIdentityToken(thirdAccount.getIdentityToken());
                        reqMap.put("ThirdPartyUid", appleUserId);
                        reqMap.put("Code", appleUserId);
                    } catch (BusinessException e) {
                        log.error("苹果登录失败, token={}", thirdAccount.getIdentityToken(), e);
                        resp.setResultCode(WSEnum.ERROR.getResultCode());
                        resp.setResultInfo(e.getMessage());
                        return resp;
                    } catch (Exception e) {
                        log.error("苹果登录失败, token={}", thirdAccount.getIdentityToken(), e);
                        resp.setResultCode(WSEnum.ERROR.getResultCode());
                        resp.setResultInfo("苹果账号登陆失败");
                        return resp;
                    }
                } else { //微信，支付宝登录
                    reqMap.put("Code", thirdAccount.getCode());
                }
            } else {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo(UNKNOWN_LOGIN_TYPE);
                return resp;
            }
            PtApiCRMRequest<Map<String, Object>> ptApiRequest = buildCommReq(request, channelCode, req.getRequest().getFfpId(), req.getRequest().getToken());
            ptApiRequest.setData(reqMap);
            PtCRMResponse ptCRMResponse = memberService.memberBindAccout(ptApiRequest);
            if (ptCRMResponse.isIsSuccess()) {
                if (ptCRMResponse.getCode() == 0) {
                    resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                    resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                } else {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo(ptCRMResponse.getMsg());
                }
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo(ptCRMResponse.getMsg());
            }
            return resp;
        } catch (Exception e) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR.getResultInfo());
            return resp;
        }
    }

    @ApiOperation(value = "第三方帐号解绑(登录态)", notes = "第三方帐号解绑(登录态)")
    @RequestMapping(value = "/unBindthirdaccount", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp unBindThirdPartyAccount(@RequestBody BaseReq<UnBindThirdPartyAccountReq> req, HttpServletRequest request) {
        BaseResp resp = new BaseResp();
        String reqId = StringUtil.newGUID() + "_unBindthirdaccount";
        String ip = this.getClientIP(request);
        try {
            String logStr = JsonUtil.objectToJson(req);
            log.info(COMMON_LOG_WITH_REQ_INFO, reqId, ip, logStr);
            ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
            Validator validator = factory.getValidator();
            Set<ConstraintViolation<BaseReq>> violations = validator.validate(req);
            if (CollectionUtils.isNotEmpty(violations)) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo(violations.iterator().next().getMessage());
                return resp;
            }
            UnBindThirdPartyAccountReq accountReq = req.getRequest();
            boolean flag = this.checkKeyInfo(accountReq.getFfpId(), accountReq.getLoginKeyInfo(), req.getChannelCode());
            if (!flag) {
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                return resp;
            }
            String channelCode = req.getChannelCode();
            PtApiCRMRequest<UnBindThirdPartyAccountRequest> ptApiRequest = buildCommReq(request, channelCode, accountReq.getFfpId(), accountReq.getToken());
            UnBindThirdPartyAccountRequest accountRequest = new UnBindThirdPartyAccountRequest();
            accountRequest.setThirdPartyType(accountReq.getThirdPartyType());
            ptApiRequest.setData(accountRequest);
            PtCRMResponse ptCRMResponse = memberService.unBindThirdPartyAccount(ptApiRequest);
            if (ptCRMResponse.isIsSuccess()) {
                if (ptCRMResponse.getCode() == 0) {
                    resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                    resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                } else {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo(ptCRMResponse.getMsg());
                }
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo(ptCRMResponse.getMsg());
            }
            log.info("请求号:{}，IP地址:{}，服务端响应：{}", reqId, ip, resp);
            return resp;
        } catch (Exception e) {
            log.error("请求号:{}，IP地址:{}，服务端异常：", reqId, ip, e);
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR.getResultInfo());
            return resp;
        }
    }

    @InterfaceLog
    @ApiOperation(value = "支付宝实名认证", notes = "支付宝实名认证")
    @RequestMapping(value = "/alipayauth", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp aliPayAuth(@RequestBody @Validated BaseReq<AliPayAuthReq> req, BindingResult bindingResult, HttpServletRequest request) {
        BaseResp resp = new BaseResp();
        resp.success(WSEnum.ERROR.getResultCode(), "此版本过低，请升级版本");
        return resp;
        /*if (bindingResult.hasErrors()) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
            return resp;
        }
        AliPayAuthReq aliPayAuthReq = req.getRequest();
        boolean flag = this.checkKeyInfo(aliPayAuthReq.getFfpId(), aliPayAuthReq.getLoginKeyInfo(), req.getChannelCode());
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        String channelCode = req.getChannelCode();
        ClientInfo clientInfo = initClientInfo(request, channelCode, aliPayAuthReq.getFfpId(), aliPayAuthReq.getFfpCardNo());
        memberPasswordService.alipayResetConsumerPwd(clientInfo, aliPayAuthReq);
        resp.success(WSEnum.SUCCESS.getResultCode(), WSEnum.SUCCESS.getResultInfo());
        return resp;*/
    }

    @ApiOperation(value = "支付宝实名认证，去掉消费密码", notes = "支付宝实名认证，去掉消费密码")
    @RequestMapping(value = "/alipayauthNoPwd", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp aliPayAuthNoPwd(@RequestBody @Validated BaseReq<AliPayAuthNoPwdReq> req, BindingResult bindingResult, HttpServletRequest request) {
        BaseResp resp = new BaseResp();
        if (bindingResult.hasErrors()) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
            return resp;
        }
        AliPayAuthNoPwdReq aliPayAuthReq = req.getRequest();
        if (StringUtils.isNotBlank(aliPayAuthReq.getOperation()) && !SensitiveOperationEnum.checkEnum(aliPayAuthReq.getOperation())) {
            throw new ServiceException("参数超出规定范围值");
        }
        try {
            boolean flag = this.checkKeyInfo(aliPayAuthReq.getFfpId(), aliPayAuthReq.getLoginKeyInfo(), req.getChannelCode());
            if (!flag) {
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                return resp;
            }
            String channelCode = req.getChannelCode();
            //判断年龄不满12周岁不能实名认证
            boolean verifyAge = verifyAge(request, channelCode, aliPayAuthReq.getFfpCardNo(), aliPayAuthReq.getFfpId(), resp);
            if (!verifyAge) {
                return resp;
            }
            PtApiCRMRequest<Map<String, Object>> ptApiRequest = buildCommReq(request, channelCode, aliPayAuthReq.getFfpId());
            Map<String, Object> reqMap = new HashMap();
            reqMap.put("Code", req.getRequest().getCode());
            ptApiRequest.setData(reqMap);
            PtCRMResponse ptCRMResponse = memberService.alipayAuthName(ptApiRequest);
            if (ptCRMResponse.isIsSuccess()) {
                if (ptCRMResponse.getCode() == 0) {
                    String operation = StringUtils.isBlank(aliPayAuthReq.getOperation()) ? "realName" : aliPayAuthReq.getOperation();
                    String redisKey = RedisKeyConfig.ACCOUNT_REAL_METHOD + aliPayAuthReq.getFfpCardNo() + ":" + operation;
                    apiRedisService.putData(redisKey, VerifyChannelEnum.ZhiFuBao.code, 3 * 60L);
                    //成功返回
                    resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                    resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                } else {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo(ptCRMResponse.getMsg());
                }
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo(ptCRMResponse.getMsg());
            }
            return resp;
        } catch (Exception e) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR.getResultInfo());
            return resp;
        }
    }


    /**
     * 判断年龄  不满12周岁不能实名认证
     *
     * @param request
     * @param channelCode
     * @param ffCardNo
     * @param ffpId
     * @return
     */
    private boolean verifyAge(HttpServletRequest request, String channelCode, String ffCardNo, String ffpId, BaseResp resp) {
        //查询会员信息
        String[] items = {MemberDetailRequestItemsEnum.BASICINFO.eName, MemberDetailRequestItemsEnum.CERTIFICATEINFO.eName};
        PtApiCRMRequest ptApiCRMRequest = buildCommReqNoToken(request, channelCode);
        PtMemberDetailRequest ptMemberDetailRequest = new PtMemberDetailRequest();
        ptMemberDetailRequest.setCardNO(ffCardNo);
        ptMemberDetailRequest.setRequestItems(items);
        ptApiCRMRequest.setData(ptMemberDetailRequest);
        PtCRMResponse<PtMemberDetail> crmResponse = memberService.memberDetail(ptApiCRMRequest);
        boolean flag = false;
        if (crmResponse.getCode() == 0) {
            MemberBasicInfoSoaModel basicInfo = crmResponse.getData().getBasicInfo();
            if (ffpId.equals(String.valueOf(basicInfo.getMemberId()))) {
                MemberCertificateSoaModelV2 memberCertificateSoaModelV2 = CRMReqUtil.getCertificateInfo(crmResponse.getData().getCertificateInfo(), CertificateTypeEnum.ID_CARD.getCode());
                if (memberCertificateSoaModelV2 == null || StringUtil.isNullOrEmpty(memberCertificateSoaModelV2.getCertificateNumber())) {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo("请先完善身份证件信息");
                } else {
                    String birthDate = CertUtil.certNoToDate(memberCertificateSoaModelV2.getCertificateNumber());
                    if (StringUtils.isNotBlank(birthDate)) {
                        int age = DateUtils.getAgeByBirthIncludeBirthDay(
                                birthDate, DateUtils.dateToString(new Date(), DateUtils.YYYY_MM_DD_PATTERN), DateUtils.YYYY_MM_DD_PATTERN);
                        if (age >= 12) {
                            flag = true;
                        } else {
                            resp.setResultCode(WSEnum.ERROR.getResultCode());
                            resp.setResultInfo("儿童不支持实名认证");
                        }
                    }
                }
            }
        }
        return flag;
    }

    @ApiOperation(value = "支付宝人脸认证，去掉消费密码", notes = "支付宝实名认证，去掉消费密码")
    @RequestMapping(value = "aliFaceVerification", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp aliFaceVerification(@RequestBody BaseReq<UserInfoMust> req, HttpServletRequest request) {
        BaseResp resp = new BaseResp();
        String ip = this.getClientIP(request);
        String reqId = StringUtil.newGUID() + request.getRequestURI();
        String reqJson = JsonMapper.buildNormalMapper().toJson(req);
        long t1 = System.currentTimeMillis();
        saveReqInfo(SERVICE_NAME, reqId, ip, reqJson);
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<BaseReq>> violations = validator.validate(req);
        if (CollectionUtils.isNotEmpty(violations)) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo(violations.iterator().next().getMessage());
            return resp;
        }
        UserInfoMust userInfo = req.getRequest();
        String channelCode = req.getChannelCode();
        String realChannelCode = request.getHeader(HandlerConstants.TOKEN_CHANNELCODE);
        boolean flag = this.checkKeyInfo(userInfo.getFfpId(), userInfo.getLoginKeyInfo(), channelCode);
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        try {
            String[] items = {MemberDetailRequestItemsEnum.BASICINFO.eName, MemberDetailRequestItemsEnum.CERTIFICATEINFO.eName};
            PtApiCRMRequest<PtMemberDetailRequest> ptMemberDetailRequestPtApiCRMRequest = CRMReqUtil.buildMemberDetailReq(userInfo.getFfpCardNo(), userInfo.getFfpId(), request, channelCode, items);
            PtCRMResponse<PtMemberDetail> ptMemberDetailPtCRMResponse = memberService.memberDetail(ptMemberDetailRequestPtApiCRMRequest);
            if (ptMemberDetailPtCRMResponse.getCode() == 0) {
                PtMemberDetail ptMemberDetail = ptMemberDetailPtCRMResponse.getData();
                String name = CRMReqUtil.getChinaName(ptMemberDetail.getBasicInfo());
                if (StringUtil.isNullOrEmpty(name)) {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo("请先完善中文姓名");
                    return resp;
                }
                MemberCertificateSoaModelV2 memberCertificateSoaModelV2 = CRMReqUtil.getCertificateInfo(ptMemberDetail.getCertificateInfo(), CertificateTypeEnum.ID_CARD.getCode());
                if (memberCertificateSoaModelV2 == null || StringUtil.isNullOrEmpty(memberCertificateSoaModelV2.getCertificateNumber())) {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo("请先完善身份证件信息");
                    return resp;
                }
                //支付宝个人刷脸认证初始化
                InitIndividualidFaceauthRequest initIndividualidFaceauthRequest = new InitIndividualidFaceauthRequest();
                //认证方式,FACE表示在支付宝内进行认证,FACE_SDK表示在客户的应用中进行认证 默认为FACE
                initIndividualidFaceauthRequest.setBizCode("FACE_SDK");
                initIndividualidFaceauthRequest.setCertName(name);
                initIndividualidFaceauthRequest.setCertNo(memberCertificateSoaModelV2.getCertificateNumber());
                //固定值，直接写死plus
                initIndividualidFaceauthRequest.setProductInstanceId("plus");
                InitIndividualidFaceauthResponse initIndividualidFaceauthResponse = antCloudService.FaceAuthInit(initIndividualidFaceauthRequest);
                saveRespInfo(SERVICE_NAME, reqId, ip, t1, reqJson, JsonUtil.objectToJson(initIndividualidFaceauthResponse));
                if (!"200".equals(initIndividualidFaceauthResponse.getResultCode())) {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo(initIndividualidFaceauthResponse.getResultMsg());
                    return resp;
                }
                //支付宝个人刷脸认证开始
                CertifyIndividualidFaceauthRequest certifyIndividualidFaceauthRequest = new CertifyIndividualidFaceauthRequest();
                //通知地址暂不设置，采取主动查询方式
                //certifyIndividualidFaceauthRequest.setCallbackUrl("");
                certifyIndividualidFaceauthRequest.setCertifyId(initIndividualidFaceauthResponse.getCertifyId());
                certifyIndividualidFaceauthRequest.setRedirectUrl(HandlerConstants.MWEB_URL_NEW);
                //固定值，直接写死plus
                certifyIndividualidFaceauthRequest.setProductInstanceId("plus");
                CertifyIndividualidFaceauthResponse certifyIndividualidFaceauthResponse = antCloudService.FaceAuth(certifyIndividualidFaceauthRequest);
                saveRespInfo(SERVICE_NAME, reqId, ip, t1, reqJson, JsonUtil.objectToJson(certifyIndividualidFaceauthResponse));
                if (!"200".equals(certifyIndividualidFaceauthResponse.getResultCode())) {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo(initIndividualidFaceauthResponse.getResultMsg());
                    return resp;
                }
                resp.setObjData(certifyIndividualidFaceauthResponse);
                String key = RedisKeyConfig.ALI_FACE + realChannelCode + ":" + userInfo.getFfpCardNo() + ":" + memberCertificateSoaModelV2.getCertificateNumber();
                //默认设置5分钟的操作有效期
                apiRedisService.putData(key, certifyIndividualidFaceauthResponse.getCertifyId(), 5 * 60L);
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                return resp;
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo(ptMemberDetailPtCRMResponse.getMsg());
                return resp;
            }
        } catch (Exception e) {
            saveError(SERVICE_NAME, reqId, ip, reqJson, e);
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("信息处理异常");
            return resp;
        }
    }

    @InterfaceLog
    @ApiOperation(value = "支付宝人脸认证状态查询", notes = "支付宝人脸认证状态查询")
    @RequestMapping(value = "aliFaceQuery", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp aliFaceQuery(@RequestBody BaseReq<AliFaceQuery> req, HttpServletRequest request) {
        BaseResp resp = new BaseResp();
        String ip = this.getClientIP(request);
        String reqId = StringUtil.newGUID() + request.getRequestURI();
        String reqJson = JsonMapper.buildNormalMapper().toJson(req);
        long t1 = System.currentTimeMillis();
        saveReqInfo(SERVICE_NAME, reqId, ip, reqJson);
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<BaseReq>> violations = validator.validate(req);
        if (CollectionUtils.isNotEmpty(violations)) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo(violations.iterator().next().getMessage());
            return resp;
        }
        AliFaceQuery userInfo = req.getRequest();
        String channelCode = req.getChannelCode();
        String realChannelCode = request.getHeader(HandlerConstants.TOKEN_CHANNELCODE);
        boolean flag = this.checkKeyInfo(userInfo.getFfpId(), userInfo.getLoginKeyInfo(), channelCode);
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        try {
            String[] items = {MemberDetailRequestItemsEnum.BASICINFO.eName, MemberDetailRequestItemsEnum.CERTIFICATEINFO.eName};
            PtApiCRMRequest<PtMemberDetailRequest> ptMemberDetailRequestPtApiCRMRequest = CRMReqUtil.buildMemberDetailReq(userInfo.getFfpCardNo(), userInfo.getFfpId(), request, channelCode, items);
            PtCRMResponse<PtMemberDetail> ptMemberDetailPtCRMResponse = memberService.memberDetail(ptMemberDetailRequestPtApiCRMRequest);
            if (ptMemberDetailPtCRMResponse.getCode() == 0) {
                PtMemberDetail ptMemberDetail = ptMemberDetailPtCRMResponse.getData();
                MemberCertificateSoaModelV2 memberCertificateSoaModelV2 = CRMReqUtil.getCertificateInfo(ptMemberDetail.getCertificateInfo(), CertificateTypeEnum.ID_CARD.getCode());
                if (memberCertificateSoaModelV2 == null || StringUtil.isNullOrEmpty(memberCertificateSoaModelV2.getCertificateNumber())) {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo("请先完善身份证件信息");
                    return resp;
                }
                String key = RedisKeyConfig.ALI_FACE + realChannelCode + ":" + userInfo.getFfpCardNo() + ":" + memberCertificateSoaModelV2.getCertificateNumber();
                String certifyId = apiRedisService.getData(key);
                if (StringUtil.isNullOrEmpty(certifyId)) {
                    resp.setResultCode(WSEnum.OPERATION_TIMEOUT.getResultCode());
                    resp.setResultInfo(WSEnum.OPERATION_TIMEOUT.getResultInfo());
                    return resp;
                }
                if (!certifyId.equals(userInfo.getCertifyId())) {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo("认证信息不匹配");
                    return resp;
                }
                QueryIndividualidFaceauthRequest queryIndividualidFaceauthRequest = new QueryIndividualidFaceauthRequest();
                queryIndividualidFaceauthRequest.setCertifyId(certifyId);
                //固定值，直接写死plus
                queryIndividualidFaceauthRequest.setProductInstanceId("plus");
                QueryIndividualidFaceauthResponse queryIndividualidFaceauthResponse = antCloudService.FaceAuthQuery(queryIndividualidFaceauthRequest);
                saveRespInfo(SERVICE_NAME, reqId, ip, t1, reqJson, JsonUtil.objectToJson(queryIndividualidFaceauthResponse));
                if (!"200".equals(queryIndividualidFaceauthResponse.getResultCode())) {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo(queryIndividualidFaceauthResponse.getResultMsg());
                    return resp;
                }
                //实名认证通过
                if (queryIndividualidFaceauthResponse.getPassed()) {
                    //调用外部实名认证接口
                    PtApiCRMRequest<OuterRealNameRequest> ptApiCRMRequest = buildCommReq(request, channelCode, userInfo.getFfpId(), "");
                    OuterRealNameRequest outerRealNameRequest = new OuterRealNameRequest();
                    outerRealNameRequest.setVerifyChannel(VerifyChannelEnum.Face.code);
                    ptApiCRMRequest.setData(outerRealNameRequest);
                    PtCRMResponse crmResponse = memberService.outRealName(ptApiCRMRequest);
                    if (crmResponse.getCode() != 0) {
                        resp.setResultCode(WSEnum.ERROR.getResultCode());
                        resp.setResultInfo(crmResponse.getMsg());
                        return resp;
                    }
                    resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                    resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                } else {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo("实名认证未通过");
                }
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo(ptMemberDetailPtCRMResponse.getMsg());
            }
        } catch (Exception e) {
            saveError(SERVICE_NAME, reqId, ip, reqJson, e);
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("实名认证失败");
        }
        return resp;
    }

    @ApiOperation(value = "注销（登录态）", notes = "注销（登录态）")
    @RequestMapping(value = "logout", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp logout(@RequestBody BaseReq<LogoutReq> req, HttpServletRequest request) {
        BaseResp resp = new BaseResp();
        try {
            ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
            Validator validator = factory.getValidator();
            Set<ConstraintViolation<BaseReq>> violations = validator.validate(req);
            if (CollectionUtils.isNotEmpty(violations)) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo(violations.iterator().next().getMessage());
                return resp;
            }
            LogoutReq logoutReq = req.getRequest();
            boolean flag = this.checkKeyInfo(logoutReq.getFfpId(), logoutReq.getLoginKeyInfo(), req.getChannelCode());
            if (!flag) {
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                return resp;
            }
            String channelCode = req.getChannelCode();
            PtApiCRMRequest ptApiRequest = buildCommReq(request, channelCode, logoutReq.getFfpId(), logoutReq.getToken());
            PtCRMResponse chekcResp = memberService.logout(ptApiRequest);
            if (0 == chekcResp.getCode()) {//成功
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo(chekcResp.getMsg());
            }
            return resp;
        } catch (Exception e) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR.getResultInfo());
            return resp;
        }
    }

    @ApiOperation(value = "注销（账号）验证", notes = "注销（账号）验证")
    @RequestMapping(value = "memberLogoutVerify", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp<LogoutVerifyResp> memberLogoutVerify(@RequestBody BaseReq<LogoutVerifyReq> req, HttpServletRequest request) {
        BaseResp<LogoutVerifyResp> resp = new BaseResp();
        LogoutVerifyResp logoutVerifyResp = new LogoutVerifyResp();
        /**
         * 验证是否有未完成的订单，以及是否实名认证
         */
        try {
            ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
            Validator validator = factory.getValidator();
            Set<ConstraintViolation<BaseReq>> violations = validator.validate(req);
            if (CollectionUtils.isNotEmpty(violations)) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo(violations.iterator().next().getMessage());
                return resp;
            }
            LogoutVerifyReq logoutVerifyReq = req.getRequest();
            boolean flag = this.checkKeyInfo(logoutVerifyReq.getFfpId(), logoutVerifyReq.getLoginKeyInfo(), req.getChannelCode());
            if (!flag) {
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                return resp;
            }

            /**
             * 未完成订单的判断条件：
             * 取该账号全部订单中的最大航班日期，判断是否早于查询当日超过10个自然日，若符合则判断账号不存在未完成订单；
             */
            boolean logoutFlag = getMaxFlight(request, req, resp);
            //如果没有未完成订单，判断用户的实名认证和基本信息
            if (logoutFlag) {
                getRealNameAndDetail(request, req.getChannelCode(), logoutVerifyReq, logoutVerifyResp, resp);
            } else {
                resp.setResultCode(WSEnum.LOGOUT_VERIFY_ERROR.getResultCode());
                resp.setResultInfo(WSEnum.LOGOUT_VERIFY_ERROR.getResultInfo());
                return resp;
            }
            return resp;
        } catch (Exception e) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR.getResultInfo());
            return resp;
        }
    }

    @RequestMapping(value = "logoutPageInfo", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @ApiOperation(value = "注销页面文案查询", notes = "注销页面文案查询")
    public BaseResp logoutPageInfo(@RequestBody BaseReq<String> req, HttpServletRequest request) {
        BaseResp<LogoutPageInfoConfig> resp = new BaseResp<>();
        String ip = this.getClientIP(request);
        String reqId = StringUtil.newGUID() + request.getRequestURI();
        try {
            //校验参数
            this.checkRequest(req);
            LogoutPageInfoConfig logoutPageInfoConfig = handConfig.getLogoutPageInfoConfig();
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
            resp.setObjData(logoutPageInfoConfig);
        } catch (Exception e) {
            this.logError(resp, reqId, ip, req, e);
        }
        return resp;
    }


    @InterfaceLog
    @RequestMapping(value = "getCoBrandedCardInfo", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @ApiOperation(value = "联名信用卡文案", notes = "联名信用卡文案")
    public BaseResp getCoBrandedCardInfo(HttpServletRequest request) {
        BaseResp<List<CoBrandedCardInfo>> resp = new BaseResp<>();
        String ip = this.getClientIP(request);
        String reqId = StringUtil.newGUID() + request.getRequestURI();
        try {
            List<CoBrandedCardInfo> coBrandedCardInfoList = handConfig.getCoBrandedCardInfoList();
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
            resp.setObjData(coBrandedCardInfoList);
        } catch (Exception e) {
            this.logError(resp, reqId, ip, null, e);
        }
        return resp;
    }



    @RequestMapping(value = "getAssociateMemberInfo", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @ApiOperation(value = "携程联合会员文案", notes = "携程联合会员文案")
    public BaseResp  getAssociateMemberInfo(HttpServletRequest request) {
        BaseResp<List<AssociateMemberInfo>> resp = new BaseResp<>();
        String ip = this.getClientIP(request);
        String reqId = StringUtil.newGUID() + request.getRequestURI();
        try {
            List<AssociateMemberInfo> coBrandedCardInfoList = handConfig.getAssociateMemberInfoList();
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
            resp.setObjData(coBrandedCardInfoList);
        } catch (Exception e) {
            this.logError(resp, reqId, ip, null, e);
        }
        return resp;
    }

    /**
     * 注销账号条件验证  是否含有未完成的订单
     *
     * @param request
     * @param req
     * @param resp
     * @return
     */
    private boolean getMaxFlight(HttpServletRequest request, BaseReq<LogoutVerifyReq> req, BaseResp resp) {

        /**
         * 未完成订单的判断条件：
         * 取该账号全部订单中的最大航班日期，判断是否早于查询当日超过10个自然日，若符合则判断账号不存在未完成订单；
         */
        String userNo = getChannelInfo(req.getChannelCode(), "10");
        OrderMaxFlightDateRequestDto orderMaxFlightDateRequestDto = new OrderMaxFlightDateRequestDto();
        orderMaxFlightDateRequestDto.setVersion(SystemConstants.VERSION);
        orderMaxFlightDateRequestDto.setUserNo(userNo);
        orderMaxFlightDateRequestDto.setChannelCode(req.getChannelCode());
        orderMaxFlightDateRequestDto.setCustomerNo(req.getRequest().getFfpId());
        Map<String, String> headMap = HttpUtil.getHeaderMap(getClientIP(request), "");

        HttpResult result = doPostClient(orderMaxFlightDateRequestDto, HandlerConstants.URL_FARE_API + HandlerConstants.SUB_QUERY_ORDER_MAX, headMap, handConfig.getReadTimeout(), handConfig.getConnectTimeout());
        OrderMaxFlightDateResponseDto orderMaxFlightDateResponseDto = null;

        boolean flag = false;
        if (result.isResult()) {
            orderMaxFlightDateResponseDto = (OrderMaxFlightDateResponseDto) JsonUtil.jsonToBean(result.getResponse(), OrderMaxFlightDateResponseDto.class);
            if (orderMaxFlightDateResponseDto != null) {
                if (orderMaxFlightDateResponseDto.getResultCode().equals("1001")) {

                    String flightDate = orderMaxFlightDateResponseDto.getFlightDate();
                    Date date = DateUtils.addOrLessDay(new Date(), -10);
                    Date arrDate = DateUtils.toDate(flightDate, DateUtils.YYYY_MM_DD_PATTERN);
                    if (!arrDate.before(date)) {
                        flag = false;
                        resp.setResultCode(WSEnum.ERROR.getResultCode());
                        resp.setResultInfo("您的账户存在未完成订单");
                    } else {
                        flag = true;
                    }
                } else if (orderMaxFlightDateResponseDto.getResultCode().equals("9999")) {
                    flag = true;
                }
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo(WSEnum.ERROR.getResultInfo());
            }
        }

        return flag;
    }

    /**
     * 注销账号条件验证通过 获取实名认证状态和基本信息
     *
     * @param request
     * @param channelCode
     * @param logoutVerifyReq
     * @param logoutVerifyResp
     * @param resp
     */
    private BaseResp getRealNameAndDetail(HttpServletRequest request, String channelCode, LogoutVerifyReq logoutVerifyReq, LogoutVerifyResp logoutVerifyResp, BaseResp resp) {

        try {
            //实名认证的信息
            PtRealNameReq ptRealNameReq = new PtRealNameReq();
            ptRealNameReq.setID(logoutVerifyReq.getFfpId());
            ptRealNameReq.setClientCode(channelCode);
            ptRealNameReq.setSignature(EncoderHandler.encodeByMD5(channelCode + logoutVerifyReq.getFfpId() + getClientPwd(channelCode).toUpperCase()));
            PtRealNameResp realNameResp = memberService.realNameState(ptRealNameReq);

            //存在认证审核记录的000   用户未进行过实名认证操作的001
            if ("000".equals(realNameResp.getStatusCode()) || "001".equals(realNameResp.getStatusCode())) {
                VerifyStatusEnum verifyStatusEnum = VerifyStatusEnum.formatVerifyStatus(realNameResp.getVerifyStatus());
//                if (!VerifyStatusEnum.PASS.equals(verifyStatusEnum)) {
//                    resp.setResultCode(WSEnum.NO_REAL_NAME.getResultCode());
//                    resp.setResultInfo("账号未通过实名认证");
//                    return resp;
//                }
                VerifyChannelEnum verifyChannelEnum = VerifyChannelEnum.checkEnum(realNameResp.getVerifyChannel());

                BeanUtils.copyProperties(realNameResp, logoutVerifyResp);
                logoutVerifyResp.setVerifyDesc(verifyStatusEnum == null ? "" : verifyStatusEnum.desc);
                logoutVerifyResp.setVerifyStatus("-2");// 此处返回未认证，兼容6.1.0之前版本app注销
                logoutVerifyResp.setVerifyChannelName(verifyChannelEnum == null ? "" : verifyChannelEnum.desc);
                List<VerifyChannel> verifyChannels = null;

                PtApiCRMRequest<PtMemberDetailRequest> detaiReq = buildCommReq(request, channelCode, logoutVerifyReq.getFfpId(), "");
                PtMemberDetailRequest ptMemberDetailRequest = new PtMemberDetailRequest();
                ptMemberDetailRequest.setCardNO(logoutVerifyReq.getFfpCardNo());
                String[] items = {MemberDetailRequestItemsEnum.BASICINFO.eName, MemberDetailRequestItemsEnum.CONTACTINFO.eName, MemberDetailRequestItemsEnum.CERTIFICATEINFO.eName};
                ptMemberDetailRequest.setRequestItems(items);
                detaiReq.setData(ptMemberDetailRequest);
                PtCRMResponse<PtMemberDetail> detailPtCRMResponse = memberService.memberDetail(detaiReq);
                if (detailPtCRMResponse.getCode() == 0) {
                    PtMemberDetail detail = detailPtCRMResponse.getData();
                    if (detail.getContactInfo() != null) {
                        MemberContactSoaModel memberContactEmail = toMemberContactSoaModel(detail.getContactInfo(), ContactTypeEnum.EMAIL.getCode());
                        if (memberContactEmail != null) {
                            logoutVerifyResp.setPhone(memberContactEmail.getContactNumber());
                            logoutVerifyResp.setPhoneId(AESTool.encrypt(memberContactEmail.getRecordId() + "", HandlerConstants.DEFAULT_TOKEN.substring(0, 16), HandlerConstants.DEFAULT_TOKEN.substring(0, 16)));
                            verifyChannels = addSupportLogoutVerChannel("E");
                        }
                        MemberContactSoaModel memberContactMobile = toMemberContactSoaModel(detail.getContactInfo(), ContactTypeEnum.MOBILE.getCode());
                        if (memberContactMobile != null) {
                            logoutVerifyResp.setPhone(memberContactMobile.getContactNumber());
                            logoutVerifyResp.setPhoneId(AESTool.encrypt(memberContactMobile.getRecordId() + "", HandlerConstants.DEFAULT_TOKEN.substring(0, 16), HandlerConstants.DEFAULT_TOKEN.substring(0, 16)));
                            verifyChannels = addSupportLogoutVerChannel("N");
                        }
                    } else {
                        resp.setResultCode(WSEnum.ERROR.getResultCode());
                        resp.setResultInfo("会员联系信息不能为空！");
                        return resp;
                    }
                    logoutVerifyResp.setLastName(detail.getBasicInfo().getCLastName());
                    logoutVerifyResp.setFirstName(detail.getBasicInfo().getCFirstName());
                    Map<String, String> map = new HashMap<>();
                    map.put("cardNo", logoutVerifyReq.getFfpCardNo());
                    if (CollectionUtils.isNotEmpty(verifyChannels) && verifyChannels.get(0) != null) {
                        map.put("verifyCode", verifyChannels.get(0).getVerifyCode());
                    } else {
                        resp.setResultCode(WSEnum.ERROR.getResultCode());
                        resp.setResultInfo("没有合适的注销验证渠道");
                        return resp;
                    }
                    //将符合注销条件结果放到redis
                    apiRedisService.replaceData("LOGOUT:" + logoutVerifyReq.getFfpId(), JsonUtil.objectToJson(map), 300);
                    //验证码
                    //支持认证的渠道列表
                    logoutVerifyResp.setSupportVerifyChannel(verifyChannels);

                } else {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo(detailPtCRMResponse.getMsg());
                    return resp;
                }

            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo(realNameResp.getMessage());
                return resp;
            }
            resp.setObjData(logoutVerifyResp);
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());

            return resp;
        } catch (Exception e) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR.getResultInfo());
            return resp;
        }

    }


    /**
     * 注销账号身份认证渠道
     *
     * @param verType
     * @return
     */
    private List<VerifyChannel> addSupportLogoutVerChannel(String verType) {
        List<VerifyChannel> verifyChannelList = new ArrayList<>();
        if (!StringUtil.isNullOrEmpty(handConfig.getSupportLogoutVerifyChannel())) {
            List<VerifyChannel> allVerifyChannelList = handConfig.getSupportLogoutVerifyChannel();
            for (VerifyChannel verifyChannel : allVerifyChannelList) {
                //如果BusinessList为空代表适用所有业务
                if (StringUtil.isNullOrEmpty(verifyChannel.getBusinessList())) {
                    if (StringUtil.isNullOrEmpty(verType)) {
                        verifyChannelList.add(verifyChannel);
                    }
                } else {
                    if (!StringUtil.isNullOrEmpty(verType) && verifyChannel.getBusinessList().contains(verType)) {
                        verifyChannelList.add(verifyChannel);
                    }
                }
            }
        }
        return verifyChannelList;
    }

    @ApiOperation(value = "注销（账号）", notes = "注销（账号）")
    @RequestMapping(value = "memberLogout", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @InterfaceLog
    public BaseResp memberLogout(@RequestBody @Validated BaseReq<LogoutAuthVerifyReq> req, BindingResult bindingResult, HttpServletRequest request) {
        BaseResp resp = new BaseResp();
        checkRequest(req, bindingResult);
        String ip = this.getClientIP(request);
        LogoutAuthVerifyReq logoutReq = req.getRequest();
        boolean flag = this.checkKeyInfo(logoutReq.getFfpId(), logoutReq.getLoginKeyInfo(), req.getChannelCode());
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        String regCacheKey = "LOGOUT:" + logoutReq.getFfpId();
        String cardNo = logoutReq.getFfpCardNo();
        String verifyCode = logoutReq.getVerifyCode();
        String errorInfo = "";
        //取出符合条件会员卡号，判断是否存在
        String veryCodeCacheStr = apiRedisService.getData(regCacheKey);
        log.info("redis 注销条件验证结果：" + veryCodeCacheStr);
        if (StringUtils.isBlank(veryCodeCacheStr)) {
            errorInfo = "注销账号条件验证已失效！";
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo(errorInfo);
            resp.setResultInfo(errorInfo);
            return resp;
        } else {
            Type type = new TypeToken<Map<String, String>>() {
            }.getType();
            Map<String, String> map = (Map<String, String>) JsonUtil.jsonToMap(veryCodeCacheStr, type);
            String redisCardNo = map.get("cardNo");
            String redisVerifyCode = map.get("verifyCode");
            //验证渠道
            if (StringUtils.isBlank(verifyCode) || !verifyCode.equals(redisVerifyCode)) {
                errorInfo = "注销身份验证渠道不匹配！";
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo(errorInfo);
                resp.setResultInfo(errorInfo);
                return resp;
            }
            //验证卡号
            if (StringUtils.isBlank(cardNo) || !cardNo.equals(redisCardNo)) {
                errorInfo = "输入用户卡号与注销账号条件验证卡号不匹配！";
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo(errorInfo);
                resp.setResultInfo(errorInfo);
                return resp;
            }
        }

        /**
         * 根据不同的身份验证方式，判断验证是否通过
         */
        boolean logoutFlag;
        if (logoutReq.getVerifyCode().equals(VerifyChannelEnum.ZhiFuBao.code)) {
            //支付宝验证
            logoutFlag = aliPayAuth(request, req, resp);
        } else if (SourceType.MEMBERLOGOUT_MOBILE_SOURCE.getValue().equals(logoutReq.getVerifyCode()) ||
                SourceType.MEMBERLOGOUT_EMAIL_SOURCE.getValue().equals(logoutReq.getVerifyCode())) {
            //验证码验证
            logoutFlag = checkVerifyCode(request, req, resp);
        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("注销账号身份验证渠道不正确！");
            return resp;
        }
        if (logoutFlag) {
            String channelCode = req.getChannelCode();
            PtApiCRMRequest<MemberLogoutRequest> ptApiRequest = CRMReqUtil.buildCommReq(channelCode, logoutReq.getFfpId(), ip);
            MemberLogoutRequest memberLogoutRequest = new MemberLogoutRequest();
            memberLogoutRequest.setMemberCardNo(logoutReq.getFfpCardNo());
            memberLogoutRequest.setMemberID(Long.valueOf(logoutReq.getFfpId()));
            ptApiRequest.setData(memberLogoutRequest);
            PtCRMResponse ptCRMResponse = memberService.memberLogout(ptApiRequest);
            if (0 == ptCRMResponse.getCode()) {//成功
                //清除历史登录token
                TokenUtils.logoutAll(logoutReq.getFfpCardNo());
                //注销成功设置为关闭账户
                TokenUtils.verifyIsClosedNewest(logoutReq.getFfpId(), logoutReq.getFfpCardNo(), request);
                // 2025-07-07 通知sso平台刷新账号状态信息
                authService.refreshAccountStatus(channelCode, logoutReq.getFfpCardNo());
                resp.success(WSEnum.SUCCESS.getResultCode(), WSEnum.SUCCESS.getResultInfo());
            } else {
                resp.fail(WSEnum.ERROR.getResultCode(), ptCRMResponse.getDesc());
            }
        }
        return resp;
    }

    /**
     * 注销账号身份验证  支付宝验证
     *
     * @param request
     * @param req
     * @param resp
     * @return
     */
    private boolean aliPayAuth(HttpServletRequest request, BaseReq<LogoutAuthVerifyReq> req, BaseResp resp) {

        AliPayAuthNoPwdReq aliPayAuthReq = new AliPayAuthNoPwdReq();
        BeanUtils.copyProperties(req.getRequest(), aliPayAuthReq);
        //验证支付宝授权码
        if (StringUtil.isNullOrEmpty(aliPayAuthReq.getCode())) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo("支付宝授权码不能为空！");
            resp.setResultInfo("支付宝授权码不能为空！");
            return false;
        }
        boolean flag = false;
        String channelCode = req.getChannelCode();
        PtApiCRMRequest<Map<String, Object>> ptApiRequest = buildCommReq(request, channelCode, aliPayAuthReq.getFfpId());
        Map<String, Object> reqMap = new HashMap();
        reqMap.put("Code", req.getRequest().getCode());
        ptApiRequest.setData(reqMap);
        PtCRMResponse ptCRMResponse = memberService.alipayAuthName(ptApiRequest);
        if (ptCRMResponse.isIsSuccess()) {
            if (ptCRMResponse.getCode() == 0) {
                //成功返回
                flag = true;
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo(ptCRMResponse.getMsg());
            }
        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(ptCRMResponse.getMsg());
        }

        return flag;
    }

    /**
     * 注销账号身份验证 短信/邮箱验证
     *
     * @param request
     * @param req
     * @param resp
     * @return
     */
    private boolean checkVerifyCode(HttpServletRequest request, BaseReq<LogoutAuthVerifyReq> req, BaseResp resp) {
        LogoutAuthVerifyReq logoutAuthVerifyReq = req.getRequest();
        String pro = "";
        String error = "手机号码";
        if (!SourceType.MEMBERLOGOUT_MOBILE_SOURCE.getValue().equals(logoutAuthVerifyReq.getVerifyCode())) {
            error = "邮箱";
            String email = mobileMemberService.getMemberEmail(logoutAuthVerifyReq.getFfpCardNo(), ChannelCodeEnum.MOBILE.getChannelCode(), request);
            if (!email.equals(logoutAuthVerifyReq.getMobileNo())) {
                throw new ServiceException("输入邮箱与预留邮箱不一致");
            }
        } else {
            pro = "SMS:";
            CrmPhoneInfo crmPhoneInfo = mobileMemberService.getMemberPhone(logoutAuthVerifyReq.getFfpCardNo(), ChannelCodeEnum.MOBILE.getChannelCode(), request);
            if (!crmPhoneInfo.getPhone().equals(logoutAuthVerifyReq.getMobileNo())) {
                throw new ServiceException("输入手机号与预留手机号不一致");
            }
        }
        //验证手机号码
        if (StringUtil.isNullOrEmpty(logoutAuthVerifyReq.getMobileNo())) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo(error + "不能为空！");
            resp.setResultInfo(error + "不能为空！");
            return false;
        }
        //验证验证码
        if (StringUtil.isNullOrEmpty(logoutAuthVerifyReq.getVeriCode())) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo("验证码不能为空！");
            resp.setResultInfo("验证码不能为空！");
            return false;
        }
        //验证短信/邮箱类型
        if (StringUtil.isNullOrEmpty(logoutAuthVerifyReq.getType())) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo("类型不能为空！");
            resp.setResultInfo("类型不能为空！");
            return false;
        }

        String ip = this.getClientIP(request);
        log.info(LOG_RESP_TEN, req.getChannelCode(), ip, logoutAuthVerifyReq.getMobileNo());
        if (!SourceType.checkType(logoutAuthVerifyReq.getType())) {
            resp.fail(WSEnum.ERROR.getResultCode(), "非法的请求类型！");
            return false;
        }
        //验证IP
        if (!this.chkDayOptErr(ip, logoutAuthVerifyReq.getType(), "")) {
            resp.fail(WSEnum.ERROR.getResultCode(), "IP操作过于频繁！");
            return false;
        }
        //验证手机
        if (!this.chkDayOptErr(logoutAuthVerifyReq.getMobileNo(), logoutAuthVerifyReq.getType(), "")) {
            resp.fail(WSEnum.ERROR.getResultCode(), "账号操作过于频繁！");
            return false;
        }
        String veryCode = logoutAuthVerifyReq.getVeriCode();
        if (!checkVeriCode(pro, logoutAuthVerifyReq.getMobileNo(), logoutAuthVerifyReq.getType(), veryCode)) {
            String errorInfo = "验证码错误或已经失效！";
            resp.fail(WSEnum.ERROR.getResultCode(), errorInfo);
            return false;
        }
        this.clearDayVisit("", logoutAuthVerifyReq.getMobileNo(), logoutAuthVerifyReq.getType(), "");//验证成功，清除账号限制
        return true;
    }


    /**
     * 验证验证码
     *
     * @param dir  键值
     * @param type 类型
     * @param code 验证码
     * @return
     */
    @Override
    public boolean checkVeriCode(String dir, String pre, String type, String code) {
        String veryCodeCacheStr = apiRedisService.getData(dir + pre + type);
        log.info("key：" + dir + pre + type);
        log.info("验证码：" + veryCodeCacheStr);
        return !(StringUtils.isBlank(code) || StringUtils.isBlank(veryCodeCacheStr) || !code.equals(veryCodeCacheStr));
    }

    @InterfaceLog
    @ApiOperation(value = "福卡会员申请", notes = "福卡会员申请")
    @RequestMapping(value = "applyMember", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp applyMember(@RequestBody @Validated BaseReq<MemberApplyReq> req, BindingResult bindingResult, HttpServletRequest request) {
        BaseResp resp = new BaseResp();
        if (bindingResult.hasErrors()) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
            return resp;
        }
        MemberApplyReq memberApplyReq = req.getRequest();
        boolean flag = this.checkKeyInfo(memberApplyReq.getFfpId(), memberApplyReq.getLoginKeyInfo(), req.getChannelCode());
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        try {
            String channelCode = req.getChannelCode();
            PtModifyCustomerInfoRequest ptModifyCustomerInfoRequest = buildPtModifyCustomerInfoRequest(req, resp);
            //身份证类型的根据证件判断下基本信息
            boolean isIdCard = CertificateTypeEnum.ID_CARD.geteName().equals(memberApplyReq.getCertificateType());
            if (isIdCard) {
                Pattern pattern = Pattern.compile(PatternCommon.ID_NUMBER);
                Matcher matcher = pattern.matcher(memberApplyReq.getCertificateNumber());
                if (matcher.matches()) {
                    String birthDate = CertUtil.certNoToDate(memberApplyReq.getCertificateNumber());
                    if (StringUtil.isNullOrEmpty(birthDate)) {
                        resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                        resp.setResultInfo(WRONG_IDCARD);
                        return resp;
                    }
                    Date birDate = DateUtils.toDate(birthDate, DateUtils.YYYY_MM_DD_PATTERN);
                    ptModifyCustomerInfoRequest.setBirthday(birDate.getTime());
                    String sex = CertUtil.checkSex(memberApplyReq.getCertificateNumber());
                    ptModifyCustomerInfoRequest.setSex(sex);
                    ptModifyCustomerInfoRequest.setSalutationCode("M".equals(sex) ? SalutationEnum.MR.geteName() : SalutationEnum.MS.geteName());
                } else {
                    resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                    resp.setResultInfo(WRONG_IDCARD);
                    return resp;
                }
                //身份证的情况下，中文姓名不能为空
                if (StringUtils.isBlank(memberApplyReq.getCLastName()) || StringUtils.isBlank(memberApplyReq.getCFirstName())) {
                    resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                    resp.setResultInfo("请输入中文姓名！");
                    return resp;
                }
            } else {
                //其他证件下，英文姓名不能为空
                if (StringUtils.isBlank(memberApplyReq.getELastName()) || StringUtils.isBlank(memberApplyReq.getEFirstName())) {
                    resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                    resp.setResultInfo("请输入英文姓名！");
                    return resp;
                }
                ptModifyCustomerInfoRequest.setSex(SexEnum.UNKNOWN.eName);
                //护照
                if (CertificateTypeEnum.PASSPORT.geteName().equals(memberApplyReq.getCertificateType())) {
                    ptModifyCustomerInfoRequest.setSex(StringUtil.isNullOrEmpty(memberApplyReq.getSex()) ? SexEnum.UNKNOWN.eName : memberApplyReq.getSex());
                    if (!StringUtil.isNullOrEmpty(memberApplyReq.getBirthDate())) {
                        Date birthDate = DateUtils.toDate(memberApplyReq.getBirthDate(), DateUtils.YYYY_MM_DD_PATTERN);
                        if (birthDate == null) {
                            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                            resp.setResultInfo("出生日期格式：yyyy-MM-dd！");
                            return resp;
                        }
                        ptModifyCustomerInfoRequest.setBirthday(birthDate.getTime());
                    }
                }
            }
            //查询下现有信息，比对是否可以修改姓名信息
            String[] items = {MemberDetailRequestItemsEnum.BASICINFO.eName,
                    MemberDetailRequestItemsEnum.CERTIFICATEINFO.eName,
                    MemberDetailRequestItemsEnum.REALVERIFYINFOS.eName};
            PtApiCRMRequest<PtMemberDetailRequest> ptApiCRMRequest = CRMReqUtil.buildMemberDetailReq(memberApplyReq.getFfpCardNo(), memberApplyReq.getFfpId(), request, channelCode, items);
            PtCRMResponse<PtMemberDetail> ptMemberDetailPtCRMResponse = memberService.memberDetail(ptApiCRMRequest);
            if (ptMemberDetailPtCRMResponse.getCode() != 0) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo(ptMemberDetailPtCRMResponse.getMsg());
                return resp;
            }
            PtMemberDetail ptMemberDetail = ptMemberDetailPtCRMResponse.getData();
            boolean addCertFlag = true;//是否添加证件
            boolean modifyCustomInfo = false;//是否修改客户基础信息
            if (StringUtil.isNullOrEmpty(ptMemberDetail.getCertificateInfo())) {
                addCertFlag = true;
            } else {
                boolean isExist = ptMemberDetail.getCertificateInfo().stream().anyMatch(memberCertificateSoaModelV2 -> memberApplyReq.getCertificateNumber().equals(memberCertificateSoaModelV2.getCertificateNumber()));
                if (isExist) {
                    addCertFlag = false;
                }
            }
            if (addCertFlag) {
                PtApiCRMRequest<PtAddMemberCertificateRequest> ptApiRequest = buildCommReq(request, channelCode, memberApplyReq.getFfpId(), memberApplyReq.getToken());
                PtAddMemberCertificateRequest ptAddMemberCertificateRequest = buildPtAddMemberCertificateRequest(req, resp);
                if (!WSEnum.SUCCESS.getResultCode().equals(resp.getResultCode())) {
                    return resp;
                }
                //会员申请  先添加证件 后修改信息
                ptApiRequest.setData(ptAddMemberCertificateRequest);
                PtCRMResponse addCertResponse = memberService.addMemberCertificate(ptApiRequest);
                if (addCertResponse.getCode() != 0) {
                    if (addCertResponse.getCode() == 122005) {
                        String[] items1 = {MemberDetailRequestItemsEnum.BASICINFO.eName, MemberDetailRequestItemsEnum.CONTACTINFO.eName};
                        PtApiCRMRequest<PtMemberDetailRequest> ptApiCRMRequest1 = buildMemberQueryCardNoReq(req.getRequest().getCertificateType(), req.getRequest().getCertificateNumber(), "", request, channelCode, items1);
                        //构建查询证件号所绑定会员信息
                        PtCRMResponse<PtMemberDetail> crmResponse = memberService.memberDetail(ptApiCRMRequest1);
                        PtCrmMileageRequest<MemberLevelChangeReq> memberLevelChangeReqBaseReq = new PtCrmMileageRequest<>();
                        MemberLevelChangeReq memberLevelChangeReq = new MemberLevelChangeReq();
                        memberLevelChangeReq.setId(String.valueOf(crmResponse.getData().getBasicInfo().getMemberId()));
                        memberLevelChangeReqBaseReq.setChannel(req.getChannelCode());
                        memberLevelChangeReqBaseReq.setClientIP(req.getClientIp());
                        if ("WEIXIN".equals(req.getChannelCode())) {
                            memberLevelChangeReqBaseReq.setChannelPwd(HandlerConstants.W_CLIENT_PWD);
                        }
                        if ("MOBILE".equals(req.getChannelCode())) {
                            memberLevelChangeReqBaseReq.setChannelPwd(HandlerConstants.M_CLIENT_PWD);
                        }
                        if ("WXAPP".equals(req.getChannelCode())) {
                            memberLevelChangeReqBaseReq.setChannelPwd(HandlerConstants.WXAPP_CLIENT_PWD);
                        }
                        if ("MWEB".equals(req.getChannelCode())) {
                            memberLevelChangeReqBaseReq.setChannelPwd(HandlerConstants.MWEB_CLIENT_PWD);
                        }
                        memberLevelChangeReqBaseReq.setTimestamp(req.getTimeStamp());
                        memberLevelChangeReqBaseReq.setVersion(req.getVersionCode());
                        memberLevelChangeReqBaseReq.setData(memberLevelChangeReq);
                        NumberAndTypeResponse numberAndTypeResponse = new NumberAndTypeResponse();
                        //该账号已存在 判断此账号用户是否有会员等级变动
                        HttpResult httpResult = this.doPostClient(memberLevelChangeReqBaseReq, HandlerConstants.CRM_OPENAPI_URL + HandlerConstants.QUERY_MEMBERLEVEL_LEVELCHANGE);
                        if (httpResult.isResult()) {
                            MemberchangeResp<MermberChangeReq> result = JSON.parseObject(httpResult.getResponse(), new TypeReference<MemberchangeResp<MermberChangeReq>>() {
                            });
                            if (result.getCode() == 0) {
                                List<Memberchange> items9 = result.getData().getItems();
                                if (!ObjectUtils.isEmpty(items9)) {
                                    if (ObjectUtils.isEmpty(crmResponse.getData())) {
                                        resp.setResultCode(WSEnum.NO_DATA.getResultCode());
                                        resp.setResultInfo("未查询到该证件对应的基本信息");
                                        return resp;
                                    }
                                    MemberBasicInfoSoaModel basicInfo = crmResponse.getData().getBasicInfo();
                                    String cardNO = basicInfo.getCardNO();
                                    log.info("会员卡号{}" + cardNO);
                                    PtApiCRMRequest<PtMemberDetailRequest> ptMemberDetailRequestPtApiCRMRequest = buildMemberDetailReq(cardNO, String.valueOf(basicInfo.getMemberId()), request, channelCode, items1);
                                    PtCRMResponse<PtMemberDetail> crmResponse1 = memberService.memberDetail(ptMemberDetailRequestPtApiCRMRequest);
                                    if (ObjectUtils.isEmpty(crmResponse1.getData())) {
                                        resp.setResultCode(WSEnum.NO_DATA.getResultCode());
                                        resp.setResultInfo("未查询到该证件对应的基本信息");
                                        return resp;
                                    }
                                    MemberContactSoaModel memberContactSoaModel = crmResponse1.getData().getContactInfo().stream().filter(certificateInfo -> certificateInfo.getContactType() == ContactTypeEnum.MOBILE.getCode()).findFirst().orElse(null);
                                    if (memberContactSoaModel != null && !StringUtil.isNullOrEmpty(memberContactSoaModel.getContactNumber())) {
                                        numberAndTypeResponse.setNumber(memberContactSoaModel.getContactNumber());
                                        numberAndTypeResponse.setType("1");
                                        resp.setResultCode(WSEnum.CHOOSE_HINT.getResultCode());
                                        resp.setResultInfo(numberAndTypeResponse.getNumber());
                                        resp.setObjData(numberAndTypeResponse);
                                    } else {
                                        MemberContactSoaModel memberContactSoaModel1 = crmResponse1.getData().getContactInfo().stream().filter(certificateInfo -> certificateInfo.getContactType() == ContactTypeEnum.EMAIL.getCode()).findFirst().orElse(null);
                                        if (ObjectUtils.isEmpty(memberContactSoaModel1)) {
                                            resp.setResultCode(WSEnum.NO_DATA.getResultCode());
                                            resp.setResultInfo(WSEnum.NO_DATA.getResultInfo());
                                            return resp;
                                        } else {
                                            numberAndTypeResponse.setNumber(memberContactSoaModel1.getContactNumber());
                                            numberAndTypeResponse.setType("2");
                                            resp.setResultCode(WSEnum.CHOOSE_HINT.getResultCode());
                                            resp.setResultInfo(numberAndTypeResponse.getNumber());
                                            resp.setObjData(numberAndTypeResponse);
                                        }
                                    }
                                } else {
                                    resp.setResultCode(WSEnum.CERT_NUMBER_EXIST.getResultCode());
                                    resp.setResultInfo(WSEnum.CERT_NUMBER_EXIST.getResultInfo());
                                }
                            } else {
                                resp.setResultCode(WSEnum.CERT_NUMBER_EXIST.getResultCode());
                                resp.setResultInfo(WSEnum.CERT_NUMBER_EXIST.getResultInfo());
                            }
                        }
                    } else {
                        resp.setResultCode(WSEnum.ERROR.getResultCode());
                        resp.setResultInfo(addCertResponse.getMsg());
                    }
                    return resp;
                }
                modifyCustomInfo = true;//添加证件的允许直接修改客户信息
            } else {
                boolean allowModify = false;
                List<MemberRealNameSummarySoaModel> realVerifyInfos = ptMemberDetail.getRealVerifyInfos();
                //验证是否实名认证 未实名的情况允许修改
                if (!CrmUtil.judgeRealNameStatus(realVerifyInfos)) {
                    allowModify = true;
                }
                modifyCustomInfo = allowModify || modifyCustomInfo;
                /*if (isIdCard) {//身份证
                    if (StringUtil.isNullOrEmpty(memberBasicInfoSoaModel.getCFirstName()) && StringUtil.isNullOrEmpty(memberBasicInfoSoaModel.getCLastName())) {
                        modifyCustomInfo = true;
                    }
                } else {//除身份证之外的证件
                    if (StringUtil.isNullOrEmpty(memberBasicInfoSoaModel.getEFirstName()) && StringUtil.isNullOrEmpty(memberBasicInfoSoaModel.getELastName())) {
                        modifyCustomInfo = true;
                    }
                }*/
            }
            if (modifyCustomInfo) {
                PtApiCRMRequest<PtModifyCustomerInfoRequest> ptModifyRequest = buildCommReq(request, channelCode, memberApplyReq.getFfpId(), memberApplyReq.getToken());
                ptModifyRequest.setData(ptModifyCustomerInfoRequest);
                PtCRMResponse modifyResponse = memberService.modifyCustomerInfo(ptModifyRequest);
                if (modifyResponse.getCode() != 0) {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo(modifyResponse.getMsg());
                    return resp;
                } else {
                    resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                    resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                    return resp;
                }
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("请联系客服修改");
                return resp;
            }
        } catch (Exception e) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR.getResultInfo());
            return resp;
        }
    }

    @ApiOperation(value = "会员详细信息", notes = "会员详细信息(包含基本信息，证件信息,地址信息，联系方式)")
    @RequestMapping(value = "memberdetail", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp<MemberDetailResp> memberDetail(@RequestBody @Validated BaseReq<MemberDetailReq> req, BindingResult bindingResult, HttpServletRequest request) {
        BaseResp resp = new BaseResp();
        try {
            if (bindingResult.hasErrors()) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
                return resp;
            }
            MemberDetailReq memberDetailReq = req.getRequest();
            String channelCode = req.getChannelCode();
            boolean flag = this.checkKeyInfo(memberDetailReq.getFfpId(), memberDetailReq.getLoginKeyInfo(), req.getChannelCode());
            if (!flag) {
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                return resp;
            }
            String memberCardNo = memberDetailReq.getFfpCardNo();
            String memberId = memberDetailReq.getFfpId();
            String[] items = {MemberDetailRequestItemsEnum.BASICINFO.eName
                    , MemberDetailRequestItemsEnum.STATEINFO.eName
                    , MemberDetailRequestItemsEnum.CERTIFICATEINFO.eName
                    , MemberDetailRequestItemsEnum.CONTACTINFO.eName
                    , MemberDetailRequestItemsEnum.ADDRESSINFOS.eName
                    , MemberDetailRequestItemsEnum.THIRDPARTYINFOS.eName};
            PtApiCRMRequest<PtMemberDetailRequest> ptApiRequest = buildMemberDetailReq(memberCardNo, memberId, request, channelCode, items);
            PtCRMResponse<PtMemberDetail> ptCRMResponse = memberService.memberDetail(ptApiRequest);
            if (ptCRMResponse.getCode() == 0) {
                MemberDetailResp detail = new MemberDetailResp();
                MemberBasicInfoSoaModel basicInfo = ptCRMResponse.getData().getBasicInfo();
                List<MemberContactSoaModel> contactSoaModelList = ptCRMResponse.getData().getContactInfo();
                List<MemberAddressSoaModel> addressInfos = ptCRMResponse.getData().getAddressInfos();
                detail.setCLastName(basicInfo.getCLastName());
                detail.setCFirstName(basicInfo.getCFirstName());
                detail.setELastName(basicInfo.getELastName());
                detail.setEFirstName(basicInfo.getEFirstName());
                detail.setMemberHeadImgUrl(basicInfo.getHeadImageUrl());
                //证件处理
                List<CertificateModel> certList = null;
                if (!StringUtil.isNullOrEmpty(ptCRMResponse.getData().getCertificateInfo())) {
                    certList = new ArrayList<>();
                    for (MemberCertificateSoaModelV2 c : ptCRMResponse.getData().getCertificateInfo()) {
                        CertificateModel cert = new CertificateModel();
                        BeanUtils.copyProperties(c, cert);
                        CertificateTypeEnum ct = CertificateTypeEnum.checkType(cert.getCertificateType());
                        cert.setCertificateTypeEName(ct.geteName());
                        cert.setCertificateTypeCName(ct.getDesc());
                        certList.add(cert);
                    }
                }
                detail.setCertList(certList);
                //联系方式
                if (!StringUtil.isNullOrEmpty(contactSoaModelList)) {
                    for (MemberContactSoaModel con : contactSoaModelList) {
                        //手机号
                        if (con.getContactType() == ContactTypeEnum.MOBILE.getCode()) {
                            detail.setMobileNum(con.getContactNumber());
                        }
                        //邮箱
                        if (con.getContactType() == ContactTypeEnum.EMAIL.getCode()) {
                            detail.setMemberEmail(con.getContactNumber());
                        }
                    }
                }
                //完善会员信息标记
                if (StringUtil.isNullOrEmpty(certList)) {
                    detail.setImproveFlag(true);
                }
                //地址信息
                if (!StringUtil.isNullOrEmpty(addressInfos)) {
                    AddressInfo addressInfo = new AddressInfo();
                    MemberAddressSoaModel addr = addressInfos.get(0);
                    addressInfo.setCountryCode(addr.getCountryCode());
                    addressInfo.setProvinceCode(addr.getProvinceCode());
                    addressInfo.setAddressCityCode(addr.getCityCode());
                    addressInfo.setAddressContent(addr.getAddress());
                    addressInfo.setPostCode(addr.getPostCode());
                    detail.setAddressInfo(addressInfo);
                }
                //第三方账户信息
                dealThirdPartAccount(ptCRMResponse.getData().getThirdpartyInfos(), detail);
                resp.setObjData(detail);
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo(ptCRMResponse.getMsg());
            }
            return resp;
        } catch (Exception e) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("查询会员详情出错！");
            return resp;
        }
    }


    @ApiOperation(value = "支付宝小程序会员详细信息", notes = "会员详细信息(会员id、会员卡号、手机号、中英文姓名、邮箱)")
    @RequestMapping(value = "memberDetailAliapp", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp<MemberDetailAliResp> memberDetailAliapp(HttpServletRequest request) {
        BaseResp resp = new BaseResp();
        try {
            //1.获取token，拼装用户信息
            String token = request.getHeader(TOKEN);
            String channelCode = request.getHeader(CHANNEL_CODE);

            if (StringUtils.isBlank(channelCode)) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("渠道用户号不能为空");
                return resp;
            }
            if (StringUtils.isBlank(token)) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("token 不能为空！");
                return resp;
            }
            ParseTokenResult parseTokenResult = authService.parseToken(channelCode, token);
            String memberCardNo = parseTokenResult.getFfpNo();
            String memberId = parseTokenResult.getFfpId();
            String[] items = {MemberDetailRequestItemsEnum.BASICINFO.eName
                    , MemberDetailRequestItemsEnum.CONTACTINFO.eName
            };
            PtApiCRMRequest<PtMemberDetailRequest> ptApiRequest = buildMemberDetailReq(memberCardNo, memberId, request, channelCode, items);
            PtCRMResponse<PtMemberDetail> ptCRMResponse = memberService.memberDetail(ptApiRequest);
            if (ptCRMResponse.getCode() == 0) {
                MemberDetailAliResp detail = new MemberDetailAliResp();
                MemberBasicInfoSoaModel basicInfo = ptCRMResponse.getData().getBasicInfo();
                List<MemberContactSoaModel> contactSoaModelList = ptCRMResponse.getData().getContactInfo();
                detail.setCLastName(basicInfo.getCLastName());
                detail.setCFirstName(basicInfo.getCFirstName());
                detail.setELastName(basicInfo.getELastName());
                detail.setEFirstName(basicInfo.getEFirstName());
                detail.setFfpCardNo(memberCardNo);
                detail.setFfpId(memberId);
                //联系方式
                if (!StringUtil.isNullOrEmpty(contactSoaModelList)) {
                    for (MemberContactSoaModel con : contactSoaModelList) {
                        //手机号
                        if (con.getContactType() == ContactTypeEnum.MOBILE.getCode()) {
                            detail.setMobileNum(con.getContactNumber());
                        }
                        //邮箱
                        if (con.getContactType() == ContactTypeEnum.EMAIL.getCode()) {
                            detail.setMemberEmail(con.getContactNumber());
                        }
                    }
                }
                resp.setObjData(detail);
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo(ptCRMResponse.getMsg());
            }
            return resp;
        } catch (Exception e) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("查询会员详情出错！");
            return resp;
        }
    }


    @ApiOperation(value = "会员我的页面", notes = "会员我的页面")
    @RequestMapping(value = "membermy", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @InterfaceLog
    public BaseResp<MyBriefInfo> memberMy(@RequestBody @Validated BaseReq<MemberDetailReq> req, BindingResult bindingResult, HttpServletRequest request) {
        BaseResp resp = new BaseResp();
        String ip = this.getClientIP(request);
        try {
            if (bindingResult.hasErrors()) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
                return resp;
            }
            MemberDetailReq memberDetailReq = req.getRequest();
            String channelCode = req.getChannelCode();
            boolean flag = this.checkKeyInfo(memberDetailReq.getFfpId(), memberDetailReq.getLoginKeyInfo(), req.getChannelCode());
            if (!flag) {
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                return resp;
            }
            //异步多线程查询，加快响应速度
            //会员信息处理
            String memberCardNo = memberDetailReq.getFfpCardNo();
            Callable memberCall = () -> {
                //会员信息处理
                PtApiCRMRequest<PtMemberDetailRequest> ptApiRequest = buildCommReq(request, req.getChannelCode(), memberDetailReq.getFfpId(), memberDetailReq.getToken());
                PtMemberDetailRequest ptMemberDetailRequest = new PtMemberDetailRequest();
                ptMemberDetailRequest.setCardNO(memberCardNo);
                String[] items = {MemberDetailRequestItemsEnum.BASICINFO.eName, MemberDetailRequestItemsEnum.CONTACTINFO.eName,
                        MemberDetailRequestItemsEnum.STATEINFO.eName, MemberDetailRequestItemsEnum.CERTIFICATEINFO.eName,
                        MemberDetailRequestItemsEnum.REALVERIFYINFOS.eName};
                ptMemberDetailRequest.setRequestItems(items);
                ptApiRequest.setData(ptMemberDetailRequest);
                PtCRMResponse<PtMemberDetail> ptCRMResponse = memberService.memberDetail(ptApiRequest);
                return ptCRMResponse;
            };
            Callable totalScore = () -> {
                //获取总积分数
                PtApiCRMRequest ptApiCRMRequest = CRMReqUtil.buildCommReq(channelCode, getClientPwd(channelCode), memberDetailReq.getFfpId(), "", ip);
                MileageAccountQueryRequest mileageAccountQueryRequest = new MileageAccountQueryRequest();
                String[] items = new String[]{MileageAccountRequestItemsEnum.TOTALBILL.eName};
                mileageAccountQueryRequest.setMemberCardNo(memberDetailReq.getFfpCardNo());
                mileageAccountQueryRequest.setRequestItems(items);
                ptApiCRMRequest.setData(mileageAccountQueryRequest);
                MemberRemainScoreResp memberRemainScoreResp = memberService.queryMemberRemainScore(ptApiCRMRequest);
                return memberRemainScoreResp;
            };
            //查询会员航段
            Callable currentSegmentCall = () -> {
                PtCrmMileageResponse<MemberSegmentResponse> ptCrmMileageResponse = memberCacheService.queryMemberSegment(request, memberDetailReq.getFfpId(), ip, channelCode);
                return ptCrmMileageResponse;
            };
            MyBriefInfo detail = new MyBriefInfo();
            if ("N".equals(handConfig.getClosed())) {
                String userNo = this.getChannelInfo(channelCode, "10");
                Callable unpayCall = () -> {
                    //未支付订单数
                    Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
                    Map param = new HashMap<>();
                    param.put("Version", HandlerConstants.VERSION);
                    param.put("ChannelCode", channelCode);
                    param.put("UserNo", userNo);
                    param.put("CustomerNo", memberDetailReq.getFfpId());
                    param.put("RandCode", "");
                    String url = HandlerConstants.URL_FARE_API + HandlerConstants.COUNT_UNPAY_ORDER;
                    HttpResult httpResult = doPostClient(param, url, headMap, 5000, 2000);
                    UnpayOrderResponse unpayOrderResponse = null;
                    if (httpResult.isResult() && !StringUtil.isNullOrEmpty(httpResult.getResponse())) {
                        unpayOrderResponse = JsonUtil.fromJson(httpResult.getResponse(), UnpayOrderResponse.class);
                        if (UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(unpayOrderResponse.getResultCode())) {
                            return unpayOrderResponse;
                        }
                    }
                    return unpayOrderResponse;
                };
                Future<UnpayOrderResponse> unpayFuture = taskExecutor.submit(unpayCall);
                UnpayOrderResponse unpayCount = unpayFuture.get();
                if (unpayCount != null) {
                    String headChannelCode = request.getHeader(HEAD_CHANNEL_CODE);
                    // APP 5.9.3版本增加未支付权益券查询
                    if (ChannelCodeEnum.MOBILE.getChannelCode().equals(headChannelCode) && NumberUtils.toInt(req.getVersionCode()) < 59300
                            || ChannelCodeEnum.WXAPP.getChannelCode().equals(headChannelCode)) {
                        detail.setUnPayOrderCount(unpayCount.getUnpayCount());
                    } else {
                        int unPayCouponAmount = (int) unpayCount.getOrderCouponOrderList().stream().filter(order -> {
                            Date orderCreateTime = DateUtils.toDate(order.getOrderCreateDatetime(), DateUtils.YYYY_MM_DD_HH_MM_SS_PATTERN);
                            return null != orderCreateTime && orderCreateTime.getTime() + handConfig.getTimeLimit() * 60 * 1000 > new Date().getTime();
                        }).count();
                        detail.setUnPayOrderCount(unpayCount.getUnpayCount() + unPayCouponAmount);
                        Set<String> subOrderTypes = new HashSet<>();
                        if (CollectionUtils.isNotEmpty(unpayCount.getOrderList())) {
                            subOrderTypes.addAll(unpayCount.getOrderList().stream().map(UnpayOrderInfo::getSubOrderType).collect(Collectors.toSet()));
                        }
                        if (CollectionUtils.isNotEmpty(unpayCount.getOrderCouponOrderList())) {
                            subOrderTypes.addAll(unpayCount.getOrderCouponOrderList().stream().map(UnpayOrderInfo::getSubOrderType).collect(Collectors.toSet()));
                        }
                        detail.setUnPaySubOrderTypes(Lists.newArrayList(subOrderTypes));
                    }
                }
            }
            Future<PtCRMResponse<PtMemberDetail>> memberFuture = taskExecutor.submit(memberCall);
            Future<MemberRemainScoreResp> remainScoreFuture = taskExecutor.submit(totalScore);
            Future<PtCrmMileageResponse<MemberSegmentResponse>> currentSegmentsFuture = taskExecutor.submit(currentSegmentCall);
            PtCRMResponse<PtMemberDetail> ptCRMResponse = memberFuture.get();
            MemberRemainScoreResp memberRemainScoreResp = remainScoreFuture.get();
            PtCrmMileageResponse<MemberSegmentResponse> mileageResponse = currentSegmentsFuture.get();//查询会员航段
            //处理航段信息
            processMemberSegmentInfo(detail, mileageResponse);
            if (ptCRMResponse.getCode() == 0) {
                //2021-02-01 查询会员信息，更新首页会员中心的会员信息
                long secondDayDifference = DateUtils.getSecondDayDifference(new Date());
                apiRedisService.putData(RedisKeyConfig.MEMBER_CENTER_MEMBER_INFO + memberDetailReq.getFfpId(), JsonUtil.objectToJson(ptCRMResponse), secondDayDifference);//暂时存放一天
                PtMemberDetail memberDetail = ptCRMResponse.getData();
                //会员姓名  没有姓名的情况下，使用手机号作为姓名
                String name = CRMReqUtil.formatMemName(memberDetail);
                detail.setName(name);
                detail.setCFirstName(memberDetail.getBasicInfo().getCFirstName());
                detail.setCLastName(memberDetail.getBasicInfo().getCLastName());
                detail.setEFirstName(memberDetail.getBasicInfo().getEFirstName());
                detail.setELastName(memberDetail.getBasicInfo().getELastName());
                SexEnum sex = SexEnum.formatSexCode(memberDetail.getBasicInfo().getSex());
                detail.setSex(sex == null ? SexEnum.UNKNOWN.showCode : sex.showCode);
                detail.setHeadImageUrl(memberDetail.getBasicInfo().getHeadImageUrl());
                //会员级别处理
                MemberStateInfoSoaModel stateInfo = ptCRMResponse.getData().getStateInfo();
                List<MemberCertificateSoaModelV2> certificateInfoList = ptCRMResponse.getData().getCertificateInfo();
                detail.setLevel(stateInfo.getMemberLevelCode());
                detail.setLevelName(MemberLevelEnum.findLevelNameByLevelCode(stateInfo.getMemberLevelCode()));
                //会员星级 旧
                detail.setMemberStar(stateInfo.getMemberStar());
                if ("Y".equals(handConfig.getMemberStarQueryOpen())) {
                    PtApiCRMRequest<MemberStarQueryRequest> ptApiCRMRequestMemberStar = CRMReqUtil.buildCommReqNoToken(request, channelCode);
                    MemberStarQueryRequest memberStarQueryRequest = new MemberStarQueryRequest(Integer.valueOf(memberDetailReq.getFfpId()));
                    ptApiCRMRequestMemberStar.setData(memberStarQueryRequest);
                    PtCRMResponse<MemberStarQueryResp> ptStarResp = memberService.queryMemberStar(ptApiCRMRequestMemberStar);
                    if (ptStarResp.getCode() != null && ptStarResp.getCode() == 0) {
                        detail.setMemberStar(ptStarResp.getData().getMemberStarCode());
                    }
                }
                if (CollectionUtils.isEmpty(certificateInfoList)) {
                    detail.setImproveFlag(true);
                }
                //手机号记录ID
                MemberContactSoaModel memberContactSoaModel = toMemberContactSoaModel(memberDetail.getContactInfo(), ContactTypeEnum.MOBILE.getCode());
                if (null != memberContactSoaModel) {
                    detail.setRecordId(AESTool.encrypt(memberContactSoaModel.getRecordId() + "",
                            HandlerConstants.DEFAULT_TOKEN.substring(0, 16), HandlerConstants.DEFAULT_TOKEN.substring(0, 16)));
                    detail.setPhoneNumber(memberContactSoaModel.getContactNumber());
                    if (memberContactSoaModel.getContactNumber().matches(PatternCommon.MOBILE_GLOBAL_TYPE)) {
                        String countryTelCode = StringUtils.substringBefore(memberContactSoaModel.getContactNumber(), "-");
                        String handPhoneNo = StringUtils.substringAfter(memberContactSoaModel.getContactNumber(), "-");
                        if (StringUtils.isNotBlank(countryTelCode)) {
                            detail.setCountryTelCode(countryTelCode);
                            detail.setPhoneNumber(handPhoneNo);
                        } else {
                            detail.setCountryTelCode("86");
                        }
                    } else {
                        detail.setCountryTelCode("86");
                    }
                }
                if (CrmResultEnum.SUC000.getResultCode().equals(memberRemainScoreResp.getStatusCode())) {
                    //2021-02-01 查询会员总积分数，更新首页会员中心的会员总积分数
                    apiRedisService.putData(RedisKeyConfig.MEMBER_CENTER_MEMBER_SCORE + memberDetailReq.getFfpId(), JsonUtil.objectToJson(memberRemainScoreResp), secondDayDifference);//暂时存放一天
                    detail.setMyScore(Long.valueOf(memberRemainScoreResp.getPoint()));
                }
                // 是否实名认证
                boolean realName = CrmUtil.judgeRealNameStatus(ptCRMResponse.getData().getRealVerifyInfos());
                detail.setRealName(realName);
                resp.setObjData(detail);
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo(ptCRMResponse.getMsg());
            }
        } catch (Exception e) {
            log.error("membermy查询异常", e);
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR.getResultInfo());
        }
        return resp;
    }

    //处理会员航段数信息
    private void processMemberSegmentInfo(MyBriefInfo detail, PtCrmMileageResponse<MemberSegmentResponse> ptCrmMileageResponse) {
        if (null != ptCrmMileageResponse && ptCrmMileageResponse.getCode() == 0) {
            int totalNumbers = 0;
            if (null != ptCrmMileageResponse.getData()) {
                if (CollectionUtils.isNotEmpty(ptCrmMileageResponse.getData().getSegments())) {
                    for (MemberSegmentResponse.Segments segments : ptCrmMileageResponse.getData().getSegments()) {
                        totalNumbers += segments.getNumber();
                    }
                }
            }
            detail.setMySegment(totalNumbers);
        } else {
            throw new ServiceException("【会员我的页面】会员航段数信息处理异常");
        }
    }

    @InterfaceLog
    @ApiOperation(value = "查询我的实名认证状态", notes = "查询我的实名认证状态")
    @RequestMapping(value = "/realName", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp<MemberRealInfo> memberRealName(@RequestBody @Validated BaseReq<MemberDetailReq> req, BindingResult bindingResult, HttpServletRequest request) {
        BaseResp resp = new BaseResp();
        String ip = this.getClientIP(request);
        try {
            if (bindingResult.hasErrors()) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
                return resp;
            }
            MemberDetailReq memberDetailReq = req.getRequest();
            String channelCode = req.getChannelCode();
            boolean flag = this.checkKeyInfo(memberDetailReq.getFfpId(), memberDetailReq.getLoginKeyInfo(), channelCode);
            if (!flag) {
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR_925.getResultCode());
                resp.setResultInfo(WSEnum.ERROR_CHK_ERROR_925.getResultInfo());
                return resp;
            }
            PtApiCRMRequest<PtMemberDetailRequest> detaiReq = buildCommReq(request, req.getChannelCode(), memberDetailReq.getFfpId(), "");
            PtMemberDetailRequest ptMemberDetailRequest = new PtMemberDetailRequest();
            ptMemberDetailRequest.setCardNO(memberDetailReq.getFfpCardNo());
            String[] items = {MemberDetailRequestItemsEnum.BASICINFO.eName, MemberDetailRequestItemsEnum.CONTACTINFO.eName, MemberDetailRequestItemsEnum.CERTIFICATEINFO.eName, MemberDetailRequestItemsEnum.REALVERIFYINFOS.eName};
            ptMemberDetailRequest.setRequestItems(items);
            detaiReq.setData(ptMemberDetailRequest);
            PtCRMResponse<PtMemberDetail> detailPtCRMResponse = memberService.memberDetail(detaiReq);
            if (detailPtCRMResponse.getCode() != 0) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo(detailPtCRMResponse.getMsg());
                return resp;
            }
            List<MemberRealNameSummarySoaModel> verifyInfoList = detailPtCRMResponse.getData().getRealVerifyInfos();
            //用户不存在认证记录 认为没进行
            MemberRealInfo memberRealInfo;
            if (CollectionUtils.isEmpty(verifyInfoList)) {
                //渲染无实名数据结果
                memberRealInfo = initMemberRealInfo("", VerifyStatusEnum.UNKNOW.code, "", "");
            } else {
                //获取最新的认证记录
                MemberRealNameSummarySoaModel memberRealNameSummarySoaModel = verifyInfoList.stream().max(Comparator.comparing(MemberRealNameSummarySoaModel::getVerifyDate)).orElse(null);
                if (memberRealNameSummarySoaModel != null) {
                    VerifyStatusEnum verifyStatusEnum = VerifyStatusEnum.formatVerifyStatus(memberRealNameSummarySoaModel.getStatus());
                    VerifyChannelEnum verifyChannelEnum = VerifyChannelEnum.checkEnum(memberRealNameSummarySoaModel.getVerifyChannel());
                    if (verifyChannelEnum == null) {
                        memberRealInfo = initMemberRealInfo(verifyStatusEnum.desc, verifyStatusEnum.code, memberRealNameSummarySoaModel.getVerifyChannel(), "外部认证");
                    } else {
                        memberRealInfo = initMemberRealInfo(verifyStatusEnum.desc, verifyStatusEnum.code, memberRealNameSummarySoaModel.getVerifyChannel(), verifyChannelEnum.desc);
                        //认证通过异步存入缓存
                        if (verifyStatusEnum != null && VerifyStatusEnum.PASS.code.equals(verifyStatusEnum.code)) {
                            taskExecutor.execute(new RealInfoThread(memberDetailReq.getFfpCardNo(), verifyStatusEnum.desc, verifyStatusEnum.code, memberRealNameSummarySoaModel.getVerifyChannel(), verifyChannelEnum.desc));
                        }
                    }
                    //实名认证审核拒绝加上拒绝原因
                    if (verifyStatusEnum != null && VerifyStatusEnum.REJECT.code.equals(verifyStatusEnum.code)) {
                        memberRealInfo.setRejectReason(memberRealNameSummarySoaModel.getComments());
                    }
                    memberRealInfo.setVerifyDesc(verifyStatusEnum == null ? "" : verifyStatusEnum.desc);
                    memberRealInfo.setVerifyStatus(verifyStatusEnum == null ? "-2" : verifyStatusEnum.code);
                    //实名认证审核拒绝加上拒绝原因
                    if (verifyStatusEnum != null && verifyStatusEnum.code.equals(VerifyStatusEnum.REJECT.code)) {
                        memberRealInfo.setRejectReason(memberRealNameSummarySoaModel.getComments());
                    }

                    //是否为OTA渠道认证
                    memberRealInfo.setMemberIsOta(true);
                    String ownRealChannel = handConfig.getOwnRealChannel();
                    if (StringUtils.isNotBlank(ownRealChannel)) {
                        if (ownRealChannel.contains(memberRealNameSummarySoaModel.getVerifyChannel())) {
                            memberRealInfo.setMemberIsOta(false);
                        }
                    }

                } else {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo("未查询到相关实名记录！");
                    return resp;
                }
            }
            PtMemberDetail detail = detailPtCRMResponse.getData();
            memberRealInfo.setLastName(detail.getBasicInfo().getCLastName());
            memberRealInfo.setFirstName(detail.getBasicInfo().getCFirstName());
            memberRealInfo.setPhone(filterContactInfo(detail.getContactInfo(), ContactTypeEnum.MOBILE.getCode()));
            MemberContactSoaModel memberContactSoaModel = toMemberContactSoaModel(detail.getContactInfo(), ContactTypeEnum.MOBILE.getCode());
            if (memberContactSoaModel != null) {
                memberRealInfo.setPhone(memberContactSoaModel.getContactNumber());
                memberRealInfo.setPhoneId(AESTool.encrypt(memberContactSoaModel.getRecordId() + "", HandlerConstants.DEFAULT_TOKEN.substring(0, 16), HandlerConstants.DEFAULT_TOKEN.substring(0, 16)));
            }
            //获取身份证信息
            MemberCertificateSoaModelV2 memberCertificateSoaModelV2 = filterCert(detail.getCertificateInfo(), CertificateTypeEnum.ID_CARD.getCode());
            //护照
            MemberCertificateSoaModelV2 passport = filterCert(detail.getCertificateInfo(), CertificateTypeEnum.PASSPORT.getCode());
            if (memberCertificateSoaModelV2 != null) {
                CertificateModel customerCertificateInfo = new CertificateModel();
                BeanUtils.copyProperties(memberCertificateSoaModelV2, customerCertificateInfo);
                customerCertificateInfo.setCertificateTypeEName(CertificateTypeEnum.ID_CARD.geteName());
                customerCertificateInfo.setCertificateTypeCName(CertificateTypeEnum.ID_CARD.getDesc());
                memberRealInfo.setCertificateInfo(customerCertificateInfo);
            }
            //没有证件信息需要先补全证件
            if (StringUtil.isNullOrEmpty(memberDetailReq.getVerifyType())) {
                if (StringUtil.isNullOrEmpty(detail.getCertificateInfo())) {
                    memberRealInfo.setImproveFlag(true);
                }
            } else {
                if (VerifyChannelEnum.ZhiFuBao.code.equals(memberDetailReq.getVerifyType())) {
                    //支付宝的目前只要有身份证
                    if (memberCertificateSoaModelV2 == null) {
                        memberRealInfo.setImproveFlag(true);
                    }
                } else if (VerifyChannelEnum.Face.code.equals(memberDetailReq.getVerifyType())) {
                    //人脸识别的目前只要有身份证
                    if (memberCertificateSoaModelV2 == null) {
                        memberRealInfo.setImproveFlag(true);
                    }
                } else if (VerifyChannelEnum.Photo.code.equals(memberDetailReq.getVerifyType())) {
                    //证件照目前支持的证件类型是身份证和护照
                    if (memberCertificateSoaModelV2 == null && passport == null) {
                        memberRealInfo.setImproveFlag(true);
                    }
                } else {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo("暂不支持的认证方式！");
                    return resp;
                }
            }
            memberRealInfo.setSalePwdSetYesOrNo(detail.getBasicInfo().getIsSetConsumePwd() == null ? "0" : (detail.getBasicInfo().getIsSetConsumePwd() ? "1" : "0"));
            if ("N".equals(handConfig.getClosed())) {
                //设置学生认证状态
                setStudentVerifyStatus(memberRealInfo, req, request);
                //设置企业认证状态
                setCompanyVerifyStatus(memberRealInfo, req, request);
            }

            MemberTagResponse memberTagResponse = iMemberTagService.queryMemberTag(request, channelCode, req.getRequest().getFfpId(), "YDZ", false);
            if (memberTagResponse.getIsEffective()) {
                memberRealInfo.setIsYjYsMember("1");
            } else if (StringUtils.isBlank(memberTagResponse.getExpireDate())) {
                memberRealInfo.setIsYjYsMember("0");
            } else {
                memberRealInfo.setIsYjYsMember("2");
            }
            memberRealInfo.setEffectiveDate(memberTagResponse.getExpireDate());
            resp.setObjData(memberRealInfo);
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
        } catch (Exception e) {
            this.logError(resp, ip, req, e, "小吉正在努力处理中，请稍候再试");
        }
        return resp;
    }

    @InterfaceLog
    @ApiOperation(value = "查询最近实名渠道信息", notes = "查询最近实名认证通过渠道信息")
    @RequestMapping(value = "/realNameStatus", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp<MemberRealChannel> realNameStatus(@RequestBody @Validated BaseReq<UserInfoMust> req, BindingResult bindingResult, HttpServletRequest request) {
        BaseResp resp = new BaseResp();
        if (bindingResult.hasErrors()) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
            return resp;
        }
        UserInfoMust userInfoMust = req.getRequest();
        String channelCode = req.getChannelCode();
        boolean flag = this.checkKeyInfo(userInfoMust.getFfpId(), userInfoMust.getLoginKeyInfo(), channelCode);
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR_925.getResultCode());
            resp.setResultInfo(WSEnum.ERROR_CHK_ERROR_925.getResultInfo());
            return resp;
        }
        PtApiCRMRequest<PtMemberDetailRequest> detaiReq = buildCommReq(request, req.getChannelCode(), userInfoMust.getFfpId(), "");
        PtMemberDetailRequest ptMemberDetailRequest = new PtMemberDetailRequest();
        ptMemberDetailRequest.setCardNO(userInfoMust.getFfpCardNo());
        String[] items = {MemberDetailRequestItemsEnum.REALVERIFYINFOS.eName};
        ptMemberDetailRequest.setRequestItems(items);
        detaiReq.setData(ptMemberDetailRequest);
        PtCRMResponse<PtMemberDetail> detailPtCRMResponse = memberService.memberDetail(detaiReq);
        if (detailPtCRMResponse.getCode() != 0) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(detailPtCRMResponse.getMsg());
            return resp;
        }
        List<MemberRealNameSummarySoaModel> verifyInfoList = detailPtCRMResponse.getData().getRealVerifyInfos();
        if (CollectionUtils.isEmpty(verifyInfoList)) {
            throw new CommonException(WSEnum.NO_REAL_NAME.getResultCode(), WSEnum.NO_REAL_NAME.getResultInfo());
        }
        MemberRealNameSummarySoaModel memberRealNameSummarySoaModel = CrmUtil.getNewRealNamePassSummary(verifyInfoList);
        if (memberRealNameSummarySoaModel == null) {
            throw new CommonException(WSEnum.NO_REAL_NAME.getResultCode(), WSEnum.NO_REAL_NAME.getResultInfo());
        }
        MemberRealChannel memberRealChannel = new MemberRealChannel();
        memberRealChannel.setVerifyChannel(memberRealNameSummarySoaModel.getVerifyChannel());
        memberRealChannel.setVerifyStatus(memberRealNameSummarySoaModel.getStatus());
        boolean verifyCheck = false;
        if (VerifyChannelEnum.Photo.code.equals(memberRealNameSummarySoaModel.getVerifyChannel())) {
            //实名认证时间
            long useTime = DateUtils.millisecondDiff(memberRealNameSummarySoaModel.getVerifyDate());
            if (useTime <= handConfig.getPhotoUseTime()) {
                verifyCheck = true;
            }
        } else {
            //可信渠道判断
            if (memberAggrService.checkOwnRealChannel(memberRealNameSummarySoaModel)) {
                verifyCheck = true;
            }
        }
        memberRealChannel.setVerifyCheck(verifyCheck);
        resp.setObjData(memberRealChannel);
        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
        resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
        return resp;
    }

    @InterfaceLog
    @ApiOperation(value = "获取会员简要信息", notes = "获取会员简要信息")
    @RequestMapping(value = "queryBriefMember", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp<MemberContactInfo> queryBriefMember(@RequestBody @Validated BaseReq<UserInfoMust> req, BindingResult bindingResult, HttpServletRequest request) {
        BaseResp resp = new BaseResp();
        String channelCode = req.getChannelCode();
        String ip = this.getClientIP(request);
        String reqId = MdcUtils.getRequestId();
        if (bindingResult.hasErrors()) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
            return resp;
        }
        boolean flag = this.checkKeyInfo(req.getRequest().getFfpId(), req.getRequest().getLoginKeyInfo(), channelCode);
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR_925.getResultCode());
            resp.setResultInfo(WSEnum.ERROR_CHK_ERROR_925.getResultInfo());
            return resp;
        }
        try {
            PtApiCRMRequest<PtMemberDetailRequest> detaiReq = buildCommReq(request, req.getChannelCode(), req.getRequest().getFfpId(), "");
            PtMemberDetailRequest ptMemberDetailRequest = new PtMemberDetailRequest();
            ptMemberDetailRequest.setCardNO(req.getRequest().getFfpCardNo());
            String[] items = {MemberDetailRequestItemsEnum.BASICINFO.eName, MemberDetailRequestItemsEnum.CONTACTINFO.eName};
            ptMemberDetailRequest.setRequestItems(items);
            detaiReq.setData(ptMemberDetailRequest);
            PtCRMResponse<PtMemberDetail> detailPtCRMResponse = memberService.memberDetail(detaiReq);
            if (detailPtCRMResponse.getCode() != 0) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo(detailPtCRMResponse.getMsg());
                return resp;
            }
            MemberContactInfo memberContactInfo = new MemberContactInfo();
            PtMemberDetail detail = detailPtCRMResponse.getData();
            memberContactInfo.setLastName(detail.getBasicInfo().getCLastName());
            memberContactInfo.setFirstName(detail.getBasicInfo().getCFirstName());
            memberContactInfo.setEFirstName(detail.getBasicInfo().getEFirstName());
            memberContactInfo.setELastName(detail.getBasicInfo().getELastName());
            memberContactInfo.setFfpCardNo(detail.getBasicInfo().getCardNO());
            memberContactInfo.setFfpId(detail.getBasicInfo().getMemberId());
            MemberContactSoaModel memberContactSoaModel = toMemberContactSoaModel(detail.getContactInfo(), ContactTypeEnum.MOBILE.getCode());
            if (memberContactSoaModel != null) {
                if (StringUtils.isNotBlank(memberContactSoaModel.getContactNumber())) {
                    if (memberContactSoaModel.getContactNumber().contains("-")) {
                        String countryTelCode = StringUtils.substringBefore(memberContactSoaModel.getContactNumber(), "-");
                        String handPhoneNo = StringUtils.substringAfter(memberContactSoaModel.getContactNumber(), "-");
                        memberContactInfo.setCountryTelCode(countryTelCode);
                        memberContactInfo.setHandPhoneNo(handPhoneNo);
                    } else {
                        memberContactInfo.setCountryTelCode("86");
                        memberContactInfo.setHandPhoneNo(memberContactSoaModel.getContactNumber());
                    }
                }

            }
            MemberContactSoaModel memberContactEmail = toMemberContactSoaModel(detail.getContactInfo(), ContactTypeEnum.EMAIL.getCode());
            if (memberContactEmail != null) {
                memberContactInfo.setEmail(memberContactEmail.getContactNumber());
            }
            resp.setObjData(memberContactInfo);
            resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
        } catch (Exception e) {
            this.logError(resp, reqId, ip, req, e, "获取会员联系方式失败");
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("获取会员联系方式失败");
            return resp;
        }
        return resp;
    }


    /**
     * 渲染认证信息
     *
     * @param desc
     * @param verifyStatus
     * @param verifyChannel
     * @param verifyChannelName
     * @return
     */
    private MemberRealInfo initMemberRealInfo(String desc, String verifyStatus, String verifyChannel, String verifyChannelName) {
        MemberRealInfo memberRealInfo = new MemberRealInfo();
        memberRealInfo.setVerifyDesc(desc);
        memberRealInfo.setVerifyStatus(verifyStatus);
        memberRealInfo.setVerifyChannel(verifyChannel);
        memberRealInfo.setVerifyChannelName(verifyChannelName);
        return memberRealInfo;
    }

    private List<VerifyChannel> addSupportVerChannel(String verType) {
        List<VerifyChannel> verifyChannelList = new ArrayList<>();
        if (!StringUtil.isNullOrEmpty(handConfig.getSupportVerifyChannel())) {
            List<VerifyChannel> allVerifyChannelList = handConfig.getSupportVerifyChannel();
            for (VerifyChannel verifyChannel : allVerifyChannelList) {
                //如果BusinessList为空代表适用所有业务
                if (StringUtil.isNullOrEmpty(verifyChannel.getBusinessList())) {
                    verifyChannelList.add(verifyChannel);
                } else {
                    if (verifyChannel.getBusinessList().contains(verType)) {
                        verifyChannelList.add(verifyChannel);
                    }
                }
            }
        }
        return verifyChannelList;
    }


    private void setStudentVerifyStatus(MemberRealInfo memberRealInfo, BaseReq<MemberDetailReq> req, HttpServletRequest request) {
        try {
            MemberTagResponse memberTagResponse = iMemberTagService.queryMemberTag(request, req.getChannelCode(), req.getRequest().getFfpId(), "XS", true);
            memberRealInfo.setStudentVerifyStatus(memberTagResponse.getIsEffective());
        } catch (Exception e) {
            log.info("查询学生认证出错", e);
        }
    }


    /**
     * @param memberRealInfo
     * @param req
     * @param request
     * @return void
     * <AUTHOR>
     * @Description 企业认证
     * @Date 10:12 2024/3/27
     **/
    private void setCompanyVerifyStatus(MemberRealInfo memberRealInfo, BaseReq<MemberDetailReq> req, HttpServletRequest request) {
        try {
            CompanyVerifyApplyRequest companyVerifyApplyRequest = new CompanyVerifyApplyRequest();
            companyVerifyApplyRequest.setFfpId(req.getRequest().getFfpId());
            companyVerifyApplyRequest.setFfpCardNo(req.getRequest().getFfpCardNo());
            CompanyVerifyApplyResponse applyResult = companyMemberVerifyService.toCatchApplyResult(companyVerifyApplyRequest, request, this.getClientIP(request), request.getHeader(HEAD_CHANNEL_CODE), false, false);
            memberRealInfo.setCompanyVerifyStatus(null != applyResult && StringUtils.isNotEmpty(applyResult.getStatus()) && CompanyVerifiedStatusEnum.PASS.getCode().equals(applyResult.getStatus()));
        } catch (CommonException commonException) {
            log.error("会员ID【{}】企业认证结果查询出错，状态码：【{}】，错误信息：【{}】", req.getRequest().getFfpId(), commonException.getResultCode(), commonException.getErrorMsg());
        } catch (Exception exception) {
            log.error("会员ID【{}】企业认证结果查询出错，错误信息：", req.getRequest().getFfpId(), exception);
        }
    }

    @InterfaceLog
    @ApiOperation(value = "设置消费密码", notes = "设置消费密码")
    @RequestMapping(value = "/resetSalePwd", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp resetSalePwd(@RequestBody @Validated BaseReq<ResetSalePassword> req, BindingResult bindingResult, HttpServletRequest request) {
        BaseResp resp = new BaseResp();
        if (bindingResult.hasErrors()) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
            return resp;
        }
        ResetSalePassword resetSalePassword = req.getRequest();
        String channelCode = req.getChannelCode();
        boolean flag = this.checkKeyInfo(resetSalePassword.getFfpId(), resetSalePassword.getLoginKeyInfo(), channelCode);
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        if (!resetSalePassword.getConsumePwd().equals(resetSalePassword.getConfirmePwd())) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("两次消费密码不一致，请重新输入");
            return resp;
        }
        //检查认证状态
        PtApiCRMRequest<PtMemberDetailRequest> detaiReq = buildCommReq(request, req.getChannelCode(), resetSalePassword.getFfpId(), "");
        PtMemberDetailRequest ptMemberDetailRequest = new PtMemberDetailRequest();
        ptMemberDetailRequest.setCardNO(resetSalePassword.getFfpCardNo());
        String[] items = {MemberDetailRequestItemsEnum.REALVERIFYINFOS.eName};
        ptMemberDetailRequest.setRequestItems(items);
        detaiReq.setData(ptMemberDetailRequest);
        PtCRMResponse<PtMemberDetail> ptCRMResponse = memberService.memberDetail(detaiReq);
        if (ptCRMResponse.getCode() != 0) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("未查询到用户信息");
            return resp;
        }
        MemberRealNameSummarySoaModel memberRealNameSummarySoaModel = CrmUtil.getNewRealNamePassSummary(ptCRMResponse.getData().getRealVerifyInfos());
        if (memberRealNameSummarySoaModel == null) {
            throw new CommonException(WSEnum.NO_REAL_NAME.getResultCode(), WSEnum.NO_REAL_NAME.getResultInfo());
        }
        //实名认证时间
        long useTime = DateUtils.millisecondDiff(memberRealNameSummarySoaModel.getVerifyDate());
        boolean ownFlag = false;
        String ownRealChannel = handConfig.getOwnRealChannel();
        if (StringUtils.isNotBlank(ownRealChannel)) {
            if (ownRealChannel.contains(memberRealNameSummarySoaModel.getVerifyChannel())) {
                ownFlag = true;
            }
        } else {
            ownFlag = true;
        }
        if (!ownFlag) {
            throw new CommonException(WSEnum.DO_REAL_NAME.getResultCode(), WSEnum.DO_REAL_NAME.getResultInfo());
        }
        if (VerifyChannelEnum.Photo.code.equals(memberRealNameSummarySoaModel.getVerifyChannel())) {
            if (useTime > handConfig.getPhotoUseTime()) {
                throw new CommonException(WSEnum.DO_REAL_NAME.getResultCode(), WSEnum.DO_REAL_NAME.getResultInfo());
            }
        } else {
            //认证时长超过两分钟或者非自营渠道认证，需要重新进行认证
            if (useTime > handConfig.getUseTime()) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("存在风险，请退出重新认证");
                return resp;
            }
        }
        String redisKey = RedisKeyConfig.createPhotoConsumerPasswd(resetSalePassword.getFfpCardNo(), memberRealNameSummarySoaModel.getVerifyChannel());
        String result = apiRedisService.getData(redisKey);
        //如果是Photo且当前实名信息重新设置过消费密码则不允许再次设置
        if (VerifyChannelEnum.Photo.code.equals(memberRealNameSummarySoaModel.getVerifyChannel()) && StringUtils.isNotBlank(result)) {
            if (String.valueOf(memberRealNameSummarySoaModel.getVerifyDate()).equals(result)) {
                throw new CommonException(WSEnum.ERROR.getResultCode(), "请勿重复设置消费密码");
            }
        }
        //设置消费密码
        PtApiCRMRequest<PtSetConsumePasswordRequest> ptApiRequest = buildCommReq(request, channelCode, resetSalePassword.getFfpId(), resetSalePassword.getToken());
        PtSetConsumePasswordRequest ptSetConsumePasswordRequest = new PtSetConsumePasswordRequest();
        ptSetConsumePasswordRequest.setPassword(resetSalePassword.getConfirmePwd());
        ptApiRequest.setData(ptSetConsumePasswordRequest);
        PtCRMResponse ptCRMSetResponse = memberService.setConsumePassword(ptApiRequest);
        if (ptCRMSetResponse.getCode() == 0) {
            //如果是验证的Photo，进行缓存标记7天内不可再次进行修改
            if (VerifyChannelEnum.Photo.code.equals(memberRealNameSummarySoaModel.getVerifyChannel())) {
                apiRedisService.putData(redisKey, String.valueOf(memberRealNameSummarySoaModel.getVerifyDate()), handConfig.getPhotoUseTime());
            }
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            resp.setResultInfo("消费密码设置成功！");
        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(ptCRMSetResponse.getMsg());
        }
        return resp;
    }

    @ApiOperation(value = "检验手机号是否注册", notes = "检验手机号是否注册")
    @RequestMapping(value = "/checkMobile", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp checkMobile(@RequestBody BaseReq<MemberCheck> req, HttpServletRequest request) {
        BaseResp resp = new BaseResp();
        try {
            ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
            Validator validator = factory.getValidator();
            Set<ConstraintViolation<BaseReq>> violations = validator.validate(req);
            if (CollectionUtils.isNotEmpty(violations)) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo(violations.iterator().next().getMessage());
                return resp;
            }
            MemberCheck memberCheck = req.getRequest();
            String channelCode = req.getChannelCode();
            PtApiCRMRequest<PtMemberDetailRequest> ptApiRequest = buildCommReqNoToken(request, channelCode);
            PtMemberDetailRequest ptMemberDetailRequest = new PtMemberDetailRequest();
            ptMemberDetailRequest.setMobile(PhoneUtil.formatMobile(memberCheck.getCountryCode(), memberCheck.getMobilePhone()));
            String[] items = {MemberDetailRequestItemsEnum.BASICINFO.eName};
            ptMemberDetailRequest.setRequestItems(items);
            ptApiRequest.setData(ptMemberDetailRequest);
            PtCRMResponse<PtMemberDetail> ptCRMResponse = memberService.memberDetail(ptApiRequest);
            if (ptCRMResponse.getCode() == 0) {
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setResultInfo(WSEnum.SUCCESS.getResultCode());
            } else if (ptCRMResponse.getCode() == 100002) {
                resp.setResultCode(WSEnum.NO_DATA.getResultCode());
                resp.setResultInfo(ptCRMResponse.getMsg());
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo(ptCRMResponse.getMsg());
            }
        } catch (Exception e) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR.getResultInfo());
        }
        return resp;
    }

    @InterfaceLog
    @ApiOperation(value = "重置登录密码", notes = "重置登录密码")
    @RequestMapping(value = "/resetLoginPwd", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp resetLoginPwd(@RequestBody BaseReq<ResetLoginPwd> req, HttpServletRequest request) {
        BaseResp resp = new BaseResp();
        try {
            ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
            Validator validator = factory.getValidator();
            Set<ConstraintViolation<BaseReq>> violations = validator.validate(req);
            if (CollectionUtils.isNotEmpty(violations)) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo(violations.iterator().next().getMessage());
                return resp;
            }
            ResetLoginPwd resetInfo = req.getRequest();
            if (ChannelCodeEnum.MP_ALIPAY.getChannelCode().equals(req.getChannelCode())) {
                //支付宝目前统一走微信接口
                req.setChannelCode(ChannelCodeEnum.WEIXIN.getChannelCode());
            }
            if (StringUtil.isNullOrEmpty(resetInfo.getCountryCode())) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo("手机国际代码不能为空");
                return resp;
            }
            if (!resetInfo.getCountryCode().matches(PatternCommon.MOBILE_GLOBAL_ROAMING)) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo(INTERNATIONAL_AREA_CODE_MUST_BE_DIGITAL);
                return resp;
            }
            String channelCode = req.getChannelCode();
            PtApiCRMRequest<PtResetLoginPasswordRequest> ptApiRequest = buildCommReqNoToken(request, channelCode);
            PtResetLoginPasswordRequest ptResetLoginPasswordRequest = new PtResetLoginPasswordRequest();
            ptResetLoginPasswordRequest.setResetType(1);
            ptResetLoginPasswordRequest.setAccount(PhoneUtil.formatMobile(resetInfo.getCountryCode(), resetInfo.getMobileNum()));
            ptResetLoginPasswordRequest.setCaptcha(resetInfo.getVeriCode());
            ptResetLoginPasswordRequest.setNewPassword(resetInfo.getNewPwd());
            ptResetLoginPasswordRequest.setIsAutoGenerate(false);
            ptApiRequest.setData(ptResetLoginPasswordRequest);
            PtCRMResponse ptCRMResponse = memberService.resetLoginPassword(ptApiRequest);
            if (ptCRMResponse.getCode() == 0) {
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo(ptCRMResponse.getMsg());
            }
        } catch (Exception e) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR.getResultInfo());
        }
        return resp;
    }

    @ApiOperation(value = "登录状态检查", notes = "登录状态检查")
    @RequestMapping(value = "/checkLogin", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp checkLogin(@RequestBody BaseReq<UserInfoNoMust> req, HttpServletRequest request) {
        BaseResp resp = new BaseResp();
        UserInfoNoMust userInfo = req.getRequest();
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<BaseReq>> violations = validator.validate(req);
        if (CollectionUtils.isNotEmpty(violations)) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo(violations.iterator().next().getMessage());
            return resp;
        }
        //登录态检查
        checkLogin(request, req.getChannelCode(), resp, userInfo.getFfpId(), userInfo.getLoginKeyInfo());
        if (WSEnum.SUCCESS.getResultCode().equals(resp.getResultCode())) {
            //为true时表示需要重新登录
            if (resp.isLoginFlag()) {
                return resp;
            }
        } else {
            return resp;
        }
        return resp;
    }

    @ApiOperation(value = "用户头像修改", notes = "用户头像修改(待做)")
    @RequestMapping(value = "/uploadMemberPhoto", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp uploadMemberPhoto(@RequestParam("formData") String formData, @RequestParam("file") MultipartFile[] file, HttpServletRequest request) {
        BaseResp resp = new BaseResp();
        log.info("表单接收参数：formData:{}", formData);
        return resp;
    }

    @ApiOperation(value = "检查是否设置过消费密码", notes = "检查是否设置过消费密码")
    @RequestMapping(value = "/checkSetSalePwdYn", method = RequestMethod.POST)
    public BaseResponse checkSetPwdYn(@RequestBody BaseReq<IdNameCheckReq> baseReq) {
        BaseResponse baseResponse = new BaseResponse();
        IdNameCheckReq idNameCheckReq = baseReq.getRequest();
        if (StringUtils.isBlank(idNameCheckReq.getFfpId())) {
            baseResponse.setResultCode(WSEnum.ERROR.getResultCode());
            baseResponse.setErrorInfo("用户编号不能为空！");
            return baseResponse;
        }
        if (StringUtils.isBlank(idNameCheckReq.getFfpCardNo())) {
            baseResponse.setResultCode(WSEnum.ERROR.getResultCode());
            baseResponse.setErrorInfo("用户卡号不能为空！");
            return baseResponse;
        }
        if (StringUtils.isBlank(idNameCheckReq.getLoginKeyInfo())) {
            baseResponse.setResultCode(WSEnum.ERROR.getResultCode());
            baseResponse.setErrorInfo("用户验证信息不能为空！");
            return baseResponse;
        }
        Boolean flag = this.checkKeyInfo(idNameCheckReq.getFfpId(), idNameCheckReq.getLoginKeyInfo(), baseReq.getChannelCode());
        if (!flag) {
            baseResponse.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            baseResponse.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return baseResponse;
        }
        PtRealNameReq checkRequest = new PtRealNameReq();
        checkRequest.setID(idNameCheckReq.getFfpId());
        checkRequest.setClientCode(baseReq.getChannelCode());
        checkRequest.setSignature(EncoderHandler.encodeByMD5(baseReq.getChannelCode() +
                idNameCheckReq.getFfpId() + getClientPwd(baseReq.getChannelCode())).toUpperCase());
        try {
            PtRealNameResp realNameResp = memberService.realNameStateA(checkRequest);
            baseResponse.setResultCode(WSEnum.SUCCESS.getResultCode());
            baseResponse.setResult(realNameResp.getExist());
            return baseResponse;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            baseResponse.setResultCode(WSEnum.ERROR.getResultCode());
            baseResponse.setErrorInfo("网络错误！");
            return baseResponse;
        }
    }

    @RequestMapping(value = "/faceVerify", method = RequestMethod.POST)
    @ApiOperation(value = "人脸识别", notes = "人脸识别")
    public BaseResp<String> faceVerify(@RequestBody BaseReq<FaceVerifyRequest> req, HttpServletRequest request) {
        BaseResp<String> response = new BaseResp<>();
        String ip = this.getClientIP(request);
        String reqId = StringUtil.newGUID() + "_Member_FaceVerify";
        try {
            //校验参数
            ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
            Validator validator = factory.getValidator();
            Set<ConstraintViolation<BaseReq>> violations = validator.validate(req);
            if (CollectionUtils.isNotEmpty(violations)) {
                throw new RequestParamErrorException(violations.iterator().next().getMessage());
            }
            response.setResultCode(WSEnum.ERROR.getResultCode());
            response.setResultInfo("抱歉的通知您，因技术原因，人脸识别功能暂时无法提供服务，请您使用其他方式或联系吉祥航空官方客服电话95520");
            return response;
            //判断是否登录
            /*FaceVerifyRequest reqParam = req.getRequest();
            boolean flag = this.checkKeyInfo(reqParam.getFfpId(),
                    reqParam.getLoginKeyInfo(), req.getChannelCode());
            if (!flag) {
                throw new CheckErrorException("校验出错");
            }
            CheckFaceResult checkFaceResult = LiveBodyUtil.checkFace(reqParam.getPhoneModel(), reqParam.getImage());
            if (!CheckFaceResultEnum.REAL_FACE.getResultCode().equals(checkFaceResult.getRESULT())) {
                throw new OperationFailedException(checkFaceResult.getMESSAGE());
            }
            BaseReq<IdNameCheckReq> idNameCheckReqBaseReq = new BaseReq<>();
            BeanUtils.copyNotNullProperties(req, idNameCheckReqBaseReq);
            String channelCode = request.getHeader(CHANNEL_CODE);
            //M站无需校验设备信息
            if (ChannelCodeEnum.MWEB.getChannelCode().equals(channelCode)) {
                idNameCheckReqBaseReq.getRequest().setCheckDevice(false);
            }
            if (ChannelCodeEnum.WXAPP.getChannelCode().equals(channelCode)) {
                idNameCheckReqBaseReq.getRequest().setCheckDevice(false);
            }
            response = idNamePhotoCheck(idNameCheckReqBaseReq, request);*/
        } catch (Exception e) {
            this.logError(response, reqId, ip, req, e);
        }
        return response;
    }

    @InterfaceLog
    @RequestMapping(value = "searchMemberRight", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @ApiOperation(value = "查询会员权益", notes = "查询会员权益")
    public BaseResp<SeachMemberRightsResponse> searchMemberRight(@RequestBody @Validated BaseReq<SearchMemberRightRequest> req, BindingResult bindingResult, HttpServletRequest request) {
        BaseResp response = new BaseResp<SeachMemberRightsResponse>();
        String ip = this.getClientIP(request);
        String realChannelCode = request.getHeader(HandlerConstants.TOKEN_CHANNELCODE);
        String reqId = MdcUtils.getRequestId(realChannelCode);
        boolean fal = false;
        try {
            if (bindingResult.hasErrors()) {
                throw new RequestParamErrorException(bindingResult.getAllErrors().get(0).getDefaultMessage());
            }
            SearchMemberRightRequest searchMemberRightRequest = req.getRequest();
            // key 为会员权益规则ID value 为权益等级
            Map<String, MemberLevelDTO> memberRightMap = new HashMap<>();
            // 全部权益列表,按照权益类型分类（等级权益、星级权益、敬医权益、企业专属权益） key是类型，value是权益等级集合
            Map<String, List<MemberLevelDTO>> memberRightMapList = new HashMap<>();
            // 获取权益信息
            getMemberRights(memberRightMapList, memberRightMap, req, ip);
            //前端响应对象
            SeachMemberRightsResponse seachMemberRightsResponse = new SeachMemberRightsResponse();
            //用户ID为空，没有登录获取所有权益
            if (StringUtil.isNullOrEmpty(searchMemberRightRequest.getFfpId())) {
                if (memberRightMapList.get("Level") != null) {
                    //前端响应 会员等级权益集合对象
                    List<MemberRightsResponse> responseList = Lists.newArrayList();
                    MemberLevelDTO memberLevelDTO = new MemberLevelDTO();
                    for (MemberLevelDTO level : memberRightMapList.get("Level")) {
                        BeanUtils.copyProperties(level, memberLevelDTO);
                        List<MemberRightsDTO> value = memberLevelDTO.getMemberRights();
                        for (MemberRightsDTO memberRightsDTO : value) {
                            memberRightsDTO.setAvailable(fal);
                        }
                        MemberRightsResponse memberRightsResponse = getMemberRightsResponse(memberLevelDTO, value);
                        responseList.add(memberRightsResponse);
                    }
                    responseList.sort(Comparator.comparing(MemberRightsResponse::getSerialNumber));
                    seachMemberRightsResponse.setMemberRightsResponse(responseList);
                    response.setObjData(seachMemberRightsResponse);
                    response.setResultCode(WSEnum.SUCCESS.getResultCode());
                    response.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                    return response;
                } else {
                    response.setResultCode(WSEnum.ERROR.getResultCode());
                    response.setResultInfo(WSEnum.ERROR.getResultInfo());
                    response.setErrorInfo("权益信息为空");
                    return response;
                }
            }
            //判断用户是否登录
            boolean flag = this.checkKeyInfo(searchMemberRightRequest.getFfpId(), searchMemberRightRequest.getLoginKeyInfo(), req.getChannelCode());
            if (!flag) {
                response.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                response.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                return response;
            }
            //查询会员当前级别
            String[] items = {MemberDetailRequestItemsEnum.BASICINFO.eName
                    , MemberDetailRequestItemsEnum.STATEINFO.eName};
            PtApiCRMRequest ptApiCRM = buildCommReqNoToken(request, req.getChannelCode());
            PtMemberDetailRequest ptMemberDetailRequest = new PtMemberDetailRequest();
            ptMemberDetailRequest.setCardNO(req.getRequest().getFfpCardNo());
            ptMemberDetailRequest.setRequestItems(items);
            ptApiCRM.setData(ptMemberDetailRequest);
            PtCRMResponse<PtMemberDetail> ptCRM = memberService.memberDetail(ptApiCRM);
            if (ptCRM.getCode() != 0) {
                throw new ServiceException("会员服务查询异常");
            }
            //当前会员等级
            String memberLevelCode = "";
            if (req.getRequest().getFfpId().equals(String.valueOf(ptCRM.getData().getBasicInfo().getMemberId()))) {
                memberLevelCode = ptCRM.getData().getStateInfo().getMemberLevelCode();
            } else {
                // 检证失败返回
                throw new ServiceException("会员信息不匹配");
            }
            PtApiCRMRequest ptApiCRMRequest = buildCommReq(request, req.getChannelCode(), req.getRequest().getFfpId(), "");
            //查询当前会员权益
            PtCRMResponse<MemberRightsQueryResponse> ptCRMResponse = this.memberService.queryMemberRights(ptApiCRMRequest);
            List<MemberRightsRuleSoaModel> rightsInfos = Lists.newArrayList();
            if (ptCRMResponse.isIsSuccess() && ptCRMResponse.getCode() == 0 && null != ptCRMResponse.getData()) {
                rightsInfos = ptCRMResponse.getData().getRightsInfos();
            }
            //会员当前普通权益
            MemberLevelDTO memberLevel = new MemberLevelDTO();
            for (Map.Entry<String, List<MemberLevelDTO>> entry : memberRightMapList.entrySet()) {
                switch (entry.getKey()) {
                    case ("Level"):
                        processCommonMemberRight(entry.getValue(), memberLevel, rightsInfos, memberRightMap, seachMemberRightsResponse); //处理等级权益
                        processCurMemberRight(memberLevelCode, rightsInfos, seachMemberRightsResponse);
                        break;
                    case ("Star"):
                        processStarMemberRight(entry.getValue(), rightsInfos, req, request, response, memberRightMap, seachMemberRightsResponse, ptCRM.getData()); //处理星级权益
                        break;
                    case ("Other"):
                        processDoctorMemberRight(rightsInfos, memberRightMap, seachMemberRightsResponse); //处理敬医权益
                        break;
                    case ("Company"):
                        processCompanyMemberRight(rightsInfos, memberRightMap, req, request, seachMemberRightsResponse); //处理企业专属权益
                        break;

                }
            }
            if (memberLevel != null) {
                if (StringUtils.isBlank(seachMemberRightsResponse.getMemberStarRightsResponse().getMemberLevelDesc())) {
                    if (StringUtils.isNotBlank(memberLevel.getRuleName())) {
                        seachMemberRightsResponse.getMemberStarRightsResponse().setMemberLevelDesc(memberLevel.getRuleName().replace("权益", ""));
                    }
                }
                seachMemberRightsResponse.getMemberStarRightsResponse().setImgUrl(memberLevel.getImgUrl());
            }
            if (seachMemberRightsResponse.getMemberCompanyRightsResponse() != null &&
                    CollectionUtils.isNotEmpty(seachMemberRightsResponse.getMemberCompanyRightsResponse().getMemberRights())) {
                seachMemberRightsResponse.getMemberStarRightsResponse().setHeadDesc("我的专享权益");
            }
        } catch (Exception e) {
            this.logError(response, reqId, ip, req, e);
        }
        return response;
    }

    /**
     * @param levelCode                 当前会员等级
     * @param seachMemberRightsResponse 整体响应结果
     * @param memberLevelDTOList        当前会员权益
     * @see com.juneyaoair.appenum.member.MemberLevelEnum
     */
    private void processCurMemberRight(String levelCode, List<MemberRightsRuleSoaModel> memberLevelDTOList, SeachMemberRightsResponse seachMemberRightsResponse) {
        MemberRight memberRight = new MemberRight();
        memberRight.setCurrentLevel(levelCode);
        MemberRightsRuleSoaModel curMemberRightsRuleSoaModel = null;
        if (CollectionUtils.isNotEmpty(memberLevelDTOList)) {
            curMemberRightsRuleSoaModel = memberLevelDTOList.stream().filter(memberRightsRuleSoaModel -> "Level".equals(memberRightsRuleSoaModel.getRuleType())).findFirst().orElse(null);
        }
        if (curMemberRightsRuleSoaModel != null) {
            memberRight.setMemberLevelCode(curMemberRightsRuleSoaModel.getRuleId());
        }
        seachMemberRightsResponse.setMemberRight(memberRight);
    }

    private MemberRightsResponse getMemberRightsResponse(MemberLevelDTO memberLevelDTO, List<MemberRightsDTO> value) {
        MemberRightsResponse memberRightsResponse = new MemberRightsResponse();
        memberRightsResponse.setMemberLevelName(memberLevelDTO.getRuleName());
        memberRightsResponse.setMemberLevelCode(memberLevelDTO.getRuleId());
        memberRightsResponse.setWatermark(memberLevelDTO.getWaterMark());
        memberRightsResponse.setSerialNumber(memberLevelDTO.getSerialNumber());
        memberRightsResponse.setMemberRights(value);
        return memberRightsResponse;
    }

    /**
     * 获取权益信息
     *
     * @param memberRightList
     * @param memberRightMap
     * @param req
     * @param ip
     */
    private void getMemberRights(Map<String, List<MemberLevelDTO>> memberRightList,
                                 Map<String, MemberLevelDTO> memberRightMap,
                                 BaseReq<SearchMemberRightRequest> req, String ip) {
        List<MemberLevelDTO> list;
        String memberRights = apiRedisService.getData(RedisKeyConfig.NEW_MEMBER_RIGHTS_KEY);
        if (StringUtils.isEmpty(memberRights)) {
            MemberCenterRequest<ParamMemberRightsDTO> centerRequest = new MemberCenterRequest<>();
            centerRequest.setChannelCode(req.getChannelCode());
            centerRequest.setIp(ip);
            centerRequest.setVersion("1.0");
            centerRequest.setRequest(new ParamMemberRightsDTO(req.getChannelCode()));
            HttpResult httpResult = doPostClient(centerRequest,
                    HandlerConstants.BASIC_INFO_URL + HandlerConstants.SEARCH_MEMBER_RIGHT_URL);
            if (!httpResult.isResult() || StringUtils.isBlank(httpResult.getResponse())) {
                throw new OperationFailedException(NETWORK_ERROR_MESSAGE);
            }
            MemberCenterResponse<List<MemberLevelDTO>> memberCenterResponse = (MemberCenterResponse<List<MemberLevelDTO>>)
                    JsonUtil.jsonToBean(httpResult.getResponse(), new TypeToken<MemberCenterResponse<List<MemberLevelDTO>>>() {
                    }.getType());
            if ("10001".equalsIgnoreCase(memberCenterResponse.getResultCode())) {
                list = memberCenterResponse.getResult();
                apiRedisService.putData(RedisKeyConfig.NEW_MEMBER_RIGHTS_KEY, JsonUtil.objectToJson(list), 60 * 5L);
            } else {
                throw new OperationFailedException(memberCenterResponse.getErrorMsg());
            }
        } else {
            list = (List<MemberLevelDTO>) JsonUtil.jsonToBean(memberRights, new TypeToken<List<MemberLevelDTO>>() {
            }.getType());
        }

        if (CollectionUtils.isNotEmpty(list)) {
            //将全部会员权益列表，分开保存
            List<MemberLevelDTO> memberLevelRightList = Lists.newArrayList(); //等级权益
            List<MemberLevelDTO> memberStarRightList = Lists.newArrayList(); //星级权益
            List<MemberLevelDTO> memberDoctorRightList = Lists.newArrayList(); //敬医权益
            List<MemberLevelDTO> memberCompanyRightList = Lists.newArrayList(); //企业专属权益
            for (MemberLevelDTO item : list) {
                if (item != null) {
                    if (MemberRightsTypeEnum.LEVEL.getRuleType().equals(item.getType())) {
                        memberRightMap.computeIfAbsent(item.getRuleId(), k -> item);
                        memberLevelRightList.add(item);
                    } else if (MemberRightsTypeEnum.STAR.getRuleType().equals(item.getType())) {
                        memberRightMap.computeIfAbsent(item.getRuleId(), k -> item);
                        memberStarRightList.add(item);
                    } else if (MemberRightsTypeEnum.OTHER.getRuleType().equals(item.getType())) {
                        memberRightMap.computeIfAbsent(item.getRuleId(), k -> item);
                        memberDoctorRightList.add(item);
                    } else if (MemberRightsTypeEnum.COMPANY.getRuleType().equals(item.getType())) {
                        memberRightMap.computeIfAbsent(item.getRuleId(), k -> item);
                        memberCompanyRightList.add(item);
                    }
                }
            }
            memberRightList.put(MemberRightsTypeEnum.LEVEL.getRuleType(), memberLevelRightList);
            memberRightList.put(MemberRightsTypeEnum.STAR.getRuleType(), memberStarRightList);
            memberRightList.put(MemberRightsTypeEnum.OTHER.getRuleType(), memberDoctorRightList);
            memberRightList.put(MemberRightsTypeEnum.COMPANY.getRuleType(), memberCompanyRightList);
        }

    }


    /**
     * 处理会员等级权益
     *
     * @param levelDTOS                 等级权益集合
     * @param memberLevel               当前会员等级
     * @param rightsInfos               会员所属权益（crm返回）
     * @param memberRightMap            所有权益map集合（key 为会员权益规则ID value 为权益等级）
     * @param seachMemberRightsResponse 前端响应对象
     */
    private void processCommonMemberRight(List<MemberLevelDTO> levelDTOS, MemberLevelDTO memberLevel,
                                          List<MemberRightsRuleSoaModel> rightsInfos,
                                          Map<String, MemberLevelDTO> memberRightMap,
                                          SeachMemberRightsResponse seachMemberRightsResponse) {
        int maxSerialNumber = 0;//最大的排序序号，此序号之前的权益全部点亮
        for (MemberRightsRuleSoaModel rightsInfo : rightsInfos) {
            if (rightsInfo.getRuleType().equals(MemberRightsTypeEnum.LEVEL.getRuleType())) {
                MemberLevelDTO memberLevelDTO = memberRightMap.get(rightsInfo.getRuleId());
                if (memberLevelDTO != null) {
                    if (memberLevelDTO.getSerialNumber() > maxSerialNumber) {
                        maxSerialNumber = memberLevelDTO.getSerialNumber();
                    }
                }
            }
        }

        //处理普通权益展示及点亮会员所属权益图标
        if (CollectionUtils.isNotEmpty(levelDTOS)) {
            levelDTOS.sort(Comparator.comparing(MemberLevelDTO::getSerialNumber));
            //前端响应 会员等级权益集合对象
            List<MemberRightsResponse> responseList = Lists.newArrayList();
            for (MemberLevelDTO memberLevelDTO : levelDTOS) {
                List<MemberRightsDTO> value = memberLevelDTO.getMemberRights();
                MemberRightsResponse memberRightsResponse = new MemberRightsResponse();
                boolean available = memberLevelDTO.getSerialNumber() != null && memberLevelDTO.getSerialNumber() <= maxSerialNumber;//最大序号之前的都亮
                if (available) {
                    BeanUtils.copyProperties(memberLevelDTO, memberLevel);
                }
                if (CollectionUtils.isNotEmpty(value)) {
                    value.sort((e1, e2) -> {
                        if (null != e1.getOrderNo() && null != e2.getOrderNo()) {
                            return e1.getOrderNo() - e2.getOrderNo();
                        } else {
                            return 0;
                        }
                    });
                    value.forEach(i -> i.setAvailable(available));
                }
                memberRightsResponse.setMemberLevelName(memberLevelDTO.getRuleName());
                memberRightsResponse.setMemberLevelCode(memberLevelDTO.getRuleId());
                memberRightsResponse.setWatermark(memberLevelDTO.getWaterMark());
                memberRightsResponse.setSerialNumber(memberLevelDTO.getSerialNumber());
                memberRightsResponse.setMemberRights(value);
                responseList.add(memberRightsResponse);
            }
            responseList.sort(Comparator.comparing(MemberRightsResponse::getSerialNumber));
            responseList.forEach(i -> {
                List<MemberRightsDTO> memberRights = i.getMemberRights();
                memberRights.forEach(rightsDTO -> rightsDTO.setReceiveNumber(0));
            });
            seachMemberRightsResponse.setMemberRightsResponse(responseList);
        }
    }

    /**
     * 处理会员星级权益
     *
     * @param levelDTOS                 星级权益集合
     * @param rightsInfos               会员所属权益（crm返回）
     * @param req
     * @param request
     * @param response
     * @param memberRightMap            所有权益map集合（key 为会员权益规则ID value 为权益等级）
     * @param seachMemberRightsResponse
     * @param ptMemberDetail            会员信息
     */
    private void processStarMemberRight(List<MemberLevelDTO> levelDTOS, List<MemberRightsRuleSoaModel> rightsInfos,
                                        BaseReq<SearchMemberRightRequest> req, HttpServletRequest request, BaseResp response,
                                        Map<String, MemberLevelDTO> memberRightMap, SeachMemberRightsResponse seachMemberRightsResponse,
                                        PtMemberDetail ptMemberDetail) {
        //星级权益
        MemberLevelDTO memberStarLevelDTO = null;
        for (MemberRightsRuleSoaModel rightsInfo : rightsInfos) {
            if (MemberRightsTypeEnum.STAR.getRuleType().equals(rightsInfo.getRuleType())) {
                //将当前剩余可领取数量赋值给星级对象（做这一步的原因是因为，有的账号的星级权益领取过，与原数量不符）
                List<MemberRightsRuleDetailSoaModel> details = rightsInfo.getDetails();
                memberStarLevelDTO = memberRightMap.get(rightsInfo.getRuleId());
                if (memberStarLevelDTO != null) {
                    List<MemberRightsDTO> mrs = memberStarLevelDTO.getMemberRights();
                    if (CollectionUtils.isNotEmpty(details) && CollectionUtils.isNotEmpty(mrs)) {
                        for (MemberRightsRuleDetailSoaModel model : details) {
                            for (MemberRightsDTO dto : mrs) {
                                if (model.getRightsRecordId().equals(dto.getRightsRecordId())) {
                                    dto.setReceiveNumber(Integer.parseInt(model.getNumber()));
                                    //券的类型
                                    RightsProductTypeEnum type = RightsProductTypeEnum.checkEnum(model.getProductType());
                                    if (type != null) {
                                        dto.setRightsType(type.getCode());
                                    }

                                    //如果是优惠券
                                    if (model.getProductType().equalsIgnoreCase(RightsProductTypeEnum.COUPON.getName())) {
                                        processCouponActivity(model.getProductId(), dto);
                                    } else {
                                        //权益券
                                        processCoupon(model.getProductId(), dto);
                                    }
                                }
                            }
                        }
                    }

                }
            }
        }
        //处理星级权益及下一级星级权益展示
        processStarMemberRightsResponse(levelDTOS, memberStarLevelDTO, req, request, response, memberRightMap, seachMemberRightsResponse, ptMemberDetail);
    }

    /**
     * 处理会员敬医权益
     *
     * @param rightsInfos               会员所属权益（crm返回）
     * @param memberRightMap            所有权益map集合（key 为会员权益规则ID value 为权益等级）
     * @param seachMemberRightsResponse 前端响应
     */
    private void processDoctorMemberRight(List<MemberRightsRuleSoaModel> rightsInfos, Map<String, MemberLevelDTO> memberRightMap,
                                          SeachMemberRightsResponse seachMemberRightsResponse) {

        List<MemberRightsDTO> lists = processUsingChannelsApplyRange(rightsInfos, memberRightMap, MemberRightsTypeEnum.OTHER.getRuleType());
        if (CollectionUtils.isNotEmpty(lists)) {
            //将敬医金权益，放到前端响应对象中
            MemberDoctorRightsResponse memberDoctorRightsResponse = new MemberDoctorRightsResponse();
            lists.forEach(i -> {
                i.setAvailable(true);

            });
            memberDoctorRightsResponse.setMemberRights(lists);
            seachMemberRightsResponse.setMemberDoctorRightsResponse(memberDoctorRightsResponse);
        }
    }

    /**
     * 处理会员企业专属权益
     *
     * @param rightsInfos               会员所属权益（crm返回）
     * @param memberRightMap            所有权益map集合（key 为会员权益规则ID value 为权益等级）
     * @param req
     * @param request
     * @param seachMemberRightsResponse 前端响应
     */
    private void processCompanyMemberRight(List<MemberRightsRuleSoaModel> rightsInfos, Map<String, MemberLevelDTO> memberRightMap,
                                           BaseReq<SearchMemberRightRequest> req, HttpServletRequest request,
                                           SeachMemberRightsResponse seachMemberRightsResponse) {

        try {
            //创建调用 企业会员信息查询 请求对象
            PtCrmMileageRequest ptCrmMileageRequest = buildCommCrmReq(request, req.getChannelCode());
            CompanyMemberQueryInfoReqDto companyMemberQueryInfoReqDto = new CompanyMemberQueryInfoReqDto();
            companyMemberQueryInfoReqDto.setMemberId(Integer.parseInt(req.getRequest().getFfpId()));
            ptCrmMileageRequest.setData(companyMemberQueryInfoReqDto);
            String ip = this.getClientIP(request);
            Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");

            HttpResult httpResult = this.doPostClient(ptCrmMileageRequest, HandlerConstants.CRM_MEMBER_OPENAPI_URL + HandlerConstants.COMPANY_MEMBER_QUERYINFO, headMap, handConfig.getReadTimeout(), handConfig.getConnectTimeout());
            CompanyMemberInfoResponse companyMemberInfoResponse = new CompanyMemberInfoResponse();
            if (httpResult.isResult()) {
                PtCrmMileageResponse<CompanyMemberQueryInfoResDto> ptCrmMileageResponse;
                if (StringUtils.isNotEmpty(httpResult.getResponse())) {

                    ptCrmMileageResponse = (PtCrmMileageResponse<CompanyMemberQueryInfoResDto>) JsonUtil.jsonToBean(httpResult.getResponse(), new TypeToken<PtCrmMileageResponse<CompanyMemberQueryInfoResDto>>() {
                    }.getType());
                    if (null != ptCrmMileageResponse) {
                        if (ptCrmMileageResponse.getCode() == 0) {
                            // 根据状态显示信息，如果是已绑定，传给前端企业信息
                            CompanyMemberQueryInfoResDto companyMemberQueryInfoResDto = ptCrmMileageResponse.getData();
                            if (companyMemberQueryInfoResDto != null && companyMemberQueryInfoResDto.getStatus() == CompanyVerifyStatusEnum.BIND.code) {
                                if (companyMemberQueryInfoResDto.getCompanyInfo() != null) {
                                    BeanUtils.copyProperties(companyMemberQueryInfoResDto.getCompanyInfo(), companyMemberInfoResponse);
                                }
                                //将敬医金权益，放到前端响应对象中
                                MemberCompanyRightsResponse memberCompanyRightsResponse = new MemberCompanyRightsResponse();
                                //判断失效日期是否已到期
                                int dateDiff = DateUtils.dateDiff(DateUtils.dateToString(new Date(), DateUtils.YYYY_MM_DD_PATTERN), companyMemberInfoResponse.getExpireDate(), DateUtils.YYYY_MM_DD_PATTERN);
                                if (dateDiff <= 0) {
                                    log.info("渠道:{},卡号:{},企业会员状态:{}", req.getChannelCode(), req.getRequest().getFfpId(), "企业会员已到期，不支持查看企业会员专属权益");
                                } else {
                                    List<MemberRightsDTO> lists = processUsingChannelsApplyRange(rightsInfos, memberRightMap, MemberRightsTypeEnum.COMPANY.getRuleType());

                                    if (CollectionUtils.isNotEmpty(lists)) {
                                        lists.forEach(i -> {
                                            i.setAvailable(true);

                                        });
                                    }
                                    memberCompanyRightsResponse.setMemberRights(lists);
                                }
                                memberCompanyRightsResponse.setMemberCompanyRightsDesc("企业会员专享权益");
                                seachMemberRightsResponse.setMemberCompanyRightsResponse(memberCompanyRightsResponse);
                            }

                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("企业会员信息查询异常", e);
        }
    }


    /**
     * 处理使用渠道和适用范围
     *
     * @param rightsInfos    会员所属权益（crm返回）
     * @param memberRightMap 所有权益map集合（key 为会员权益规则ID value 为权益等级）
     * @param ruleType       权益类型
     * @return
     */
    public List<MemberRightsDTO> processUsingChannelsApplyRange(List<MemberRightsRuleSoaModel> rightsInfos, Map<String, MemberLevelDTO> memberRightMap, String ruleType) {
        List<MemberRightsDTO> lists = null;
        for (MemberRightsRuleSoaModel rightsInfo : rightsInfos) {
            if (rightsInfo.getRuleType().equals(ruleType)) {
                //将当前剩余可领取数量赋值给星级对象（做这一步的原因是因为，有的账号的星级权益领取过，与原数量不符）
                List<MemberRightsRuleDetailSoaModel> details = rightsInfo.getDetails();
                //获取会员敬医卡权益
                MemberLevelDTO memberLevelDTO = memberRightMap.get(rightsInfo.getRuleId());
                if (memberLevelDTO != null) {
                    lists = memberLevelDTO.getMemberRights();
                    if (CollectionUtils.isNotEmpty(details) && CollectionUtils.isNotEmpty(lists)) {
                        for (MemberRightsRuleDetailSoaModel model : details) {
                            for (MemberRightsDTO dto : lists) {
                                if (model.getRightsRecordId().equals(dto.getRightsRecordId())) {
                                    dto.setReceiveNumber(Integer.parseInt(model.getNumber()));
                                    //券的类型
                                    RightsProductTypeEnum type = RightsProductTypeEnum.checkEnum(model.getProductType());
                                    if (type != null) {
                                        dto.setRightsType(type.getCode());
                                    }

                                    //如果是优惠券
                                    if (model.getProductType().equalsIgnoreCase(RightsProductTypeEnum.COUPON.getName())) {
                                        processCouponActivity(model.getProductId(), dto);
                                    } else {
                                        //权益券
                                        processCoupon(model.getProductId(), dto);
                                    }
                                }
                            }
                        }
                    }

                }
            }
        }


        return lists;
    }

    /**
     * 处理优惠券的使用渠道、适用范围
     *
     * @param dto
     * @param activityNo
     */
    public void processCouponActivity(String activityNo, MemberRightsDTO dto) {
        dto.setActivityNo(activityNo);
        CouponActivityReq couponActivityReq = new CouponActivityReq();
        couponActivityReq.setActivityNo(activityNo);
        couponActivityReq.setChannelCode(HandlerConstants.CRM_CHANNEL_CODE);
        couponActivityReq.setVersion("10");
        couponActivityReq.setUserNo("10007");
        HttpResult httpResult = doPostClient(couponActivityReq,
                HandlerConstants.URL_FARE_API + HandlerConstants.SUB_QUERY_COUPON_ACTIVITY);
        if (httpResult.isResult()) {
            CouponActivityResp couponActivityResp = (CouponActivityResp)
                    JsonUtil.jsonToBean(httpResult.getResponse(), new TypeToken<CouponActivityResp>() {
                    }.getType());
            if ("1001".equals(couponActivityResp.getResultCode())) {
                List<CouponActivityInfo> couponActivityList = couponActivityResp.getCouponActivityList();
                if (!CollectionUtils.isEmpty(couponActivityList)) {
                    //适用范围
                    dto.setApplyRange(StringUtil.isNullOrEmpty(couponActivityList.get(0).getRemark()) ? handConfig.getApplyRange() : couponActivityList.get(0).getRemark());
                    //使用渠道
                    String channelNm = couponActivityList.get(0).getChannelNm();
                    if (!StringUtil.isNullOrEmpty(channelNm)) {
                        String[] channelNms = channelNm.split(",");
                        for (String c : channelNms) {
                            UsingChannelsEnum usingChannelsEnum = UsingChannelsEnum.checkEnum(c);
                            if (usingChannelsEnum != null) {
                                channelNm = channelNm.replaceAll(usingChannelsEnum.getUsingChannel(), usingChannelsEnum.getDesc());
                            }
                        }
                    } else {
                        channelNm = handConfig.getUsingChannels();
                    }
                    dto.setUsingChannels(channelNm);
                } else {
                    dto.setUsingChannels(handConfig.getUsingChannels());
                    dto.setApplyRange(handConfig.getApplyRange());
                }
            } else {
                dto.setUsingChannels(handConfig.getUsingChannels());
                dto.setApplyRange(handConfig.getApplyRange());
            }
        } else {
            dto.setUsingChannels(handConfig.getUsingChannels());
            dto.setApplyRange(handConfig.getApplyRange());
        }
    }

    /**
     * 处理权益券的使用渠道、适用范围
     *
     * @param dto
     * @param activityNo
     */
    public void processCoupon(String activityNo, MemberRightsDTO dto) {
        dto.setActivityNo(activityNo);
        CouponActivityReq couponActivityReq = new CouponActivityReq();
        couponActivityReq.setActivityNo(activityNo);
        couponActivityReq.setChannelCode(HandlerConstants.CRM_CHANNEL_CODE);
        couponActivityReq.setVersion("10");
        HttpResult httpResult = doPostClient(couponActivityReq,
                HandlerConstants.URL_COUPON_API + HandlerConstants.COUPON_QUERY);
        if (httpResult.isResult()) {
            CouponResp couponActivityResp = (CouponResp)
                    JsonUtil.jsonToBean(httpResult.getResponse(), new TypeToken<CouponResp>() {
                    }.getType());
            if ("10001".equals(couponActivityResp.getResultCode())) {
                //适用范围
                dto.setApplyRange(StringUtil.isNullOrEmpty(couponActivityResp.getApplyRange()) ? handConfig.getApplyRange() : couponActivityResp.getApplyRange().replaceAll("<p>", "").replaceAll("</p>", ""));
                //使用渠道
                dto.setUsingChannels(StringUtil.isNullOrEmpty(couponActivityResp.getUsingChannels()) ? handConfig.getUsingChannels() : couponActivityResp.getUsingChannels().replaceAll("<p>", "").replaceAll("</p>", ""));
            } else {
                dto.setUsingChannels(handConfig.getUsingChannels());
                dto.setApplyRange(handConfig.getApplyRange());
            }
        } else {
            dto.setUsingChannels(handConfig.getUsingChannels());
            dto.setApplyRange(handConfig.getApplyRange());
        }
    }

    /**
     * 处理星级响应
     *
     * @param memberStarRightList 会员所有星级权益集合
     * @param memberStarLevelDTO  会员权益
     * @param req
     * @param request
     * @param response
     */
    public void processStarMemberRightsResponse(List<MemberLevelDTO> memberStarRightList, MemberLevelDTO memberStarLevelDTO,
                                                BaseReq<SearchMemberRightRequest> req,
                                                HttpServletRequest request, BaseResp response, Map<String, MemberLevelDTO> memberRightMap,
                                                SeachMemberRightsResponse seachMemberRightsResponse,
                                                PtMemberDetail ptMemberDetail) {
        memberStarRightList.sort((e1, e2) -> {
            if (null != e1.getSerialNumber() && null != e2.getSerialNumber()) {
                return e1.getSerialNumber() - e2.getSerialNumber();
            } else {
                return 0;
            }
        });
        //处理星级权益展示
        MemberStarRightsResponse memberStarRightsResponse = new MemberStarRightsResponse();
        memberStarRightsResponse.setCurrentLevel(ptMemberDetail.getStateInfo().getMemberLevelCode());
        memberStarRightsResponse.setHeadDesc("我的星级专享权益");
        memberStarRightsResponse.setMemberRightsDesc("会员权益");
        //调用crm 查询会员信息
        String channelCode = req.getChannelCode();//MOBILE
        //查询会员当前级别
        String[] items = {MemberDetailRequestItemsEnum.BASICINFO.eName
                , MemberDetailRequestItemsEnum.STATEINFO.eName};
        PtApiCRMRequest ptApiCRM = buildCommReqNoToken(request, channelCode);
        PtMemberDetailRequest ptMemberDetailRequest = new PtMemberDetailRequest();
        ptMemberDetailRequest.setCardNO(req.getRequest().getFfpCardNo());
        ptMemberDetailRequest.setRequestItems(items);
        ptApiCRM.setData(ptMemberDetailRequest);
        PtApiCRMRequest<MemberStarQueryRequest> ptApiCRMRequestMemberStar = CRMReqUtil.buildCommReqNoToken(request, channelCode);
        MemberStarQueryRequest memberStarQueryRequest = new MemberStarQueryRequest();
        memberStarQueryRequest.setId(Integer.valueOf(req.getRequest().getFfpId()));
        ptApiCRMRequestMemberStar.setData(memberStarQueryRequest);
        PtCRMResponse<MemberStarQueryResp> ptCRMResponse = memberService.queryMemberStar(ptApiCRMRequestMemberStar);
        if (memberStarLevelDTO == null &&
                MemberLevelEnum.Fu_Card.getLevelCode().equals(ptMemberDetail.getStateInfo().getMemberLevelCode())
                && ptCRMResponse.getData().getMemberStarCode() == 0) {
            memberStarLevelDTO = memberRightMap.get(handConfig.getMemberStarRuleId());
        }
        //星级响应赋值
        if (memberStarLevelDTO != null && !StringUtil.isNullOrEmpty(memberStarLevelDTO.getRuleId())) {
            memberStarRightsResponse.setMemberLevelName(memberStarLevelDTO.getRuleName());
            memberStarRightsResponse.setMemberLevelCode(memberStarLevelDTO.getRuleId());
            memberStarRightsResponse.setWatermark(memberStarLevelDTO.getWaterMark());
            memberStarRightsResponse.setSerialNumber(memberStarLevelDTO.getSerialNumber());
            String currentRuleName = memberStarLevelDTO.getRuleName().replace("权益", "").replace("-新版", "");
            memberStarRightsResponse.setMemberLevelDesc(ptMemberDetail.getStateInfo().isIsDoctorCard() ? "敬医" + currentRuleName : currentRuleName);
            memberStarLevelDTO.getMemberRights().sort((e1, e2) -> {
                if (null != e1.getOrderNo() && null != e2.getOrderNo()) {
                    return e1.getOrderNo() - e2.getOrderNo();
                } else {
                    return 0;
                }
            });
            memberStarLevelDTO.getMemberRights().forEach(i -> {
                i.setAvailable(true);

            });
            memberStarRightsResponse.setMemberRights(memberStarLevelDTO.getMemberRights());
            if (CollectionUtils.isEmpty(memberStarRightsResponse.getMemberRights())) {
                memberStarRightsResponse.setDescription("暂无星级权益，提高星级获取专享权益");
            }
        } else {
            if (CollectionUtils.isEmpty(memberStarRightsResponse.getMemberRights())) {
                memberStarRightsResponse.setMemberRights(Lists.newArrayList());
                memberStarRightsResponse.setDescription("暂无星级权益，提高星级获取专享权益");
            }
        }

        if (ptMemberDetail.getStateInfo().isIsDoctorCard()) {
            if (ptMemberDetail.getStateInfo().getMemberLevelCode().equals(MemberLevelEnum.Silver.getLevelCode()) ||
                    ptMemberDetail.getStateInfo().getMemberLevelCode().equals(MemberLevelEnum.Golden.getLevelCode())) {
                if (ptMemberDetail.getStateInfo().getMemberStar() == 3) {
                    seachMemberRightsResponse.setMemberStarRightsResponse(memberStarRightsResponse);
                    response.setResultCode(WSEnum.SUCCESS.getResultCode());
                    response.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                    response.setObjData(seachMemberRightsResponse);
                    return;
                }
            }
        }

        //赋值下一级星级权益
        MemberLevelDTO nextMemberStarLevelDTO = null;
        for (int i = 0; i < memberStarRightList.size(); i++) {
            MemberLevelDTO msr = memberStarRightList.get(i);
            //判断会员是否有星级权益
            if (memberStarLevelDTO != null) {
                if (!StringUtil.isNullOrEmpty(memberStarLevelDTO.getRuleId())) {
                    if (msr.getSerialNumber() > memberStarLevelDTO.getSerialNumber()) {
                        if (CollectionUtils.isNotEmpty(msr.getMemberRights())) {
                            nextMemberStarLevelDTO = msr;
                            break;
                        }
                    }
                } else {
                    //如果会员没有星级权益，下一星级权益赋值
                    if (CollectionUtils.isNotEmpty(msr.getMemberRights())) {
                        nextMemberStarLevelDTO = msr;
                        break;
                    }
                }

            }

        }
        if (nextMemberStarLevelDTO != null) {
            memberStarRightsResponse.setNextMemberLevelName(ptMemberDetail.getStateInfo().isIsDoctorCard() ? "敬医" + nextMemberStarLevelDTO.getRuleName() : nextMemberStarLevelDTO.getRuleName());
            memberStarRightsResponse.setNextMemberLevelCode(nextMemberStarLevelDTO.getRuleId());
            nextMemberStarLevelDTO.getMemberRights().sort((e1, e2) -> {
                if (null != e1.getOrderNo() && null != e2.getOrderNo()) {
                    return e1.getOrderNo() - e2.getOrderNo();
                } else {
                    return 0;
                }
            });
            nextMemberStarLevelDTO.getMemberRights().forEach(i -> i.setAvailable(false));
            memberStarRightsResponse.setNextMemberStarRights(nextMemberStarLevelDTO.getMemberRights());
            String nextRuleName = memberStarRightsResponse.getNextMemberLevelName().replace("权益", "").replace("-新版", "");
            if ((Integer.valueOf(ptMemberDetail.getStateInfo().getMemberLevelCode()) <= 1 && ptCRMResponse.getData().getMemberStarCode() < 5) || ptCRMResponse.getData().getMemberStarCode() < 3) {
                memberStarRightsResponse.setNextDescription("升级至" + nextRuleName + "获得更多权益");
            }
        } else {
            if (memberStarLevelDTO != null && !StringUtil.isNullOrEmpty(memberStarLevelDTO.getRuleId())) {
                //判断当前星级权益级别是不是最高级别
                Boolean flag = memberStarRightList.get(memberStarRightList.size() - 1).getRuleId().equals(memberStarLevelDTO.getRuleId());
                String ruleName = "";
                if (flag) {
                    if ((Integer.valueOf(ptMemberDetail.getStateInfo().getMemberLevelCode()) <= 1 && ptCRMResponse.getData().getMemberStarCode() < 5) || ptCRMResponse.getData().getMemberStarCode() < 3) {
                        ruleName = memberStarRightList.get(memberStarRightList.size() - 1).getRuleName().replace("权益", "会员").replace("-新版", "");
                        memberStarRightsResponse.setNextDescription("您已成为" + ruleName + "，续级成功后将保持享有以上权益");
                    }
                } else {
                    if ((Integer.valueOf(ptMemberDetail.getStateInfo().getMemberLevelCode()) <= 1 && ptMemberDetail.getStateInfo().getMemberStar() < 5) || ptCRMResponse.getData().getMemberStarCode() < 3) {
                        ruleName = memberStarLevelDTO.getRuleName().replace("权益", "").replace("-新版", "");
                        memberStarRightsResponse.setNextDescription("您当前星级为" + ruleName);
                    }
                }
            } else {
                memberStarRightsResponse.setNextDescription(null);
            }
        }

        if (StringUtil.isNullOrEmpty(memberStarRightsResponse.getMemberLevelName())) {
            memberStarRightsResponse.setMemberLevelName(MemberLevelEnum.findLevelNameByLevelCode(ptMemberDetail.getStateInfo().getMemberLevelCode()));
        }
        if (StringUtil.isNullOrEmpty(memberStarRightsResponse.getMemberLevelCode())) {
            memberStarRightsResponse.setMemberLevelCode(ptMemberDetail.getStateInfo().getMemberLevelCode());
        }
        if (StringUtil.isNullOrEmpty(memberStarRightsResponse.getMemberLevelDesc())) {
            memberStarRightsResponse.setMemberLevelDesc(MemberLevelEnum.findLevelNameByLevelCode(ptMemberDetail.getStateInfo().getMemberLevelCode()) + ptMemberDetail.getStateInfo().getMemberStar() + "星");
        }
        seachMemberRightsResponse.setMemberStarRightsResponse(memberStarRightsResponse);
        response.setResultCode(WSEnum.SUCCESS.getResultCode());
        response.setResultInfo(WSEnum.SUCCESS.getResultInfo());
        response.setObjData(seachMemberRightsResponse);
    }

    @RequestMapping(value = "receiveMemberRight", method = RequestMethod.POST)
    @ApiOperation(value = "领取会员权益", notes = "领取会员权益")
    @NotDuplicate
    public BaseResp receiveMemberRight(@RequestBody BaseReq<ReceiveMemberRightRequest> req, HttpServletRequest request) {
        BaseResp response = new BaseResp<>();
        String ip = this.getClientIP(request);
        String reqId = StringUtil.newGUID() + "_Member_ReceiveMemberRight";
        String logStr = JsonUtil.objectToJson(req);
        saveReqInfo(SERVICE_NAME, reqId, ip, logStr);
        try {
            //校验参数
            ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
            Validator validator = factory.getValidator();
            Set<ConstraintViolation<BaseReq>> violations = validator.validate(req);
            if (CollectionUtils.isNotEmpty(violations)) {
                response.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                response.setResultInfo(violations.iterator().next().getMessage());
                return response;
            }

            PtApiCRMRequest ptApiCRMRequest = buildCommReq(request, req.getChannelCode(), req.getRequest().getFfpId(), "");
            Map<String, Integer> map = new HashMap<>();
            map.put("RightsRecordId", req.getRequest().getRightsRecordId());
            ptApiCRMRequest.setData(map);
            PtCRMResponse ptCRMResponse = this.memberService.receiveMemberRights(ptApiCRMRequest);

            if (ptCRMResponse.isIsSuccess() && ptCRMResponse.getCode() == 0) {
                response.setResultCode(WSEnum.SUCCESS.getResultCode());
                response.setResultInfo(WSEnum.SUCCESS.getResultInfo());
            } else if (!ptCRMResponse.isIsSuccess() && ptCRMResponse.getCode() == 2) {
                response.setResultCode(WSEnum.ERROR.getResultCode());
                response.setResultInfo("您的星级已发生变动，新权益等待开放，请稍后再试。");
            } else {

                /**
                 * 领取失败 当crm上限大于产品上限，导致领取失败，提示信息【您已有5张未使用的+权益券名称，请使用后再继续领取】
                 * 我给前端的错误码是10012，错误信息直接显示给前端
                 */
                response.setResultCode(WSEnum.ERROR.getResultCode());
                response.setResultInfo("领取失败");
                if (ptCRMResponse.getCode() == 156008) {
                    if (!StringUtil.isNullOrEmpty(ptCRMResponse.getMsg())) {
                        List<String> arrays = Arrays.asList(ptCRMResponse.getMsg().split("："));
                        if (CollectionUtils.isNotEmpty(Arrays.asList(arrays)) && arrays.size() == 2) {
                            if ("5302".equals(arrays.get(0))) {
                                response.setResultCode("10012");
                                response.setResultInfo(arrays.get(1));
                            }
                        }

                    }

                } else {
                    response.setResultCode(WSEnum.ERROR.getResultCode());
                    if (ptCRMResponse.getMsg().equals("领取失败")) {
                        response.setResultInfo("领取失败");
                    } else {
                        response.setResultInfo("领取失败，" + ptCRMResponse.getMsg());
                    }
                }


            }
        } catch (Exception e) {
            this.logError(response, reqId, ip, req, e);
            //告诉前端这个错的时候刷新一下数据
            response.setResultCode(WSEnum.ERROR.getResultCode());
            response.setResultInfo("您的星级已发生变动，新权益等待开放，请稍后再试。");
        }
        return response;
    }

    //支付宝验证是否是本人
    @ApiOperation(value = "支付宝身份验证", notes = "支付宝身份验证")
    @RequestMapping(value = "/alipayIdentityVerify", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp alipayIdentityVerify(@RequestBody BaseReq<AlipayIdentityVerifyReq> req, HttpServletRequest request) {
        //响应
        BaseResp resp = new BaseResp();
        try {
            //验证请求参数
            ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
            Validator validator = factory.getValidator();
            Set<ConstraintViolation<BaseReq>> violations = validator.validate(req);
            if (CollectionUtils.isNotEmpty(violations)) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo(violations.iterator().next().getMessage());
                return resp;
            }
            AlipayIdentityVerifyReq identityVerifyReq = req.getRequest();
            boolean flag = this.checkKeyInfo(identityVerifyReq.getFfpId(), identityVerifyReq.getLoginKeyInfo(), req.getChannelCode());
            if (!flag) {
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                return resp;
            }
            //证件号校验，只支持身份证认证
            boolean matcheID = identityVerifyReq.getIdCard().matches(PatternCommon.ID_NUMBER);
            //只支持身份证合并账户
            if (!matcheID) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo("不支持合并的证件类型");
                return resp;
            }
            //构建crm请求参数
            String channelCode = req.getChannelCode();
            PtApiCRMRequest<Map<String, Object>> ptApiRequest = buildCommReq(request, channelCode, identityVerifyReq.getFfpId(), "");
            Map<String, Object> reqMap = new HashMap();
            reqMap.put(CODE, identityVerifyReq.getCode());
            reqMap.put(NAME, identityVerifyReq.getLastName() + identityVerifyReq.getFirstName());
            reqMap.put(IDCARD, identityVerifyReq.getIdCard());
            ptApiRequest.setData(reqMap);
            PtCRMResponse ptCRMResponse = memberService.alipayIdentityVerify(ptApiRequest);
            if (ptCRMResponse.isIsSuccess()) {
                if (ptCRMResponse.getCode() == 0) {
                    //成功返回，将验证结果添加到redis
                    apiRedisService.putData(MEMBER_MERGE_KEY_PREFIX + identityVerifyReq.getFfpCardNo() + identityVerifyReq.getIdCard(), ALIPAY_IDENTITY_VERIFY, 120);
                    log.info("[合并账户支付宝身份验证],用户姓名{},证件号{}，验证时间{}", identityVerifyReq.getLastName() + identityVerifyReq.getFirstName(), identityVerifyReq.getIdCard(), System.currentTimeMillis());
                    //认证成功后，将会员姓名存到会员信息中
                    PtApiCRMRequest<PtModifyCustomerInfoRequest> ptModifyRequest = buildCommReq(request, channelCode, identityVerifyReq.getFfpId(), "");
                    PtModifyCustomerInfoRequest modifyCustomerInfoRequest = new PtModifyCustomerInfoRequest();
                    //名
                    modifyCustomerInfoRequest.setCFirstName(identityVerifyReq.getFirstName());
                    //姓
                    modifyCustomerInfoRequest.setCLastName(identityVerifyReq.getLastName());
                    ptModifyRequest.setData(modifyCustomerInfoRequest);
                    PtCRMResponse modifyResponse = memberService.modifyCustomerInfo(ptModifyRequest);
                    //未修改成功
                    if (modifyResponse.getCode() != 0) {
                        resp.setResultCode(WSEnum.ERROR.getResultCode());
                        resp.setResultInfo(modifyResponse.getMsg());
                        return resp;
                    } else {
                        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                        resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                        return resp;
                    }
                } else {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo(ptCRMResponse.getMsg());
                    return resp;
                }
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo(ptCRMResponse.getMsg());
                return resp;
            }
        } catch (Exception e) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR.getResultInfo());
            return resp;
        }
    }

    @ApiOperation(value = "开通免密支付", notes = "开通免密支付")
    @RequestMapping(value = "openFreePayment", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp openFreePayment(@RequestBody BaseReq<ScoreFreeState> req, HttpServletRequest request) {
        BaseResp resp = new BaseResp();
        //验证请求参数
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<BaseReq>> violations = validator.validate(req);
        if (CollectionUtils.isNotEmpty(violations)) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo(violations.iterator().next().getMessage());
            return resp;
        }
        String channelCode = req.getChannelCode();
        ScoreFreeState scoreFreeState = req.getRequest();
        if (scoreFreeState == null) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo(WSEnum.ERROR_REQUEST_PARAMS.getResultInfo());
            return resp;
        }
        String ffpId = scoreFreeState.getFfpId();
        String loginKeyInfo = scoreFreeState.getLoginKeyInfo();
        // 认证信息认证
        if (!this.checkKeyInfo(ffpId, loginKeyInfo, channelCode)) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("信息检验失败！");
            return resp;
        }
        //开通必须先进行实名认证
        if ("Y".equals(scoreFreeState.getOpenState())) {
            boolean accmflag = false;
            PtRealNameReq ptApiRequest = new PtRealNameReq();
            ptApiRequest.setID(ffpId);
            ptApiRequest.setClientCode(channelCode);
            ptApiRequest.setSignature(EncoderHandler.encodeByMD5(channelCode + ffpId + getClientPwd(channelCode)).toUpperCase());
            PtRealNameResp realNameResp = memberService.realNameState(ptApiRequest);
            if ("000".equals(realNameResp.getStatusCode())) {
                VerifyStatusEnum verifyStatusEnum = VerifyStatusEnum.formatVerifyStatus(realNameResp.getVerifyStatus());
                if (verifyStatusEnum != null && VerifyStatusEnum.PASS.code.equals(verifyStatusEnum.code)) {
                    accmflag = true;
                }
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo(realNameResp.getMessage());
                return resp;
            }
            if (!accmflag) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("请先完成实名认证！");
                return resp;
            }
        }
        PtApiCRMRequest ptApiCRMRequest = buildCommReq(request, channelCode, ffpId);
        Map<String, Object> param = new HashMap<>();
        param.put("IsSmallExemptPwd", scoreFreeState.getOpenState());
        ptApiCRMRequest.setData(param);
        PtCRMResponse ptCRMResponse = memberService.modifyStateInfo(ptApiCRMRequest);
        if (ptCRMResponse.getCode() == 0) {
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(ptCRMResponse.getMsg());
        }
        return resp;
    }

    @ApiOperation(value = "token换取账户信息", notes = "token换取账户信息")
    @RequestMapping(value = "checkLoginInfo", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp<MemberBaseInfo> checkLoginInfo(@RequestBody BaseReq<CheckLogin> req, HttpServletRequest request) {
        BaseResp resp = new BaseResp();
        //验证请求参数
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<BaseReq>> violations = validator.validate(req);
        if (CollectionUtils.isNotEmpty(violations)) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo(violations.iterator().next().getMessage());
            return resp;
        }
        CheckLogin checkLogin = req.getRequest();
        if (checkLogin == null) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo("请求参数不完整！");
            return resp;
        }
        //验证数据签名
        String sign = EncoderHandler.encodeByMD5(checkLogin.getAccountNo() + checkLogin.getAccountPwd() + req.getTimeStamp());
        sign = EncoderHandler.encodeByMD5(sign + checkLogin.getAccountNo()).toUpperCase();
        if (!sign.equals(req.getSign())) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo("请求参数验证不通过！");
            return resp;
        }
        //IP地址限制，只允许指定机器访问
        String ip = this.getClientIP(request);
        if (checkIp(ip, handConfig.getBillAuthIp())) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(ip + "未被授权访问！");
            return resp;
        }
        String channelCode = req.getChannelCode();
        boolean flag = this.checkKeyInfo(checkLogin.getFfpId(), checkLogin.getLoginKeyInfo(), channelCode);
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        //返回对应的卡号，姓名，证件信息
        String[] items = {MemberDetailRequestItemsEnum.BASICINFO.eName, MemberDetailRequestItemsEnum.CERTIFICATEINFO.eName};
        PtApiCRMRequest<PtMemberDetailRequest> ptApiRequest = CRMReqUtil.buildMemberDetailReq(checkLogin.getFfpCardNo(), checkLogin.getFfpId(), request, channelCode, items);
        PtCRMResponse<PtMemberDetail> ptMemberDetailPtCRMResponse = memberService.memberDetail(ptApiRequest);
        if (ptMemberDetailPtCRMResponse.getCode() != 0) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(ptMemberDetailPtCRMResponse.getMsg());
            return resp;
        }
        PtMemberDetail ptMemberDetail = ptMemberDetailPtCRMResponse.getData();
        List<MemberCertificateSoaModelV2> memberCertificateSoaModelV2List = ptMemberDetail.getCertificateInfo();
        List<CertificateModel> certList = new ArrayList<>();
        if (!StringUtil.isNullOrEmpty(memberCertificateSoaModelV2List)) {
            for (MemberCertificateSoaModelV2 modelV2 : memberCertificateSoaModelV2List) {
                CertificateModel certificateModel = new CertificateModel();
                certificateModel.setCertificateNumber(modelV2.getCertificateNumber());
                CertificateTypeEnum ct = CertificateTypeEnum.checkType(modelV2.getCertificateType());
                certificateModel.setCertificateTypeCName(ct.getDesc());
                certificateModel.setCertificateTypeEName(ct.geteName());
                certList.add(certificateModel);
            }
        }
        MemberBaseInfo memberBaseInfo = new MemberBaseInfo();
        memberBaseInfo.setMemberNo(checkLogin.getFfpCardNo());
        memberBaseInfo.setName(CRMReqUtil.getMemberName(ptMemberDetail.getBasicInfo()));
        memberBaseInfo.setCertList(certList);
        resp.setObjData(memberBaseInfo);
        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
        resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
        return resp;
    }

    @NotDuplicate
    @ApiOperation(value = "三要素注册", notes = "三要素注册")
    @InterfaceLog
    @RequestMapping(value = "registerBasic", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp registerBasic(@RequestBody @Validated BaseReq<RegisterBasicReq> req, BindingResult bindingResult, HttpServletRequest request) {
        BaseResp resp = new BaseResp();
        //验证请求参数
        if (bindingResult.hasErrors()) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
            return resp;
        }
        RegisterBasicReq registerBasicReq = req.getRequest();
        ObjCheckUtil.notNull(registerBasicReq, "参数不可为空");
        ObjCheckUtil.notNull(req.getSign(), "签名不可为空");
        //签名信息验证
        String registerStr = JsonMapper.buildNonNullMapper().toJson(registerBasicReq);
        log.info("待签名字段:{}", registerStr);
        String sign = EncoderHandler.encodeByMD5(registerStr + handConfig.getApiKey());
        if (!req.getSign().equals(sign)) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo("参数检验不通过");
            return resp;
        }
        PtApiCRMRequest<PtRegisterRequest> ptApiCRMRequest = buildRegisterBasicReq(registerBasicReq, this.getClientIP(request));
        PtCRMResponse<PtRegisterResponse> ptCRMResponse = memberService.register(ptApiCRMRequest);
        if (ptCRMResponse.isIsSuccess() && ptCRMResponse.getCode() == 0) {
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
            PtRegisterResponse ptRegisterResponse = ptCRMResponse.getData();
            UserInfoNoMust userInfo = new UserInfoNoMust();
            userInfo.setFfpId(String.valueOf(ptRegisterResponse.getMemberId()));
            userInfo.setFfpCardNo(ptRegisterResponse.getMemberCardNo());
            userInfo.setLoginKeyInfo(EncoderHandler.encodeByMD5(userInfo.getFfpId() + userInfo.getFfpCardNo() + HandlerConstants.ACCESSSECRET));
            resp.setObjData(userInfo);
        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(ptCRMResponse.getMsg());
        }
        return resp;
    }

    @InterfaceLog
    @ApiOperation(value = "实名认证", notes = "实名认证")
    @RequestMapping(value = "authMember", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp authMember(@RequestBody BaseReq<AuthBasic> req, HttpServletRequest request) {
        BaseResp resp = new BaseResp();
        //验证请求参数
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<BaseReq>> violations = validator.validate(req);
        if (CollectionUtils.isNotEmpty(violations)) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo(violations.iterator().next().getMessage());
            return resp;
        }
        AuthBasic userInfo = req.getRequest();
        ObjCheckUtil.notNull(userInfo, "请求参数不可为空");
        ObjCheckUtil.notNull(req.getSign(), "签名不可为空");
        //签名信息验证
        String registerStr = JsonMapper.buildNonNullMapper().toJson(userInfo);
        log.info("待签名字段:{}", registerStr);
        String sign = EncoderHandler.encodeByMD5(registerStr + handConfig.getApiKey());
        if (!req.getSign().equals(sign)) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo("参数检验不通过");
            return resp;
        }
        //检验信息
        String loginKeyInfo = EncoderHandler.encodeByMD5(userInfo.getFfpId() + userInfo.getFfpCardNo() + HandlerConstants.ACCESSSECRET);
        if (!userInfo.getLoginKeyInfo().equals(loginKeyInfo)) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("用户信息检验失败");
            return resp;
        }
        String[] items = {MemberDetailRequestItemsEnum.BASICINFO.eName, MemberDetailRequestItemsEnum.CERTIFICATEINFO.eName};
        PtApiCRMRequest<PtMemberDetailRequest> ptMemberDetailRequestPtApiCRMRequest = CRMReqUtil.buildMemberDetailReq(userInfo.getFfpCardNo(), userInfo.getFfpId(), request, ChannelCodeEnum.MOBILE.getChannelCode(), items);
        PtCRMResponse<PtMemberDetail> ptMemberDetailPtCRMResponse = memberService.memberDetail(ptMemberDetailRequestPtApiCRMRequest);
        if (ptMemberDetailPtCRMResponse.getCode() == 0) {
            PtMemberDetail ptMemberDetail = ptMemberDetailPtCRMResponse.getData();
            String name = CRMReqUtil.getChinaName(ptMemberDetail.getBasicInfo());
            if (StringUtil.isNullOrEmpty(name)) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("请先完善中文姓名");
                return resp;
            }
            MemberCertificateSoaModelV2 memberCertificateSoaModelV2 = CRMReqUtil.getCertificateInfo(ptMemberDetail.getCertificateInfo(), CertificateTypeEnum.ID_CARD.getCode());
            if (memberCertificateSoaModelV2 == null || StringUtil.isNullOrEmpty(memberCertificateSoaModelV2.getCertificateNumber())) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("请先完善身份证件信息");
                return resp;
            }
            if (!userInfo.getCertNo().equals(memberCertificateSoaModelV2.getCertificateNumber())) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("身份信息不匹配");
                return resp;
            }
            //调用外部实名认证接口
            PtApiCRMRequest<OuterRealNameRequest> ptApiCRMRequest = buildCommReq(request, ChannelCodeEnum.MOBILE.getChannelCode(), userInfo.getFfpId());
            OuterRealNameRequest outerRealNameRequest = new OuterRealNameRequest();
            outerRealNameRequest.setVerifyChannel(VerifyChannelEnum.Face.code);
            ptApiCRMRequest.setData(outerRealNameRequest);
            PtCRMResponse crmResponse = memberService.outRealName(ptApiCRMRequest);
            if (crmResponse.getCode() != 0) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo(crmResponse.getMsg());
                return resp;
            }
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
            return resp;
        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(ptMemberDetailPtCRMResponse.getMsg());
            return resp;
        }
    }

    /**
     * 封装基础请求参数
     *
     * @param registerBasicReq
     * @return
     */
    private PtApiCRMRequest<PtRegisterRequest> buildRegisterBasicReq(RegisterBasicReq registerBasicReq, String ip) {
        PtApiCRMRequest<PtRegisterRequest> ptApiCRMRequest = new PtApiCRMRequest<>();
        PtRegisterRequest ptRegisterRequest = new PtRegisterRequest();
        //证件类型目前固定为身份证
        ptRegisterRequest.setCertificateType(CertificateTypeEnum.ID_CARD.geteName());
        ptRegisterRequest.setCertificateNumber(registerBasicReq.getCertNo());
        ptRegisterRequest.setCFirstName(registerBasicReq.getcFirstName());
        ptRegisterRequest.setCLastName(registerBasicReq.getcLastName());
        ptRegisterRequest.setMobile(registerBasicReq.getPhoneNum());
        String birthDate = CertUtil.certNoToDate(registerBasicReq.getCertNo());
        Date birDate = DateUtils.toDate(birthDate, DateUtils.YYYY_MM_DD_PATTERN);
        ptRegisterRequest.setBirthday(birDate.getTime());
        String sex = CertUtil.checkSex(registerBasicReq.getCertNo());
        ptRegisterRequest.setSex(sex);
        ptRegisterRequest.setSalutationCode("M".equals(sex) ? SalutationEnum.MR.geteName() : SalutationEnum.MS.geteName());
        //密码默认为手机号
        ptRegisterRequest.setPassword(registerBasicReq.getPhoneNum());
        Header header = CRMReqUtil.buildHeader("-1", "", ip);
        ptApiCRMRequest.setHeader(header);
        ptApiCRMRequest.setChannel(ChannelCodeEnum.MOBILE.getChannelCode());
        ptApiCRMRequest.setChannelPwd(getClientPwd(ChannelCodeEnum.MOBILE.getChannelCode()));
        ptApiCRMRequest.setData(ptRegisterRequest);
        return ptApiCRMRequest;
    }

    /**
     * 检验访问IP是否被授权访问
     *
     * @param ip
     * @param authIp
     * @return
     */
    private boolean checkIp(String ip, String authIp) {
        if (StringUtil.isNullOrEmpty(authIp)) {
            return false;
        }
        String[] ips = authIp.split(",");
        for (String t : ips) {
            if (ip.equals(t)) {
                return true;
            }
        }
        return false;
    }

    @ApiOperation(value = "实名认证方式", notes = "实名认证方式")
    @RequestMapping(value = "supportVerMethod", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp supportVerMethod(@RequestBody BaseReq<VerChannel> req, HttpServletRequest request) {
        BaseResp resp = new BaseResp();
        //验证请求参数
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<BaseReq>> violations = validator.validate(req);
        if (CollectionUtils.isNotEmpty(violations)) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo(violations.iterator().next().getMessage());
            return resp;
        }
        String channelCode = req.getChannelCode();
        VerChannel verChannel = req.getRequest();
        if (verChannel == null) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo(WSEnum.ERROR_REQUEST_PARAMS.getResultInfo());
            return resp;
        }
        String ffpId = verChannel.getFfpId();
        String loginKeyInfo = verChannel.getLoginKeyInfo();
        // 认证信息认证
        if (!this.checkKeyInfo(ffpId, loginKeyInfo, channelCode)) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("信息检验失败！");
            return resp;
        }
        resp.setObjData(addSupportVerChannel(verChannel.getVerType()));
        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
        resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
        return resp;
    }



    /**
     * 易宝用，通过ID生成loginkeyinfo
     *
     * @param req
     * @param request
     * @return
     */
    @RequestMapping(value = "checkLoginKeyInfo", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @ApiOperation(value = "验证加密串", notes = "验证加密串")
    public BaseResp<Boolean> genLoginKeyInfo(@RequestBody BaseReq<CheckLoginKeyInfoReq> req, HttpServletRequest request) {
        BaseResp<Boolean> resp = new BaseResp<>();
        String ip = this.getClientIP(request);
        String reqId = StringUtil.newGUID() + request.getRequestURI();
        try {
            //校验参数
            this.checkRequest(req);
            boolean flag = this.checkKeyInfo(req.getRequest().getMemberId() + "",
                    req.getRequest().getLoginKeyInfo(), req.getChannelCode());
            resp.setObjData(flag);
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
        } catch (Exception e) {
            this.logError(resp, reqId, ip, req, e);
        }
        return resp;
    }

    /**
     * 验证码登录请求类
     *
     * @param request
     * @param req
     * @return
     */
    private PtApiCRMRequest<CaptchaLoginRequest> buildCaptchaLogin(HttpServletRequest
                                                                           request, BaseReq<LoginInfo> req) {
        LoginInfo loginInfo = req.getRequest();
        CaptchaLoginRequest captchaLoginRequest = new CaptchaLoginRequest();
        captchaLoginRequest.setCaptcha(loginInfo.getVeriCode());
        captchaLoginRequest.setChannelValue(PhoneUtil.formatMobile(loginInfo.getCountryCode(), loginInfo.getMobileNum()));
        captchaLoginRequest.setRegisterSource(req.getPlatformInfo());
        captchaLoginRequest.setSendChannel(1);
        Header header = buildHeader(request, "", "");

        PtApiCRMRequest ptApiCRMRequest = new PtApiCRMRequest();
        ptApiCRMRequest.setHeader(header);
        ptApiCRMRequest.setChannel(req.getChannelCode());
        ptApiCRMRequest.setChannelPwd(getClientPwd(req.getChannelCode()));
        ptApiCRMRequest.setData(captchaLoginRequest);
        return ptApiCRMRequest;
    }

    /**
     * 账号密码登录请求类
     *
     * @param request
     * @param channelCode
     * @param userName
     * @param password
     * @return
     */
    private PtApiCRMRequest<PtLoginRequest> buildLoginReq(HttpServletRequest request, String channelCode, String
            userName, String password) {
        PtLoginRequest ptLoginRequest = new PtLoginRequest();
        ptLoginRequest.setUserName(userName);
        ptLoginRequest.setPassword(password);
        Header header = buildHeader(request, "-1", "");

        PtApiCRMRequest ptApiCRMRequest = new PtApiCRMRequest();
        ptApiCRMRequest.setHeader(header);
        ptApiCRMRequest.setChannel(channelCode);
        ptApiCRMRequest.setChannelPwd(getClientPwd(channelCode));
        ptApiCRMRequest.setData(ptLoginRequest);
        return ptApiCRMRequest;
    }

    /**
     * 福卡注册请求
     *
     * @param request
     * @param req
     * @return
     */
    private PtApiCRMRequest<PtRegisterRequest> buildRegisterReq(HttpServletRequest
                                                                        request, BaseReq<RegisterReq> req, BaseResp resp) {
        PtRegisterRequest target = new PtRegisterRequest();
        RegisterReq source = req.getRequest();
        BeanUtils.copyProperties(source, target);
        //中文姓
        String cLastName = source.getcLastName();
        //中文名
        String cFirstName = source.getcFirstName();
        target.setCLastName(cLastName);
        target.setCFirstName(cFirstName);
        target.setELastName(source.geteLastName());
        target.setEFirstName(source.geteFirstName());
        target.setMobile(PhoneUtil.formatMobile(source.getCountryCode(), source.getMobile()));
        //身份证类型的根据证件判断下基本信息
        if (CertificateTypeEnum.ID_CARD.geteName().equals(source.getCertificateType())) {
            Pattern pattern = Pattern.compile(PatternCommon.ID_NUMBER);
            Matcher matcher = pattern.matcher(source.getCertificateNumber());
            if (matcher.matches()) {
                String birthDate = CertUtil.certNoToDate(source.getCertificateNumber());
                if (StringUtil.isNullOrEmpty(birthDate)) {
                    resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                    resp.setResultInfo(WRONG_IDCARD);
                    return null;
                }
                Date birDate = DateUtils.toDate(birthDate, DateUtils.YYYY_MM_DD_PATTERN);
                target.setBirthday(birDate.getTime());
                String sex = CertUtil.checkSex(source.getCertificateNumber());
                target.setSex(sex);
                target.setSalutationCode("M".equals(sex) ? SalutationEnum.MR.geteName() : SalutationEnum.MS.geteName());
                if (StringUtil.isNullOrEmpty(cLastName) || StringUtil.isNullOrEmpty(cFirstName)) {
                    resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                    resp.setResultInfo("请输入中文姓名！");
                    return null;
                }
            } else {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo(WRONG_IDCARD);
                return null;
            }
        }
        //其他证件类型性别未知
        else {
            if (StringUtil.isNullOrEmpty(source.geteLastName()) || StringUtil.isNullOrEmpty(source.geteFirstName())) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo("请输入英文姓名！");
                return null;
            }
            //护照
            boolean isPassPort = CertificateTypeEnum.PASSPORT.geteName().equals(source.getCertificateType());
            boolean isOtherCert = CertificateTypeEnum.OTHER.geteName().equals(source.getCertificateType());
            boolean isTaiwanMtp = CertificateTypeEnum.TAIWAN_MTP.geteName().equals(source.getCertificateType());
            boolean isHKMacaoMtp = CertificateTypeEnum.HK_MACAO_MTP.geteName().equals(source.getCertificateType());
            boolean isForeignerIdCard = CertificateTypeEnum.FOREIGNER_ID_CARD.geteName().equals(source.getCertificateType());
            if (isPassPort || isOtherCert || isTaiwanMtp || isHKMacaoMtp || isForeignerIdCard) {
                target.setSex(StringUtil.isNullOrEmpty(source.getSex()) ? SexEnum.UNKNOWN.eName : source.getSex());
                if (!StringUtil.isNullOrEmpty(source.getBirthDate())) {
                    Date birthDate = DateUtils.toDate(source.getBirthDate(), DateUtils.YYYY_MM_DD_PATTERN);
                    if (birthDate == null) {
                        resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                        resp.setResultInfo("出生日期格式：yyyy-MM-dd！");
                        return null;
                    }
                    target.setBirthday(birthDate.getTime());
                }
            }
        }
        target.setSubmitDate(System.currentTimeMillis());
        target.setValidateMode(ValidateModeEnum.PHONE.validMethod);
        PtApiCRMRequest<PtRegisterRequest> ptApiCRMRequest = new PtApiCRMRequest();
        Header header = buildHeader(request, "-1", "");
        ptApiCRMRequest.setHeader(header);
        ptApiCRMRequest.setChannel(req.getChannelCode());
        ptApiCRMRequest.setChannelPwd(getClientPwd(req.getChannelCode()));
        ptApiCRMRequest.setData(target);
        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
        resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
        return ptApiCRMRequest;
    }

    /**
     * 用于账号密码登录
     */
    private void memberLoginFun(HttpServletRequest request, PtApiCRMRequest<PtLoginRequest> ptApiRequest, String
            reqId, String ip, String clientVer, String channelCode, String deviceId, String pushNum, String
                                        systemInfo, String typeStr, BaseResp resp, String platFormInfo, String mobile, String blackBox) {
        PtCRMResponse<PtLoginResponse> ptCRMResponse = memberService.memberLogin(ptApiRequest);
        String headChannelCode = request.getHeader(HandlerConstants.TOKEN_CHANNELCODE);
        String blackBoxFrom = request.getHeader(HandlerConstants.BLACKBOX_FROM);
        if (ptCRMResponse.getCode() == 0) {  //登录成功
            PtLoginResponse captchaLoginResponse = ptCRMResponse.getData();
            LoginResponse loginInfo = new LoginResponse();
            loginInfo.setToken(captchaLoginResponse.getLoginInfo().getToken());
            loginInfo.setExpiryTime(captchaLoginResponse.getLoginInfo().getExpiryTime());
            MemberLoginResponse memberLoginResponse = formatLoginResp(captchaLoginResponse.getMemberInfo(), request, channelCode);
            this.chkDayVisit(ip, LOGIN_SOURCE, "");//IP限制
            this.chkDayVisit(ptApiRequest.getData().getUserName(), LOGIN_SOURCE, "");//账号登录限制
            if (!ptApiRequest.getData().getUserName().equals(memberLoginResponse.getMemberID())) {
                this.chkDayVisit(memberLoginResponse.getMemberID(), LOGIN_SOURCE, "");//卡号登录限制
            }
            if ("Y".equals(handConfig.getUseTongDun()) && !ChannelCodeEnum.WXAPP.getChannelCode().equals(headChannelCode)) {
                if (FraudApiInvoker.needRiskControl(handConfig.getFraudEscapeConfig(), 0, ip)) {
                    //验证同盾
                    FraudApiResponse fraudApiResponse = FraudApiInvoker.loginRiskControl(headChannelCode, platFormInfo, memberLoginResponse.getMemberID(), blackBox, blackBoxFrom, LoginTypeEnum.PASSWORD.getCode(), mobile, ip, clientVer);
                    if (fraudApiResponse.getSuccess() && fraudApiResponse.getFinal_decision().equalsIgnoreCase(FinalDecisionEnum.REJECT.getCode())) {
                        resp.setResultCode(WSEnum.TONGDUN_FAIL_LOGIN.getResultCode());
                        resp.setResultInfo(WSEnum.TONGDUN_FAIL_LOGIN.getResultInfo());
                    } else {
                        memberLoginInfo(reqId, headChannelCode, ip, clientVer, deviceId, pushNum, systemInfo, typeStr, resp, captchaLoginResponse, loginInfo, memberLoginResponse);
                    }
                } else {
                    memberLoginInfo(reqId, headChannelCode, ip, clientVer, deviceId, pushNum, systemInfo, typeStr, resp, captchaLoginResponse, loginInfo, memberLoginResponse);
                }
            } else {
                memberLoginInfo(reqId, headChannelCode, ip, clientVer, deviceId, pushNum, systemInfo, typeStr, resp, captchaLoginResponse, loginInfo, memberLoginResponse);
            }
        } else if (ptCRMResponse.getCode() == 103004 || ptCRMResponse.getCode() == 103003) { //用户名或密码错误  此时需要记录错误次数
            this.chkDayVisit(ip, LOGIN_ERR, "");//IP限制
            this.chkDayVisit(ptApiRequest.getData().getUserName(), LOGIN_ERR, "memberLogin");//账号登录限制
            String dataStr = DateUtils.dateToString(new Date(), DATE_FORMAT);
            String key = dataStr + ptApiRequest.getData().getUserName() + "_" + LOGIN_ERR + "memberLogin";
            String visitCode = apiRedisService.getData(key);
            int limitTimes = getLimitTimesBySource(LOGIN_ERR);
            int num = Integer.parseInt(visitCode == null ? "5" : visitCode);
            int remainderNum = limitTimes - num;
            String resultInfo = "";
            if (remainderNum <= 0) {
                resultInfo = "密码错误次数今日已达上限，可明日再次尝试；若有疑问可咨询95520";
            } else {
                resultInfo = "密码错误，您还有" + remainderNum + "次机会，输入错误账户将被锁定";
            }
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(resultInfo);
        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(ptCRMResponse.getMsg());
        }
    }

    private void memberLoginInfo(String reqId, String headChannelCode, String ip, String clientVer, String
            deviceId, String pushNum, String systemInfo, String typeStr, BaseResp resp, PtLoginResponse
                                         captchaLoginResponse, LoginResponse loginInfo, MemberLoginResponse memberLoginResponse) {
        if (WSEnum.SUCCESS.getResultCode().equals(memberLoginResponse.getResultCode())) {
            memberLoginResponse.setToken(captchaLoginResponse.getLoginInfo().getToken());
            memberLoginResponse.setExpiryTime(captchaLoginResponse.getLoginInfo().getExpiryTime());
            loginInfo.setMemberLoginResponse(memberLoginResponse);
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
            resp.setObjData(loginInfo);
            //保存登陆人员设备信息 异步调用
            if ("Y".equals(handConfig.getSaveDeviceInfoFlag())) {
                if (ChannelCodeEnum.MOBILE.getChannelCode().equalsIgnoreCase(headChannelCode) && loginInfo.getMemberLoginResponse() != null && !StringUtil.isNullOrEmpty(deviceId)) {
                    try {
                        AppCustomer appCustomer = createAppCustomer(loginInfo.getMemberLoginResponse(), deviceId);
                        appCustomer.setClientVersion(clientVer);
                        DeviceInfo device = new DeviceInfo(deviceId, systemInfo, pushNum, typeStr, ip, appCustomer.getFfp_id() + "", appCustomer.getFfp_card_no());
                        device.setClientVersion(clientVer);
                        //  2017/12/22利用线程池
                        taskExecutor.execute(new DeviceThread(reqId, appCustomer, device));
                    } catch (Exception e) {
                        log.error(EQUIPMENT_SAVE_ERR, e);
                    }
                }
            }
        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(memberLoginResponse.getErrorInfo());
        }
    }

    /**
     * 针对登录统一的返回结果构造 老的结构数据
     */
    private MemberLoginResponse formatLoginResp(MemberSummarySoaModel memberSummarySoaModel, HttpServletRequest
            request, String channelCode) {
        return formatLoginResp(memberSummarySoaModel.getMemberId(), memberSummarySoaModel.getMemberCardNo(), request, channelCode);
    }

    private MemberLoginResponse formatLoginResp(Long memberId, String memberCardNo, HttpServletRequest
            request, String channelCode) {
        MemberLoginResponse resp = new MemberLoginResponse();
        String[] items = {MemberDetailRequestItemsEnum.BASICINFO.eName, MemberDetailRequestItemsEnum.STATEINFO.eName, MemberDetailRequestItemsEnum.CERTIFICATEINFO.eName, MemberDetailRequestItemsEnum.CONTACTINFO.eName, MemberDetailRequestItemsEnum.ADDRESSINFOS.eName};
        PtApiCRMRequest<PtMemberDetailRequest> ptApiRequest = buildMemberDetailReq(memberCardNo, String.valueOf(memberId), request, channelCode, items);
        PtCRMResponse<PtMemberDetail> ptCRMResponse = memberService.memberDetail(ptApiRequest);
        if (ptCRMResponse.isIsSuccess()) {
            if (ptCRMResponse.getCode() == 0) {
                PtMemberDetail detail = ptCRMResponse.getData();
                resp.setId(memberId);
                resp.setMemberID(memberCardNo);
                dealBasicInfo(detail.getBasicInfo(), resp);
                resp.setLevelName(MemberLevelEnum.findLevelNameByLevelCode(detail.getStateInfo().getMemberLevelCode()));
                resp.setMemberLevelCode(detail.getStateInfo().getMemberLevelCode());
                resp.setMemberStatusCode(detail.getStateInfo().getMemberStatus());
                resp.setBalanceOfMileage(0);
                dealCertInfos(detail.getCertificateInfo(), resp);
                dealContactInfo(detail.getContactInfo(), resp);
                dealAddressInfos(detail.getAddressInfos(), resp);
                String key = HandlerConstants.W_CHANNEL_CODE.equals(channelCode) ? HandlerConstants.USER_INFO_KEY : HandlerConstants.USER_INFO_KEY_M;
                String infoIdKey = EncoderHandler.encode("MD5", String.valueOf(resp.getId()) + key).toUpperCase();
                resp.setLoginKeyInfo(infoIdKey);
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo(ptCRMResponse.getMsg());
            }
        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo(ptCRMResponse.getMsg());
        }
        return resp;
    }

    /**
     * 会员详情请求类
     * 基本信息 状态信息 证件信息  联系信息 地址信息
     *
     * @param cardNo
     * @param ffpId
     * @param request
     * @param channelCode
     * @param items       需要的请求信息
     * @return
     */
    private PtApiCRMRequest<PtMemberDetailRequest> buildMemberDetailReq(String cardNo, String
            ffpId, HttpServletRequest request, String channelCode, String[] items) {
        PtMemberDetailRequest ptMemberDetailRequest = new PtMemberDetailRequest();
        ptMemberDetailRequest.setCardNO(cardNo);
        ptMemberDetailRequest.setRequestItems(items);
        Header header = buildHeader(request, ffpId, "");

        PtApiCRMRequest ptApiCRMRequest = new PtApiCRMRequest();
        ptApiCRMRequest.setHeader(header);
        ptApiCRMRequest.setChannel(channelCode);
        ptApiCRMRequest.setChannelPwd(getClientPwd(channelCode));
        ptApiCRMRequest.setData(ptMemberDetailRequest);
        return ptApiCRMRequest;
    }


    /**
     * 添加证件请求类
     *
     * @param req
     * @param resp
     * @return
     */
    private PtAddMemberCertificateRequest buildPtAddMemberCertificateRequest
    (BaseReq<MemberApplyReq> req, BaseResp resp) {
        MemberApplyReq memberApplyReq = req.getRequest();
        PtAddMemberCertificateRequest ptAddMemberCertificateRequest = new PtAddMemberCertificateRequest();
        MemberCertificateSoaModel memberCertificateSoaModel = new MemberCertificateSoaModel();
        CertificateTypeEnum cert = CertificateTypeEnum.checkName(memberApplyReq.getCertificateType());
        if (cert == null) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("证件类型不合法");
            return null;
        }
        memberCertificateSoaModel.setCertificateType(cert.getCode());
        memberCertificateSoaModel.setCertificateNumber(memberApplyReq.getCertificateNumber());
        ptAddMemberCertificateRequest.setCertificateInfo(memberCertificateSoaModel);
        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
        resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
        return ptAddMemberCertificateRequest;
    }

    /**
     * 修改客户信息
     *
     * @param req
     * @param resp
     * @return
     */
    private PtModifyCustomerInfoRequest buildPtModifyCustomerInfoRequest(BaseReq<MemberApplyReq> req, BaseResp resp) {
        MemberApplyReq memberApplyReq = req.getRequest();
        PtModifyCustomerInfoRequest ptModifyCustomerInfoRequest = new PtModifyCustomerInfoRequest();
        BeanUtils.copyProperties(memberApplyReq, ptModifyCustomerInfoRequest);
        //处理英文姓名大小写问题
        if (StringUtils.isNotBlank(memberApplyReq.getELastName())) {
            ptModifyCustomerInfoRequest.setELastName(memberApplyReq.getELastName().toUpperCase());
        }
        if (StringUtils.isNotBlank(memberApplyReq.getEFirstName())) {
            ptModifyCustomerInfoRequest.setEFirstName(memberApplyReq.getEFirstName().toUpperCase());
        }
        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
        resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
        return ptModifyCustomerInfoRequest;
    }

    /**
     * 会员基本信息处理
     *
     * @param basicInfo
     * @param resp
     */
    private void dealBasicInfo(MemberBasicInfoSoaModel basicInfo, MemberLoginResponse resp) {
        resp.setcLastName(basicInfo.getCLastName());
        resp.setcFirstName(basicInfo.getCFirstName());
        resp.seteLastName(basicInfo.getELastName());
        resp.seteFirstName(basicInfo.getEFirstName());
        resp.setName(basicInfo.getCLastName() + basicInfo.getCFirstName());
        SexEnum sex = SexEnum.formatSexCode(basicInfo.getSex());
        resp.setSex(sex == null ? "" : sex.showCode);
        resp.setBirthday(DateUtils.timeStampToDateStr(basicInfo.getBirthDay()));
        SalutationEnum salution = SalutationEnum.formatSalutationCode(basicInfo.getMemberSalutation());
        resp.setTitle(salution == null ? "" : salution.getDesc());
        resp.setHeadImageUrl(basicInfo.getHeadImageUrl());
    }

    /**
     * 证件信息处理
     *
     * @param certList
     * @param resp
     */
    private void dealCertInfos(List<MemberCertificateSoaModelV2> certList, MemberLoginResponse resp) {
        if (StringUtil.isNullOrEmpty(certList)) {
            return;
        }
        List<CustomerCertificateInfo> certListNew = new ArrayList<>();
        int i = 0;
        for (MemberCertificateSoaModelV2 cert : certList) {
            CustomerCertificateInfo custCert = new CustomerCertificateInfo();
            CertificateTypeEnum cer = CertificateTypeEnum.checkType(cert.getCertificateType());
            if (cer != null) {
                custCert.setCertType(cer.geteName());
                custCert.setCertNumber(cert.getCertificateNumber());
                if (i == 0) {
                    resp.setCertType(cer.geteName());
                    resp.setCertNumber(cert.getCertificateNumber());
                    i++;
                }
                certListNew.add(custCert);
            }
        }
        resp.setCustomerCertificateInfos(certListNew);
    }

    //联系方式处理
    private void dealContactInfo(List<MemberContactSoaModel> contactList, MemberLoginResponse resp) {
        if (StringUtil.isNullOrEmpty(contactList)) {
            return;
        }
        for (MemberContactSoaModel con : contactList) {
            if (con.getContactType() == 1) {//手机号
                resp.setMemberTel(con.getContactNumber());
            } else if (con.getContactType() == 5) { //邮箱
                resp.setMemberEmail(con.getContactNumber());
            }
        }
    }

    //处理地址信息
    private void dealAddressInfos(List<MemberAddressSoaModel> addrList, MemberLoginResponse resp) {
        if (StringUtil.isNullOrEmpty(addrList)) {
            return;
        }
        MemberAddressSoaModel addr = addrList.get(0);
        resp.setCountryCode(addr.getCountryCode());
        resp.setProvinceCode(addr.getProvinceCode());
        resp.setAddressCityCode(addr.getCityCode());
        resp.setAddressContent(addr.getAddress());
        resp.setPostCode(addr.getPostCode());
    }

    /**
     * 处理第三方账号信息
     *
     * @param thirdpartyInfoList
     * @param detail
     */
    private void dealThirdPartAccount(List<MemberThirdpartySoaModel> thirdpartyInfoList, MemberDetailResp detail) {
        if (StringUtil.isNullOrEmpty(thirdpartyInfoList)) {
            return;
        }
        for (MemberThirdpartySoaModel memberThirdpartySoaModel : thirdpartyInfoList) {
            if (ThirdPartyTypeEnum.WECHAT.getEnName().equals(memberThirdpartySoaModel.getThirdpartyType())) {
                detail.setWeixinthird(memberThirdpartySoaModel);
            }
            if (ThirdPartyTypeEnum.ALIPAY.getEnName().equals(memberThirdpartySoaModel.getThirdpartyType())) {
                detail.setAliPaythird(memberThirdpartySoaModel);
            }
        }
    }

    /**
     * 筛选手机号
     *
     * @param contactSoaModelList
     * @return
     */
    private String filterContactInfo(List<MemberContactSoaModel> contactSoaModelList, int type) {
        if (StringUtil.isNullOrEmpty(contactSoaModelList)) {
            return null;
        }
        for (MemberContactSoaModel contactSoaModel : contactSoaModelList) {
            if (contactSoaModel.getContactType() == type) {
                return contactSoaModel.getContactNumber();
            }
        }
        return null;
    }

    /**
     * 筛选手机号
     *
     * @param contactSoaModelList
     * @return
     */
    private MemberContactSoaModel toMemberContactSoaModel(List<MemberContactSoaModel> contactSoaModelList,
                                                          int type) {
        if (StringUtil.isNullOrEmpty(contactSoaModelList)) {
            return null;
        }
        for (MemberContactSoaModel contactSoaModel : contactSoaModelList) {
            if (contactSoaModel.getContactType() == type) {
                return contactSoaModel;
            }
        }
        return null;
    }

    /**
     * 筛选证件信息
     *
     * @param certificateInfoList
     * @param type
     * @return
     */
    private MemberCertificateSoaModelV2 filterCert(List<MemberCertificateSoaModelV2> certificateInfoList,
                                                   int type) {
        if (StringUtil.isNullOrEmpty(certificateInfoList)) {
            return null;
        } else {
            for (MemberCertificateSoaModelV2 c : certificateInfoList) {
                if (type == (c.getCertificateType())) {
                    return c;
                }
            }
        }
        return null;
    }

    /**
     * @param memberInfo
     * @param deviceId
     * @return
     */
    //构建人员设备信息
    private AppCustomer createAppCustomer(MemberLoginResponse memberInfo, String deviceId) {
        AppCustomer appCustomer = new AppCustomer(memberInfo.getMemberID(), memberInfo.getId(), memberInfo.getName());
        appCustomer.setLast_deviceId(deviceId);
        return appCustomer;
    }

    class DeviceThread implements Runnable {
        private String reqId;
        private AppCustomer appCustomer;
        private DeviceInfo deviceInfo;

        public DeviceThread(AppCustomer appCustomer, DeviceInfo deviceInfo) {
            this.appCustomer = appCustomer;
            this.deviceInfo = deviceInfo;
        }

        public DeviceThread(String reqId, AppCustomer appCustomer, DeviceInfo deviceInfo) {
            this.reqId = reqId;
            this.appCustomer = appCustomer;
            this.deviceInfo = deviceInfo;
        }

        public AppCustomer getAppCustomer() {
            return appCustomer;
        }

        public void setAppCustomer(AppCustomer appCustomer) {
            this.appCustomer = appCustomer;
        }

        public DeviceInfo getDeviceInfo() {
            return deviceInfo;
        }

        public void setDeviceInfo(DeviceInfo deviceInfo) {
            this.deviceInfo = deviceInfo;
        }

        public String getReqId() {
            return reqId;
        }

        public void setReqId(String reqId) {
            this.reqId = reqId;
        }

        @Override
        public void run() {
            String logStr = JsonUtil.objectToJson(appCustomer);
            log.info("{}利用线程{}mongo保存设备用户关联{}", reqId, Thread.currentThread().getName(), logStr);
            //用户设备信息
            appCustomerServiceMongo.addOrUpdateAppCustomer(appCustomer);
            //设备信息
            deviceServiceMongo.addDevice(deviceInfo);
            log.info("{}利用线程{}mongo保存操作完毕", reqId, Thread.currentThread().getName());

        }
    }

    class RealInfoThread implements Runnable {

        private String ffpCardNo;
        private String verifyStatusDesc;
        private String verifyStatusCode;
        private String verifyChannel;
        private String verifyChannelDesc;

        public RealInfoThread(String ffpCardNo, String verifyStatusDesc, String verifyStatusCode, String verifyChannel, String verifyChannelDesc) {
            this.ffpCardNo = ffpCardNo;
            this.verifyStatusDesc = verifyStatusDesc;
            this.verifyStatusCode = verifyStatusCode;
            this.verifyChannel = verifyChannel;
            this.verifyChannelDesc = verifyChannelDesc;
        }

        /**
         * When an object implementing interface <code>Runnable</code> is used
         * to create a thread, starting the thread causes the object's
         * <code>run</code> method to be called in that separately executing
         * thread.
         * <p>
         * The general contract of the method <code>run</code> is that it may
         * take any action whatsoever.
         *
         * @see Thread#run()
         */
        @Override
        public void run() {
            //默认缓存认证实名状态10分钟
            Map map = new HashMap();
            map.put("ffpCardNo", ffpCardNo);
            map.put("verifyStatusDesc", verifyStatusDesc);
            map.put("verifyStatusCode", verifyStatusCode);
            map.put("verifyChannel", verifyChannel);
            map.put("verifyChannelDesc", verifyChannelDesc);
            apiRedisService.setHashData(new StringBuilder(RedisKeyConfig.MEMBER_REAL).append(ffpCardNo).toString(), map, 60 * 10L);
        }
    }


    /**
     * 校验照片和身份证号与姓名是否匹配
     */
    private IdNameRes checkIdPhoto(Map<String, Object> postStr) {
        long time = System.currentTimeMillis();
        String param = JsonUtil.objectToJson(postStr);
        log.info("当前时间戳：{}，调用人脸比对接口请求参数为：{}", time, param);
        String date = new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("mvTrackId", date + "_IdNamePhotoCheck_jixiang_" +
                UUID.randomUUID().toString().replace("-", "").substring(0, 8));
        String result = HttpsUtil.doHttpsPost(HandlerConstants.ID_NAME_CHECK_URL, param, "UTF-8", headerMap);
        log.info("当前时间戳：{}，调用人脸比对接口返回结果为：{}", time, result);
        IdNameRes resp = (IdNameRes) JsonUtil.jsonToBean(result, IdNameRes.class);
        return resp;
    }

    //查询证件当前所绑定的账号是否以实名，已实名账号不支持合并 0未实名 1已实名通过 2查询失败
    private Integer getCertBindAccountRealNameState(String certificateNumber, HttpServletRequest request, String
            channelCode) {
        try {
            //通过证件号查询所绑定账号信息
            String certType = String.valueOf(CertificateTypeEnum.ID_CARD.getCode());
            //查询会员卡号，所以只查询会员基本信息
            String[] items = {MemberDetailRequestItemsEnum.BASICINFO.eName};
            //构建查询证件号所绑定会员的ffpCardNo
            PtApiCRMRequest<PtMemberDetailRequest> ptApiCRMRequest = buildMemberQueryCardNoReq(certType, certificateNumber, "", request, channelCode, items);
            PtCRMResponse<PtMemberDetail> crmResponse = memberService.memberDetail(ptApiCRMRequest);
            if (crmResponse.getCode() != 0) {
                return 2;
            }
            //查询所绑定账号是否实名
            MemberBasicInfoSoaModel basicInfo = crmResponse.getData().getBasicInfo();
            PtRealNameReq ptApiRequest = new PtRealNameReq();
            ptApiRequest.setID(String.valueOf(basicInfo.getMemberId()));
            ptApiRequest.setClientCode(channelCode);
            ptApiRequest.setSignature(EncoderHandler.encodeByMD5(channelCode + String.valueOf(basicInfo.getMemberId()) + getClientPwd(channelCode)).toUpperCase());
            PtRealNameResp realNameResp = memberService.realNameState(ptApiRequest);
            //存在认证审核记录的000   用户未进行过实名认证操作的001
            if ("000".equals(realNameResp.getStatusCode())) {
                VerifyStatusEnum verifyStatusEnum = VerifyStatusEnum.formatVerifyStatus(realNameResp.getVerifyStatus());
                if (verifyStatusEnum != null && VerifyStatusEnum.PASS.code.equals(verifyStatusEnum.code)) {
                    return 1;
                } else {
                    return 0;
                }
            } else if ("001".equals(realNameResp.getStatusCode())) {
                return 0;
            } else {
                return 2;
            }
        } catch (Exception e) {
            return 2;
        }
    }

    /**
     * 构建用证件信息查询会员信息请求
     *
     * @param certificateType   证件类型
     * @param certificateNumber 证件号
     * @param ffpId
     * @param request
     * @param channelCode
     * @param items             要查询的信息
     * @return
     */
    private PtApiCRMRequest<PtMemberDetailRequest> buildMemberQueryCardNoReq(String certificateType, String
            certificateNumber, String ffpId, HttpServletRequest request, String channelCode, String[] items) {
        PtMemberDetailRequest ptMemberDetailRequest = new PtMemberDetailRequest();
        ptMemberDetailRequest.setCertificateType(certificateType);
        ptMemberDetailRequest.setCertificateNumber(certificateNumber);
        ptMemberDetailRequest.setRequestItems(items);
        Header header = buildHeader(request, ffpId, "");
        PtApiCRMRequest ptApiCRMRequest = new PtApiCRMRequest();
        ptApiCRMRequest.setHeader(header);
        ptApiCRMRequest.setChannel(channelCode);
        ptApiCRMRequest.setChannelPwd(getClientPwd(channelCode));
        ptApiCRMRequest.setData(ptMemberDetailRequest);
        return ptApiCRMRequest;
    }

    @RequestMapping(value = "catchCouponUseDetail", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @ApiOperation(value = "查询优惠券/权益券使用规则", notes = "查询优惠券/权益券使用规则")
    public BaseResp<CouponUseDetailResponse> catchCouponUseDetail(@RequestBody @Validated BaseReq<CouponUseDetailRequest> req, BindingResult bindingResult, HttpServletRequest request) {
        BaseResp<CouponUseDetailResponse> response = new BaseResp<>();
        String ip = this.getClientIP(request);
        String reqId = StringUtil.newGUID() + request.getRequestURI();
        String logStr = JsonUtil.objectToJson(req);
        saveReqInfo(SERVICE_NAME, reqId, ip, logStr);
        try {
            if (bindingResult.hasErrors()) {
                throw new RequestParamErrorException(bindingResult.getAllErrors().get(0).getDefaultMessage());
            }
            CouponUseDetailRequest couponRequest = req.getRequest();
            String userNo = getChannelInfo(req.getChannelCode(), "10");
            // 认证信息认证
            if (!this.checkKeyInfo(couponRequest.getFfpId(), couponRequest.getLoginKeyInfo(), req.getChannelCode())) {
                throw new CheckErrorException(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            }
            CouponUseDetailResponse couponUseDetailResponse = new CouponUseDetailResponse();
            //券号
            couponUseDetailResponse.setCouponId(couponRequest.getCouponId());
            //------------优惠券处理开始-------------
            if (RightsProductTypeEnum.COUPON.getCode() == couponRequest.getCouponType()) {
                CouponActivityReq couponActivityReq = new CouponActivityReq();
                couponActivityReq.setActivityNo(couponRequest.getCouponId());
                couponActivityReq.setChannelCode(HandlerConstants.CRM_CHANNEL_CODE);
                couponActivityReq.setVersion(HandlerConstants.VERSION);
                couponActivityReq.setUserNo(userNo);
                HttpResult httpResult = doPostClient(couponActivityReq,
                        HandlerConstants.URL_FARE_API + HandlerConstants.SUB_QUERY_COUPON_ACTIVITY);
                if (httpResult.isResult()) {
                    CouponActivityResp couponActivityResp = (CouponActivityResp)
                            JsonUtil.jsonToBean(httpResult.getResponse(), new TypeToken<CouponActivityResp>() {
                            }.getType());
                    if ("1001".equals(couponActivityResp.getResultCode())) {
                        List<CouponActivityInfo> couponActivityList = couponActivityResp.getCouponActivityList();
                        if (!CollectionUtils.isEmpty(couponActivityList)) {
                            CouponActivityInfo couponActivityInfo = couponActivityList.get(0);
                            //适用范围
                            couponUseDetailResponse.setApplicableScope(StringUtil.isNullOrEmpty(couponActivityInfo.getRemark()) ? handConfig.getApplyRange() : couponActivityInfo.getRemark());
                            //可使用渠道
                            String channelNm = couponActivityList.get(0).getChannelNm();
                            if (!StringUtil.isNullOrEmpty(channelNm)) {
                                String[] channelNms = channelNm.split(",");
                                for (String c : channelNms) {
                                    UsingChannelsEnum usingChannelsEnum = UsingChannelsEnum.checkEnum(c);
                                    if (usingChannelsEnum != null) {
                                        channelNm = channelNm.replaceAll(usingChannelsEnum.getUsingChannel(), usingChannelsEnum.getDesc());
                                    }
                                }
                            } else {
                                channelNm = handConfig.getUsingChannels();
                            }
                            couponUseDetailResponse.setUsedChannels(channelNm);
                            response.setObjData(couponUseDetailResponse);
                            response.setResultCode(WSEnum.SUCCESS.getResultCode());
                            response.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                            return response;
                        } else {
                            couponUseDetailResponse.setUsedChannels(handConfig.getUsingChannels());
                            couponUseDetailResponse.setApplicableScope(handConfig.getApplyRange());
                        }
                    } else {
                        couponUseDetailResponse.setUsedChannels(handConfig.getUsingChannels());
                        couponUseDetailResponse.setApplicableScope(handConfig.getApplyRange());
                    }
                }
                response.setObjData(couponUseDetailResponse);
                response.setResultCode(WSEnum.SUCCESS.getResultCode());
                response.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                return response;
                //------------优惠券处理结束-------------
            } else {
                //------------权益券处理开始-------------
                couponUseDetailResponse.setCouponId(couponRequest.getCouponId());
                /*CouponActivityReq couponActivityReq = new CouponActivityReq();
                couponActivityReq.setActivityNo(couponRequest.getCouponId());
                couponActivityReq.setChannelCode(HandlerConstants.CRM_CHANNEL_CODE);
                couponActivityReq.setVersion("10");*/
                RightsCouponReq rightsCouponReq = new RightsCouponReq();
                rightsCouponReq.setVersion("10").setChannelCode(HandlerConstants.CRM_CHANNEL_CODE).setActivityNo(couponRequest.getCouponId());
                HttpResult httpResult = doPostClient(rightsCouponReq,
                        HandlerConstants.URL_COUPON_API + HandlerConstants.RIGHTS_COUPON_QUERY);
                if (httpResult.isResult()) {
                    RightsCouponResponse couponActivityResp = (RightsCouponResponse)
                            JsonUtil.jsonToBean(httpResult.getResponse(), new TypeToken<RightsCouponResponse>() {
                            }.getType());
                    if ("10001".equals(couponActivityResp.getResultCode())) {
                        //适用范围
                        couponUseDetailResponse.setApplicableScope(StringUtil.isNullOrEmpty(couponActivityResp.getApplyRange()) ? handConfig.getApplyRange() : couponActivityResp.getApplyRange().replaceAll("<p>", "").replaceAll("</p>", ""));
                        //使用渠道
                        couponUseDetailResponse.setUsedChannels(StringUtil.isNullOrEmpty(couponActivityResp.getUseMode()) ? handConfig.getUsingChannels() : couponActivityResp.getUseMode().replaceAll("<p>", "").replaceAll("</p>", ""));
                    } else {
                        couponUseDetailResponse.setUsedChannels(handConfig.getUsingChannels());
                        couponUseDetailResponse.setApplicableScope(handConfig.getApplyRange());
                    }
                } else {
                    couponUseDetailResponse.setUsedChannels(handConfig.getUsingChannels());
                    couponUseDetailResponse.setApplicableScope(handConfig.getApplyRange());
                }
                //------------权益券处理结束-------------
                response.setObjData(couponUseDetailResponse);
                response.setResultCode(WSEnum.SUCCESS.getResultCode());
                response.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                return response;
            }
        } catch (Exception e) {
            this.logError(response, reqId, ip, req, e);
            response.setResultCode(WSEnum.ERROR.getResultCode());
            response.setResultInfo(WSEnum.ERROR.getResultInfo());
        }
        return response;
    }
}

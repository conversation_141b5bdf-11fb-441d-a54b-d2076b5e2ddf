package com.juneyaoair.mobile.handler.service.impl;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.beust.jcommander.internal.Lists;
import com.google.gson.reflect.TypeToken;
import com.juneyaoair.appenum.ChannelCodeEnum;
import com.juneyaoair.appenum.WSEnum;
import com.juneyaoair.appenum.sendcoupon.SendCouponSceneEnum;
import com.juneyaoair.baseclass.ad.cobrandcreditcard.dto.CoBrandCreditCardAdDTO;
import com.juneyaoair.baseclass.basicsys.request.Antifraud;
import com.juneyaoair.baseclass.basicsys.request.FlightExistDateReqDTO;
import com.juneyaoair.baseclass.basicsys.response.*;
import com.juneyaoair.baseclass.common.request.BaseReq;
import com.juneyaoair.baseclass.common.request.BasicBaseReq;
import com.juneyaoair.baseclass.common.request.RequestData;
import com.juneyaoair.baseclass.common.response.*;
import com.juneyaoair.baseclass.flight.common.CityAirportInfo;
import com.juneyaoair.baseclass.messageV2.response.NoticeResponse;
import com.juneyaoair.baseclass.request.airport.AirPortInfoReqDTO;
import com.juneyaoair.baseclass.request.av.QueryFlightFareReq;
import com.juneyaoair.baseclass.request.console.MessageRequest;
import com.juneyaoair.baseclass.request.console.PictureRequest;
import com.juneyaoair.baseclass.request.flightDistance.FlightDistanceReq;
import com.juneyaoair.baseclass.request.flightDistance.Segment;
import com.juneyaoair.baseclass.request.flightDynamic.AttentionFlightParam;
import com.juneyaoair.baseclass.request.manage.AppPictureRequest;
import com.juneyaoair.baseclass.request.premium.basic.ActivityPremiumResp;
import com.juneyaoair.baseclass.response.airport.AirPortInfoRespDTO;
import com.juneyaoair.baseclass.response.av.QueryFlightFareResp;
import com.juneyaoair.baseclass.response.av.ThemeFlightDetails;
import com.juneyaoair.baseclass.response.console.Mess;
import com.juneyaoair.baseclass.response.console.MessDetail;
import com.juneyaoair.baseclass.response.flightDistance.FlightDistanceDTO;
import com.juneyaoair.baseclass.response.flightdynamic.FollowAirLineInfo;
import com.juneyaoair.baseclass.response.manage.LoadPic;
import com.juneyaoair.baseclass.sendcoupon.request.AvFareCouponReceiveDTO;
import com.juneyaoair.baseclass.sendcoupon.request.QueryActivityCouponGrantReq;
import com.juneyaoair.baseclass.sendcoupon.request.QueryCouponReceiveCountReq;
import com.juneyaoair.baseclass.sendcoupon.response.ActivityCouponGrantDTO;
import com.juneyaoair.common.annotation.CacheDuration;
import com.juneyaoair.common.dto.base.BaseRequestDTO;
import com.juneyaoair.exception.CommonException;
import com.juneyaoair.exception.NetworkException;
import com.juneyaoair.exception.ServiceException;
import com.juneyaoair.mobile.comm.service.IRedisService;
import com.juneyaoair.mobile.constants.MobileCoreBeanNames;
import com.juneyaoair.mobile.handler.HandlerConstants;
import com.juneyaoair.mobile.handler.bean.message.PushDetailQuery;
import com.juneyaoair.mobile.handler.bean.message.PushDetailUpdateState;
import com.juneyaoair.mobile.handler.bean.mileage.AirlineMileageParam;
import com.juneyaoair.mobile.handler.bean.mileage.AirlineMileageResult;
import com.juneyaoair.mobile.handler.comm.RedisKeyConfig;
import com.juneyaoair.mobile.handler.config.bean.HandConfig;
import com.juneyaoair.mobile.handler.service.IBasicService;
import com.juneyaoair.mobile.handler.service.UpdateAirportNameService;
import com.juneyaoair.mobile.handler.service.bean.country.TCountryDTO;
import com.juneyaoair.mobile.mongo.entity.FlightInfo;
import com.juneyaoair.mobile.mongo.entity.PushDetailInfo;
import com.juneyaoair.mobile.mongo.service.push.IPushService;
import com.juneyaoair.thirdentity.basic.*;
import com.juneyaoair.thirdentity.flight.request.QueryFlight;
import com.juneyaoair.thirdentity.flight.request.QueryFlightByCityAndDate;
import com.juneyaoair.thirdentity.tongdun.response.FraudApiResponse;
import com.juneyaoair.utils.IPUtil;
import com.juneyaoair.utils.StringUtil;
import com.juneyaoair.utils.ZipUtil;
import com.juneyaoair.utils.http.HttpResult;
import com.juneyaoair.utils.http.HttpUtil;
import com.juneyaoair.utils.http.HttpsUtil;
import com.juneyaoair.utils.json.JsonMapper;
import com.juneyaoair.utils.json.JsonUtil;
import com.juneyaoair.utils.util.DateUtils;
import com.juneyaoair.utils.util.MdcUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.Type;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2019/8/8  9:01.
 */
@Service("basicService")
public class BasicService implements IBasicService {
    private Logger log = LoggerFactory.getLogger(this.getClass());
    @Autowired
    private HandConfig handConfig;
    @Resource(name = MobileCoreBeanNames.API_REDIS_SERVICE)
    private IRedisService apiRedisService;
    @Autowired
    private UpdateAirportNameService updateAirportNameService;
    @Autowired
    private IPushService pushService;

    @Resource(name = "mongoTemplate")
    protected MongoTemplate mongoTemplate;

    private static final int READ_TIME = 5000;
    private static final int CONNECT_TIME = 3000;

    @Override
    public List<TCountryDTO> queryCountrys() {
        List<TCountryDTO> tCountryDTOList = new ArrayList<>();
        //先走缓存结果
        String redisResult = apiRedisService.getData(RedisKeyConfig.COMMON_COUNTRYS);
        //缓存中无值时去基础服务查询
        if (StringUtil.isNullOrEmpty(redisResult) || "null".equals(redisResult)) {
            String channelCode = MdcUtils.getChannelCode();
            BasicBaseReq basicReq = new BasicBaseReq(null == channelCode ? "MOBILE" : channelCode, IPUtil.getLocalIp(), HandlerConstants.BASIC_INFO_VERSION);
            String url = HandlerConstants.BASIC_INFO_URL + HandlerConstants.BASIC_INFO_COUNTRYS;
            HttpResult httpResult = HttpUtil.doPostClient(basicReq, url, null, handConfig.getReadTimeout(), handConfig.getConnectTimeout());
            if (httpResult.isResult() && !StringUtil.isNullOrEmpty(httpResult.getResponse())) {
                try {
                    Type type = new TypeToken<BasicBaseResp<List<TCountryDTO>>>() {
                    }.getType();
                    BasicBaseResp<List<TCountryDTO>> basicResp = (BasicBaseResp<List<TCountryDTO>>) JsonUtil.jsonToBean(httpResult.getResponse(), type);
                    if (WSEnum.SUCCESS.getResultCode().equals(basicResp.getResultCode()) && !StringUtil.isNullOrEmpty(basicResp.getResult())) {
                        tCountryDTOList = basicResp.getResult();
                        tCountryDTOList.sort(Comparator.comparing(TCountryDTO::getSequence));
                        apiRedisService.putData(RedisKeyConfig.COMMON_COUNTRYS, JsonUtil.objectToJson(tCountryDTOList), 7 * 24 * 3600L);//默认存放7天
                    }
                } catch (Exception e) {
                    log.error("【基础服务-国家查询】异常信息:{},请求参数:{}", e, JsonUtil.objectToJson(basicReq));
                }
            }
        } else {
            tCountryDTOList = (List<TCountryDTO>) JsonUtil.jsonToList(redisResult, new TypeToken<List<TCountryDTO>>() {
            }.getType());
        }
        return tCountryDTOList;
    }

    @Override
    public TCountryDTO queryCountry(String countryCode) {
        if (StringUtils.isBlank(countryCode)) {
            return null;
        }
        List<TCountryDTO> countryList = queryCountrys();
        if (CollectionUtils.isEmpty(countryList)) {
            return null;
        }
        for (TCountryDTO country : countryList) {
            if (countryCode.equals(country.getCountryCode())) {
                return country;
            }
        }
        return null;
    }

    @Override
    public List<AirLineLabelDTO> queryCacheFlightLine(String depCity, String arrCity, String channelCode, String ip) {
        return queryCacheFlightLine(depCity, arrCity, null, null, channelCode, ip);
    }

    @Override
    public List<AirLineLabelDTO> queryCacheFlightLine(String depCity, String arrCity, String depAirport, String arrAirport, String channelCode, String ip) {
        //先获取缓存，无缓存信息的再发送请求
        String key = RedisKeyConfig.COMMON_DEP_ARR_AIRLINE + depCity + arrCity + (depAirport == null ? "" : depAirport) + (arrAirport == null ? "" : arrAirport);
        String json = apiRedisService.getData(key);
        List<AirLineLabelDTO> airLineLabelDTOList;
        if (StringUtil.isNullOrEmpty(json) || "null".equals(json)) {
            BasicBaseReq<Map<String, Object>> basicBaseReq = new BasicBaseReq<>(HandlerConstants.BASIC_INFO_VERSION, channelCode, ip);
            Map<String, Object> param = new HashMap<>();
            param.put("depCity", depCity);
            param.put("arrCity", arrCity);
            if (!StringUtil.isNullOrEmpty(depAirport)) {
                param.put("depAirport", depAirport);
            }
            if (!StringUtil.isNullOrEmpty(arrAirport)) {
                param.put("arrAirport", arrAirport);
            }
            basicBaseReq.setRequest(param);
            airLineLabelDTOList = queryFlightLine(basicBaseReq);
            if (!StringUtil.isNullOrEmpty(airLineLabelDTOList)) {
                apiRedisService.putData(key, JsonUtil.objectToJson(airLineLabelDTOList), 4 * 3600L);
            } else {
                airLineLabelDTOList = new ArrayList<>();
            }
        } else {
            Type type = new TypeToken<List<AirLineLabelDTO>>() {
            }.getType();
            airLineLabelDTOList = (List<AirLineLabelDTO>) JsonUtil.jsonToList(json, type);
        }
        return airLineLabelDTOList;
    }

    /**
     * 查询航线信信息
     *
     * @param basicReq
     * @return
     */
    private List<AirLineLabelDTO> queryFlightLine(BasicBaseReq basicReq) {
        List<AirLineLabelDTO> airLineLabelDTOList;
        String url = HandlerConstants.BASIC_INFO_URL + HandlerConstants.BASIC_INFO_AIRLINES;
        HttpResult httpResult = HttpUtil.doPostClient(basicReq, url, null, handConfig.getReadTimeout(), handConfig.getConnectTimeout());
        if (httpResult.isResult() && !StringUtil.isNullOrEmpty(httpResult.getResponse())) {
            try {
                Type type = new TypeToken<BasicBaseResp<List<AirLineLabelDTO>>>() {
                }.getType();
                BasicBaseResp<List<AirLineLabelDTO>> basicResp = (BasicBaseResp<List<AirLineLabelDTO>>) JsonUtil.jsonToBean(httpResult.getResponse(), type);
                if (WSEnum.SUCCESS.getResultCode().equals(basicResp.getResultCode())) {
                    if (!StringUtil.isNullOrEmpty(basicResp.getResult())) {
                        airLineLabelDTOList = basicResp.getResult();
                    } else {
                        airLineLabelDTOList = new ArrayList<>();
                    }
                    return airLineLabelDTOList;
                } else {
                    return null;
                }
            } catch (Exception e) {
                log.error("【基础服务-航线查询】异常信息:{},请求参数:{}", e, JsonUtil.objectToJson(basicReq));
                return null;
            }
        } else {
            return null;
        }
    }

    @Override
    public List<AirLineInfoDepCityDto> selectAllAirline() {
        String key = RedisKeyConfig.REDIS_AIRLINE_INFO_HASH;
        List<String> airlineList = apiRedisService.getHashValue(key);
        if (CollectionUtils.isEmpty(airlineList)) {
            Map<String, String> airlineListMap = new HashMap<>();
            String url = HandlerConstants.BASIC_INFO_URL + HandlerConstants.BASIC_API_AIRLINES_QUERY;
            BasicBaseReq basicReq = new BasicBaseReq(HandlerConstants.BASIC_INFO_VERSION, ChannelCodeEnum.MOBILE.getChannelCode(), IPUtil.getLocalIp());
            HttpResult httpResult = HttpUtil.doPostClient(basicReq, url, 8000, 3000);
            if (httpResult.isResult()) {
                Type type = new TypeToken<BasicBaseResp<List<AirLineInfoDepCityDto>>>() {
                }.getType();
                BasicBaseResp<List<AirLineInfoDepCityDto>> basicResp = (BasicBaseResp<List<AirLineInfoDepCityDto>>) JsonUtil.jsonToBean(httpResult.getResponse(), type);
                if (WSEnum.SUCCESS.getResultCode().equals(basicResp.getResultCode())) {
                    List<AirLineInfoDepCityDto> airLineInfoDepCityDtoList = basicResp.getResult();
                    if (!StringUtil.isNullOrEmpty(airLineInfoDepCityDtoList)) {
                        airlineListMap = airLineInfoDepCityDtoList.parallelStream()
                                .collect(Collectors.toMap(AirLineInfoDepCityDto::getCityCode, airLineInfoDepCityDto -> {
                                    CityInfoDto depCity = selectCity(airLineInfoDepCityDto.getCityCode());
                                    if (depCity != null && !StringUtil.isNullOrEmpty(depCity.getCityLabelInfoList())) {
                                        airLineInfoDepCityDto.setCityLabelInfoList(depCity.getCityLabelInfoList());
                                    }
                                    if (!StringUtil.isNullOrEmpty(airLineInfoDepCityDto.getAirline())) {
                                        airLineInfoDepCityDto.getAirline().parallelStream().forEach(airLineInfoArrCityDto -> {
                                            CityInfoDto arrCity = selectCity(airLineInfoArrCityDto.getCityCode());
                                            if (arrCity != null && !StringUtil.isNullOrEmpty(arrCity.getCityLabelInfoList())) {
                                                airLineInfoArrCityDto.setCityLabelInfoList(arrCity.getCityLabelInfoList());
                                            }
                                        });
                                    }
                                    return ZipUtil.zipHex(JsonUtil.objectToJson(airLineInfoDepCityDto));
                                }));
                    }
                    if (Objects.nonNull(airlineListMap) && !airlineListMap.isEmpty()) {
                        apiRedisService.setHashData(RedisKeyConfig.REDIS_AIRLINE_INFO_HASH, airlineListMap, 60 * 60 * 24 * 3L);
                    }
                    if (!StringUtil.isNullOrEmpty(airLineInfoDepCityDtoList)) {
                        apiRedisService.putData(RedisKeyConfig.REDIS_AIRLINE_INFO_CD, ZipUtil.zipHex(JsonUtil.objectToJson(airLineInfoDepCityDtoList)), 60 * 60 * 24 * 7L);
                    }
                    return airLineInfoDepCityDtoList;
                } else {
                    log.error("【基础服务-航线查询】异常信息:{},请求参数:{}", JsonUtil.objectToJson(basicResp), JsonUtil.objectToJson(basicReq));
                }
            } else {
                log.error("【基础服务-航线查询】异常信息:{},请求参数:{}", JsonUtil.objectToJson(httpResult), JsonUtil.objectToJson(basicReq));
            }
            return new ArrayList<>();
        } else {
            List<AirLineInfoDepCityDto> airLineInfoDepCityDtoList = airlineList.parallelStream().map(a -> (AirLineInfoDepCityDto) JsonUtil.jsonToBean(ZipUtil.unzipHex(a), AirLineInfoDepCityDto.class)).collect(Collectors.toList());
            airLineInfoDepCityDtoList.sort(Comparator.comparing(AirLineInfoDepCityDto::getCityPinYin));
            return airLineInfoDepCityDtoList;
        }
    }


    /**
     * 查询城市信息
     *
     * @param basicReq
     * @return
     */
    private List<CityInfoDto> queryAllCity(BasicBaseReq basicReq) {
        String url = HandlerConstants.BASIC_PROVIDER_URL + HandlerConstants.BASIC_API_CITYS_QUERY;
        HttpResult httpResult = HttpUtil.doPostClient(basicReq, url, 5000, 3000);
        if (httpResult.isResult()) {
            Type type = new TypeToken<BasicBaseResp<List<CityInfoDto>>>() {
            }.getType();
            BasicBaseResp<List<CityInfoDto>> basicResp = (BasicBaseResp<List<CityInfoDto>>) JsonUtil.jsonToBean(httpResult.getResponse(), type);
            if (WSEnum.SUCCESS.getResultCode().equals(basicResp.getResultCode())) {
                return basicResp.getResult();
            } else {
                log.error("【基础服务-城市查询】异常信息:{},请求参数:{}", JsonUtil.objectToJson(basicResp), JsonUtil.objectToJson(basicReq));
            }
        } else {
            log.error("【基础服务-城市查询】异常信息:{},请求参数:{}", JsonUtil.objectToJson(httpResult), JsonUtil.objectToJson(basicReq));
        }
        return new ArrayList<>();
    }

    @Override
    public Map<String, String> queryAllCityMap() {
        Map<String, String> cityJson = apiRedisService.getHashData(RedisKeyConfig.REDIS_CITY_INFO_HASH);
        if (cityJson == null || cityJson.isEmpty()) {
            cityJson = saveCityInfoRedis();
        }
        return cityJson;
    }

    @Override
    public Map<String, CityInfoDto> queryAllCityMap(String channelCode, String ip) {
        BasicBaseReq basicReq = new BasicBaseReq(HandlerConstants.BASIC_INFO_VERSION, channelCode, ip);
        String cityJson = apiRedisService.getData(RedisKeyConfig.REDIS_CITY_INFO_CD);
        if (StringUtil.isNullOrEmpty(cityJson) || "null".equals(cityJson)) {
            Map<String, CityInfoDto> cityMap = new HashMap<>();
            List<CityInfoDto> cityInfoDtoList = queryAllCity(basicReq);
            if (!StringUtil.isNullOrEmpty(cityInfoDtoList)) {
                for (CityInfoDto cityInfoDto : cityInfoDtoList) {
                    cityMap.put(cityInfoDto.getCityCode(), cityInfoDto);
                }
                apiRedisService.putData(RedisKeyConfig.REDIS_CITY_INFO_CD, JsonUtil.objectToJson(cityMap), 0);
            }
            return cityMap;
        } else {
            Type type = new com.google.gson.reflect.TypeToken<Map<String, CityInfoDto>>() {
            }.getType();
            return (Map<String, CityInfoDto>) JsonUtil.jsonToMap(cityJson, type);
        }
    }

    /**
     * 根据城市三字码获取
     *
     * @param cityCode
     * @return
     */
    @Override
    public CityInfoDto selectCity(String cityCode) {
        String key = RedisKeyConfig.REDIS_CITY_INFO_HASH;
        Object obj = apiRedisService.getHashData(key, cityCode);
        //如果未查询到重清除缓存重新查询，可能是缓存未更新
        if (obj == null) {
            log.info("{}缓存中未查询到城市信息", cityCode);
            String errorKey = RedisKeyConfig.REDIS_CITY_INFO_ERROR + cityCode;
            //检查系统中是否标记为异常数据，异常数据直接返回
            if (StringUtils.isNotBlank(apiRedisService.getData(errorKey))) {
                return null;
            }
            Map<String, String> cityStrMap = saveCityInfoRedis();
            if (cityStrMap != null && !cityStrMap.isEmpty()) {
                if (cityStrMap.get(cityCode) == null) {
                    log.info("{}数据库中未查询到城市信息", cityCode);
                    //标记为异常数据，记录缓存信息
                    apiRedisService.putData(errorKey, cityCode, 3600 * 24L);
                    return null;
                }
                return JsonUtil.fromJson(cityStrMap.get(cityCode), CityInfoDto.class);
            }
        } else {
            return JsonUtil.fromJson(obj.toString(), CityInfoDto.class);
        }
        return null;
    }

    private Map<String, String> saveCityInfoRedis() {
        Map<String, CityInfoDto> cityMap = new HashMap<>();
        Map<String, String> cityStrMap = new HashMap<>();
        BasicBaseReq basicBaseReq = new BasicBaseReq(HandlerConstants.BASIC_INFO_VERSION, ChannelCodeEnum.MOBILE.getChannelCode(), IPUtil.getLocalIp());
        List<CityInfoDto> cityInfoDtoList = queryAllCity(basicBaseReq);
        if (!StringUtil.isNullOrEmpty(cityInfoDtoList)) {
            for (CityInfoDto cityInfoDtoTemp : cityInfoDtoList) {
                cityMap.put(cityInfoDtoTemp.getCityCode(), cityInfoDtoTemp);
                cityStrMap.put(cityInfoDtoTemp.getCityCode(), JsonUtil.objectToJson(cityInfoDtoTemp));
            }
            apiRedisService.putData(RedisKeyConfig.REDIS_CITY_INFO_CD, JsonUtil.objectToJson(cityMap), 300L);
            apiRedisService.setHashData(RedisKeyConfig.REDIS_CITY_INFO_HASH, cityStrMap, 300L);
        }
        return cityStrMap;
    }

    /**
     * 根据机场三字码查询
     *
     * @param airportCode
     * @return
     */
    @Override
    public AirPortInfoDto selectAirport(String airportCode) {
        String key = RedisKeyConfig.REDIS_AIRPORT_INFO_HASH;
        Object obj = apiRedisService.getHashData(key, airportCode);
        //如果未查询到重清除缓存重新查询，可能是缓存未更新
        if (obj == null) {
            Map<String, AirPortInfoDto> airportMap = new HashMap<>();
            Map<String, String> airportStrMap = new HashMap<>();
            BasicBaseReq basicBaseReq = new BasicBaseReq(HandlerConstants.BASIC_INFO_VERSION, ChannelCodeEnum.MOBILE.getChannelCode(), "127.0.0.1");
            List<AirPortInfoDto> airPortInfoDtoList = queryAllAirport(basicBaseReq);
            if (!StringUtil.isNullOrEmpty(airPortInfoDtoList)) {
                for (AirPortInfoDto airPortInfoDto : airPortInfoDtoList) {
                    airportMap.put(airPortInfoDto.getAirPortCode(), airPortInfoDto);
                    airportStrMap.put(airPortInfoDto.getAirPortCode(), JsonUtil.objectToJson(airPortInfoDto));
                }
                apiRedisService.putData(RedisKeyConfig.REDIS_AIRPORT_INFO_CD, JsonUtil.objectToJson(airportMap), 0);
                apiRedisService.setHashData(key, airportStrMap, 600L);
                return airPortInfoDtoList.stream().filter(airPortInfoDto -> airportCode.equals(airPortInfoDto.getAirPortCode())).findFirst().orElse(null);
            }
        } else {
            AirPortInfoDto airPortInfoDto = (AirPortInfoDto) JsonUtil.jsonToBean(obj.toString(), new TypeToken<AirPortInfoDto>() {
            }.getType());
            return airPortInfoDto;
        }
        return null;
    }

    @Override
    public List<AirPortInfoDto> queryAllAirport(BasicBaseReq basicReq) {
        String url = HandlerConstants.BASIC_INFO_URL + HandlerConstants.BASIC_API_AIRPORTS_QUERY;
        HttpResult httpResult = HttpUtil.doPostClient(basicReq, url, 5000, 3000);
        if (httpResult.isResult()) {
            Type type = new TypeToken<BasicBaseResp<List<AirPortInfoDto>>>() {
            }.getType();
            BasicBaseResp<List<AirPortInfoDto>> basicResp = (BasicBaseResp<List<AirPortInfoDto>>) JsonUtil.jsonToBean(httpResult.getResponse(), type);
            if (WSEnum.SUCCESS.getResultCode().equals(basicResp.getResultCode())) {
                return basicResp.getResult();
            } else {
                log.error("【基础服务-机场查询】异常信息:{},请求参数:{}", JsonUtil.objectToJson(basicResp), JsonUtil.objectToJson(basicReq));
            }
        } else {
            log.error("【基础服务-机场查询】异常信息:{},请求参数:{}", JsonUtil.objectToJson(httpResult), JsonUtil.objectToJson(basicReq));
        }
        return new ArrayList<>();
    }

    @Override
    public Map<String, AirPortInfoDto> queryAllAirportMap(String channelCode, String ip) {
        BasicBaseReq basicBaseReq = new BasicBaseReq(HandlerConstants.BASIC_INFO_VERSION, channelCode, ip);
        return queryAllAirportMap(basicBaseReq);
    }

    @Override
    public Map<String, AirPortInfoDto> queryAllAirportMap(BasicBaseReq basicReq) {
        String airportJson = apiRedisService.getData(RedisKeyConfig.REDIS_AIRPORT_INFO_CD);
        if (StringUtil.isNullOrEmpty(airportJson) || "null".equals(airportJson)) {
            Map<String, AirPortInfoDto> airPortInfoDtoMap = new HashMap<>();
            List<AirPortInfoDto> airPortInfoDtoList = queryAllAirport(basicReq);
            if (!StringUtil.isNullOrEmpty(airPortInfoDtoList)) {
                for (AirPortInfoDto airPortInfoTemp : airPortInfoDtoList) {
                    airPortInfoDtoMap.put(airPortInfoTemp.getAirPortCode(), airPortInfoTemp);
                }
                apiRedisService.putData(RedisKeyConfig.REDIS_AIRPORT_INFO_CD, JsonUtil.objectToJson(airPortInfoDtoMap), 0);
            }
            return airPortInfoDtoMap;
        } else {
            Type type = new com.google.gson.reflect.TypeToken<Map<String, AirPortInfoDto>>() {
            }.getType();
            return (Map<String, AirPortInfoDto>) JsonUtil.jsonToMap(airportJson, type);
        }
    }


    @Override
    public Map<String, AirPortInfoDto> queryAllAirportMap(String channelCode, String ip, Date date) {
        Map<String, AirPortInfoDto> airPortInfoDtoMap = queryAllAirportMap(channelCode, ip);
        return updateAirportNameService.updateMapByDate(airPortInfoDtoMap, date);
    }


    @Override
    @Cacheable(value = "cityAirportInfoCache", key = "#redisKey")
    @CacheDuration(duration = 3600 * 24 * 7)
    public List<CityAirportInfo> searchCityAirportInfo(BasicBaseReq basicBaseReq, String redisKey) {
        //all 城市信息
        List<CityInfoDto> cityInfoDtoList = queryAllCity(basicBaseReq);
        //all 机场信息
        List<AirPortInfoDto> airPortInfoDtoList = queryAllAirport(basicBaseReq);
        List<CityAirportInfo> cityAirportInfoList = new ArrayList<>();
        List<CityAirportInfo> cityInfoListD = new ArrayList<>();
        List<CityAirportInfo> cityInfoListI = new ArrayList<>();
        List<CityAirportInfo> airportInfoListD = new ArrayList<>();
        List<CityAirportInfo> airportInfoListI = new ArrayList<>();
        if (!StringUtil.isNullOrEmpty(cityInfoDtoList)) {
            for (CityInfoDto cityInfoDto : cityInfoDtoList) {
                CityAirportInfo cityAirportInfo = new CityAirportInfo();
                cityAirportInfo.setCityOrAirportType("city");
                cityAirportInfo.setCityCode(cityInfoDto.getCityCode());
                cityAirportInfo.setCityName(cityInfoDto.getCityName());
                cityAirportInfo.setCityPinYin(cityInfoDto.getCityPinYin());
                cityAirportInfo.setCityPinYinAbb(cityInfoDto.getCityPinYinAbb());
                cityAirportInfo.setIsInternational(cityInfoDto.getIsInternational());
                cityAirportInfo.setCityNameKeyword(cityInfoDto.getCityNameKeyword());
                cityAirportInfo.setCityLabelInfoList(cityInfoDto.getCityLabelInfoList());
                if (HandlerConstants.TRIP_TYPE_D.equals(cityInfoDto.getIsInternational())) {
                    cityInfoListD.add(cityAirportInfo);
                } else {
                    cityInfoListI.add(cityAirportInfo);
                }
            }
            cityAirportInfoList.addAll(cityInfoListD);
            cityAirportInfoList.addAll(cityInfoListI);
        }
        if (!StringUtil.isNullOrEmpty(airPortInfoDtoList)) {
            for (AirPortInfoDto airPortInfoDto : airPortInfoDtoList) {
                CityAirportInfo cityAirportInfo = new CityAirportInfo();
                cityAirportInfo.setCityOrAirportType("airport");
                cityAirportInfo.setAirportCode(airPortInfoDto.getAirPortCode());
                cityAirportInfo.setAirportPinYin(airPortInfoDto.getAirPortPinYin());
                cityAirportInfo.setAirportPinYinAbb(airPortInfoDto.getPinYinAbb());
                String airPortName = airPortInfoDto.getAirPortName();
                if (!StringUtil.isNullOrEmpty(airPortName)) {
                    if (!airPortName.endsWith("机场")) {
                        cityAirportInfo.setAirportName(airPortName + "机场");
                    } else {
                        cityAirportInfo.setAirportName(airPortName);
                    }
                }
                cityAirportInfo.setCityCode(airPortInfoDto.getCityCode());
                cityAirportInfo.setCityName(airPortInfoDto.getCityName());
                cityAirportInfo.setCityPinYin(airPortInfoDto.getCityPinYin());
                cityAirportInfo.setIsInternational(airPortInfoDto.getIsInternational());
                //获取对应的城市标签
                if (!StringUtil.isNullOrEmpty(cityInfoDtoList)) {
                    CityInfoDto cityInfoDto = cityInfoDtoList.stream().filter(city -> city.getCityCode().equals(airPortInfoDto.getCityCode())).findFirst().orElse(null);
                    if (cityInfoDto != null) {
                        cityAirportInfo.setCityLabelInfoList(cityInfoDto.getCityLabelInfoList());
                    }
                }
                if (HandlerConstants.TRIP_TYPE_D.equals(airPortInfoDto.getIsInternational())) {
                    airportInfoListD.add(cityAirportInfo);
                } else {
                    airportInfoListI.add(cityAirportInfo);
                }
            }
            cityAirportInfoList.addAll(airportInfoListD);
            cityAirportInfoList.addAll(airportInfoListI);
        }
        return cityAirportInfoList;
    }

    @Override
    public List<PictureDto> getPictureList(PictureRequest pictureRequest) {
        String url = HandlerConstants.BASIC_INFO_URL + HandlerConstants.BASIC_INFO_PICTURELIST;
        HttpResult result = HttpUtil.doPostClient(pictureRequest, url, READ_TIME, CONNECT_TIME);
        if (result.isResult()) {
            String respStr = result.getResponse();
            if (!StringUtil.isNullOrEmpty(respStr)) {
                try {
                    BaseResp<List<PictureDto>> resp = (BaseResp<List<PictureDto>>) JsonUtil.jsonToBean(respStr, new TypeToken<BaseResp<List<PictureDto>>>() {
                    }.getType());
                    if (WSEnum.SUCCESS.getResultCode().equals(resp.getResultCode()) && !StringUtil.isNullOrEmpty(resp.getObjData())) {
                        return resp.getObjData();
                    } else {
                        return new ArrayList<>();
                    }
                } catch (Exception e) {
                    log.error("查询广告位发生异常：{}", JsonUtil.objectToJson(pictureRequest));
                    return new ArrayList<>();
                }
            } else {
                throw new ServiceException("返回数据空");
            }
        } else {
            throw new NetworkException(result.getResponse());
        }
    }

    @Override
    public BaseResp<List<PictureDto>> getPictureListResult(PictureRequest pictureRequest) {
        BaseResp<List<PictureDto>> resp;
        String url = HandlerConstants.BASIC_INFO_URL + HandlerConstants.BASIC_INFO_PICTURELIST;
        HttpResult result = HttpUtil.doPostClient(pictureRequest, url, READ_TIME, CONNECT_TIME);
        if (result.isResult()) {
            String respStr = result.getResponse();
            if (!StringUtil.isNullOrEmpty(respStr)) {
                resp = (BaseResp<List<PictureDto>>) JsonUtil.jsonToBean(respStr, new TypeToken<BaseResp<List<PictureDto>>>() {
                }.getType());
            } else {
                throw new ServiceException("返回数据空");
            }
        } else {
            throw new NetworkException("查询网络异常");
        }
        return resp;
    }

    @Override
    public BaseResp<List<AVDTO>> fetchAggregatePageAV(PictureRequest pictureRequest) {
        BaseResp<List<AVDTO>> resp;
        String url = HandlerConstants.BASIC_INFO_URL + HandlerConstants.BASIC_INFO_AGGREGATE;
        HttpResult result = HttpUtil.doPostClient(pictureRequest, url, READ_TIME, CONNECT_TIME);
        if (result.isResult()) {
            String respStr = result.getResponse();
            if (!StringUtil.isNullOrEmpty(respStr)) {
                resp = (BaseResp<List<AVDTO>>) JsonUtil.jsonToBean(respStr, new TypeToken<BaseResp<List<AVDTO>>>() {
                }.getType());
            } else {
                throw new ServiceException("返回数据空");
            }
        } else {
            throw new NetworkException("查询网络异常");
        }
        return resp;
    }


    @Override
    @Cacheable(value = "queryPayMethods", key = "#payMethResultKey")
    @CacheDuration(duration = 60 * 60)
    public List<PayMethodDTO> queryPayMethods(BasicBaseReq<ParamPayMethodDTO> paramPayMethodDTO, String payMethResultKey) {
        String url = HandlerConstants.BASIC_INFO_URL + HandlerConstants.QUERY_PAY_METHOD_INFO;
        HttpResult result = HttpUtil.doPostClient(paramPayMethodDTO, url, READ_TIME, CONNECT_TIME);
        if (result.isResult()) {
            String respStr = result.getResponse();
            if (StringUtils.isNotBlank(respStr)) {
                try {
                    BasicBaseResp<List<PayMethodDTO>> resp = JsonUtil.fromJson(respStr, new TypeToken<BasicBaseResp<List<PayMethodDTO>>>() {
                    }.getType());
                    if (WSEnum.SUCCESS.getResultCode().equals(resp.getResultCode()) && !StringUtil.isNullOrEmpty(resp.getResult())) {
                        return resp.getResult();
                    } else {
                        return new ArrayList<>();
                    }
                } catch (Exception e) {
                    log.error("查询支付配置信息异常：{}", JsonUtil.objectToJson(paramPayMethodDTO));
                    return new ArrayList<>();
                }

            } else {
                throw new ServiceException("返回数据空");
            }
        } else {
            throw new NetworkException(result.getResponse());
        }
    }

    /**
     * BASIC_INFO_HOTEL_ADVERTISEMENT已处理 配置有效期、arrCity null 数据为匹配全部城市
     *
     * @param channelCode
     * @param ip
     * @param location
     * @param flightDate
     * @param arrCityList
     * @return
     */
    @Override
    public List<HotelProductRespDTO> queryHotelProductList(String channelCode, String ip, String location, String flightDate, List<String> arrCityList) {
        HotelProductReqDTO reqDTO = new HotelProductReqDTO(location, flightDate, arrCityList, channelCode);
        BasicBaseReq<HotelProductReqDTO> req = new BasicBaseReq<>();
        req.setChannelCode(channelCode);
        req.setIp(ip);
        req.setVersion(HandlerConstants.VERSION);
        req.setRequest(reqDTO);
        List<HotelProductRespDTO> respDTOList = Lists.newArrayList();
        String url = HandlerConstants.BASIC_INFO_URL + HandlerConstants.BASIC_INFO_HOTEL_ADVERTISEMENT;
        HttpResult result = HttpUtil.doPostClient(req, url, 2000, 1000);
        if (!result.isResult() || StringUtils.isBlank(result.getResponse())) {
            return respDTOList;
        }
        BasicBaseResp<List<HotelProductRespDTO>> resp = JsonUtil.fromJson(result.getResponse(), new TypeToken<BasicBaseResp<List<HotelProductRespDTO>>>() {
        }.getType());
        if (WSEnum.SUCCESS.getResultCode().equals(resp.getResultCode())) {
            return resp.getResult();
        }
        return respDTOList;
    }

    @Override
    public CouponGrantRecord queryCouponGrantRecord(String channelCode, String ip, String ffpId, String ffpCardNo,
                                                    SendCouponSceneEnum scene, String ticketNo, String depAirport, String arrAirport) {
        BasicBaseReq<CouponGrantReqDTO> req = genBasicBaseReq(channelCode, ip, ffpId, ffpCardNo);
        CouponGrantReqDTO reqDTO = new CouponGrantReqDTO();
        reqDTO.setType(scene.getType());
        reqDTO.setChannelCode(channelCode);
        reqDTO.setTicketNo(ticketNo);
        reqDTO.setArrAirport(arrAirport);
        reqDTO.setDepAirport(depAirport);
        req.setRequest(reqDTO);
        String url = HandlerConstants.BASIC_INFO_URL + HandlerConstants.QUERY_COUPON_GRANT;
        HttpResult result = HttpUtil.doPostClient(req, url, READ_TIME, CONNECT_TIME);
        if (!result.isResult() || StringUtils.isBlank(result.getResponse())) {
            return null;
        }
        BasicBaseResp<CouponGrantRecord> resp = JsonUtil.fromJson(result.getResponse(), new TypeToken<BasicBaseResp<CouponGrantRecord>>() {
        }.getType());
        if (WSEnum.SUCCESS.getResultCode().equals(resp.getResultCode())) {
            return resp.getResult();
        }
        return null;
    }

    @Override
    public BasicBaseResp<CouponGrantRecord> saveSendCouponRecord(String channelCode, String ip, String ffpId, String ffpCardNo, String recordId, String couponCode) {
        BasicBaseReq<CouponGrantReceiveReqDTO> req = genBasicBaseReq(channelCode, ip, ffpId, ffpCardNo);
        CouponGrantReceiveReqDTO reqDTO = new CouponGrantReceiveReqDTO();
        reqDTO.setCouponRecordId(recordId);
        reqDTO.setCouponCode(couponCode);
        req.setRequest(reqDTO);
        String url = HandlerConstants.BASIC_INFO_URL + HandlerConstants.SAVE_COUPON_GRANT_RECORD;
        HttpResult result = HttpUtil.doPostClient(req, url, READ_TIME, CONNECT_TIME);
        if (!result.isResult() || StringUtils.isBlank(result.getResponse())) {
            return null;
        }
        return JsonUtil.fromJson(result.getResponse(), new TypeToken<BasicBaseResp<CouponGrantReqDTO>>() {
        }.getType());
    }

    /**
     * 查询会员的任务中心的开启状态 T表示已开启 F表示未开启
     *
     * @param channelCode
     * @param ip
     * @param ffpId
     * @param ffpCardNo
     * @return
     */
    @Override
    public BasicBaseResp<TaskCenterRespDTO> queryMemberTaskCenterStatus(String channelCode, String ip, String ffpId, String ffpCardNo) {
        BasicBaseReq req = genBasicBaseReq(channelCode, ip, ffpId, ffpCardNo);
        String url = HandlerConstants.BASIC_INFO_URL + HandlerConstants.QUERY_TASK_STATUS;
        HttpResult result = HttpUtil.doPostClient(req, url, READ_TIME, CONNECT_TIME);
        if (!result.isResult() || StringUtils.isBlank(result.getResponse())) {
            return null;
        }
        return JsonUtil.fromJson(result.getResponse(), new TypeToken<BasicBaseResp<TaskCenterRespDTO>>() {
        }.getType());
    }

    /**
     * 查询会员的任务列表
     *
     * @param channelCode
     * @param ip
     * @param ffpId
     * @param ffpCardNo
     * @return
     */
    @Override
    public BasicBaseResp<List<TaskCenterDescRespDTO>> queryMemberTaskList(String channelCode, String ip, String ffpId, String ffpCardNo) {
        BasicBaseReq req = genBasicBaseReq(channelCode, ip, ffpId, ffpCardNo);
        String url = HandlerConstants.BASIC_INFO_URL + HandlerConstants.QUERY_TASK_LIST;
        HttpResult result = HttpUtil.doPostClient(req, url, READ_TIME, CONNECT_TIME);
        if (!result.isResult() || StringUtils.isBlank(result.getResponse())) {
            return null;
        }
        return JsonUtil.fromJson(result.getResponse(), new TypeToken<BasicBaseResp<List<TaskCenterDescRespDTO>>>() {
        }.getType());

    }

    /**
     * 更新会员的任务中心的开启状态
     *
     * @param channelCode
     * @param ip
     * @param ffpId
     * @param ffpCardNo
     * @return
     */
    @Override
    public BasicBaseResp<TaskCenterRespDTO> updateMemberTaskCenterStatus(String channelCode, String ip, String ffpId, String ffpCardNo) {
        BasicBaseReq req = genBasicBaseReq(channelCode, ip, ffpId, ffpCardNo);
        String url = HandlerConstants.BASIC_INFO_URL + HandlerConstants.UPDATE_TASK_STATUS;
        HttpResult result = HttpUtil.doPostClient(req, url, READ_TIME, CONNECT_TIME);
        if (!result.isResult() || StringUtils.isBlank(result.getResponse())) {
            return null;
        }
        return JsonUtil.fromJson(result.getResponse(), new TypeToken<BasicBaseResp<TaskCenterDescRespDTO>>() {
        }.getType());

    }

    /**
     * 更换新会员该条任务的任务状态
     *
     * @param channelCode
     * @param ip
     * @param ffpId
     * @param ffpCardNo
     * @return
     */
    @Override
    public BasicBaseResp<TaskCenterRespDTO> updateTaskMemberInfo(String taskTypeDetails, String channelCode, String ip,
                                                                 String ffpId, String ffpCardNo, String completeStatus) {
        BasicBaseReq req = genBasicBaseReq(channelCode, ip, ffpId, ffpCardNo);
        TaskMemberInfoReqDTO taskMemberInfoReqDTO = new TaskMemberInfoReqDTO();
        taskMemberInfoReqDTO.setFfpId(ffpId);
        taskMemberInfoReqDTO.setFfpCardNo(ffpCardNo);
        taskMemberInfoReqDTO.setTaskCompleteStatus(completeStatus);
        taskMemberInfoReqDTO.setTaskTypeDetails(taskTypeDetails);
        req.setRequest(taskMemberInfoReqDTO);
        String url = HandlerConstants.BASIC_INFO_URL + HandlerConstants.UPDATE_TASK_MEMBER_INFO;
        HttpResult result = HttpUtil.doPostClient(req, url, READ_TIME, CONNECT_TIME);
        if (!result.isResult() || StringUtils.isBlank(result.getResponse())) {
            return null;
        }
        return JsonUtil.fromJson(result.getResponse(), new TypeToken<BasicBaseResp<TaskCenterRespDTO>>() {
        }.getType());
    }

    /**
     * 验证会员该条任务的任务状态
     *
     * @param channelCode
     * @param ip
     * @param ffpId
     * @param ffpCardNo
     * @return
     */
    @Override
    public BasicBaseResp<Boolean> checkTaskMemberStatus(String taskTypeDetails, String channelCode, String ip,
                                                        String ffpId, String ffpCardNo, String completeStatus) {
        BasicBaseReq req = genBasicBaseReq(channelCode, ip, ffpId, ffpCardNo);
        TaskMemberInfoReqDTO taskMemberInfoReqDTO = new TaskMemberInfoReqDTO();
        taskMemberInfoReqDTO.setFfpId(ffpId);
        taskMemberInfoReqDTO.setFfpCardNo(ffpCardNo);
        taskMemberInfoReqDTO.setTaskCompleteStatus(completeStatus);
        taskMemberInfoReqDTO.setTaskTypeDetails(taskTypeDetails);
        req.setRequest(taskMemberInfoReqDTO);
        String url = HandlerConstants.BASIC_INFO_URL + HandlerConstants.CHECK_TASK_MEMBER_STATUS;
        HttpResult result = HttpUtil.doPostClient(req, url, READ_TIME, CONNECT_TIME);
        if (!result.isResult() || StringUtils.isBlank(result.getResponse())) {
            return null;
        }
        return JsonUtil.fromJson(result.getResponse(), new TypeToken<BasicBaseResp<Boolean>>() {
        }.getType());
    }

    /**
     * 统一风险控制判断
     *
     * @param channelCode
     * @param antifraud
     * @return
     */
    @Override
    public FraudApiResponse antifraud(String channelCode, Antifraud antifraud) {
        BasicBaseReq basicBaseReq = genBasicBaseReq(channelCode, antifraud.getIp(), antifraud.getFfpId(), antifraud.getFfpCardNo());
        basicBaseReq.setRequest(antifraud);
        String url = HandlerConstants.BASIC_INFO_URL + HandlerConstants.RISK_ANTIFRAUD;
        HttpResult result = HttpUtil.doPostClient(basicBaseReq, url, 2000, 1000);
        FraudApiResponse fraudApiResponse = new FraudApiResponse();
        if (!result.isResult() || StringUtils.isBlank(result.getResponse())) {
            fraudApiResponse.setSuccess(false);
            fraudApiResponse.setRemark("网络原因");
            return fraudApiResponse;
        }
        BaseResp<FraudApiResponse> baseResp = JsonUtil.fromJson(result.getResponse(), new TypeToken<BaseResp<FraudApiResponse>>() {
        }.getType());
        if (WSEnum.SUCCESS.getResultCode().equals(baseResp.getResultCode())) {
            return baseResp.getObjData();
        } else {
            fraudApiResponse.setSuccess(false);
            fraudApiResponse.setRemark(baseResp.getResultInfo());
            return fraudApiResponse;
        }
    }

    @Override
    public BasicBaseResp<List<ActivityPremiumResp>> queryPremiumByChannelCode(String channelCode, String ip, String ffpId, String ffpCardNo) {
        BasicBaseReq req = genBasicBaseReq(channelCode, ip, ffpId, ffpCardNo);
        String url = HandlerConstants.BASIC_INFO_URL + HandlerConstants.QUERY_PREMIUM_BY_CHANEL_CODE;
        HttpResult result = HttpUtil.doPostClient(req, url, READ_TIME, CONNECT_TIME);
        if (!result.isResult() || StringUtils.isBlank(result.getResponse())) {
            return null;
        }
        return JsonUtil.fromJson(result.getResponse(), new TypeToken<BasicBaseResp<List<ActivityPremiumResp>>>() {
        }.getType());
    }

    //查询赠送优惠券活动
    @Override
    public BasicBaseResp<List<ActivityCouponGrantDTO>> queryActivityCouponGrant(QueryActivityCouponGrantReq couponGrantReq, String channelCode, String ip, String ffpId, String ffpCardNo) {
        BasicBaseReq<QueryActivityCouponGrantReq> req = genBasicBaseReq(channelCode, ip, ffpId, ffpCardNo);
        req.setRequest(couponGrantReq);
        String url = HandlerConstants.BASIC_INFO_URL + HandlerConstants.QUERY_COUPON_GRANT_BY_TYPEANDCHANNELCODE;

        HttpResult result = HttpUtil.doPostClient(req, url, READ_TIME, CONNECT_TIME);
        if (!result.isResult() || StringUtils.isBlank(result.getResponse())) {
            return null;
        }
        BasicBaseResp<List<ActivityCouponGrantDTO>> resp = JSON.parseObject(result.getResponse(), new TypeToken<BasicBaseResp<List<ActivityCouponGrantDTO>>>() {
        }.getType());
        if (!WSEnum.SUCCESS.getResultCode().equals(resp.getResultCode())) {
            log.info("查询赠送优惠券活动异常,请求内容:{},响应内容{}", JsonUtil.objectToJson(req), JsonUtil.objectToJson(resp));
            throw new NetworkException("查询赠送优惠券网络异常");
        }
        return resp;
    }

    //查询已申请次数
    @Override
    public BasicBaseResp<Integer> queryCouponGrantReceiveCount(QueryCouponReceiveCountReq receiveCountReq, String channelCode, String ip, String ffpId, String ffpCardNo) {
        BasicBaseReq<QueryCouponReceiveCountReq> req = genBasicBaseReq(channelCode, ip, ffpId, ffpCardNo);
        req.setRequest(receiveCountReq);
        String url = HandlerConstants.BASIC_INFO_URL + HandlerConstants.QUERY_COUPON_GRANT_RECEIVE_COUNT;

        HttpResult result = HttpUtil.doPostClient(req, url, READ_TIME, CONNECT_TIME);
        if (!result.isResult() || StringUtils.isBlank(result.getResponse())) {
            return null;
        }
        BasicBaseResp<Integer> resp = JsonUtil.fromJson(result.getResponse(), new TypeToken<BasicBaseResp<Integer>>() {
        }.getType());
        if (!WSEnum.SUCCESS.getResultCode().equals(resp.getResultCode())) {
            log.info("查询查询已申请次数异常,请求内容:{},响应内容{}", JsonUtil.objectToJson(req), JsonUtil.objectToJson(resp));
            throw new NetworkException("查询已申请次数网络异常");
        }
        return resp;
    }

    //保存领取优惠券信息
    @Override
    public BasicBaseResp<CouponGrantRecord> avFareCouponReceive(AvFareCouponReceiveDTO receiveDTO, String channelCode, String ip, String ffpId, String ffpCardNo, String recordId, String couponCode) {
        BasicBaseReq<AvFareCouponReceiveDTO> req = genBasicBaseReq(channelCode, ip, ffpId, ffpCardNo);
        req.setRequest(receiveDTO);
        String url = HandlerConstants.BASIC_INFO_URL + HandlerConstants.RECEIVE_COUPON_GRANT_RECEIVE;
        HttpResult result = HttpUtil.doPostClient(req, url, READ_TIME, CONNECT_TIME);
        if (!result.isResult() || StringUtils.isBlank(result.getResponse())) {
            log.info("申请网络异常网络异常,请求:{},响应:{}", JsonUtil.objectToJson(req), result);
            throw new NetworkException("申请网络异常网络异常");
        }
        return JsonUtil.fromJson(result.getResponse(), new TypeToken<BasicBaseResp<CouponGrantReqDTO>>() {
        }.getType());
    }

    /**
     * 查询首页公告
     *
     * @param messageRequest
     * @return
     */
    @Override
    public BaseResp<List<Mess>> queryMessage(MessageRequest messageRequest) {
        BaseResp resp = new BaseResp();
        //置顶公告做降级处理
        if ("Y".equals(messageRequest.getIsTop()) && !"N".equals(handConfig.getClosed())) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(WSEnum.NO_DATA.getResultInfo());
            return resp;
        }
        HttpResult httpResult = HttpUtil.doPostClient(messageRequest, HandlerConstants.BASIC_INFO_URL + HandlerConstants.BASIC_INFO_MESSAGE + "?t=" + System.currentTimeMillis());
        if (httpResult.isResult()) {
            if (!StringUtil.isNullOrEmpty(httpResult.getResponse())) {
                try {
                    resp = (BaseResp) JsonUtil.jsonToBean(httpResult.getResponse(), BaseResp.class);
                } catch (Exception e) {
                    log.error("查询公告异常", e);
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo(WSEnum.NO_DATA.getResultInfo());
                }
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo(WSEnum.ERROR.getResultInfo());
            }
        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR.getResultInfo() + "(1)");
        }
        return resp;
    }

    /**
     * 根据消息ID查询公告详情
     *
     * @param messageRequest
     * @return
     */
    @Override
    public BaseResp<List<NoticeResponse>> fetchMessageList(MessageRequest messageRequest) throws CommonException {
        BaseResp<List<NoticeResponse>> resp = new BaseResp<>();
        //置顶公告做降级处理
        if ("Y".equals(messageRequest.getIsTop()) && !"N".equals(handConfig.getClosed())) {
            throw new CommonException(WSEnum.ERROR.getResultCode(), WSEnum.NO_DATA.getResultInfo());
        }
        try {
            HttpResult result = HttpUtil.doPostClient(messageRequest, HandlerConstants.BASIC_INFO_URL + HandlerConstants.BASIC_INFO_MESSAGE_V2);
            if (!result.isResult()) {
                throw new CommonException(WSEnum.NETWORK_ERROR.getResultCode(), WSEnum.NETWORK_ERROR.getResultInfo());
            }

            String response = result.getResponse();
            if (null == response) {
                throw new CommonException(WSEnum.NO_DATA.getResultCode(), WSEnum.NO_DATA.getResultInfo());
            }

            resp = JSON.parseObject(response, new TypeReference<BaseResp<List<NoticeResponse>>>() {
            });
            return resp;
        } catch (CommonException ce) {
            throw ce;
        } catch (Exception e) {
            StackTraceElement stackTraceElement = e.getStackTrace()[0];
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("查询异常！" + stackTraceElement.getLineNumber());
            log.error("【公告列表查询】,请求参数：{}，错误行数：{}，错误信息：", JsonMapper.buildNormalMapper().toJson(messageRequest), stackTraceElement.getLineNumber(), e);
            return resp;
        }
    }

    @Override
    public BaseResp<MessDetail> queryMessageDetail(MessageRequest messageRequest) {
        BaseResp<MessDetail> resp = new BaseResp<>();
        try {
            HttpResult result = HttpUtil.doPostClient(messageRequest, HandlerConstants.BASIC_INFO_URL + HandlerConstants.BASIC_INFO_MESSAGE_DETAIL);

            if (!result.isResult()) {
                throw new CommonException(WSEnum.NETWORK_ERROR.getResultCode(), WSEnum.NETWORK_ERROR.getResultInfo());
            }

            String response = result.getResponse();
            if (null == response) {
                throw new CommonException(WSEnum.NO_DATA.getResultCode(), WSEnum.NO_DATA.getResultInfo());
            }

            resp = JSON.parseObject(response, new TypeReference<BaseResp<MessDetail>>() {
            });
            MessDetail mess = resp.getObjData();

            //如果基础服务未查询到消息详情，ios需要临时从Mongo中去查询
            if (null != mess && StringUtils.isAnyEmpty(mess.getMessageTitle(), mess.getMessageContent()) && "ios".equals(messageRequest.getPlatformInfo())) {
                //此消息详情从mongodb中获取
                PushDetailInfo pushDetailInfo = null;
                List<PushDetailInfo> list = pushService.getListByMessageId(messageRequest.getFfpId(), messageRequest.getMessageId());
                if (CollectionUtils.isNotEmpty(list)) {
                    Optional<PushDetailInfo> first = list.stream().filter(e -> e.getMessageId().equals(messageRequest.getMessageId())).collect(Collectors.toList()).stream().findFirst();
                    if (first.isPresent()) {
                        pushDetailInfo = first.get();
                    }
                }

                if (null != pushDetailInfo) {
                    MessDetail messDetail = new MessDetail();
                    messDetail.setMessageTitle(pushDetailInfo.getMessageTitle());
                    messDetail.setMessageContent(pushDetailInfo.getMessageContent2());
                    if (StringUtils.isNotEmpty(pushDetailInfo.getPushTime())) {
                        messDetail.setMessageCreateTimeStr(pushDetailInfo.getPushTime().substring(0, 10));
                        String passedDateTime = calculateDateAndTime(pushDetailInfo.getPushTime());
                        messDetail.setPassedDateTime(passedDateTime);
                    }
                    messDetail.setMessageUrl(StringUtils.isNotEmpty(pushDetailInfo.getLinkUrl()) ? pushDetailInfo.getLinkUrl() : pushDetailInfo.getPushUrl());

                    resp.setObjData(messDetail);
                    resp.setChannelCode(messageRequest.getChannelCode());
                    resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                    return resp;
                }
            }

            resp = JSON.parseObject(response, new TypeReference<BaseResp<MessDetail>>() {
            });
            if (null != resp.getObjData() && CollectionUtils.isEmpty(resp.getObjData().getServiceModularList())) {
                resp.getObjData().setServiceModularList(null);
            }
        } catch (CommonException ce) {
            throw ce;
        } catch (Exception e) {
            StackTraceElement stackTraceElement = e.getStackTrace()[0];
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("查询公告详情异常！" + stackTraceElement.getLineNumber());
            log.error("【公告详情查询出错】,请求参数：{}，错误行数：{}，错误信息：", JsonMapper.buildNormalMapper().toJson(messageRequest), stackTraceElement.getLineNumber(), e);
            return resp;
        }

        return resp;
    }

    private String calculateDateAndTime(String dateTimeStr) {
        if (StringUtils.isEmpty(dateTimeStr)) {
            return null;
        }
        Date dateTime = DateUtils.toTotalDate(dateTimeStr);
        Date nowDate = new Date();
        long days = DateUtil.between(dateTime, new Date(), DateUnit.DAY);
        if (days >= 1) {
            return null;
        } else {
            //需要显示当前已经过去多长时间

            //计算秒
            long betweenSeconds = ChronoUnit.SECONDS.between(Instant.ofEpochMilli(dateTime.getTime()), Instant.ofEpochMilli(nowDate.getTime()));
            if (betweenSeconds < 1) {
                return "1秒前";
            } else if (betweenSeconds < 60) {
                String s = String.valueOf(betweenSeconds);
                return s.concat("秒前");
            }
            //计算分钟数
            long betweenMinutes = ChronoUnit.MINUTES.between(Instant.ofEpochMilli(dateTime.getTime()), Instant.ofEpochMilli(nowDate.getTime()));
            if (1 <= betweenMinutes && betweenMinutes < 60) {
                //只显示分钟
                String s = String.valueOf(betweenMinutes);
                return s.concat("分钟前");
            } else {
                //计算小时数
                long betweenHours = ChronoUnit.HOURS.between(Instant.ofEpochMilli(dateTime.getTime()), Instant.ofEpochMilli(nowDate.getTime()));
                //只显示小时数
                String s = String.valueOf(betweenHours);
                return s.concat("小时前");
            }
        }
    }


    /**
     * 获取APP启动图
     *
     * @param req
     * @return
     */
    @Override
    public BaseResp<List<LoadPic>> queryLoadApp(BaseReq<AppPictureRequest> req) {
        BaseResp resp = new BaseResp();
        try {
            if ("N".equals(handConfig.getClosed())) {
                HttpResult httpResult = HttpUtil.doPostClient(req, HandlerConstants.BASIC_INFO_URL + HandlerConstants.BASIC_INFO_SPLASH_PATH);
                if (httpResult.isResult()) {
                    if (!StringUtil.isNullOrEmpty(httpResult.getResponse())) {
                        try {
                            resp = (BaseResp) JsonUtil.jsonToBean(httpResult.getResponse(), BaseResp.class);
                            return resp;
                        } catch (Exception e) {
                            log.error("查询启动图异常", e);
                            resp.setResultCode(WSEnum.ERROR.getResultCode());
                            resp.setResultInfo(WSEnum.NO_DATA.getResultInfo());
                            return resp;
                        }
                    } else {
                        resp.setResultCode(WSEnum.ERROR.getResultCode());
                        resp.setResultInfo("返回结果为空！");
                        return resp;
                    }
                } else {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo(WSEnum.ERROR.getResultInfo());
                    return resp;
                }
            } else {
                resp.setResultCode(WSEnum.NO_DATA.getResultCode());
                resp.setResultInfo(WSEnum.NO_DATA.getResultInfo());
                return resp;
            }
        } catch (Exception e) {
            log.error("启动屏数据获取失败:{}", e.getMessage());
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("数据获取失败");
        }
        return resp;
    }


    /**
     * 根据城市以及日期集合查询航班信息
     *
     * @param flightInfo
     * @return
     */
    @Override
    public BasicBaseResp<List<FlightInfo>> queryFlightInfoByCityAndDate(FlightInfo flightInfo) {
        BasicBaseResp resp = new BasicBaseResp();
        BasicBaseReq basicBaseReq = new BasicBaseReq(HandlerConstants.BASIC_INFO_VERSION, ChannelCodeEnum.MOBILE.getChannelCode(), IPUtil.getLocalIp());
        QueryFlightByCityAndDate queryFlightByCityAndDate = new QueryFlightByCityAndDate(flightInfo.getDepCity(), flightInfo.getArrCity(), flightInfo.getFlightDates());
        basicBaseReq.setRequest(queryFlightByCityAndDate);
        HttpResult httpResult = HttpUtil.doPostClient(basicBaseReq, HandlerConstants.BASIC_PROVIDER_URL + HandlerConstants.BASIC_FLIGHTINFO_QUERY_BY_CITY_AND_DATES);
        if (httpResult.isResult()) {
            Type type = new TypeToken<BasicBaseResp<List<FlightInfo>>>() {
            }.getType();
            resp = (BasicBaseResp) JsonUtil.jsonToBean(httpResult.getResponse(), type);
        } else {
            resp.setResultCode(WSEnum.NETWORK_ERROR.getResultCode());
            resp.setResultInfo(WSEnum.NETWORK_ERROR.getResultInfo());
        }
        return resp;
    }

    @Override
    public List<FlightExistDateDTO> queryFlightInfoExistByCity(BasicBaseReq<FlightExistDateReqDTO> basicBaseReq) {
     HttpResult httpResult = HttpUtil.doPostClient(basicBaseReq, HandlerConstants.BASIC_PROVIDER_URL + HandlerConstants.EXIST_FLIGHTINFO_QUERY_BY_CITY_AND_DATES);
        if (httpResult.isResult()) {
            Type type = new TypeToken<BasicBaseResp<List<FlightExistDateDTO>>>() {
            }.getType();
            BasicBaseResp<List<FlightExistDateDTO>> resp = (BasicBaseResp) JsonUtil.jsonToBean(httpResult.getResponse(), type);
            if(CollectionUtils.isNotEmpty(resp.getResult())){
                return resp.getResult();
            }
        }
        return Collections.emptyList();
    }

    /**
     * 根据指定的航班条件搜索航班信息
     *
     * @param flightInfo
     * @return
     */
    @Override
    public List<FlightInfo> queryFlightInfo(FlightInfo flightInfo) {
        BasicBaseResp<List<FlightInfo>> resp = new BasicBaseResp();
        BasicBaseReq basicBaseReq = new BasicBaseReq(HandlerConstants.BASIC_INFO_VERSION, ChannelCodeEnum.MOBILE.getChannelCode(), IPUtil.getLocalIp());
        QueryFlight queryFlight = new QueryFlight();
        queryFlight.setFlightNo(flightInfo.getFlightNo());
        queryFlight.setFlightDate(flightInfo.getFlightDate());
        queryFlight.setDepAirport(flightInfo.getDepAirport());
        queryFlight.setArrAirport(flightInfo.getArrAirport());
        queryFlight.setDepCity(flightInfo.getDepCity());
        queryFlight.setArrCity(flightInfo.getArrCity());
        basicBaseReq.setRequest(queryFlight);
        HttpResult httpResult = HttpUtil.doPostClient(basicBaseReq, HandlerConstants.BASIC_PROVIDER_URL + HandlerConstants.BASIC_FLIGHTINFO_QUERY);
        if (httpResult.isResult()) {
            Type type = new TypeToken<BasicBaseResp<List<FlightInfo>>>() {
            }.getType();
            resp = (BasicBaseResp) JsonUtil.jsonToBean(httpResult.getResponse(), type);
            return resp.getResult();
        } else {
            resp.setResultCode(WSEnum.NETWORK_ERROR.getResultCode());
            resp.setResultInfo(WSEnum.NETWORK_ERROR.getResultInfo());
            return new ArrayList<>();
        }
    }

    /**
     * 查询机场信息
     *
     * @param airportCode
     * @param cityCode
     * @param isInternational
     * @param status
     * @return
     */
    @Override
    public List<AirPortInfoRespDTO> fetchAirPortInfo(String channelCode, String ip, String airportCode, String cityCode, String isInternational, String status) {
        BasicBaseResp<List<AirPortInfoRespDTO>> resp = new BasicBaseResp();
        BasicBaseReq basicBaseReq = new BasicBaseReq(HandlerConstants.BASIC_INFO_VERSION, ChannelCodeEnum.MOBILE.getChannelCode(), IPUtil.getLocalIp());
        // set param
        AirPortInfoReqDTO reqDTO = new AirPortInfoReqDTO();
        reqDTO.setAirportCode(airportCode);
        basicBaseReq.setChannelCode(channelCode);
        basicBaseReq.setRequest(reqDTO);
        //
        HttpResult httpResult = HttpUtil.doPostClient(basicBaseReq, HandlerConstants.BASIC_INFO_URL + HandlerConstants.BASIC_INFO_AIRPORTS);
        if (httpResult.isResult()) {
            Type type = new TypeToken<BasicBaseResp<List<AirPortInfoRespDTO>>>() {
            }.getType();
            resp = (BasicBaseResp) JsonUtil.jsonToBean(httpResult.getResponse(), type);
            return resp.getResult();
        } else {
            resp.setResultCode(WSEnum.NETWORK_ERROR.getResultCode());
            resp.setResultInfo(WSEnum.NETWORK_ERROR.getResultInfo());
            return new ArrayList<>();
        }
    }

    /**
     * 查询航距
     *
     * @param channelCode
     * @param ip
     * @param segmentList
     * @return
     */
    @Override
    public FlightDistanceDTO fetchFlightDistance(String channelCode, String ip, List<Segment> segmentList) {
        BasicBaseResp<FlightDistanceDTO> resp = new BasicBaseResp();
        BasicBaseReq basicBaseReq = new BasicBaseReq(HandlerConstants.BASIC_INFO_VERSION, ChannelCodeEnum.MOBILE.getChannelCode(), IPUtil.getLocalIp());
        // set param
        FlightDistanceReq reqDTO = new FlightDistanceReq();
        reqDTO.setSegmentList(segmentList);
        basicBaseReq.setChannelCode(channelCode);
        basicBaseReq.setRequest(reqDTO);
        //
        HttpResult httpResult = HttpUtil.doPostClient(basicBaseReq, HandlerConstants.BASIC_PROVIDER_URL + HandlerConstants.BASIC_FLIGHT_DISTANCE);
        if (httpResult.isResult()) {
            Type type = new TypeToken<BasicBaseResp<FlightDistanceDTO>>() {
            }.getType();
            resp = (BasicBaseResp) JsonUtil.jsonToBean(httpResult.getResponse(), type);
            return resp.getResult();
        } else {
            resp.setResultCode(WSEnum.NETWORK_ERROR.getResultCode());
            resp.setResultInfo(WSEnum.NETWORK_ERROR.getResultInfo());
            return null;
        }
    }


    /**
     * 根据指定的航班条件批量搜索航班信息
     *
     * @param
     * @return
     */
    @Override
    public List<FlightInfo> queryBaseFlightByList(List<FlightInfo> flightInfoListQuery) {
        List<FlightInfo> flightInfoList = new ArrayList<>();
        if (CollectionUtils.isEmpty(flightInfoListQuery)) {
            return flightInfoList;
        }
        flightInfoList.parallelStream().forEach(flightInfo -> {
            List<FlightInfo> flightInfos = queryFlightInfo(flightInfo);
            if (CollectionUtils.isNotEmpty(flightInfos)) {
                flightInfoList.addAll(flightInfos);
            }
        });
        return flightInfoList;
    }


    /**
     * 生成基础查询类
     *
     * @param channelCode
     * @param ip
     * @return
     */
    private <T> BasicBaseReq<T> genBasicBaseReq(String channelCode, String ip, String ffpId, String ffpCardNo) {
        BasicBaseReq<T> req = new BasicBaseReq<>();
        req.setChannelCode(StringUtils.isBlank(channelCode) ? ChannelCodeEnum.MOBILE.getChannelCode() : channelCode);
        req.setIp(ip);
        req.setFfpId(ffpId);
        req.setFfpCardNo(ffpCardNo);
        req.setServiceCode(StringUtil.newGUID());
        req.setVersion(HandlerConstants.VERSION);
        return req;
    }

    @Override
    public AirlineMileageResult getAirlineMileageByCity(String depCityCode, String arrCityCode) {
        AirlineMileageParam airlineMileageParam = new AirlineMileageParam();
        airlineMileageParam.setDepCity(depCityCode);
        airlineMileageParam.setArrCity(arrCityCode);
        List<AirlineMileageResult> airlineMileageList = getAirlineMileage(airlineMileageParam);
        return CollectionUtils.isEmpty(airlineMileageList) ? null : airlineMileageList.get(0);
    }

    @Override
    public String getChannelCode(String channelCode, String interFlag) {
        if (HandlerConstants.TRIP_TYPE_I.equals(interFlag) && (ChannelCodeEnum.WEIXIN.getChannelCode().equals(channelCode)
                || ChannelCodeEnum.WXAPP.getChannelCode().equals(channelCode))) {
            channelCode = ChannelCodeEnum.MOBILE.getChannelCode();
        }
        return channelCode;
    }

    @Override
    public void addAttentionFlight(String channelCode, String ffpId, String ffpNo, String originIp, AttentionFlightParam attentionFlightParam) {
        RequestData requestData = RequestData.builder()
                .channelNo(channelCode)
                .ffpId(ffpId)
                .ffpNo(ffpNo)
                .originIp(originIp)
                .data(attentionFlightParam).build();
        String url = HandlerConstants.BASIC_PROVIDER_URL + HandlerConstants.ATTENTION_ADD_FLIGHT;
        HttpResult httpResult = HttpUtil.doPostClient(requestData, url);
        if (httpResult.isResult()) {
            ResponseData responseData = JsonUtil.fromJson(httpResult.getResponse(), ResponseData.class);
            if (!ResponseCode.SUCCESS.getCode().equals(responseData.getCode())) {
                throw new ServiceException(responseData.getMessage());
            }
        } else {
            throw new NetworkException("请求关注航班网络异常");
        }
    }

    @Override
    public List<FollowAirLineInfo> queryAttentionFlightList(String channelCode, String ffpId, String ffpNo, String originIp) {
        RequestData requestData = RequestData.builder()
                .channelNo(channelCode)
                .ffpId(ffpId)
                .ffpNo(ffpNo)
                .originIp(originIp).build();
        String url = HandlerConstants.BASIC_PROVIDER_URL + HandlerConstants.ATTENTION_LiST_FLIGHT;
        HttpResult httpResult = HttpUtil.doPostClient(requestData, url);
        if (httpResult.isResult()) {
            ResponseData<List<FollowAirLineInfo>> responseData = JsonUtil.fromJson(httpResult.getResponse(), new TypeToken<ResponseData<List<FollowAirLineInfo>>>() {
            }.getType());
            if (!ResponseCode.SUCCESS.getCode().equals(responseData.getCode())) {
                throw new ServiceException(responseData.getMessage());
            }
            return responseData.getData();
        } else {
            throw new NetworkException("请求关注航班网络异常");
        }
    }

    /**
     * 标记原神航班
     *
     * @param fareReq
     * @param resp
     */
    @Override
    public void searchThemeFlightInfoList(QueryFlightFareReq fareReq, QueryFlightFareResp resp) {
        BasicBaseReq basicBaseReq = new BasicBaseReq(HandlerConstants.BASIC_INFO_VERSION, ChannelCodeEnum.MOBILE.getChannelCode(), IPUtil.getLocalIp());
        QueryFlight queryFlight = new QueryFlight();
        queryFlight.setFlightDate(fareReq.getDepartureDate());
        queryFlight.setDepCity(fareReq.getSendCode());
        queryFlight.setArrCity(fareReq.getArrCode());
        basicBaseReq.setRequest(queryFlight);
        try {
            HttpResult httpResult = HttpUtil.doPostClient(basicBaseReq, HandlerConstants.FLIGHTBASCI_URL + HandlerConstants.SEARCH_THEME_FLIGHT_INFO_LIST);
            if (httpResult.isResult()) {
                Type type = new TypeToken<BasicBaseResp<List<com.juneyaoair.mobile.mongo.entity.FlightInfo>>>() {
                }.getType();
                BasicBaseResp themeFlightInfo = (BasicBaseResp) JsonUtil.jsonToBean(httpResult.getResponse(), type);
                List<com.juneyaoair.mobile.mongo.entity.FlightInfo> flightInfos = (List<com.juneyaoair.mobile.mongo.entity.FlightInfo>) themeFlightInfo.getResult();
                List<com.juneyaoair.mobile.mongo.entity.FlightInfo> genShinFlightInfo = flightInfos.stream().filter(flightInfo -> "LEGO".equals(flightInfo.getTheme())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(genShinFlightInfo)) {
                    if (CollectionUtils.isNotEmpty(resp.getFlightInfoList())) {
                        genShinFlightInfo.forEach(themeFlight -> {
                            resp.getFlightInfoList().forEach(flightInfo -> {
                                if (themeFlight.getFlightNo().equals(flightInfo.getFlightNo()) && themeFlight.getDepCity().equals(flightInfo.getDepCity())
                                        && themeFlight.getArrCity().equals(flightInfo.getArrCity()) && themeFlight.getFlightDate().equals(flightInfo.getFlightDate())) {
                                    if (ObjectUtil.isNotEmpty(handConfig.getThemeFlightDetailsMap())) {
                                        flightInfo.setThemeFlight(themeFlight.getTheme());
                                        ThemeFlightDetails themeFlightDetails = handConfig.getThemeFlightDetailsMap().get(themeFlight.getTheme());
                                        if(ObjectUtil.isNotEmpty(themeFlightDetails)){
                                            flightInfo.setThemeFlightDetails(themeFlightDetails);
                                        }
                                    }
                                }
                            });
                        });
                    }
                }

            } else {
                log.info("查询主题航班网络出错");
            }
        } catch (Exception e) {
            log.info("查询主题航班信息异常，错误信息为：{}", e);
        }

    }


    /**
     * 航距查询
     *
     * @param airlineMileageParam
     * @return
     */
    private List<AirlineMileageResult> getAirlineMileage(AirlineMileageParam airlineMileageParam) {
        BaseRequestDTO<AirlineMileageParam> baseRequest = new BaseRequestDTO<>();
        baseRequest.setIp(IPUtil.getLocalIp());
        baseRequest.setChannelCode(ChannelCodeEnum.MOBILE.getChannelCode());
        baseRequest.setVersion(HandlerConstants.BASIC_INFO_VERSION);
        baseRequest.setRequest(airlineMileageParam);
        String resultStr = HttpsUtil.invokePost(baseRequest, HandlerConstants.BASIC_PROVIDER_URL + HandlerConstants.FLIGHT_BASE_AIRLINE_MILEAGE);
        if (StringUtils.isBlank(resultStr)) {
            log.error("查询航距信息失败，查询参数：{} 返回空", JSON.toJSONString(airlineMileageParam));
            return null;
        }
        BaseResultDTO<List<AirlineMileageResult>> resp = JSON.parseObject(resultStr, new TypeReference<BaseResultDTO<List<AirlineMileageResult>>>() {
        });
        if (!WSEnum.SUCCESS.getResultCode().equals(resp.getResultCode())) {
            log.error("查询航距信息失败，查询参数：{} 返回空", JSON.toJSONString(airlineMileageParam));
            return null;
        }
        return resp.getResult();
    }


    /**
     * 查询联名信用卡信息
     *
     * @return
     */
    @Override
    public List<CoBrandCreditCardAdDTO> queryCoBrandCreditCardAd() {
        //
        BaseResultDTO<List<CoBrandCreditCardAdDTO>> resp = new BaseResultDTO();
        BasicBaseReq basicBaseReq = new BasicBaseReq(HandlerConstants.BASIC_INFO_VERSION, ChannelCodeEnum.MOBILE.getChannelCode(), IPUtil.getLocalIp());
        String url = HandlerConstants.BASIC_PROVIDER_URL + HandlerConstants.BASIC_CO_BRAND_CREDIT_AD;
        //
        HttpResult httpResult = HttpUtil.doPostClient(basicBaseReq, url);
        if (httpResult.isResult()) {
            Type type = new TypeToken<BaseResultDTO<List<CoBrandCreditCardAdDTO>>>() {
            }.getType();
            resp = (BaseResultDTO) JsonUtil.jsonToBean(httpResult.getResponse(), type);
            return resp.getResult();
        } else {
            throw new NetworkException(httpResult.getResponse());
        }
    }


    @Override
    public List<Mess> getPushDetailList(String ffpId, String ffpCardNo, String appMessageTag) {
        PushDetailQuery pushDetailQuery = new PushDetailQuery();
        pushDetailQuery.setFfpId(ffpId);
        pushDetailQuery.setFfpCardNo(ffpCardNo);
        pushDetailQuery.setMessageType("CUSTOM");
        pushDetailQuery.setAppMessageTag(appMessageTag);
        Date date = new Date();
        Date beforDate = DateUtils.addOrLessMonth(date, -3);
        String startDate = DateUtils.getDateStringAllDate(beforDate);
        pushDetailQuery.setStartDate(startDate);
        //允许时间差
        Date furDate = DateUtils.addOrLessDay(date, 1);
        String endDate = DateUtils.getDateStringAllDate(furDate);
        pushDetailQuery.setEndDate(endDate);
        BaseRequestDTO<PushDetailQuery> baseRequest = new BaseRequestDTO<>();
        baseRequest.setIp(IPUtil.getLocalIp());
        baseRequest.setChannelCode(ChannelCodeEnum.MOBILE.getChannelCode());
        baseRequest.setVersion(HandlerConstants.BASIC_INFO_VERSION);
        baseRequest.setRequest(pushDetailQuery);
        String resultStr = HttpsUtil.invokePost(baseRequest, HandlerConstants.BASIC_PROVIDER_URL + HandlerConstants.GET_PUSH_DETAIL_LIST);
        if (StringUtils.isBlank(resultStr)) {
            log.error("查询APP消息失败，请求ID：{} 查询参数：{} 返回空", MdcUtils.getRequestId(), JSON.toJSONString(baseRequest));
            return null;
        }
        BaseResultDTO<List<Mess>> resp = JSON.parseObject(resultStr, new TypeReference<BaseResultDTO<List<Mess>>>() {
        });
        if (!WSEnum.SUCCESS.getResultCode().equals(resp.getResultCode())) {
            log.error("查询APP消息失败，请求ID：{} 查询参数：{} 返回信息：{}", MdcUtils.getRequestId(), JSON.toJSONString(baseRequest), JSON.toJSONString(resp));
            return null;
        }
        return resp.getResult();
    }

    @Override
    public boolean updateState(String ffpId, String ffpCardNo, String appMessageTag, Set<String> messageIds, String state) {
        PushDetailUpdateState updateState = new PushDetailUpdateState();
        updateState.setFfpId(ffpId);
        updateState.setFfpCardNo(ffpCardNo);
        updateState.setMessageType("CUSTOM");
        updateState.setAppMessageTag(appMessageTag);
        updateState.setMessageIds(messageIds);
        updateState.setState(state);
        BaseRequestDTO<PushDetailUpdateState> baseRequest = new BaseRequestDTO<>();
        baseRequest.setIp(IPUtil.getLocalIp());
        baseRequest.setChannelCode(ChannelCodeEnum.MOBILE.getChannelCode());
        baseRequest.setVersion(HandlerConstants.BASIC_INFO_VERSION);
        baseRequest.setRequest(updateState);
        String resultStr = HttpsUtil.invokePost(baseRequest, HandlerConstants.BASIC_PROVIDER_URL + HandlerConstants.UPDATE_STATE);
        if (StringUtils.isBlank(resultStr)) {
            log.error("更新APP消息状态失败，请求ID：{} 查询参数：{} 返回空", MdcUtils.getRequestId(), JSON.toJSONString(baseRequest));
            return false;
        }
        BaseResultDTO<List<Mess>> resp = JSON.parseObject(resultStr, new TypeReference<BaseResultDTO<List<Mess>>>() {
        });
        if (!WSEnum.SUCCESS.getResultCode().equals(resp.getResultCode())) {
            log.error("更新APP消息状态失败，请求ID：{} 查询参数：{} 返回信息：{}", MdcUtils.getRequestId(), JSON.toJSONString(baseRequest), JSON.toJSONString(resp));
            return false;
        }
        return true;
    }

    @Override
    public Long getNoReadNum(String ffpId, String ffpCardNo, String appMessageTag) {
        PushDetailQuery pushDetailQuery = new PushDetailQuery();
        pushDetailQuery.setFfpId(ffpId);
        pushDetailQuery.setFfpCardNo(ffpCardNo);
        pushDetailQuery.setMessageType("CUSTOM");
        pushDetailQuery.setAppMessageTag(appMessageTag);
        Date date = new Date();
        Date beforDate = DateUtils.addOrLessMonth(date, -3);
        String startDate = DateUtils.getDateStringAllDate(beforDate);
        pushDetailQuery.setStartDate(startDate);
        //允许时间差
        Date furDate = DateUtils.addOrLessDay(date, 1);
        String endDate = DateUtils.getDateStringAllDate(furDate);
        pushDetailQuery.setEndDate(endDate);
        BaseRequestDTO<PushDetailQuery> baseRequest = new BaseRequestDTO<>();
        baseRequest.setIp(IPUtil.getLocalIp());
        baseRequest.setChannelCode(ChannelCodeEnum.MOBILE.getChannelCode());
        baseRequest.setVersion(HandlerConstants.BASIC_INFO_VERSION);
        baseRequest.setRequest(pushDetailQuery);
        String resultStr = HttpsUtil.invokePost(baseRequest, HandlerConstants.BASIC_PROVIDER_URL + HandlerConstants.GET_NO_READ_NUM);
        if (StringUtils.isBlank(resultStr)) {
            log.error("查询APP未读消息数失败，请求ID：{} 查询参数：{} 返回空", MdcUtils.getRequestId(), JSON.toJSONString(baseRequest));
            return null;
        }
        BaseResultDTO<Long> resp = JSON.parseObject(resultStr, new TypeReference<BaseResultDTO<Long>>() {
        });
        if (!WSEnum.SUCCESS.getResultCode().equals(resp.getResultCode())) {
            log.error("查询APP未读消息数失败，请求ID：{} 查询参数：{} 返回信息：{}", MdcUtils.getRequestId(), JSON.toJSONString(baseRequest), JSON.toJSONString(resp));
            return null;
        }
        return resp.getResult();
    }

    @Override
    public void sendCheckVerifyCodeByKey(String type, String key) {
        Map<String, Integer> sendCheckVerifyCodeByFfp = handConfig.getSendCheckVerifyCodeByFfp();
        Integer maxCount = sendCheckVerifyCodeByFfp.get(type);
        if (null == maxCount || maxCount < 0) {
            throw new CommonException(WSEnum.ERROR_CHK_ERROR.getResultCode(), "未配置发送规则，请稍后再试！");
        }
        String dataStr = DateUtils.dateToString(new Date(), DateUtils.YYYY_MM_DD_PATTERN);
        final String redisKey = RedisKeyConfig.SEND_VERIFY_CODE_FFP_COUNT + type + ":" + dataStr + ":" + key;
        Long nowCount = apiRedisService.increment(redisKey, 1, 24 * 60 * 60L);
        if (maxCount < nowCount) {
            throw new CommonException(WSEnum.ERROR_CHK_ERROR.getResultCode(), "操作过于频繁，请明日再试！");
        }
    }
}

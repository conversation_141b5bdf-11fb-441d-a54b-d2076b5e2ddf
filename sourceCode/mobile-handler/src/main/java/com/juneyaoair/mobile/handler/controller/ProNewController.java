package com.juneyaoair.mobile.handler.controller;

import com.juneyaoair.baseclass.shopmall.response.ProNew;
import com.juneyaoair.mobile.comm.service.IRedisService;
import com.juneyaoair.mobile.constants.MobileCoreBeanNames;
import com.juneyaoair.mobile.handler.comm.RedisKeyConfig;
import com.juneyaoair.utils.StringUtil;
import com.juneyaoair.utils.json.JsonUtil;
import com.juneyaoair.utils.util.FileUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @description  商城上新推荐服务
 * @date 2018/8/14  8:33.
 */
@RestController
@RequestMapping("/data")
@Api(value = "产品推荐服务")
public class ProNewController {
    private Logger log = LoggerFactory.getLogger(this.getClass());
    private static final String MALL_KEY = RedisKeyConfig.MALL_KEY;
    @Resource(name = MobileCoreBeanNames.API_REDIS_SERVICE)
    private IRedisService apiRedisService;
    @RequestMapping(value = "/pronew.json",method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @ApiOperation(value = "商城商品推荐", notes = "商城商品推荐")
    public ProNew listProNew(HttpServletRequest request){
        String pro = apiRedisService.getData(MALL_KEY);
        ProNew proNew;
        if(StringUtil.isNullOrEmpty(pro)){
            try{
                //项目所在的实际根路径
                String realPath = request.getSession().getServletContext().getRealPath("/");
                String json = FileUtils.loadFile(realPath+"/data/pronew.json");
                apiRedisService.replaceData(MALL_KEY, json, 24 * 3600L * 7);
                proNew=(ProNew) JsonUtil.jsonToBean(json,ProNew.class);
            }catch (Exception ex){
                log.error("[商品推荐读取文件错误]：",ex);
                proNew = new ProNew();
            }
        }else{
            proNew=(ProNew) JsonUtil.jsonToBean(pro,ProNew.class);
        }
        return proNew;
    }
}

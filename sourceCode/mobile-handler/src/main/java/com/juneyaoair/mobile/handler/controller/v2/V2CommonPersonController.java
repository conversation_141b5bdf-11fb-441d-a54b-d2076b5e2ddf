package com.juneyaoair.mobile.handler.controller.v2;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.juneyaoair.appenum.ChannelCodeEnum;
import com.juneyaoair.appenum.WSEnum;
import com.juneyaoair.appenum.av.FlightQueryTypeEnum;
import com.juneyaoair.appenum.av.PackageTypeEnum;
import com.juneyaoair.appenum.comonperson.CommonPersonCabinType;
import com.juneyaoair.appenum.comonperson.CommonPersonMessageType;
import com.juneyaoair.appenum.member.CertificateTypeEnum;
import com.juneyaoair.appenum.member.ContactTypeEnum;
import com.juneyaoair.appenum.member.MemberDetailRequestItemsEnum;
import com.juneyaoair.appenum.order.PassengerTypeEnum;
import com.juneyaoair.appenum.order.UnifiedOrderResultEnum;
import com.juneyaoair.appenum.rightcoupon.VoucherTypesEnum;
import com.juneyaoair.baseclass.basicsys.response.CityInfoDto;
import com.juneyaoair.baseclass.common.base.UserInfoMust;
import com.juneyaoair.baseclass.common.request.BaseReq;
import com.juneyaoair.baseclass.common.response.BaseResp;
import com.juneyaoair.baseclass.member.response.MemberBeneficiaryCertificateDTO;
import com.juneyaoair.baseclass.member.response.MemberBeneficiaryDTO;
import com.juneyaoair.baseclass.member.response.MemberTagResponse;
import com.juneyaoair.baseclass.passengers.CommonPersonCardQuery;
import com.juneyaoair.baseclass.passengers.CommonPersonQuery;
import com.juneyaoair.baseclass.passengers.req.*;
import com.juneyaoair.baseclass.request.crm.MemberBeneficiaryRequest;
import com.juneyaoair.baseclass.response.booking.Segment;
import com.juneyaoair.baseclass.response.commonPerson.QueryCommonPersonInfo;
import com.juneyaoair.baseclass.response.commonPerson.QueryCommonPersonResp;
import com.juneyaoair.baseclass.unlimit.UnlimitedCard2Config;
import com.juneyaoair.crm.IMemberCrmMemberService;
import com.juneyaoair.crm.IMemberService;
import com.juneyaoair.exception.ExpectableException;
import com.juneyaoair.exception.ServiceException;
import com.juneyaoair.mobile.aop.annotation.InterfaceLog;
import com.juneyaoair.mobile.handler.HandlerConstants;
import com.juneyaoair.mobile.handler.config.bean.HandConfig;
import com.juneyaoair.mobile.handler.controller.BassController;
import com.juneyaoair.mobile.handler.controller.util.CRMReqUtil;
import com.juneyaoair.mobile.handler.controller.util.OrderObjectConvert;
import com.juneyaoair.mobile.handler.manage.OrderManage;
import com.juneyaoair.mobile.handler.service.*;
import com.juneyaoair.mobile.handler.service.bean.country.TCountryDTO;
import com.juneyaoair.mobile.handler.util.CertNoUtil;
import com.juneyaoair.pattern.PatternCommon;
import com.juneyaoair.thirdentity.chdunlimitedfly.UnlimitedFlyBindRecord;
import com.juneyaoair.thirdentity.chdunlimitedfly.UnlimitedFlyV2BindRecord;
import com.juneyaoair.thirdentity.comm.request.Header;
import com.juneyaoair.thirdentity.comm.request.PtApiCRMRequest;
import com.juneyaoair.thirdentity.comm.request.PtCrmMileageRequest;
import com.juneyaoair.thirdentity.comm.response.PtCRMResponse;
import com.juneyaoair.thirdentity.comm.response.PtResponse;
import com.juneyaoair.thirdentity.member.comm.CrmMemberBaseApiRequest;
import com.juneyaoair.thirdentity.member.comm.CrmMemberBaseApiResponse;
import com.juneyaoair.thirdentity.member.request.PtMemberDetailRequest;
import com.juneyaoair.thirdentity.member.request.QueryMemberCertificateReqDto;
import com.juneyaoair.thirdentity.member.response.*;
import com.juneyaoair.thirdentity.passengers.common.CertType;
import com.juneyaoair.thirdentity.passengers.common.GeneralContactCertInfo;
import com.juneyaoair.thirdentity.passengers.common.GeneralContactInfo;
import com.juneyaoair.thirdentity.passengers.req.GeneralCert;
import com.juneyaoair.thirdentity.passengers.req.GeneralContactRequest;
import com.juneyaoair.thirdentity.passengers.req.QueryGeneralContactRequest;
import com.juneyaoair.thirdentity.passengers.req.QueryPeersRequest;
import com.juneyaoair.thirdentity.passengers.resp.PtV2GeneralContactResponse;
import com.juneyaoair.thirdentity.passengers.resp.QueryGeneralContactResponse;
import com.juneyaoair.thirdentity.passengers.resp.QueryPeersResponse;
import com.juneyaoair.utils.StringUtil;
import com.juneyaoair.utils.http.HttpResult;
import com.juneyaoair.utils.http.HttpUtil;
import com.juneyaoair.utils.json.JsonUtil;
import com.juneyaoair.utils.util.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.juneyaoair.utils.util.DateUtils.YYYY_MM_DD_PATTERN;

/**
 * <AUTHOR>
 * @description 新版常用乘机人服务
 * @date 2019/8/5  16:18.
 */
@RequestMapping("/v2/commonPersonService")
@RestController
@Api(value = "常用乘机人服务2.0", tags = {"常用乘机人服务2.0"})
public class V2CommonPersonController extends BassController {
    @Autowired
    private IMemberService memberService;
    @Autowired
    private IBasicService basicService;
    @Autowired
    private LocalCacheService localCacheService;
    @Autowired
    private HandConfig handConfig;
    @Autowired
    private IUnlimitedFlyService unlimitedFlyService;
    @Autowired
    private IBeneficiaryService beneficiaryService;
    @Autowired
    private OrderManage orderManage;

    @Autowired
    private IMemberTagService iMemberTagService;

    @Autowired
    private IMemberCrmMemberService iMemberCrmMemberService;

    @ApiOperation(value = "查询常用乘机人", notes = "查询常用乘机人")
    @RequestMapping(value = "/commonPersonQuerys", method = RequestMethod.POST)
    @InterfaceLog
    public BaseResp<QueryCommonPersonResp> commonPersonQuery(@RequestBody @Validated BaseReq<CommonPersonQuery> req, BindingResult bindingResult, HttpServletRequest request) {
        BaseResp<QueryCommonPersonResp> resp = new BaseResp<>();
        String ip = this.getClientIP(request);
        String channelCode = req.getChannelCode();
        String userNo = this.getChannelInfo(channelCode, "10");
        CommonPersonQuery commonPersonQuery = req.getRequest();
        if (commonPersonQuery == null) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo("业务参数不能为空！");
            return resp;
        }
        boolean flag = this.checkKeyInfo(commonPersonQuery.getChannelCustomerNo(), commonPersonQuery.getLoginKeyInfo(), req.getChannelCode());
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        if (commonPersonQuery.getPageNo() <= 0) {
            commonPersonQuery.setPageNo(1);
        }
        if (commonPersonQuery.getPageSize() <= 0) {
            commonPersonQuery.setPageSize(50);
        }
        String versionCode = request.getHeader(HandlerConstants.CLIENT_VERSION_CODE);
        int ver = VersionNoUtil.toMVerInt(versionCode);
        String headChannelCode = request.getHeader(HandlerConstants.TOKEN_CHANNELCODE);

        // 是否需要过滤护照
        boolean isFilterPP = false;
        boolean isAddOwnPass = CollectionUtils.isEmpty(commonPersonQuery.getCabinTypes())
                || !(commonPersonQuery.getCabinTypes().contains(PackageTypeEnum.CHD_UNLIMITED_FLY.getPackType())
                || commonPersonQuery.getCabinTypes().contains(PackageTypeEnum.ADT_UNLIMITED_FLY.getPackType())
                || commonPersonQuery.getCabinTypes().contains(PackageTypeEnum.UNLIMITED_FARE_V2.getPackType()));

        boolean isFreeTicketOrIntlStudent = FlightQueryTypeEnum.AWARD_FLY_FREE_TICKET.getType().equals(commonPersonQuery.getFlightQueryType())
                || FlightQueryTypeEnum.STUDENT.getType().equals(commonPersonQuery.getFlightQueryType());

        Segment segmentInfo = localCacheService.getSegment(commonPersonQuery.getDepCityCode(), commonPersonQuery.getArrCityCode());
        // 免票查询为了匹配到本人信息，查询所有证件信息，国际护照校验由前端完成
        if (isFreeTicketOrIntlStudent && (segmentInfo.isInternal())) {
            isFilterPP = true;
            isAddOwnPass = true;
        }
        // 查询常用乘机人
        QueryGeneralContactRequest queryGeneralContactRequest = this.genQueryGeneralContactRequest(req, commonPersonQuery, userNo);
        String url = HandlerConstants.URL_FARE_API + HandlerConstants.QUERY_COMMON_PERSON_V20;
        HttpResult httpResult = doPostClient(queryGeneralContactRequest, url, HttpUtil.getHeaderMap(ip, ""));
        if (httpResult.isResult()) {
            if (StringUtil.isNullOrEmpty(httpResult.getResponse())) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("乘机人信息缺失，请重新选择");
                return resp;
            }
            QueryCommonPersonResp respData = new QueryCommonPersonResp();
            QueryGeneralContactResponse queryGeneralContactResponse = (QueryGeneralContactResponse) JsonUtil.jsonToBean(httpResult.getResponse(), QueryGeneralContactResponse.class);
            if (!UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(queryGeneralContactResponse.getResultCode())
                    && !UnifiedOrderResultEnum.NO_PASS.getResultCode().equals(queryGeneralContactResponse.getResultCode())) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo(queryGeneralContactResponse.getErrorInfo());
                return resp;
            }
            // ？？是否有必要填充
            BeanUtils.copyProperties(queryGeneralContactResponse, respData);
            List<QueryCommonPersonInfo> personInfoList = this.responseConvertToQueryCommonPersonInfo(queryGeneralContactResponse);
            List<CertType> userCertTypeListFromUnitOrder = queryGeneralContactResponse.getUseCertTypeList();
            // 开始处理useCertTypeList
            // 默认从本地获取，基于segment从配置文件获取可使用的证件类型(由于实名原因可使用证件类型改为本地获取)
            List<CertType> useCertTypeList = handConfig.getUseCertTypeList(segmentInfo.getSegmentCode());
            if (CollectionUtils.isEmpty(useCertTypeList)) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo("未能获取可使用的证件类型");
                return resp;
            }
            // 军残警残,证件类型为后台接口数据过滤处理
            boolean isGmjc = "Y".equals(commonPersonQuery.getIsGmjc()) && CollectionUtils.isNotEmpty(userCertTypeListFromUnitOrder);
            if (isGmjc) {
                useCertTypeList = userCertTypeListFromUnitOrder.stream().filter(certType ->
                                CertificateTypeEnum.MD_CARD.getShowCode().equals(certType.getCertType())
                                        || CertificateTypeEnum.PD_CARD.getShowCode().equals(certType.getCertType()))
                        .collect(Collectors.toList());
            }

            Boolean isYjYSMember = false; //* 优待证 是否已认证或有效
            if (FlightQueryTypeEnum.YJ_YS.getType().equals(commonPersonQuery.getFlightQueryType())) {
                MemberTagResponse memberTagResponse = iMemberTagService.queryMemberTag(request, channelCode, req.getRequest().getChannelCustomerNo(), "YDZ", false);
                isYjYSMember = memberTagResponse.getIsEffective();
            }
            //暂时恢复原始版本
            if (!ChannelCodeEnum.MOBILE.getChannelCode().equals(headChannelCode) || ver >= 78000) {
                // 开始处理personInfoList
                // 标记是否本人
                this.markOwnPassV2(personInfoList, req, channelCode, request, userCertTypeListFromUnitOrder, isAddOwnPass);
            } else {
                this.markOwnPassV1(personInfoList, req, channelCode, request, userCertTypeListFromUnitOrder, isAddOwnPass);
            }
            // 免票兑换，留学生兑换只显示本人
            if ((isFreeTicketOrIntlStudent && FlightQueryTypeEnum.STUDENT.getType().equals(commonPersonQuery.getFlightQueryType()))
                    || FlightQueryTypeEnum.THEME_TIKTOK_COUPON.getType().equals(commonPersonQuery.getFlightQueryType())
                    || FlightQueryTypeEnum.THEME_SX_TIKTOK_V1_COUPON.getType().equals(commonPersonQuery.getFlightQueryType())
                    || FlightQueryTypeEnum.OPEN_FREE_TICKET.getType().equals(commonPersonQuery.getFlightQueryType())
                    || CollectionUtils.isNotEmpty(commonPersonQuery.getCabinTypes()) &&
                    commonPersonQuery.getCabinTypes().contains(FlightQueryTypeEnum.FIRST_RIDE_MEMBER_FARE.getType())) {
                personInfoList = personInfoList.stream().filter(QueryCommonPersonInfo::getIsOwn).collect(Collectors.toList());
            }


            // 国内拥军优属 仅显示证件类型为身份证的会员本人
            if (FlightQueryTypeEnum.YJ_YS.getType().equals(commonPersonQuery.getFlightQueryType())) {
                personInfoList = personInfoList.stream().filter(QueryCommonPersonInfo::getIsOwn).collect(Collectors.toList());
                personInfoList = this.fliterNI(personInfoList);
                useCertTypeList = this.filterUseCertTypeListNI(userCertTypeListFromUnitOrder);
                for (QueryCommonPersonInfo queryCommonPersonInfo : personInfoList) {
                    if (!StringUtil.isNullOrEmpty(queryCommonPersonInfo.getContactCertList())) {
                        //国内拥军优待资格判定
                        queryCommonPersonInfo.setIsYjYs(false);
                        //YJ_YS
                        if (FlightQueryTypeEnum.YJ_YS.getType().equals(commonPersonQuery.getFlightQueryType())
                                && isYjYSMember && queryCommonPersonInfo.getIsOwn()) {
                            queryCommonPersonInfo.setIsYjYs(true);
                        }
                    }
                }
            }

            // 处理畅飞卡2.0
//            if (CollectionUtils.isNotEmpty(req.getRequest().getCabinTypes()) || req.getRequest().isUnlimitedCardV2()) {
//                personInfoList = handleFreeFlightCard(req, ip, commonPersonQuery, personInfoList);
//            }
            // 乘机人部分信息完善？完善什么
            this.infoComplete(personInfoList, channelCode, ip, commonPersonQuery.getDepartureDate(), commonPersonQuery.getReturnDate(), commonPersonQuery.getCabinTypes());
            //迪士尼特殊处理
            if (!CollectionUtils.isEmpty(commonPersonQuery.getCabinTypes())
                    && commonPersonQuery.getCabinTypes().contains(PackageTypeEnum.DISNEY_FARE.getPackType())) {
                for (QueryCommonPersonInfo queryCommonPersonInfo : personInfoList) {
                    if (StringUtils.isNotBlank(req.getRequest().getDepartureDate())) {
                        queryCommonPersonInfo.setPassengerIdentity(OrderObjectConvert.disneyPassType(queryCommonPersonInfo.getBirthdate(), req.getRequest().getDepartureDate()));
                    }
                }
                personInfoList = personInfoList.stream().filter(personInfo -> !PassengerTypeEnum.INF.getPassType().equals(personInfo.getPassengerIdentity())).collect(Collectors.toList());
            }


            if (isFilterPP) {
                personInfoList = this.fliterPP(personInfoList);
                useCertTypeList = this.filterUseCertTypeListPP(userCertTypeListFromUnitOrder);
            }
            // 国际票销售本人开关打开 且 国际航线
            boolean isOpenAndInternal = HandlerConstants.TRIP_TYPE_I.equals(segmentInfo.getSegmentType()) && "Y".equalsIgnoreCase(handConfig.getPersonInternalSelfFlag());
            if (isOpenAndInternal) {
                personInfoList = personInfoList.stream()
                        .filter(queryCommonPersonInfo -> Boolean.TRUE.equals(queryCommonPersonInfo.getIsOwn())
                                || !PassengerTypeEnum.ADT.getPassType().equals(queryCommonPersonInfo.getPassengerType()))
                        .collect(Collectors.toList());
            }

            // 一单多券下单开关
            boolean isOpenMultiCoupon = "Y".equalsIgnoreCase(handConfig.getOpenMultiCouponFlag());
            // 筛选航线可使用的证件类型 不存在 进行补充,处理personInfoList
            if (isGmjc) {
                this.useCertTypeHand(userCertTypeListFromUnitOrder, personInfoList, segmentInfo);
            } else {
                this.useCertTypeHand(useCertTypeList, personInfoList, segmentInfo);
            }
            boolean isFreeTicket = FlightQueryTypeEnum.FREE_TICKET.getType().equals(commonPersonQuery.getFlightQueryType())
                    || FlightQueryTypeEnum.BUSINESS_FLY_FREE_TICKET.getType().equals(commonPersonQuery.getFlightQueryType());
            // 不开放一单多券 进行受益人处理
            // 开放一单多券 非免票 进行受益人处理
            if (!isOpenMultiCoupon || !isFreeTicket || (isFreeTicket && segmentInfo.isInternal())) {
                // 处理受益人信息
                this.dealUseScoreInfo(personInfoList, request, req, ip, handConfig);
            }

           //新增排序逻辑：新编辑过的，及新增的乘机人自动排至“本人”下方，但位于其他乘机人上方
            personInfoList =personInfoListSorted(personInfoList);

            respData.setUseCertTypeList(useCertTypeList);
            respData.setCommonPersonInfoList(personInfoList);
            respData.setPersonInternalSelfFlag(isOpenAndInternal);
            respData.setMultiCouponFlag(isOpenMultiCoupon);
            respData.setWarmRemind(isOpenAndInternal ? handConfig.getCommonPersonWarmRemind(segmentInfo.getSegmentType()) : null);
            resp.setObjData(respData);
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(httpResult.getResponse());
            return resp;
        }
        return resp;
    }

    /**
     * //新增排序逻辑：新编辑过的，及新增的乘机人自动排至“本人”下方，但位于其他乘机人上方
     * @param personInfoList
     */
    private List<QueryCommonPersonInfo> personInfoListSorted(List<QueryCommonPersonInfo> personInfoList) {
        List<QueryCommonPersonInfo> personInfos  =new ArrayList<>();
        List<QueryCommonPersonInfo> isOwnpersonInfoList= personInfoList.stream().filter(QueryCommonPersonInfo::getIsOwn).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(isOwnpersonInfoList)) {
            personInfos.addAll(isOwnpersonInfoList);
        }
        List<QueryCommonPersonInfo> isNotOwnpersonInfoList= personInfoList.stream().filter(personInfo->!personInfo.getIsOwn()).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(isNotOwnpersonInfoList)) {
            isNotOwnpersonInfoList  =isNotOwnpersonInfoList.stream().sorted(Comparator.comparing(QueryCommonPersonInfo::lastEditTime,
                    Comparator.nullsLast(Comparator.naturalOrder())).reversed()).collect(Collectors.toList());
        }
        personInfos.addAll(isNotOwnpersonInfoList);

        return personInfos;
    }


    @ApiOperation(value = "多次卡绑定人信息", notes = "多次卡绑定人信息")
    @RequestMapping(value = "/commonPersonBindRecord", method = RequestMethod.POST)
    @InterfaceLog
    public BaseResp<QueryCommonPersonResp> commonPersonBindRecord(@RequestBody @Validated BaseReq<CommonPersonCardQuery> req, BindingResult bindingResult, HttpServletRequest request) {
        BaseResp<QueryCommonPersonResp> resp = new BaseResp<>();
        QueryCommonPersonResp queryCommonPersonResp = new QueryCommonPersonResp();
        String ip = this.getClientIP(request);
        String channelCode = req.getChannelCode();
        CommonPersonCardQuery commonPersonQuery = req.getRequest();
        if (commonPersonQuery == null) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo("业务参数不能为空！");
            return resp;
        }
        if (bindingResult.hasErrors()) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
            return resp;
        }
        List<UnlimitedFlyV2BindRecord> unlimitedFlyV2BindRecords = unlimitedFlyService.themeCardBindRecord(req.getChannelCode(), req.getRequest().getChannelCustomerNo(),
                ip, req.getRequest().getFfpCardNo(), req.getRequest().getSearchType());
        List<QueryCommonPersonInfo> queryCommonPersonInfoList = new ArrayList<>();
        try {
            if (CollectionUtils.isNotEmpty(unlimitedFlyV2BindRecords)) {
                // 筛选有效绑定记录
                unlimitedFlyV2BindRecords = unlimitedFlyV2BindRecords.stream().filter(bindRecord ->
                        "yes".equalsIgnoreCase(bindRecord.getBindStatus()) && !"yes".equalsIgnoreCase(bindRecord.getNoShowStatus())
                ).collect(Collectors.toList());
                List<TCountryDTO> tCountryDTOList = basicService.queryCountrys();
                unlimitedFlyV2BindRecords.forEach(unlimitedFlyV2BindRecord -> {
                    QueryCommonPersonInfo queryCommonPersonInfo = new QueryCommonPersonInfo();
                    queryCommonPersonInfo.setCommonContactId(unlimitedFlyV2BindRecord.getBindRecordId());
                    queryCommonPersonInfo.setPassengerType(unlimitedFlyV2BindRecord.getBindingType());
                    String certValidity = null;
                    if (unlimitedFlyV2BindRecord.getCertValidity() != null) {
                        certValidity = DateUtils.dateToString(unlimitedFlyV2BindRecord.getCertValidity(), YYYY_MM_DD_PATTERN);
                    }
                    List<GeneralContactCertInfo> contactCertList = new ArrayList<>();
                    GeneralContactCertInfo generalContactCertInfo = new GeneralContactCertInfo();
                    generalContactCertInfo.setGeneralContactId(unlimitedFlyV2BindRecord.getBindRecordId());
                    generalContactCertInfo.setCertType(unlimitedFlyV2BindRecord.getCertType());
                    generalContactCertInfo.setCouponNo(unlimitedFlyV2BindRecord.getVoucherNo());
                    generalContactCertInfo.setCouponType(unlimitedFlyV2BindRecord.getResourceType());
                    generalContactCertInfo.setBelongCountry(unlimitedFlyV2BindRecord.getBelongCountry());
                    generalContactCertInfo.setCertValidity(certValidity);
                    queryCommonPersonInfo.setNationality(unlimitedFlyV2BindRecord.getNationality());
                    if (unlimitedFlyV2BindRecord.getBirthDate() != null) {
                        queryCommonPersonInfo.setBirthdate(DateUtils.dateToString(unlimitedFlyV2BindRecord.getBirthDate(), YYYY_MM_DD_PATTERN));
                    }
                    queryCommonPersonInfo.setBelongCountry(unlimitedFlyV2BindRecord.getBelongCountry());
                    queryCommonPersonInfo.setHandphoneNo(unlimitedFlyV2BindRecord.getPhoneNumber());
                    if (!StringUtil.isNullOrEmpty(tCountryDTOList)) {
                        TCountryDTO countryDTO = tCountryDTOList.stream().filter(tCountryDTO -> tCountryDTO.getCountryCode().equals(queryCommonPersonInfo.getNationality())).findFirst().orElse(null);
                        TCountryDTO BelongDTO = tCountryDTOList.stream().filter(tCountryDTO -> tCountryDTO.getCountryCode().equals(generalContactCertInfo.getBelongCountry())).findFirst().orElse(null);
                        if (countryDTO != null) {
                            queryCommonPersonInfo.setNationalityName(StringUtil.isNullOrEmpty(countryDTO.getCountryName()) ? countryDTO.getCountryCode() : countryDTO.getCountryName());
                        }
                        if (BelongDTO != null) {
                            generalContactCertInfo.setBelongCountryName(StringUtil.isNullOrEmpty(BelongDTO.getCountryName()) ? BelongDTO.getCountryCode() : BelongDTO.getCountryName());
                        }
                    }
                    queryCommonPersonInfo.setSex(unlimitedFlyV2BindRecord.getSex());
                    queryCommonPersonInfo.setCountryTelCode(unlimitedFlyV2BindRecord.getCountryTelCode());
                    queryCommonPersonInfo.setCertValidity(certValidity);
                    if (PassengerTypeEnum.ADT.getPassType().equals(unlimitedFlyV2BindRecord.getBindingType())) {
                        queryCommonPersonInfo.setPassengerName(unlimitedFlyV2BindRecord.getAdultName());
                        queryCommonPersonInfo.setPassEnNameS(unlimitedFlyV2BindRecord.getAdultElastName());
                        queryCommonPersonInfo.setPassEnNameF(unlimitedFlyV2BindRecord.getAdultEfirstName());
                        generalContactCertInfo.setCertNo(unlimitedFlyV2BindRecord.getAultIdNumber());
                    } else {
                        queryCommonPersonInfo.setPassengerName(unlimitedFlyV2BindRecord.getChildCnName());
                        queryCommonPersonInfo.setPassEnNameS(unlimitedFlyV2BindRecord.getChildElastName());
                        queryCommonPersonInfo.setPassEnNameF(unlimitedFlyV2BindRecord.getChildEfirstName());
                        generalContactCertInfo.setCertNo(unlimitedFlyV2BindRecord.getChildIdNumber());
                    }
                    contactCertList.add(generalContactCertInfo);
                    queryCommonPersonInfo.setContactCertList(contactCertList);
                    queryCommonPersonInfoList.add(queryCommonPersonInfo);
                });
            }
        } catch (Exception e) {
            log.error("多次卡绑定人信息", e);
        }
        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
        resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
        queryCommonPersonResp.setCommonPersonInfoList(queryCommonPersonInfoList);
        resp.setObjData(queryCommonPersonResp);
        return resp;
    }

    /**
     * /Book/QueryGeneralContact response to QueryCommonPersonInfo
     *
     * @param queryGeneralContactResponse
     * @return
     */
    private List<QueryCommonPersonInfo> responseConvertToQueryCommonPersonInfo(QueryGeneralContactResponse queryGeneralContactResponse) {
        List<QueryCommonPersonInfo> personInfoList = new ArrayList<>();
        if (!StringUtil.isNullOrEmpty(queryGeneralContactResponse.getGeneralContactList())) {
            for (GeneralContactInfo generalContactInfo : queryGeneralContactResponse.getGeneralContactList()) {
                QueryCommonPersonInfo queryCommonPersonInfo = new QueryCommonPersonInfo();
                BeanUtils.copyProperties(generalContactInfo, queryCommonPersonInfo);
                // 生僻字拼音转大写
                if (StringUtils.isNotBlank(generalContactInfo.getPassengerName())) {
                    queryCommonPersonInfo.setPassengerName(generalContactInfo.getPassengerName().toUpperCase());
                }
                queryCommonPersonInfo.setCommonContactId(generalContactInfo.getGeneralContactId());
                queryCommonPersonInfo.setIsOwn(false);
                queryCommonPersonInfo.setIsCrmData(false);
                personInfoList.add(queryCommonPersonInfo);
            }
        }
        return personInfoList;
    }

    private List<QueryCommonPersonInfo> fliterPP(List<QueryCommonPersonInfo> personInfoList) {
        personInfoList.forEach(commonPersonInfo ->
                commonPersonInfo.setContactCertList(commonPersonInfo.getContactCertList().stream().filter(generalContactCertInfo
                                -> CertificateTypeEnum.PASSPORT.getShowCode().equals(generalContactCertInfo.getCertType()))
                        .collect(Collectors.toList())));
        return personInfoList;
    }


    private List<QueryCommonPersonInfo> fliterNI(List<QueryCommonPersonInfo> personInfoList) {
        personInfoList.forEach(commonPersonInfo ->
                commonPersonInfo.setContactCertList(commonPersonInfo.getContactCertList().stream().filter(generalContactCertInfo
                                -> CertificateTypeEnum.ID_CARD.getShowCode().equals(generalContactCertInfo.getCertType()))
                        .collect(Collectors.toList())));
        return personInfoList;
    }

    /**
     * 畅飞卡2.0
     *
     * @param req
     * @param ip
     * @param commonPersonQuery
     * @param personInfoList
     * @return
     */
    private List<QueryCommonPersonInfo> handleFreeFlightCard(BaseReq<CommonPersonQuery> req, String ip, CommonPersonQuery commonPersonQuery, List<QueryCommonPersonInfo> personInfoList) {
        // 畅飞卡2.0存在成人付费购买普通舱位的情况，另加字段处理
        if (req.getRequest().getCabinTypes().contains(PackageTypeEnum.CHD_UNLIMITED_FLY.getPackType())
                || req.getRequest().getCabinTypes().contains(PackageTypeEnum.ADT_UNLIMITED_FLY.getPackType())) {
            personInfoList = dealUnlimitedFly(personInfoList, req, ip, req.getRequest().getCabinTypes().contains(PackageTypeEnum.ADT_UNLIMITED_FLY.getPackType()));
        } else if (req.getRequest().getCabinTypes().contains(PackageTypeEnum.UNLIMITED_FARE_V2.getPackType()) || req.getRequest().isUnlimitedCardV2()) {
            UnlimitedCard2Config unlimitedCard2Config = handConfig.getUnlimitedCard2Config();
            boolean springFestival = false;
            Date limitedDateBegin = DateUtils.toDate(unlimitedCard2Config.getBCardUnusableFlightTimeBegin(), DateUtils.YYYY_MM_DD_HH_MM_SS_PATTERN);
            Date limitedDateEnd = DateUtils.toDate(unlimitedCard2Config.getBCardUnusableFlightTimeEnd(), DateUtils.YYYY_MM_DD_HH_MM_SS_PATTERN);
            Date flightDate = DateUtils.toDate(req.getRequest().getDepartureDate(), DateUtils.YYYY_MM_DD_PATTERN);
            if (null != limitedDateBegin && null != limitedDateEnd && null != flightDate
                    && flightDate.getTime() <= limitedDateEnd.getTime() && flightDate.getTime() >= limitedDateBegin.getTime()) {
                springFestival = true;
            }
            personInfoList = dealUnlimitedFlyV2(personInfoList, req, ip, springFestival);
        } else if (!Collections.disjoint(commonPersonQuery.getCabinTypes(), handConfig.getThemeCouponList())) {
            personInfoList = dealUnlimitedTheme(personInfoList, req, ip, commonPersonQuery.getCabinTypes(),
                    commonPersonQuery.getDepartureDate(), commonPersonQuery.getReturnDate());
        }
        return personInfoList;
    }

    /**
     * @param req
     * @param commonPersonQuery
     * @param userNo
     * @return
     */
    private QueryGeneralContactRequest genQueryGeneralContactRequest(BaseReq<CommonPersonQuery> req, CommonPersonQuery commonPersonQuery, String userNo) {

        QueryGeneralContactRequest queryGeneralContactRequest = new QueryGeneralContactRequest(HandlerConstants.VERSION, req.getChannelCode(), userNo, "CRM", "LastBooking");
        queryGeneralContactRequest.setChannelCustomerNo(commonPersonQuery.getChannelCustomerNo());
        queryGeneralContactRequest.setIsGMJC(commonPersonQuery.getIsGmjc());

        // 多程参数未实装,前端未传值
        if (CollectionUtils.isNotEmpty(commonPersonQuery.getSegments())) {
            // 多程机票下单， 优先使用国际城市查询，其次使用港澳台城市查询
            for (QueryCommonPersonSegment segment : commonPersonQuery.getSegments()) {
                CityInfoDto dept = localCacheService.getLocalCity(segment.getDepCityCode());
                CityInfoDto arr = localCacheService.getLocalCity(segment.getArrCityCode());
                boolean HKMCTWRegion = false;
                boolean international = false;
                if (HandlerConstants.TRIP_TYPE_I.equals(dept.getIsInternational())) {
                    if (handConfig.getHKMCTWRegions().contains(segment.getDepCityCode())) {
                        HKMCTWRegion = true;
                    } else {
                        international = true;
                    }
                }
                if (HandlerConstants.TRIP_TYPE_I.equals(arr.getIsInternational())) {
                    if (handConfig.getHKMCTWRegions().contains(segment.getArrCityCode())) {
                        HKMCTWRegion = true;
                    } else {
                        international = true;
                    }
                }
                segment.setHKMCTWRegion(HKMCTWRegion);
                segment.setInterNational(international);
            }
            Optional<QueryCommonPersonSegment> segment = commonPersonQuery.getSegments().stream().filter(QueryCommonPersonSegment::isInterNational).findFirst();
            if (!segment.isPresent()) {
                segment = commonPersonQuery.getSegments().stream().filter(QueryCommonPersonSegment::isHKMCTWRegion).findFirst();
            }
            if (!segment.isPresent()) {
                segment = commonPersonQuery.getSegments().stream().findFirst();
            }
            if (segment.isPresent()) {
                commonPersonQuery.setDepCityCode(segment.get().getDepCityCode());
                commonPersonQuery.setArrCityCode(segment.get().getArrCityCode());
            }
            // 出发时间取最大的出发时间
            commonPersonQuery.getSegments().sort(Comparator.comparing(QueryCommonPersonSegment::getDepartureDate).reversed());
            commonPersonQuery.setDepartureDate(commonPersonQuery.getSegments().get(0).getDepartureDate());
        }
        queryGeneralContactRequest.setPageNo(commonPersonQuery.getPageNo());
        queryGeneralContactRequest.setPageSize(commonPersonQuery.getPageSize());
        if (CollectionUtils.isNotEmpty(commonPersonQuery.getCabinTypes())) {
            if (commonPersonQuery.getCabinTypes().contains(PackageTypeEnum.CHD_UNLIMITED_FLY.getPackType())
                    || commonPersonQuery.getCabinTypes().contains(PackageTypeEnum.ADT_UNLIMITED_FLY.getPackType())) {
                queryGeneralContactRequest.setContactType(Arrays.asList("A", "B"));
            } else if (commonPersonQuery.getCabinTypes().contains(PackageTypeEnum.UNLIMITED_FARE_V2.getPackType())) {
                queryGeneralContactRequest.setContactType(Arrays.asList("C", "D"));
            } else if (!Collections.disjoint(commonPersonQuery.getCabinTypes(), handConfig.getThemeCouponList())) {
                queryGeneralContactRequest.setContactType(commonPersonQuery.getCabinTypes());
            }
        }
        return queryGeneralContactRequest;
    }

    /**
     * 筛选航线可使用的证件类型 不存在 进行补充
     *
     * @param useCertTypeList
     * @param personInfoList
     * @param segment
     */
    private void useCertTypeHand(List<CertType> useCertTypeList, List<QueryCommonPersonInfo> personInfoList, Segment segment) {
        // 无航线信息不需要补充和筛选
        if (StringUtils.isBlank(segment.getSegmentCode())) {
            return;
        }
        //主题卡可使用的证件类型不需要补充
        if (CollectionUtils.isNotEmpty(personInfoList)
                && personInfoList.stream().allMatch(queryCommonPersonInfo ->
                StringUtils.isNotEmpty(queryCommonPersonInfo.getContactType())
                        && queryCommonPersonInfo.getContactType().matches(PatternCommon.TOTALTYPE))) {
            return;
        }
        // 可使用证件类型清单
        List<String> certTypeCodeList = Lists.newArrayList();
        useCertTypeList.forEach(certType -> certTypeCodeList.add(certType.getCertType()));
        // 筛选并指定证件类型
        for (QueryCommonPersonInfo queryCommonPersonInfo : personInfoList) {
            /*if (queryCommonPersonInfo.getIsOwn()) {
                continue;
            }*/
            Map<String, List<GeneralContactCertInfo>> map = Maps.newHashMap();
            List<GeneralContactCertInfo> contactCertList = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(queryCommonPersonInfo.getContactCertList())) {
                queryCommonPersonInfo.getContactCertList().forEach(contactCertInfo -> {
                    List<GeneralContactCertInfo> list = map.get(contactCertInfo.getCertType());
                    if (null == list) {
                        list = Lists.newArrayList();
                    }
                    list.add(contactCertInfo);
                    map.put(contactCertInfo.getCertType(), list);
                });
            }
            certTypeCodeList.forEach(certTypeCode -> {
                List<GeneralContactCertInfo> list = map.get(certTypeCode);
                if (null == list) {
                    GeneralContactCertInfo generalContactCertInfo = new GeneralContactCertInfo();
                    generalContactCertInfo.setCertType(certTypeCode);
                    contactCertList.add(generalContactCertInfo);
                } else {
                    contactCertList.addAll(list);
                }
            });
            queryCommonPersonInfo.setContactCertList(contactCertList);
        }
    }

    /**
     * 过滤护照
     *
     * @param useCertTypeList
     * @return
     */
    private List<CertType> filterUseCertTypeListPP(List<CertType> useCertTypeList) {
        if (CollectionUtils.isNotEmpty(useCertTypeList)) {
            return useCertTypeList.stream().filter(certType -> CertificateTypeEnum.PASSPORT.getShowCode().equals(certType.getCertType())).collect(Collectors.toList());
        }
        return useCertTypeList;
    }


    private List<CertType> filterUseCertTypeListNI(List<CertType> useCertTypeList) {
        if (CollectionUtils.isNotEmpty(useCertTypeList)) {
            return useCertTypeList.stream().filter(certType -> CertificateTypeEnum.ID_CARD.getShowCode().equals(certType.getCertType())).collect(Collectors.toList());
        }
        return useCertTypeList;
    }

    /**
     * 处理受益人信息，异常时不影响正常查询
     */
    private void dealUseScoreInfo(List<QueryCommonPersonInfo> queryCommonPersonInfoList, HttpServletRequest request, BaseReq<CommonPersonQuery> req, String ip, HandConfig handConfig) {
        if (null != DateUtils.toDate(handConfig.getLimitScoreUseDate()) && DateUtils.toDate(handConfig.getLimitScoreUseDate()).before(new Date())) {
            try {
                PtCrmMileageRequest ptCrmMileageRequest = buildCommCrmReq(request, req.getChannelCode());
                ptCrmMileageRequest.setData(new MemberBeneficiaryRequest(Integer.parseInt(req.getRequest().getChannelCustomerNo()), Collections.singletonList("A"), null));
                List<MemberBeneficiaryDTO> beneficiaryDTOList = this.beneficiaryService.listEffectiveBeneficiaryInfoRecord(ptCrmMileageRequest, ip);
                // 筛选生效的受益人
                beneficiaryDTOList.forEach(memberBeneficiaryDTO -> {
                    if (CollectionUtils.isNotEmpty(memberBeneficiaryDTO.getCertificate())) {
                        // 身份证、港澳台居民居住证匹配中文姓名
                        // 受益人证件是身份证且证件号不为空
                        memberBeneficiaryDTO.getCertificate().stream().filter(beneficiaryCertificate -> (CertificateTypeEnum.ID_CARD.getCode() == beneficiaryCertificate.getCtype() || CertificateTypeEnum.HMT_ID_CARD.getCode() == beneficiaryCertificate.getCtype()) && StringUtils.isNotBlank(beneficiaryCertificate.getCnumber()))
                                // 常用乘机人中文名不为空且与受益人匹配且证件号列表不为空
                                .forEach(beneficiaryCertificate -> queryCommonPersonInfoList.stream().filter(person -> StringUtils.isNotBlank(person.getPassengerName()) && person.getPassengerName().equals(memberBeneficiaryDTO.getCLastName() + memberBeneficiaryDTO.getCFirstName()) && CollectionUtils.isNotEmpty(person.getContactCertList()))
                                        // 常用乘机人证件与受益人证件匹配
                                        .forEach(person -> person.getContactCertList().stream().filter(personCert -> beneficiaryCertificate.getCtype() == CertificateTypeEnum.checkShowCode(personCert.getCertType()).getCode() && beneficiaryCertificate.getCnumber().equals(personCert.getCertNo()))
                                                .forEach(certificate -> certificate.setUseScore(true)))
                                );
                        // 非身份证号匹配英文姓名
                        // 受益人证件不是身份证且证件号不为空
                        memberBeneficiaryDTO.getCertificate().stream()
                                .filter(beneficiaryCertificate -> CertificateTypeEnum.ID_CARD.getCode() != beneficiaryCertificate.getCtype() && CertificateTypeEnum.HMT_ID_CARD.getCode() != beneficiaryCertificate.getCtype() && StringUtils.isNotBlank(beneficiaryCertificate.getCnumber()))
                                // 常用乘机人英文姓/名不为空且与受益人匹配且证件号列表不为空
                                .forEach(beneficiaryCertificate -> queryCommonPersonInfoList.stream()
                                        .filter(person ->
                                                StringUtils.isNotBlank(person.getPassEnNameS())
                                                        && StringUtils.isNotBlank(person.getPassEnNameF())
                                                        && person.getPassEnNameF().equals(memberBeneficiaryDTO.getEFirstName())
                                                        && person.getPassEnNameS().equals(memberBeneficiaryDTO.getELastName())
                                                        && CollectionUtils.isNotEmpty(person.getContactCertList()))
                                        // 常用乘机人证件与受益人证件匹配
                                        .forEach(person -> person.getContactCertList().stream()
                                                .filter(personCert ->
                                                        beneficiaryCertificate.getCtype() == CertificateTypeEnum.checkShowCode(personCert.getCertType()).getCode()
                                                                && beneficiaryCertificate.getCnumber().equals(personCert.getCertNo()))
                                                .forEach(certificate -> certificate.setUseScore(true)))
                                );
                    }
                });
                if (FlightQueryTypeEnum.FREE_TICKET.getType().equals(req.getRequest().getFlightQueryType())
                        || FlightQueryTypeEnum.AWARD_FLY_FREE_TICKET.getType().equals(req.getRequest().getFlightQueryType())
                        || FlightQueryTypeEnum.BUSINESS_FLY_FREE_TICKET.getType().equals(req.getRequest().getFlightQueryType())) {
                    toMarkBeneficiary(queryCommonPersonInfoList, beneficiaryDTOList);
                }
            } catch (Exception e) {
                log.error("查询受益人信息出现异常", e);
            }
        } else {
            queryCommonPersonInfoList.forEach(commonPersonInfo -> {
                if (CollectionUtils.isNotEmpty(commonPersonInfo.getContactCertList())) {
                    commonPersonInfo.getContactCertList().forEach(generalContactCertInfo -> generalContactCertInfo.setUseScore(true));
                }
            });
        }

    }

    /**
     * 标记受益人
     *
     * @param queryCommonPersonInfoList
     * @param beneficiaryDTOList
     */
    private void toMarkBeneficiary(List<QueryCommonPersonInfo> queryCommonPersonInfoList, List<MemberBeneficiaryDTO> beneficiaryDTOList) {
        List<QueryCommonPersonInfo> finalPersonInfoList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(queryCommonPersonInfoList) || CollectionUtils.isNotEmpty(beneficiaryDTOList)) {
            List<QueryCommonPersonInfo> ownPersons = queryCommonPersonInfoList.stream().filter(e -> null != e.getIsOwn() && StringUtils.isNotEmpty(e.getPassengerType()) && e.getIsOwn() && "ADT".equals(e.getPassengerType())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(ownPersons)) {
                //将本人的是否可用积分复制给证件是否可用
                for (QueryCommonPersonInfo ownPerson : ownPersons
                ) {
                    if (CollectionUtils.isNotEmpty(ownPerson.getContactCertList())) {
                        for (GeneralContactCertInfo generalContact : ownPerson.getContactCertList()
                        ) {
                            generalContact.setValid(generalContact.isUseScore());
                        }
                    }

                }
                finalPersonInfoList.addAll(ownPersons);
            }

            List<QueryCommonPersonInfo> notOwnPersons = queryCommonPersonInfoList.stream().filter(e -> null == e.getIsOwn() || (null != e.getIsOwn() && !e.getIsOwn()) && StringUtils.isNotEmpty(e.getPassengerType()) && "ADT".equals(e.getPassengerType())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(notOwnPersons)) {
                for (QueryCommonPersonInfo queryCommonPersonInfo : notOwnPersons) {
                    if (!StringUtil.isNullOrEmpty(queryCommonPersonInfo.getContactCertList())) {
                        boolean isBeneficial = false;
                        for (GeneralContactCertInfo generalContactCertInfo : queryCommonPersonInfo.getContactCertList()) {
                            boolean isValid = false;
                            if (!StringUtil.isNullOrEmpty(generalContactCertInfo.getCertNo())) {
                                for (MemberBeneficiaryDTO memberBeneficiaryDTO : beneficiaryDTOList) {
                                    if (CollectionUtils.isNotEmpty(memberBeneficiaryDTO.getCertificate())) {
                                        for (MemberBeneficiaryCertificateDTO beneficiaryCertificate : memberBeneficiaryDTO.getCertificate()
                                        ) {
                                            CertificateTypeEnum memberCertEnum = CertificateTypeEnum.checkType(beneficiaryCertificate.getCtype());
                                            if (memberCertEnum != null && (memberCertEnum.getShowCode().equals(generalContactCertInfo.getCertType())
                                                    && generalContactCertInfo.getCertNo().equals(beneficiaryCertificate.getCnumber()))
                                                    // 处理CRM那边记录的其他类型的证件
                                                    || (memberCertEnum == CertificateTypeEnum.OTHER
                                                    && generalContactCertInfo.getCertNo().equals(beneficiaryCertificate.getCnumber()))) {
                                                // 不存在英文姓名 或 英文姓名匹配
                                                boolean enName = (StringUtils.isBlank(queryCommonPersonInfo.getPassEnNameS()) && StringUtils.isBlank(queryCommonPersonInfo.getPassEnNameF())) || toMatchEnName(memberBeneficiaryDTO, queryCommonPersonInfo);
                                                // 身份证匹配姓名
                                                if ((CertificateTypeEnum.ID_CARD.equals(memberCertEnum) || CertificateTypeEnum.HMT_ID_CARD.equals(memberCertEnum))
                                                        && (memberBeneficiaryDTO.getCLastName() + memberBeneficiaryDTO.getCFirstName()).equals(queryCommonPersonInfo.getPassengerName()) && enName) {
                                                    isBeneficial = true;
                                                    isValid = true;
                                                } else if (!CertificateTypeEnum.ID_CARD.equals(memberCertEnum) && !CertificateTypeEnum.HMT_ID_CARD.equals(memberCertEnum)
                                                        && toMatchEnName(memberBeneficiaryDTO, queryCommonPersonInfo)) {
                                                    // 其他证件匹配英文姓名
                                                    isBeneficial = true;
                                                    isValid = true;
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            generalContactCertInfo.setValid(isValid);
                        }
                        queryCommonPersonInfo.setIsBeneficiary(isBeneficial);
                    }
                }
            }
            List<QueryCommonPersonInfo> beneficialPersons = notOwnPersons.stream().filter(e -> null != e.getIsOwn() && null != e.getIsBeneficiary() && (e.getIsOwn() || e.getIsBeneficiary())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(beneficialPersons)) {
                finalPersonInfoList.addAll(beneficialPersons);
            }

        }
        queryCommonPersonInfoList.clear();
        if (CollectionUtils.isNotEmpty(finalPersonInfoList)) {
            queryCommonPersonInfoList.addAll(finalPersonInfoList);
        } else {
            List<QueryCommonPersonInfo> ownPersons = queryCommonPersonInfoList.stream().filter(e -> null != e.getIsOwn() && e.getIsOwn() && StringUtils.isNotEmpty(e.getPassengerType()) && "ADT".equals(e.getPassengerType())).collect(Collectors.toList());
            queryCommonPersonInfoList.addAll(ownPersons);
        }
    }

    private List<QueryCommonPersonInfo> dealUnlimitedFly(List<QueryCommonPersonInfo> queryCommonPersonInfoList, BaseReq<CommonPersonQuery> req, String ip, boolean adtUnlimitedFly) {
        List<QueryCommonPersonInfo> result = Lists.newArrayList();
        List<QueryCommonPersonInfo> ownerList = queryCommonPersonInfoList.stream().filter(QueryCommonPersonInfo::getIsOwn).collect(Collectors.toList());
        ownerList.forEach(commonPersonInfo -> commonPersonInfo.getContactCertList().stream()
                .filter(cert -> StringUtils.isNotBlank(cert.getCertNo())).forEach(cert -> cert.setCabinType(CommonPersonCabinType.CUF_ADT.getCode())));
        List<QueryCommonPersonInfo> chdUnlimitedFlyList = Lists.newArrayList();
        try {
            List<UnlimitedFlyBindRecord> childFlyBindRecords = unlimitedFlyService.listChdBindRecord(req.getChannelCode(), req.getRequest().getChannelCustomerNo(), ip);
            if (CollectionUtils.isNotEmpty(childFlyBindRecords)) {
                childFlyBindRecords = childFlyBindRecords.stream().filter(bindRecord -> "yes".equalsIgnoreCase(bindRecord.getBindStatus())
                ).collect(Collectors.toList());
                for (UnlimitedFlyBindRecord childFlyBindRecord : childFlyBindRecords) {
                    for (QueryCommonPersonInfo queryCommonPersonInfo : queryCommonPersonInfoList) {
                        if (PassengerTypeEnum.CHD.getPassType().equals(queryCommonPersonInfo.getPassengerType())
                                && CollectionUtils.isNotEmpty(queryCommonPersonInfo.getContactCertList())) {
                            // 儿童畅飞卡支持使用身份证或护照
                            List<GeneralContactCertInfo> contactCertInfos = queryCommonPersonInfo.getContactCertList().stream().filter(generalContactCertInfo ->
                                    CertificateTypeEnum.PASSPORT.getShowCode().equals(generalContactCertInfo.getCertType())
                                            || CertificateTypeEnum.ID_CARD.getShowCode().equals(generalContactCertInfo.getCertType())).collect(Collectors.toList());
                            for (GeneralContactCertInfo certInfo : queryCommonPersonInfo.getContactCertList()) {
                                boolean match = false;
                                // 身份证号相同且中文名相同允许使用儿童随心飞
                                if (CertificateTypeEnum.ID_CARD.getShowCode().equals(certInfo.getCertType())) {
                                    if (childFlyBindRecord.getChildIdNumber().equals(certInfo.getCertNo())
                                            && childFlyBindRecord.getChildCnName().equals(queryCommonPersonInfo.getPassengerName())) {
                                        match = true;
                                    }
                                } else {//其他证件目前匹配英文姓名一致
                                    if (childFlyBindRecord.getChildEnName().equals(queryCommonPersonInfo.getPassEnNameS() + "/" + queryCommonPersonInfo.getPassEnNameF())) {
                                        match = true;
                                    }
                                }
        /*                        if (CertificateTypeEnum.ID_CARD.getShowCode().equals(certInfo.getCertType())
                                        && childFlyBindRecord.getChildIdNumber().equals(certInfo.getCertNo())
                                        && childFlyBindRecord.getChildCnName().equals(queryCommonPersonInfo.getPassengerName()))*/
                                if (match) {
                                    contactCertInfos.forEach(useCert -> {
                                        useCert.setCabinType(CommonPersonCabinType.CUF_CHD.getCode());
                                        useCert.setCouponNo(childFlyBindRecord.getVoucherNo());
                                    });
                                    queryCommonPersonInfo.setContactCertList(contactCertInfos);
                                    chdUnlimitedFlyList.add(queryCommonPersonInfo);
                                    break;
                                }
                            }
                        }
                    }
                }
            }
            List<UnlimitedFlyBindRecord> adultFlyBindRecords = unlimitedFlyService.listFlyCardBindRecord(req.getChannelCode(), req.getRequest().getChannelCustomerNo(), ip);
            if (CollectionUtils.isNotEmpty(adultFlyBindRecords)) {
                Optional<UnlimitedFlyBindRecord> adultFlyBindRecord = adultFlyBindRecords.stream().filter(bindRecord -> "yes".equalsIgnoreCase(bindRecord.getBindStatus())).findFirst();
                adultFlyBindRecord.ifPresent(bindRecord ->
                        ownerList.forEach(owner -> {
                            for (GeneralContactCertInfo certInfo : owner.getContactCertList()) {
                                if (adtUnlimitedFly) {
                                    certInfo.setCabinType(CommonPersonCabinType.UF_ADT.getCode());
                                    certInfo.setCouponNo(bindRecord.getVoucherNo());
                                } else {
                                    certInfo.setCabinType(CommonPersonCabinType.CUF_ADT.getCode());
                                }
                            }
                        })
                );
            }
        } catch (ExpectableException e) {
            log.error("查询随心飞绑定记录出现异常", e);
        }
        result.addAll(ownerList);
        result.addAll(chdUnlimitedFlyList);
        return result;
    }


    private List<QueryCommonPersonInfo> dealUnlimitedTheme(List<QueryCommonPersonInfo> queryCommonPersonInfoList, BaseReq<CommonPersonQuery> req,
                                                           String ip, List<String> cabinTypes, String departureDate, String returnDate) {
        Set<QueryCommonPersonInfo> result = new HashSet<>();
        try {
            List<UnlimitedFlyV2BindRecord> unlimitedFlyV2BindRecords = unlimitedFlyService.ThemeCouponBindRecord(req.getChannelCode(), req.getRequest().getChannelCustomerNo(), ip, cabinTypes, req.getRequest().getFfpCardNo());


            if (CollectionUtils.isNotEmpty(unlimitedFlyV2BindRecords)) {
                // 筛选有效绑定记录
                unlimitedFlyV2BindRecords = unlimitedFlyV2BindRecords.stream().filter(bindRecord ->
                        "yes".equalsIgnoreCase(bindRecord.getBindStatus()) && !"yes".equalsIgnoreCase(bindRecord.getNoShowStatus())
                ).collect(Collectors.toList());
                for (UnlimitedFlyV2BindRecord bindRecord : unlimitedFlyV2BindRecords) {
                    queryCommonPersonInfoList.forEach(commonPersonInfo -> bindingMatch(commonPersonInfo, bindRecord));
                }
                //乘机人证件过滤
                queryCommonPersonInfoList.forEach(queryCommonPersonInfo -> {
                    List<GeneralContactCertInfo> newQueryCommonPerson = queryCommonPersonInfo.getContactCertList().stream().filter(commonPersonQuery -> !StringUtil.isNullOrEmpty(commonPersonQuery.getCertNo())).collect(Collectors.toList());
                    queryCommonPersonInfo.setContactCertList(newQueryCommonPerson);
                });

                //按照兑换航班日期和儿童生日日期作比较
                queryCommonPersonInfoList.forEach(queryCommonPersonInfo -> {
                    if (StringUtils.isNotBlank(departureDate)) {
                        queryCommonPersonInfo.setPassengerType(OrderObjectConvert.adjustPassType(queryCommonPersonInfo.getBirthdate(), departureDate));
                    }
                    if (StringUtils.isNotBlank(returnDate)) {
                        queryCommonPersonInfo.setPassengerType(OrderObjectConvert.adjustPassType(queryCommonPersonInfo.getBirthdate(), returnDate));
                    }
                });

                //主题卡判断
                queryCommonPersonInfoList = queryCommonPersonInfoList.stream()
                        .filter(passenger -> cabinTypes.get(0).equals(passenger.getContactType())).collect(Collectors.toList());
                // 成人乘机人
                Optional<QueryCommonPersonInfo> ownPassenger = queryCommonPersonInfoList.stream().filter(passenger ->
                        PassengerTypeEnum.ADT.getPassType().equals(passenger.getPassengerType()) && passenger.getIsOwn()
                ).findFirst();
                // 儿童乘机人
                List<QueryCommonPersonInfo> chdPassenger = queryCommonPersonInfoList.stream()
                        .filter(passenger -> PassengerTypeEnum.CHD.getPassType().equals(passenger.getPassengerType())
                                /*绑定记录未匹配上的不展示*/).collect(Collectors.toList());
                ownPassenger.ifPresent(passenger -> result.add(passenger));

                result.addAll(chdPassenger);
            }
        } catch (ExpectableException e) {
            log.error("查询畅飞卡飞绑定记录出现异常", e);
        }
        return Lists.newArrayList(result);
    }

    private List<QueryCommonPersonInfo> dealUnlimitedFlyV2(List<QueryCommonPersonInfo> queryCommonPersonInfoList, BaseReq<CommonPersonQuery> req, String ip, boolean springFestival) {
        Set<QueryCommonPersonInfo> result = new HashSet<>();
        try {
            List<UnlimitedFlyV2BindRecord> unlimitedFlyV2BindRecords = unlimitedFlyService.listFlyCard2BindRecord(req.getChannelCode(), req.getRequest().getChannelCustomerNo(), ip, req.getRequest().getFfpCardNo());
            if (CollectionUtils.isNotEmpty(unlimitedFlyV2BindRecords)) {
                // 筛选有效绑定记录
                unlimitedFlyV2BindRecords = unlimitedFlyV2BindRecords.stream().filter(bindRecord ->
                        "yes".equalsIgnoreCase(bindRecord.getBindStatus()) && !"yes".equalsIgnoreCase(bindRecord.getNoShowStatus())
                ).collect(Collectors.toList());
                for (UnlimitedFlyV2BindRecord bindRecord : unlimitedFlyV2BindRecords) {
                    queryCommonPersonInfoList.forEach(commonPersonInfo -> bindingMatch(commonPersonInfo, bindRecord));
                }
                // 成人乘机人
                Optional<QueryCommonPersonInfo> ownPassenger = queryCommonPersonInfoList.stream().filter(passenger ->
                        PassengerTypeEnum.ADT.getPassType().equals(passenger.getPassengerType()) && passenger.getIsOwn()
                ).findFirst();
                // 儿童乘机人
                List<QueryCommonPersonInfo> chdPassenger = queryCommonPersonInfoList.stream()
                        .filter(passenger -> PassengerTypeEnum.CHD.getPassType().equals(passenger.getPassengerType())
                                && StringUtils.isNotBlank(passenger.getUnlimitedCardType())/*绑定记录未匹配上的不展示*/).collect(Collectors.toList());
                // 设置提示信息
                if (springFestival) {
                    // 成人未购买春运卡，儿童购买了春运卡，且购买春运期间航班，提示信息并允许正价购买
                    ownPassenger.ifPresent(passenger -> {
                        if (VoucherTypesEnum.UNLIMITED_FLY_V2.getCode().equals(passenger.getUnlimitedCardType())) {
                            passenger.getContactCertList().forEach(cert -> cert.setCouponNo(null));// 不使用畅飞卡
                            passenger.setMessageType(CommonPersonMessageType.UNLIMITED_FLY_V2_PAY.getCode());
                            passenger.setAlertMessage("乘机人：" + passenger.getPassengerName() + "未购买吉祥畅飞卡春运版，预订该航班将会按照正价收费，确认预订吗？");
                        } else if (StringUtils.isBlank(passenger.getUnlimitedCardType())) {
                            // 成人未购卡，儿童购买春运版
                            passenger.setMessageType(CommonPersonMessageType.UNLIMITED_FLY_V2_NOT_PURCHASED.getCode());
                            passenger.setAlertMessage("乘机人：" + passenger.getPassengerName() + "未购买吉祥畅飞卡，预订该航班将会按照正价收费，确认预订吗？");
                        }
                    });
                    // 成人购买了春运卡，儿童购买了非春运卡，且购买春运期间航班，儿童不可购买并提示
                    if (CollectionUtils.isNotEmpty(chdPassenger)) {
                        chdPassenger.forEach(passenger -> {
                            if (VoucherTypesEnum.UNLIMITED_FLY_V2.getCode().equals(passenger.getUnlimitedCardType())) {
                                passenger.setMessageType(CommonPersonMessageType.NOT_ALLOWED.getCode());
                                passenger.setAlertMessage("乘机人：" + passenger.getPassengerName() + "未购买吉祥畅飞卡春运版，不可以预订该航班");
                            }
                        });
                    }
                } else {
                    // 成人未购卡，儿童购买卡，提示信息并允许正价购买
                    ownPassenger.ifPresent(passenger -> {
                        if (StringUtils.isBlank(passenger.getUnlimitedCardType())) {
                            passenger.setMessageType(CommonPersonMessageType.UNLIMITED_FLY_V2_NOT_PURCHASED.getCode());
                            passenger.setAlertMessage("乘机人：" + passenger.getPassengerName() + "未购买吉祥畅飞卡，预订该航班将会按照正价收费，确认预订吗？");
                        }
                    });
                }
                ownPassenger.ifPresent(passenger -> result.add(passenger));
                result.addAll(chdPassenger);
            }
        } catch (ExpectableException e) {
            log.error("查询畅飞卡飞绑定记录出现异常", e);
        }
        return Lists.newArrayList(result);
    }

    /**
     * 判断是否绑定并设置绑定信息
     *
     * @param commonPersonInfo
     * @param bindRecord
     * @return
     */
    private boolean bindingMatch(QueryCommonPersonInfo commonPersonInfo, UnlimitedFlyV2BindRecord bindRecord) {
        if (CollectionUtils.isEmpty(commonPersonInfo.getContactCertList())) {
            return false;
        }
        boolean match = false;
        if (PassengerTypeEnum.ADT.getPassType().equals(commonPersonInfo.getPassengerType())) {
            for (GeneralContactCertInfo certInfo : commonPersonInfo.getContactCertList()) {
                // 身份证号相同且中文名相同允许使用畅飞卡
                if (CertificateTypeEnum.ID_CARD.getShowCode().equals(certInfo.getCertType())) {
                    if (!StringUtils.isAnyBlank(bindRecord.getAultIdNumber(), bindRecord.getAdultName())) {
                        if (bindRecord.getAultIdNumber().equals(certInfo.getCertNo())
                                && bindRecord.getAdultName().equals(commonPersonInfo.getPassengerName())) {
                            match = true;
                            break;
                        }
                    }

                } else {//其他证件匹配英文姓名一致证件
                    if (!StringUtils.isAnyBlank(bindRecord.getAdultElastName(), bindRecord.getAdultEfirstName())) {
                        if (bindRecord.getAdultElastName().equals(commonPersonInfo.getPassEnNameS()) && bindRecord.getAdultEfirstName().equals(commonPersonInfo.getPassEnNameF())) {
                            if (commonPersonInfo.getContactCertList().stream().anyMatch(cert -> bindRecord.getAultIdNumber().equals(cert.getCertNo()))) {
                                match = true;
                                break;
                            }
                        }
                    }

                }
            }
        } else if (PassengerTypeEnum.CHD.getPassType().equals(commonPersonInfo.getPassengerType())) {

            List<GeneralContactCertInfo> contactCertInfos = commonPersonInfo.getContactCertList().stream().filter(generalContactCertInfo ->
                    CertificateTypeEnum.PASSPORT.getShowCode().equals(generalContactCertInfo.getCertType())
                            || CertificateTypeEnum.ID_CARD.getShowCode().equals(generalContactCertInfo.getCertType())).collect(Collectors.toList());
            commonPersonInfo.setContactCertList(contactCertInfos);

            for (GeneralContactCertInfo certInfo : commonPersonInfo.getContactCertList()) {
                // 身份证号相同且中文名相同允许使用畅飞卡
                if (CertificateTypeEnum.ID_CARD.getShowCode().equals(certInfo.getCertType())) {
                    if (!StringUtils.isAnyBlank(bindRecord.getChildIdNumber(), bindRecord.getChildCnName())) {
                        if (bindRecord.getChildIdNumber().equals(certInfo.getCertNo())
                                && bindRecord.getChildCnName().equals(commonPersonInfo.getPassengerName())) {
                            match = true;
                            break;
                        }
                    }

                } else {//其他证件目前匹配英文姓名一致
                    if (!StringUtils.isAnyBlank(bindRecord.getChildEfirstName(), bindRecord.getChildElastName())) {
                        if (bindRecord.getChildElastName().equals(commonPersonInfo.getPassEnNameS()) && bindRecord.getChildEfirstName().equals(commonPersonInfo.getPassEnNameF())) {
                            if (commonPersonInfo.getContactCertList().stream().anyMatch(cert -> bindRecord.getChildIdNumber().equalsIgnoreCase(cert.getCertNo()))) {
                                match = true;
                                break;
                            }
                        }
                    }

                }
            }
        }
        if (match) {
            // 设置畅飞卡卡号
            commonPersonInfo.getContactCertList().forEach(userCert -> {
                userCert.setCouponNo(bindRecord.getVoucherNo());
                userCert.setCouponType(bindRecord.getResourceType());
            });

        }
        return match;
    }

    @InterfaceLog
    @ApiOperation(value = "单订儿童-通行人查询接口", notes = "单订儿童-通行人查询接口")
    @RequestMapping(value = "/queryChildPeers", method = RequestMethod.POST)
    public BaseResp queryChildPeers(@RequestBody @Validated BaseReq<QueryChildPeersReq> queryChildPeersReq, BindingResult bindingResult, HttpServletRequest request) {
        String ip = this.getClientIP(request);
        BaseResp resp = new BaseResp();
        if (bindingResult.hasErrors()) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
            return resp;
        }
        QueryChildPeersReq queryChildPeersReq1 = queryChildPeersReq.getRequest();
        if (queryChildPeersReq1 == null) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo("业务参数不能为空！");
            return resp;
        }
        if (CollectionUtils.isEmpty(queryChildPeersReq1.getSegments())) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo("航段参数不能为空！");
            return resp;
        }
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(Long.valueOf(queryChildPeersReq.getRequest().getFfpId()) + "",
                queryChildPeersReq.getRequest().getLoginKeyInfo(), queryChildPeersReq.getChannelCode());
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        QueryPeersRequest queryPeersRequest = new QueryPeersRequest();
        org.springframework.beans.BeanUtils.copyProperties(queryChildPeersReq1, queryPeersRequest);
        queryPeersRequest.setCustomerNo(queryChildPeersReq1.getFfpId());
        queryPeersRequest.setVersion("10");
        queryPeersRequest.setChannelCode(ChannelCodeEnum.MOBILE.getChannelCode());
        queryPeersRequest.setPassengerName(queryChildPeersReq1.getCustomerName());
        queryPeersRequest.setTicketNo(queryChildPeersReq1.getTicketNo());
        Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
        String url = HandlerConstants.URL_FARE_API + HandlerConstants.ORDER_QUERY_PEERS;
        HttpResult httpResult = doPostClient(queryPeersRequest, url, headMap);
        if (httpResult.isResult()) {
            if (StringUtil.isNullOrEmpty(httpResult.getResponse())) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("返回数据结果为空");
                return resp;
            }
            QueryPeersResponse ptResponse = (QueryPeersResponse) JsonUtil.jsonToBean(httpResult.getResponse(), QueryPeersResponse.class);
            //此错误编码时，前端需根据提示选择操作方式
            if (!UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(ptResponse.getResultCode())) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo(ptResponse.getErrorInfo());
                return resp;
            }
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
            resp.setObjData(ptResponse.getPeerOrderList());
        }
        return resp;
    }

    @InterfaceLog
    @ApiOperation(value = "添加常用乘机人", notes = "添加常用乘机人")
    @RequestMapping(value = "/addCommonPerson", method = RequestMethod.POST)
    public BaseResp addCommonPerson(@RequestBody BaseReq<ModifyPerson> req, HttpServletRequest request) {
        BaseResp resp = new BaseResp();
        String ip = this.getClientIP(request);
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<BaseReq>> violations = validator.validate(req);
        if (null != violations && violations.size() > 0) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo(violations.iterator().next().getMessage());
            return resp;
        }
        ModifyPerson modifyPerson = req.getRequest();
        if (modifyPerson == null) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo("业务参数不能为空！");
            return resp;
        }
        String channelCode = req.getChannelCode();
        String userNo = this.getChannelInfo(channelCode, "10");
        UserInfoMust userInfo = modifyPerson.getUserInfo();
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(Long.valueOf(userInfo.getFfpId()) + "", userInfo.getLoginKeyInfo(), channelCode);
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        GeneralContactRequest generalContactRequest = createGeneralContactRequest(modifyPerson, channelCode, userNo, resp, ip, request, "add");
        if (generalContactRequest == null) {
            return resp;
        }
        Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
        String url = HandlerConstants.URL_FARE_API + HandlerConstants.ADD_COMMON_PERSON_V20;
        HttpResult httpResult = doPostClient(generalContactRequest, url, headMap);
        if (httpResult.isResult()) {
            if (StringUtil.isNullOrEmpty(httpResult.getResponse())) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("返回数据结果为空");
                return resp;
            }
            PtV2GeneralContactResponse ptResponse = (PtV2GeneralContactResponse) JsonUtil.jsonToBean(httpResult.getResponse(), PtV2GeneralContactResponse.class);
            //此错误编码时，前端需根据提示选择操作方式
            if (UnifiedOrderResultEnum.REPEATED_PASS.getResultCode().equals(ptResponse.getResultCode())) {
                resp.setResultCode(WSEnum.REPEATED_PASS.getResultCode());
                resp.setResultInfo(WSEnum.REPEATED_PASS.getResultInfo());
                return resp;
            }
            if (!UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(ptResponse.getResultCode())) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo(ptResponse.getErrorInfo());
                return resp;
            }
            List<QueryCommonPersonInfo> queryCommonPersonInfoList = new ArrayList<>();
            for (GeneralContactInfo generalContactInfo : ptResponse.getGeneralContactList()) {
                QueryCommonPersonInfo queryCommonPersonInfo = new QueryCommonPersonInfo();
                queryCommonPersonInfo.setCommonContactId(generalContactInfo.getGeneralContactId());
                queryCommonPersonInfo.setPassengerName(generalContactInfo.getPassengerName());
                queryCommonPersonInfo.setPassEnNameS(generalContactInfo.getPassEnNameS());
                queryCommonPersonInfo.setPassEnNameF(generalContactInfo.getPassEnNameF());
                queryCommonPersonInfoList.add(queryCommonPersonInfo);
            }
            resp.setObjData(queryCommonPersonInfoList);
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
            return resp;
        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(httpResult.getResponse());
            return resp;
        }
    }

    @InterfaceLog
    @ApiOperation(value = "修改，删除常用乘机人", notes = "修改，删除常用乘机人")
    @RequestMapping(value = "/modifyCommonPerson", method = RequestMethod.POST)
    public BaseResp modifyCommonPerson(@RequestBody @Validated BaseReq<ModifyPerson> req, BindingResult bindingResult, HttpServletRequest request) {
        BaseResp resp = new BaseResp();
        String ip = this.getClientIP(request);
        if (bindingResult.hasErrors()) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
            return resp;
        }
        ModifyPerson modifyPerson = req.getRequest();
        if (modifyPerson == null) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo("业务参数不能为空！");
            return resp;
        }

        String channelCode = req.getChannelCode();
        String userNo = this.getChannelInfo(channelCode, "10");
        UserInfoMust userInfo = modifyPerson.getUserInfo();
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(Long.valueOf(userInfo.getFfpId()) + "", userInfo.getLoginKeyInfo(), channelCode);
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }

        GeneralContactRequest generalContactRequest = createGeneralContactRequest(modifyPerson, channelCode, userNo, resp, ip, request, "modify");
        if (generalContactRequest == null) {
            return resp;
        }
        generalContactRequest.setIsRemove(modifyPerson.getIsRemove());
        Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
        String url = HandlerConstants.URL_FARE_API + HandlerConstants.MODIFY_COMMON_PERSON_V20;
        HttpResult httpResult = doPostClient(generalContactRequest, url, headMap);
        if (httpResult.isResult()) {
            if (StringUtil.isNullOrEmpty(httpResult.getResponse())) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("返回数据结果为空");
                return resp;
            }
            PtResponse ptResponse = (PtResponse) JsonUtil.jsonToBean(httpResult.getResponse(), PtResponse.class);
            //此错误编码时，前端需根据提示选择操作方式
            if (UnifiedOrderResultEnum.REPEATED_PASS.getResultCode().equals(ptResponse.getResultCode())) {
                resp.setResultCode(WSEnum.REPEATED_PASS.getResultCode());
                resp.setResultInfo(WSEnum.REPEATED_PASS.getResultInfo());
                return resp;
            }
            Segment segment = null;
            // 国际本人开关打开 且 入参有城市信息
            boolean hasCity = StringUtils.isNotBlank(modifyPerson.getDepCityCode()) || StringUtils.isNotBlank(modifyPerson.getArrCityCode());
            if ("Y".equals(handConfig.getPersonInternalSelfFlag()) && hasCity) {
                segment = localCacheService.getSegment(modifyPerson.getDepCityCode(), modifyPerson.getArrCityCode());
            }
            // 国际航线 且 错误类型SHOW_PILOT_BUTTON（修改操作证件号码重复） 转错误编码用于前端展示引导按钮
            boolean internalSegment = null != segment && HandlerConstants.TRIP_TYPE_I.equals(segment.getSegmentType());
            if (internalSegment && UnifiedOrderResultEnum.CHECK_90023.getResultCode().equals(ptResponse.getResultCode())) {
                resp.setResultCode(WSEnum.SHOW_PILOT_BUTTON.getResultCode());
                resp.setResultInfo(ptResponse.getErrorInfo());
                return resp;
            }
            if (!UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(ptResponse.getResultCode())) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo(ptResponse.getErrorInfo());
                return resp;
            }
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
            return resp;
        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(httpResult.getResponse());
            return resp;
        }
    }

    @ApiOperation(value = "新增证件信息", notes = "新增证件信息（暂无用途）")
    @RequestMapping(value = "/addCommonPersonCert", method = RequestMethod.POST)
    public BaseResp addCommonPersonCert(@RequestBody BaseReq<ModifyCertInfo> req, HttpServletRequest request) {
        BaseResp resp = new BaseResp();
        String ip = this.getClientIP(request);
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<BaseReq>> violations = validator.validate(req);
        if (null != violations && violations.size() > 0) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo(violations.iterator().next().getMessage());
            return resp;
        }
        ModifyCertInfo modifyCertInfo = req.getRequest();
        if (modifyCertInfo == null) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo("业务参数不能为空！");
            return resp;
        }
        String channelCode = req.getChannelCode();
        String userNo = this.getChannelInfo(channelCode, "10");
        UserInfoMust userInfo = modifyCertInfo.getUserInfo();
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(userInfo.getFfpId(), userInfo.getLoginKeyInfo(), channelCode);
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        GeneralCert generalCert = createGeneralCertReq(modifyCertInfo, channelCode, userNo);
        Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
        String url = HandlerConstants.URL_FARE_API + HandlerConstants.ADD_CERT_V20;
        HttpResult httpResult = doPostClient(generalCert, url, headMap);
        if (httpResult.isResult()) {
            if (StringUtil.isNullOrEmpty(httpResult.getResponse())) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("返回数据结果为空");
                return resp;
            }
            PtResponse ptResponse = (PtResponse) JsonUtil.jsonToBean(httpResult.getResponse(), PtResponse.class);
            if (!UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(ptResponse.getResultCode())) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo(ptResponse.getErrorInfo());
                return resp;
            }
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
            return resp;
        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(httpResult.getResponse());
            return resp;
        }
    }

    @ApiOperation(value = "修改，删除常用乘机人证件", notes = "修改，删除常用乘机人证件（暂无用途）")
    @RequestMapping(value = "/modifyCommonPersonCert", method = RequestMethod.POST)
    public BaseResp modifyCommonPersonCert(@RequestBody BaseReq<ModifyCertInfo> req, HttpServletRequest request) {
        BaseResp resp = new BaseResp();
        String ip = this.getClientIP(request);
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<BaseReq>> violations = validator.validate(req);
        if (null != violations && violations.size() > 0) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo(violations.iterator().next().getMessage());
            return resp;
        }
        ModifyCertInfo modifyCertInfo = req.getRequest();
        if (modifyCertInfo == null) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo("业务参数不能为空！");
            return resp;
        }
        String channelCode = req.getChannelCode();
        String userNo = this.getChannelInfo(channelCode, "10");
        UserInfoMust userInfo = modifyCertInfo.getUserInfo();
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(userInfo.getFfpId(), userInfo.getLoginKeyInfo(), channelCode);
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        GeneralCert generalCert = createGeneralCertReq(modifyCertInfo, channelCode, userNo);
        generalCert.setIsRemove(modifyCertInfo.getIsRemove());
        Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
        String url = HandlerConstants.URL_FARE_API + HandlerConstants.MODIFY_CERT_V20;
        HttpResult httpResult = doPostClient(generalCert, url, headMap);
        if (httpResult.isResult()) {
            if (StringUtil.isNullOrEmpty(httpResult.getResponse())) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("返回数据结果为空");
                return resp;
            }
            PtResponse ptResponse = (PtResponse) JsonUtil.jsonToBean(httpResult.getResponse(), PtResponse.class);
            if (!UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(ptResponse.getResultCode())) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo(ptResponse.getErrorInfo());
                return resp;
            }
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
            return resp;
        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(httpResult.getResponse());
            return resp;
        }
    }

    @InterfaceLog
    @ApiOperation(value = "批量删除常用乘机人", notes = "批量删除常用乘机人")
    @RequestMapping(value = "/modifyCommonPersonBatch", method = RequestMethod.POST)
    public BaseResp modifyCommonPersonBatch(@RequestBody  BaseReq<ModifyPersonBatch> req, HttpServletRequest request) {
        BaseResp resp = new BaseResp();
        String ip = this.getClientIP(request);
        ModifyPersonBatch modifyPersonBatch = req.getRequest();
        if (modifyPersonBatch == null) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo("业务参数不能为空！");
            return resp;
        }
        if (CollectionUtils.isEmpty(modifyPersonBatch.getPersonInfoList())){
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo("至少选择一个常用乘机人！");
            return resp;
        }
        modifyPersonBatch.getPersonInfoList().forEach(personInfo -> {
            List<GeneralContactCertInfo> contactCertList = personInfo.getContactCertList().stream()
                    .filter(contactCert->StringUtils.isNotBlank(contactCert.getCertNo())
                            &&contactCert.getGeneralContactCertId()!=null)
                    .collect(Collectors.toList());
            personInfo.setContactCertList(contactCertList);
        });

        String channelCode = req.getChannelCode();
        String userNo = this.getChannelInfo(channelCode, "10");
        UserInfoMust userInfo = modifyPersonBatch.getUserInfo();
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(userInfo.getFfpId(), userInfo.getLoginKeyInfo(), channelCode);
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        try {
            GeneralContactRequest generalContactRequest = createGeneralContactRequest(modifyPersonBatch, channelCode, userNo);
            Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
            String url = HandlerConstants.URL_FARE_API + HandlerConstants.MODIFY_COMMON_PERSON_V20;
            HttpResult httpResult = doPostClient(generalContactRequest, url, headMap);
            if (httpResult.isResult()) {
                if (StringUtil.isNullOrEmpty(httpResult.getResponse())) {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo("返回数据结果为空");
                    return resp;
                }
                PtResponse ptResponse = (PtResponse) JsonUtil.jsonToBean(httpResult.getResponse(), PtResponse.class);
                if (UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(ptResponse.getResultCode())) {
                    resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                    resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                } else {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo(ptResponse.getErrorInfo());
                }
                return resp;
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo(httpResult.getResponse());
                return resp;
            }
        } catch (ServiceException e) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(e.getMessage());
            return resp;
        } catch (Exception e) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(e.getMessage());
            return resp;
        }
    }


    /**
     * 第三方请求参数构造 新增，修改常用乘机人
     *
     * @param modifyPerson
     * @param channelCode
     * @param userNo
     * @return
     */
    private GeneralContactRequest createGeneralContactRequest(ModifyPerson modifyPerson, String channelCode, String userNo, BaseResp resp, String ip,
                                                              HttpServletRequest request, String type) {
        String realChannelCode = request.getHeader("channelCode");
        String versionCode = request.getHeader(HandlerConstants.CLIENT_VERSION_CODE);
        UserInfoMust userInfo = modifyPerson.getUserInfo();
        QueryCommonPersonInfo commonPersonInfo = modifyPerson.getCommonPersonInfo();
        if (StringUtils.isNotBlank(commonPersonInfo.getPassengerName())) {
            commonPersonInfo.setPassengerName(commonPersonInfo.getPassengerName().toUpperCase());
        }
        List<GeneralContactCertInfo> contactCertList = commonPersonInfo.getContactCertList();
        String birthDate = commonPersonInfo.getBirthdate();
        String sex = commonPersonInfo.getSex();
        int age = 0;
        boolean isRemove = modifyPerson.getIsRemove() != null && modifyPerson.getIsRemove();
        //是否检验签发国，证件有效期等信息 默认检验 国内城市不检验(非大陆 国际及港澳台)
        boolean isInter = true;
        Segment segment = null;
        if (StringUtils.isNotBlank(modifyPerson.getDepCityCode()) && StringUtils.isNotBlank(modifyPerson.getArrCityCode())) {
            segment = localCacheService.getSegment(modifyPerson.getDepCityCode(), modifyPerson.getArrCityCode());
            isInter = segment.isInternal();
        }
        // 国际航线成人本人开关是否打开
        boolean personInternalSelfFlag = "Y".equals(handConfig.getPersonInternalSelfFlag());
        // 国际航线
        boolean internalSegment = null != segment && HandlerConstants.TRIP_TYPE_I.equals(segment.getSegmentType());
        // 国际航线成人本人开关打开 且 国际航线 且 添加修改操作
        boolean internalSelfAddFlag = personInternalSelfFlag && internalSegment && ("add".equals(type) || "modify".equals(type));
        if (!isRemove) {
            //排除本地删除的证件信息
            contactCertList.removeIf(GeneralContactCertInfo::filterRemovedCert);
            if (StringUtil.isNullOrEmpty(contactCertList)) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("证件信息不能为空！");
                return null;
            }
            if (StringUtil.isNullOrEmpty(commonPersonInfo.getPassengerName()) && (StringUtil.isNullOrEmpty(commonPersonInfo.getPassEnNameF()) || StringUtil.isNullOrEmpty(commonPersonInfo.getPassEnNameS()))) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("中文姓名与英文姓名不能同时为空！");
                return null;
            }
            if (StringUtil.isNullOrEmpty(commonPersonInfo.getCountryTelCode())) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("国际区号不能为空！");
                return null;
            }
            if (StringUtil.isNullOrEmpty(commonPersonInfo.getHandphoneNo())) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("手机号不能为空！");
                return null;
            }
            boolean hasId = false;
            for (GeneralContactCertInfo generalContactCertInfo : contactCertList) {
                //订单传入  修改人 修改时间 创建人 创建时间会报错, 才将这字段置空
                if (!"add".equals(type)&&!"addOwner".equals(type)){
                    initGeneralContactCertInfo(generalContactCertInfo);
                }
                if (!(generalContactCertInfo.getIsRemove() != null && generalContactCertInfo.getIsRemove())) {
                    //身份证
                    if (CertificateTypeEnum.ID_CARD.getShowCode().equals(generalContactCertInfo.getCertType())) {
                        if (!Pattern.matches(PatternCommon.ID_NUMBER, generalContactCertInfo.getCertNo())) {
                            resp.setResultCode(WSEnum.ERROR.getResultCode());
                            resp.setResultInfo("【#TYPE#】请输入正确的证件号！".replace("#TYPE#", CertificateTypeEnum.ID_CARD.getDesc()));
                            return null;
                        }
                        //根据身份证信息获取性别和出生日期
                        birthDate = CertUtil.certNoToDate(generalContactCertInfo.getCertNo());
                        sex = CertUtil.checkSex(generalContactCertInfo.getCertNo());
                        hasId = true;
                    } else {
                        if (CertificateTypeEnum.HMT_ID_CARD.getShowCode().equals(generalContactCertInfo.getCertType())
                                && (
                                ChannelCodeEnum.WXAPP.getChannelCode().equals(realChannelCode)
                                        || ChannelCodeEnum.WEIXIN.getChannelCode().equals(realChannelCode)
                                        || ChannelCodeEnum.MWEB.getChannelCode().equals(realChannelCode)
                                        || (ChannelCodeEnum.MOBILE.getChannelCode().equals(realChannelCode) && VersionNoUtil.toMVerInt(versionCode) >= 63100)
                        )) {
                            if (!Pattern.matches(PatternCommon.HMT_ID_CARD, generalContactCertInfo.getCertNo())) {
                                resp.setResultCode(WSEnum.ERROR.getResultCode());
                                resp.setResultInfo("【#TYPE#】请输入正确的证件号！".replace("#TYPE#", CertificateTypeEnum.HMT_ID_CARD.getDesc()));
                                return null;
                            }
                            //根据身份证信息获取性别和出生日期
                            birthDate = CertUtil.certNoToDate(generalContactCertInfo.getCertNo());
                            sex = CertUtil.checkSex(generalContactCertInfo.getCertNo());
                            hasId = true;
                        }
                        // 若类型为BC:出生医学证明，不校验，
                        if (CertificateTypeEnum.BC.getShowCode().equals(generalContactCertInfo.getCertType())) {
                            int bcAge = DateUtils.getAgeByBirthIncludeBirthDay(
                                    birthDate, DateUtils.dateToString(new Date(), DateUtils.YYYY_MM_DD_PATTERN), DateUtils.YYYY_MM_DD_PATTERN);
                            if (bcAge >= 16) {
                                resp.setResultCode(WSEnum.ERROR.getResultCode());
                                resp.setResultInfo("未满16周岁旅客可用");
                                return null;
                            }
                            // app前端默认填充了国籍CN，为M站填充国籍，避免提示证件信息不完整
                            commonPersonInfo.setNationality("CN");
                            //
                            generalContactCertInfo.setBelongCountry("CN");
                            generalContactCertInfo.setCertValidity("2099-12-31");
                            // 移除自动生成证件号的逻辑,使用用户传入的证件号
                            continue;
                        }
                        if (isInter) {
                            if (StringUtil.isNullOrEmpty(generalContactCertInfo.getBelongCountry())) {
                                resp.setResultCode(WSEnum.ERROR.getResultCode());
                                resp.setResultInfo("证件签发国不能为空！");
                                return null;
                            }
                            if (!CertificateTypeEnum.TI.getShowCode().equals(generalContactCertInfo.getCertType())) {
                                if (StringUtil.isNullOrEmpty(generalContactCertInfo.getCertValidity())) {
                                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                                    resp.setResultInfo("证件有效期不能为空！");
                                    return null;
                                } else {
                                    if (!Pattern.matches(PatternCommon.DATE_NORMAL, generalContactCertInfo.getCertValidity())) {
                                        resp.setResultCode(WSEnum.ERROR.getResultCode());
                                        resp.setResultInfo("证件有效期格式为yyyy-MM-dd！");
                                        return null;
                                    }
                                }
                            }

                        }
                    }
                }
            }
            //不存在身份证的情况下，检验出生日期
            if (!hasId) {
                if (StringUtil.isNullOrEmpty(birthDate)) {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo("出生日期不能为空！");
                    return null;
                } else {
                    if (!Pattern.matches(PatternCommon.DATE_NORMAL, birthDate)) {
                        resp.setResultCode(WSEnum.ERROR.getResultCode());
                        resp.setResultInfo("出生日期格式为yyyy-MM-dd！");
                        return null;
                    }
                }
            }
            String date = DateUtils.getCurrentDateStr();
            // 存在航班日期使用航班日期计算年龄 否则使用当前时间计算
            if (StringUtils.isNotBlank(modifyPerson.getFlightDate())) {
                date = modifyPerson.getFlightDate();
            }
            age = DateUtils.getAgeByBirthIncludeBirthDay(birthDate, date, DateUtils.YYYY_MM_DD_PATTERN);
            if (age < 0) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("出生日期不合法！");
                return null;
            }
        }

        String passType = CertNoUtil.toPassType(age);
        // 国际航线成人本人开关打开 且 国际航线 且 数据为成人 且 （添加操作 或 非本人）
        if (internalSelfAddFlag && PassengerTypeEnum.ADT.getPassType().equals(passType) && (null == commonPersonInfo.getCommonContactId() || !Boolean.TRUE.equals(commonPersonInfo.getIsOwn()))) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("您添加的乘机人不符合婴儿/儿童乘机人年龄，请重新选择！");
            return null;
        }
        //参数构造
        GeneralContactRequest generalContactRequest = new GeneralContactRequest(HandlerConstants.VERSION, channelCode, userNo);
        List<GeneralContactInfo> generalContactInfoList = new ArrayList<>();
        GeneralContactInfo generalContactInfo = new GeneralContactInfo();
        BeanUtils.copyProperties(commonPersonInfo, generalContactInfo);
        generalContactInfo.setChannelCustomerType("CRM");
        generalContactInfo.setChannelCustomerNo(userInfo.getFfpId());
        generalContactInfo.setGeneralContactId(commonPersonInfo.getCommonContactId());
        generalContactInfo.setSex(sex);
        generalContactInfo.setBirthdate(birthDate);
        generalContactInfo.setPassengerType(passType);
        if (!"add".equals(type)&&!"addOwner".equals(type)){
            initGeneralContactInfo(generalContactInfo);
        }
        //中文姓名为空的情况下，默认使用英文姓名赋值
        //中文姓名包含“/”时，且英文姓名不为空时已英文姓名为主
        //其他情况暂时原样赋值
        if (StringUtils.isBlank(generalContactInfo.getPassengerName())
                && !(StringUtils.isAnyBlank(generalContactInfo.getPassEnNameS(), generalContactInfo.getPassEnNameF()))) {
            generalContactInfo.setPassengerName(generalContactInfo.getPassEnNameS() + "/" + generalContactInfo.getPassEnNameF());
        } else if (generalContactInfo.getPassengerName().contains("/")) {//如果是英文的名字，放到对应字段里
            String[] enName = generalContactInfo.getPassengerName().split("/");
            if (StringUtils.isAnyBlank(generalContactInfo.getPassEnNameS(), generalContactInfo.getPassEnNameF())) {
                generalContactInfo.setPassEnNameS(enName[0]);
                generalContactInfo.setPassEnNameF(enName[1]);
            } else {
                generalContactInfo.setPassengerName(generalContactInfo.getPassEnNameS() + "/" + generalContactInfo.getPassEnNameF());
            }
        }
        generalContactInfoList.add(generalContactInfo);
        generalContactRequest.setAddFlag(modifyPerson.getAddFlag());
        generalContactRequest.setInterFlag(isInter);
        generalContactRequest.setSegmentType(null == segment ? null : segment.getSegmentType());
        generalContactRequest.setGeneralContactList(generalContactInfoList);
        return generalContactRequest;
    }

    private void initGeneralContactInfo(GeneralContactInfo generalContactInfo) {
        generalContactInfo.setModiferName("");
        generalContactInfo.setModifyDatetime(null);
        generalContactInfo.setCreateDatetime(null);
        generalContactInfo.setCreatorName("");
    }

    private void initGeneralContactCertInfo(GeneralContactCertInfo generalContactCertInfo) {
        generalContactCertInfo.setCreatorName("");
        generalContactCertInfo.setModiferName("");
        generalContactCertInfo.setCreateDatetime(null);
        generalContactCertInfo.setModifyDatetime(null);
    }

    /**
     * 批量删除常用乘机人
     *
     * @param modifyPersonBatch
     * @param channelCode
     * @param userNo
     * @return
     */
    private GeneralContactRequest createGeneralContactRequest(ModifyPersonBatch modifyPersonBatch, String channelCode, String userNo) throws ServiceException {
        UserInfoMust userInfo = modifyPersonBatch.getUserInfo();
        List<QueryCommonPersonInfo> commonPersonInfoList = modifyPersonBatch.getPersonInfoList();
        //参数构造
        GeneralContactRequest generalContactRequest = new GeneralContactRequest(HandlerConstants.VERSION, channelCode, userNo);
        generalContactRequest.setIsRemove(true);
        List<GeneralContactInfo> generalContactInfoList = new ArrayList<>();
        for (QueryCommonPersonInfo commonPersonInfo : commonPersonInfoList) {
            if (commonPersonInfo.getCommonContactId() == null) {
                throw new ServiceException("缺少记录信息");
            }
            GeneralContactInfo generalContactInfo = new GeneralContactInfo();
            BeanUtils.copyProperties(commonPersonInfo, generalContactInfo);
            generalContactInfo.setChannelCustomerType("CRM");
            generalContactInfo.setChannelCustomerNo(userInfo.getFfpId());
            generalContactInfo.setGeneralContactId(commonPersonInfo.getCommonContactId());
            initGeneralContactInfo(generalContactInfo);
            if (CollectionUtils.isNotEmpty(generalContactInfo.getContactCertList())){
                for (GeneralContactCertInfo generalContactCertInfo : generalContactInfo.getContactCertList()) {
                    initGeneralContactCertInfo(generalContactCertInfo);
                }
            }
            //中文姓名为空的情况下，默认使用英文姓名赋值
            //中文姓名包含“/”时，且英文姓名不为空时已英文姓名为主
            //其他情况暂时原样赋值
            if (StringUtils.isBlank(generalContactInfo.getPassengerName())
                    && !(StringUtils.isAnyBlank(generalContactInfo.getPassEnNameS(), generalContactInfo.getPassEnNameF()))) {
                generalContactInfo.setPassengerName(generalContactInfo.getPassEnNameS() + "/" + generalContactInfo.getPassEnNameF());
            } else if (generalContactInfo.getPassengerName().contains("/")) {//如果是英文的名字，放到对应字段里
                String[] enName = generalContactInfo.getPassengerName().split("/");
                if (StringUtils.isAnyBlank(generalContactInfo.getPassEnNameS(), generalContactInfo.getPassEnNameF())) {
                    generalContactInfo.setPassEnNameS(enName[0]);
                    generalContactInfo.setPassEnNameF(enName[1]);
                } else {
                    generalContactInfo.setPassengerName(generalContactInfo.getPassEnNameS() + "/" + generalContactInfo.getPassEnNameF());
                }
            }
            generalContactInfoList.add(generalContactInfo);
        }
        generalContactRequest.setGeneralContactList(generalContactInfoList);
        return generalContactRequest;
    }

    /**
     * 新增，修改证件信息
     *
     * @param modifyCertInfo
     * @param channelCode
     * @param userNo
     * @return
     */
    private GeneralCert createGeneralCertReq(ModifyCertInfo modifyCertInfo, String channelCode, String userNo) {
        UserInfoMust userInfo = modifyCertInfo.getUserInfo();
        GeneralContactCertInfo generalContactCertInfo = modifyCertInfo.getGeneralContactCertInfo();
        GeneralCert generalCert = new GeneralCert(HandlerConstants.VERSION, channelCode, userNo);
        BeanUtils.copyNotNullProperties(generalContactCertInfo, generalCert);
        generalCert.setChannelCustomerType("CRM");
        generalCert.setChannelCustomerNo(userInfo.getFfpId());
        generalCert.setGeneralContactId(generalContactCertInfo.getGeneralContactId());
        return generalCert;
    }

    /**
     * @param personInfoList
     * @param req
     * @param channelCode
     * @param request
     * @param contactCertTemplates 支持的证件类型
     * @param addOwnPass
     */
    private void markOwnPassV2(List<QueryCommonPersonInfo> personInfoList, BaseReq<CommonPersonQuery> req, String channelCode,
                               HttpServletRequest request, List<CertType> contactCertTemplates, boolean addOwnPass) {
        String[] items = {MemberDetailRequestItemsEnum.CERTIFICATEINFO.eName, MemberDetailRequestItemsEnum.BASICINFO.eName,
                MemberDetailRequestItemsEnum.ADDRESSINFOS.eName, MemberDetailRequestItemsEnum.CONTACTINFO.eName, MemberDetailRequestItemsEnum.REALVERIFYINFOS.eName};
        PtMemberDetailRequest ptMemberDetailRequest = new PtMemberDetailRequest();
        ptMemberDetailRequest.setRequestItems(items);
        CommonPersonQuery commonPersonQuery = req.getRequest();
        Header header = buildHeader(request, commonPersonQuery.getChannelCustomerNo(), "");
        PtApiCRMRequest<PtMemberDetailRequest> ptApiCRMRequest = new PtApiCRMRequest<>();
        ptApiCRMRequest.setHeader(header);
        ptApiCRMRequest.setChannel(channelCode);
        ptApiCRMRequest.setChannelPwd(getChannelInfo(channelCode, "40"));
        ptApiCRMRequest.setData(ptMemberDetailRequest);
        PtCRMResponse<PtMemberDetail> ptCRMResponse = memberService.memberDetail(ptApiCRMRequest);
        if (ptCRMResponse.getCode() != 0) {
            return;
        }
        PtMemberDetail ptMemberDetail = ptCRMResponse.getData();
        if (null == ptMemberDetail) {
            return;
        }
        String interFlag = "D";
        //是否本人的判断如果不存在本人，则添加本人到返回结果中
        if (addOwnPass && StringUtils.isNotBlank(commonPersonQuery.getDepCityCode()) && StringUtils.isNotBlank(commonPersonQuery.getArrCityCode())) {
            CityInfoDto dept = localCacheService.getLocalCity(commonPersonQuery.getDepCityCode());
            CityInfoDto arr = localCacheService.getLocalCity(commonPersonQuery.getArrCityCode());
            interFlag = HandlerConstants.TRIP_TYPE_I.equals(dept.getIsInternational()) || HandlerConstants.TRIP_TYPE_I.equals(arr.getIsInternational()) ? HandlerConstants.FLIGHT_INTER_I : HandlerConstants.FLIGHT_INTER_D;
        }
        // 如果不包含会员本人则手动添加会员本人信息
        QueryCommonPersonInfo personInfo = addOwnerCommonPersonV2(ptCRMResponse.getData(), contactCertTemplates, interFlag, request, channelCode);
        if (personInfo != null) {
            personInfoList.add(personInfo);
        }
        //将会员本人的证件排列在列表前面
        if (CollectionUtils.isNotEmpty(personInfoList)) {
            personInfoList.sort(Comparator.comparing(QueryCommonPersonInfo::getPriority));
        }
    }


    /**
     * @param personInfoList
     * @param req
     * @param channelCode
     * @param request
     * @param contactCertTemplates 支持的证件类型
     * @param addOwnPass
     */
    private void markOwnPassV1(List<QueryCommonPersonInfo> personInfoList, BaseReq<CommonPersonQuery> req, String channelCode,
                               HttpServletRequest request, List<CertType> contactCertTemplates, boolean addOwnPass) {
        String[] items = {MemberDetailRequestItemsEnum.CERTIFICATEINFO.eName, MemberDetailRequestItemsEnum.BASICINFO.eName,
                MemberDetailRequestItemsEnum.ADDRESSINFOS.eName, MemberDetailRequestItemsEnum.CONTACTINFO.eName, MemberDetailRequestItemsEnum.REALVERIFYINFOS.eName};
        PtMemberDetailRequest ptMemberDetailRequest = new PtMemberDetailRequest();
        ptMemberDetailRequest.setRequestItems(items);
        CommonPersonQuery commonPersonQuery = req.getRequest();
        Header header = buildHeader(request, commonPersonQuery.getChannelCustomerNo(), "");
        PtApiCRMRequest<PtMemberDetailRequest> ptApiCRMRequest = new PtApiCRMRequest<>();
        ptApiCRMRequest.setHeader(header);
        ptApiCRMRequest.setChannel(channelCode);
        ptApiCRMRequest.setChannelPwd(getChannelInfo(channelCode, "40"));
        ptApiCRMRequest.setData(ptMemberDetailRequest);
        PtCRMResponse<PtMemberDetail> ptCRMResponse = memberService.memberDetail(ptApiCRMRequest);
        if (ptCRMResponse.getCode() != 0) {
            return;
        }
        PtMemberDetail ptMemberDetail = ptCRMResponse.getData();
        if (null == ptMemberDetail) {
            return;
        }
        //常用联系人中是否包含会员本人
        boolean containsOwner = false;
        List<MemberCertificateSoaModelV2> certificateInfoList = ptMemberDetail.getCertificateInfo();
        MemberBasicInfoSoaModel memberInfo = ptMemberDetail.getBasicInfo();
        if ((!StringUtil.isNullOrEmpty(certificateInfoList)) && (!StringUtil.isNullOrEmpty(personInfoList))) {
            int priority = 1;
            for (QueryCommonPersonInfo queryCommonPersonInfo : personInfoList) {
                if (!StringUtil.isNullOrEmpty(queryCommonPersonInfo.getContactCertList())) {
                    boolean isOwn = false;
                    queryCommonPersonInfo.setPriority(priority);
                    for (GeneralContactCertInfo generalContactCertInfo : queryCommonPersonInfo.getContactCertList()) {
                        if (!StringUtil.isNullOrEmpty(generalContactCertInfo.getCertNo())) {
                            for (MemberCertificateSoaModelV2 memberCertificateSoaModelV2 : certificateInfoList) {
                                CertificateTypeEnum memberCertEnum = CertificateTypeEnum.checkType(memberCertificateSoaModelV2.getCertificateType());
                                if (memberCertEnum != null && (memberCertEnum.getShowCode().equals(generalContactCertInfo.getCertType())
                                        && generalContactCertInfo.getCertNo().equals(memberCertificateSoaModelV2.getCertificateNumber()))
                                        // 处理CRM那边记录的其他类型的证件
                                        || (memberCertEnum == CertificateTypeEnum.OTHER
                                        && generalContactCertInfo.getCertNo().equals(memberCertificateSoaModelV2.getCertificateNumber()))) {
                                    // 不存在英文姓名 或 英文姓名匹配
                                    boolean enName = (StringUtils.isBlank(queryCommonPersonInfo.getPassEnNameS()) && StringUtils.isBlank(queryCommonPersonInfo.getPassEnNameF())) || matchEnName(memberInfo, queryCommonPersonInfo);
                                    // 身份证匹配姓名
                                    if ((CertificateTypeEnum.ID_CARD.equals(memberCertEnum) || CertificateTypeEnum.HMT_ID_CARD.equals(memberCertEnum))
                                            && (memberInfo.getCLastName() + memberInfo.getCFirstName()).equals(queryCommonPersonInfo.getPassengerName()) && enName) {
                                        isOwn = true;
                                        containsOwner = true;
                                        generalContactCertInfo.setUseScore(true);// 本人可以使用积分
                                    } else if (!CertificateTypeEnum.ID_CARD.equals(memberCertEnum) && !CertificateTypeEnum.HMT_ID_CARD.equals(memberCertEnum)
                                            && matchEnName(memberInfo, queryCommonPersonInfo)) {
                                        // 其他证件匹配英文姓名
                                        isOwn = true;
                                        containsOwner = true;
                                        generalContactCertInfo.setUseScore(true);// 本人可以使用积分
                                    }
                                }
                            }
                        }
                    }
                    priority++;
                    queryCommonPersonInfo.setIsOwn(isOwn);
                    if (isOwn) {
                        queryCommonPersonInfo.setPriority(0);
                    }
                }
            }
        }
        String interFlag = "U";
        //是否本人的判断如果不存在本人，则添加本人到返回结果中
        if (addOwnPass && !StringUtil.isNullOrEmpty(commonPersonQuery.getDepCityCode()) && !StringUtil.isNullOrEmpty(commonPersonQuery.getArrCityCode())) {
            CityInfoDto dept = localCacheService.getLocalCity(commonPersonQuery.getDepCityCode());
            CityInfoDto arr = localCacheService.getLocalCity(commonPersonQuery.getArrCityCode());
            interFlag = HandlerConstants.TRIP_TYPE_I.equals(dept.getIsInternational()) || HandlerConstants.TRIP_TYPE_I.equals(arr.getIsInternational()) ? HandlerConstants.FLIGHT_INTER_I : HandlerConstants.FLIGHT_INTER_D;
        }
        // 如果不包含会员本人则手动添加会员本人信息
        if (!containsOwner) {
            String userNo = this.getChannelInfo(channelCode, "10");
            String ip = this.getClientIP(request);
            QueryCommonPersonInfo personInfo = null;
            if (HandlerConstants.FLIGHT_INTER_D.equals(interFlag)) {
                personInfo = addOwnerCommonPerson(ptCRMResponse.getData(), contactCertTemplates, commonPersonQuery.getDepCityCode(),
                        commonPersonQuery.getArrCityCode(), req, this.getClientIP(request), request);
            }
            // 2022-04-22 国际港澳台航线全部添加本人（不在需要留学生运价STUDENT）
            if (HandlerConstants.FLIGHT_INTER_I.equals(interFlag)) {
                personInfo = addInterPass(ptCRMResponse.getData(), contactCertTemplates, channelCode, userNo, interFlag, ip);
            }
            if (personInfo != null) {
                personInfoList.add(personInfo);
            }
        }
        //将会员本人的证件排列在列表前面
        if (!StringUtil.isNullOrEmpty(personInfoList)) {
            personInfoList.sort(Comparator.comparing(QueryCommonPersonInfo::getPriority));
        }
    }

    /**
     * 添加本人到常用乘机人列表
     *
     * @param memberDetail
     * @param contactCertTemplates
     * @param depCityCode
     * @param arrCityCode
     * @param req
     * @return
     */
    private QueryCommonPersonInfo addOwnerCommonPerson(PtMemberDetail memberDetail, List<CertType> contactCertTemplates,
                                                       String depCityCode, String arrCityCode, BaseReq<CommonPersonQuery> req, String ip, HttpServletRequest httpServletRequest) {
        ModifyPerson modifyPerson = new ModifyPerson();
        modifyPerson.setAddFlag(true);
        modifyPerson.setDepCityCode(depCityCode);
        modifyPerson.setArrCityCode(arrCityCode);
        modifyPerson.setIsRemove(false);
        QueryCommonPersonInfo personInfo = new QueryCommonPersonInfo();
        personInfo.setInterFlag(HandlerConstants.FLIGHT_INTER_D);
        MemberBasicInfoSoaModel basicInfoSoaModel = memberDetail.getBasicInfo();
        UserInfoMust userInfoMust = new UserInfoMust();
        userInfoMust.setFfpCardNo(req.getRequest().getChannelCustomerNo());
        userInfoMust.setFfpId(basicInfoSoaModel.getMemberId() + "");
        modifyPerson.setUserInfo(userInfoMust);
        List<MemberAddressSoaModel> addressInfos = memberDetail.getAddressInfos();
        if (CollectionUtils.isNotEmpty(addressInfos)) {
            personInfo.setNationality(addressInfos.get(0).getCountryCode());
            personInfo.setBelongCountry(addressInfos.get(0).getCountryCode());
        }
        personInfo.setSex(basicInfoSoaModel.getSex() == 1 ? "M" : "F");
        personInfo.setBirthdate(DateUtils.dateToString(new Date(basicInfoSoaModel.getBirthDay()), DateUtils.YYYY_MM_DD_PATTERN));
        personInfo.setFfCardNo("HO" + basicInfoSoaModel.getCardNO());
        personInfo.setPassengerType(PassengerTypeEnum.ADT.getPassType());
        personInfo.setChannelCustomerNo(userInfoMust.getFfpId());
        personInfo.setChannelCustomerType("CRM");
        personInfo.setPassengerName(basicInfoSoaModel.getCLastName() + basicInfoSoaModel.getCFirstName());
        personInfo.setPassEnNameF(basicInfoSoaModel.getEFirstName());
        personInfo.setPassEnNameS(basicInfoSoaModel.getELastName());
        personInfo.setCountryTelCode("86");
        List<MemberCertificateSoaModelV2> certificateInfoList = memberDetail.getCertificateInfo();
        List<GeneralContactCertInfo> contactCertList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(contactCertTemplates) && CollectionUtils.isNotEmpty(certificateInfoList)) {
            for (CertType cert : contactCertTemplates) {
                CertificateTypeEnum certificateTypeEnum = CertificateTypeEnum.checkShowCode(cert.getCertType());
                if (certificateTypeEnum != null) {
                    MemberCertificateSoaModelV2 memberCertificateSoaModelV2 = certificateInfoList.stream().filter(v2 -> v2.getCertificateType() == certificateTypeEnum.getCode()).findFirst().orElse(null);
                    if (memberCertificateSoaModelV2 != null) {
                        contactCertList.add(new GeneralContactCertInfo(cert.getCertType(), memberCertificateSoaModelV2.getCertificateNumber(), true));
                    } else {
                        contactCertList.add(new GeneralContactCertInfo(cert.getCertType()));
                    }
                }
            }
        }
        List<MemberContactSoaModel> contactSoaModels = memberDetail.getContactInfo();
        if (CollectionUtils.isNotEmpty(contactSoaModels)) {
            for (MemberContactSoaModel contactSoaModel : contactSoaModels) {
                if (ContactTypeEnum.MOBILE.getCode() == contactSoaModel.getContactType()) {
                    personInfo.setHandphoneNo(contactSoaModel.getContactNumber());
                }
            }
        }
        List<GeneralContactCertInfo> addContactCertList = contactCertList.stream().filter(cert -> StringUtils.isNotBlank(cert.getCertNo())).collect(Collectors.toList());
        personInfo.setContactCertList(addContactCertList);
        if (CollectionUtils.isEmpty(personInfo.getContactCertList())) {
            return null;
        }
        modifyPerson.setCommonPersonInfo(personInfo);
        GeneralContactRequest request = createGeneralContactRequest(modifyPerson, req.getChannelCode(), this.getChannelInfo(req.getChannelCode(), "10"), new BaseResp(), ip, httpServletRequest, "addOwner");
        if (request == null) {
            return null;
        }
        Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
        PtV2GeneralContactResponse ptResponse = orderManage.addGeneralContact(request, headMap);
        if (!UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(ptResponse.getResultCode())) {
            return null;
        } else {
            personInfo.setContactCertList(contactCertList);
            personInfo.setCommonContactId(ptResponse.getGeneralContactList().get(0).getGeneralContactId());
            personInfo.setIsOwn(true);
            return personInfo;
        }
    }


    /**
     * 英文姓名匹配
     *
     * @param memberInfo
     * @param commonPersonInfo
     * @return
     */
    private boolean matchEnName(MemberBasicInfoSoaModel memberInfo, QueryCommonPersonInfo commonPersonInfo) {
        if (StringUtils.isNotBlank(commonPersonInfo.getPassEnNameS()) && StringUtils.isNotBlank(commonPersonInfo.getPassEnNameF())) {
            return (commonPersonInfo.getPassEnNameS().equals(memberInfo.getELastName()) && commonPersonInfo.getPassEnNameF().equals(memberInfo.getEFirstName()));
        } else {
            return false;
        }
    }

    private boolean toMatchEnName(MemberBeneficiaryDTO memberBeneficiaryDTO, QueryCommonPersonInfo commonPersonInfo) {
        if (StringUtils.isNotBlank(commonPersonInfo.getPassEnNameS()) && StringUtils.isNotBlank(commonPersonInfo.getPassEnNameF())) {
            return (commonPersonInfo.getPassEnNameS().equals(memberBeneficiaryDTO.getELastName()) && commonPersonInfo.getPassEnNameF().equals(memberBeneficiaryDTO.getEFirstName()));
        } else {
            return false;
        }
    }


    /**
     * 添加国际乘机人
     *
     * @param memberDetail
     * @param contactCertTemplates
     */
    private QueryCommonPersonInfo addInterPass(PtMemberDetail memberDetail, List<CertType> contactCertTemplates, String channelCode, String userNo, String interFlag, String ip) {
        try {
            //增加实名的证件信息
            List<MemberCertificateSoaModelV2> certificateInfoList = memberDetail.getCertificateInfo();
            List<GeneralContactCertInfo> addContactCertList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(certificateInfoList)) {
                for (MemberCertificateSoaModelV2 memberCertificateSoaModelV2 : certificateInfoList) {
                    if (memberCertificateSoaModelV2.isVerify()) {
                        CertificateTypeEnum certificateTypeEnum = CertificateTypeEnum.checkType(memberCertificateSoaModelV2.getCertificateType());
                        if (certificateTypeEnum != null) {
                            GeneralContactCertInfo certInfo = new GeneralContactCertInfo();
                            certInfo.setCertType(certificateTypeEnum.getShowCode());
                            certInfo.setCertNo(memberCertificateSoaModelV2.getCertificateNumber());
                            certInfo.setBelongCountry("CN");
                            //  身份证不需要有效期
                            if (!CertificateTypeEnum.ID_CARD.getShowCode().equals(certificateTypeEnum.getShowCode())) {
                                certInfo.setCertValidity(DateUtils.getCurrentDateStr());
                            }
                            addContactCertList.add(certInfo);
                        }
                    }
                }
            }
            if (CollectionUtils.isEmpty(addContactCertList)) {
                return null;
            }
            //补充其余支持的证件
            List<GeneralContactCertInfo> contactCertList = new ArrayList<>();
            for (CertType certType : contactCertTemplates) {
                //判断当前证件列表是否有此证件枚举
                GeneralContactCertInfo generalContactCertInfo = addContactCertList.stream().filter(certInfo -> certInfo.getCertType().equals(certType.getCertType())).findFirst().orElse(null);
                if (generalContactCertInfo == null) {
                    contactCertList.add(new GeneralContactCertInfo(certType.getCertType()));
                } else {
                    contactCertList.add(generalContactCertInfo);
                }
            }
            QueryCommonPersonInfo personInfo = new QueryCommonPersonInfo();
            personInfo.setInterFlag(interFlag);
            MemberBasicInfoSoaModel basicInfoSoaModel = memberDetail.getBasicInfo();
            personInfo.setSex(basicInfoSoaModel.getSex() == 1 ? "M" : "F");
            personInfo.setBirthdate(DateUtils.dateToString(new Date(basicInfoSoaModel.getBirthDay()), DateUtils.YYYY_MM_DD_PATTERN));
            personInfo.setFfCardNo("HO" + basicInfoSoaModel.getCardNO());
            personInfo.setPassengerType(PassengerTypeEnum.ADT.getPassType());
            personInfo.setChannelCustomerNo(String.valueOf(basicInfoSoaModel.getMemberId()));
            personInfo.setChannelCustomerType("CRM");
            personInfo.setPassengerName(basicInfoSoaModel.getCLastName() + basicInfoSoaModel.getCFirstName());
            personInfo.setPassEnNameF(basicInfoSoaModel.getEFirstName());
            personInfo.setPassEnNameS(basicInfoSoaModel.getELastName());
            personInfo.setContactCertList(contactCertList);
            //personInfo.setCountryTelCode("86");
            //调用远程接口增加乘机人
            //参数构造
            GeneralContactRequest generalContactRequest = new GeneralContactRequest(HandlerConstants.VERSION, channelCode, userNo);
            List<GeneralContactInfo> generalContactInfoList = new ArrayList<>();
            GeneralContactInfo generalContactInfo = new GeneralContactInfo();
            generalContactInfo.setChannelCustomerType("CRM");
            generalContactInfo.setChannelCustomerNo(personInfo.getChannelCustomerNo());
            generalContactInfo.setSex(personInfo.getSex());
            generalContactInfo.setBirthdate(personInfo.getBirthdate());
            generalContactInfo.setPassengerType(personInfo.getPassengerType());
            generalContactInfo.setPassEnNameS(personInfo.getPassEnNameS());
            generalContactInfo.setPassEnNameF(personInfo.getPassEnNameF());
            generalContactInfo.setPassengerName(personInfo.getPassengerName());
            generalContactInfo.setContactCertList(addContactCertList);
            generalContactInfoList.add(generalContactInfo);
            generalContactRequest.setAddFlag(true);
            generalContactRequest.setInterFlag(HandlerConstants.FLIGHT_INTER_I.equals(interFlag) ? true : false);
            generalContactRequest.setGeneralContactList(generalContactInfoList);
            Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
            PtV2GeneralContactResponse ptResponse = orderManage.addGeneralContact(generalContactRequest, headMap);
            if (!UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(ptResponse.getResultCode())) {
                return null;
            } else {
                GeneralContactInfo generalContactInfoResult = ptResponse.getGeneralContactList().get(0);
                Integer generalContactId = generalContactInfoResult.getGeneralContactId();
                personInfo.setCommonContactId(generalContactId);
                personInfo.setIsOwn(true);
                if (CollectionUtils.isNotEmpty(contactCertList) && CollectionUtils.isNotEmpty(generalContactInfoResult.getContactCertList())) {
                    Map<String, GeneralContactCertInfo> generalContactCertInfoMap = Maps.newHashMap();
                    for (GeneralContactCertInfo generalContactCertInfo : generalContactInfoResult.getContactCertList()) {
                        if (StringUtils.isNoneBlank(generalContactCertInfo.getCertType(), generalContactCertInfo.getCertNo())) {
                            generalContactCertInfoMap.put(generalContactCertInfo.getCertType() + "_" + generalContactCertInfo.getCertNo(), generalContactCertInfo);
                        }
                    }
                    for (GeneralContactCertInfo generalContactCertInfo : contactCertList) {
                        if (StringUtils.isNoneBlank(generalContactCertInfo.getCertType(), generalContactCertInfo.getCertNo())) {
                            GeneralContactCertInfo generalContactCertInfoTmp = generalContactCertInfoMap.get(generalContactCertInfo.getCertType() + "_" + generalContactCertInfo.getCertNo());
                            if (null != generalContactCertInfoTmp) {
                                generalContactCertInfo.setGeneralContactId(generalContactCertInfoTmp.getGeneralContactId());
                                generalContactCertInfo.setGeneralContactCertId(generalContactCertInfoTmp.getGeneralContactCertId());
                            }
                        }
                    }
                }
                return personInfo;
            }
        } catch (Exception e) {
            log.error("自动添加乘机人发生异常,会员信息:{},异常信息:", JsonUtil.objectToJson(memberDetail), e);
            return null;
        }
    }

    /**
     * 添加国际乘机人
     *
     * @param memberDetail
     * @param contactCertTemplates
     */
    private QueryCommonPersonInfo addInterPassV2(PtMemberDetail memberDetail, List<CertType> contactCertTemplates, String interFlag) {
        //增加实名的证件信息
        List<MemberCertificateSoaModelV2> certificateInfoList = memberDetail.getCertificateInfo();
        List<GeneralContactCertInfo> addContactCertList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(certificateInfoList)) {
            for (MemberCertificateSoaModelV2 memberCertificateSoaModelV2 : certificateInfoList) {
                CertificateTypeEnum certificateTypeEnum = CertificateTypeEnum.checkType(memberCertificateSoaModelV2.getCertificateType());
                if (certificateTypeEnum != null) {
                    GeneralContactCertInfo certInfo = new GeneralContactCertInfo();
                    certInfo.setCertType(certificateTypeEnum.getShowCode());
                    certInfo.setCertNo(memberCertificateSoaModelV2.getCertificateNumber());
                    certInfo.setBelongCountry("CN");
                    //  身份证不需要有效期
                    if (!CertificateTypeEnum.ID_CARD.getShowCode().equals(certificateTypeEnum.getShowCode())) {
                        certInfo.setCertValidity(DateUtils.getCurrentDateStr());
                    }
                    addContactCertList.add(certInfo);
                }
            }
        }
        if (CollectionUtils.isEmpty(addContactCertList)) {
            return null;
        }
        //补充其余支持的证件
        List<GeneralContactCertInfo> contactCertList = new ArrayList<>();
        for (CertType certType : contactCertTemplates) {
            //判断当前证件列表是否有此证件枚举
            GeneralContactCertInfo generalContactCertInfo = addContactCertList.stream().filter(certInfo -> certInfo.getCertType().equals(certType.getCertType())).findFirst().orElse(null);
            if (generalContactCertInfo == null) {
                contactCertList.add(new GeneralContactCertInfo(certType.getCertType()));
            } else {
                contactCertList.add(generalContactCertInfo);
            }
        }
        MemberBasicInfoSoaModel basicInfoSoaModel = memberDetail.getBasicInfo();
        QueryCommonPersonInfo personInfo = new QueryCommonPersonInfo();
        personInfo.setInterFlag(interFlag);
        personInfo.setSex(basicInfoSoaModel.getSex() == 1 ? "M" : "F");
        personInfo.setBirthdate(DateUtils.dateToString(new Date(basicInfoSoaModel.getBirthDay()), DateUtils.YYYY_MM_DD_PATTERN));
        personInfo.setFfCardNo("HO" + basicInfoSoaModel.getCardNO());
        personInfo.setPassengerType(PassengerTypeEnum.ADT.getPassType());
        personInfo.setChannelCustomerNo(String.valueOf(basicInfoSoaModel.getMemberId()));
        personInfo.setChannelCustomerType("CRM");
        personInfo.setPassengerName(basicInfoSoaModel.getCLastName() + basicInfoSoaModel.getCFirstName());
        personInfo.setPassEnNameF(basicInfoSoaModel.getEFirstName());
        personInfo.setPassEnNameS(basicInfoSoaModel.getELastName());
        personInfo.setContactCertList(contactCertList);
        personInfo.setIsOwn(true);
        return personInfo;
    }

    /**
     * 添加本人到常用乘机人列表
     *
     * @param memberDetail         会员本人信息
     * @param contactCertTemplates 支持的证件类型
     * @param interFlag
     * @param request
     * @param channelCode
     * @return
     */
    private QueryCommonPersonInfo addOwnerCommonPersonV2(PtMemberDetail memberDetail, List<CertType> contactCertTemplates,
                                                        String interFlag, HttpServletRequest request, String channelCode) {

        QueryCommonPersonInfo personInfo = new QueryCommonPersonInfo();
        try {

        personInfo.setInterFlag(interFlag);
        MemberBasicInfoSoaModel basicInfoSoaModel = memberDetail.getBasicInfo();
        String ffpId = basicInfoSoaModel.getMemberId() + "";
        personInfo.setSex(basicInfoSoaModel.getSex() == 1 ? "M" : "F");
        personInfo.setBirthdate(DateUtils.dateToString(new Date(basicInfoSoaModel.getBirthDay()), DateUtils.YYYY_MM_DD_PATTERN));
        personInfo.setFfCardNo("HO" + basicInfoSoaModel.getCardNO());
        personInfo.setPassengerType(PassengerTypeEnum.ADT.getPassType());
        personInfo.setChannelCustomerNo(ffpId);
        personInfo.setChannelCustomerType("CRM");
        personInfo.setPassengerName(basicInfoSoaModel.getCLastName() + basicInfoSoaModel.getCFirstName());
        personInfo.setPassEnNameF(basicInfoSoaModel.getEFirstName());
        personInfo.setPassEnNameS(basicInfoSoaModel.getELastName());
        personInfo.setCLastName(basicInfoSoaModel.getCLastName());
        personInfo.setCFirstName(basicInfoSoaModel.getCFirstName());
        personInfo.setCommonContactId(-1);
        personInfo.setIsCrmData(true);
        // 因旧会员系统不包含海员证等新证件类型，查询新的证件信息接口
        CrmMemberBaseApiRequest<QueryMemberCertificateReqDto> queryCertRequest = buildCommCrmMemberReq(request, channelCode);
        QueryMemberCertificateReqDto queryCertDto = new QueryMemberCertificateReqDto();
        queryCertDto.setMemberId(Integer.parseInt(ffpId));
        queryCertRequest.setData(queryCertDto);
        CrmMemberBaseApiResponse<QueryMemberCertificateResDto> queryCertResponse = iMemberCrmMemberService.queryMemberCertificate(queryCertRequest);
        List<MemberCertificateResModel> certList = queryCertResponse.getData().getCertificateList();
        List<MemberCertificateResModel> newCertList = new ArrayList<>();
        List<MemberCertificateSoaModelV2> certificateInfoList = new ArrayList<>();
        //同一种证件类型,多个证件号,取最大的证件有效期
        if (CollectionUtils.isNotEmpty(certList)) {
            Map<String, List<MemberCertificateResModel>> memberCertificateSoaModelV2Map = certList.stream().collect(Collectors.groupingBy(MemberCertificateResModel::getCertificateType));
            for (Map.Entry<String, List<MemberCertificateResModel>> entry : memberCertificateSoaModelV2Map.entrySet()) {
                List<MemberCertificateResModel> memberCertificateSoaModels = entry.getValue();
                if (CollectionUtils.isNotEmpty(memberCertificateSoaModels)) {
                    // 分离 validDate 为 null 的对象
                    Map<Boolean, List<MemberCertificateResModel>> partitioned = memberCertificateSoaModels.stream()
                            .collect(Collectors.partitioningBy(m -> m.getValidDate() != null));

                    List<MemberCertificateResModel> validDateNullList = partitioned.get(false); // validDate 为 null 的对象
                    List<MemberCertificateResModel> validDateNotNullList = partitioned.get(true); // validDate 不为 null 的对象

                    MemberCertificateResModel memberCertificateResModel = validDateNotNullList.stream().max(Comparator.comparing(MemberCertificateResModel::getValidDate)).orElse(null);
                    if (memberCertificateResModel != null) {
                        newCertList.add(memberCertificateResModel);
                    }
                    newCertList.addAll(validDateNullList);
                }
            }
            // certList转为List<MemberCertificateSoaModelV2> 兼容接口
            certificateInfoList = newCertList.stream().map(MemberCertificateResModel::convertToMemberCertificateSoaModelV2).collect(Collectors.toList());
        }
        List<GeneralContactCertInfo> contactCertList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(contactCertTemplates) && CollectionUtils.isNotEmpty(certificateInfoList)) {
            for (CertType cert : contactCertTemplates) {
                CertificateTypeEnum certificateTypeEnum = CertificateTypeEnum.checkShowCode(cert.getCertType());
                if (certificateTypeEnum != null) {
                    MemberCertificateSoaModelV2 memberCertificateSoaModelV2 = certificateInfoList.stream().filter(v2 -> v2.getCertificateType() == certificateTypeEnum.getCode()).findFirst().orElse(null);
                    if (memberCertificateSoaModelV2 != null) {
                        if (StringUtils.isNotBlank(memberCertificateSoaModelV2.getSigningAuthority())) {
                            List<String> countryCode = Collections.singletonList(memberCertificateSoaModelV2.getSigningAuthority());
                            List<TCountryDTO> localCountryList = localCacheService.getLocalCountry(countryCode);
                            String signingAuthorityName = localCountryList.stream().filter(i -> memberCertificateSoaModelV2.getSigningAuthority().equalsIgnoreCase(i.countryCode))
                                    .findFirst().map(i -> i.countryName).orElse(null);
                            memberCertificateSoaModelV2.setSigningAuthorityName(signingAuthorityName);
                        }
                        GeneralContactCertInfo contactCertInfo = new GeneralContactCertInfo();
                        contactCertInfo.setCertValidity(memberCertificateSoaModelV2.getValidDate());
                        contactCertInfo.setCertType(cert.getCertType());
                        contactCertInfo.setCertNo(memberCertificateSoaModelV2.getCertificateNumber());
                        contactCertInfo.setUseScore(true);
                        contactCertInfo.setBelongCountry(memberCertificateSoaModelV2.getSigningAuthority());
                        contactCertInfo.setBelongCountryName(memberCertificateSoaModelV2.getSigningAuthorityName());
                        contactCertInfo.setVerify(memberCertificateSoaModelV2.isVerify());
                        String  record =AESTool.encrypt(memberCertificateSoaModelV2.getRecordId() + "", HandlerConstants.DEFAULT_TOKEN.substring(0, 16), HandlerConstants.DEFAULT_TOKEN.substring(0, 16));
                        contactCertInfo.setRecord(record);
                        contactCertList.add(contactCertInfo);
                    } else {
                        contactCertList.add(new GeneralContactCertInfo(cert.getCertType()));
                    }
                }
            }
        }
        //联系方式处理
        personInfo.setCountryTelCode("86");
        MemberContactSoaModel mobile = CRMReqUtil.getContactInfo(memberDetail.getContactInfo(), ContactTypeEnum.MOBILE.getCode());
        if (mobile != null) {
            CrmPhoneInfo crmPhoneInfo = CRMReqUtil.spiltPhoneNum(mobile.getContactNumber());
            personInfo.setCountryTelCode(crmPhoneInfo.getAreaId());
            personInfo.setHandphoneNo(crmPhoneInfo.getPhone());
        }
        List<GeneralContactCertInfo> addContactCertList = contactCertList.stream().filter(cert -> StringUtils.isNotBlank(cert.getCertNo())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(addContactCertList)) {
            return null;
        }
        personInfo.setContactCertList(addContactCertList);
        //国籍以及签发国处理，目前无会员国籍，暂时使用签发国代替
        GeneralContactCertInfo generalContactCertInfo = filterGeneralContactCertInfo(addContactCertList);
        if (generalContactCertInfo != null) {
            //如果是身份证件，国籍默认都是CN
            if (CertificateTypeEnum.ID_CARD.getShowCode().equals(generalContactCertInfo.getCertType())) {
                personInfo.setNationality("CN");
                personInfo.setBelongCountry("CN");
            } else {
                personInfo.setNationality(generalContactCertInfo.getBelongCountry());
                personInfo.setBelongCountry(generalContactCertInfo.getBelongCountry());
            }
        }
        personInfo.setIsOwn(true);
        }catch (Exception e){
            log.info("添加本人到常用乘机人列表出错",e);
            return null;
        }
        return personInfo;
    }


    private GeneralContactCertInfo filterGeneralContactCertInfo(List<GeneralContactCertInfo> addContactCertList) {
        GeneralContactCertInfo generalContactCertInfo = null;
        generalContactCertInfo = addContactCertList.stream().filter(cert -> CertificateTypeEnum.ID_CARD.getShowCode().equals(cert.getCertType())).findFirst().orElse(null);
        if (generalContactCertInfo != null) {
            return generalContactCertInfo;
        }
        generalContactCertInfo = addContactCertList.stream().filter(cert -> CertificateTypeEnum.PASSPORT.getShowCode().equals(cert.getCertType())).findFirst().orElse(null);
        if (generalContactCertInfo != null) {
            return generalContactCertInfo;
        }
        List<GeneralContactCertInfo> list = addContactCertList.stream().filter(cert -> !CertificateTypeEnum.ID_CARD.getShowCode().equals(cert.getCertType())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(list)) {
            return list.get(0);
        }
        return null;
    }


    /**
     * 乘机人部分信息完善
     *
     * @param queryCommonPersonInfoList
     * @param channelCode
     * @param ip
     */
    private void infoComplete(List<QueryCommonPersonInfo> queryCommonPersonInfoList, String channelCode, String ip,
                              String departureDate, String returnDate, List<String> cabinTypes) {
        try {
            List<TCountryDTO> tCountryDTOList = basicService.queryCountrys();
            if (!StringUtil.isNullOrEmpty(tCountryDTOList)) {
                for (QueryCommonPersonInfo queryCommonPersonInfo : queryCommonPersonInfoList) {
                    queryCommonPersonInfo.setNationalityName(queryCommonPersonInfo.getNationality());
                    if (StringUtils.isNotBlank(departureDate)) {
                        queryCommonPersonInfo.setPassengerType(OrderObjectConvert.adjustPassType(queryCommonPersonInfo.getBirthdate(), departureDate));
                    }
                    if (StringUtils.isNotBlank(returnDate)) {
                        queryCommonPersonInfo.setPassengerType(OrderObjectConvert.adjustPassType(queryCommonPersonInfo.getBirthdate(), returnDate));
                    }
                    if (CollectionUtils.isNotEmpty(cabinTypes)) {
                        //青老年特惠需判断成人年龄范围
                        if (cabinTypes.contains(PackageTypeEnum.YOUTH_FARE.getPackType())) {
                            if (PassengerTypeEnum.ADT.getPassType().equals(queryCommonPersonInfo.getPassengerType())) {
                                String finalFlightDate = StringUtils.isBlank(returnDate) ? departureDate : returnDate;
                                int age = DateUtils.getAgeByBirthIncludeBirthDay(queryCommonPersonInfo.getBirthdate(), finalFlightDate, DateUtils.YYYY_MM_DD_PATTERN);
                                if ((age < 18 || age > 24 && age < 55)) {
                                    queryCommonPersonInfo.setAlertMessage("很抱歉，您预订的航班乘机人年龄不在指定范围内，请重新选择！");
                                }
                            } else {
                                queryCommonPersonInfo.setAlertMessage("很抱歉，您预订的航班乘机人年龄不在指定范围内，请重新选择！");
                            }

                        }
                        // 畅飞卡类型处理
                        if (cabinTypes.contains(PackageTypeEnum.CHD_UNLIMITED_FLY.getPackType())) {
                            queryCommonPersonInfo.getContactCertList().forEach(generalContactCertInfo -> {
                                if (PassengerTypeEnum.ADT.getPassType().equals(queryCommonPersonInfo.getPassengerType())) {
                                    generalContactCertInfo.setCabinType(CommonPersonCabinType.CUF_ADT.getCode());
                                } else if (PassengerTypeEnum.CHD.getPassType().equals(queryCommonPersonInfo.getPassengerType())) {
                                    generalContactCertInfo.setCabinType(CommonPersonCabinType.CUF_CHD.getCode());
                                }
                            });
                        } else if (cabinTypes.contains(PackageTypeEnum.ADT_UNLIMITED_FLY.getPackType())) {
                            queryCommonPersonInfo.getContactCertList().forEach(generalContactCertInfo -> {
                                if (PassengerTypeEnum.ADT.getPassType().equals(queryCommonPersonInfo.getPassengerType())) {
                                    generalContactCertInfo.setCabinType(CommonPersonCabinType.UF_ADT.getCode());
                                } else if (PassengerTypeEnum.CHD.getPassType().equals(queryCommonPersonInfo.getPassengerType())) {
                                    generalContactCertInfo.setCabinType(CommonPersonCabinType.CUF_CHD.getCode());
                                }
                            });
                        }
                    }
                    TCountryDTO countryDTO = tCountryDTOList.stream().filter(tCountryDTO -> tCountryDTO.getCountryCode().equals(queryCommonPersonInfo.getNationality())).findFirst().orElse(null);
                    if (countryDTO != null) {
                        queryCommonPersonInfo.setNationalityName(StringUtil.isNullOrEmpty(countryDTO.getCountryName()) ? countryDTO.getCountryCode() : countryDTO.getCountryName());
                    }
                    //证件签发国
                    for (GeneralContactCertInfo generalContactCertInfo : queryCommonPersonInfo.getContactCertList()) {
                        generalContactCertInfo.setBelongCountryName(generalContactCertInfo.getBelongCountry());
                        TCountryDTO issuingCountryDTO = tCountryDTOList.stream().filter(tCountryDTO -> tCountryDTO.getCountryCode().equals(generalContactCertInfo.getBelongCountry())).findFirst().orElse(null);
                        if (issuingCountryDTO != null) {
                            generalContactCertInfo.setBelongCountryName(StringUtil.isNullOrEmpty(issuingCountryDTO.getCountryName()) ? issuingCountryDTO.getCountryCode() : issuingCountryDTO.getCountryName());
                        }
                        Date certExpireDate = DateUtils.toDate(generalContactCertInfo.getCertValidity(), DateUtils.YYYY_MM_DD_PATTERN);
                        if (null != certExpireDate) {
                            Calendar expiringDateLimit = Calendar.getInstance();
                            expiringDateLimit.setTime(certExpireDate);
                            expiringDateLimit.add(Calendar.MONTH, -6);
                            Date retDate = DateUtils.toDate(returnDate, DateUtils.YYYY_MM_DD_PATTERN);
                            Date depDate = DateUtils.toDate(departureDate, DateUtils.YYYY_MM_DD_PATTERN);
                            if (null != retDate && retDate.after(expiringDateLimit.getTime())) {
                                // span 内内容高亮
                                generalContactCertInfo.setMessage("按出入境规定，您的证件有效期距旅行结束日不足六个月，为避免无法乘机，请您及时<span>办理新证件</span>！");
                            } else if (null != depDate && depDate.after(expiringDateLimit.getTime())) {
                                generalContactCertInfo.setMessage("按出入境规定，您的证件有效期距出行日不足六个月，为避免无法乘机，请您及时<span>办理新证件</span>！");
                            }
                            Date now = new Date();
                            if (now.after(certExpireDate)) {
                                generalContactCertInfo.setExpiringMessage("已经过期");
                            } else if (CertificateTypeEnum.PASSPORT.getShowCode().equals(generalContactCertInfo.getCertType())) {
                                // 查询常用乘机人页面，提示有效期在一年内即将到期的护照
                                expiringDateLimit.add(Calendar.MONTH, -6);
                                generalContactCertInfo.setExpiringMessage(now.after(expiringDateLimit.getTime()) ? "即将过期" : "");
                            } else {
                                generalContactCertInfo.setExpiringMessage(now.after(expiringDateLimit.getTime()) ? "即将过期" : "");
                            }
                        }
                        // 过滤NI类型证件提醒
                        if (CertificateTypeEnum.ID_CARD.getShowCode().equals(generalContactCertInfo.getCertType())) {
                            generalContactCertInfo.setExpiringMessage("");
                        }
                    }
                    if (StringUtils.isBlank(queryCommonPersonInfo.getPassengerName())) {
                        queryCommonPersonInfo.setPassengerName(queryCommonPersonInfo.getPassEnNameS() + "/" + queryCommonPersonInfo.getPassEnNameF());
                    }
                }
            }
        } catch (Exception e) {
            log.error("常用乘机人处理国籍和证件签发国发生异常", e);
        }
    }
}

package com.juneyaoair.mobile.handler.controller;

import com.geetest.geeguard.sdk.GeetestLib;
import com.juneyaoair.appenum.ChannelCodeEnum;
import com.juneyaoair.appenum.WSEnum;
import com.juneyaoair.appenum.geetest.GeetestTypeEnum;
import com.juneyaoair.appenum.order.*;
import com.juneyaoair.baseclass.InsureQueryRule;
import com.juneyaoair.baseclass.basicsys.response.AirPortInfoDto;
import com.juneyaoair.baseclass.common.request.BaseReq;
import com.juneyaoair.baseclass.common.response.BaseResp;
import com.juneyaoair.baseclass.request.geetest.Geetest;
import com.juneyaoair.baseclass.request.order.query.OrderBriefReq;
import com.juneyaoair.baseclass.request.order.refund.query.RefundDetailInfoRequest;
import com.juneyaoair.baseclass.request.order.refund.query.TotalRefundTicketBriefRequest;
import com.juneyaoair.baseclass.request.speedRefund.*;
import com.juneyaoair.baseclass.response.insure.InsureInfo;
import com.juneyaoair.baseclass.response.order.query.ApplyStatusResp;
import com.juneyaoair.baseclass.response.order.query.OrderTotalBriefInfo;
import com.juneyaoair.baseclass.response.order.query.OrderTotalBriefResp;
import com.juneyaoair.baseclass.response.order.query.PassengerSegment;
import com.juneyaoair.baseclass.response.order.refund.detail.RefundOrderDetailResponse;
import com.juneyaoair.baseclass.response.order.refund.detail.RefundPassengerInfo;
import com.juneyaoair.baseclass.response.order.refund.detail.RefundTicketDetail;
import com.juneyaoair.baseclass.response.order.refund.detail.TransferSegmentInfo;
import com.juneyaoair.baseclass.response.order.refund.query.RefundTicketBriefInfo;
import com.juneyaoair.baseclass.response.order.refund.query.RefundTotalBriefInfo;
import com.juneyaoair.baseclass.response.order.refund.query.TotalRefundTicketBriefResp;
import com.juneyaoair.baseclass.response.speedRefund.*;
import com.juneyaoair.baseclass.speedrefund.request.BankNoInfo;
import com.juneyaoair.baseclass.ticket.req.QueryPassengerByTicketNoReq;
import com.juneyaoair.baseclass.ticket.resp.QueryPassengerInfoByTicketResp;
import com.juneyaoair.exception.NetworkException;
import com.juneyaoair.exception.OperationFailedException;
import com.juneyaoair.mobile.aop.annotation.InterfaceLog;
import com.juneyaoair.mobile.aop.annotation.NotDuplicate;
import com.juneyaoair.mobile.comm.service.IRedisService;
import com.juneyaoair.mobile.constants.MobileCoreBeanNames;
import com.juneyaoair.mobile.handler.HandlerConstants;
import com.juneyaoair.mobile.handler.config.bean.HandConfig;
import com.juneyaoair.mobile.handler.controller.service.InsuranceService;
import com.juneyaoair.mobile.handler.controller.util.IdentityInfoUtil;
import com.juneyaoair.mobile.handler.controller.util.OrderPayStateConvert;
import com.juneyaoair.mobile.handler.controller.util.SpeedTicketConvert;
import com.juneyaoair.mobile.handler.controller.v2.util.CommonUtil;
import com.juneyaoair.mobile.handler.controller.v2.util.V2OrderObjectConvert;
import com.juneyaoair.mobile.handler.manage.OrderManage;
import com.juneyaoair.mobile.handler.service.GeetestService;
import com.juneyaoair.mobile.handler.service.IBasicService;
import com.juneyaoair.mobile.handler.service.LocalCacheService;
import com.juneyaoair.mobile.handler.util.CertNoUtil;
import com.juneyaoair.mobile.handler.util.GeetestUtil;
import com.juneyaoair.thirdentity.request.order.query.PtOrderTotalBriefReq;
import com.juneyaoair.thirdentity.request.order.refund.query.PtRefundOrderDetailReq;
import com.juneyaoair.thirdentity.request.order.refund.query.PtTotalRefundBriefReq;
import com.juneyaoair.thirdentity.request.speedRefund.*;
import com.juneyaoair.thirdentity.response.detr.IdentityInfo;
import com.juneyaoair.thirdentity.response.detr.PtIBETicketInfo;
import com.juneyaoair.thirdentity.response.speedRefund.*;
import com.juneyaoair.utils.StringUtil;
import com.juneyaoair.utils.http.HttpResult;
import com.juneyaoair.utils.http.HttpUtil;
import com.juneyaoair.utils.json.JsonMapper;
import com.juneyaoair.utils.json.JsonUtil;
import com.juneyaoair.utils.util.BeanUtils;
import com.juneyaoair.utils.util.DateUtils;
import com.juneyaoair.utils.util.MdcUtils;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2017/1/16.
 * 极速退款管理类
 */
@RestController
@RequestMapping("speedRefund")
public class SpeedRefundController extends BassController {
    @Autowired
    private IBasicService basicService;
    @Autowired
    private HandConfig handConfig;
    @Autowired
    private InsuranceService insuranceService;
    @Resource(name = MobileCoreBeanNames.API_REDIS_SERVICE)
    private IRedisService apiRedisService;
    @Autowired
    private LocalCacheService localCacheService;
    @Autowired
    private GeetestService geetestService;
    @Autowired
    private OrderManage orderManage;

    private static final String REDIS_SPEED_REFUND = "SPEEDREFUND";
    private static final String ERROR_RESP = "查询网络出错";
    private static final String SUBORDER_FILTER_CONTENT = "CouponOrder";
    private static final String LOG_RESP_ONE = "网络返回数据空!";
    private static final String LOG_RESP_THREE = "查询订单列表返回结果空";
    private static final int CONNECT_TIMEOUT = 15000;
    private static final int READ_TIMEOUT = 15000;

    /**
     * 获取行程
     *
     * @param ticketInfoReq
     * @param request
     *
     * @return
     */
    @InterfaceLog
    @ApiOperation(value = "获取行程", notes = "获取行程")
    @RequestMapping(value = "getTicketInfo", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public TicketListInfoResp queryTicketList(@RequestBody @Validated TicketInfoReq ticketInfoReq, BindingResult bindingResult, HttpServletRequest request) {
        TicketListInfoResp resp = new TicketListInfoResp();
        String ip = getClientIP(request);
        String headChannelCode = request.getHeader(HEAD_CHANNEL_CODE);
        ticketInfoReq.setIp(ip);
        if (bindingResult.hasErrors()) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
            return resp;
        }
        if (StringUtils.isAnyBlank(ticketInfoReq.getFlightNo())) {
            throw new IllegalArgumentException("请求参数不完整");
        }
        //验证用户查询是否正常
        if (ChannelCodeEnum.MWEB.getChannelCode().equalsIgnoreCase(ticketInfoReq.getChannelCode())) {
            ticketInfoReq.setChannelCode(ChannelCodeEnum.MOBILE.getChannelCode());
        }
        boolean flag = this.checkKeyInfo(ticketInfoReq.getFfpId(), ticketInfoReq.getLoginKeyInfo(), ticketInfoReq.getChannelCode());
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        //极验验证操作
        GeetestLib gtSdk = GeetestUtil.initGeetest(headChannelCode, GeetestTypeEnum.TICKET.getGeetestType());
        HashMap<String, String> param = new HashMap<>();
        param.put("user_id", ip); //网站用户id  设备号
        param.put("client_type", ticketInfoReq.getClient_type()); //web:电脑上的浏览器；h5:手机上的浏览器，包括移动应用内完全内置的web_view；native：通过原生SDK植入APP应用的方式
        param.put("ip_address", ip); //传输用户请求验证时所携带的IP
        Geetest geetest = new Geetest(ticketInfoReq.getGeetest_challenge(), ticketInfoReq.getGeetest_validate(), ticketInfoReq.getGeetest_seccode());
        geetestService.validateMd5(gtSdk, geetest, param);
        //快速退票判断客户是使用证件还是票号获取客票信息？如果是证件，直接通过证件获取该证件的退票次数，如果是票号，通过票号查出客户证件，在查询客户证件的退票次数
        String userNo = getChannelInfo(ticketInfoReq.getChannelCode(), "10");
        PtRefundCountReq ptRefundCountReq = new PtRefundCountReq(HandlerConstants.VERSION, ticketInfoReq.getChannelCode(), userNo);
        String certType = CertNoUtil.getCertTypeByCertNo(ticketInfoReq.getCertificateNo());
        if (!"TN".equals(certType)) {
            ptRefundCountReq.setCertNo(ticketInfoReq.getCertificateNo());
            ptRefundCountReq.setPassengerName(ticketInfoReq.getPassengerName());
            int customerTicket = getCustomerTicket(ptRefundCountReq, request);
            if (customerTicket >= 3) {
                resp.setResultCode(WSEnum.ALERT_ERROR_MESSAGE.getResultCode());
                resp.setErrorInfo("抱歉，您的证件号已超过极速退款申请次数，如有疑问，欢迎致电吉祥航空客服热线95520咨询");
                return resp;
            }
        }
        TicketInfoRequest ticketInfoRequest = SpeedTicketConvert.formatTicketInfoRequest(ticketInfoReq, getChannelInfo(ticketInfoReq.getChannelCode(), "10"));
        Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
        TicketListInfoResponse ticketListInfoResponse = orderManage.getTicketInfo(ticketInfoRequest, headMap);
        try {
            List<IBETicketInfo> list = new ArrayList<>();
            boolean isEqual = true;//请求的姓名与返回的姓名数据是否一致
            if (CollectionUtils.isNotEmpty(ticketListInfoResponse.getIBETicketInfoList())) {
                Map<String, AirPortInfoDto> airportMap = basicService.queryAllAirportMap(ChannelCodeEnum.MOBILE.getChannelCode(), ip);
                for (PtIBETicketInfo ibeTicketInfo : ticketListInfoResponse.getIBETicketInfoList()) {
                    //2021-07-12 调整校验顺序。首先判断乘客姓名与证件是否匹配
                    if ("PASSENGER_ADULT".equals(ibeTicketInfo.getPassengerType()) || "PASSENGER_CHILD".equals(ibeTicketInfo.getPassengerType())) {//成人或儿童校验姓名
                        if (!ticketInfoReq.getPassengerName().equals(ibeTicketInfo.getPassengerName())) {
                            log.info("输入的姓名为:{}，客票的乘机姓名为:{}", ticketInfoReq.getPassengerName(), ibeTicketInfo.getPassengerName());
                            isEqual = false;
                            break;
                        }
                    }
                    //机场名称处理,费用处理,票号处理
                    IBETicketInfo ibe = null;
                    if ("TN".equals(certType)) {
                        ibe = SpeedTicketConvert.convertIBEByTicketNo(ibeTicketInfo, airportMap, handConfig);
                    } else {
                        if (ticketListInfoResponse.getIBETicketInfoList().size() == 1) {
                            ibe = SpeedTicketConvert.convertIBEByTicketNo(ibeTicketInfo, airportMap, handConfig);
                        } else {
                            //证件查询需要捕获异常，不直接返回
                            try {
                                ibe = SpeedTicketConvert.convertIBEByTicketNo(ibeTicketInfo, airportMap, handConfig);
                            } catch (Exception e) {
                                log.error("{}不符合快速退票,原因:{}", ibeTicketInfo.getTicketNo(), ibeTicketInfo.getUnValidReson());
                            }
                        }

                    }
                    //乘客姓名过滤,可退票号过滤,国内航班,无改签
                    if (ibe != null) {
                        if ("TN".equals(certType)) {
                            //FF表示的是会员卡号，过滤排除,PH表示的是国际航线phone项
                            IdentityInfo identityInfo = IdentityInfoUtil.getIdentityInfo(ibeTicketInfo.getIdentityInfoList());
                            if (identityInfo != null) {
                                ptRefundCountReq.setCertNo(identityInfo.getIdNo());
                                ptRefundCountReq.setPassengerName(ticketInfoReq.getPassengerName());
                                int customerTicket = getCustomerTicket(ptRefundCountReq, request);
                                if (customerTicket >= 3) {
                                    resp.setResultCode(WSEnum.ALERT_ERROR_MESSAGE.getResultCode());
                                    resp.setErrorInfo("抱歉，您的证件号已超过极速退款申请次数，如有疑问，欢迎致电吉祥航空客服热线95520咨询");
                                    return resp;
                                }
                            }
                        }
                        if (ibe.getRefundAble() > 0 && "D".equals(ibe.getInterFlag()) && ibe.isHOFlight()) {
                            list.add(ibe);
                        }
                    }
                }
                if (!isEqual) {
                    resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                    resp.setErrorInfo("您输入的证件号/票号或姓名有误，请核对后重新输入，如有疑问，欢迎致电吉祥航空客服热线95520咨询");
                    return resp;
                }
            }
            if (CollectionUtils.isNotEmpty(list)) {
                boolean check = false;
                for (IBETicketInfo ptIBETicketInfo : list) {
                    check = ptIBETicketInfo.getSegmentInfoList().stream().anyMatch(ptSegmentInfo -> {
                        if (ticketInfoReq.getFlightNo().equals(ptSegmentInfo.getFlightNo())) {
                            return true;
                        } else {
                            return false;
                        }
                    });
                    if (check) {
                        break;
                    }
                }
                if (!check) {
                    resp.setResultCode(WSEnum.NO_DATA.getResultCode());
                    resp.setErrorInfo("未查询符合条件的客票");
                    return resp;
                }
                if (list.size() == 1) {
                    IBETicketInfo ticketInfo = list.get(0);
                    if (ticketInfo.getSegmentInfoList().stream().anyMatch(segmentInfo -> "J".equals(CommonUtil.getCabinClassByCabinCode(segmentInfo.getCabin(), handConfig.getCabinClass())))) {
                        resp.setResultCode(WSEnum.ALERT_ERROR_MESSAGE.getResultCode());
                        resp.setErrorInfo("公务舱客票暂不支持快速退票，请联系原订票渠道办理，如有疑问，欢迎致电吉祥航空客服热线95520咨询");
                        return resp;
                    }
                }
                resp.setiBETicketInfoList(list);
                resp.setPassengerName(ticketInfoReq.getPassengerName());
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo("暂未查询到可办理极速退款的行程，请联系原订票渠道办理退票");
            }
            apiRedisService.replaceData(REDIS_SPEED_REFUND + ":" + ticketInfoReq.getFfpCardNo(), JsonUtil.objectToJson(resp), 24 * 3600L);
            return resp;

        } catch (Exception e) {
            saveError("快速退票查询", MdcUtils.getRequestId(), ip, JsonUtil.objectToJson(ticketInfoReq), e);
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            if (e instanceof OperationFailedException) {
                resp.setErrorInfo(e.getMessage());
            } else {
                resp.setErrorInfo("数据异常");
            }
            return resp;
        }
    }

    /**
     * 查询客户某个证件在一个月内的自愿退票次数
     *
     * @param refundCountReq
     * @param request
     * @return
     */
    private int getCustomerTicket(PtRefundCountReq refundCountReq, HttpServletRequest request) {
        int voluntaryRefundTimes = 0;
        Map<String, String> headMap = HttpUtil.getHeaderMap(this.getClientIP(request), "");
        HttpResult serviceResult = doPostClient(refundCountReq, HandlerConstants.URL_FARE_API + HandlerConstants.SUB_QUERY_REFUND_COUNT, headMap);
        if (null != serviceResult && serviceResult.isResult()) {
            try {
                PtRefundCountResp refundInsureDetailResp = (PtRefundCountResp) JsonUtil.jsonToBean(serviceResult.getResponse(), PtRefundCountResp.class);
                if ("1001".equals(refundInsureDetailResp.getResultCode())) {
                    voluntaryRefundTimes = refundInsureDetailResp.getVoluntaryRefundTimes();
                }
            } catch (Exception e) {
                log.error("【快速退票】查询客户自愿退票次数失败", e);
            }
        } else {
            log.error("【快速退票】查询客户自愿退票次数出错，请求参数：{}，响应参数：{}", JsonUtil.objectToJson(refundCountReq), JsonUtil.objectToJson(serviceResult));
        }
        return voluntaryRefundTimes;
    }

    /**
     * 客票直退请求
     *
     * @param ticketRefundReq
     * @param request
     * @return
     */
    @InterfaceLog
    @NotDuplicate
    @ApiOperation(value = "提交退票申请", notes = "提交退票申请")
    @RequestMapping(value = "ticketRefund", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public TicketRefundResp ticketRefund(@RequestBody @Validated TicketRefundReq ticketRefundReq, BindingResult bindingResult, HttpServletRequest request) {
        TicketRefundResp resp = new TicketRefundResp();
        String ip = getClientIP(request);
        Date curDate = new Date();
        String applyDate = DateUtils.getDateStringAll(curDate);
        ticketRefundReq.setIp(ip);
        if (bindingResult.hasErrors()) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
            return resp;
        }
        //验证用户查询是否正常
        if (ChannelCodeEnum.MWEB.getChannelCode().equalsIgnoreCase(ticketRefundReq.getChannelCode())) {
            ticketRefundReq.setChannelCode(ChannelCodeEnum.MOBILE.getChannelCode());
        }
        boolean flag = this.checkKeyInfo(ticketRefundReq.getFfpId(), ticketRefundReq.getLoginKeyInfo(), ticketRefundReq.getChannelCode());
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        //自愿退票增加提示
        String refundInfo = apiRedisService.getData(REDIS_SPEED_REFUND + ":" + ticketRefundReq.getFfpCardNo());
        TicketListInfoResp ticketListInfoResp = (TicketListInfoResp) JsonUtil.jsonToBean(refundInfo, TicketListInfoResp.class);
        if (null == ticketListInfoResp) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo("页面数据失效，请退出重新查询");
            return resp;
        }
        int noVoluntary = 0;
        for (RefundSegmentInfo refundSegmentInfo : ticketRefundReq.getRefundSegmentInfoList()) {
            for (IBETicketInfo ibeTicketInfo : ticketListInfoResp.getiBETicketInfoList()) {
                for (SegmentInfo segmentInfo : ibeTicketInfo.getSegmentInfoList()) {
                    if (refundSegmentInfo.getTicketNo().replace("-", "").equals(ibeTicketInfo.getTicketNo().replace("-", ""))
                            && refundSegmentInfo.getDepAirport().equalsIgnoreCase(segmentInfo.getDepAirportCode())
                            && refundSegmentInfo.getArrAirport().equals(segmentInfo.getArrAirportCode())
                            && !ibeTicketInfo.isVoluntary()) {
                        noVoluntary++;
                    }
                }
            }
        }
        if (noVoluntary != ticketRefundReq.getRefundSegmentInfoList().size()) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setErrorInfo("因系统升级，自愿退票暂时无法提交，请至原出票地办理退票或拨打95520咨询");
            return resp;
        }
        if (StringUtil.isNullOrEmpty(ticketRefundReq.getpNR())) {
            ticketRefundReq.setpNR("AAAAAA");
        }
        //封装.net平台参数
        TicketRefundRequest ticketRefundRequest = SpeedTicketConvert.formatTicketRefundRequest(ticketRefundReq,
                getChannelInfo(ticketRefundReq.getChannelCode(), "10"));
        String serviceResult = this.invokePost(ticketRefundRequest, HandlerConstants.URL_FARE_API + HandlerConstants.SPEEDTICKET_REQ_URL);
        if (null != serviceResult) {
            try {
                TicketRefundResponse ticketRefundResponse = (TicketRefundResponse) JsonUtil.jsonToBean
                        (serviceResult, TicketRefundResponse.class);
                if ("1001".equals(ticketRefundResponse.getResultCode())) {
                    BeanUtils.copyProperties(ticketRefundResponse, resp);
                    //银行账号隐藏部分数字
                    String bankNo = ticketRefundReq.getBankAccountNO();
                    bankNo = SpeedTicketConvert.createAsterisk(bankNo);//银行帐号处理
                    resp.setBankAccountNO(bankNo);//银行账号
                    resp.setApplyDate(applyDate);//申请时间
                    resp.setRefundAmount(ticketRefundResponse.getRefundAmount());//退款总额
                    resp.setChannelPayoutNo(ticketRefundReq.getChannelPayoutNo());
                    resp.setRefundType(ticketRefundReq.getRefundType());
                    resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                    resp.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
                } else if ("1003".equals(ticketRefundResponse.getResultCode())) {//次数超限
                    resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                    resp.setErrorInfo(ticketRefundResponse.getErrorInfo());
                } else {//其他错误
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setErrorInfo(ticketRefundResponse.getErrorInfo());
                }
                return resp;
            } catch (Exception e) {
                log.error("请求号:{}，IP地址:{}，服务端响应结果：{}", MdcUtils.getRequestId(), ip, e);
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo("数据异常！");
                return resp;
            }
        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo(ERROR_RESP);
            return resp;
        }
    }

    /**
     * 我的极速退票
     *
     * @param queryTicketPayoutReq
     * @param request
     * @return
     */
    @ApiOperation(value = "查询我的快速退票记录", notes = "查询我的快速退票记录")
    @RequestMapping(value = "queryTicketPayout", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public QueryTicketPayoutResp queryTicketPayout(@RequestBody QueryTicketPayoutReq queryTicketPayoutReq,
                                                   HttpServletRequest request) {
        QueryTicketPayoutResp resp = new QueryTicketPayoutResp();
        String ip = this.getClientIP(request);
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<QueryTicketPayoutReq>> violations = validator.validate(queryTicketPayoutReq);
        if (CollectionUtils.isNotEmpty(violations)) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(violations.iterator().next().getMessage());
            return resp;
        }
        //如果是MWEB渠道，修改为MOBILE
        if (ChannelCodeEnum.MWEB.getChannelCode().equalsIgnoreCase(queryTicketPayoutReq.getChannelCode())) {
            queryTicketPayoutReq.setChannelCode(ChannelCodeEnum.MOBILE.getChannelCode());
        }
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(queryTicketPayoutReq.getFfpId(), queryTicketPayoutReq.getLoginKeyInfo(), queryTicketPayoutReq.getChannelCode());
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        //封装.net请求参数
        QueryTicketPayoutRequest queryTicketPayoutRequest = SpeedTicketConvert.formatQueryTicketPayoutRequest
                (queryTicketPayoutReq, getChannelInfo(queryTicketPayoutReq.getChannelCode(), "10"));
        String serviceResult = this.invokePost(queryTicketPayoutRequest, HandlerConstants.URL_FARE_API + HandlerConstants
                .SPEEDTICKET_QUERY_URL);
        if (null != serviceResult) {
            try {
                QueryTicketPayoutResponse queryTicketPayoutResponse = (QueryTicketPayoutResponse) JsonUtil.jsonToBean
                        (serviceResult, QueryTicketPayoutResponse.class);
                if ("1001".equals(queryTicketPayoutResponse.getResultCode())) {
                    Map<String, AirPortInfoDto> airportMap = basicService.queryAllAirportMap(ChannelCodeEnum.MOBILE.getChannelCode(), ip);
                    SpeedTicketConvert.formatTicketPayoutResp(queryTicketPayoutResponse, resp, airportMap);
                    resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                    resp.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
                } else {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setErrorInfo(queryTicketPayoutResponse.getErrorInfo());
                }
                return resp;
            } catch (Exception e) {
                log.error("数据转换异常：{}", e.getMessage());
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo("数据异常");
                return resp;
            }
        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo(ERROR_RESP);
            return resp;
        }
    }

    @ApiOperation(value = "查询支持银行列表", notes = "查询支持银行列表")
    @RequestMapping(value = "queryBankList", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp<String[]> queryBankList(@RequestBody BaseReq req, HttpServletRequest request) {
        BaseResp<String[]> resp = new BaseResp<String[]>();
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<BaseReq>> violations = validator.validate(req);
        if (CollectionUtils.isNotEmpty(violations)) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo(violations.iterator().next().getMessage());
            return resp;
        }
        try {
            String[] bankNames = handConfig.getBankNameList().split(",");
            if (bankNames != null && bankNames.length > 0) {
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                resp.setObjData(bankNames);
                return resp;
            } else {
                resp.setResultCode(WSEnum.NO_DATA.getResultCode());
                resp.setResultInfo(WSEnum.NO_DATA.getResultInfo());
                return resp;
            }
        } catch (Exception e) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("返回数据异常！");
            return resp;
        }
    }

    @ApiOperation(value = "卡号获取对应的银行名称", notes = "卡号获取对应的银行名称")
    @RequestMapping(value = "queryBankByNo", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp<String> queryBankByNo(@RequestBody BaseReq<BankNoInfo> req, HttpServletRequest request) {
        BaseResp<String> resp = new BaseResp<>();
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<BaseReq<BankNoInfo>>> violations = validator.validate(req);
        if (CollectionUtils.isNotEmpty(violations)) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo(violations.iterator().next().getMessage());
            return resp;
        }
        BankNoInfo bankNoInfo = req.getRequest();
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(bankNoInfo.getFfpId(), bankNoInfo.getLoginKeyInfo(), req.getChannelCode());
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        //通过接口方式获取银行名称
        PtBankInfoReq ptRequest = SpeedTicketConvert.fortmatPtRequest(req.getChannelCode(), getChannelInfo(req.getChannelCode(), "10"), bankNoInfo.getBankAccountNo());
        Map<String, String> parametersMap = ptRequest.getParaMap();
        HttpResult serviceResult = this.doPayPost(HandlerConstants.EPAY_URL + HandlerConstants.EPAY_URL_BANK_QUERY, parametersMap);
        if (null != serviceResult && serviceResult.isResult()) {
            try {
                PtBankResp ptResponse = (PtBankResp) JsonUtil.jsonToBean(serviceResult.getResponse(), PtBankResp.class);
                if (!"1001".equals(ptResponse.getRespCode())) {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo(ptResponse.getErrMsg());
                    return resp;
                }
                resp.setObjData(ptResponse.getBankName());
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                return resp;
            } catch (Exception e) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("数据解析异常！");
                return resp;
            }
        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("查询网络出错！");
            return resp;
        }
    }

    @InterfaceLog
    @NotDuplicate
    @ApiOperation(value = "直退重新提交", notes = "直退重新提交")
    @RequestMapping(value = "refundRetry", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp<TicketRefundResp> refundRetry(@RequestBody @Validated BaseReq<TicketRefundRetry> req, BindingResult bindingResult, HttpServletRequest request) {
        BaseResp<TicketRefundResp> resp = new BaseResp<>();
        String ip = this.getClientIP(request);
        try {
            if (bindingResult.hasErrors()) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
                return resp;
            }
            TicketRefundRetry ticketRefundRetry = req.getRequest();
            //验证用户查询是否正常
            boolean flag = this.checkKeyInfo(ticketRefundRetry.getFfpId(), ticketRefundRetry.getLoginKeyInfo(), req.getChannelCode());
            if (!flag) {
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                return resp;
            }
            PtTicketPayoutRetryRequest ptTicketPayoutRetryRequest = createPtTicketPayoutRetryRequest(req);
            String serviceResult = this.invokePost(ptTicketPayoutRetryRequest, HandlerConstants.URL_FARE_API + HandlerConstants.SPEEDTICKET_REFUND_RETRL);
            if (serviceResult != null) {
                PtTicketPayoutRetryResponse ptTicketPayoutRetryResponse = (PtTicketPayoutRetryResponse) JsonUtil.jsonToBean(serviceResult, PtTicketPayoutRetryResponse.class);
                if ("1001".equals(ptTicketPayoutRetryResponse.getResultCode())) {
                    TicketRefundResp ticketRefundResp = new TicketRefundResp();
                    ticketRefundResp.setApplyDate(DateUtils.getDateStringAll(new Date()));
                    ticketRefundResp.setRefundType(ticketRefundRetry.getRefundType());
                    ticketRefundResp.setRefundAmount(ticketRefundRetry.getRefundAmount());
                    ticketRefundResp.setBankAccountNO(SpeedTicketConvert.createAsterisk(ticketRefundRetry.getBankAccountNo()));
                    resp.setObjData(ticketRefundResp);
                    resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                    resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                } else {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo(ptTicketPayoutRetryResponse.getErrorInfo());
                }
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("退款网络异常！");
            }
        } catch (Exception e) {
            log.error("请求号:{}，IP地址:{}，错误信息描述：{}，堆栈信息：", MdcUtils.getRequestId(), ip, e.getMessage(), e);
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("退款申请失败！");
        }
        return resp;
    }

    @ApiOperation(value = "查询半年内所有已付款出票订单", notes = "查询半年内所有已付款出票订单")
    @RequestMapping(value = "queryOrderListPayed", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public OrderTotalBriefResp queryOrderListPayed(@RequestBody BaseReq<OrderBriefReq> req, HttpServletRequest request) {
        OrderTotalBriefResp resp = new OrderTotalBriefResp();
        String headChannelCode = request.getHeader(HandlerConstants.TOKEN_CHANNELCODE);
        String reqId = headChannelCode + StringUtil.newGUID() + request.getRequestURI();
        String ip = getClientIP(request);
        String reqJson = JsonMapper.buildNormalMapper().toJson(req);
        try {
            ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
            Validator validator = factory.getValidator();
            Set<ConstraintViolation<BaseReq<OrderBriefReq>>> violations = validator.validate(req);
            if (CollectionUtils.isNotEmpty(violations)) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setErrorInfo(violations.iterator().next().getMessage());
                return resp;
            }
            //判断登录状态
            OrderBriefReq briefReq = req.getRequest();
            if (ChannelCodeEnum.MWEB.getChannelCode().equalsIgnoreCase(req.getChannelCode())) {
                req.setChannelCode(ChannelCodeEnum.MOBILE.getChannelCode());
            }
            boolean flag = this.checkKeyInfo(briefReq.getCustomerNo(), briefReq.getLoginKeyInfo(), req.getChannelCode());
            if (!flag) {
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                return resp;
            }
            // 取渠道工作人员号、key
            String userNo = getChannelInfo(req.getChannelCode(), "10");
            //构建请求类
            if (briefReq.getPageNo() <= 0) {
                briefReq.setPageNo(1);
            }
            if (briefReq.getPageSize() <= 0) {
                briefReq.setPageSize(100);
            }
            PtOrderTotalBriefReq ptBrief = new PtOrderTotalBriefReq(HandlerConstants.VERSION, req.getChannelCode(), userNo);
            if (StringUtil.isNullOrEmpty(briefReq.getState())) {
                ptBrief.setOrderState("Finish");
                ptBrief.setPayState("Pay");
            }
            ptBrief.setSubOrderTypeFilterIndex(0);
            ptBrief.setSubOrderTypeFilterContent(SUBORDER_FILTER_CONTENT);
            ptBrief.setRemoveNullType(true);
            Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
            createReq(ptBrief, briefReq);
            HttpResult result = doPostClient(ptBrief, HandlerConstants.URL_FARE_API + HandlerConstants.ORDER_BRIEF, headMap, handConfig.getReadTimeout(), handConfig.getConnectTimeout());
            if (result.isResult()) {
                resp = (OrderTotalBriefResp) JsonUtil.jsonToBean(result.getResponse(), OrderTotalBriefResp.class);
                OrderTotalBriefResp orderTotalBriefResp = getOrderTotalBriefResp(resp, reqId, ip);
                // 处理改期后出现航段重复的情况
                handleRepeatedSegment(orderTotalBriefResp);
                setInsuranceName(orderTotalBriefResp);
                SetPassNameAndTicketNo(orderTotalBriefResp);
                return orderTotalBriefResp;
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo(LOG_RESP_ONE);
                return resp;
            }
        } catch (Exception e) {
            saveError("查询订单列表", reqId, ip, reqJson, e);
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo(LOG_RESP_THREE);
            return resp;
        }
    }

    /**
     * 处理改期后出现（新航段与旧航段）航段重复的情况
     *
     * @param orderTotalBriefResp
     */
    private void handleRepeatedSegment(OrderTotalBriefResp orderTotalBriefResp) {
        List<OrderTotalBriefInfo> orderBriefInfoList = orderTotalBriefResp.getOrderBriefInfoList();
        if (null != orderBriefInfoList) {
            for (OrderTotalBriefInfo elem : orderBriefInfoList
            ) {
                if ("Change".equals(elem.getOrderSort())) {
                    // 对航段信息按照出发和到达城市进行分组
                    List<com.juneyaoair.baseclass.response.order.query.SegmentInfo> segmentInfoList = elem.getSegmentInfoList();
                    ArrayList<com.juneyaoair.baseclass.response.order.query.SegmentInfo> segmentInfosNew = new ArrayList<>();
                    if (segmentInfoList.size() > 0) {
                        Map<String, List<com.juneyaoair.baseclass.response.order.query.SegmentInfo>> segmentMap = segmentInfoList.stream().sorted(Comparator.comparing(com.juneyaoair.baseclass.response.order.query.SegmentInfo::getDepDateTime).reversed())
                                .collect(Collectors.groupingBy(segmentInfo -> segmentInfo.getDepCityName() + "_" + segmentInfo.getArrCityName()));
                        for (Map.Entry<String, List<com.juneyaoair.baseclass.response.order.query.SegmentInfo>> entry : segmentMap.entrySet()
                        ) {
                            if (CollectionUtils.isNotEmpty(entry.getValue())) {
                                // 取 list 首并放入到新 list 中
                                segmentInfosNew.add(entry.getValue().get(0));
                            }
                        }
                        elem.setSegmentInfoList(segmentInfosNew);
                    }
                }
            }
        }
    }


    @ApiOperation(value = "查询退单列表（含快速退单和普通退单）", notes = "查询退单列表（含快速退单和普通退单）")
    @RequestMapping(value = "queryRefundList", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public TotalRefundTicketBriefResp queryRefundList(@RequestBody TotalRefundTicketBriefRequest req, HttpServletRequest request) {
        TotalRefundTicketBriefResp response = new TotalRefundTicketBriefResp();
        String ip = this.getClientIP(request);
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<TotalRefundTicketBriefRequest>> violations = validator.validate(req);
        if (CollectionUtils.isNotEmpty(violations)) {
            response.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            response.setErrorInfo(violations.iterator().next().getMessage());
            return response;
        }
        //验证用户查询是否正常
        if (ChannelCodeEnum.MWEB.getChannelCode().equalsIgnoreCase(req.getChannelCode())) {
            req.setChannelCode(ChannelCodeEnum.MOBILE.getChannelCode());
        }
        boolean flag = this.checkKeyInfo(req.getCustomerNo(), req.getLoginKeyInfo(), req.getChannelCode());
        if (!flag) {
            response.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            response.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return response;
        }
        String userNo = getChannelInfo(req.getChannelCode(), "10");
        // 1. 查询普通退单列表
        PtTotalRefundBriefReq ptBrief = new PtTotalRefundBriefReq(HandlerConstants.VERSION, req.getChannelCode(), userNo);
        BeanUtils.copyProperties(req, ptBrief);
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MONTH, -6);
        String CreateDateBegin = DateUtils.dateToString(calendar.getTime(), DateUtils.YYYY_MM_DD_PATTERN);
        String CreateDateEnd = DateUtils.dateToString(new Date(), DateUtils.YYYY_MM_DD_PATTERN);
        ptBrief.setCreateDateBegin(CreateDateBegin);
        ptBrief.setCreateDateEnd(CreateDateEnd);
        ptBrief.setRefundState("0");
        // 目前不做分页，先写死页码和页大小
        ptBrief.setPageNo(1);
        ptBrief.setPageSize(50);
        HashMap<String, String> map = new HashMap<>();
        HttpResult serviceResult = doPostClient(ptBrief, HandlerConstants.URL_FARE_API + HandlerConstants.SUB_QUERY_TOTAL_REFUND, map);
        List<RefundTicketBriefInfo> totalRefundBriefInfo = new ArrayList<>();
        String[] status = {OrderRefundStateEnum.Problem.getCode(), OrderRefundStateEnum.Apply.getCode(),
                OrderRefundStateEnum.OCheckBack.getCode(), OrderRefundStateEnum.SCheckBack.getCode(),
                OrderRefundStateEnum.Init.getCode(), OrderRefundStateEnum.OCheck.getCode()
        };
        try {
            if (serviceResult.isResult() && StringUtils.isNotBlank(serviceResult.getResponse())) {
                response = (TotalRefundTicketBriefResp) JsonUtil.jsonToBean(serviceResult.getResponse(), TotalRefundTicketBriefResp.class);
                if (response.getResultCode().equals("1001")) {
                    Map<String, AirPortInfoDto> airportMap = basicService.queryAllAirportMap(req.getChannelCode(), ip);
                    if (!StringUtil.isNullOrEmpty(response.getTotalRefundBriefInfoList())) {
                        List<RefundTicketBriefInfo> totalRefundBriefInfoList = response.getTotalRefundBriefInfoList();
                        totalRefundBriefInfo = totalRefundBriefInfoList.stream().filter(o -> null != o.getSegmentInfoList()).collect(Collectors.toList());
                        for (RefundTicketBriefInfo refundTicketBriefInfo : totalRefundBriefInfo) {
                            refundTicketBriefInfo.setIsSpeedRefund(false);
                            if (Arrays.asList(status).contains(refundTicketBriefInfo.getRefundState())) {
                                refundTicketBriefInfo.setChargebackState(NewRefundOrderStateEnum.REVIEW.getStateCode());
                                refundTicketBriefInfo.setChargebackText(NewRefundOrderStateEnum.REVIEW.getStateDesc());
                            }
                            if (refundTicketBriefInfo.getRefundState().equals(RefundOrderStateEnum.SCheck.getStateCode())) {
                                refundTicketBriefInfo.setChargebackState(NewRefundOrderStateEnum.CHECKED.getStateCode());
                                refundTicketBriefInfo.setChargebackText(NewRefundOrderStateEnum.CHECKED.getStateDesc());
                            }
                            if (!StringUtil.isNullOrEmpty(refundTicketBriefInfo.getRebateState()) && refundTicketBriefInfo.getRebateState().equals(RefundOrderStateEnum.Success.getStateCode())) {
                                refundTicketBriefInfo.setChargebackState(NewRefundOrderStateEnum.REFUNDED.getStateCode());
                                refundTicketBriefInfo.setChargebackText(NewRefundOrderStateEnum.REFUNDED.getStateDesc());
                            }
                            if (!StringUtil.isNullOrEmpty(refundTicketBriefInfo.getRebateState()) && refundTicketBriefInfo.getRebateState().equals(RefundOrderStateEnum.Fail.getStateCode())) {
                                refundTicketBriefInfo.setChargebackState(NewRefundOrderStateEnum.FAILURE.getStateCode());
                                refundTicketBriefInfo.setChargebackText(NewRefundOrderStateEnum.FAILURE.getStateDesc());
                            }
                            if (!StringUtil.isNullOrEmpty(refundTicketBriefInfo.getSegmentInfoList())) {
                                for (com.juneyaoair.baseclass.response.order.query.SegmentInfo segmentInfo : refundTicketBriefInfo.getSegmentInfoList()) {
                                    segmentInfo.setDepCityName(airportMap.get(segmentInfo.getDepAirport()).getCityName());
                                    segmentInfo.setArrCityName(airportMap.get(segmentInfo.getArrAirport()).getCityName());
                                    segmentInfo.setDepAirportName(airportMap.get(segmentInfo.getDepAirport()).getAirPortName());
                                    segmentInfo.setArrAirportName(airportMap.get(segmentInfo.getArrAirport()).getAirPortName());
                                }
                            }
                        }
                        // 对重复航段进行过滤
                        resolveRepeatedSegment(response.getTotalRefundBriefInfoList());

                    } else {
                        response.setTotalRefundBriefInfoList(new ArrayList<>());
                    }
                }
            }
        } catch (Exception e) {
            log.error("查询普通退单列表时出错，IP地址:{}，错误信息描述：{}，堆栈信息：", ip, e.getMessage(), e);
            response.setResultCode(WSEnum.ERROR.getResultCode());
            response.setErrorInfo("查询普通退单列表时出错！");
            return response;
        }
        // 2.查询快速退单列表
        // 先取出查询普通订单的列表页时的list，用于后面进行追加
        List<RefundTicketBriefInfo> totalRefundBriefInfoList = totalRefundBriefInfo;
        QueryTicketPayoutRequest queryTicketPayoutRequest = new QueryTicketPayoutRequest(HandlerConstants.VERSION, req.getChannelCode(), userNo, 50, 1, CreateDateEnd, CreateDateBegin);
        queryTicketPayoutRequest.setChannelCustomerNo(req.getCustomerNo());
        String serviceSpeedRefundResult = this.invokePost(queryTicketPayoutRequest, HandlerConstants.URL_FARE_API + HandlerConstants
                .SPEEDTICKET_QUERY_URL);
        if (null != serviceSpeedRefundResult) {
            QueryTicketPayoutResponse queryTicketPayoutResponse = (QueryTicketPayoutResponse) JsonUtil.jsonToBean
                    (serviceSpeedRefundResult, QueryTicketPayoutResponse.class);
            try {
                if ("1001".equals(queryTicketPayoutResponse.getResultCode())) {
                    Map<String, AirPortInfoDto> airportMap = basicService.queryAllAirportMap(ChannelCodeEnum.MOBILE.getChannelCode(), ip);
                    List<RefundTicketBriefInfo> refundTicketBriefInfos = SpeedTicketConvert.formatTicketRefundResp(totalRefundBriefInfoList, queryTicketPayoutResponse, airportMap);
                    response.setTotalRefundBriefInfoList(refundTicketBriefInfos);
                    response.setResultCode(WSEnum.SUCCESS.getResultCode());
                    response.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
                }
            } catch (Exception e) {
                log.error("查询快速退单列表时出错，IP地址:{}，错误信息描述：{}，堆栈信息：", ip, e.getMessage(), e);
                response.setResultCode(WSEnum.ERROR.getResultCode());
                response.setErrorInfo("查询快速退单列表时出错！");
                return response;
            }
        } else {
            response.setResultCode(WSEnum.ERROR.getResultCode());
            response.setErrorInfo("查询快速退单列表时出错！");
            return response;
        }
        return response;
    }

    private void resolveRepeatedSegment(List<RefundTicketBriefInfo> totalRefundBriefInfoList) {
        if (null != totalRefundBriefInfoList) {
            for (RefundTicketBriefInfo elem : totalRefundBriefInfoList
            ) {
                // 取出原有航段进行准备过滤
                List<com.juneyaoair.baseclass.response.order.query.SegmentInfo> segmentListNew = new ArrayList<>();
                List<com.juneyaoair.baseclass.response.order.query.SegmentInfo> segmentInfoList = elem.getSegmentInfoList();
                // 对航段信息按照航段ID进行分组
                if (null != segmentInfoList) {
                    Map<Integer, List<com.juneyaoair.baseclass.response.order.query.SegmentInfo>> collectMap = segmentInfoList.stream().collect(Collectors.groupingBy(com.juneyaoair.baseclass.response.order.query.SegmentInfo::getSegmentID));
                    for (Map.Entry<Integer, List<com.juneyaoair.baseclass.response.order.query.SegmentInfo>> entry : collectMap.entrySet()
                    ) {
                        if (CollectionUtils.isNotEmpty(entry.getValue())) {
                            segmentListNew.add(entry.getValue().get(0));
                        }
                    }
                    elem.setSegmentInfoList(segmentListNew);
                }

            }
        }

    }

    @ApiOperation(value = "查询退单详情", notes = "查询退单详情")
    @RequestMapping(value = "queryRefundDetail", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @InterfaceLog
    public RefundOrderDetailResponse queryRefundDetail(@RequestBody @Validated RefundDetailInfoRequest req, BindingResult bindingResult, HttpServletRequest request) {
        RefundOrderDetailResponse response = new RefundOrderDetailResponse();
        List<ApplyStatusResp> list = new ArrayList();
        //验证用户查询是否正常
        if (ChannelCodeEnum.MWEB.getChannelCode().equalsIgnoreCase(req.getChannelCode())) {
            req.setChannelCode(ChannelCodeEnum.MOBILE.getChannelCode());
        }
        if (bindingResult.hasErrors()) {
            response.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            response.setErrorInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
            return response;
        }
        boolean flag = this.checkKeyInfo(req.getCustomerNo(), req.getLoginKeyInfo(), req.getChannelCode());
        if (!flag) {
            response.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            response.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return response;
        }
        String[] status = {OrderRefundStateEnum.Init.getCode(), OrderRefundStateEnum.Apply.getCode(), OrderRefundStateEnum.OCheck.getCode(),
                OrderRefundStateEnum.OCheckBack.getCode(), OrderRefundStateEnum.SCheckBack.getCode(), OrderRefundStateEnum.Problem.getCode()};
        String[] status1 = {OrderRefundStateEnum.SCheck.getCode()};
        String ip = this.getClientIP(request);
        String userNo = getChannelInfo(req.getChannelCode(), "10");
        //1. 查询普通退单的详情
        if (!req.getIsSpeedRefund()) {
            PtRefundOrderDetailReq ptBrief = new PtRefundOrderDetailReq(HandlerConstants.VERSION, req.getChannelCode(), userNo);
            BeanUtils.copyProperties(req, ptBrief);
            Map<String, String> headMap = HttpUtil.getHeaderMap(this.getClientIP(request), "");
            String serviceResult = invokePost(ptBrief, HandlerConstants.URL_FARE_API + HandlerConstants.SUB_QUERY_REFUND_DETAIL, headMap, READ_TIMEOUT, CONNECT_TIMEOUT);
            if (StringUtils.isNotBlank(serviceResult)) {

                try {
                    response = (RefundOrderDetailResponse) JsonUtil.jsonToBean(serviceResult, RefundOrderDetailResponse.class);
                    Map<String, AirPortInfoDto> airportMap = basicService.queryAllAirportMap(ChannelCodeEnum.MOBILE.getChannelCode(), ip);
                    if (response.getResultCode().equals("1001")) {
                        //手动设置城市名
                        List<RefundPassengerInfo> refundPassengerInfoList = response.getRefundTicketPassenger().getRefundPassengerInfoList();
                        for (RefundPassengerInfo e : refundPassengerInfoList) {
                            List<RefundTicketDetail> refundDetailList = e.getRefundDetailList();
                            for (RefundTicketDetail elem : refundDetailList) {
                                com.juneyaoair.baseclass.response.order.refund.detail.SegmentInfo segmentInfo = elem.getSegmentInfo();
                                AirPortInfoDto dep = airportMap.get(segmentInfo.getDepAirport());
                                AirPortInfoDto arr = airportMap.get(segmentInfo.getArrAirport());
                                segmentInfo.setDepDateTime(segmentInfo.getDepDateTime());
                                segmentInfo.setDepAirportName(dep == null ? "" : dep.getAirPortName());
                                segmentInfo.setArrAirportName(arr == null ? "" : arr.getAirPortName());
                                segmentInfo.setDepCityName(dep == null ? "" : dep.getCityName());
                                segmentInfo.setArrCityName(arr == null ? "" : arr.getCityName());
                            }

                            List<RefundTicketDetail> refundTransferDetailList = e.getRefundTransferTicketDetailDtoList();
                            if (CollectionUtils.isNotEmpty(refundTransferDetailList)) {
                                refundTransferDetailList.forEach(elem -> {
                                    TransferSegmentInfo segmentInfo = elem.getTransferSegmentInfo();
                                    AirPortInfoDto dep = airportMap.get(segmentInfo.getDepAirport());
                                    AirPortInfoDto arr = airportMap.get(segmentInfo.getArrAirport());
                                    AirPortInfoDto transfer = airportMap.get(segmentInfo.getTransferAirport());
                                    segmentInfo.setDepCityName(dep == null ? "" : dep.getCityName());
                                    segmentInfo.setArrCityName(arr == null ? "" : arr.getCityName());
                                    segmentInfo.setTransferCityName(arr == null ? "" : transfer.getCityName());
                                });

                            }
                        }


                        //是否自愿退票
                        Boolean isVoluntaryRefund = response.getRefundTicketPassenger().getVoluntaryRefund();
                        if (isVoluntaryRefund) {
                            //自愿退票  退票费减去产品差价
                            Double RefundableAmountSum = response.getRefundTicketPassenger().getRefundableAmountSum() - response.getRefundTicketPassenger().getDisneyTicAmtSum();
                            response.getRefundTicketPassenger().setRefundableAmountSum(RefundableAmountSum);

                        }

                        if (null != response.getOrderNo()) {
                            //如果能查询到原订单号，则可以查看原订单详情
                            response.setAble2SeeOrderDetail(true);
                        }
                        ApplyStatusResp applyStatusResp = new ApplyStatusResp("提交退票申请", disposeDate(response.getRefundTicketPassenger().getRefundApplyDatetime()), "N", null);
                        ApplyStatusResp applyStatusResp1 = new ApplyStatusResp("已审核", disposeDate(response.getRefundTicketPassenger().getSecondDatetime()), "Y", "N");
                        ApplyStatusResp applyStatusResp2 = new ApplyStatusResp();
                        if (Arrays.asList(status).contains(response.getRefundTicketPassenger().getRefundState())) {
                            response.getRefundTicketPassenger().setChargebackState(NewRefundOrderStateEnum.REVIEW.getStateCode());
                            response.getRefundTicketPassenger().setChargebackText(NewRefundOrderStateEnum.REVIEW.getStateDesc());
                            applyStatusResp.setNewStatus("Y");
                            list.add(applyStatusResp);
                            list.add(new ApplyStatusResp("已审核", null, "N", null));
                            list.add(new ApplyStatusResp("已退至支付平台", null, "N", null));
                            response.setApplyStatusRespList(list);
                            response.setResultCode(WSEnum.SUCCESS.getResultCode());
                            response.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
                        }
                        if (Arrays.asList(status1).contains(response.getRefundTicketPassenger().getRefundState()) || (response.getRefundTicketPassenger().getRebateState() != null && response.getRefundTicketPassenger().getRebateState().equals(RefundRebateStateEnum.Dealing.getCode()))) {
                            response.getRefundTicketPassenger().setChargebackState(NewRefundOrderStateEnum.CHECKED.getStateCode());
                            response.getRefundTicketPassenger().setChargebackText(NewRefundOrderStateEnum.CHECKED.getStateDesc());
                            list.clear();
                            applyStatusResp.setPassStatus("Y");
                            list.add(applyStatusResp);
                            applyStatusResp1.setNewStatus("Y");
                            list.add(applyStatusResp1);
                            list.add(new ApplyStatusResp("已退至支付平台", null, "N", null));
                            response.setApplyStatusRespList(list);
                            response.setResultCode(WSEnum.SUCCESS.getResultCode());
                            response.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
                        }
                        if (response.getRefundTicketPassenger().getRebateState() != null && response.getRefundTicketPassenger().getRebateState().equals(RefundRebateStateEnum.Success.getCode())) {
                            response.getRefundTicketPassenger().setChargebackState(NewRefundOrderStateEnum.REFUNDED.getStateCode());
                            response.getRefundTicketPassenger().setChargebackText(NewRefundOrderStateEnum.REFUNDED.getStateDesc());
                            list.clear();
                            applyStatusResp.setNewStatus(null);
                            applyStatusResp.setPassStatus("Y");
                            applyStatusResp1.setNewStatus(null);
                            applyStatusResp2.setNewStatus("Y");
                            applyStatusResp2.setPassStatus("Y");
                            applyStatusResp2.setShowOfficial("已退至支付平台");
                            applyStatusResp2.setNowStatusDate(disposeDate(response.getRefundTicketPassenger().getSecondCheckDatetime()));
                            list.add(applyStatusResp);
                            list.add(applyStatusResp1);
                            list.add(applyStatusResp2);
                            response.setApplyStatusRespList(list);
                            response.setResultCode(WSEnum.SUCCESS.getResultCode());
                            response.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
                        }
                        if (response.getRefundTicketPassenger().getRebateState() != null && response.getRefundTicketPassenger().getRebateState().equals(RefundRebateStateEnum.Fail.getCode())) {
                            response.getRefundTicketPassenger().setChargebackState(NewRefundOrderStateEnum.FAILURE.getStateCode());
                            response.getRefundTicketPassenger().setChargebackText(NewRefundOrderStateEnum.FAILURE.getStateDesc());
                            list.clear();
                            applyStatusResp.setNewStatus(null);
                            applyStatusResp.setPassStatus("Y");
                            applyStatusResp1.setNewStatus(null);
                            applyStatusResp2.setNewStatus("Y");
                            applyStatusResp2.setShowOfficial("退票失败");
                            applyStatusResp2.setNowStatusDate(disposeDate(response.getRefundTicketPassenger().getSecondCheckDatetime()));
                            applyStatusResp2.setPassStatus("Y");
                            list.add(applyStatusResp);
                            list.add(applyStatusResp1);
                            list.add(applyStatusResp2);
                            response.setApplyStatusRespList(list);
                            response.setResultCode(WSEnum.SUCCESS.getResultCode());
                            response.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
                        }
                    } else {
                        response.setResultCode(WSEnum.ERROR.getResultCode());
                        response.setErrorInfo("查询普通退单详情时出错！");
                        return response;
                    }
                } catch (Exception e) {
                    log.error("普通退单参数转换时时出错，IP地址:{}，错误信息描述：{}，堆栈信息：", ip, e.getMessage(), e);
                    response.setResultCode(WSEnum.ERROR.getResultCode());
                    response.setErrorInfo("普通退单参数转换时出错！");
                    return response;
                }
            } else {
                response.setResultCode(WSEnum.ERROR.getResultCode());
                response.setErrorInfo("查询退单详情网络出错");
                return response;
            }
        } else {
            //2. 查询快速退单的详情
            TicketSpeedRefundDetailRequest ticketSpeedRefundDetailRequest = new TicketSpeedRefundDetailRequest(HandlerConstants.VERSION, req.getChannelCode(), userNo);
            ticketSpeedRefundDetailRequest.setTicketPayoutNO(req.getRefundNo());
            String serviceResult = this.invokePost(ticketSpeedRefundDetailRequest, HandlerConstants.URL_FARE_API + HandlerConstants.SPEEDTICKET_REFUND_DETAIL);
            try {
                if (StringUtils.isNotBlank(serviceResult)) {
                    QueryPassengerInfoByTicketResp responseNew = new QueryPassengerInfoByTicketResp();

                    QueryRefundDetailResponse queryRefundDetailResponse = (QueryRefundDetailResponse) JsonUtil.jsonToBean(serviceResult, QueryRefundDetailResponse.class);
                    if ("1001".equals(queryRefundDetailResponse.getResultCode())) {
                        TicketPayoutDetailInfo ticketPayoutDetailInfo = queryRefundDetailResponse.getTicketPayoutDetailInfo();
                        List<PtSpeedSegmentInfo> ticketPayoutDetailList = ticketPayoutDetailInfo.getTicketPayoutDetailList();
                        if (null != ticketPayoutDetailList.get(0)) {
                            PtSpeedSegmentInfo ptSpeedSegmentInfo = ticketPayoutDetailList.get(0);
                            if (!StringUtils.isEmpty(ptSpeedSegmentInfo.getTicketNo())) {
                                QueryPassengerByTicketNoReq requ = new QueryPassengerByTicketNoReq();
                                requ.setChannelCode(req.getChannelCode());
                                requ.setUserNo(userNo);
                                requ.setTicketNo(ptSpeedSegmentInfo.getTicketNo());
                                requ.setVersion(HandlerConstants.VERSION);
                                requ.setCustomerNo(req.getCustomerNo());
                                String url = HandlerConstants.URL_FARE_API + HandlerConstants.QUERY_PASSENGER_BT_TICKET_NO;
                                Map<String, String> headMap = new HashMap<>();
                                HttpResult result = HttpUtil.doPostClient(requ, url, headMap);
                                if (!result.isResult() || org.apache.commons.lang.StringUtils.isBlank(result.getResponse())) {
                                    throw new NetworkException("调用同一订单系统出现异常");
                                }
                                responseNew = JsonUtil.fromJson(result.getResponse(), QueryPassengerInfoByTicketResp.class);
                            }
                        }

                        Map<String, AirPortInfoDto> airportMap = basicService.queryAllAirportMap(req.getChannelCode(), ip);
                        response = SpeedTicketConvert.formChargebackDetail(queryRefundDetailResponse, airportMap, req.getRefundNo(), responseNew);
                        response.setResultCode(WSEnum.SUCCESS.getResultCode());
                        response.setErrorInfo(WSEnum.SUCCESS.getResultCode());
                    } else {
                        response.setResultCode(WSEnum.ERROR.getResultCode());
                        response.setErrorInfo("查询快速退单详情时出错！");
                        return response;
                    }
                }
            } catch (Exception e) {
                log.error("快速退单参数转换时时出错，IP地址:{}，错误信息描述：{}，堆栈信息：", ip, e.getMessage(), e);
                response.setResultCode(WSEnum.ERROR.getResultCode());
                response.setErrorInfo("快速退单参数转换时时出错！");
                return response;
            }
        }
        return response;
    }

    public String disposeDate(String str) {
        try {
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
            Date parse = format.parse(str);
            String format1 = format.format(parse);
            return format1;
        } catch (Exception e) {
            log.error("时间转换错误{}", e.getMessage());
        }
        return null;
    }

    //临时处理保险名称
    private void changeInsureName(RefundTotalBriefInfo refundTotalBriefInfo) {
        String insureName = "";
        String insureStr = "";
        if (refundTotalBriefInfo.getInsuranceInfo() != null) {
            if (!StringUtil.isNullOrEmpty(refundTotalBriefInfo.getInsuranceInfo().getInsuranceCode())) {
                insureStr = HandlerConstants.INSURE_INFO_D + "," + HandlerConstants.INSURE_INFO_I_OW + "," + HandlerConstants.INSURE_INFO_I_RT;//所有在用的保险集合
                if (!StringUtil.isNullOrEmpty(insureStr)) {
                    String[] insureArray = insureStr.split(",");
                    for (String str : insureArray) {
                        String[] curInsureStr = str.split("&");
                        if (curInsureStr[0].equals(refundTotalBriefInfo.getInsuranceInfo().getInsuranceCode())) {
                            insureName = curInsureStr[1];
                            refundTotalBriefInfo.getInsuranceInfo().setInsuranceName(insureName);
                            break;
                        }
                    }
                }
            }
        }
    }

    /**
     * 构建订单列表请求参数
     *
     * @param ptBrief
     * @param briefReq
     * @return
     */
    private PtOrderTotalBriefReq createReq(PtOrderTotalBriefReq ptBrief, OrderBriefReq briefReq) {
        ptBrief.setCreateDateBegin(briefReq.getDateBegin());
        ptBrief.setCreateDateEnd(briefReq.getDateEnd());
        if (StringUtils.isBlank(ptBrief.getCreateDateBegin())) {
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.MONTH, -6);
            ptBrief.setCreateDateBegin(DateUtils.dateToString(calendar.getTime(), DateUtils.YYYY_MM_DD_PATTERN));
        }
        if (StringUtils.isBlank(ptBrief.getCreateDateEnd())) {
            ptBrief.setCreateDateEnd(DateUtils.dateToString(new Date(), DateUtils.YYYY_MM_DD_PATTERN));
        }
        ptBrief.setPageSize(briefReq.getPageSize());
        ptBrief.setPageNo(briefReq.getPageNo());
        ptBrief.setCustomerNo(briefReq.getCustomerNo());
        return ptBrief;
    }

    private OrderTotalBriefResp getOrderTotalBriefResp(OrderTotalBriefResp resp, String reqId, String ip) {
        Date curDate = new Date();
        if ("1001".equals(resp.getResultCode())) {
            AtomicBoolean refundFlag = new AtomicBoolean(false);
            AtomicBoolean exchangeFlag = new AtomicBoolean(false);
            Map<Integer, com.juneyaoair.baseclass.response.order.query.SegmentInfo> formerSegmentList = resp.getFormerSegmentList();
            //将订单按照时间排序
            sortByCreateTime(resp.getOrderBriefInfoList());
            Map<String, AirPortInfoDto> airportMap = basicService.queryAllAirportMap(ChannelCodeEnum.MOBILE.getChannelCode(), ip);
            if (!StringUtil.isNullOrEmpty(resp.getOrderBriefInfoList())) {
                //key为升舱，改期订单的原航段ChannelOrderNo,value为OrderSort,用于判断原订单是否包含改期，升舱
                for (OrderTotalBriefInfo orderTotalBriefInfo : resp.getOrderBriefInfoList()) {
                    //如果是B2C渠道的upgrade，一律认为是改期
                    if (orderTotalBriefInfo.getChannelCode().equals("B2C") && orderTotalBriefInfo.getOrderSort().equals("Upgrade")) {
                        orderTotalBriefInfo.setOrderSort(OrderSortEnum.Change.getOrderSort());
                    }
                    // 结合去程和返程的航段
                    //orderTotalBriefInfo.setSegmentInfoList(combineSegmentInfos(orderTotalBriefInfo.getSegmentInfoList()));
                    if (!StringUtil.isNullOrEmpty(orderTotalBriefInfo.getSegmentInfoList())) {
                        for (com.juneyaoair.baseclass.response.order.query.SegmentInfo segmentInfo : orderTotalBriefInfo.getSegmentInfoList()) {
                            AirPortInfoDto depAirPort = airportMap.get(segmentInfo.getDepAirport());
                            AirPortInfoDto arrAirPort = airportMap.get(segmentInfo.getArrAirport());
                            segmentInfo.setDepCityName(depAirPort == null ? segmentInfo.getDepAirport() : depAirPort.getCityName());
                            segmentInfo.setArrCityName(arrAirPort == null ? segmentInfo.getArrAirport() : arrAirPort.getCityName());
                            segmentInfo.setDepAirportName(depAirPort == null ? segmentInfo.getDepAirport() : depAirPort.getAirPortName());
                            segmentInfo.setArrAirportName(arrAirPort == null ? segmentInfo.getArrAirport() : arrAirPort.getAirPortName());
                            //标记军残警残的特殊标记
                            segmentInfo.setJcspecialLabel(V2OrderObjectConvert.judgeJCSpecialLabel(segmentInfo,
                                    orderTotalBriefInfo.getOrderSort(), orderTotalBriefInfo.getPassengerInfoList()));
                        }
                    }
                    OrderPayStateEnum orderPayState = OrderPayStateConvert.convertOrderPayState(orderTotalBriefInfo.getOrderState(),
                            orderTotalBriefInfo.getPayState(), orderTotalBriefInfo.getOrderSort(), orderTotalBriefInfo.getPassengerSegmentList());
                    orderTotalBriefInfo.setOrderPayState(orderPayState.getStateCode());
                    orderTotalBriefInfo.setOrderPayStateName(orderPayState.getStateDesc());
                    // 已取消订单和
                    //orderTotalBriefInfo.setShowDeleteButton(OrderPayStateEnum.Cancel.equals(orderPayState) || OrderPayStateEnum.TicketOut.equals(orderPayState));
                    //标记评价标记
                    /*orderTotalBriefInfo.setEvaluateBtn(V2OrderObjectConvert.judgeAbleEvaluate(orderTotalBriefInfo.getOrderSort(),
                            orderTotalBriefInfo.getOrderState(), orderTotalBriefInfo.getPayState(), orderTotalBriefInfo.getSegmentInfoList(),
                            curDate, orderTotalBriefInfo.getPassengerSegmentList()));*/
                    //addon，spa不支持改期
                    /*if (FareTypeEnum.ADDON.getFare().equals(orderTotalBriefInfo.getFareType())
                            || FareTypeEnum.SPA.getFare().equals(orderTotalBriefInfo.getFareType())
                            || FareTypeEnum.ONEWAY.getFare().equals(orderTotalBriefInfo.getFareType())
                            // 单独购保不显示改期按钮
                            || OrderSortEnum.Insurance.getOrderSort().equals(orderTotalBriefInfo.getOrderSort())) {
                        orderTotalBriefInfo.setChangeFlag(false);
                    }*/
                    //国际机票订单关闭改期按钮
                    /*if (HandlerConstants.TRIP_TYPE_I.equalsIgnoreCase(orderTotalBriefInfo.getFlightType())) {
                        orderTotalBriefInfo.setChangeFlag(false);
                    }*/
                    // 使用无限飞卡的关闭改期按钮
                    /*if (orderTotalBriefInfo.getPassengerInfoList().stream().anyMatch(ptPassengerInfo ->
                            StringUtils.isNotBlank(ptPassengerInfo.getUnlimitedFlyCardNo()))) {
                        orderTotalBriefInfo.setChangeFlag(false);
                    }*/
                    //奖励飞判断，如果包含I或者N，就设置为不可改期
                   /* String[] split = handConfig.getAwardFlyFreeTicketCabin().split(",");
                    if (orderTotalBriefInfo.getSegmentInfoList() != null){
                        for (com.juneyaoair.baseclass.response.order.query.SegmentInfo segmentInfo : orderTotalBriefInfo.getSegmentInfoList()) {
                            for (String s : split) {
                                if (segmentInfo.getCabin()!=null && segmentInfo.getCabin().equals(s)){
                                    orderTotalBriefInfo.setChangeFlag(false);
                                }
                            }
                        }
                    }*/

                    //标记该订单是否包含已退票或者改期升舱的航段
                    if (!StringUtil.isNullOrEmpty(orderTotalBriefInfo.getPassengerSegmentList())) {
                        AtomicInteger index = new AtomicInteger();
                        Set<OrderBriefLabelEnum> orderBriefLabelEnums = new HashSet<>();
                        for (PassengerSegment passengerSegment : orderTotalBriefInfo.getPassengerSegmentList()) {
                            OrderBriefLabelEnum orderBriefLabelEnum = OrderBriefLabelEnum.getByCode(passengerSegment.getLaterTicketOrderSort());
                            if (orderBriefLabelEnum != null) {
                                orderBriefLabelEnums.add(orderBriefLabelEnum);
                            }
                            if (!StringUtil.isNullOrEmpty(passengerSegment.getTicketState())) {
                                if (passengerSegment.getTicketState().equals(TicketStateEnum.REFUNDED)
                                        || passengerSegment.getTicketState().equals(TicketStateEnum.REFUND_APPLICATION)) {
                                    index.getAndIncrement();
                                    refundFlag.set(true);
                                    orderTotalBriefInfo.setRefundFlag(refundFlag.get());
                                    orderTotalBriefInfo.setEvaluateBtn(false);
                                    orderTotalBriefInfo.setModifyRefundTime(passengerSegment.getModifyDatetime());
                                    orderBriefLabelEnums.add(OrderBriefLabelEnum.Refund);
                                } else if (passengerSegment.getTicketState().equals("EXCHANGED")) {
                                    exchangeFlag.set(true);
                                    orderTotalBriefInfo.setExchangeFlag(exchangeFlag.get());
                                    orderTotalBriefInfo.setEvaluateBtn(false);
                                    orderTotalBriefInfo.setModifyExchangeTime(passengerSegment.getModifyDatetime());
                                }
                            }
                        }
                        // 标签展示优先级： 已退款 > 已改期 > 已升舱
                        if (orderBriefLabelEnums.contains(OrderBriefLabelEnum.Refund)) {
                            orderTotalBriefInfo.setOrderLabel(OrderBriefLabelEnum.Refund.getCode());
                        } else if (orderBriefLabelEnums.contains(OrderBriefLabelEnum.Change)) {
                            orderTotalBriefInfo.setOrderLabel(OrderBriefLabelEnum.Change.getCode());
                        } else if (orderBriefLabelEnums.contains(OrderBriefLabelEnum.Upgrade)) {
                            orderTotalBriefInfo.setOrderLabel(OrderBriefLabelEnum.Upgrade.getCode());
                        }
                        if (index.get() == orderTotalBriefInfo.getPassengerSegmentList().size()) {
                            refundFlag.set(false);
                            orderTotalBriefInfo.setRefundFlag(refundFlag.get());
                        }
                    }
                    //标记该订单中哪些航段做了升舱改期
                    if (orderTotalBriefInfo.getOrderSort().equals("Upgrade") || orderTotalBriefInfo.getOrderSort().equals(OrderSortEnum.Change.getOrderSort())) {
                        AtomicInteger index = new AtomicInteger();
                        orderTotalBriefInfo.getPassengerSegmentList().forEach(passengerSegment -> {
                            if (passengerSegment.getFormerSegmentId() != 0) {
                                if (orderTotalBriefInfo.getOrderSort().equals(OrderSortEnum.Change.getOrderSort())) {
                                    orderTotalBriefInfo.getSegmentInfoList().forEach(segmentInfo -> {
                                        com.juneyaoair.baseclass.response.order.query.SegmentInfo formerSegmentInfo = formerSegmentList.get(passengerSegment.getFormerSegmentId());
                                        if (segmentInfo.getSegmentID() == passengerSegment.getSegmentId()) {
                                            if (segmentInfo.getFlightNo().equals(formerSegmentInfo.getFlightNo()) &&
                                                    segmentInfo.getArrAirport().equals(formerSegmentInfo.getArrAirport()) &&
                                                    segmentInfo.getDepAirport().equals(formerSegmentInfo.getDepAirport()) &&
                                                    segmentInfo.getDepDateTime().equals(formerSegmentInfo.getDepDateTime()) &&
                                                    segmentInfo.getCabin().equals(formerSegmentInfo.getCabin()) &&
                                                    segmentInfo.getCabinClass().equals(formerSegmentInfo.getCabinClass())) {
                                                segmentInfo.setChangeSegment(false);
                                            } else {
                                                segmentInfo.setChangeSegment(true);
                                                index.getAndIncrement();
                                            }
                                        }
                                    });
                                } else {
                                    String cabin = formerSegmentList.get(passengerSegment.getFormerSegmentId()).getCabin();
                                    String cabinClass = formerSegmentList.get(passengerSegment.getFormerSegmentId()).getCabinClass();
                                    orderTotalBriefInfo.getSegmentInfoList().forEach(segmentInfo -> {
                                        if (segmentInfo.getSegmentID() == passengerSegment.getSegmentId()) {
                                            if (!cabinClass.equals(segmentInfo.getCabinClass())) {
                                                segmentInfo.setChangeSegment(true);
                                                segmentInfo.setFormatCabin(cabin);
                                                segmentInfo.setFormatCabinClass(cabinClass);
                                                segmentInfo.setFormatCabinClassName(transformCabinClassName(cabinClass));
                                                segmentInfo.setCabinClassName(transformCabinClassName(segmentInfo.getCabinClass()));
                                            } else {
                                                segmentInfo.setChangeSegment(false);
                                            }
                                        }
                                    });
                                }
                            }
                        });
                        if (orderTotalBriefInfo.getOrderSort().equals(OrderSortEnum.Change.getOrderSort()) && index.get() == 0) {
                            orderTotalBriefInfo.getSegmentInfoList().forEach(segmentInfo ->
                                    segmentInfo.setChangeSegment(true)
                            );
                        }
                    }
                }
            }
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            resp.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo("查询结果:" + resp.getErrorInfo());
        }
        String respJson = JsonUtil.objectToJson(resp);
        log.info("请求号:{}，IP地址:{}，服务器响应结果：{}", reqId, ip, respJson);
        return resp;
    }

    private void sortByCreateTime(List<OrderTotalBriefInfo> orderBriefInfoList) {
        Collections.sort(orderBriefInfoList, (a, b) -> DateUtils.toDate(b.getCreateDatetime(), "yyyy-MM-dd HH:mm:ss").compareTo(DateUtils.toDate(a.getCreateDatetime(), "yyyy-MM-dd HH:mm:ss")));
    }

    /**
     * 舱位等级转换名字
     */
    public String transformCabinClassName(String cabinClass) {
        if ("C".equals(cabinClass)) {
            return "公务舱";
        } else if ("F".equals(cabinClass)) {
            return "超值头等舱";
        } else if ("J".equals(cabinClass)) {
            return "公务舱";
        } else {
            return "经济舱";
        }
    }

    /**
     * 处理订单列表中保险名称
     */
    private void setInsuranceName(OrderTotalBriefResp orderTotalBriefResp) {
        if (CollectionUtils.isEmpty(orderTotalBriefResp.getOrderBriefInfoList())) {
            return;
        }
        orderTotalBriefResp.getOrderBriefInfoList().stream()
                .filter(orderTotalBriefInfo -> CollectionUtils.isNotEmpty(orderTotalBriefInfo.getInsuranceInfoList()))
                .forEach(orderTotalBriefInfo -> {
                    orderTotalBriefInfo.getInsuranceInfoList().stream()
                            .forEach(insuranceInfo -> {
                                Set<InsureInfo> insuranceList = getInsuranceList();
                                if (CollectionUtils.isEmpty(insuranceList)) {
                                    return;
                                }
                                insuranceList.stream()
                                        .filter(insureInfo -> insureInfo.getInsId().equalsIgnoreCase(insuranceInfo.getInsuranceCode()))
                                        .forEach(insureInfo -> insuranceInfo.setInsuranceName(insureInfo.getInsNm()));
                            });
                });
    }

    /**
     * 得到保险列表
     */
    private Set<InsureInfo> getInsuranceList() {
        List<InsureQueryRule> insureQueryRules = insuranceService.getInsuranceList();
        Set<InsureInfo> insureInfoList = new HashSet<>();
        insureQueryRules.forEach(insureQueryRule ->
                insureQueryRule.getInsureList().stream()
                        .filter(insureInfo -> org.apache.commons.lang3.StringUtils.isNotBlank(insureInfo.getInsId()))
                        .forEach(insureInfoList::add));
        return insureInfoList;
    }

    /**
     * 处理订单列表票号和旅客姓名
     *
     * @param orderTotalBriefResp
     */
    private void SetPassNameAndTicketNo(OrderTotalBriefResp orderTotalBriefResp) {
        if (CollectionUtils.isEmpty(orderTotalBriefResp.getOrderBriefInfoList())) {
            return;
        }
        orderTotalBriefResp.getOrderBriefInfoList().stream().
                filter(orderTotalBriefInfo -> CollectionUtils.isNotEmpty(orderTotalBriefInfo.getPassengerSegmentList()) && CollectionUtils.isNotEmpty(orderTotalBriefInfo.getPassengerInfoList()))
                .forEach(orderTotalBriefInfo -> {
                    orderTotalBriefInfo.setPassName(orderTotalBriefInfo.getPassengerInfoList().get(0).getPassengerName());
                    orderTotalBriefInfo.setTicketNo(getReplace(orderTotalBriefInfo.getPassengerSegmentList().get(0).getTicketNo()));
                });
    }

    /**
     * 去掉字符串的第一个"-"
     */
    private String getReplace(String s) {
        if (StringUtils.isBlank(s)) {
            return null;
        }
        return s.replace("-", "");
    }


    private PtTicketPayoutRetryRequest createPtTicketPayoutRetryRequest(BaseReq<TicketRefundRetry> req) {
        TicketRefundRetry ticketRefundRetry = req.getRequest();
        PtTicketPayoutRetryRequest ptTicketPayoutRetryRequest = new PtTicketPayoutRetryRequest(HandlerConstants.VERSION, req.getChannelCode(), getChannelInfo(req.getChannelCode(), "10"));
        BeanUtils.copyProperties(ticketRefundRetry, ptTicketPayoutRetryRequest);
        ptTicketPayoutRetryRequest.setChannelCustomerNo(ticketRefundRetry.getFfpId());
        return ptTicketPayoutRetryRequest;
    }
}

package com.juneyaoair.mobile.handler.controller.util;

import com.juneyaoair.baseclass.response.order.query.OrderBase;

import java.util.*;

/**
 * <AUTHOR> by jiangmingming
 * @date 2019/6/21 15:14
 */
public class TicketOrderConvert {
    /**
     * 取不同类型订单
     *
     * @param oldSubOrderList
     * @param type
     * @return
     */
    public static List<OrderBase> getSubOrderBaseInfoList(List<OrderBase> oldSubOrderList, String type) {
        List<OrderBase> subOrderList = new ArrayList<>();
        for (OrderBase orderBase : oldSubOrderList) {
            if (type.equals(orderBase.getItem1())) {
                subOrderList.add(orderBase);
            }
        }
        return subOrderList;
    }


}

package com.juneyaoair.mobile.handler.service;

import com.juneyaoair.appenum.OperationProcessEnum;
import com.juneyaoair.appenum.WSEnum;
import com.juneyaoair.appenum.member.MemberDetailRequestItemsEnum;
import com.juneyaoair.client.ClientInfo;
import com.juneyaoair.crm.IMemberService;
import com.juneyaoair.exception.CommonException;
import com.juneyaoair.exception.ServiceException;
import com.juneyaoair.mobile.comm.service.IRedisService;
import com.juneyaoair.mobile.constants.MobileCoreBeanNames;
import com.juneyaoair.mobile.handler.comm.RedisKeyConfig;
import com.juneyaoair.mobile.handler.config.bean.HandConfig;
import com.juneyaoair.mobile.handler.controller.crm.login.util.CrmUtil;
import com.juneyaoair.mobile.handler.controller.util.CRMReqUtil;
import com.juneyaoair.thirdentity.comm.request.PtApiCRMRequest;
import com.juneyaoair.thirdentity.comm.response.PtCRMResponse;
import com.juneyaoair.thirdentity.member.request.PtMemberDetailRequest;
import com.juneyaoair.thirdentity.member.response.MemberRealNameSummarySoaModel;
import com.juneyaoair.thirdentity.member.response.PtMemberDetail;
import com.juneyaoair.utils.util.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description 会员聚合服务
 * @date 2023/11/13 17:32
 */
@Service
public class MemberAggrService {
    @Autowired
    private IMemberService memberService;
    @Resource(name = MobileCoreBeanNames.API_REDIS_SERVICE)
    private IRedisService apiRedisService;
    @Autowired
    private HandConfig handConfig;

    /**
     * 检验是否最近可信渠道
     *
     * @param clientInfo
     * @param operation
     * @return true-代表可信渠道 false-代表不可信
     */
    public boolean checkRealNameTime(ClientInfo clientInfo, String operation) {
        //判断最近一次实名是否来自可信渠道
        PtApiCRMRequest ptApiCRMRequest = CRMReqUtil.buildCommReq(clientInfo.getChannelCode(), clientInfo.getFfpId(), clientInfo.getClientIp());
        PtMemberDetailRequest ptMemberDetailRequest = new PtMemberDetailRequest();
        String[] items = {MemberDetailRequestItemsEnum.REALVERIFYINFOS.eName};
        ptMemberDetailRequest.setRequestItems(items);
        ptMemberDetailRequest.setCardNO(clientInfo.getFfpCardNo());
        ptApiCRMRequest.setData(ptMemberDetailRequest);
        PtCRMResponse<PtMemberDetail> ptCRMDetailResponse = memberService.memberDetail(ptApiCRMRequest);
        if (!ptCRMDetailResponse.isIsSuccess() || ptCRMDetailResponse.getCode() != 0) {
            throw new ServiceException(ptCRMDetailResponse.getMsg());
        }
        return checkRealNameTime(clientInfo,operation,ptCRMDetailResponse.getData().getRealVerifyInfos());
    }

    /**
     * 自有渠道认证时限检验
     * @param clientInfo
     * @param operation
     * @param realVerifyInfoList
     * @return true-代表可信渠道 false-代表不可信
     */
    public boolean checkRealNameTime(ClientInfo clientInfo, String operation, List<MemberRealNameSummarySoaModel> realVerifyInfoList) {
        MemberRealNameSummarySoaModel memberRealNameSummarySoaModel = CrmUtil.getNewRealNamePassSummary(realVerifyInfoList);
        boolean checkFlag = checkOwnRealChannel(memberRealNameSummarySoaModel);
        String redisKey = RedisKeyConfig.ACCOUNT_REAL_STATUS + clientInfo.getFfpCardNo() + ":" + operation;
        apiRedisService.putData(redisKey, String.valueOf(checkFlag), 600L);
        return checkFlag;
    }

    /**
     * 检验渠道是否可信
     * @param memberRealNameSummarySoaModel
     * @return
     */
    public boolean checkOwnRealChannel(MemberRealNameSummarySoaModel memberRealNameSummarySoaModel){
        if (Objects.isNull(memberRealNameSummarySoaModel)) {
            throw new CommonException(WSEnum.NO_REAL_NAME.getResultCode(), WSEnum.NO_REAL_NAME.getResultInfo());
        }
        boolean checkFlag;
        LocalDateTime verifyDate = DateUtils.toLocalDateTime(memberRealNameSummarySoaModel.getVerifyDate());
        long useTime = DateUtils.differOfYear(verifyDate, LocalDateTime.now());
        String ownRealChannel = handConfig.getOwnRealChannel();
        //为空的情况下不进行自有渠道检验
        if (StringUtils.isNotBlank(ownRealChannel)) {
            if (ownRealChannel.contains(memberRealNameSummarySoaModel.getVerifyChannel()) && useTime <= 2) {
                checkFlag = true;
            } else {
                checkFlag = false;
            }
        } else {
            checkFlag = true;
        }
        return checkFlag;
    }

    /**
     * 验证上一步短信验证码是为 SMSCHECK 状态
     * SMSCHECK  状态来源于 checkSmsCommonCodeByFfp 方法
     *
     * @param clientInfo
     * @param operation
     */
    public void checkSmsCode(ClientInfo clientInfo, String operation) {
        String redisKey = RedisKeyConfig.FFP_OPERATION_STATUS + clientInfo.getFfpCardNo() + ":" + operation;
        String result = apiRedisService.getData(redisKey);
        if (StringUtils.isBlank(result)) {
            throw new CommonException(WSEnum.OPERATION_TIMEOUT.getResultCode(), WSEnum.OPERATION_TIMEOUT.getResultInfo());
        }
        if (!OperationProcessEnum.SMS_CHECK.getCode().equals(result)) {
            throw new CommonException(WSEnum.DO_SMS_CHECK.getResultCode(), WSEnum.DO_SMS_CHECK.getResultInfo());
        }
    }
}

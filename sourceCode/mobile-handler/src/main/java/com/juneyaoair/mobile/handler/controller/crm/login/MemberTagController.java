package com.juneyaoair.mobile.handler.controller.crm.login;


import com.juneyaoair.appenum.WSEnum;
import com.juneyaoair.appenum.member.CertificateTypeEnum;
import com.juneyaoair.appenum.member.MemberDetailRequestItemsEnum;
import com.juneyaoair.baseclass.common.request.BaseReq;
import com.juneyaoair.baseclass.common.response.BaseResp;
import com.juneyaoair.baseclass.member.request.MemberTagCertificationRequest;
import com.juneyaoair.baseclass.member.response.MemberTagResponse;
import com.juneyaoair.crm.IMemberCrmMemberService;
import com.juneyaoair.crm.IMemberService;
import com.juneyaoair.mobile.aop.annotation.InterfaceLog;
import com.juneyaoair.mobile.handler.controller.BassController;
import com.juneyaoair.mobile.handler.controller.util.CRMReqUtil;
import com.juneyaoair.mobile.handler.service.IMemberTagService;
import com.juneyaoair.thirdentity.comm.request.PtApiCRMRequest;
import com.juneyaoair.thirdentity.comm.response.PtCRMResponse;
import com.juneyaoair.thirdentity.member.comm.CrmMemberBaseApiRequest;
import com.juneyaoair.thirdentity.member.comm.CrmMemberBaseApiResponse;
import com.juneyaoair.thirdentity.member.request.PtMemberDetailRequest;
import com.juneyaoair.thirdentity.member.request.SoldierAuthenticationReqDto;
import com.juneyaoair.thirdentity.member.response.MemberCertificateResDto;
import com.juneyaoair.thirdentity.member.response.MemberCertificateSoaModelV2;
import com.juneyaoair.thirdentity.member.response.PtMemberDetail;
import com.juneyaoair.utils.StringUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import javax.servlet.http.HttpServletRequest;



@RequestMapping("/memberTag")
@RestController
@Api(value = "MemberTagController", description = "拥军优待")
@Slf4j
public class MemberTagController extends BassController {

    @Autowired
    private IMemberCrmMemberService iMemberCrmMemberService;

    @Autowired
    private  IMemberTagService   iMemberTagService;
    @Autowired
    private IMemberService memberService;

    @ApiOperation(value = "优待证认证资格查询", notes = "认证资格")
    @RequestMapping(value = "/certification", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @InterfaceLog
    public BaseResp certification(@RequestBody @Validated BaseReq<MemberTagCertificationRequest> req, BindingResult bindingResult, HttpServletRequest request) {
        BaseResp baseResp = new BaseResp();

        String channelCode = req.getChannelCode();
        if (bindingResult.hasErrors()) {
            baseResp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            baseResp.setResultInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
            return baseResp;
        }
        try {
            MemberTagResponse memberTagResponse = iMemberTagService.queryMemberTag(request, channelCode, req.getRequest().getFfpId(), "YDZ", false);
            if (memberTagResponse.getIsEffective()) {
                baseResp.setResultCode(WSEnum.ERROR.getResultCode());
                baseResp.setResultInfo("你已认证成功，请勿重复认证");
                return baseResp;
            }
            CrmMemberBaseApiRequest<SoldierAuthenticationReqDto> soldierAuthenticationRequest = buildCommCrmMemberReq(request, channelCode);
            SoldierAuthenticationReqDto soldierAuthenticationReqDto = new SoldierAuthenticationReqDto();
            soldierAuthenticationReqDto.setID(Integer.valueOf(req.getRequest().getFfpId()));

            String[] items = {MemberDetailRequestItemsEnum.CERTIFICATEINFO.eName};
            PtApiCRMRequest<PtMemberDetailRequest> ptMemberDetailRequestPtApiCRMRequest = CRMReqUtil.buildMemberDetailReq(req.getRequest().getFfpCardNo(), req.getRequest().getFfpId(), request, channelCode, items);
            PtCRMResponse<PtMemberDetail> ptMemberDetailPtCRMResponse = memberService.memberDetail(ptMemberDetailRequestPtApiCRMRequest);
            if (ptMemberDetailPtCRMResponse.getCode() == 0) {
                PtMemberDetail ptMemberDetail = ptMemberDetailPtCRMResponse.getData();
                MemberCertificateSoaModelV2 memberCertificateSoaModelV2 = CRMReqUtil.getCertificateInfo(ptMemberDetail.getCertificateInfo(), CertificateTypeEnum.ID_CARD.getCode());
                if (memberCertificateSoaModelV2 == null || StringUtil.isNullOrEmpty(memberCertificateSoaModelV2.getCertificateNumber())) {
                    soldierAuthenticationReqDto.setIdentityCard(req.getRequest().getIdentityCard());
                }
            }else {
                soldierAuthenticationReqDto.setIdentityCard(req.getRequest().getIdentityCard());
            }
            soldierAuthenticationRequest.setData(soldierAuthenticationReqDto);
            CrmMemberBaseApiResponse<MemberCertificateResDto> memberCertificateResResponse = iMemberCrmMemberService.soldierAuthentication(soldierAuthenticationRequest);
            if (memberCertificateResResponse.getCode() == 0) {
                baseResp.setResultCode(WSEnum.SUCCESS.getResultCode());
                baseResp.setResultInfo("优待信息验证成功");
            } else {
                log.info("认证失败 信息 {}", memberCertificateResResponse.getDesc());
                baseResp.setResultCode(WSEnum.ERROR.getResultCode());
                baseResp.setResultInfo(memberCertificateResResponse.getDesc());
            }
        } catch (Exception e) {
            baseResp.setResultCode(WSEnum.ERROR.getResultCode());
            baseResp.setResultInfo("认证失败");
        }
        return baseResp;
    }


}

package com.juneyaoair.mobile.filter;

import com.google.gson.internal.LinkedTreeMap;
import com.juneyaoair.appenum.ChannelCodeEnum;
import com.juneyaoair.appenum.WSEnum;
import com.juneyaoair.baseclass.common.response.BaseResp;
import com.juneyaoair.baseclass.response.crm.MemberLoginInfo;
import com.juneyaoair.mobile.external.request.sso.LogoutParam;
import com.juneyaoair.mobile.external.request.sso.ParseTokenResult;
import com.juneyaoair.mobile.external.service.AuthService;
import com.juneyaoair.mobile.handler.HandlerConstants;
import com.juneyaoair.mobile.interceptor.bean.MobileTokenUserInfo;
import com.juneyaoair.mobile.interceptor.bean.MobileVerifyTokenResp;
import com.juneyaoair.mobile.interceptor.utils.TokenUtils;
import com.juneyaoair.utils.EncoderHandler;
import com.juneyaoair.utils.IPUtil;
import com.juneyaoair.utils.StringUtil;
import com.juneyaoair.utils.http.ReqUtil;
import com.juneyaoair.utils.json.JsonMapper;
import com.juneyaoair.utils.json.JsonUtil;
import com.juneyaoair.utils.util.MdcUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.context.WebApplicationContext;
import org.springframework.web.context.support.WebApplicationContextUtils;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 登录token结果解析，置入公共的请求参数
 */
@Component
public class LoginInfoFilter implements Filter {
    private Logger log = LoggerFactory.getLogger(this.getClass());
    /**
     * 需要排除的页面
     */
    private String excludedPages;
    private String uploaduri;
    private String[] excludedPageArray;
    private String[] uploaduriArray;

    private static final String CHANNEL_CODE = "channelCode";
    private static final String CLIENT_VERSION = "clientVersion";
    private static final String VERSION_CODE = "versionCode";
    private static final String TOKEN = "token";
    private static final String FFP_ID = "ffpId";
    private static final String FFP_CARD_NO = "ffpCardNo";
    private static final String LOGIN_KEY_INFO = "loginKeyInfo";
    private static final String CHANNEL_CUSTOMER_NO = "channelCustomerNo";
    private static final String CUSTOMER_NO = "customerNo";
    private static final String REQUEST = "request";
    private static final String USER_INFO = "userInfo";

    private AuthService authService;

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        log.info("【LoginInfoFilter过滤器】系统自动初始化拦截过滤器..........");
        excludedPages = filterConfig.getInitParameter("excludeuri");
        if (!StringUtil.isNullOrEmpty(excludedPages)) {
            excludedPageArray = excludedPages.split(",");
        }
        uploaduri = filterConfig.getInitParameter("uploaduri");
        if (!StringUtil.isNullOrEmpty(uploaduri)) {
            uploaduriArray = uploaduri.split(",");
        }
        WebApplicationContext context = WebApplicationContextUtils
                .getRequiredWebApplicationContext(filterConfig.getServletContext());
        this.authService = context.getBean(AuthService.class);
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        //移动端的请求参数校验，因为需要判断请求参数里面的用户id和响应参数里的用户id是否相同
        HttpServletRequest mobileHttpServletRequest = (HttpServletRequest) request;
        //如果是移动端请求，并且是json请求,则封装成wapper
        if (ChannelCodeEnum.MOBILE.getChannelCode().equals(mobileHttpServletRequest.getHeader(CHANNEL_CODE))
                && ReqUtil.isJsonReq(mobileHttpServletRequest)) {
            //将request封装成JsonParameterRequestWrapper，以便拦截器使用
            request = new JsonParameterRequestWrapper((HttpServletRequest) request);
        }

        String path = ((HttpServletRequest) request).getServletPath();
        //判断是否在过滤url之外
        boolean isExcludedPage = isSpecialPage(excludedPageArray, path);
        //是否文件上传的url
        boolean isUploadPage = isSpecialPage(uploaduriArray, path);
        if (isExcludedPage) {
            chain.doFilter(request, response);
        } else {
            HttpServletRequest httpServletRequest = (HttpServletRequest) request;
            request.setCharacterEncoding("utf-8");
            String channelCode = httpServletRequest.getHeader(CHANNEL_CODE);
            String clientVersion = httpServletRequest.getHeader(CLIENT_VERSION);
            String versionCode = httpServletRequest.getHeader(VERSION_CODE);
            String uri = httpServletRequest.getRequestURI();
            String contentType = request.getContentType();
            String ip = IPUtil.getIpAddr(httpServletRequest);
            //1.获取token，拼装用户信息
            String token = httpServletRequest.getHeader(TOKEN);
            //文件上传特殊处理追加请求参数 小程序特殊处理 其余渠道保持原有逻辑
            if (isUploadPage) {
                if (ChannelCodeEnum.WXAPP.getChannelCode().equals(channelCode) || ChannelCodeEnum.CHECKIN.getChannelCode().equals(channelCode)) {
                    ParameterRequestWrapper parameterRequestWrapper = addTokenParameter(httpServletRequest, token);
                    chain.doFilter(parameterRequestWrapper, response);
                } else {
                    chain.doFilter(request, response);
                }
            } else {
                //需要走token机制的渠道
                String[] array = {ChannelCodeEnum.MWEB.getChannelCode(), ChannelCodeEnum.WEIXIN.getChannelCode(),
                        ChannelCodeEnum.WXAPP.getChannelCode(), ChannelCodeEnum.CHECKIN.getChannelCode(),
                        ChannelCodeEnum.MP_ALIPAY.getChannelCode(), ChannelCodeEnum.HW5G.getChannelCode(),
                        ChannelCodeEnum.ALIPAY_PLUG.getChannelCode(), ChannelCodeEnum.MOBILE.getChannelCode(),
                        ChannelCodeEnum.ALIPAY_WINDOW.getChannelCode()
                    };
                List<String> channelList = new ArrayList<>(Arrays.asList(array));
                //目前只处理json请求
                if (ReqUtil.isJsonReq(httpServletRequest) && (channelList.contains(channelCode))) {
                    //处理json报文请求
                    JsonParameterRequestWrapper requestWrapper = new JsonParameterRequestWrapper(httpServletRequest);
                    // 读取请求内容
                    String body = requestWrapper.getBodyMessage();
                    Map map = JsonMapper.buildNormalMapper().fromJson(body, Map.class);
                    // 清理入参的会员信息 会员信息改为基于token获取
                    //map.put(FFP_ID, null);
                    //map.put(FFP_CARD_NO, null);
                    //2.MWEB渠道改成MOBILE  后期改走WEIXIN渠道
                    if (ChannelCodeEnum.MWEB.getChannelCode().equals(channelCode)
                            || ChannelCodeEnum.HW5G.getChannelCode().equals(channelCode)
                            || ChannelCodeEnum.MOBILE.getChannelCode().equals(channelCode)) {
                        map.put(CHANNEL_CODE, ChannelCodeEnum.MOBILE.getChannelCode());
                        map.put(CLIENT_VERSION, clientVersion);
                        map.put(VERSION_CODE, versionCode);
                    }
                    //微信小程序、值机小程序渠道的转换为WEIXIN
                    else if (ChannelCodeEnum.WXAPP.getChannelCode().equals(channelCode) ||
                            ChannelCodeEnum.CHECKIN.getChannelCode().equals(channelCode) ||
                            ChannelCodeEnum.MP_ALIPAY.getChannelCode().equals(channelCode) ||
                            ChannelCodeEnum.ALIPAY_PLUG.getChannelCode().equals(channelCode) ||
                            ChannelCodeEnum.ALIPAY_WINDOW.getChannelCode().equals(channelCode)) {
                        map.put(CHANNEL_CODE, ChannelCodeEnum.WEIXIN.getChannelCode());
                        map.put(CLIENT_VERSION, clientVersion);
                        map.put(VERSION_CODE, versionCode);
                    }
                    MemberLoginInfo memberLoginResponse = null;
                    if (StringUtils.isNotBlank(token)) {
                        // 2025-06-30 SSO TOKEN改造 MOBILE渠道且当前版本小于启用版本保持原逻辑
                        if (!TokenUtils.getSsoTokenFlag(channelCode, clientVersion)) {
                            String mobileMemberInfo = TokenUtils.getString(token, HandlerConstants.MOBILE_TOKEN_USER_INFO);
                            if (StringUtils.isNotBlank(mobileMemberInfo)) {
                                MobileTokenUserInfo mobileTokenUserInfo = (MobileTokenUserInfo) JsonUtil.jsonToBean(mobileMemberInfo, MobileTokenUserInfo.class);
                                memberLoginResponse = new MemberLoginInfo();
                                memberLoginResponse.setId(mobileTokenUserInfo.getUserId());
                                memberLoginResponse.setMemberID(mobileTokenUserInfo.getMemberId());
                                // 校验是否封禁
                                MobileVerifyTokenResp mobileVerifyTokenResp =
                                        TokenUtils.verifyIsClosed(String.valueOf(memberLoginResponse.getId()), memberLoginResponse.getMemberID(), (HttpServletRequest) request);
                                if (!mobileVerifyTokenResp.getCode().equals("success")) {
                                    BaseResp baseResp = new BaseResp();
                                    baseResp.setResultCode(WSEnum.INVALID_TOKEN.getResultCode());
                                    baseResp.setResultInfo(mobileVerifyTokenResp.getMessage());
                                    baseResp.setErrorInfo(mobileVerifyTokenResp.getMessage());
                                    response.getOutputStream().write(JsonUtil.objectToJson(baseResp).getBytes(StandardCharsets.UTF_8));
                                    return;
                                }
                            }
                        } else {
                            // 解析token
                            try {
                                ParseTokenResult parseTokenResult = authService.parseToken(channelCode, token);
                                // 校验是否封禁
                                if (!parseTokenResult.isAccountStatus()) {
                                    BaseResp baseResp = new BaseResp();
                                    baseResp.setResultCode(WSEnum.INVALID_TOKEN.getResultCode());
                                    baseResp.setResultInfo("该账号已封禁");
                                    baseResp.setErrorInfo("该账号已封禁");
                                    response.getOutputStream().write(JsonUtil.objectToJson(baseResp).getBytes(StandardCharsets.UTF_8));
                                    return;
                                }
                                memberLoginResponse = new MemberLoginInfo();
                                memberLoginResponse.setId(Long.parseLong(parseTokenResult.getFfpId()));
                                memberLoginResponse.setMemberID(parseTokenResult.getFfpNo());
                                map.put(HandlerConstants.WX_OPENID, parseTokenResult.getOpenId());
                                map.put(HandlerConstants.USER_ID, parseTokenResult.getUserId());
                            } catch (Exception e) {

                            }
                        }
                        if (memberLoginResponse != null) {
                            map.put(FFP_ID, memberLoginResponse.getId());
                            map.put(FFP_CARD_NO, memberLoginResponse.getMemberID());
                            String userkey = HandlerConstants.W_CHANNEL_CODE.equals(map.get(CHANNEL_CODE)) ? HandlerConstants.USER_INFO_KEY : HandlerConstants.USER_INFO_KEY_M;
                            String infoIdKey = EncoderHandler.encode("MD5", memberLoginResponse.getId() + userkey).toUpperCase();
                            memberLoginResponse.setLoginKeyInfo(infoIdKey);
                            map.put(LOGIN_KEY_INFO, memberLoginResponse.getLoginKeyInfo());
                            map.put(CHANNEL_CUSTOMER_NO, memberLoginResponse.getId());
                            //订单详情
                            map.put(CUSTOMER_NO, memberLoginResponse.getId());
                            //为了兼用部分校验接口,采用该id
                            map.put("customNo", memberLoginResponse.getId());
                            map.put("cardNo", memberLoginResponse.getMemberID());
                            map.put("sfCardNo", memberLoginResponse.getMemberID());//电子会员卡
                            //版本号
                            map.put(CLIENT_VERSION, clientVersion);
                            map.put(VERSION_CODE, versionCode);
                            map.put("memberCardNo", memberLoginResponse.getMemberID());
                            log.debug("【LoginInfoFilter过滤器】,请求ID：{} IP地址:{},请求路径{},contentType:{},请求参数:{}", MdcUtils.getRequestId(), ip, uri, contentType, body);
                            if (map.get(REQUEST) != null) {
                                Object obj = map.get(REQUEST);
                                if (obj instanceof Map) {
                                    Map requestMap = (Map) obj;
                                    requestMap.put(FFP_ID, memberLoginResponse.getId());
                                    requestMap.put(FFP_CARD_NO, memberLoginResponse.getMemberID());
                                    requestMap.put(LOGIN_KEY_INFO, memberLoginResponse.getLoginKeyInfo());
                                    requestMap.put(CHANNEL_CUSTOMER_NO, memberLoginResponse.getId());
                                    //订单列表
                                    requestMap.put(CUSTOMER_NO, memberLoginResponse.getId());
                                    if (requestMap.containsKey(USER_INFO)) {
                                        Object objUser = requestMap.get(USER_INFO);
                                        if (objUser instanceof Map) {
                                            Map userMap = (Map) objUser;
                                            userMap.put(FFP_ID, memberLoginResponse.getId());
                                            userMap.put(FFP_CARD_NO, memberLoginResponse.getMemberID());
                                            userMap.put(LOGIN_KEY_INFO, memberLoginResponse.getLoginKeyInfo());
                                        }
                                    }
                                }
                            }
                            //类似添加乘机人 list中有会员id
                            final MemberLoginInfo finalMemberLoginResponse = memberLoginResponse;
                            map.forEach((key, value) -> {
                                if (value != null && value.getClass().getTypeName().indexOf("ArrayList") > -1) {
                                    for (Object obj : (List) value) {
                                        if (obj instanceof LinkedTreeMap) {
                                            LinkedTreeMap objMap = (LinkedTreeMap) obj;
                                            if (null != objMap) {
                                                objMap.put(CHANNEL_CUSTOMER_NO, finalMemberLoginResponse.getId());
                                            }
                                        }
                                    }
                                }
                            });
                            //验证消费密码会员id用字段id表示
                            if (uri.indexOf("/crmService/") > -1) {
                                map.put("id", memberLoginResponse.getId());
                                if (map.get(REQUEST) != null) {
                                    Object obj = map.get(REQUEST);
                                    if (obj instanceof Map) {
                                        Map requestMap = (Map) obj;
                                        requestMap.put("id", memberLoginResponse.getId());
                                    }
                                }
                            }
                        }
                    }
                    requestWrapper.setBody(JsonUtil.objectToJson(map).getBytes(StandardCharsets.UTF_8));
                    String logStr = JsonUtil.objectToJson(map);
                    log.debug("【LoginInfoFilter过滤器】,请求ID：{} IP地址:{},请求路径{},contentType:{},请求参数：{}", MdcUtils.getRequestId(), ip, uri, contentType, logStr);
                    chain.doFilter(requestWrapper, response);
                    //登出操作
                    if (uri.equals("/member/logout")) {
                        HttpServletResponse httpServletResponse = (HttpServletResponse) response;
                        //取返回的json串
                        ResponseWrapper responseWrapper = new ResponseWrapper(httpServletResponse);
                        byte[] bytes = responseWrapper.getBytes();
                        String result = new String(bytes, StandardCharsets.UTF_8);
                        BaseResp loginResponseResp = JsonMapper.buildNonNullMapper().fromJson(result, BaseResp.class);
                        if (WSEnum.SUCCESS.getResultCode().equals(loginResponseResp.getResultCode())) {
                            // 不使用SSO-TOKEN
                            if (!TokenUtils.getSsoTokenFlag(channelCode, clientVersion)) {
                                if (!"ok".equals(TokenUtils.logout(token))) {
                                    BaseResp resp = setResult(WSEnum.INVALID_TOKEN.getResultCode(), "退出登录TOKEN异常！");
                                    response.getOutputStream().write(JsonUtil.objectToJson(resp).getBytes(StandardCharsets.UTF_8));
                                }
                            } else {
                                LogoutParam logoutParam = new LogoutParam();
                                logoutParam.setToken(token);
                                try {
                                    authService.logout(channelCode, logoutParam);
                                } catch (Exception e) {
                                    BaseResp resp = setResult(WSEnum.INVALID_TOKEN.getResultCode(), "退出登录TOKEN异常！");
                                    response.getOutputStream().write(JsonUtil.objectToJson(resp).getBytes(StandardCharsets.UTF_8));
                                }
                            }
                        }
                    }
                } else {
                    chain.doFilter(request, response);
                }
            }
        }
    }

    @Override
    public void destroy() {
        log.info("【LoginInfoFilter过滤器】系统自动销毁拦截过滤器..........");
    }

    //是否特殊配置的路径
    private boolean isSpecialPage(String[] uriArray, String path) {
        for (String page : uriArray) {
            if (path.equals(page.trim())) {
                return true;
            }
        }
        return false;
    }

    //文件上传追加参数
    private ParameterRequestWrapper addTokenParameter(HttpServletRequest httpServletRequest, String token) {
        ParameterRequestWrapper parameterRequestWrapper = new ParameterRequestWrapper(httpServletRequest);
        parameterRequestWrapper.addParameter(CHANNEL_CODE, ChannelCodeEnum.WEIXIN.getChannelCode());
        if (!StringUtil.isNullOrEmpty(token) && !HandlerConstants.DEFAULT_TOKEN.equals(token)) {
            ParseTokenResult parseTokenResult = authService.parseToken(ChannelCodeEnum.WEIXIN.getChannelCode(), token);
            parameterRequestWrapper.addParameter("memberID", parseTokenResult.getFfpNo());
            parameterRequestWrapper.addParameter("id", parseTokenResult.getFfpId());
            String key = HandlerConstants.USER_INFO_KEY;
            String infoIdKey = EncoderHandler.encode("MD5", parseTokenResult.getFfpId() + key).toUpperCase();
            parameterRequestWrapper.addParameter(LOGIN_KEY_INFO, infoIdKey);
        }
        return parameterRequestWrapper;
    }

    /**
     * 设置输出的结果
     *
     * @param msg
     * @return
     */
    private BaseResp setResult(String code, String msg) {
        BaseResp resp = new BaseResp();
        resp.setResultCode(code);
        resp.setResultInfo(msg);
        return resp;
    }

    /**
     * 输出
     *
     * @param response
     * @param obj
     * @throws IOException
     */
    private void sendResp(ServletResponse response, BaseResp obj) throws IOException {
        response.setContentType("application/json; charset=utf-8");
        PrintWriter writer = response.getWriter();
        writer.print(JsonUtil.objectToJson(obj));
        writer.close();
        response.flushBuffer();
    }

}

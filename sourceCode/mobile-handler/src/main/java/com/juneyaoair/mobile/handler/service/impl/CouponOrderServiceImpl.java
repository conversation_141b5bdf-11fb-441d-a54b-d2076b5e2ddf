package com.juneyaoair.mobile.handler.service.impl;

import com.google.common.collect.Lists;
import com.juneyaoair.appenum.order.OrderPayStateEnum;
import com.juneyaoair.appenum.order.OrderStateEnum;
import com.juneyaoair.appenum.order.UnifiedOrderResultEnum;
import com.juneyaoair.baseclass.newcoupon.req.CouponProductOrderGetRequestDto;
import com.juneyaoair.baseclass.response.order.query.SaleCouponGetResponse;
import com.juneyaoair.mobile.handler.HandlerConstants;
import com.juneyaoair.mobile.handler.service.ICouponOrderService;
import com.juneyaoair.mobile.handler.util.ChannelUtils;
import com.juneyaoair.thirdentity.salecoupon.response.PtSaleCouponOrderGetResponse;
import com.juneyaoair.utils.http.HttpResult;
import com.juneyaoair.utils.http.HttpUtil;
import com.juneyaoair.utils.json.JsonUtil;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description
 * @create 2020-11-06 16:58
 */
@Service
public class CouponOrderServiceImpl implements ICouponOrderService {


    @Override
    public List<PtSaleCouponOrderGetResponse.CouponOrder> queryCouponOrderList(String ffpId, String ffpCardNo, String ip, String channelCode, boolean containSelectSeatOrder,
                                                          OrderPayStateEnum payStateEnum,OrderStateEnum orderState, List<String> voucherTypes, int pageSize, int pageNo) {
        CouponProductOrderGetRequestDto requestDto = new CouponProductOrderGetRequestDto();
        requestDto.setUserNo(ChannelUtils.getChannelInfo(channelCode, "10"));
        requestDto.setIsRemoved("0");
        requestDto.setChannelCode(channelCode);
        requestDto.setSearchType(containSelectSeatOrder ? 0 : 1);
        requestDto.setIsRemoved("0");
        requestDto.setFfpId(ffpId);
        requestDto.setFfpCardNo(ffpCardNo);
        requestDto.setPageSize(pageSize);
        requestDto.setPageNo(pageNo);
        if (null != payStateEnum) {
            requestDto.setPayState(payStateEnum.getStateCode());
        }
        if (null != orderState) {
            requestDto.setOrderState(orderState.getStateCode());
        }
        requestDto.setVoucherType(voucherTypes);
        //发起请求
        Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
        HttpResult result = HttpUtil.doPostClient(requestDto, HandlerConstants.URL_FARE_API + HandlerConstants.QUERY_COUPON_ORDER_LIST, headMap);
        if (result.isResult()) {
            SaleCouponGetResponse resp = (SaleCouponGetResponse) JsonUtil.jsonToBean(result.getResponse(), SaleCouponGetResponse.class);
            if (UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(resp.getResultCode())) {
                return resp.getCouponOrderList();
            }
        }
        return Lists.newArrayList();
    }
}

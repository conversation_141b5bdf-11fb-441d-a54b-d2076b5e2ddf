package com.juneyaoair.mobile.handler.controller.util;

import com.juneyaoair.appenum.order.OrderPayEnumType;
import com.juneyaoair.mobile.handler.HandlerConstants;
import com.juneyaoair.thirdentity.request.payment.PtPaymentReq;
import com.juneyaoair.utils.DES3;
import com.juneyaoair.utils.EncoderHandler;
import com.juneyaoair.utils.StringUtil;
import com.juneyaoair.utils.util.DateUtils;

import java.util.Map;

/**
 * Created by yaocf on 2018/1/18.
 * 虚拟支付请求
 */
public class VirtualPaymentConvert {
    //虚拟支付使用，仅限内部测试使用
    public static Map<String,String> virtualPayment(String channelCode,String orderNo,String channelOrderNo,String key,String bodyName,String channelBuyDatetime,String orderType,String amount){
        PtPaymentReq paymentReq = new PtPaymentReq(HandlerConstants.VERSION,HandlerConstants.CURRENCY_CODE, "",
                HandlerConstants.BACK_RETURN_URL_PAY, "","订单", "机票订单", "", "Web",channelCode);
        if(StringUtil.isNullOrEmpty(orderType)){
            orderType="D";
        }else if("O".equals(orderType)){
            paymentReq.setBody("非机票订单");
        } else if (OrderPayEnumType.EasyLiveHotel.getPayType().equals(orderType)) {
            orderType = OrderPayEnumType.EasyLiveHotel.getOrderType();
            paymentReq.setBody("非机票订单");
        }
        switch (bodyName){
            case "UPGRADE_COUPON":
                paymentReq.setBody("优惠券升舱订单");
                orderType="D";
                break;
            case "LoungeType":
                paymentReq.setBody("休息室订单");
                break;
            case "CouponType":
                paymentReq.setBody("权益券订单");
                break;
            case "UPGRADE":
                paymentReq.setBody("升舱订单");
                break;
            case "CHANGE":
                paymentReq.setBody("改期订单");
                break;
        }
        //参数拼接
        paymentReq.setChannelNo(channelCode);
        paymentReq.setGatewayNo(HandlerConstants.VIRTUAL_GATEWAYNO);
        paymentReq.setChannelOrderNo(channelOrderNo);
        paymentReq.setOrderType(orderType);
        paymentReq.setOrderNo(orderNo);
        if(StringUtil.isNullOrEmpty(channelBuyDatetime)){
            paymentReq.setChannelBuyDatetime(DateUtils.getCurrentTimeStr());
        }else{
            paymentReq.setChannelBuyDatetime(channelBuyDatetime);
        }
        paymentReq.setAmount(amount);
        paymentReq.setChannelPriInfo("");
        paymentReq.setGatewayType("4");
        paymentReq.setCardInfo(HandlerConstants.VIRTUAL_CARDINFO);
        paymentReq.setUseScore("0");
        Map<String,String> parametersMap = paymentReq.getPayPara();
        String sha1Content = paymentReq.getPayParaSHA1Str();
        //19支付平台渠道用户号
        String payChannel = channelCode;
        sha1Content = sha1Content+payChannel;
        parametersMap.put("PaymentChannelNo", payChannel);
        //支付卡信息
        //支付卡加密
        String cardInfo = DES3.des3EncodeECB(key.substring(0, key.length()-8),paymentReq.getCardInfo());
        paymentReq.setCardInfo(cardInfo);
        parametersMap.put("CardInfo",paymentReq.getCardInfo());
        //签名
        String chkValue = SHA1Encode(sha1Content+key);
        parametersMap.put("ChkValue",chkValue);
        paymentReq.setChkValue(chkValue);
        return parametersMap;
    }
    //0元付
    public static Map<String,String> payment0(String channelCode,String orderNo,String channelOrderNo,String key,String bodyName,String channelBuyDatetime,String orderType){
        PtPaymentReq paymentReq = new PtPaymentReq(HandlerConstants.VERSION,HandlerConstants.CURRENCY_CODE, "",
                HandlerConstants.BACK_RETURN_URL_PAY, "","订单", "机票订单", "", "Web",channelCode);
        if(StringUtil.isNullOrEmpty(orderType)){
            orderType="D";
        }else if("O".equals(orderType)){
            paymentReq.setBody("非机票订单");
        }
        switch (bodyName){
            case "UPGRADE_COUPON":
                paymentReq.setBody("优惠券升舱订单");
                orderType="D";
                break;
            case "LoungeType":
                paymentReq.setBody("休息室订单");
                break;
            case "CouponType":
                paymentReq.setBody("权益券订单");
                break;
            case "UPGRADE":
                paymentReq.setBody("升舱订单");
                break;
            case "CHANGE":
                paymentReq.setBody("改期订单");
                break;
            case "PAYSEAT":
                paymentReq.setBody("付费选座订单");
                break;
        }
        //参数拼接
        paymentReq.setChannelNo(channelCode);
        paymentReq.setGatewayNo(HandlerConstants.VIRTUAL_GATEWAYNO);
        paymentReq.setChannelOrderNo(channelOrderNo);
        paymentReq.setOrderType(orderType);
        paymentReq.setOrderNo(orderNo);
        if(StringUtil.isNullOrEmpty(channelBuyDatetime)){
            paymentReq.setChannelBuyDatetime(DateUtils.getCurrentTimeStr());
        }else{
            paymentReq.setChannelBuyDatetime(channelBuyDatetime);
        }
        paymentReq.setAmount("0.00");
        paymentReq.setChannelPriInfo("");
        paymentReq.setGatewayType("4");
        paymentReq.setCardInfo(HandlerConstants.VIRTUAL_CARDINFO);
        paymentReq.setUseScore("0");
        Map<String,String> parametersMap = paymentReq.getPayPara();
        String sha1Content = paymentReq.getPayParaSHA1Str();
        //19支付平台渠道用户号
        String payChannel = channelCode;
        sha1Content = sha1Content+payChannel;
        parametersMap.put("PaymentChannelNo", payChannel);
        //支付卡信息
        //支付卡加密
        String cardInfo = DES3.des3EncodeECB(key.substring(0, key.length()-8),paymentReq.getCardInfo());
        paymentReq.setCardInfo(cardInfo);
        parametersMap.put("CardInfo",paymentReq.getCardInfo());
        //签名
        String chkValue = SHA1Encode(sha1Content+key);
        parametersMap.put("ChkValue",chkValue);
        paymentReq.setChkValue(chkValue);
        return parametersMap;
    }
    //SHA1Encode加密
    private static String SHA1Encode(String str){
        String chkValue = null;
        chkValue = EncoderHandler.encodeBySHA1(str);
        return chkValue;
    }
}

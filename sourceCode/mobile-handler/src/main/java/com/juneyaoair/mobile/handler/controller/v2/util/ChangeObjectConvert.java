package com.juneyaoair.mobile.handler.controller.v2.util;

import com.google.common.collect.Lists;
import com.juneyaoair.CommonBaseConstants;
import com.juneyaoair.appenum.WSEnum;
import com.juneyaoair.appenum.av.PackageTypeEnum;
import com.juneyaoair.appenum.flight.FlightDirection;
import com.juneyaoair.appenum.member.CertificateTypeEnum;
import com.juneyaoair.appenum.order.OrderCouponStateEnum;
import com.juneyaoair.appenum.order.PassengerTypeEnum;
import com.juneyaoair.appenum.rightcoupon.VoucherTypesEnum;
import com.juneyaoair.baseclass.av.common.FlightInfoComb;
import com.juneyaoair.baseclass.av.common.TransferInfo;
import com.juneyaoair.baseclass.av.common.TrrDateLimit;
import com.juneyaoair.baseclass.basicsys.response.AirPortInfoDto;
import com.juneyaoair.baseclass.change.ChangeFlightInfo;
import com.juneyaoair.baseclass.change.ChangePassengerInfo;
import com.juneyaoair.baseclass.change.TicketInfo;
import com.juneyaoair.baseclass.newcoupon.bean.SingleBookCondition;
import com.juneyaoair.baseclass.request.av.AircraftModel;
import com.juneyaoair.baseclass.request.booking.*;
import com.juneyaoair.baseclass.response.coupons.UsePassengerSegment;
import com.juneyaoair.baseclass.response.order.comm.SegmentPriceInfo;
import com.juneyaoair.baseclass.response.order.comm.TaxInfo;
import com.juneyaoair.baseclass.response.order.query.SubOrderResp;
import com.juneyaoair.exception.OperationFailedException;
import com.juneyaoair.exception.QueryNoDataException;
import com.juneyaoair.mobile.handler.HandlerConstants;
import com.juneyaoair.mobile.handler.config.bean.HandConfig;
import com.juneyaoair.mobile.handler.controller.util.IdentityInfoUtil;
import com.juneyaoair.mobile.handler.service.IBasicService;
import com.juneyaoair.mobile.handler.service.LocalCacheService;
import com.juneyaoair.mobile.handler.service.UpdateAirportNameService;
import com.juneyaoair.mobile.handler.service.bean.country.TCountryDTO;
import com.juneyaoair.mobile.handler.util.FlightUtil;
import com.juneyaoair.pattern.PatternCommon;
import com.juneyaoair.thirdentity.av.comm.FareTaxInfo;
import com.juneyaoair.thirdentity.change.request.CouponRelationShip;
import com.juneyaoair.thirdentity.change.request.PtOrderChangeConfirmRequest;
import com.juneyaoair.thirdentity.change.response.ChangeSegmentInfo;
import com.juneyaoair.thirdentity.change.response.OrderChangeBrief;
import com.juneyaoair.thirdentity.request.booking.*;
import com.juneyaoair.thirdentity.response.detr.IdentityInfo;
import com.juneyaoair.thirdentity.response.detr.PtIBETicketInfo;
import com.juneyaoair.thirdentity.response.detr.PtSegmentInfo;
import com.juneyaoair.thirdentity.response.order.apply.*;
import com.juneyaoair.thirdentity.response.tax.InternatTaxInfo;
import com.juneyaoair.thirdentity.salecoupon.request.PtCouponProductGetRequestDto;
import com.juneyaoair.utils.StringUtil;
import com.juneyaoair.utils.util.BeanUtils;
import com.juneyaoair.utils.util.DateUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.juneyaoair.mobile.handler.controller.v2.util.AVObjectConvertV2.*;
import static com.juneyaoair.utils.util.CertUtil.certNoToDate;

/**
 * <AUTHOR>
 * @description 改期对象处理
 * @date 2018/11/28  19:49.
 */
public class ChangeObjectConvert {

    private static final String OTHER_ERROR_MESSAGE = "暂未查询到可线上改期的客票，如有疑问，欢迎致电吉祥航空客服热线95520咨询";
    public static final String LIST_PSP = "listPSP";
    public static final String LIST_SEG = "listSeg";
    public static final String LIST_PRICE = "listPrice";

    /**
     * 根据客票处理航班信息
     *
     * @param ibeTicketInfoList
     * @return
     */
    public static List<TicketInfo> outputTicketInfo(List<PtIBETicketInfo> ibeTicketInfoList, LocalCacheService localCacheService,
                                                    Map<String, AircraftModel> aircraftModelMap, String cabinClassCollection,
                                                    IBasicService flightBaseInfoService, TrrDateLimit trrDateLimit, HandConfig handConfig) {
        List<TicketInfo> ticketInfoList = new ArrayList<>();
        //客票处理
        for (PtIBETicketInfo ibeTicketInfo : ibeTicketInfoList) {
            if (ibeTicketInfo.getSegmentInfoList().stream().anyMatch(o -> HandlerConstants.CHECKED_IN.equals(o.getTicketStatus()))) {
                TicketInfo ticketInfo = new TicketInfo();
                ticketInfo.setNotCanUseMessage("已值机客票暂不支持办理线上改期，请取消值机后再次尝试提交");
                ticketInfoList.add(ticketInfo);
                continue;
            }
            if (ibeTicketInfo.getSegmentInfoList().stream().anyMatch(o -> HandlerConstants.THEM.equals(o.getFareBasic()))) {
                TicketInfo ticketInfo = new TicketInfo();
                ticketInfo.setNotCanUseMessage("您查询的客票不支持自愿改期");
                ticketInfoList.add(ticketInfo);
                continue;
            }
            if (ibeTicketInfo.getAllowChange() != null && !ibeTicketInfo.getAllowChange()) {
                TicketInfo ticketInfo = new TicketInfo();
                ticketInfo.setNotCanUseMessage("超过有效期客票不支持办理改期，如需退票，请致电吉祥航空客服热线95520办理");
                ticketInfoList.add(ticketInfo);
                continue;
            }
            if (HandlerConstants.TRIP_TYPE_I.equalsIgnoreCase(ibeTicketInfo.getInterFlag())) {
                boolean  irrFlag =ibeTicketInfo.getSegmentInfoList().stream().anyMatch(PtSegmentInfo::getIrrFlag);
                if (!irrFlag){
                    //国际航班不可改期
                    TicketInfo ticketInfo = new TicketInfo();
                    ticketInfo.setNotCanUseMessage("国际客票暂不支持办理线上自愿改期，请致电吉祥航空客服热线95520办理");
                    ticketInfoList.add(ticketInfo);
                }
                continue;
            }
            int count = 0, hoCount = 0;
            for (PtSegmentInfo ptSegmentInfo : ibeTicketInfo.getSegmentInfoList()) {
                if ("HO".equals(ptSegmentInfo.getAirline()) && (StringUtils.isBlank(ptSegmentInfo.getOperationAirline()) || "HO".equals(ptSegmentInfo.getOperationAirline()))) {
                    hoCount++;
                }
                //只保留客票可用和改期过的客票
                if (HandlerConstants.OPEN_FOR_USE.equals(ptSegmentInfo.getTicketStatus()) || HandlerConstants.EXCHANGED.equals(ptSegmentInfo.getTicketStatus())) {
                    count++;
                }
            }
            //无有效客票
            if (count == 0) {
                TicketInfo ticketInfo = new TicketInfo();
                ticketInfo.setNotCanUseMessage(OTHER_ERROR_MESSAGE);
                ticketInfoList.add(ticketInfo);
                continue;
            }
            //非自有航班暂不支持线上改期
            if (hoCount != ibeTicketInfo.getSegmentInfoList().size()) {
                continue;
            }
            //多客票时多航段暂不支持升舱
            if (!StringUtil.isNullOrEmpty(ibeTicketInfo.getFollowTicketNo())
                    && HandlerConstants.TRIP_TYPE_D.equals(ibeTicketInfo.getInterFlag())) {
                //多航段不支持改期
                TicketInfo ticketInfo = new TicketInfo();
                ticketInfo.setNotCanUseMessage("中转客票暂不支持办理线上改期，请致电吉祥航空客服热线95520办理");
                ticketInfoList.add(ticketInfo);
                continue;
            }
            //单程航班多于一段 也不支持升舱
            if (ibeTicketInfo.getSegmentInfoList().size() >= 2 && !ibeTicketInfo.getDstCity().equals(ibeTicketInfo.getOrgCity())
                    && HandlerConstants.TRIP_TYPE_D.equals(ibeTicketInfo.getInterFlag())) {
                //如果始发城市和终点城市不相同,就代表不是往返,则为联程
                TicketInfo ticketInfo = new TicketInfo();
                ticketInfo.setNotCanUseMessage("中转客票暂不支持办理线上改期，请致电吉祥航空客服热线95520办理");
                ticketInfoList.add(ticketInfo);
                continue;
            }
            //一个客票航段数多于2段的，暂不支持升舱
            if (ibeTicketInfo.getSegmentInfoList().size() > 2
                    && HandlerConstants.TRIP_TYPE_D.equals(ibeTicketInfo.getInterFlag())) {
                continue;
            }
            //团队票
            boolean teamFlag = FlightUtil.judgeTeamTicket(ibeTicketInfo.getSigningInfo());
            boolean changeFlag = false;  //说明是改期升舱客票
            boolean hasIRRFlag = false; // 拥有非正常航班改期标记
            if (!StringUtil.isNullOrEmpty(ibeTicketInfo.getExchangeInfo())) {
                changeFlag = true;
            }
            TicketInfo ticketInfo = new TicketInfo();
            ticketInfo.setInterFlag(ibeTicketInfo.getInterFlag());
            ticketInfo.setRouteType(HandlerConstants.ROUTE_TYPE_OW);
            ticketInfo.setTicketNo(ibeTicketInfo.getTicketNo());
            ticketInfo.setPnrNo(ibeTicketInfo.getSegmentInfoList().get(0).getPnrNo());
            ticketInfo.setIssueDate(ibeTicketInfo.getIssueDate());
            ticketInfo.setTicketNoInf(ibeTicketInfo.getTicketNoInf());
            ticketInfo.setIsDisneyFlag(ibeTicketInfo.getDisneyFlag());
            int openCount = 0, segCount = 0;
            //查询航班辅助信息
            List<com.juneyaoair.mobile.mongo.entity.FlightInfo> paramList = new ArrayList<>();
            ibeTicketInfo.getSegmentInfoList().stream().forEach(ptSegmentInfo -> {
                com.juneyaoair.mobile.mongo.entity.FlightInfo param = new com.juneyaoair.mobile.mongo.entity.FlightInfo();
                param.setFlightNo(ptSegmentInfo.getFlightNo());
                String[] deptDateTime = separateTime(ptSegmentInfo.getDepTime());
                param.setFlightDate(deptDateTime[0]);
                param.setDepAirport(ptSegmentInfo.getDepAirportCode());
                param.setArrAirport(ptSegmentInfo.getArrAirportCode());
                paramList.add(param);
            });
            Map<String, com.juneyaoair.mobile.mongo.entity.FlightInfo> flightInfoMap = new HashMap<>();
            List<com.juneyaoair.mobile.mongo.entity.FlightInfo> baseInfoList = flightBaseInfoService.queryBaseFlightByList(paramList);
            baseInfoList.forEach(flightInfo -> {
                flightInfoMap.put(flightInfo.getFlightNo(), flightInfo);
            });
            //航段处理
            List<ChangeFlightInfo> changeFlightInfoList = new ArrayList<>();
            boolean freeTicket = false;
            for (PtSegmentInfo segmentInfo : ibeTicketInfo.getSegmentInfoList()) {
                segCount++;
                AirPortInfoDto deptAirPort = localCacheService.getLocalAirport(segmentInfo.getDepAirportCode());
                AirPortInfoDto arrAirPort = localCacheService.getLocalAirport(segmentInfo.getArrAirportCode());
                ChangeFlightInfo changeFlightInfo = new ChangeFlightInfo();
                if (segCount == 2) {
                    AirPortInfoDto deptAirPort1 = localCacheService.getLocalAirport(ibeTicketInfo.getSegmentInfoList().get(segCount - 2).getDepAirportCode());
                    AirPortInfoDto arrAirPort2 = localCacheService.getLocalAirport(ibeTicketInfo.getSegmentInfoList().get(segCount - 1).getArrAirportCode());
                    ;
                    if (!deptAirPort1.getCityCode().equals(arrAirPort2.getCityCode())) {
                        changeFlightInfo.setFlightDirection("G");
                        ticketInfo.setRouteType(HandlerConstants.ROUTE_TYPE_CT);
                    } else {
                        changeFlightInfo.setFlightDirection("B");
                        ticketInfo.setRouteType(HandlerConstants.ROUTE_TYPE_RT);
                    }
                }
                if (segCount == 1) {
                    changeFlightInfo.setFlightDirection("G");
                }
                if (segCount > 2) {
                    changeFlightInfo.setFlightDirection("B");
                    ticketInfo.setRouteType(HandlerConstants.ROUTE_TYPE_RT);
                }
                changeFlightInfo.setDepCityCode(deptAirPort == null ? "" : deptAirPort.getCityCode());
                changeFlightInfo.setDepCityName(deptAirPort == null ? "" : deptAirPort.getCityName());
                changeFlightInfo.setArrCityCode(arrAirPort == null ? "" : arrAirPort.getCityCode());
                changeFlightInfo.setArrCityName(arrAirPort == null ? "" : arrAirPort.getCityName());
                String[] deptDateTime = separateTime(segmentInfo.getDepTime());
                changeFlightInfo.setDepFlightDate(deptDateTime[0]);
                changeFlightInfo.setDepDate(deptDateTime[1]);
                changeFlightInfo.setDepDateTime(deptDateTime[2]);
                String[] arrDateTime = separateTime(segmentInfo.getArrTime());
                changeFlightInfo.setArrFlightDate(arrDateTime[0]);
                changeFlightInfo.setArrDate(arrDateTime[1]);
                changeFlightInfo.setArrDateTime(arrDateTime[2]);
                changeFlightInfo.setFlightNo(segmentInfo.getFlightNo());
                changeFlightInfo.setDepAirportCode(segmentInfo.getDepAirportCode());
                changeFlightInfo.setDepAirportName(deptAirPort == null ? "" : deptAirPort.getAirPortName());
                changeFlightInfo.setArrAirportCode(segmentInfo.getArrAirportCode());
                changeFlightInfo.setArrAirportName(arrAirPort == null ? "" : arrAirPort.getAirPortName());
                changeFlightInfo.setDepTerm(segmentInfo.getDepAirportTerminal());
                changeFlightInfo.setArrTerm(segmentInfo.getArrAirportTerminal());
                changeFlightInfo.setCabin(segmentInfo.getCabin());
                changeFlightInfo.setCabinClass(CommonUtil.getCabinClassByCabinCode(segmentInfo.getCabin(), cabinClassCollection));
                changeFlightInfo.setCabinName(CommonUtil.showCabinClassName(changeFlightInfo.getCabinClass()));
                changeFlightInfo.setTicketPrice(segmentInfo.getTicketPrice());
                String depTimeZone = deptAirPort == null ? "8" : deptAirPort.getCityTimeZone();
                String arrTimeZone = arrAirPort == null ? "8" : arrAirPort.getCityTimeZone();
                int days = DateUtils.diffDays(segmentInfo.getDepTime(), depTimeZone, segmentInfo.getArrTime(), DateUtils.YYYY_MM_DD_PATTERN);
                changeFlightInfo.setDays(days);
                changeFlightInfo.setDuration(DateUtils.calDuration(segmentInfo.getDepTime(), depTimeZone, segmentInfo.getArrTime(), arrTimeZone));
                //东航没有此基础信息
                if ("HO".equals(segmentInfo.getAirline())) {
                    if (flightInfoMap != null && !flightInfoMap.isEmpty()) {
                        com.juneyaoair.mobile.mongo.entity.FlightInfo baseInfo = flightInfoMap.get(segmentInfo.getFlightNo());
                        //查询不到航班信息，flightinfo不存在航班信息 JET属于未知机型
                        if (baseInfo == null) {
                            changeFlightInfo.setAircraftModel("JET");
                        } else {
                            changeFlightInfo.setAircraftModel(aircraftModelMap == null ? "JET" : (aircraftModelMap.get(baseInfo.getPlanType()) != null ? aircraftModelMap.get(baseInfo.getPlanType()).getRemark() : "JET"));
                        }
                    }
                }
                changeFlightInfo.setMealCode("");
                if (HandlerConstants.OPEN_FOR_USE.equals(segmentInfo.getTicketStatus())) {
                    openCount++;
                    changeFlightInfo.setAbleChange(true);
                    // 国内IRR票客进行非自愿改期
                    if (HandlerConstants.FLIGHT_INTER_I.equals(ticketInfo.getInterFlag())){
                           boolean  irrFlag =ibeTicketInfo.getSegmentInfoList().stream().anyMatch(PtSegmentInfo::getIrrFlag);
                           if (irrFlag) {
                               ticketInfo.setNotVoluntaryChange(true);
                               hasIRRFlag = true;
                               segmentInfo.setNotVoluntaryDesc("因您的航班发生变动，可免费更改至前3天或后3天的航班");
                               changeFlightInfo.setNotVoluntaryChange(true);
                           }
                       }else {
                           if (segmentInfo.getIrrFlag()) {
                               ticketInfo.setNotVoluntaryChange(true);
                               hasIRRFlag = true;
                               changeFlightInfo.setNotVoluntaryChange(segmentInfo.getIrrFlag());
                               segmentInfo.setNotVoluntaryDesc("因您的航班发生变动，可免费更改至前3天或后3天的航班");
                           }
                       }
                }
                changeFlightInfo.setAbleChange(segmentInfo.isOperation());
                changeFlightInfo.setNotVoluntaryDesc(segmentInfo.getNotVoluntaryDesc());

                changeFlightInfo.setFireBase(segmentInfo.getFareBasic());
                changeFlightInfo.setChanged(changeFlag);
                //存在前序票号且不是非自愿的不允许改期
                if (changeFlag && !segmentInfo.getIrrFlag()) {
                    changeFlightInfo.setAbleChange(false);
                }
                // 畅飞卡不允许改期
                if (handConfig.getFreeTicketCabin().equals(changeFlightInfo.getCabin()) && !segmentInfo.getIrrFlag()) {
                    changeFlightInfo.setAbleChange(false);
                    freeTicket = true;
                }
                if (handConfig.getAwardFlyFreeTicketCabin().contains(changeFlightInfo.getCabin()) && !segmentInfo.getIrrFlag()) {
                    changeFlightInfo.setAbleChange(false);
                    freeTicket = true;
                }
                //客票查询无对应的改期规则 票号查询时单独处理
                changeFlightInfo.setChangeRuleList(new ArrayList<>());
                changeFlightInfoList.add(changeFlightInfo);
            }
            //乘客处理
            List<ChangePassengerInfo> passengerInfoList = ticketInfoToOrderPass(ibeTicketInfo, ticketInfo.getTicketNo(), localCacheService);
            ticketInfo.setPassengerInfoList(passengerInfoList);
            if (openCount > 0 && !changeFlag && !freeTicket) {
                ticketInfo.setAbleChange(true);
            } else if (hasIRRFlag) {
                ticketInfo.setAbleChange(true);
            }
            //处理旧的航班舱位信息
            List<FlightInfo> oldFlightInfoList = ticketInfoToFlight(ibeTicketInfo, passengerInfoList.get(0).getPassengerType(), localCacheService, cabinClassCollection);
            //国际航班税费处理
            if (HandlerConstants.TRIP_TYPE_I.equals(ibeTicketInfo.getInterFlag())) {
                List<FareTaxInfo> fareTaxInfoList = new ArrayList<>();
                Double amount = 0.0, cn = 0.0, yq = 0.0, q = 0.0;
                List<TaxInfo> taxInfoDetailList = new ArrayList<>();
                for (com.juneyaoair.thirdentity.response.detr.TaxInfo taxInfo : ibeTicketInfo.getTaxInfoList()) {
                    TaxInfo taxInfoDetail = new TaxInfo();
                    BeanUtils.copyProperties(taxInfo, taxInfoDetail);
                    if ("CN".equalsIgnoreCase(taxInfo.getTaxCode())) {
                        cn = taxInfo.getTaxAmount();
                    } else if ("YQ".equals(taxInfo.getTaxCode())) {
                        yq = taxInfo.getTaxAmount();
                    } else if ("Q".equals(taxInfo.getTaxCode())) {
                        q = taxInfo.getTaxAmount();
                    }
                    taxInfoDetail.setCurrency(taxInfo.getTaxCurrencyType());
                    amount += taxInfo.getTaxAmount();
                    taxInfoDetailList.add(taxInfoDetail);
                }
                FareTaxInfo fareTaxInfo = new FareTaxInfo(passengerInfoList.get(0).getPassengerType(), cn, yq, q, amount - cn - yq - q);
                fareTaxInfo.setTaxInfoList(taxInfoDetailList);
                fareTaxInfoList.add(fareTaxInfo);
                ticketInfo.setOldFareTaxInfoList(fareTaxInfoList);
            }
            ticketInfo.setChangeFlightInfoList(changeFlightInfoList);
            ticketInfo.setOldFlightInfoList(oldFlightInfoList);
            ticketInfo.setTeamFlag(teamFlag);
            ticketInfo.setTrrDateLimit(trrDateLimit);
            // 可改期航班才展示
            if (ticketInfo.isAbleChange()) {
                ticketInfoList.add(ticketInfo);
            }
        }
        return ticketInfoList;
    }

    /**
     * 订单信息转换
     *
     * @param orderChangeBriefList
     * @return
     */
    public static List<TicketInfo> changeOrderToTicketInfoList(List<OrderChangeBrief> orderChangeBriefList, Map<String, AirPortInfoDto> airPortInfoMap, HandConfig handConfig) {
        List<TicketInfo> ticketInfoList = new ArrayList<>();
        if (StringUtil.isNullOrEmpty(orderChangeBriefList)) {
            return ticketInfoList;
        }
        Map<String, AircraftModel> aircraftModelMap = FlightUtil.toAircraftModelMap(handConfig.getAircraftModel());
        String cabinClassCollection = handConfig.getCabinClass();
        for (OrderChangeBrief orderChangeBrief : orderChangeBriefList) {
            // 多程客票暂不支持订单改期
            if (HandlerConstants.ORDER_TICKET_RANGE_TYPE_MULTIPLE.equals(orderChangeBrief.getTicketRangeType())) {
                continue;
            }
            TicketInfo ticketInfo = new TicketInfo();
            ticketInfo.setOrderNo(orderChangeBrief.getOrderNo());
            ticketInfo.setChannelOrderNo(orderChangeBrief.getChannelOrderNo());
            ticketInfo.setInterFlag(orderChangeBrief.getFlightType());
            ticketInfo.setOrderSort(orderChangeBrief.getOrderSort());
            ticketInfo.setAbleChange(true);

            List<ChangeSegmentInfo> GoFlightInfoCombList = orderChangeBrief.getSegmentInfoList()
                    .stream().filter(segmentInfo -> FlightDirection.GO.getCode().equals(segmentInfo.getFlightDirection())).collect(Collectors.toList());

            List<PtSegmentPriceInfo> ptSegmentPriceList = new ArrayList<>();
            for (ChangeSegmentInfo changeSegment : GoFlightInfoCombList) {
                PtSegmentPriceInfo ptSegment = new PtSegmentPriceInfo();
                 BeanUtils.copyProperties(changeSegment, ptSegment);
                ptSegmentPriceList.add(ptSegment);
            }

            List<TransferInfo> transferInfoList = setTransferInfoList(ptSegmentPriceList, airPortInfoMap);
            //航段处理
            int changeCount = 0;
            List<ChangeFlightInfo> changeFlightInfoList = Lists.newArrayList();
            // key : 航线
            for (ChangeSegmentInfo changeSegmentInfo : orderChangeBrief.getSegmentInfoList()) {
                airPortInfoMap = UpdateAirportNameService.updateMapByDate(airPortInfoMap, DateUtils.toDate(changeSegmentInfo.getDepDateTime(), "yyyy-MM-dd HH:mm"));
                AirPortInfoDto deptAirPort = airPortInfoMap.get(changeSegmentInfo.getDepAirport());
                //获取时间
                airPortInfoMap = UpdateAirportNameService.updateMapByDate(airPortInfoMap, DateUtils.toDate(changeSegmentInfo.getArrDateTime(), "yyyy-MM-dd HH:mm"));
                AirPortInfoDto arrAirPort = airPortInfoMap.get(changeSegmentInfo.getArrAirport());
                ChangeFlightInfo changeFlightInfo = new ChangeFlightInfo();
                changeFlightInfo.setAbleChange(changeSegmentInfo.isIsChange());
                changeFlightInfo.setFlightNo(changeSegmentInfo.getFlightNo());
                changeFlightInfo.setFlightDirection(changeSegmentInfo.getFlightDirection());
                changeFlightInfo.setDepCityCode(changeSegmentInfo.getDepCity());
                changeFlightInfo.setDepCityName(deptAirPort == null ? "" : deptAirPort.getCityName());
                changeFlightInfo.setArrCityCode(changeSegmentInfo.getArrCity());
                changeFlightInfo.setArrCityName(arrAirPort == null ? "" : arrAirPort.getCityName());
                String[] deptDateTime = separateTime(changeSegmentInfo.getDepDateTime());
                changeFlightInfo.setDepFlightDate(deptDateTime[0]);
                changeFlightInfo.setDepDate(deptDateTime[1]);
                changeFlightInfo.setDepDateTime(deptDateTime[2]);
                String[] arrDateTime = separateTime(changeSegmentInfo.getArrDateTime());
                changeFlightInfo.setArrFlightDate(arrDateTime[0]);
                changeFlightInfo.setArrDate(arrDateTime[1]);
                changeFlightInfo.setArrDateTime(arrDateTime[2]);
                changeFlightInfo.setDepAirportCode(changeSegmentInfo.getDepAirport());
                changeFlightInfo.setDepAirportName(deptAirPort == null ? "" : deptAirPort.getAirPortName());
                changeFlightInfo.setArrAirportCode(changeSegmentInfo.getArrAirport());
                changeFlightInfo.setArrAirportName(arrAirPort == null ? "" : arrAirPort.getAirPortName());
                changeFlightInfo.setDepTerm(changeSegmentInfo.getDepTerm() == null ? "" : changeSegmentInfo.getDepTerm());
                changeFlightInfo.setArrTerm(changeSegmentInfo.getArrTerm() == null ? "" : changeSegmentInfo.getArrTerm());
                changeFlightInfo.setCabin(changeSegmentInfo.getCabin());
                changeFlightInfo.setCabinClass(CommonUtil.getCabinClassByCabinCode(changeSegmentInfo.getCabin(), cabinClassCollection));
                changeFlightInfo.setCabinName(CommonUtil.showCabinClassName(changeFlightInfo.getCabinClass()));
                changeFlightInfo.setAircraftModel(aircraftModelMap == null ? "" :
                        (aircraftModelMap.get(changeSegmentInfo.getPlaneStyle()) == null ? "" :
                                aircraftModelMap.get(changeSegmentInfo.getPlaneStyle()).getRemark()));
                String depTimeZone = deptAirPort == null ? "8" : deptAirPort.getCityTimeZone();
                String arrTimeZone = arrAirPort == null ? "8" : arrAirPort.getCityTimeZone();
                int days = DateUtils.diffDays(changeSegmentInfo.getDepDateTime(), depTimeZone, changeSegmentInfo.getArrDateTime(), DateUtils.YYYY_MM_DD_PATTERN);
                changeFlightInfo.setDays(days);
                changeFlightInfo.setDuration(DateUtils.calDuration(changeSegmentInfo.getDepDateTime(), depTimeZone, changeSegmentInfo.getArrDateTime(), arrTimeZone));
                changeFlightInfo.setTransferInfoList(transferInfoList);
                //true代表可改期  此处统计不可改期
                if (!changeSegmentInfo.isIsChange() || changeSegmentInfo.getCarrierFlightNo().startsWith("3U")) {
                    changeCount++;
                }
                changeFlightInfoList.add(changeFlightInfo);

            }
            if (CollectionUtils.isNotEmpty(changeFlightInfoList)) {
                ticketInfo.setDepDate(changeFlightInfoList.get(0).getDepFlightDate()+" "+changeFlightInfoList.get(0).getDepDateTime());
            }
            ticketInfo.setChangeFlightInfoList(changeFlightInfoList);
            //最终状态判定
            if (changeCount > 0) {
                ticketInfo.setAbleChange(false);
            }
            //非正常机票不支持改期
            if (!"Normal".equals(orderChangeBrief.getOrderSort())) {
                ticketInfo.setAbleChange(false);
            }
            //国际航班不做返回以及同一方向航段数大于1
            if (ticketInfo.isAbleChange()) {
                ticketInfoList.add(ticketInfo);
            }
        }
        ticketInfoList= ticketInfoList
                .stream()
                .sorted(Comparator.comparing(TicketInfo::getDepDate))
                .collect(Collectors.toList());
        return ticketInfoList;
    }

    /**
     * 根据订单信息转换航段以及乘客信息
     */
    public static TicketInfo toTicketInfo(SubOrderResp subOrderResp, PtRefundApplyResp reRefundResp, Map<String, AirPortInfoDto> airportMap, LocalCacheService localCacheService) {
        TicketInfo ticketInfo = new TicketInfo();
        ticketInfo.setInterFlag(reRefundResp.getInterFlag());
        ticketInfo.setOrderSort(reRefundResp.getOrderSort());
        ticketInfo.setOrderNo(subOrderResp.getOrderNo());
        ticketInfo.setRouteType(reRefundResp.getRouteType());
        ticketInfo.setChannelOrderNo(subOrderResp.getChannelOrderNo());
        ticketInfo.setOriginChannelCode(subOrderResp.getOriginChannelCode());
        ticketInfo.setLinker(reRefundResp.getLinker());
        ticketInfo.setLinkerHandphone(reRefundResp.getLinkerHandphone());
        ticketInfo.setFareType(reRefundResp.getFareType());
        List<ChangeFlightInfo> changeFlightInfoList = new ArrayList<>();
        List<PtSegmentPriceInfo> GoFlightInfoCombList = reRefundResp.getSegmentInfoList()
                .stream().filter(segmentInfo -> FlightDirection.GO.getCode().equals(segmentInfo.getFlightDirection())).collect(Collectors.toList());
        List<TransferInfo> transferInfoList = setTransferInfoList(GoFlightInfoCombList, airportMap);

        //航班处理
        reRefundResp.getSegmentInfoList().forEach(ptSegmentPriceInfo -> {
            ChangeFlightInfo changeFlightInfo = new ChangeFlightInfo();
            changeFlightInfo.setFlightDirection(ptSegmentPriceInfo.getFlightDirection());
            UpdateAirportNameService.updateMapByDate(airportMap, DateUtils.toDate(ptSegmentPriceInfo.getDepDateTime(), "yyyy-MM-dd HH:mm"));
            AirPortInfoDto deptAirPort = airportMap.get(ptSegmentPriceInfo.getDepAirport());
            AirPortInfoDto arrAirPort = airportMap.get(ptSegmentPriceInfo.getArrAirport());
            changeFlightInfo.setDepCityCode(deptAirPort == null ? "" : deptAirPort.getCityCode());
            changeFlightInfo.setDepCityName(deptAirPort == null ? "" : deptAirPort.getCityName());
            changeFlightInfo.setArrCityCode(arrAirPort == null ? "" : arrAirPort.getCityCode());
            changeFlightInfo.setArrCityName(arrAirPort == null ? "" : arrAirPort.getCityName());
            String[] deptDateTime = separateTime(ptSegmentPriceInfo.getDepDateTime());
            changeFlightInfo.setDepFlightDate(deptDateTime[0]);
            changeFlightInfo.setDepDate(deptDateTime[1]);
            changeFlightInfo.setDepDateTime(deptDateTime[2]);
            String[] arrDateTime = separateTime(ptSegmentPriceInfo.getArrDateTime());
            changeFlightInfo.setArrFlightDate(arrDateTime[0]);
            changeFlightInfo.setArrDate(arrDateTime[1]);
            changeFlightInfo.setArrDateTime(arrDateTime[2]);
            changeFlightInfo.setFlightNo(ptSegmentPriceInfo.getFlightNo());
            changeFlightInfo.setDepAirportCode(ptSegmentPriceInfo.getDepAirport());
            changeFlightInfo.setDepAirportName(deptAirPort == null ? "" : deptAirPort.getAirPortName());
            changeFlightInfo.setArrAirportCode(ptSegmentPriceInfo.getArrAirport());
            changeFlightInfo.setArrAirportName(arrAirPort == null ? "" : arrAirPort.getAirPortName());
            changeFlightInfo.setDepTerm(ptSegmentPriceInfo.getDepTerm());
            changeFlightInfo.setArrTerm(ptSegmentPriceInfo.getArrTerm());
            changeFlightInfo.setCabin(ptSegmentPriceInfo.getCabin());
            changeFlightInfo.setTransferInfoList(transferInfoList);
            changeFlightInfoList.add(changeFlightInfo);
        });
        ticketInfo.setChangeFlightInfoList(changeFlightInfoList);
        //乘客信息
        List<ChangePassengerInfo> passengerInfoList = toPassengerInfoList(reRefundResp, airportMap, localCacheService);
        ticketInfo.setPassengerInfoList(passengerInfoList);
        // 存放订单使用的优惠券信息
        ticketInfo.setOrderCouponList(reRefundResp.getOrderCouponList());
        return ticketInfo;
    }


    private static List<TransferInfo> setTransferInfoList(List<PtSegmentPriceInfo> flightInfoList, Map<String, AirPortInfoDto> map) {
        if (flightInfoList.size() > 1) {
            List<TransferInfo> transferInfoList = new ArrayList<>();
            for (int i = 1; i < flightInfoList.size(); i++) {
                TransferInfo transferInfo = new TransferInfo("中转");
                PtSegmentPriceInfo preFlightInfo = flightInfoList.get(i - 1);
                PtSegmentPriceInfo curFlightInfo = flightInfoList.get(i);
                AirPortInfoDto deptAirPort = map.get(preFlightInfo.getArrAirport());
                transferInfo.setTransferCityName(deptAirPort.getCityName());
                transferInfo.setTransferAirPort(preFlightInfo.getArrAirport());
                transferInfo.setTransferAirPortFrom(curFlightInfo.getDepAirport());
                //中转一般都为同一时区中转，可不考虑时区问题
                Date arrTime = DateUtils.toDate(preFlightInfo.getArrDateTime(), DateUtils.YYYY_MM_DD_HH_MM_PATTERN);
                Date depTime = DateUtils.toDate(curFlightInfo.getDepDateTime(), DateUtils.YYYY_MM_DD_HH_MM_PATTERN);
                if (depTime != null && arrTime != null) {
                    transferInfo.setTransferTime(depTime.getTime() - arrTime.getTime());
                    transferInfo.setCrossDays(DateUtils.durDays(arrTime, depTime));
                }
                if (!preFlightInfo.getArrAirport().equals(curFlightInfo.getDepAirport())) {
                    transferInfo.setTransferDesc("不同机场中转");
                    transferInfo.setSameAirport(false);
                } else {
                    transferInfo.setTransferDesc("相同机场中转");
                    transferInfo.setSameAirport(true);
                }
                preFlightInfo.setTransferInfo(transferInfo);
                transferInfoList.add(transferInfo);
            }
            return transferInfoList;
        }
        return new ArrayList<>();
    }

    /**
     * 根据订单信息处理乘客信息
     *
     * @param resp
     * @param airportMap
     * @param localCacheService
     * @return
     */
    public static List<ChangePassengerInfo> toPassengerInfoList(PtRefundApplyResp resp, Map<String, AirPortInfoDto> airportMap, LocalCacheService localCacheService) {
        List<ChangePassengerInfo> passlist = new ArrayList<>();
        for (PtOrderPassengerInfo ptPass : resp.getPassengerInfoList()) {
            ChangePassengerInfo orderPass = new ChangePassengerInfo();
            BeanUtils.copyProperties(ptPass, orderPass);
            orderPass.setSegmentPriceInfoList(ToSegmentPriceInfoList(orderPass.getPassengerID(), resp, airportMap));
            orderPass.setTicketOrderNo(resp.getTicketOrderNo());
            orderPass.setCantRefundReason("Y".equals(ptPass.getAdtIsConnectedChd()) ? "当前客票已关联同行儿童旅客，请致电客服热线95520为儿童客票一同办理改期；如希望儿童在无成人陪伴的情况下单独出行，请务必提前申请“无成人陪伴儿童”服务，吉祥航空有权拒绝未申请无陪服务的儿童独自出行的要求。" : "");
            orderPass.setAdtIsConnectedChd(ptPass.getAdtIsConnectedChd());
            orderPass.setCertTypeName("证件号");
            orderPass.setCertType(ptPass.getCertType());
            orderPass.setBirthdate(ptPass.getBirthdate());
            List<String> countryCode = Arrays.asList(ptPass.getNationality(), ptPass.getBelongCountry()).stream().filter(i -> i != null).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(countryCode)) {
                List<TCountryDTO> localCountryList = localCacheService.getLocalCountry(countryCode);
                if (CollectionUtils.isNotEmpty(localCountryList)) {
                    if (StringUtils.isNotBlank(ptPass.getNationality())) {
                        orderPass.setNationalityName(localCountryList.stream().filter(i -> ptPass.getNationality().equalsIgnoreCase(i.countryCode)).findFirst().map(i -> i.countryName).orElse(null));
                        orderPass.setNationalityEName(localCountryList.stream().filter(i -> ptPass.getNationality().equalsIgnoreCase(i.countryCode)).findFirst().map(i -> i.englishName).orElse(null));
                    }
                    if (StringUtils.isNotBlank(ptPass.getBelongCountry())) {
                        orderPass.setBelongCountryName(localCountryList.stream().filter(i -> ptPass.getBelongCountry().equalsIgnoreCase(i.countryCode)).findFirst().map(i -> i.countryName).orElse(null));
                        orderPass.setBelongCountryEName(localCountryList.stream().filter(i -> ptPass.getBelongCountry().equalsIgnoreCase(i.countryCode)).findFirst().map(i -> i.englishName).orElse(null));
                    }
                }
            }
            if (StringUtils.isNotBlank(orderPass.getPassengerName()) && orderPass.getPassengerName().contains("/")) {
                String[] split = orderPass.getPassengerName().split("/");
                orderPass.setPassEnNameS(split[0]);
                orderPass.setPassEnNameF(split[1]);
            }

            List<TaxInfo> taxList = new ArrayList<>();
            for (int tax = 0; tax < ptPass.getTaxInfoList().length; tax++) {
                TaxInfo taxInfo = new TaxInfo();
                PtTaxInfo ptTaxInfo = ptPass.getTaxInfoList()[tax];
                BeanUtils.copyProperties(ptTaxInfo, taxInfo);
                taxList.add(taxInfo);
            }
            orderPass.setTaxInfoList(taxList);
            //国际税费处理
            if (HandlerConstants.TRIP_TYPE_I.equals(resp.getInterFlag())) {
                Double taxAmount = 0.0;
                for (TaxInfo taxInfo : orderPass.getTaxInfoList()) {
                    taxAmount += taxInfo.getTaxAmount();
                }
                orderPass.getSegmentPriceInfoList().get(0).setXTax(taxAmount);
            }
            //改期乘客不算保险信息
            orderPass.setIsBuyInsurance("N");
            orderPass.setInsuranceList(null);
            orderPass.setInsuranceAmount(0D);
            for (PtPassengerSegment ptPassengerSegment : resp.getPassengerSegmentList()) {
                if (ptPassengerSegment.getPassengerID() == orderPass.getPassengerID()) {
                    orderPass.setETicketNo(ptPassengerSegment.getTicketNo());
                    if (StringUtils.isNotBlank(ptPassengerSegment.getFareBasis())) {
                        if (ptPassengerSegment.getFareBasis().startsWith(HandlerConstants.PASSENGER_TYPE_GMJC)) {
                            orderPass.setPassengerType(HandlerConstants.PASSENGER_TYPE_GMJC);
                        }
                    }
                }
            }
            passlist.add(orderPass);
        }
        //计算乘客总的应付金额
        double amountPayable = 0;
        for (ChangePassengerInfo changePassengerInfo : passlist) {
            for (SegmentPriceInfo segmentPriceInfo : changePassengerInfo.getSegmentPriceInfoList()) {
                amountPayable = amountPayable + segmentPriceInfo.getPricePaid() + segmentPriceInfo.getUseScore() + segmentPriceInfo.getCouponAmount();
            }
            changePassengerInfo.setParPrice(amountPayable);
        }
        return passlist;
    }

    //人下航段
    public static List<SegmentPriceInfo> ToSegmentPriceInfoList(int passengerID, PtRefundApplyResp resp, Map<String, AirPortInfoDto> airportMap) {
        List<SegmentPriceInfo> segPrc = new ArrayList<SegmentPriceInfo>();
        List<PtPassengerSegment> curSegList = new ArrayList<PtPassengerSegment>();
        //人对应的航段
        for (PtPassengerSegment ptPassSeg : resp.getPassengerSegmentList()) {
            if (ptPassSeg.getPassengerID() == passengerID) {
                curSegList.add(ptPassSeg);
            }
        }

        for (PtPassengerSegment ptPassengerSegment : curSegList) {
            //获取乘客下的航段信息
            PtSegmentPriceInfo ptSegPrc = getSegmentPrc(resp.getSegmentInfoList(), ptPassengerSegment.getSegmentID());
            //人航段价格
            PtPassengerSegment ptSeg = getSegment(resp.getPassengerSegmentList(), ptPassengerSegment.getPassengerID(), ptPassengerSegment.getSegmentID());
            if (null != ptSeg) {
                Boolean insureApplySuccess = HandlerConstants.INSURE_STATE_SUCCESS.equals(ptSeg.getInsuranceState()) || HandlerConstants.INSURE_STATE_APPLY.equals(ptSeg.getInsuranceState());
                SegmentPriceInfo segmentPriceInfo = new SegmentPriceInfo();
                if (null != ptSegPrc) {
                    BeanUtils.copyProperties(ptSegPrc, segmentPriceInfo);
                    airportMap = UpdateAirportNameService.updateMapByDate(airportMap, DateUtils.toDate(ptSegPrc.getDepDateTime(), "yyyy-MM-dd HH:mm"));
                    segmentPriceInfo.setDepCityName(airportMap.get(ptSegPrc.getDepAirport()).getCityName());
                    segmentPriceInfo.setArrCityName(airportMap.get(ptSegPrc.getArrAirport()).getCityName());
                    segmentPriceInfo.setDepAirportName(airportMap.get(ptSegPrc.getDepAirport()).getAirPortName());
                    segmentPriceInfo.setArrAirportName(airportMap.get(ptSegPrc.getArrAirport()).getAirPortName());
                }
                BeanUtils.copyProperties(ptSeg, segmentPriceInfo);
                segmentPriceInfo.setIsBuyInsurance(insureApplySuccess);
                segmentPriceInfo.setAmount(ptSeg.getPricePaid() + ptSeg.getYQTax() + ptSeg.getCNTax() + (insureApplySuccess ? ptSeg.getInsuranceAmount() : 0.0));
                segmentPriceInfo.setID(ptSeg.getPassengerSegmentID());
                segmentPriceInfo.setUseScore(ptSeg.getDeductibls());
                segmentPriceInfo.setCouponAmount(ptSeg.getCouponAmount());
                segmentPriceInfo.setGiftScore(ptSeg.getGifiScore());
                segmentPriceInfo.setETicketNo(ptSeg.getTicketNo());
                segmentPriceInfo.setTKTStatus(ptSeg.getTicketState());
                segmentPriceInfo.setUpgradeTicketPrice(ptSeg.getSegmentInfoOTO() != null ? (ptSeg.getSegmentInfoOTO().getUpgradeTicketPrice() == null ? 0 : ptSeg.getSegmentInfoOTO().getUpgradeTicketPrice()) : 0);
                segPrc.add(segmentPriceInfo);
            }
        }
        return segPrc;
    }

    //获取指定航线
    public static PtSegmentPriceInfo getSegmentPrc(List<PtSegmentPriceInfo> segPrcList, int SegId) {
        for (PtSegmentPriceInfo segmentPriceInfo : segPrcList) {
            if (SegId == segmentPriceInfo.getSegmentID()) {
                return segmentPriceInfo;
            }
        }
        return null;
    }

    //获取人航段
    public static PtPassengerSegment getSegment(List<PtPassengerSegment> passSeg, int passengerID, int SegId) {
        for (PtPassengerSegment passengerSegment : passSeg) {
            if (SegId == passengerSegment.getSegmentID() && passengerID == passengerSegment.getPassengerID()) {
                return passengerSegment;
            }
        }
        return null;
    }

    /**
     * 组装机票确认改期订单请求参数
     *
     * @param channelCode
     * @param userNo
     * @param orderChangeBookingReq
     * @return
     */
    public static PtOrderChangeConfirmRequest toOrderChangeConfirmReq(String channelCode, String userNo, Map<String, List<ChangePassengerInfo>> passMap,
                                                                      OrderChangeBookingReq orderChangeBookingReq) {
        PtOrderChangeConfirmRequest ptOrderChangeConfirmRequest =
                new PtOrderChangeConfirmRequest(HandlerConstants.VERSION, channelCode, userNo, HandlerConstants.CURRENCY_CODE);
        ptOrderChangeConfirmRequest.setTicketOrderSort("Change");
        //乘客基本复制
        List<PassengerInfo> passengerInfoList = new ArrayList<>();
        for (ChangePassengerInfo changePassengerInfo : orderChangeBookingReq.getPassengerInfoList()) {
            PassengerInfo passengerInfo = new PassengerInfo();
            BeanUtils.copyProperties(changePassengerInfo, passengerInfo);
            passengerInfoList.add(passengerInfo);
        }
        int h = 0;// 乘客序号赋值
        for (PassengerInfo pass : passengerInfoList) {// 乘客集合
            pass.setPassengerNO(h++);
        }
        BeanUtils.copyProperties(orderChangeBookingReq, ptOrderChangeConfirmRequest);
        ptOrderChangeConfirmRequest.setVoluntarilyChangeFlag(!orderChangeBookingReq.isNotVoluntaryChange());
        //封装基本历史航班运价
        List<FlightInfo> oldFlightInfoList = toOldFlightInfoList(passMap);
        List<FlightInfo> newFlightInfoList = new ArrayList<>();
        //匹配改期航段
        for (FlightInfo oldFlightInfo : oldFlightInfoList) {
            FlightInfo newFlightInfo = new FlightInfo();
            BeanUtils.copyProperties(oldFlightInfo, newFlightInfo);
            newFlightInfo.setSelectFlag(false);
            for (FlightInfo flightInfo : orderChangeBookingReq.getFlightInfoList()) {
                //说明是改期航段
                if (oldFlightInfo.getDepCity().equals(flightInfo.getDepCity()) && oldFlightInfo.getArrCity().equals(flightInfo.getArrCity())) {
                    BeanUtils.copyProperties(flightInfo, newFlightInfo);
                    newFlightInfo.setSelectFlag(true);
                    if (newFlightInfo.isNotVoluntaryChange()) {
                        copyNotVoluntaryChangeCabinFare(oldFlightInfo.getCabinFareList(), newFlightInfo.getCabinFareList());
                        copyNotVoluntaryChangeCabinFare(oldFlightInfo.getCabinCHDINFFareList(), newFlightInfo.getCabinCHDINFFareList());
                        copyNotVoluntaryChangeCabinFare(oldFlightInfo.getCabinGMJCFareList(), newFlightInfo.getCabinGMJCFareList());
                    }
                    break;
                }
            }
            newFlightInfoList.add(newFlightInfo);
        }
        //子订单的人员分组
        Map<String, List<PassengerInfo>> dicTicketGroup = toGroupPassenger(passengerInfoList, newFlightInfoList, orderChangeBookingReq.getInterFlag());
        int segNo = 0;// 航段序号赋值
        int priceNo = 0;// 运价序号赋值
        Map<String, List<?>> segPriceMap = new HashMap<>();
        for (Map.Entry<String, List<PassengerInfo>> entry : dicTicketGroup.entrySet()) {
            List<PassengerInfo> passGroupList = entry.getValue();
            List<com.juneyaoair.thirdentity.request.booking.PtSegmentInfo> ptSegmentInfoList = new ArrayList<>();
            List<PtPrice> ptPriceList = new ArrayList<>();
            List<PtPSPriceRel> ptPSPriceRelList = new ArrayList<>();
            if (!segPriceMap.isEmpty()) {
                ptSegmentInfoList = (List<com.juneyaoair.thirdentity.request.booking.PtSegmentInfo>) segPriceMap.get("listSeg");
                ptPriceList = (List<PtPrice>) segPriceMap.get("listPrice");
                ptPSPriceRelList = (List<PtPSPriceRel>) segPriceMap.get("listPSP");
            }
            segNo = ptSegmentInfoList.size();
            priceNo = ptPriceList.size();
            //用于存储人，航段，运价关系
            Map<String, List<?>> tempMap = toPassSegPriceMap(newFlightInfoList, entry.getKey(), passGroupList, segNo, priceNo);
            for (PassengerInfo passengerInfo : passGroupList) {
                List<PtPSPriceRel> priceList = new ArrayList<>();
                List<PtPSPriceRel> curPriceRelList = (List<PtPSPriceRel>) tempMap.get("listPSP");//乘客 运价 航段对应关系
                for (PtPSPriceRel ptPriceRel : curPriceRelList) {// 乘客对应所有价格
                    if (ptPriceRel.getPassengerNO() == passengerInfo.getPassengerNO()) {
                        priceList.add(ptPriceRel);
                    }
                }
                List<PtPrice> curAllPriceList = (List<PtPrice>) tempMap.get("listPrice");
                // 计算价格
                double ticketPrice = 0.0, pricePaid = 0.0, YQTax = 0.0, CNTax = 0.0;
                double totalDiffPrice = 0.0;
                for (PtPSPriceRel ptPSPriceRel : priceList) {
                    for (PtPrice ptPrice : curAllPriceList) {
                        if (ptPSPriceRel.getPriceNO() == ptPrice.getPriceNO()) {
                            ticketPrice += ptPrice.getRSP();
                            pricePaid += ptPrice.getPriceValue();
                            totalDiffPrice += ptPrice.getTotalDiff();
                            if (HandlerConstants.FLIGHT_INTER_D.equals(orderChangeBookingReq.getInterFlag())) {// 国内机建燃油费
                                YQTax += ptPrice.getYQTax();
                                CNTax += ptPrice.getCNTax();
                            }
                        }
                    }
                }
                passengerInfo.setTicketPrice(ticketPrice);
                passengerInfo.setPricePaid(pricePaid);
                passengerInfo.setTotalDiff(totalDiffPrice);
                if (HandlerConstants.FLIGHT_INTER_D.equals(orderChangeBookingReq.getInterFlag())) {
                    passengerInfo.setyQTax(YQTax);
                    passengerInfo.setcNTax(CNTax);
                }
            }

            ptSegmentInfoList.addAll((List<com.juneyaoair.thirdentity.request.booking.PtSegmentInfo>) tempMap.get("listSeg"));
            ptPriceList.addAll((List<PtPrice>) tempMap.get("listPrice"));
            ptPSPriceRelList.addAll((List<PtPSPriceRel>) tempMap.get("listPSP"));
            segPriceMap.put("listSeg", ptSegmentInfoList);
            segPriceMap.put("listPrice", ptPriceList);
            segPriceMap.put("listPSP", ptPSPriceRelList);
        }
        //乘客信息
        double payAmount = 0.0;
        List<PtPassengerInfo> ptPassList = new ArrayList<>();
        for (PassengerInfo pass : passengerInfoList) {
            PtPassengerInfo ptPassengerInfo = new PtPassengerInfo();
            BeanUtils.copyProperties(pass, ptPassengerInfo);
            //改期乘客是无保险的
            ptPassengerInfo.setInsuranceList(null);
            payAmount += pass.getTotalDiff();
            ptPassList.add(ptPassengerInfo);
        }
        List<com.juneyaoair.thirdentity.request.booking.PtSegmentInfo> segmentInfos =
                (List<com.juneyaoair.thirdentity.request.booking.PtSegmentInfo>) segPriceMap.get("listSeg");

        List<PtPrice> prices = (List<PtPrice>) segPriceMap.get("listPrice");
        List<PtPSPriceRel> rels = (List<PtPSPriceRel>) segPriceMap.get("listPSP");
        List<CouponRelationShip> useCouponRel = genUseCouponRel(orderChangeBookingReq.getUsePassengerSegments(),
                rels, segmentInfos, prices, ptPassList);
        if (CollectionUtils.isNotEmpty(useCouponRel)) {
            ptOrderChangeConfirmRequest.setCouponFlag(true);
            ptOrderChangeConfirmRequest.setCouponRelationShip(useCouponRel);
            for (CouponRelationShip couponRelationShip : useCouponRel) {
                if (BigDecimal.ZERO.equals(couponRelationShip.getPriceValue())) {
                    throw new OperationFailedException("改期券抵扣金额计算有误");
                }
                payAmount -= couponRelationShip.getPriceValue().doubleValue();
            }
        }
        ptOrderChangeConfirmRequest.setPayAmount(payAmount);
        ptOrderChangeConfirmRequest.setPassengerInfoList(ptPassList);
        ptOrderChangeConfirmRequest.setSegmentInfoList(segmentInfos);
        ptOrderChangeConfirmRequest.setPriceInfoList(prices);
        ptOrderChangeConfirmRequest.setPSPriceRelList(rels);
        ptOrderChangeConfirmRequest.setChannelCustomerNo(orderChangeBookingReq.getFfpId());
        return ptOrderChangeConfirmRequest;
    }


    /**
     * 组装机票确认改期订单请求参数
     *
     * @param channelCode
     * @param userNo
     * @param orderChangeBookingReq
     * @return
     */
    public static PtOrderChangeConfirmRequest toOrderChangeConfirmReqV30(String channelCode, String userNo, Map<String, List<ChangePassengerInfo>> passMap,
                                                                         OrderChangeBookingReq orderChangeBookingReq) {
        PtOrderChangeConfirmRequest ptOrderChangeConfirmRequest =
                new PtOrderChangeConfirmRequest(HandlerConstants.VERSION, channelCode, userNo, HandlerConstants.CURRENCY_CODE);
        ptOrderChangeConfirmRequest.setTicketOrderSort("Change");
        //乘客基本复制
        List<PassengerInfo> passengerInfos = new ArrayList<>();
        for (ChangePassengerInfo changePassengerInfo : orderChangeBookingReq.getPassengerInfoList()) {
            PassengerInfo passengerInfo = new PassengerInfo();
            BeanUtils.copyProperties(changePassengerInfo, passengerInfo);
            passengerInfos.add(passengerInfo);
        }
        BeanUtils.copyProperties(orderChangeBookingReq, ptOrderChangeConfirmRequest);
        boolean notVoluntaryChange = orderChangeBookingReq.getFlightInfoComb().getCombFlightInfoList().stream()
                .anyMatch(com.juneyaoair.baseclass.response.av.FlightInfo::isNotVoluntaryChange);
        ptOrderChangeConfirmRequest.setVoluntarilyChangeFlag(!notVoluntaryChange);


        List<PtPassengerInfo> passengerInfoArrayList = new ArrayList<>();
        Map<String, List<PassengerInfo>> groupPassenger = toGroupPassenger(passengerInfos, orderChangeBookingReq.getInterFlag(), orderChangeBookingReq.getFlightInfoComb());
        for (Map.Entry<String, List<PassengerInfo>> entry : groupPassenger.entrySet()) {
            String passengerType = entry.getKey();
            List<PassengerInfo> passengerInfoList = entry.getValue();
            Map<String, List<?>> segPriceMap = toOrderChangeSegmentInfoList(orderChangeBookingReq.getFlightInfoComb(), passengerType, passengerInfoList, orderChangeBookingReq.getInterFlag());
            for (PassengerInfo passengerInfo : passengerInfoList) {
                if (passengerInfo.getCertType().equalsIgnoreCase("PP") && !passengerInfo.getCertNo().matches(PatternCommon.PASSPORT_NO)) {
                    throw new OperationFailedException("护照号不正确");
                }
                List<PtPSPriceRel> priceList = new ArrayList<>();//该乘客的 运价 航段对应关系
                List<PtPSPriceRel> curPriceRelList = (List<PtPSPriceRel>) segPriceMap.get(LIST_PSP);//该类乘客的 乘客 运价 航段对应关系
                for (PtPSPriceRel ptPriceRel : curPriceRelList) {// 乘客对应所有价格
                    if (ptPriceRel.getPassengerNO() == passengerInfo.getPassengerNO()) {
                        priceList.add(ptPriceRel);
                    }
                }
                List<PtPrice> curAllPriceList = (List<PtPrice>) segPriceMap.get(LIST_PRICE);//该类乘客的运价
                // 计算价格
                double ticketPrice = 0.0;
                double pricePaid = 0.0;
                double yqTax = 0.0;
                double cnTax = 0.0;
                BigDecimal otherTax = BigDecimal.ZERO;
                List<InternatTaxInfo> otherTaxInfos = new ArrayList<>();
                // 价格序号
                int[] priceNos = priceList.stream().mapToInt(PtPSPriceRel::getPriceNO).distinct().toArray();
                for (int priceNo : priceNos) {
                    for (PtPrice ptPrice : curAllPriceList) {
                        if (priceNo == ptPrice.getPriceNO()) {
                            ticketPrice += ptPrice.getRSP();
                            pricePaid += ptPrice.getPriceValue();
                            yqTax += ptPrice.getYQTax();
                            cnTax += ptPrice.getCNTax();
                            otherTax = otherTax.add(BigDecimal.valueOf(ptPrice.getOtherTax()));
                            if (CollectionUtils.isNotEmpty(ptPrice.getOtherTaxList())) {
                                otherTaxInfos.addAll(ptPrice.getOtherTaxList());
                            }
                        }
                    }
                }
                passengerInfo.setTicketPrice(ticketPrice);
                passengerInfo.setPricePaid(pricePaid);
                passengerInfo.setyQTax(yqTax);
                passengerInfo.setcNTax(cnTax);
                passengerInfo.setOtherTax(otherTax.doubleValue());
                passengerInfo.setOtherTaxList(otherTaxInfos);
                PtPassengerInfo ptPassengerInfo = new PtPassengerInfo();
                BeanUtils.copyProperties(passengerInfo, ptPassengerInfo);
                passengerInfoArrayList.add(ptPassengerInfo);
            }

            ptOrderChangeConfirmRequest.setSegmentInfoList((List<com.juneyaoair.thirdentity.request.booking.PtSegmentInfo>) segPriceMap.get(LIST_SEG));
            ptOrderChangeConfirmRequest.setPriceInfoList((List<PtPrice>) segPriceMap.get(LIST_PRICE));
            ptOrderChangeConfirmRequest.setPSPriceRelList((List<PtPSPriceRel>) segPriceMap.get(LIST_PSP));

        }
        ptOrderChangeConfirmRequest.setPassengerInfoList(passengerInfoArrayList);
        ptOrderChangeConfirmRequest.setPayAmount(0.0);
        ptOrderChangeConfirmRequest.setChannelCustomerNo(orderChangeBookingReq.getFfpId());
        return ptOrderChangeConfirmRequest;
    }


    private static Map<String, List<PassengerInfo>> toGroupPassenger(List<PassengerInfo> passengerInfoList, String interFlag, FlightInfoComb flightInfoComb) {
        Map<String, List<PassengerInfo>> mapTicketGroup = new HashMap<>();
        if (HandlerConstants.FLIGHT_INTER_I.equals(interFlag)) {
            mapTicketGroup.put(HandlerConstants.PASSENGER_TYPE_ADT, passengerInfoList);
            return mapTicketGroup;
        }
        List<PassengerInfo> chdList = new ArrayList<>();
        List<PassengerInfo> adtList = new ArrayList<>();
        List<PassengerInfo> infList = new ArrayList<>();
        List<PassengerInfo> gmjcList = new ArrayList<>();
        for (PassengerInfo pass : passengerInfoList) {
            if (HandlerConstants.PASSENGER_TYPE_CHD.equals(pass.getPassengerType())) {
                chdList.add(pass);
            } else if (HandlerConstants.PASSENGER_TYPE_ADT.equals(pass.getPassengerType())) {
                if ("GM".equals(pass.getCertType()) || "JC".equals(pass.getCertType())) {
                    gmjcList.add(pass);
                } else {
                    adtList.add(pass);
                }
            } else if (HandlerConstants.PASSENGER_TYPE_GMJC.equals(pass.getPassengerType())) {
                gmjcList.add(pass);
            } else if (HandlerConstants.PASSENGER_TYPE_INF.equals(pass.getPassengerType())) {
                infList.add(pass);
            }
        }
        //成人与婴儿分组合并
        if (CollectionUtils.isNotEmpty(adtList)) {
            adtList.addAll(infList);
        } else {
            gmjcList.addAll(infList);
        }
        if (HandlerConstants.FLIGHT_INTER_D.equals(interFlag) && CollectionUtils.isEmpty(chdList)) {
            //无儿童直接返回
            if (CollectionUtils.isNotEmpty(adtList)) {
                mapTicketGroup.put(HandlerConstants.PASSENGER_TYPE_ADT, adtList);
            }
            if (CollectionUtils.isNotEmpty(gmjcList)) {
                mapTicketGroup.put(HandlerConstants.PASSENGER_TYPE_GMJC, gmjcList);
            }
            return mapTicketGroup;
        }
        Optional<com.juneyaoair.baseclass.response.av.CabinFare> chdCabinFare =
                flightInfoComb.getChdCabinFareList().stream()
                        .filter(cabinFare -> HandlerConstants.PASSENGER_TYPE_CHD.equals(cabinFare.getPassengerType())).findFirst();
        if (!chdCabinFare.isPresent()) {
            throw new OperationFailedException("未查询到儿童票价");
        }
        //成人与儿童同舱
        com.juneyaoair.baseclass.response.av.CabinFare adtCabinFare = flightInfoComb.getAdtCabinFareList().get(0);
        if (StringUtils.isNotBlank(chdCabinFare.get().getCabinCode()) && chdCabinFare.get().getCabinCode().equals(adtCabinFare.getCabinCode())) {
            mapTicketGroup.put(HandlerConstants.PASSENGER_TYPE_ADT, passengerInfoList);// //因为成人、儿童与婴儿航段相同，使用成人类型取相同航段
            return mapTicketGroup;
        }
        if (StringUtils.isNotBlank(chdCabinFare.get().getCabinComb()) && chdCabinFare.get().getCabinComb().equals(adtCabinFare.getCabinComb())) {
            mapTicketGroup.put(HandlerConstants.PASSENGER_TYPE_ADT, passengerInfoList);// //因为成人、儿童与婴儿航段相同，使用成人类型取相同航段
            return mapTicketGroup;
        }
        // 不同情况分别取
        if (CollectionUtils.isNotEmpty(adtList)) {
            mapTicketGroup.put(HandlerConstants.PASSENGER_TYPE_ADT, adtList);// 因为成人与婴儿航段相同，使用成人类型取相同航段
        }
        if (CollectionUtils.isNotEmpty(chdList)) {
            mapTicketGroup.put(HandlerConstants.PASSENGER_TYPE_CHD, chdList);
        }
        if (CollectionUtils.isNotEmpty(gmjcList)) {
            mapTicketGroup.put(HandlerConstants.PASSENGER_TYPE_GMJC, gmjcList);
        }
        return mapTicketGroup;
    }


    /**
     * 生成优惠券使用关系
     *
     * @param usePassengerSegments
     * @param segmentInfos
     * @param prices
     * @param passList
     * @return
     */
    private static List<CouponRelationShip> genUseCouponRel(List<UsePassengerSegment> usePassengerSegments, List<PtPSPriceRel> rels,
                                                            List<com.juneyaoair.thirdentity.request.booking.PtSegmentInfo> segmentInfos, List<PtPrice> prices, List<PtPassengerInfo> passList) {
        List<CouponRelationShip> couponRelationShips = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(usePassengerSegments)) {
            for (UsePassengerSegment usePassengerSegment : usePassengerSegments) {
                for (com.juneyaoair.thirdentity.request.booking.PtSegmentInfo segmentInfo : segmentInfos) {
                    for (PtPassengerInfo passengerInfo : passList) {
                        if (usePassengerSegment.getFlightInfo().getDepCity().equals(segmentInfo.getDepCity())
                                && usePassengerSegment.getFlightInfo().getArrCity().equals(segmentInfo.getArrCity())
                                && usePassengerSegment.getPassengerInfo().getPassengerID() == passengerInfo.getPassengerID()) {
                            CouponRelationShip couponRelationShip = new CouponRelationShip(
                                    passengerInfo.getPassengerNO(),
                                    segmentInfo.getSegNO(),
                                    usePassengerSegment.getCouponCode(),
                                    null
                            );
                            couponRelationShips.add(couponRelationShip);
                            break;
                        }
                    }
                }
            }
        }
        ListIterator<CouponRelationShip> it = couponRelationShips.listIterator();
        while (it.hasNext()) {
            CouponRelationShip couponRelationShip = it.next();
            PtPSPriceRel rel = rels.stream().filter(e -> e.getPassengerNO() == couponRelationShip.getPassengerNO()
                    && e.getSegNO() == couponRelationShip.getSegNo()).findFirst().orElse(null);
            if (rel != null) {
                PtPrice price = prices.stream().filter(e -> e.getPriceNO() == rel.getPriceNO()).findFirst().orElse(null);
                couponRelationShip.setPriceValue(BigDecimal.valueOf(price == null ? 0 : price.getChangeServiceCharge()));
            } else {
                // 成人和儿童/婴儿同时改期时，存在segment重复的情况，未存在rel的时候删除
                it.remove();
            }
        }
        return couponRelationShips;
    }

    /**
     * 非自愿改期订单运价取原航段运价
     *
     * @param oldCabinFares
     * @param newCabinFares
     */
    private static void copyNotVoluntaryChangeCabinFare(List<CabinFare> oldCabinFares, List<CabinFare> newCabinFares) {
        if (CollectionUtils.isEmpty(oldCabinFares) || CollectionUtils.isEmpty(newCabinFares)) {
            return;
        }
        for (CabinFare oldCabinFare : oldCabinFares) {
            for (CabinFare newCabinFare : newCabinFares) {
                if (newCabinFare.getCabinCode().equals(oldCabinFare.getCabinCode())) {
                    newCabinFare.setPriceValue(oldCabinFare.getPriceValue());
                    newCabinFare.setRSP(oldCabinFare.getRSP());
                    newCabinFare.setYQTax(oldCabinFare.getYQTax());
                    newCabinFare.setCNTax(oldCabinFare.getCNTax());
                }
            }
        }
    }


    /**
     * 非自愿改期订单运价取原航段运价
     *
     * @param oldCabinFares
     * @param newCabinFares
     */
    private static void copyNotVoluntaryChangeCabinFareI(List<CabinFare> oldCabinFares, List<com.juneyaoair.baseclass.response.av.CabinFare> newCabinFares) {
        if (CollectionUtils.isEmpty(oldCabinFares) || CollectionUtils.isEmpty(newCabinFares)) {
            return;
        }
        for (CabinFare oldCabinFare : oldCabinFares) {
            for (com.juneyaoair.baseclass.response.av.CabinFare newCabinFare : newCabinFares) {
                if (newCabinFare.getCabinCode().equals(oldCabinFare.getCabinCode())) {
                    newCabinFare.setPriceValue(oldCabinFare.getPriceValue());
                    newCabinFare.setRSP(oldCabinFare.getRSP());
                    newCabinFare.setYQTax(oldCabinFare.getYQTax());
                    newCabinFare.setCNTax(oldCabinFare.getCNTax());
                }
            }
        }
    }

    /**
     * 转换航班预定的航班结构
     *
     * @param passMap
     * @return
     */
    private static List<FlightInfo> toOldFlightInfoList(Map<String, List<ChangePassengerInfo>> passMap) {
        List<FlightInfo> flightInfoList = new ArrayList<>();
        boolean isFirst = true;
        for (Map.Entry<String, List<ChangePassengerInfo>> entry : passMap.entrySet()) {
            String passType = entry.getKey();
            //每种类型的只需一个即可
            ChangePassengerInfo changePassengerInfo = entry.getValue().get(0);
            int i = 0;
            for (SegmentPriceInfo segmentPriceInfo : changePassengerInfo.getSegmentPriceInfoList()) {
                FlightInfo flightInfo;
                //第一个乘客
                if (isFirst) {
                    flightInfo = new FlightInfo();
                    BeanUtils.copyProperties(segmentPriceInfo, flightInfo);
                    flightInfo.setFType(segmentPriceInfo.getPlaneStyle());
                    flightInfoList.add(flightInfo);
                } else {
                    flightInfo = flightInfoList.get(i++);
                }
                CabinFare cabinFare = new CabinFare();
                BeanUtils.copyProperties(segmentPriceInfo, cabinFare);
                cabinFare.setPassengerType(passType);
                cabinFare.setCabinCode(segmentPriceInfo.getCabin());
                cabinFare.setPriceValue(segmentPriceInfo.getPricePaid() + segmentPriceInfo.getUseScore() + segmentPriceInfo.getCouponAmount());
                cabinFare.setRSP(segmentPriceInfo.getPricePaid() + segmentPriceInfo.getUseScore() + segmentPriceInfo.getCouponAmount());
                List<CabinFare> cabinFareList = new ArrayList<>();
                cabinFareList.add(cabinFare);
                List<CabinFare> chdInfCabinFareList = flightInfo.getCabinCHDINFFareList();
                if (HandlerConstants.PASSENGER_TYPE_ADT.equals(passType)) {
                    flightInfo.setCabinFareList(cabinFareList);
                } else if (HandlerConstants.PASSENGER_TYPE_GMJC.equals(passType)) {
                    flightInfo.setCabinGMJCFareList(cabinFareList);
                } else if (HandlerConstants.PASSENGER_TYPE_CHD.equals(passType) || HandlerConstants.PASSENGER_TYPE_INF.equals(passType)) {
                    if (StringUtil.isNullOrEmpty(chdInfCabinFareList)) {
                        flightInfo.setCabinCHDINFFareList(cabinFareList);
                    } else {
                        chdInfCabinFareList.addAll(cabinFareList);
                    }
                }
            }
            isFirst = false;

        }
        return flightInfoList;
    }

    /**
     * 组装客票确认改期订单请求参数
     *
     * @param channelCode
     * @param userNo
     * @return
     */
    public static PtTicketBookingReq toTicketChangeConfirmReq(String channelCode, String userNo, TicketChangeBookingReq ticketChangeBookingReq, HandConfig handConfig) {
        List<ChangePassengerInfo> passengerInfoList = ticketChangeBookingReq.getPassengerInfoList();
        ChangePassengerInfo passengerInfo = passengerInfoList.get(0);
        String pnr = ticketChangeBookingReq.getPnrNo();
        String passType = passengerInfo.getPassengerType();
        //公用信息
        PtTicketBookingReq ptTicketBookingReq = new PtTicketBookingReq(HandlerConstants.VERSION, channelCode, userNo, HandlerConstants.CURRENCY_CODE, HandlerConstants.LANG_CODE);
        ptTicketBookingReq.setChannelCode(channelCode);
        ptTicketBookingReq.setChannelOrderNo(ticketChangeBookingReq.getChannelOrderNo());
        ptTicketBookingReq.setChannelCustomerNo(ticketChangeBookingReq.getFfpId());
        ptTicketBookingReq.setRouteType(ticketChangeBookingReq.getRouteType());
        ptTicketBookingReq.setInterFlag(ticketChangeBookingReq.getInterFlag());
        ptTicketBookingReq.setCurrencyCode(HandlerConstants.CURRENCY_CODE);
        ptTicketBookingReq.setLangCode(HandlerConstants.LANG_CODE);
        ptTicketBookingReq.setTicketOrderSort("Change");
        ptTicketBookingReq.setTicketOrderType("Person");
        List<PtTicketOrderInfo> formerOrderList = new ArrayList<>();
        if (HandlerConstants.FLIGHT_INTER_D.equals(ticketChangeBookingReq.getInterFlag())) {
            //机票子订单，第一个为客票的原有航段信息
            formerOrderList = ToTicketOrderInfoList(ticketChangeBookingReq, passType, pnr, "NORMAL");//机票订单信息列表
        } else {
            formerOrderList = ToTicketInterOrderInfoList(ticketChangeBookingReq, passType, pnr, "NORMAL");
        }
        if (formerOrderList.size() == 1) {
            ticketChangeBookingReq.setRouteType("OW");
            ptTicketBookingReq.setRouteType("OW");
        }
        ptTicketBookingReq.setFormerTicketOrderInfoList(formerOrderList);
        //机票子订单，第二个为客票的新有航段信息
        List<PtTicketOrderInfo> upTicketOrderInfoList = ToTicketOrderInfoChangeList(formerOrderList, ticketChangeBookingReq, passType,
                pnr, "CHANGE", handConfig);//机票订单信息列表
        ptTicketBookingReq.setTicketOrderInfoList(upTicketOrderInfoList);
        Integer useScoreTotal = 0;
        for (PtTicketOrderInfo ptTicketOrderInfo : upTicketOrderInfoList) {
            useScoreTotal = useScoreTotal + ptTicketOrderInfo.getUseScoreTotal();
        }
        ptTicketBookingReq.setUseScoreTotal(useScoreTotal);
        double totalDiff = 0.0;
        for (PtTicketOrderInfo ptTicketOrderInfo : upTicketOrderInfoList) {
            totalDiff += ptTicketOrderInfo.getPriceDiff();
        }
        ptTicketBookingReq.setPayAmount(totalDiff);
        return ptTicketBookingReq;
    }

    /**
     * 客票生成机票子订单列表(原客票信息)
     *
     * @param ticketChangeBookingReq
     * @param passType
     * @param pnr
     * @param orderType              NORMAL
     * @return
     */
    public static List<PtTicketOrderInfo> ToTicketOrderInfoList(TicketChangeBookingReq ticketChangeBookingReq, String passType, String pnr, String orderType) {
        int i = 0;// 机票子订单序号
        List<PtTicketOrderInfo> tOrderList = new ArrayList<>();
        List<PassengerInfo> passengerInfoList = toPassList(ticketChangeBookingReq.getPassengerInfoList());
        //按pnr分组 舱位
        Map<String, List<PassengerInfo>> dicTicketGroup = ticketChangeGroupPassenger(passengerInfoList, ticketChangeBookingReq.getFlightInfoList(), ticketChangeBookingReq.getInterFlag());// 子订单分组乘客  ADT CHD GMJC

        for (Map.Entry<String, List<PassengerInfo>> entry : dicTicketGroup.entrySet()) {
            List<PassengerInfo> passPnrGroupList = entry.getValue();
            PtTicketOrderInfo tickOrder = toTicketOrderInfo(ticketChangeBookingReq, i++, passType, orderType);
            tickOrder.setPnr(passPnrGroupList.get(0).getPnrNo());

            Map<String, List<PassengerInfo>> passengerInfoMap = passPnrGroupList.stream()
                    .collect(Collectors.groupingBy(PassengerInfo::getPassengerType));
            for (Map.Entry<String, List<PassengerInfo>> passengerMap : passengerInfoMap.entrySet()) {

                List<PassengerInfo> passGroupList = passengerMap.getValue();
                passType = passGroupList.get(0).getPassengerType();
                List<FlightInfo> oldFlightInfoList = ticketChangeBookingReq.getOldFlightInfoList().stream().filter(FlightInfo::isSelectFlag).collect(Collectors.toList());
                Map<String, List<?>> segPriceMap = ToSegmentInfoList(oldFlightInfoList, passType, passGroupList);
                for (PassengerInfo passengerInfo : passGroupList) {
                    List<PtPSPriceRel> priceList = new ArrayList<>();
                    List<PtPSPriceRel> curPriceRelList = (List<PtPSPriceRel>) segPriceMap
                            .get("listPSP");//乘客 运价 航段对应关系
                    for (PtPSPriceRel ptPriceRel : curPriceRelList) {// 乘客对应所有价格
                        if (ptPriceRel.getPassengerNO() == passengerInfo.getPassengerNO()) {
                            priceList.add(ptPriceRel);
                        }
                    }
                    List<PtPrice> curAllPriceList = (List<PtPrice>) segPriceMap.get("listPrice");
                    // 计算价格
                    double ticketPrice = 0.0, pricePaid = 0.0, YQTax = 0.0, CNTax = 0.0;
                    for (PtPSPriceRel ptPSPriceRel : priceList) {
                        for (PtPrice ptPrice : curAllPriceList) {
                            if (ptPSPriceRel.getPriceNO() == ptPrice.getPriceNO()) {
                                ticketPrice += ptPrice.getRSP();
                                pricePaid += ptPrice.getPriceValue();
                                if (HandlerConstants.FLIGHT_INTER_D.equals(ticketChangeBookingReq.getInterFlag())) {// 国内机建燃油费
                                    YQTax += ptPrice.getYQTax();
                                    CNTax += ptPrice.getCNTax();
                                }
                            }
                        }
                    }
                    passengerInfo.setTicketPrice(ticketPrice);
                    passengerInfo.setPricePaid(pricePaid);
                    if (HandlerConstants.FLIGHT_INTER_D.equals(ticketChangeBookingReq.getInterFlag())) {
                        passengerInfo.setyQTax(YQTax);
                        passengerInfo.setcNTax(CNTax);
                    }
                }
                tickOrder = addPriceInfo(tickOrder, segPriceMap, 0, passGroupList);
                tickOrder.setPnr(pnr);
                tickOrder.setUseScoreTotal(0);
            }
            tickOrder = handlerTickOrder(tickOrder);
            tOrderList.add(tickOrder);
        }

        return tOrderList;
    }


    /**
     * 国际客票生成机票子订单列表(原客票信息)
     *
     * @param ticketChangeBookingReq
     * @param passType
     * @param pnr
     * @param orderType              NORMAL
     * @return
     */
    public static List<PtTicketOrderInfo> ToTicketInterOrderInfoList(TicketChangeBookingReq ticketChangeBookingReq, String passType, String pnr, String orderType) {
        int i = 0;// 机票子订单序号
        List<PtTicketOrderInfo> tOrderList = new ArrayList<>();
        //乘客基本复制
        List<PassengerInfo> passengerInfos = toPassList(ticketChangeBookingReq.getPassengerInfoList());
        Map<String, List<PassengerInfo>> groupPassenger = toGroupPassenger(passengerInfos, ticketChangeBookingReq.getInterFlag(), ticketChangeBookingReq.getFlightInfoComb());
        for (Map.Entry<String, List<PassengerInfo>> entry : groupPassenger.entrySet()) {
            List<PassengerInfo> passengerInfoList = entry.getValue();
            PtTicketOrderInfo tickOrder = toTicketOrderInfo(ticketChangeBookingReq, i++, passType, orderType);
            tickOrder.setPnr(passengerInfoList.get(0).getPnrNo());
            passType = entry.getKey();
            List<FlightInfo> oldFlightInfoList = ticketChangeBookingReq.getOldFlightInfoList().stream().filter(FlightInfo::isSelectFlag).collect(Collectors.toList());
            Map<String, List<?>> segPriceMap = ToSegmentInfoList(oldFlightInfoList, passType, passengerInfoList);
            for (PassengerInfo passengerInfo : passengerInfoList) {
                if (passengerInfo.getCertType().equalsIgnoreCase("PP") && !passengerInfo.getCertNo().matches(PatternCommon.PASSPORT_NO)) {
                    throw new OperationFailedException("护照号不正确");
                }
                List<PtPSPriceRel> priceList = new ArrayList<>();
                List<PtPSPriceRel> curPriceRelList = (List<PtPSPriceRel>) segPriceMap
                        .get("listPSP");//乘客 运价 航段对应关系
                for (PtPSPriceRel ptPriceRel : curPriceRelList) {// 乘客对应所有价格
                    if (ptPriceRel.getPassengerNO() == passengerInfo.getPassengerNO()) {
                        priceList.add(ptPriceRel);
                    }
                }
                List<PtPrice> curAllPriceList = (List<PtPrice>) segPriceMap.get("listPrice");
                List<InternatTaxInfo> otherTaxInfos = new ArrayList<>();
                // 计算价格
                double ticketPrice = 0.0, pricePaid = 0.0, YQTax = 0.0, CNTax = 0.0;
                for (PtPSPriceRel ptPSPriceRel : priceList) {
                    for (PtPrice ptPrice : curAllPriceList) {
                        if (ptPSPriceRel.getPriceNO() == ptPrice.getPriceNO()) {
                            ticketPrice += ptPrice.getRSP();
                            pricePaid += ptPrice.getPriceValue();
                        }
                    }
                }
                if (CollectionUtils.isNotEmpty(ticketChangeBookingReq.getOldFareTaxInfoList())) {
                    ticketChangeBookingReq.getOldFareTaxInfoList().stream().forEach(fareTaxInfo -> {
                        if (passengerInfo.getPassengerType().equals(fareTaxInfo.getPassengerType())) {
                            fareTaxInfo.getTaxInfoList().stream().forEach(taxInfo -> {
                                InternatTaxInfo info = new InternatTaxInfo();
                                BeanUtils.copyProperties(taxInfo, info);
                                otherTaxInfos.add(info);
                            });

                        }
                    });
                }
                passengerInfo.setOtherTaxList(otherTaxInfos);
                passengerInfo.setTicketPrice(ticketPrice);
                passengerInfo.setPricePaid(pricePaid);
            }
            tickOrder = addPriceInfo(tickOrder, segPriceMap, 0, passengerInfoList);
            tickOrder.setPnr(pnr);
            tickOrder.setUseScoreTotal(0);
            tOrderList.add(tickOrder);

        }
        return tOrderList;
    }


    /**
     * 客票生成改期机票子订单
     *
     * @param ticketChangeBookingReq
     * @param passType
     * @param pnr
     * @param orderType
     * @return
     */
    public static List<PtTicketOrderInfo> ToTicketOrderInfoChangeList(List<PtTicketOrderInfo> oldTicketOrderList,
                                                                      TicketChangeBookingReq ticketChangeBookingReq, String passType, String pnr, String orderType, HandConfig config) {
        //改期子订单处理
        int i = 0; // 机票子订单序号 在原有子订单的基础上累计
        List<PtTicketOrderInfo> tOrderList = new ArrayList<>();
        List<PassengerInfo> passengerInfoList = toPassList(ticketChangeBookingReq.getPassengerInfoList());
        Map<String, List<PassengerInfo>> dicTicketGroup = new HashMap<>();
        if (HandlerConstants.FLIGHT_INTER_D.equals(ticketChangeBookingReq.getInterFlag())) {
            dicTicketGroup = ticketChangeGroupPassenger(passengerInfoList, ticketChangeBookingReq.getFlightInfoList(), ticketChangeBookingReq.getInterFlag());
        } else {
            dicTicketGroup = toGroupPassenger(passengerInfoList, ticketChangeBookingReq.getInterFlag(), ticketChangeBookingReq.getFlightInfoComb());
        }
        //子订单的分类
        for (Map.Entry<String, List<PassengerInfo>> entry : dicTicketGroup.entrySet()) {
            List<PassengerInfo> passPnrGroupList = entry.getValue();
            PtTicketOrderInfo tickOrder = toTicketOrderInfo(ticketChangeBookingReq, i++, passType, orderType);
            tickOrder.setPnr(passPnrGroupList.get(0).getPnrNo());

            //根据乘客类型分组
            Map<String, List<PassengerInfo>> passengerInfoMap = passPnrGroupList.stream()
                    .collect(Collectors.groupingBy(PassengerInfo::getPassengerType));
            int t = 0;
            for (Map.Entry<String, List<PassengerInfo>> passengerMap : passengerInfoMap.entrySet()) {
                List<PassengerInfo> passGroupList = passengerMap.getValue();
                List<FlightInfo> newFlightInfoList = new ArrayList<>();
                passType = passGroupList.get(0).getPassengerType();
                List<FlightInfo> oldFlightInfoList = ticketChangeBookingReq.getOldFlightInfoList().stream().filter(FlightInfo::isSelectFlag).collect(Collectors.toList());
                //匹配改期航段
                for (FlightInfo oldFlightInfo : oldFlightInfoList) {
                    FlightInfo newFlightInfo = new FlightInfo();
                    BeanUtils.copyProperties(oldFlightInfo, newFlightInfo);
                    newFlightInfo.setSelectFlag(false);
                    if (CollectionUtils.isNotEmpty(ticketChangeBookingReq.getFlightInfoList())) {
                        for (FlightInfo flightInfo : ticketChangeBookingReq.getFlightInfoList()) {
                            //说明是改期航段
                            if (oldFlightInfo.getDepCity().equals(flightInfo.getDepCity()) && oldFlightInfo.getArrCity().equals(flightInfo.getArrCity())) {
                                BeanUtils.copyProperties(flightInfo, newFlightInfo);
                                newFlightInfo.setSelectFlag(true);
                                if (newFlightInfo.isNotVoluntaryChange()) {
                                    copyNotVoluntaryChangeCabinFare(oldFlightInfo.getCabinFareList(), newFlightInfo.getCabinFareList());
                                    copyNotVoluntaryChangeCabinFare(oldFlightInfo.getCabinCHDINFFareList(), newFlightInfo.getCabinCHDINFFareList());
                                    copyNotVoluntaryChangeCabinFare(oldFlightInfo.getCabinGMJCFareList(), newFlightInfo.getCabinGMJCFareList());
                                }
                                break;
                            }
                        }
                    }

                    newFlightInfoList.add(newFlightInfo);
                }

                Map<String, List<?>> segPriceMap = new HashMap<>();
                if (HandlerConstants.FLIGHT_INTER_D.equals(ticketChangeBookingReq.getInterFlag())) {
                    segPriceMap = ToSegmentInfoUpList(newFlightInfoList, passType, passGroupList,
                            ticketChangeBookingReq.getUsePassengerSegments(), config);
                } else {
                    segPriceMap = toOrderChangeSegmentInfoList(ticketChangeBookingReq.getFlightInfoComb(), passType, passengerInfoList, ticketChangeBookingReq.getInterFlag());
                }
                double ticketOrderPriceDiff = 0.0;  //当前子订单差价
                for (PassengerInfo passengerInfo : passGroupList) {
                    List<PtPSPriceRel> priceList = new ArrayList<>();
                    //乘客 运价 航段对应关系
                    List<PtPSPriceRel> curPriceRelList = (List<PtPSPriceRel>) segPriceMap.get("listPSP");
                    for (PtPSPriceRel ptPriceRel : curPriceRelList) {// 乘客对应所有价格
                        if (ptPriceRel.getPassengerNO() == passengerInfo.getPassengerNO()) {
                            priceList.add(ptPriceRel);
                        }
                    }
                    List<PtPrice> curAllPriceList = (List<PtPrice>) segPriceMap.get("listPrice");
                    List<InternatTaxInfo> otherTaxInfos = new ArrayList<>();
                    // 计算价格
                    double ticketPrice = 0.0, pricePaid = 0.0, YQTax = 0.0, CNTax = 0.0;
                    //总的差价
                    double passTotalDiffPrice = 0.0;
                    int[] priceNos = priceList.stream().mapToInt(PtPSPriceRel::getPriceNO).distinct().toArray();
                    for (int priceNo : priceNos) {
                        for (PtPrice ptPrice : curAllPriceList) {
                            if (priceNo == ptPrice.getPriceNO()) {
                                ticketPrice += ptPrice.getRSP();
                                pricePaid += ptPrice.getPriceValue();
                                passTotalDiffPrice += ptPrice.getTotalDiff();
                                if (HandlerConstants.FLIGHT_INTER_D.equals(ticketChangeBookingReq.getInterFlag())) {// 国内机建燃油费
                                    YQTax += ptPrice.getYQTax();
                                    CNTax += ptPrice.getCNTax();
                                }
                                if (CollectionUtils.isNotEmpty(ptPrice.getOtherTaxList())) {
                                    otherTaxInfos.addAll(ptPrice.getOtherTaxList());
                                }
                            }
                        }
                    }
                    passengerInfo.setOtherTaxList(otherTaxInfos);
                    passengerInfo.setTicketPrice(ticketPrice);
                    passengerInfo.setPricePaid(pricePaid);
                    passengerInfo.setTotalDiff(passTotalDiffPrice);
                    if (HandlerConstants.FLIGHT_INTER_D.equals(ticketChangeBookingReq.getInterFlag())) {
                        passengerInfo.setyQTax(YQTax);
                        passengerInfo.setcNTax(CNTax);
                    }
                    //儿童将卡号设置为生日
//                    if (passengerInfo.getPassengerType().equals(HandlerConstants.PASSENGER_TYPE_INF)) {
//                        passengerInfo.setCertNo(passengerInfo.getBirthdate().replace("-", ""));
//                        passengerInfo.setCertType(CertificateTypeEnum.UNKNOW.getShowCode());
//                    }
                    passengerInfo.setTicketNo("");
                    ticketOrderPriceDiff += passTotalDiffPrice;
                }
                if (HandlerConstants.FLIGHT_INTER_D.equals(ticketChangeBookingReq.getInterFlag())) {
                    //设置乘客价格信息
                    tickOrder = addPriceInfo(tickOrder, segPriceMap, ticketOrderPriceDiff, passGroupList);
                } else {
                    if (t < 1) {
                        tickOrder = addIPriceInfo(tickOrder, segPriceMap, ticketOrderPriceDiff, passengerInfoList);
                        ++t;
                    }

                }

                //表示差额费用
                tickOrder.setUseScoreTotal(0);
                //设置价格信息
                List<CouponRelationShip> useCouponRel = genUseCouponRel(ticketChangeBookingReq.getUsePassengerSegments(),
                        tickOrder.getPSPriceRelList(), tickOrder.getSegmentInfoList(), tickOrder.getPriceInfoList(), tickOrder.getPassengerInfoList());
                if (CollectionUtils.isNotEmpty(useCouponRel)) {
                    tickOrder.setCouponRelationShip(useCouponRel);
                    for (CouponRelationShip couponRelationShip : useCouponRel) {
                        if (BigDecimal.ZERO.equals(couponRelationShip.getPriceValue())) {
                            throw new OperationFailedException("改期券抵扣金额计算有误");
                        }
                        //表示差额费用
                        tickOrder.setUseScoreTotal(tickOrder.getUseScoreTotal() + couponRelationShip.getPriceValue().intValue());
                    }
                }
            }
            if (HandlerConstants.FLIGHT_INTER_D.equals(ticketChangeBookingReq.getInterFlag())) {
                tickOrder = handlerTickOrder(tickOrder);
            }
            //转换类:

            tOrderList.add(tickOrder);
        }
        return tOrderList;
    }

    private static PtTicketOrderInfo handlerTickOrder(PtTicketOrderInfo tickOrder) {
        List<com.juneyaoair.thirdentity.request.booking.PtSegmentInfo> segmentInfoList = tickOrder.getSegmentInfoList();
        Map<String, List<com.juneyaoair.thirdentity.request.booking.PtSegmentInfo>> directionMap = segmentInfoList.stream()
                .collect(Collectors.groupingBy(com.juneyaoair.thirdentity.request.booking.PtSegmentInfo::getFlightDirection));
        Map<Integer, Integer> gBNumberMap = new HashMap<>();
        Map<Integer, com.juneyaoair.thirdentity.request.booking.PtSegmentInfo> segmentInfoListNewMap = new HashMap<>();
        //1.获取到所有的航段信息
        for (com.juneyaoair.thirdentity.request.booking.PtSegmentInfo ptSegmentInfo : segmentInfoList) {
            List<com.juneyaoair.thirdentity.request.booking.PtSegmentInfo> ptSegmentInfos = directionMap.get(ptSegmentInfo.getFlightDirection());
            ptSegmentInfos = ptSegmentInfos.stream().filter(o -> !o.getPassengerType().equals("INF")).collect(Collectors.toList());
            com.juneyaoair.thirdentity.request.booking.PtSegmentInfo ptSegmentInfoNew = ptSegmentInfos.get(0);
            int segNO = ptSegmentInfoNew.getSegNO();
            segmentInfoListNewMap.put(segNO, ptSegmentInfoNew);
            gBNumberMap.put(ptSegmentInfo.getSegNO(), segNO);
        }

        List<PtPSPriceRel> psPriceRelList = tickOrder.getPSPriceRelList();
        for (PtPSPriceRel ptPSPriceRel : psPriceRelList) {
            ptPSPriceRel.setSegNO(gBNumberMap.get(ptPSPriceRel.getSegNO()));
        }
        List<CouponRelationShip> couponRelationShipList = tickOrder.getCouponRelationShip();
        if (!StringUtil.isNullOrEmpty(couponRelationShipList)) {
            for (CouponRelationShip couponRelationShip : couponRelationShipList) {
                couponRelationShip.setSegNo(gBNumberMap.get(couponRelationShip.getSegNo()));
            }
        }

        tickOrder.setSegmentInfoList(new ArrayList<>(segmentInfoListNewMap.values()));
        tickOrder.setPSPriceRelList(psPriceRelList);
        return tickOrder;
    }


    private static PtTicketOrderInfo handlerITickOrder(PtTicketOrderInfo tickOrder) {
        List<com.juneyaoair.thirdentity.request.booking.PtSegmentInfo> segmentInfoList = tickOrder.getSegmentInfoList();
        Map<String, List<com.juneyaoair.thirdentity.request.booking.PtSegmentInfo>> directionMap = segmentInfoList.stream()
                .collect(Collectors.groupingBy(com.juneyaoair.thirdentity.request.booking.PtSegmentInfo::getFlightDirection));
        Map<Integer, Integer> gBNumberMap = new HashMap<>();
        Map<Integer, com.juneyaoair.thirdentity.request.booking.PtSegmentInfo> segmentInfoListNewMap = new HashMap<>();
        //1.获取到所有的航段信息
        for (com.juneyaoair.thirdentity.request.booking.PtSegmentInfo ptSegmentInfo : segmentInfoList) {
            List<com.juneyaoair.thirdentity.request.booking.PtSegmentInfo> ptSegmentInfos = directionMap.get(ptSegmentInfo.getFlightDirection());
            ptSegmentInfos = ptSegmentInfos.stream().filter(o -> !o.getPassengerType().equals("INF")).collect(Collectors.toList());
            com.juneyaoair.thirdentity.request.booking.PtSegmentInfo ptSegmentInfoNew = ptSegmentInfos.get(0);
            int segNO = ptSegmentInfoNew.getSegNO();
            segmentInfoListNewMap.put(segNO, ptSegmentInfoNew);
            gBNumberMap.put(ptSegmentInfo.getSegNO(), segNO);
        }

        List<PtPSPriceRel> psPriceRelList = tickOrder.getPSPriceRelList();
        for (PtPSPriceRel ptPSPriceRel : psPriceRelList) {
            ptPSPriceRel.setSegNO(gBNumberMap.get(ptPSPriceRel.getSegNO()));
        }
        List<CouponRelationShip> couponRelationShipList = tickOrder.getCouponRelationShip();
        if (!StringUtil.isNullOrEmpty(couponRelationShipList)) {
            for (CouponRelationShip couponRelationShip : couponRelationShipList) {
                couponRelationShip.setSegNo(gBNumberMap.get(couponRelationShip.getSegNo()));
            }
        }

        tickOrder.setSegmentInfoList(new ArrayList<>(segmentInfoListNewMap.values()));
        tickOrder.setPSPriceRelList(psPriceRelList);
        return tickOrder;
    }

    /**
     * 将人航段价格信息添加
     *
     * @param ptTicketOrderInfo
     * @param segPriceMap
     * @param ticketOrderPriceDiff
     * @param passList
     * @return
     */
    private static PtTicketOrderInfo addPriceInfo(PtTicketOrderInfo ptTicketOrderInfo, Map<String, List<?>> segPriceMap, double ticketOrderPriceDiff, List<PassengerInfo> passList) {

        ptTicketOrderInfo.setPriceDiff(ptTicketOrderInfo.getPriceDiff() + ticketOrderPriceDiff);

        List<PtPassengerInfo> ptPassList = new ArrayList<PtPassengerInfo>();
        for (PassengerInfo pass : passList) {
            PtPassengerInfo ptPassengerInfo = new PtPassengerInfo();
            BeanUtils.copyProperties(pass, ptPassengerInfo);
            ptPassengerInfo.setPnr(pass.getPnrNo());
            ptPassengerInfo.setTicketNo(pass.getTicketNo());
            ptPassengerInfo.setInsuranceList(null);
            ptPassList.add(ptPassengerInfo);
        }
        ptTicketOrderInfo.getPassengerInfoList().addAll(ptPassList);

        ptTicketOrderInfo.getSegmentInfoList().addAll((List<com.juneyaoair.thirdentity.request.booking.PtSegmentInfo>) segPriceMap.get("listSeg"));
        ptTicketOrderInfo.getPriceInfoList().addAll((List<PtPrice>) segPriceMap.get("listPrice"));
        ptTicketOrderInfo.getPSPriceRelList().addAll((List<PtPSPriceRel>) segPriceMap.get("listPSP"));
        return ptTicketOrderInfo;
    }

    private static PtTicketOrderInfo addIPriceInfo(PtTicketOrderInfo ptTicketOrderInfo, Map<String, List<?>> segPriceMap, double ticketOrderPriceDiff, List<PassengerInfo> passList) {

        ptTicketOrderInfo.setPriceDiff(ptTicketOrderInfo.getPriceDiff() + ticketOrderPriceDiff);

        List<PtPassengerInfo> ptPassList = new ArrayList<PtPassengerInfo>();
        for (PassengerInfo pass : passList) {
            PtPassengerInfo ptPassengerInfo = new PtPassengerInfo();
            BeanUtils.copyProperties(pass, ptPassengerInfo);
            ptPassengerInfo.setPnr(pass.getPnrNo());
            ptPassengerInfo.setTicketNo(pass.getTicketNo());
            ptPassengerInfo.setInsuranceList(null);
            ptPassList.add(ptPassengerInfo);
        }
        ptTicketOrderInfo.setPassengerInfoList(ptPassList);
        List<com.juneyaoair.thirdentity.request.booking.PtSegmentInfo> ptSegmentInfos = (List<com.juneyaoair.thirdentity.request.booking.PtSegmentInfo>) segPriceMap.get("listSeg");
        ptTicketOrderInfo.setSegmentInfoList(ptSegmentInfos);
        ptTicketOrderInfo.setPriceInfoList((List<PtPrice>) segPriceMap.get("listPrice"));
        ptTicketOrderInfo.setPSPriceRelList((List<PtPSPriceRel>) segPriceMap.get("listPSP"));
        return ptTicketOrderInfo;
    }

    //乘客对象转换
    private static List<PassengerInfo> toPassList(List<ChangePassengerInfo> passengerInfoList) {
        List<PassengerInfo> passengerInfoArrayList = new ArrayList<>();
        for (ChangePassengerInfo changePassengerInfo : passengerInfoList) {
            PassengerInfo passengerInfo = new PassengerInfo();
            BeanUtils.copyNotNullProperties(changePassengerInfo, passengerInfo);
            if (StringUtils.isBlank(changePassengerInfo.getBirthdate())){
                if (CertificateTypeEnum.ID_CARD.getShowCode().equals(changePassengerInfo.getCertType())){
                    String birthDate = certNoToDate(changePassengerInfo.getCertNo());
                    passengerInfo.setBirthdate(birthDate);
                }
            }
            passengerInfoArrayList.add(passengerInfo);
        }
        return passengerInfoArrayList;
    }

    /**
     * 乘客类型分类
     *
     * @param passengerInfoList
     * @return
     */
    public static Map<String, List<ChangePassengerInfo>> toGroupPassengerType(List<ChangePassengerInfo> passengerInfoList) {
        Map<String, List<ChangePassengerInfo>> mapTicketGroup = new HashMap<>();
        List<ChangePassengerInfo> chdList = new ArrayList<>();
        List<ChangePassengerInfo> adtList = new ArrayList<>();
        List<ChangePassengerInfo> infList = new ArrayList<>();
        List<ChangePassengerInfo> gmjcList = new ArrayList<>();
        for (ChangePassengerInfo pass : passengerInfoList) {
            if (HandlerConstants.PASSENGER_TYPE_CHD.equals(pass.getPassengerType())) {
                chdList.add(pass);
            } else if (HandlerConstants.PASSENGER_TYPE_ADT.equals(pass.getPassengerType())) {
                if ("GM".equals(pass.getCertType()) || "JC".equals(pass.getCertType())) {
                    gmjcList.add(pass);
                } else {
                    adtList.add(pass);
                }
            } else if (HandlerConstants.PASSENGER_TYPE_GMJC.equals(pass.getPassengerType())) {
                gmjcList.add(pass);
            } else if (HandlerConstants.PASSENGER_TYPE_INF.equals(pass.getPassengerType())) {
                infList.add(pass);
            }
        }
        if (adtList.size() > 0) {
            mapTicketGroup.put(HandlerConstants.PASSENGER_TYPE_ADT, adtList);
        }
        if (chdList.size() > 0) {
            mapTicketGroup.put(HandlerConstants.PASSENGER_TYPE_CHD, chdList);
        }
        if (gmjcList.size() > 0) {
            mapTicketGroup.put(HandlerConstants.PASSENGER_TYPE_GMJC, gmjcList);
        }
        if (infList.size() > 0) {
            mapTicketGroup.put(HandlerConstants.PASSENGER_TYPE_INF, infList);
        }
        return mapTicketGroup;
    }

    /**
     * 机票子订单
     *
     * @param ticketChangeBookingReq 接口请求内容
     * @param orderNo
     * @param passengerType          乘客类型
     * @param orderType              NORMAL
     * @return 下单请求内容
     */
    private static PtTicketOrderInfo toTicketOrderInfo(TicketChangeBookingReq ticketChangeBookingReq, int orderNo, String passengerType, String orderType) {
        PtTicketOrderInfo ptTicketOrderInfo = new PtTicketOrderInfo();
        BeanUtils.copyProperties(ticketChangeBookingReq, ptTicketOrderInfo);
        ptTicketOrderInfo.setSfCardNo(ticketChangeBookingReq.getFfpCardNo());
        ptTicketOrderInfo.setFFPId(ticketChangeBookingReq.getFfpId());
        ptTicketOrderInfo.setSfCardNo(ticketChangeBookingReq.getFfpCardNo());
        ptTicketOrderInfo.setFFPCardType("Member");
        ptTicketOrderInfo.setTicketOrderId(orderNo);
        ptTicketOrderInfo.setPriceDiff(0);

        ptTicketOrderInfo.setFFPLevel(null);
        if (HandlerConstants.PASSENGER_TYPE_CHD.equals(passengerType)) {
            ptTicketOrderInfo.setFFPLevel("");
            ptTicketOrderInfo.setUseScoreTotal(0);
            ptTicketOrderInfo.setCouponCode("");
            ptTicketOrderInfo.setPromoCode("");
        }
        ptTicketOrderInfo.setPassengerInfoList(new ArrayList<>());
        ptTicketOrderInfo.setSegmentInfoList(new ArrayList<>());
        ptTicketOrderInfo.setPriceInfoList(new ArrayList<>());
        ptTicketOrderInfo.setPSPriceRelList(new ArrayList<>());
        //子订单的票号必须为空
        ptTicketOrderInfo.setTicketNo("");
        return ptTicketOrderInfo;
    }

    // 航段、价格列表
    private static Map<String, List<?>> ToSegmentInfoList(List<FlightInfo> flightList, String passengerType, List<PassengerInfo> passengerList) {
        int number = 0;
        switch (passengerType) {
            case HandlerConstants.PASSENGER_TYPE_ADT:
                number = 0;
                break;
            case HandlerConstants.PASSENGER_TYPE_CHD:
                number = 10;
                break;
            case HandlerConstants.PASSENGER_TYPE_INF:
                number = 20;
                break;
            case HandlerConstants.PASSENGER_TYPE_GMJC:
                number = 30;
                break;
            default:
                throw new RuntimeException("未知的乘客类型");
        }
        int i = number;
        int j = number;
        int h = number;
        for (PassengerInfo pass : passengerList) {// 乘客集合
            pass.setPassengerNO(h++);
        }
        List<com.juneyaoair.thirdentity.request.booking.PtSegmentInfo> listSeg = new ArrayList<>();// 航段集合
        List<PtPrice> listPrice = new ArrayList<>();// 运价集合
        List<PtPSPriceRel> listPSP = new ArrayList<>();// 乘客、航段与运价关系集合

        for (FlightInfo flight : flightList) {
            com.juneyaoair.thirdentity.request.booking.PtSegmentInfo seg = ToSegmentInfo(flight, i++, passengerType, null);//航段信息
            seg.setPassengerType(passengerType);
            listSeg.add(seg);
            for (PassengerInfo pass : passengerList) {
                List<CabinFare> cabinfareList = null;
                if (HandlerConstants.PASSENGER_TYPE_ADT.equals(pass.getPassengerType())) {
                    cabinfareList = flight.getCabinFareList();
                } else if (HandlerConstants.PASSENGER_TYPE_GMJC.equals(pass.getPassengerType())) {//军残警残
                    cabinfareList = flight.getCabinGMJCFareList();
                } else {
                    cabinfareList = flight.getCabinCHDINFFareList();//获取婴儿儿童舱位
                }

                CabinFare cabinFare = getCabinFare(cabinfareList, pass.getPassengerType());
                if (null != cabinFare) {
                    PtPrice price = getPriceByPassengerType(cabinFare, j++);
                    listPrice.add(price);
                    // 人与价格关系
                    listPSP.add(new PtPSPriceRel(pass.getPassengerNO(), seg.getSegNO(), price
                            .getPriceNO(), true));
                }
            }
        }
        Map<String, List<?>> map = new HashMap<String, List<?>>();
        map.put("listSeg", listSeg);
        map.put("listPrice", listPrice);
        map.put("listPSP", listPSP);
        return map;
    }

    // 航段、价格列表  升舱使用 客票改期新航段
    private static Map<String, List<?>> ToSegmentInfoUpList(List<FlightInfo> flightList, String passengerType,
                                                            List<PassengerInfo> passengerList, List<UsePassengerSegment> usePassengerSegments, HandConfig config) {
        int number = 0;
        switch (passengerType) {
            case HandlerConstants.PASSENGER_TYPE_ADT:
                number = 0;
                break;
            case HandlerConstants.PASSENGER_TYPE_CHD:
                number = 10;
                break;
            case HandlerConstants.PASSENGER_TYPE_INF:
                number = 20;
                break;
            case HandlerConstants.PASSENGER_TYPE_GMJC:
                number = 30;
                break;
            default:
                throw new RuntimeException("未知的乘客类型");
        }
        int i = number;
        int j = number;
        int h = number;
        for (PassengerInfo pass : passengerList) {// 乘客集合
            pass.setPassengerNO(h++);
        }
        List<com.juneyaoair.thirdentity.request.booking.PtSegmentInfo> listSeg = new ArrayList<>();// 航段集合
        List<PtPrice> listPrice = new ArrayList<>();// 运价集合
        List<PtPSPriceRel> listPSP = new ArrayList<>();// 乘客、航段与运价关系集合

        for (FlightInfo flight : flightList) {
            String couponCode = null;
            if (CollectionUtils.isNotEmpty(usePassengerSegments)) {
                for (UsePassengerSegment usePassengerSegment : usePassengerSegments) {
                    if (flight.getDepCity().equals(usePassengerSegment.getFlightInfo().getDepCity())
                            && flight.getArrCity().equals(usePassengerSegment.getFlightInfo().getArrCity())) {
                        couponCode = usePassengerSegment.getCouponCode();
                        break;
                    }
                }
            }
            com.juneyaoair.thirdentity.request.booking.PtSegmentInfo seg = toSegmentInfoNew(flight, i++, passengerType, couponCode, config);//航段信息
            seg.setPassengerType(passengerType);
            listSeg.add(seg);
            List<CabinFare> cabinfareList = null;
            if (HandlerConstants.PASSENGER_TYPE_ADT.equals(passengerType)) {
                cabinfareList = flight.getCabinFareList();
            } else if (HandlerConstants.PASSENGER_TYPE_GMJC.equals(passengerType)) {//军残警残
                cabinfareList = flight.getCabinGMJCFareList();
            } else {
                cabinfareList = flight.getCabinCHDINFFareList();//获取婴儿儿童舱位
            }
            CabinFare cabinFare = getCabinFare(cabinfareList, passengerType);
            if (null != cabinFare) {
                PtPrice price = getPriceByPassengerType(cabinFare, j++);
                //使用优惠券抵用手续费
                if (StringUtils.isNotBlank(couponCode)) {
                    price.setTotalDiff(price.getTotalDiff() - price.getChangeServiceCharge());
                }
                //配置价格,根据类型分组
                long count = passengerList.stream().filter(o -> o.getPassengerType().equals(passengerType)).count();
                price.setUpgradeFee(count * price.getUpgradeFee());
                listPrice.add(price);
                // 人与价格关系
                for (PassengerInfo pass : passengerList) {
                    listPSP.add(new PtPSPriceRel(pass.getPassengerNO(), seg.getSegNO(), price
                            .getPriceNO(), true));
                }
            }
        }
        Map<String, List<?>> map = new HashMap<>();
        map.put("listSeg", listSeg);
        map.put("listPrice", listPrice);
        map.put("listPSP", listPSP);
        return map;
    }

    /**
     * 按照机票子订单的方式对人员分组
     *
     * @param passengerInfoList
     * @return
     */
    private static Map<String, List<PassengerInfo>> toGroupPassenger(List<PassengerInfo> passengerInfoList, List<FlightInfo> flightInfoList, String interFlag) {
        Map<String, List<PassengerInfo>> mapTicketGroup = new HashMap<>();
        List<PassengerInfo> chdList = new ArrayList<>();
        List<PassengerInfo> adtList = new ArrayList<>();
        List<PassengerInfo> oldList = new ArrayList<>();
        List<PassengerInfo> infList = new ArrayList<>();
        List<PassengerInfo> gmjcList = new ArrayList<>();
        for (PassengerInfo pass : passengerInfoList) {
            if (HandlerConstants.PASSENGER_TYPE_CHD.equals(pass.getPassengerType())) {
                chdList.add(pass);
            } else if (HandlerConstants.PASSENGER_TYPE_ADT.equals(pass.getPassengerType())) {
                if ("GM".equals(pass.getCertType()) || "JC".equals(pass.getCertType())) {
                    gmjcList.add(pass);
                } else {
                    adtList.add(pass);
                }
            } else if (HandlerConstants.PASSENGER_TYPE_GMJC.equals(pass.getPassengerType())) {
                gmjcList.add(pass);
            } else if (HandlerConstants.PASSENGER_TYPE_INF.equals(pass.getPassengerType())) {
                infList.add(pass);
            }
        }
        //成人与婴儿分组合并
        if (adtList.size() > 0) {
            adtList.addAll(infList);
        } else {
            gmjcList.addAll(infList);
        }
        // 1、国际直接返回
        if (HandlerConstants.FLIGHT_INTER_I.equals(interFlag)) {
            mapTicketGroup.put(HandlerConstants.PASSENGER_TYPE_ADT, passengerInfoList);
            return mapTicketGroup;
        } else if (HandlerConstants.FLIGHT_INTER_D.equals(interFlag)) {
            //无儿童直接返回
            if (chdList.size() <= 0) {
                if (adtList.size() > 0) {
                    mapTicketGroup.put(HandlerConstants.PASSENGER_TYPE_ADT, adtList);
                }
//                if (oldList.size() > 0) {
//                    mapTicketGroup.put(HandlerConstants.PASSENGER_TYPE_OLD, oldList);
//                }
                if (gmjcList.size() > 0) {
                    mapTicketGroup.put(HandlerConstants.PASSENGER_TYPE_GMJC, gmjcList);
                }
                return mapTicketGroup;
            }
        }
        // 2、判断舱位 如果儿童舱位与成人舱位一致保持一个航段
        int cabinCnt = 0;
        for (FlightInfo flightInfo : flightInfoList) {
            List<CabinFare> listCabinFare = flightInfo.getCabinFareList();
            String cabinCode = listCabinFare.get(0).getCabinCode();
            if (flightInfo.getCabinCHDINFFareList().stream().anyMatch(cabinFare -> cabinCode.equals(cabinFare.getCabinCode()) && HandlerConstants.PASSENGER_TYPE_CHD.equals(cabinFare.getPassengerType()))) {
                cabinCnt++;
            }
        }
        //因为成人、儿童与婴儿航段相同，使用成人类型取相同航段
        if (cabinCnt == flightInfoList.size()) {
            mapTicketGroup.put(HandlerConstants.PASSENGER_TYPE_ADT, passengerInfoList);
            return mapTicketGroup;
        }
        // 不同情况分别取
        if (adtList.size() > 0) {
            mapTicketGroup.put(HandlerConstants.PASSENGER_TYPE_ADT, adtList);// 因为成人与婴儿航段相同，使用成人类型取相同航段
        }
        if (chdList.size() > 0) {
            mapTicketGroup.put(HandlerConstants.PASSENGER_TYPE_CHD, chdList);
        }
        if (gmjcList.size() > 0) {
            mapTicketGroup.put(HandlerConstants.PASSENGER_TYPE_GMJC, gmjcList);
        }
//        if (oldList.size() > 0) {
//            mapTicketGroup.put(HandlerConstants.PASSENGER_TYPE_OLD, oldList);
//        }
        return mapTicketGroup;
    }


    /**
     * 按pnr和舱位分组
     *
     * @param passengerInfoList
     * @param flightInfoList
     * @param interFlag
     * @return
     */
    private static Map<String, List<PassengerInfo>> ticketChangeGroupPassenger(List<PassengerInfo> passengerInfoList, List<FlightInfo> flightInfoList, String interFlag) {
        //先判断航线是否包含儿童,且航线不同,儿童舱位与舱位符合,任何一个航线不同则代表不同,true 不包含 ,false 包含
        //true && true  = true true && false = false;
        Map<String, Boolean> flagMap = new HashMap<>();

        for (FlightInfo flightInfo : flightInfoList) {
            CabinFare cabinFareADT = flightInfo.getCabinFareList().get(0);
            flagMap.put("ADT", true);

            if (!StringUtil.isNullOrEmpty(flightInfo.getCabinCHDINFFareList())) {
                groupCabinFareAndJudgeCabin(flagMap, cabinFareADT, flightInfo.getCabinCHDINFFareList());
            }
            if (!StringUtil.isNullOrEmpty(flightInfo.getCabinGMJCFareList())) {
                groupCabinFareAndJudgeCabin(flagMap, cabinFareADT, flightInfo.getCabinGMJCFareList());
            }
            //婴儿必须同一个组
            flagMap.put("INF", true);
        }

        final Map<String, Boolean> finalFlagMap = flagMap;

        Map<String, List<PassengerInfo>> collect = passengerInfoList.stream()
                .collect(Collectors.groupingBy(o -> {
                    //先pnr,然后如果舱位类型不同则分组
                    return o.getPnrNo() + (finalFlagMap.get(o.getPassengerType()) ? "" : o.getPassengerType());
                }));
        return collect;
    }

    /**
     * 判断舱位是否相同,相同为true,不同添加false
     *
     * @param flagMap
     * @param cabinFareADT
     * @param cabinFareList
     */
    private static void groupCabinFareAndJudgeCabin(Map<String, Boolean> flagMap, CabinFare cabinFareADT, List<CabinFare> cabinFareList) {
        for (CabinFare cabinFare : cabinFareList) {
            Boolean b = flagMap.get(cabinFare.getPassengerType()) == null ? true : flagMap.get(cabinFare.getPassengerType());
            flagMap.put(cabinFare.getPassengerType(), b && cabinFare.getCabinCode().equals(cabinFareADT.getCabinCode()));
        }
    }

    /**
     * 机票订单确认改期
     * 转换人航段的对应的关系
     *
     * @return
     */
    private static Map<String, List<?>> toPassSegPriceMap(List<FlightInfo> flightList, String passengerType, List<PassengerInfo> passengerList,
                                                          int segNo, int priceNo) {
        int i = segNo;// 航段序号赋值
        int j = priceNo;// 运价序号赋值
        List<com.juneyaoair.thirdentity.request.booking.PtSegmentInfo> listSeg = new ArrayList<>();// 航段集合
        List<PtPrice> listPrice = new ArrayList<>();// 运价集合
        List<PtPSPriceRel> listPSP = new ArrayList<>();// 乘客、航段与运价关系集合
        for (FlightInfo flight : flightList) {
            //航段信息
            com.juneyaoair.thirdentity.request.booking.PtSegmentInfo seg = ToSegmentInfo(flight, i++, passengerType, null);
            if (!HandlerConstants.PASSENGER_TYPE_OLD.equals(passengerType)) {
                listSeg.add(seg);
            }
            // 乘客分组  ADT CHD INF GMJC  去对应的运价信息
            Map<String, List<PassengerInfo>> passMap = toPassageGroup(passengerList);
            //按照乘客类型采用对应的价格信息，同一类型的乘客运价编号是一致的
            for (Map.Entry<String, List<PassengerInfo>> entry : passMap.entrySet()) {
                List<CabinFare> cabinfareList = null;
                if (HandlerConstants.PASSENGER_TYPE_ADT.equals(entry.getKey())) {
                    cabinfareList = flight.getCabinFareList();
                } else if (HandlerConstants.PASSENGER_TYPE_GMJC.equals(entry.getKey())) {
                    cabinfareList = flight.getCabinGMJCFareList();
                } else if (HandlerConstants.PASSENGER_TYPE_OLD.equals(entry.getKey())) {
                    cabinfareList = flight.getCabinOLDFareList();
                } else if (flight != null) {
                    cabinfareList = flight.getCabinCHDINFFareList();//获取婴儿儿童舱位
                }
                CabinFare cabinFare = getCabinFare(cabinfareList, entry.getKey());
                if (null != cabinFare) {
                    PtPrice price = getPriceByPassengerType(cabinFare, j++);
                    listPrice.add(price);
                    // 人与价格关系
                    for (PassengerInfo pass : entry.getValue()) {
                        listPSP.add(new PtPSPriceRel(pass.getPassengerNO(), seg.getSegNO(), price
                                .getPriceNO(), true));
                    }
                }
            }
        }
        Map<String, List<?>> map = new HashMap<>();
        map.put("listSeg", listSeg);
        map.put("listPrice", listPrice);
        map.put("listPSP", listPSP);
        return map;
    }


    /**
     * 按照乘客类型构建航段信息
     *
     * @param flight
     * @param num
     * @param passengerType
     * @return
     */
    private static com.juneyaoair.thirdentity.request.booking.PtSegmentInfo ToSegmentInfo(FlightInfo flight, int num, String passengerType, String couponCode) {
        com.juneyaoair.thirdentity.request.booking.PtSegmentInfo ptSegmentInfo = new com.juneyaoair.thirdentity.request.booking.PtSegmentInfo();
        BeanUtils.copyProperties(flight, ptSegmentInfo);
        ptSegmentInfo.setSegNO(num);
        ptSegmentInfo.setIsCodeShare(flight.getCodeShare());
        ptSegmentInfo.setCarrierFlightNo(flight.getCarrierNo());
        ptSegmentInfo.setIsSeatedOnPlane(flight.getASR());
        //ftype 缺少会影响数据库存储
        ptSegmentInfo.setPlaneStyle(flight.getFType() == null ? "320" : flight.getFType());
        ptSegmentInfo.setFlightNo(flight.getFlightNo());
        ptSegmentInfo.setUpgradeFlag(flight.isSelectFlag());
        ptSegmentInfo.setVoluntarilyChangeFlag(!flight.isNotVoluntaryChange());
        ptSegmentInfo.setUpgradeCouponCode(couponCode);//改期券码
        List<CabinFare> cabinList = null;
        if (HandlerConstants.PASSENGER_TYPE_ADT.equals(passengerType)) {
            cabinList = flight.getCabinFareList();
        } else if (HandlerConstants.PASSENGER_TYPE_GMJC.equals(passengerType)) {
            cabinList = flight.getCabinGMJCFareList();
        } else if (HandlerConstants.PASSENGER_TYPE_OLD.equals(passengerType)) {
            cabinList = flight.getCabinOLDFareList();
        } else {
            cabinList = flight.getCabinCHDINFFareList();
        }

        for (CabinFare cabinFare : cabinList) {
            if (passengerType.equals(cabinFare.getPassengerType())) {
                ptSegmentInfo.setCabin(cabinFare.getCabinCode());
                ptSegmentInfo.setDisneyFlag(PackageTypeEnum.DISNEY_FARE.getPackType().equals(cabinFare.getCabinType()));
                ptSegmentInfo.setCabinClass(cabinFare.getCabinClass());
                break;
            }
        }
        return ptSegmentInfo;
    }

    /**
     * 按照乘客类型构建航段信息
     */
    private static com.juneyaoair.thirdentity.request.booking.PtSegmentInfo toSegmentInfoNew(FlightInfo flight, int num,
                                                                                             String passengerType, String couponCode, HandConfig config) {
        com.juneyaoair.thirdentity.request.booking.PtSegmentInfo ptSegmentInfo = ToSegmentInfo(flight, num, passengerType, couponCode);
        ptSegmentInfo.setCabinClass(CommonUtil.getCabinClassByCabinCode(ptSegmentInfo.getCabin(), config.getCabinClass()));
        return ptSegmentInfo;
    }

    /**
     * 按照乘客类型分组
     *
     * @param passengerList
     * @return
     */
    private static Map<String, List<PassengerInfo>> toPassageGroup(List<PassengerInfo> passengerList) {
        Map<String, List<PassengerInfo>> group = new HashMap<String, List<PassengerInfo>>();
        List<PassengerInfo> adtList = new ArrayList<PassengerInfo>();
        List<PassengerInfo> chdList = new ArrayList<PassengerInfo>();
        List<PassengerInfo> infList = new ArrayList<PassengerInfo>();
        List<PassengerInfo> gmjcList = new ArrayList<>();
        List<PassengerInfo> oldList = new ArrayList<>();
        for (PassengerInfo pass : passengerList) {
            if (HandlerConstants.PASSENGER_TYPE_ADT.equals(pass.getPassengerType())) {
                if ("GM".equals(pass.getCertType()) || "JC".equals(pass.getCertType())) {//军残警残处理
                    gmjcList.add(pass);
                } else if (HandlerConstants.PASSENGER_TYPE_OLD.equals(pass.getPassengerIdentity())) {
                    oldList.add(pass);
                } else {
                    adtList.add(pass);
                }
            } else if (HandlerConstants.PASSENGER_TYPE_GMJC.equals(pass.getPassengerType())) {
                gmjcList.add(pass);
            } else if (HandlerConstants.PASSENGER_TYPE_CHD.equals(pass.getPassengerType())) {
                chdList.add(pass);
            } else if (HandlerConstants.PASSENGER_TYPE_INF.equals(pass.getPassengerType())) {
                infList.add(pass);
            }
        }
        if (null != adtList && adtList.size() > 0) {
            group.put(HandlerConstants.PASSENGER_TYPE_ADT, adtList);
        }
        if (null != chdList && chdList.size() > 0) {
            group.put(HandlerConstants.PASSENGER_TYPE_CHD, chdList);
        }
        if (null != infList && infList.size() > 0) {
            group.put(HandlerConstants.PASSENGER_TYPE_INF, infList);
        }
        if (null != gmjcList && gmjcList.size() > 0) {
            group.put(HandlerConstants.PASSENGER_TYPE_GMJC, gmjcList);
        }
        if (null != oldList && oldList.size() > 0) {
            group.put(HandlerConstants.PASSENGER_TYPE_OLD, oldList);
        }
        return group;
    }

    // 取乘客运价
    private static CabinFare getCabinFare(List<CabinFare> cabinList, String passengerType) {
        for (CabinFare cabinFare : cabinList) {
            if (StringUtils.isNotBlank(cabinFare.getFareBasis())) {
                if (cabinFare.getFareBasis().startsWith(passengerType)) {
                    return cabinFare;
                }
            }
            if (passengerType.equals(cabinFare.getPassengerType())) {
                return cabinFare;
            }
            if (HandlerConstants.PASSENGER_TYPE_OLD.equals(passengerType)) {
                if (HandlerConstants.PASSENGER_TYPE_ADT.equals(cabinFare.getPassengerType())
                        && HandlerConstants.PASSENGER_TYPE_OLD.equals(cabinFare.getPassengerIdentity())) {
                    return cabinFare;
                }
            }
        }
        return null;
    }

    // 生成运价
    private static PtPrice getPriceByPassengerType(CabinFare cabinFare, int num) {
        PtPrice ptPrice = new PtPrice();
        BeanUtils.copyProperties(cabinFare, ptPrice);
        ptPrice.setYQTax(ptPrice.getYQTax() < 0 ? 0 : ptPrice.getYQTax());
        ptPrice.setCNTax(ptPrice.getCNTax() < 0 ? 0 : ptPrice.getCNTax());
        ptPrice.setPriceNO(num);
        ptPrice.setIsGiftScore(null != cabinFare.getScoreGiftInfo());
        ptPrice.setUpgradeFee(cabinFare.getChangeServiceCharge());//升舱改期手续费
        if (null != cabinFare.getScoreGiftInfo()) {
            PtScoreGift PtScoreGift = new PtScoreGift();
            BeanUtils.copyProperties(cabinFare.getScoreGiftInfo(), PtScoreGift);
            ptPrice.setScoreGiftInfo(PtScoreGift);
        } else {
            ptPrice.setScoreGiftInfo(null);
        }
        return ptPrice;

    }

    /**
     * 根据票号处理订单乘客信息
     *
     * @param ticketInfo
     * @param ticketNo
     * @return
     */
    private static List<ChangePassengerInfo> ticketInfoToOrderPass(PtIBETicketInfo ticketInfo, String ticketNo, LocalCacheService localCacheService) {
        List<ChangePassengerInfo> passengerInfoList = new ArrayList<>();
        ChangePassengerInfo pass = new ChangePassengerInfo();
        String passengerType = HandlerConstants.PASSENGER_TYPE_ADT;
        if (CommonBaseConstants.IBE_PASSENGER_TYPE_ADT.equals(ticketInfo.getPassengerType())) {
            passengerType = HandlerConstants.PASSENGER_TYPE_ADT;
        } else if (CommonBaseConstants.IBE_PASSENGER_TYPE_CHD.equals(ticketInfo.getPassengerType())) {
            passengerType = HandlerConstants.PASSENGER_TYPE_CHD;
        } else if (CommonBaseConstants.IBE_PASSENGER_TYPE_INF.equals(ticketInfo.getPassengerType())) {
            passengerType = HandlerConstants.PASSENGER_TYPE_INF;
        } else if (CommonBaseConstants.IBE_PASSENGER_TYPE_GMJC.equals(ticketInfo.getPassengerType())) {
            passengerType = HandlerConstants.PASSENGER_TYPE_GMJC;
        }
        pass.setTicketNoInf(ticketInfo.getTicketNoInf());
        pass.setTicketNo(ticketInfo.getTicketNo());
        pass.setPassengerNO(0);
        pass.setPnrNo(ticketInfo.getSegmentInfoList().get(0).getPnrNo());
        pass.setETicketNo(ticketInfo.getTicketNo());
        String passengerName = ticketInfo.getPassengerName().split(" ")[0];
        pass.setPassengerName(passengerName);
        pass.setPassengerType(passengerType);
        pass.setAdtNameToInf(ticketInfo.getAdtNameToInf());
        pass.setBirthdate(ticketInfo.getPassengerBirthday());
        pass.setAdtNameToInfTicketNo(ticketInfo.getAdtNameToInfTicketNo());
        pass.setBirthdate(ticketInfo.getPassengerBirthday());
        pass.setCantRefundReason("Y".equals(ticketInfo.getAdtisConnectedChd()) ? "当前客票已关联同行儿童旅客，请致电客服热线95520为儿童客票一同办理改期；如希望儿童在无成人陪伴的情况下单独出行，请务必提前申请“无成人陪伴儿童”服务，吉祥航空有权拒绝未申请无陪服务的儿童独自出行的要求。" : "");
        pass.setAdtIsConnectedChd(ticketInfo.getAdtisConnectedChd());
        boolean isINF = "PASSENGER_INFANT".equals(ticketInfo.getPassengerType());
        IdentityInfo identityInfo = isINF ? IdentityInfoUtil.getIdentityInfoByINF(ticketInfo.getIdentityInfoList())
                : IdentityInfoUtil.getIdentityInfo(ticketInfo.getIdentityInfoList());
        String certTypeName = "证件号";
        if (null != identityInfo) {
            pass.setCertType(identityInfo.getIdType());
            pass.setCertNo(identityInfo.getIdNo());
            if (StringUtils.isNotBlank(identityInfo.Birthdate)){
                pass.setBirthdate(identityInfo.Birthdate);
            }
            pass.setSex(identityInfo.Sex);
            pass.setNationality(identityInfo.Nationality);
            ;//国家二字码
            pass.setBelongCountry(identityInfo.BelongCountry);
            List<String> countryCode = Arrays.asList(identityInfo.Nationality, identityInfo.BelongCountry).stream().filter(i -> i != null).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(countryCode)) {
                List<TCountryDTO> localCountryList = localCacheService.getLocalCountry(countryCode);
                if (CollectionUtils.isNotEmpty(localCountryList)) {
                    IdentityInfo finalIdentityInfo = identityInfo;
                    if (StringUtils.isNotBlank(identityInfo.Nationality)) {
                        pass.setNationalityName(localCountryList.stream().filter(i -> finalIdentityInfo.Nationality.equalsIgnoreCase(i.countryCode)).findFirst().map(i -> i.countryName).orElse(null));
                        pass.setNationalityEName(localCountryList.stream().filter(i -> finalIdentityInfo.Nationality.equalsIgnoreCase(i.countryCode)).findFirst().map(i -> i.englishName).orElse(null));
                    }
                    if (StringUtils.isNotBlank(identityInfo.BelongCountry)) {
                        pass.setBelongCountryName(localCountryList.stream().filter(i -> finalIdentityInfo.BelongCountry.equalsIgnoreCase(i.countryCode)).findFirst().map(i -> i.countryName).orElse(null));
                        pass.setBelongCountryEName(localCountryList.stream().filter(i -> finalIdentityInfo.BelongCountry.equalsIgnoreCase(i.countryCode)).findFirst().map(i -> i.englishName).orElse(null));
                    }
                }
            }
            pass.setCertValidity(identityInfo.CertValidity);
            String name = ticketInfo.getPassengerName();
            if (StringUtils.isNotBlank(name) && name.contains("/")) {
                pass.setPassEnNameS(name.split("/")[0]);
                pass.setPassEnNameF(name.split("/")[1]);
            }
        }
        pass.setCertTypeName(certTypeName);
        pass.setIsBuyInsurance("N");
        //乘客航段处理
        List<SegmentPriceInfo> segmentPriceInfoList = new ArrayList<>();
        int i = 0;
        //国内航班采取机建，燃油平分方式
        double cn = 0;
        double yq = 0;
        pass.setYQTax(ticketInfo.getYQ());
        pass.setCNTax(ticketInfo.getCN());
        if (HandlerConstants.TRIP_TYPE_D.equals(ticketInfo.getInterFlag())) {
            int segCnt = ticketInfo.getSegmentInfoList().size();
            cn = ticketInfo.getCN() / (double) segCnt;
            yq = ticketInfo.getYQ() / (double) segCnt;
        } else {//国际直接放在人上面
            //其他税费
            pass.setOtherTax(ticketInfo.getTax() - ticketInfo.getYQ() - ticketInfo.getCN());
            pass.setYQTax(ticketInfo.getYQ());
            pass.setCNTax(ticketInfo.getCN());
        }
        boolean taxFlag = false;
        for (PtSegmentInfo ptSegmentInfo : ticketInfo.getSegmentInfoList()) {
            SegmentPriceInfo segmentPriceInfo = new SegmentPriceInfo();
            BeanUtils.copyProperties(ptSegmentInfo, segmentPriceInfo);
            segmentPriceInfo.setSegNO(i++);
            segmentPriceInfo.setDepAirport(ptSegmentInfo.getDepAirportCode());
            segmentPriceInfo.setArrAirport(ptSegmentInfo.getArrAirportCode());
            segmentPriceInfo.setDepDateTime(ptSegmentInfo.getDepTime());
            segmentPriceInfo.setArrDateTime(ptSegmentInfo.getArrTime());
            segmentPriceInfo.setDepTerm(ptSegmentInfo.getDepAirportTerminal());
            segmentPriceInfo.setArrTerm(ptSegmentInfo.getArrAirportTerminal());
            if (HandlerConstants.TRIP_TYPE_I.equals(ticketInfo.getInterFlag())) {
                if (StringUtils.isNotBlank(ptSegmentInfo.getBrandCode())) {
                    segmentPriceInfo.setBrandCode(ptSegmentInfo.getBrandCode());
                } else {
                    if (ptSegmentInfo.getRate().length() > 7) {
                        segmentPriceInfo.setBrandCode(ptSegmentInfo.getRate().substring(6, 7));
                    }
                }

            }

            //2021-04-23 计算票面价逻辑调整增加使用优惠券抵扣的金额
            segmentPriceInfo.setPricePaid(ptSegmentInfo.getTicketPrice() + ptSegmentInfo.getUsedScore());//实际支付票价 + 积分 +优惠券抵扣
            //2021-05-27 由于下单失败，将计算方式还原成以前的
//            segmentPriceInfo.setPricePaid(ptSegmentInfo.getTicketPrice() + ptSegmentInfo.getUsedScore());//实际支付票价 + 积分
            /*if(HandlerConstants.TRIP_TYPE_I.equals(ticketInfo.getInterFlag())&&!taxFlag){
                segmentPriceInfo.setCNTax(cn);
                segmentPriceInfo.setYQTax(yq);
                segmentPriceInfo.setXTax(tax);
                taxFlag = true;
            }*/
            if (HandlerConstants.TRIP_TYPE_D.equals(ticketInfo.getInterFlag())) {
                segmentPriceInfo.setCNTax(cn);
                segmentPriceInfo.setYQTax(yq);
            }
            segmentPriceInfo.setUseScore(0.0);
            segmentPriceInfo.setCouponAmount(ptSegmentInfo.getUsedCoupon());
            //segmentPriceInfo.setAmount();////票面总额  实际支付票价+建设+燃油+保险
            segmentPriceInfoList.add(segmentPriceInfo);
        }
        pass.setSegmentPriceInfoList(segmentPriceInfoList);
        //018-1156015214 或 0181156015214 -> 1156015214
        String replace = ticketNo.replace("-", "");
        pass.setPassengerID(Integer.valueOf(replace.substring(5, replace.length())));
        passengerInfoList.add(pass);
        return passengerInfoList;
    }

    /**
     * 根据票号处理成普通
     *
     * @param ticketInfo
     * @return
     */
    private static List<PassengerInfo> ticketInfoToPass(PtIBETicketInfo ticketInfo) {
        List<PassengerInfo> passengerInfoList = new ArrayList<>();
        PassengerInfo pass = new PassengerInfo();
        String passengerType = HandlerConstants.PASSENGER_TYPE_ADT;
        if (CommonBaseConstants.IBE_PASSENGER_TYPE_ADT.equals(ticketInfo.getPassengerType())) {
            passengerType = HandlerConstants.PASSENGER_TYPE_ADT;
        } else if (CommonBaseConstants.IBE_PASSENGER_TYPE_CHD.equals(ticketInfo.getPassengerType())) {
            passengerType = HandlerConstants.PASSENGER_TYPE_CHD;
        }
        pass.setPassengerNO(0);
        pass.setPassengerName(ticketInfo.getPassengerName());
        pass.setPassengerType(passengerType);
        //默认取身份证，若无则取第一个证件
        IdentityInfo identityInfo = null;
        for (IdentityInfo temp : ticketInfo.getIdentityInfoList()) {
            if ("NI".equals(temp.getIdType())) {
                identityInfo = temp;
                break;
            }
        }
        if (identityInfo == null) {
            identityInfo = ticketInfo.getIdentityInfoList().get(0);
        }
        pass.setCertType(identityInfo.getIdType());
        pass.setCertNo(identityInfo.getIdNo());
        pass.setIsBuyInsurance("N");
        passengerInfoList.add(pass);
        return passengerInfoList;
    }

    /**
     * 处理时间 将yyyy-MM-dd HH:mm
     *
     * @param time
     * @return
     */
    private static String[] separateTime(String time) {
        String[] timeStr = new String[3];
        if (time.length() >= 15) {
            timeStr[0] = time.substring(0, 10);
            timeStr[1] = time.substring(5, 10);
            timeStr[2] = time.substring(11, 16);
        } else {
            timeStr[0] = "";
            timeStr[1] = "";
            timeStr[2] = "";
        }
        return timeStr;
    }

    /**
     * 根据证件类型转换名称
     *
     * @param type
     * @return
     */
    private static String getCertTypeNameByType(String type) {
        String certTypeName;
        switch (type) {
            case "NI": {
                certTypeName = "身份证";
                break;
            }
            case "PP": {
                certTypeName = "护照";
                break;
            }
            default: {
                certTypeName = "证件号";
                break;
            }
        }
        return certTypeName;
    }

    //处理客票的航段信息  //根据客票生成的默认信息
    private static List<FlightInfo> ticketInfoToFlight(PtIBETicketInfo ibeTicketInfo, String passType, LocalCacheService localCacheService, String cabinClassCollection) {
        List<FlightInfo> flightInfoList = new ArrayList<>();
        int i = 0;
        //国内航班采取机建，燃油平分方式
        double cn = 0;
        double yq = 0;
        double tax = 0;
        if (HandlerConstants.TRIP_TYPE_D.equals(ibeTicketInfo.getInterFlag())) {
            int segCnt = ibeTicketInfo.getSegmentInfoList().size();
            cn = ibeTicketInfo.getCN() / (double) segCnt;
            yq = ibeTicketInfo.getYQ() / (double) segCnt;
        }
        for (PtSegmentInfo seg : ibeTicketInfo.getSegmentInfoList()) {
            AirPortInfoDto deptAirPort = localCacheService.getLocalAirport(seg.getDepAirportCode());
            AirPortInfoDto arrAirPort = localCacheService.getLocalAirport(seg.getArrAirportCode());
            List<CabinFare> cabinFareList = new ArrayList<>();
            FlightInfo flightInfo = new FlightInfo();
            BeanUtils.copyProperties(seg, flightInfo);
            if (i == 1) {
                if ((!(ibeTicketInfo.getSegmentInfoList().get(i - 1).getDepAirportCode()
                        .equals(ibeTicketInfo.getSegmentInfoList().get(i).getArrAirportCode())))) {
                    flightInfo.setFlightDirection("G");
                } else {
                    flightInfo.setFlightDirection("B");
                }
            }
            if (i == 0) {
                flightInfo.setFlightDirection("G");
            }
            if (i > 1) {
                flightInfo.setFlightDirection("B");
            }
            flightInfo.setID(String.valueOf(i));
            flightInfo.setFlightDate(separateTime(seg.getDepTime())[0]);
            flightInfo.setDepDateTime(seg.getDepTime());
            flightInfo.setArrDateTime(seg.getArrTime());
            flightInfo.setDepCity(deptAirPort.getCityCode());
            flightInfo.setArrCity(arrAirPort.getCityCode());
            flightInfo.setDepAirport(seg.getDepAirportCode());
            flightInfo.setArrAirport(seg.getArrAirportCode());
            flightInfo.setCabinCode(seg.getCabin());
            //2021-04-23 计算票面价逻辑调整增加使用优惠券抵扣的金额
            flightInfo.setPriceValue(seg.getTicketPrice() + seg.getUsedScore() + seg.getUsedCoupon()); //处理价格 支付金额+积分+优惠券抵扣
            flightInfo.setYQTax(yq);
            flightInfo.setCNTax(cn);
            //拼造舱位价格
            CabinFare cabinFare = new CabinFare();
            cabinFare.setID("0");
            cabinFare.setCabinCode(seg.getCabin());
            cabinFare.setCabinClass(CommonUtil.getCabinClassByCabinCode(seg.getCabin(), cabinClassCollection));
            cabinFare.setPassengerType(passType);
            if (HandlerConstants.TRIP_TYPE_I.equals(ibeTicketInfo.getInterFlag())) {
                flightInfo.setYQTax(-1);
                flightInfo.setCNTax(-1);
                cabinFare.setCNTax(-1);
                cabinFare.setYQTax(-1);
                //cabinFare.setTax(tax);
                //taxFlag = true;
            }
            if (HandlerConstants.TRIP_TYPE_D.equals(ibeTicketInfo.getInterFlag())) {
                cabinFare.setCNTax(cn);
                cabinFare.setYQTax(yq);
            }
            //2021-04-23 计算票面价逻辑调整增加使用优惠券抵扣的金额
            cabinFare.setPriceValue(seg.getTicketPrice() + seg.getUsedScore());//处理价格 支付金额+积分+优惠券抵扣
            cabinFare.setRSP(cabinFare.getPriceValue());
            cabinFareList.add(cabinFare);
            cabinFareToFlight(cabinFareList, flightInfo, passType);
            flightInfoList.add(flightInfo);
            i++;
        }
        return flightInfoList;
    }

    //处理舱位价格
    private static FlightInfo cabinFareToFlight(List<CabinFare> cabinFareList, FlightInfo flightInfo, String passType) {
        if (HandlerConstants.PASSENGER_TYPE_ADT.equals(passType)) {
            flightInfo.setCabinFareList(cabinFareList);
        } else if (HandlerConstants.PASSENGER_TYPE_GMJC.equals(passType)) {
            flightInfo.setCabinGMJCFareList(cabinFareList);
        } else {
            flightInfo.setCabinCHDINFFareList(cabinFareList);
        }
        return flightInfo;
    }

    // 计算价格
    private static double sumPrice(List<PtTicketOrderInfo> orderInfoList) {
        double payAmount = 0.0;
        for (PtTicketOrderInfo ptOrder : orderInfoList) {
            for (PtPassengerInfo ptPassenger : ptOrder.getPassengerInfoList()) {
                double insureAmt = 0.0;
                if (("Y").equals(ptPassenger.getIsBuyInsurance())) {
                    for (PtInsuranceInfo insure : ptPassenger.getInsuranceList()) {
                        insureAmt += insure.getInsuranceAmount();
                    }
                }
                payAmount += ptPassenger.getPricePaid() + ptPassenger.getYQTax()
                        + ptPassenger.getCNTax() + ptPassenger.getOtherTax()
                        + ptPassenger.getQFee() + insureAmt;
            }
        }
        return payAmount;
    }

    public static PtCouponProductGetRequestDto createProductGetRequestDto(QueryAvailChangeCouponRequest couponRequest, String channelCode) {
        PtCouponProductGetRequestDto ptCouponProductGetRequestDto = new PtCouponProductGetRequestDto(HandlerConstants.VERSION,
                channelCode, couponRequest.getFfpId(), couponRequest.getFfpCardNo());
        ptCouponProductGetRequestDto.setCouponState(Collections.singletonList(OrderCouponStateEnum.Not.getStateCode()));
        List<String> voucherTypes = new ArrayList<>();
        voucherTypes.add(VoucherTypesEnum.RESCHEDULECOUPON.getCode());
        voucherTypes.add(VoucherTypesEnum.RESCHEDULE.getCode());
        ptCouponProductGetRequestDto.setVoucherTypes(voucherTypes);
        List<SingleBookCondition> singleBookConditionList = new ArrayList<>();
        for (ChangeFlightInfo flightInfo : couponRequest.getChangeFlightInfoList()) {
            SingleBookCondition singleBookCondition = new SingleBookCondition();
            singleBookCondition.setFlightType("OW");
            //使用日期 因系统时间误差增加3分钟
            singleBookCondition.setUseStartDate(DateUtils.getDateStringAll(DateUtils.dateAddOrLessSecond(new Date(), 3 * 60)) + ":00");
            //此时代表的是城市三字码
            singleBookCondition.setDepAirportCode(flightInfo.getDepCityCode());
            singleBookCondition.setArrAirportCode(flightInfo.getArrCityCode());
            //航班日期
            singleBookCondition.setFlightDate(flightInfo.getDepFlightDate() + " " + flightInfo.getDepDateTime() + ":00");
            singleBookCondition.setFlightNo(flightInfo.getFlightNo());
            singleBookCondition.setCabin(flightInfo.getCabin());
            singleBookConditionList.add(singleBookCondition);
        }
        //手续费全部为0时不需查询改期券
        if (CollectionUtils.isEmpty(singleBookConditionList)) {
            throw new QueryNoDataException(WSEnum.NO_DATA.getResultInfo());
        }
        ptCouponProductGetRequestDto.setSingleBookConditions(singleBookConditionList);
        ptCouponProductGetRequestDto.setPageNo(1);
        ptCouponProductGetRequestDto.setPageSize(100);
        return ptCouponProductGetRequestDto;
    }

    // 获取航段中的cabinCode
    public static CabinFare findCabin(FlightInfo flightInfo, String passengerType) {
        CabinFare cabin = null;
        if (PassengerTypeEnum.ADT.getPassType().equalsIgnoreCase(passengerType) && CollectionUtils.isNotEmpty(flightInfo.getCabinFareList())) {
            Optional<CabinFare> optional = flightInfo.getCabinFareList().stream().filter(e -> e.getChangeServiceCharge() > 0).findFirst();
            if (optional.isPresent()) {
                cabin = optional.get();
            }
        } else if (CollectionUtils.isNotEmpty(flightInfo.getCabinCHDINFFareList())) {
            Optional<CabinFare> optional = flightInfo.getCabinCHDINFFareList().stream()
                    .filter(e -> passengerType.equals(e.getPassengerType()) && e.getChangeServiceCharge() > 0).findFirst();
            if (optional.isPresent()) {
                cabin = optional.get();
            }
        }
        return cabin;
    }
}

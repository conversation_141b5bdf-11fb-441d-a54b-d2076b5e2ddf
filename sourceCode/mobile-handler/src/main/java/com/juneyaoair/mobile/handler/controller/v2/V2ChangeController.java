package com.juneyaoair.mobile.handler.controller.v2;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.geetest.geeguard.sdk.GeetestLib;
import com.google.common.collect.Lists;
import com.juneyaoair.CommonBaseConstants;
import com.juneyaoair.appenum.ChannelCodeEnum;
import com.juneyaoair.appenum.WSEnum;
import com.juneyaoair.appenum.av.FareBasisEnum;
import com.juneyaoair.appenum.av.FareTypeEnum;
import com.juneyaoair.appenum.av.FlightQueryTypeEnum;
import com.juneyaoair.appenum.common.AirCompanyEnum;
import com.juneyaoair.appenum.flight.FareSourceEnum;
import com.juneyaoair.appenum.flight.FlightDirection;
import com.juneyaoair.appenum.geetest.GeetestTypeEnum;
import com.juneyaoair.appenum.member.CertificateTypeEnum;
import com.juneyaoair.appenum.order.PassengerTypeEnum;
import com.juneyaoair.appenum.order.SubOrderTypeEnum;
import com.juneyaoair.appenum.order.UnifiedOrderResultEnum;
import com.juneyaoair.appenum.rightcoupon.VoucherTypesEnum;
import com.juneyaoair.appenum.ticket.TicketQueryTypeEnum;
import com.juneyaoair.baseclass.av.common.FlightInfoComb;
import com.juneyaoair.baseclass.av.common.TrrDateLimit;
import com.juneyaoair.baseclass.av.request.FlightFareChangeQuery;
import com.juneyaoair.baseclass.basicsys.response.AirPortInfoDto;
import com.juneyaoair.baseclass.change.ChangeAndRefundRule;
import com.juneyaoair.baseclass.change.ChangeFlightInfo;
import com.juneyaoair.baseclass.change.ChangePassengerInfo;
import com.juneyaoair.baseclass.change.TicketInfo;
import com.juneyaoair.baseclass.common.request.BaseReq;
import com.juneyaoair.baseclass.common.response.BaseResp;
import com.juneyaoair.baseclass.newcoupon.req.protocol.OrderCouponDto;
import com.juneyaoair.baseclass.prepayment.request.QueryCouponInfoRequest;
import com.juneyaoair.baseclass.request.av.AircraftModel;
import com.juneyaoair.baseclass.request.booking.InsuranceFlightInfo;
import com.juneyaoair.baseclass.request.booking.InsuranceInfoNew;
import com.juneyaoair.baseclass.request.booking.OrderChangeBookingReq;
import com.juneyaoair.baseclass.request.booking.TicketChangeBookingReq;
import com.juneyaoair.baseclass.request.geetest.Geetest;
import com.juneyaoair.baseclass.request.order.InterchangeFee.CalculateChangeRequest;
import com.juneyaoair.baseclass.request.order.InterchangeFee.SengmentChangeDto;
import com.juneyaoair.baseclass.request.order.query.OrderDetailQuery;
import com.juneyaoair.baseclass.request.order.query.OrderQuery;
import com.juneyaoair.baseclass.response.av.CabinFare;
import com.juneyaoair.baseclass.response.av.ChangeFlightFareResp;
import com.juneyaoair.baseclass.response.av.FlightInfo;
import com.juneyaoair.baseclass.response.booking.TicketBookingResp;
import com.juneyaoair.baseclass.response.coupons.UsePassengerSegment;
import com.juneyaoair.baseclass.response.order.InterchangeFee.CalculateChangeResponse;
import com.juneyaoair.baseclass.response.order.InterchangeFee.InterChangeFeeDto;
import com.juneyaoair.baseclass.response.order.comm.SegmentPriceInfo;
import com.juneyaoair.baseclass.response.order.comm.TaxInfo;
import com.juneyaoair.baseclass.response.order.query.OrderBase;
import com.juneyaoair.baseclass.response.order.query.SubOrderResp;
import com.juneyaoair.baseclass.ticket.TicketQuery;
import com.juneyaoair.baseclass.ticket.req.TravelPartnerChangeFlightInfo;
import com.juneyaoair.baseclass.ticket.req.TravelPartnerReq;
import com.juneyaoair.baseclass.ticketInfo.IBETicketInfo;
import com.juneyaoair.bo.FlightQueryBO;
import com.juneyaoair.exception.CommonException;
import com.juneyaoair.exception.OperationFailedException;
import com.juneyaoair.exception.RequestParamErrorException;
import com.juneyaoair.exception.ServiceException;
import com.juneyaoair.mobile.aop.annotation.InterfaceLog;
import com.juneyaoair.mobile.comm.service.IRedisService;
import com.juneyaoair.mobile.constants.MobileCoreBeanNames;
import com.juneyaoair.mobile.handler.HandlerConstants;
import com.juneyaoair.mobile.handler.comm.RedisKeyConfig;
import com.juneyaoair.mobile.handler.config.bean.HandConfig;
import com.juneyaoair.mobile.handler.config.bean.TempleteConfig;
import com.juneyaoair.mobile.handler.controller.BassController;
import com.juneyaoair.mobile.handler.controller.v2.util.AVObjectConvertV2;
import com.juneyaoair.mobile.handler.controller.v2.util.AVObjectConvertV3;
import com.juneyaoair.mobile.handler.controller.v2.util.ChangeObjectConvert;
import com.juneyaoair.mobile.handler.controller.v2.util.CommonUtil;
import com.juneyaoair.mobile.handler.manage.OrderManage;
import com.juneyaoair.mobile.handler.service.GeetestService;
import com.juneyaoair.mobile.handler.service.IBasicService;
import com.juneyaoair.mobile.handler.service.IThemeCardService;
import com.juneyaoair.mobile.handler.service.LocalCacheService;
import com.juneyaoair.mobile.handler.util.CertNoUtil;
import com.juneyaoair.mobile.handler.util.FlightUtil;
import com.juneyaoair.mobile.handler.util.GeetestUtil;
import com.juneyaoair.pattern.PatternCommon;
import com.juneyaoair.thirdentity.av.comm.Fare;
import com.juneyaoair.thirdentity.av.comm.FareTaxInfo;
import com.juneyaoair.thirdentity.av.comm.V2CabinFare;
import com.juneyaoair.thirdentity.av.comm.V2FlightInfo;
import com.juneyaoair.thirdentity.av.request.PtQueryFlightFareRequest;
import com.juneyaoair.thirdentity.av.response.PtQueryFlightFareResponse;
import com.juneyaoair.thirdentity.change.common.CabinChange;
import com.juneyaoair.thirdentity.change.common.SegmentChange;
import com.juneyaoair.thirdentity.change.request.PtCalculateChangeRequest;
import com.juneyaoair.thirdentity.change.request.PtFlightFareChangeRequest;
import com.juneyaoair.thirdentity.change.request.PtOrderChangeConfirmRequest;
import com.juneyaoair.thirdentity.change.request.PtOrderChangeRequest;
import com.juneyaoair.thirdentity.change.response.*;
import com.juneyaoair.thirdentity.request.av.Segment;
import com.juneyaoair.thirdentity.request.booking.PtTicketBookingReq;
import com.juneyaoair.thirdentity.request.order.query.PtSubOrderReq;
import com.juneyaoair.thirdentity.request.order.refund.apply.PtRefundApplyReq;
import com.juneyaoair.thirdentity.request.speedRefund.TicketInfoRequest;
import com.juneyaoair.thirdentity.response.detr.PtIBETicketInfo;
import com.juneyaoair.thirdentity.response.detr.PtSegmentInfo;
import com.juneyaoair.thirdentity.response.order.apply.PtOrderPassengerInfo;
import com.juneyaoair.thirdentity.response.order.apply.PtPassengerSegment;
import com.juneyaoair.thirdentity.response.order.apply.PtRefundApplyResp;
import com.juneyaoair.thirdentity.response.speedRefund.TicketListInfoResponse;
import com.juneyaoair.utils.StringUtil;
import com.juneyaoair.utils.http.HttpResult;
import com.juneyaoair.utils.http.HttpUtil;
import com.juneyaoair.utils.json.JsonMapper;
import com.juneyaoair.utils.json.JsonUtil;
import com.juneyaoair.utils.util.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Predicate;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @description
 * @date 2018/11/28  9:02.
 */
@RequestMapping("/v2/change")
@RestController
@Api(value = "V2ChangeController", description = "改期服务（JAVA接口）")
public class V2ChangeController extends BassController {
    @Autowired
    private LocalCacheService localCacheService;

    @Autowired
    private OrderManage orderManage;
    @Autowired
    private GeetestService geetestService;
    @Resource(name = MobileCoreBeanNames.API_REDIS_SERVICE)
    private IRedisService apiRedisService;
    @Autowired
    private IThemeCardService themeCardService;

    @Autowired
    private static final String NAME_OR_CARD_ERROR = "您输入的证件号/票号或姓名有误，请核对后重新输入，如有疑问，欢迎致电吉祥航空客服热线95520咨询";

    @InterfaceLog
    @ApiOperation(value = "客票查询改期信息", notes = "客票查询改期信息")
    @RequestMapping(value = "/getChangeTicketInfo", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp<List<TicketInfo>> getChangeTicketInfo(@RequestBody @Validated BaseReq<TicketQuery> req, BindingResult bindingResult, HttpServletRequest request) {
        String reqId = MdcUtils.getRequestId();
        BaseResp<List<TicketInfo>> resp = new BaseResp<>();
        String ip = this.getClientIP(request);
        String headChannelCode = request.getHeader(HandlerConstants.TOKEN_CHANNELCODE);
        //参数有效性检验
        if (bindingResult.hasErrors()) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
            return resp;
        }
        TicketQuery ticketQuery = req.getRequest();
        String channelCode = req.getChannelCode();
        String userNo = getChannelInfo(channelCode, "10");
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(ticketQuery.getFfpId(), ticketQuery.getLoginKeyInfo(), channelCode);
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }

        if (ticketQuery.getTicketNo().startsWith("902") || ticketQuery.getFlightNo().startsWith("AQ")) {
            resp.setResultCode(WSEnum.NO_DATA.getResultCode());
            resp.setResultInfo("九元航班暂不支持客票改期");
            return resp;
        }
        //极验验证操作
        GeetestLib gtSdk = GeetestUtil.initGeetest(headChannelCode, GeetestTypeEnum.TICKET.getGeetestType());
        HashMap<String, String> param = new HashMap<>();
        param.put("user_id", ip); //网站用户id  设备号
        param.put("client_type", ticketQuery.getClient_type()); //web:电脑上的浏览器；h5:手机上的浏览器，包括移动应用内完全内置的web_view；native：通过原生SDK植入APP应用的方式
        param.put("ip_address", ip); //传输用户请求验证时所携带的IP
        Geetest geetest = new Geetest(ticketQuery.getGeetest_challenge(), ticketQuery.getGeetest_validate(), ticketQuery.getGeetest_seccode());
        geetestService.validateMd5(gtSdk, geetest, param);
        JSONObject tags = new JSONObject();
        tags.put("IP", ip);
        tags.put("FfpCardNo", ticketQuery.getFfpCardNo());
        tags.put("ChannelCode", headChannelCode);
        MetricLogUtil.saveMetricLog("客票改期-客票提取", tags, new BigDecimal(1));
        TicketListInfoResponse ticketListInfoResponse = queryTicket(channelCode, ticketQuery.getTicketNo(), ticketQuery.getPassName(), ip);
        if (!UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(ticketListInfoResponse.getResultCode())) {
            resp.setResultCode(WSEnum.NO_DATA.getResultCode());
            resp.setResultInfo(NAME_OR_CARD_ERROR);
            return resp;
        }
        if (CollectionUtils.isEmpty(ticketListInfoResponse.getIBETicketInfoList())) {
            resp.setResultCode(WSEnum.NO_DATA.getResultCode());
            resp.setResultInfo("暂未查询到可线上改期的客票，请致电吉祥航空客服热线95520咨询");
            return resp;
        }
        boolean check = false;
        AtomicReference<String> depCity= new AtomicReference<>("");
        AtomicReference<String> arrCity= new AtomicReference<>("");
        for (PtIBETicketInfo ptIBETicketInfo : ticketListInfoResponse.getIBETicketInfoList()) {
            check = ptIBETicketInfo.getSegmentInfoList().stream().anyMatch(ptSegmentInfo -> {
                if (ticketQuery.getFlightNo().equals(ptSegmentInfo.getFlightNo())
                ) {
                    AirPortInfoDto arrAirPort = localCacheService.getLocalAirport(ptSegmentInfo.getArrAirportCode());
                    AirPortInfoDto depAirPort = localCacheService.getLocalAirport(ptSegmentInfo.getDepAirportCode());
                    depCity.set(depAirPort.getCityCode());
                    arrCity.set(arrAirPort.getCityCode());
                    return true;
                } else {
                    return false;
                }
            });
            if (check) {
                break;
            }
        }
        if (!check) {
            resp.setResultCode(WSEnum.NO_DATA.getResultCode());
            resp.setResultInfo("暂未查询到可线上改期的客票，请致电吉祥航空客服热线95520咨询");
            return resp;
        }
        List<PtIBETicketInfo> ptIBETicketInfoList = ticketListInfoResponse.getIBETicketInfoList();
        if (CollectionUtils.isNotEmpty(ptIBETicketInfoList)) {
            Boolean disneyFlag = ptIBETicketInfoList.stream().anyMatch(ptIBETicketInfo -> ptIBETicketInfo.getDisneyFlag());
            AtomicReference<Boolean> airportTransfer = new AtomicReference<>(false);
            AtomicReference<Boolean> busTransfer = new AtomicReference<>(false);
            ptIBETicketInfoList.stream().forEach(ticket -> {
                if (CollectionUtils.isNotEmpty(ticket.getSegmentInfoList())) {
                    airportTransfer.set(ticket.getSegmentInfoList().stream().filter(ptSegmentInfo -> StringUtils.isNotBlank(ptSegmentInfo.getRate()))
                            .anyMatch(ptSegmentInfo -> ptSegmentInfo.getRate().endsWith("B7")));
                    busTransfer.set(ticket.getSegmentInfoList().stream().filter(ptSegmentInfo -> StringUtils.isNotBlank(ptSegmentInfo.getRate()))
                            .anyMatch(ptSegmentInfo -> ptSegmentInfo.getRate().endsWith(FareBasisEnum.BUS_FARE.getFareBasisCode())));
                }
            });
            if (busTransfer.get()) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("客票为包车机票，如需改期，请联系客服处理");
                return resp;
            }
            if (disneyFlag || airportTransfer.get()) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("特殊产品请至原购票账户/订单操作改期");
                return resp;
            }
        }

        //过滤姓名
        List<PtIBETicketInfo> filterList = new ArrayList<>();
        for (PtIBETicketInfo ptIBETicketInfo : ptIBETicketInfoList) {
            String patternStr = ticketQuery.getPassName().toUpperCase() + "(\\s*(CHD)?\\s*(\\(CHILD\\))?\\s*|\\s*(MR)?\\s*|\\s*(MS)?\\s*|\\s*INF\\s*\\(\\w{1,3}\\d{1,2}\\))";//正则表达式
            Pattern pattern = Pattern.compile(patternStr);
            Matcher matcher = pattern.matcher(ptIBETicketInfo.getPassengerName());
            if (matcher.matches()) {
                filterList.add(ptIBETicketInfo);
            }
        }
        ticketListInfoResponse.getIBETicketInfoList().stream().forEach(ticket -> {
            if (!StringUtils.isBlank(ticket.getSigningInfo())
                    && (ticket.getSigningInfo().contains("儿童免票券") || "YPET".equals(ticket.getSigningInfo())
                    || "JPET".equals(ticket.getSigningInfo()))) {
                ticket.setAllowChange(false);
            }
            if (CollectionUtils.isNotEmpty(ticket.getSegmentInfoList())) {
                ticket.getSegmentInfoList().forEach(ptSegmentInfo -> {
                    if (!StringUtils.isBlank(ptSegmentInfo.getFareBasic())) {
                        if ("YPET".equals(ptSegmentInfo.getFareBasic()) || "JPET".equals(ptSegmentInfo.getFareBasic())
                                || "CBBG".equals(ptSegmentInfo.getFareBasic()) || ptSegmentInfo.getRate().endsWith("B7")) {
                            ticket.setAllowChange(false);
                        }
                    }

                });
            }
        });
        Boolean allNotChange = ticketListInfoResponse.getIBETicketInfoList().stream().allMatch(ticket -> !ticket.getAllowChange());
        if (allNotChange) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("您的客票暂不支持线上改期，请致电客服电话95520");
            return resp;
        }
        //证件号检测
        if (CollectionUtils.isEmpty(filterList)) {
            resp.setResultCode(WSEnum.NO_DATA.getResultCode());
            resp.setResultInfo(NAME_OR_CARD_ERROR);
            return resp;
        }
        //婴儿 儿童不支持单独改期
        String passengerType = filterList.get(0).getPassengerType();
        if (CommonBaseConstants.IBE_PASSENGER_TYPE_CHD.equals(passengerType)
                || CommonBaseConstants.IBE_PASSENGER_TYPE_INF.equals(passengerType)) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("婴儿/儿童客票不支持单独改期，请查询同行成人客票并添加婴儿/儿童一同办理");
            return resp;
        }
        //非人民币支付暂不支持改期
        boolean checkCurrency = filterList.stream().allMatch(ticket -> !HandlerConstants.CURRENCY_CODE.equals(ticket.getCurrencyType()));
        if (checkCurrency) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("非人民币支付客票暂不支持办理线上改期，请致电吉祥航空客服热线95520办理");
            return resp;
        } else {
            filterList = filterList.stream().filter(ticket -> HandlerConstants.CURRENCY_CODE.equals(ticket.getCurrencyType())).collect(Collectors.toList());
        }
        String cabinClassCollection = handConfig.getCabinClass();
        Map<String, AircraftModel> aircraftModelMap = FlightUtil.toAircraftModelMap(handConfig.getAircraftModel());
        List<TicketInfo> ticketInfoListResult = ChangeObjectConvert.outputTicketInfo(filterList, localCacheService, aircraftModelMap,
                cabinClassCollection, basicService, ticketListInfoResponse.getTrrLimit(), handConfig);
        List<TicketInfo> ticketInfoList = ticketInfoListResult.stream()
                .filter(ticketInfo -> StringUtils.isBlank(ticketInfo.getNotCanUseMessage()))
                .collect(Collectors.toList());

        List<OrderCouponDto>    orderCouponDtoList=themeCardService.queryTicketRedeem(ticketQuery.getTicketNo(),depCity.get(),arrCity.get());
        if (CollectionUtils.isNotEmpty(orderCouponDtoList)) {
            //临期次卡
            List<OrderCouponDto>  expiringCard = orderCouponDtoList.stream().filter(orderCouponDto -> VoucherTypesEnum.EXPIRING_CARD.getCode().equals(orderCouponDto.getCouponSource())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(expiringCard)){
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("您的客票暂不支持线上改期，请致电客服电话95520");
                return resp;
            }
        }
        //无效航班
        if (CollectionUtils.isEmpty(ticketInfoList)) {
            if (CollectionUtils.isNotEmpty(ticketInfoListResult)) {
                resp.setResultCode(WSEnum.NO_DATA.getResultCode());
                resp.setResultInfo(ticketInfoListResult.get(0).getNotCanUseMessage());
                return resp;
            }
            resp.setResultCode(WSEnum.NO_DATA.getResultCode());
            resp.setResultInfo("暂未查询到可线上改期的客票，如有疑问，欢迎致电吉祥航空客服热线95520咨询");
            return resp;
        }

        if (checkTicke(resp, ticketInfoList.get(0))) {
            return resp;
        }
        //票号查询查询改期规则，证件查询是不执行此方法
        if (ticketQuery.getTicketNo().matches(PatternCommon.TICKET_NO)) {
            ticketHandlerAndBuild(reqId, ip, channelCode, userNo, ticketInfoList, ticketQuery.getFfpCardNo(), ticketQuery.getFfpId());
        }
        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
        resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
        /**
         * 2021-8-20
         * 添加购买权益券判断
         */
        handlerCouponTicketInfoList(ip, channelCode, userNo, ticketInfoList);
        //处理客票隐私信息
        handSensitiveInfo(ticketInfoList);
        resp.setObjData(ticketInfoList);
        return resp;
    }

    /**
     * 处理客票隐私信息
     *
     * @param ticketInfoList
     */
    private void handSensitiveInfo(List<TicketInfo> ticketInfoList) {
        ticketInfoList.parallelStream().forEach(ticketInfo ->
                handSensitiveInfoPass(ticketInfo.getPassengerInfoList())
        );
    }

    /**
     * 处理客票隐私信息
     *
     * @param changePassengerInfoList
     */
    private void handSensitiveInfoPass(List<ChangePassengerInfo> changePassengerInfoList) {
        changePassengerInfoList.parallelStream().forEach(changePassengerInfo -> {
            String certNo = changePassengerInfo.getCertNo();
            changePassengerInfo.setCertNo(SensitiveInfoHider.hideMiddleSensitiveInfo(certNo));
            //客票对应的敏感信息缓存10分钟
            String tktNo = changePassengerInfo.getETicketNo();
            if (StringUtils.isBlank(tktNo)) {
                tktNo = changePassengerInfo.getTicketNo();
            }
            apiRedisService.putData(RedisKeyConfig.createUpIdInfo(tktNo), certNo, 60 * 10L);
        });
    }

    private void handlerCouponTicketInfoList(String ip, String channelCode, String userNo, List<TicketInfo> ticketInfoList) {
        QueryCouponInfoRequest queryCouponInfoRequest = new QueryCouponInfoRequest();
        queryCouponInfoRequest.setRequestIp(ip);
        queryCouponInfoRequest.setChannelCode(channelCode);
        queryCouponInfoRequest.setUserNo(userNo);
        queryCouponInfoRequest.setVersion("10");
        for (TicketInfo ticketInfo : ticketInfoList) {
            handlerCoupon(queryCouponInfoRequest, ticketInfo);
        }
    }

    private void handlerCoupon(QueryCouponInfoRequest queryCouponInfoRequest, TicketInfo ticketInfo) {
        List<ChangePassengerInfo> passengerInfoList = ticketInfo.getPassengerInfoList();
        if (StringUtil.isNullOrEmpty(passengerInfoList)) {
            return;
        }
        List<String> ticketNoList = new ArrayList<>();
        for (ChangePassengerInfo changePassengerInfo : passengerInfoList) {
            List<SegmentPriceInfo> segmentPriceInfoList = changePassengerInfo.getSegmentPriceInfoList();
            if (StringUtil.isNullOrEmpty(segmentPriceInfoList)) {
                continue;
            }
            ticketNoList.add(changePassengerInfo.getETicketNo());
        }
        queryCouponInfoRequest.setTicketNoList(ticketNoList);
        String payCouponDesc = orderManage.isPayCoupon(queryCouponInfoRequest);
        if (!StringUtil.isNullOrEmpty(payCouponDesc)) {
            payCouponDesc = payCouponDesc + "，需要单独申请退款，确认继续改期吗";
            ticketInfo.setPayCouponInfoDesc(payCouponDesc);
        }
    }

    //查询同行人
    @InterfaceLog
    @ApiOperation(value = "查询改期同行人", notes = "查询同行人")
    @RequestMapping(value = "/getTravelPartner", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp<ChangePassengerInfo> getTravelPartner(@RequestBody @Validated BaseReq<TravelPartnerReq> req, BindingResult bindingResult, HttpServletRequest request) {
        /*
        1.参数校验
        2.复制查询接口
        3.根据航线和响应过滤航线和舱位
         */
        String reqId = MdcUtils.getRequestId();
        BaseResp resp = new BaseResp();
        String ip = this.getClientIP(request);
        String headChannelCode = request.getHeader(HEAD_CHANNEL_CODE);
        //参数有效性检验
        if (bindingResult.hasErrors()) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
            return resp;
        }
        TravelPartnerReq ticketQuery = req.getRequest();
        String channelCode = req.getChannelCode();
        String userNo = getChannelInfo(channelCode, "10");
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(ticketQuery.getFfpId(), ticketQuery.getLoginKeyInfo(), channelCode);
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        if (CollectionUtils.isNotEmpty(ticketQuery.getChangeFlightInfoList())) {
            boolean isGmJc = ticketQuery.getChangeFlightInfoList().stream().allMatch(changeFlightInfo -> HandlerConstants.PASSENGER_TYPE_GMJC.equals(changeFlightInfo.getFireBase()));
            if (isGmJc) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("军警残客票暂不支持与其他客票一同办理改期，请分别查询客票办理或致电吉祥航空客服热线95520办理");
                return resp;
            }
        }

        JSONObject tags = new JSONObject();
        tags.put("IP", ip);
        tags.put("FfpCardNo", ticketQuery.getFfpCardNo());
        tags.put("ChannelCode", headChannelCode);
        MetricLogUtil.saveMetricLog("改期同行人-客票提取", tags, new BigDecimal(1));
        TicketListInfoResponse ticketListInfoResponse = queryTicket(channelCode, ticketQuery.getTicketNo(), ticketQuery.getPassName(), ip);
        if (!UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(ticketListInfoResponse.getResultCode())) {
            resp.setResultCode(WSEnum.NO_DATA.getResultCode());
            resp.setResultInfo(NAME_OR_CARD_ERROR);
            return resp;
        }
        if (CollectionUtils.isEmpty(ticketListInfoResponse.getIBETicketInfoList())) {
            resp.setResultCode(WSEnum.NO_DATA.getResultCode());
            resp.setResultInfo("暂未查询到可线上改期的客票，请致电吉祥航空客服热线95520咨询");
            return resp;
        }
        // 检查是否使用儿童免票
        List<PtIBETicketInfo> ptIBETicketInfoList = ticketListInfoResponse.getIBETicketInfoList().stream()
                .filter(ticket -> StringUtils.isBlank(ticket.getSigningInfo())
                        || !ticket.getSigningInfo().contains("儿童免票券"))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(ptIBETicketInfoList)) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("包含儿童100%机票抵扣券不支持改期");
            return resp;
        }
        // 检查是否使用军警残客票
        List<PtIBETicketInfo> gmjcTicketInfoList = ticketListInfoResponse.getIBETicketInfoList().stream()
                .filter(ticket -> CommonBaseConstants.IBE_PASSENGER_TYPE_GMJC.equals(ticket.getPassengerType()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(gmjcTicketInfoList)) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("军警残客票暂不支持与其他客票一同办理改期，请分别查询客票办理或致电吉祥航空客服热线95520办理");
            return resp;
        }
        //过滤姓名
        ptIBETicketInfoList = ticketListInfoResponse.getIBETicketInfoList();
        List<PtIBETicketInfo> filterList = new ArrayList<>();
        for (PtIBETicketInfo ptIBETicketInfo : ptIBETicketInfoList) {
            String patternStr = ticketQuery.getPassName().toUpperCase() + "(\\s*(CHD)?\\s*(\\(CHILD\\))?\\s*|\\s*(MR)?\\s*|\\s*(MS)?\\s*|\\s*INF\\s*\\(\\w{1,3}\\d{1,2}\\))";//正则表达式
            Pattern pattern = Pattern.compile(patternStr);
            Matcher matcher = pattern.matcher(ptIBETicketInfo.getPassengerName());
            if (matcher.matches()) {
                filterList.add(ptIBETicketInfo);
            }
        }
        //无效航班
        if (StringUtil.isNullOrEmpty(filterList)) {
            resp.setResultCode(WSEnum.NO_DATA.getResultCode());
            resp.setResultInfo(NAME_OR_CARD_ERROR);
            return resp;
        }
        //非人民币支付暂不支持改期
        boolean checkCurrency = filterList.stream().allMatch(ticket -> !HandlerConstants.CURRENCY_CODE.equals(ticket.getCurrencyType()));
        if (checkCurrency) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("非人民币支付客票暂不支持办理线上改期，请致电吉祥航空客服热线95520办理");
            return resp;
        } else {
            filterList = filterList.stream().filter(ticket -> HandlerConstants.CURRENCY_CODE.equals(ticket.getCurrencyType())).collect(Collectors.toList());
        }
        String cabinClassCollection = handConfig.getCabinClass();
        Map<String, AircraftModel> aircraftModelMap = FlightUtil.toAircraftModelMap(handConfig.getAircraftModel());
        List<TicketInfo> ticketInfoListResult = ChangeObjectConvert.outputTicketInfo(filterList, localCacheService, aircraftModelMap,
                cabinClassCollection, basicService, ticketListInfoResponse.getTrrLimit(), handConfig);
        List<TicketInfo> ticketInfoList = ticketInfoListResult.stream()
                .filter(ticketInfo -> StringUtil.isNullOrEmpty(ticketInfo.getNotCanUseMessage()))
                .collect(Collectors.toList());
        //无效航班
        if (StringUtil.isNullOrEmpty(ticketInfoList)) {
            if (!StringUtil.isNullOrEmpty(ticketInfoListResult)) {
                resp.setResultCode(WSEnum.NO_DATA.getResultCode());
                resp.setResultInfo(ticketInfoListResult.get(0).getNotCanUseMessage());
                return resp;
            }
            resp.setResultCode(WSEnum.NO_DATA.getResultCode());
            resp.setResultInfo("暂未查询到可线上改期的客票，如有疑问，欢迎致电吉祥航空客服热线95520咨询");
            return resp;
        }
        //票号查询查询改期规则，证件查询是不执行此方法
        if (ticketQuery.getTicketNo().matches(PatternCommon.TICKET_NO)) {
            ticketHandlerAndBuild(reqId, ip, channelCode, userNo, ticketInfoList, ticketQuery.getFfpCardNo(), ticketQuery.getFfpId());
        }
        /**
         * 2021-8-20
         * 添加购买权益券判断
         */
//        handlerCouponTicketInfoList(ip, channelCode, userNo, ticketInfoList);
        /**
         * 2021-11-19
         * 判断改期是否和同行人的航段相同
         */
        BaseResp baseResp = filterTicketFlight(ticketInfoList, ticketQuery, resp);
        handSensitiveInfo(ticketInfoList);
        return baseResp;
    }

    private Boolean checkTicke(BaseResp resp, TicketInfo ticketInfo) {
        //团队票不支持改期
        if (ticketInfo.isTeamFlag()) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("团队客票暂不支持办理线上改期，请致电吉祥航空客服热线95520办理");
            return true;
        }
        //免票兑换客票不支持自愿改期
        boolean isFreeTicket = ticketInfo.getChangeFlightInfoList().stream().allMatch(changeFlightInfo1 ->
                handConfig.getFreeTicketCabin().equals(changeFlightInfo1.getCabin()) && !changeFlightInfo1.isNotVoluntaryChange());
        if (isFreeTicket) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("兑换客票暂不支持办理线上改期，如有疑问，请致电吉祥航空客服热线95520咨询");
            return true;
        }
        //奖励飞免票兑换客票不支持自愿改期
        boolean isAwardFlyFreeTicket = ticketInfo.getChangeFlightInfoList().stream().allMatch(changeFlightInfo1 ->
                handConfig.getAwardFlyFreeTicketCabin().contains(changeFlightInfo1.getCabin()) && !changeFlightInfo1.isNotVoluntaryChange());
        if (isAwardFlyFreeTicket) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("抱歉，暂不支持您的客票办理线上改期，如有疑问，欢迎致电吉祥航空客服热线95520咨询");
            return true;
        }
        //常客兑换票，中转客票，免票兑换客票不支持改期
        String notAllowedChangeCabin = handConfig.getNotAllowedChangeCabin();
        boolean notAllow = ticketInfo.getChangeFlightInfoList().stream().anyMatch(changeFlightInfo1 ->
                notAllowedChangeCabin.contains(changeFlightInfo1.getCabin()));
        if (notAllow) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("抱歉，暂不支持您的客票办理线上改期，如有疑问，欢迎致电吉祥航空客服热线95520咨询");
            return true;
        }
        return false;
    }


    private void ticketHandlerAndBuild(String reqId, String ip, String channelCode, String userNo, List<TicketInfo> ticketInfoList, String ffpCardNo, String ffpId) {
        TicketInfo ticketInfo = ticketInfoList.get(0);
        //通过运价的方式查找变更改期手续费
        List<com.juneyaoair.mobile.mongo.entity.FlightInfo> paramList = new ArrayList<>();
        ticketInfo.getChangeFlightInfoList().stream().forEach(changeFlightInfo -> {
            com.juneyaoair.mobile.mongo.entity.FlightInfo param = new com.juneyaoair.mobile.mongo.entity.FlightInfo();
            param.setFlightNo(changeFlightInfo.getFlightNo());
            param.setFlightDate(changeFlightInfo.getDepFlightDate());
            param.setDepAirport(changeFlightInfo.getDepAirportCode());
            param.setArrAirport(changeFlightInfo.getArrAirportCode());
            paramList.add(param);
        });
        Map<String, com.juneyaoair.mobile.mongo.entity.FlightInfo> flightInfoMap = new HashMap<>();
        List<com.juneyaoair.mobile.mongo.entity.FlightInfo> baseInfoList = basicService.queryBaseFlightByList(paramList);
        baseInfoList.stream().forEach(flightInfo -> {
            flightInfoMap.put(flightInfo.getFlightNo(), flightInfo);
        });
        PtFlightFareChangeRequest ptFlightFareChangeRequest = createPtFlightFareChangeRequestByTicket(channelCode,
                userNo, ticketInfo.getInterFlag(), ticketInfo.getChangeFlightInfoList(), ticketInfo.getPassengerInfoList(),
                ticketInfo.getRouteType(), flightInfoMap, ticketInfo.getIssueDate(), ffpCardNo, ffpId, ticketInfo.getTicketNo());
        ptFlightFareChangeRequest.setIssueDate(ticketInfo.getIssueDate());
        String queryChangeFeeUrl = HandlerConstants.URL_FARE_API + HandlerConstants.QUERY_CHANGE_FARE;
        Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
        HttpResult httpResult = this.doPostClient(ptFlightFareChangeRequest, queryChangeFeeUrl, headMap,
                handConfig.getReadTimeout(), handConfig.getConnectTimeout());
        if (httpResult.isResult() && StringUtils.isNotBlank(httpResult.getResponse())) {
            try {
                PtFlightFareChangeResponse ptFlightFareChangeResponse = (PtFlightFareChangeResponse)
                        JsonUtil.jsonToBean(httpResult.getResponse(), PtFlightFareChangeResponse.class);
                Map<String, Fare> fareMap = null;
                if (UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(ptFlightFareChangeResponse.getResultCode())) {
                    if (!StringUtil.isNullOrEmpty(ptFlightFareChangeResponse.getFlightInfoList())) {
                        fareMap = ptFlightFareChangeResponse.getFareDic();//运价字典
                        if (fareMap != null) {
                            for (ChangeFlightInfo changeFlightInfo : ticketInfo.getChangeFlightInfoList()) {
                                Optional<V2FlightInfo> optional = ptFlightFareChangeResponse.getFlightInfoList().stream().filter(v ->
                                        v.getFlightNo().equals(changeFlightInfo.getFlightNo())).findFirst();
                                if (optional.isPresent()) {
                                    V2FlightInfo v2FlightInfo = optional.get();
                                    if (!StringUtil.isNullOrEmpty(v2FlightInfo.getCabinFareList())) {
                                        V2CabinFare cabinFare = v2FlightInfo.getCabinFareList().get(0);
                                        Fare fare = fareMap.get(cabinFare.getFareKey());
                                        //客票对应的改期规则
                                        List<ChangeAndRefundRule> refundRuleList = new ArrayList<>();
                                        List<ChangeAndRefundRule> changeAndRefundRuleList =
                                                AVObjectConvertV2.toChangeRules(fare.getChangeRules(), ptFlightFareChangeResponse.getInterFlag());
                                        FlightUtil.completeRule(refundRuleList, changeAndRefundRuleList);
                                        changeFlightInfo.setMealCode(v2FlightInfo.getMealCode());
                                        changeFlightInfo.setChangeRuleList(changeAndRefundRuleList);
                                    }
                                }
                            }
                        }
                    }
                }
                for (ChangePassengerInfo changePassengerInfo : ticketInfo.getPassengerInfoList()) {
                    //处理购保信息
                    if (CollectionUtils.isEmpty(changePassengerInfo.getInsuranceList())) {
                        List<InsuranceInfoNew> allInsurances = this.listTicketInsuranceInfo(changePassengerInfo.getETicketNo(), changePassengerInfo.getPassengerName(), headMap);
                        if (CollectionUtils.isNotEmpty(allInsurances)) {
                            changePassengerInfo.setInsuranceOrderNo(allInsurances.get(0).getOrderNo());
                            changePassengerInfo.setInsuranceChannelOrderNo(allInsurances.get(0).getChannelOrderNo());
                            changePassengerInfo.setIsBuyInsurance("Y");
                        }
                        changePassengerInfo.setAllInsurances(allInsurances);
                    } else {
                        changePassengerInfo.setInsuranceOrderNo(ticketInfo.getOrderNo());
                        changePassengerInfo.setInsuranceChannelOrderNo(ticketInfo.getChannelOrderNo());
                        changePassengerInfo.setIsBuyInsurance("Y");
                    }

                    if ("MI".equals(changePassengerInfo.getSex())) {
                        changePassengerInfo.setSex("M");
                    } else if ("FI".equals(changePassengerInfo.getSex())) {
                        changePassengerInfo.setSex("F");
                    } else if (HandlerConstants.PASSENGER_TYPE_INF.equals(changePassengerInfo.getPassengerType()) && StringUtils.isBlank(changePassengerInfo.getSex())) {
                        changePassengerInfo.setSex("F");
                    }
                }
            } catch (Exception e) {
                log.error("请求号:{}，IP地址:{}，客户端提交参数{}，服务端响应结果：{}", reqId, ip, JsonUtil.objectToJson(ptFlightFareChangeRequest), httpResult.getResponse());
            }
        }
    }

    /**
     * 匹配航线
     *
     * @param ticketInfoList
     * @param ticketQuery
     * @param resp
     * @return
     */
    private BaseResp filterTicketFlight(List<TicketInfo> ticketInfoList, TravelPartnerReq ticketQuery, BaseResp resp) {
        if (StringUtil.isNullOrEmpty(ticketInfoList)) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("未查询到匹配航线");
            return resp;
        }
        //请求有儿童的航线信息
        String resultMessage = "查询异常";
        for (TicketInfo ticketInfo : ticketInfoList) {
            ChangePassengerInfo changePassengerInfo = ticketInfo.getPassengerInfoList().get(0);
            String passengerType = changePassengerInfo.getPassengerType();
            String errorMessage = matchingChangeTicketInfo(ticketInfo.getChangeFlightInfoList(), ticketQuery, passengerType, ticketInfo.getInterFlag());
            if (StringUtils.isBlank(errorMessage)) {
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                //儿童请求航线为空,并且类型为儿童
                if (StringUtil.isNullOrEmpty(ticketQuery.getChdChangeFlightInfoList())
                        && "CHD".equals(passengerType)) {
                    ticketInfo.setChdChangeFlightInfoList(buildChdChangFlight(ticketInfo.getChangeFlightInfoList()));
                }
                ticketInfo.setChangeFlightInfoList(null);
                resp.setObjData(ticketInfo);
                return resp;
            } else {
                resultMessage = errorMessage;
            }
        }
        resp.setResultCode(WSEnum.ERROR.getResultCode());
        resp.setResultInfo(resultMessage);
        return resp;
    }

    /**
     * 减少传输
     *
     * @param chdChangeFlightInfoList
     * @return
     */
    private List<TravelPartnerChangeFlightInfo> buildChdChangFlight(List<ChangeFlightInfo> chdChangeFlightInfoList) {
        List<TravelPartnerChangeFlightInfo> changeFlightInfoList = new ArrayList<>();
        for (ChangeFlightInfo changeFlightInfo : chdChangeFlightInfoList) {
            TravelPartnerChangeFlightInfo changeFlight = new TravelPartnerChangeFlightInfo();
            changeFlight.setFlightDirection(changeFlightInfo.getFlightDirection());
            changeFlight.setCabinClass(changeFlightInfo.getCabinClass());
            changeFlight.setCabin(changeFlightInfo.getCabin());
            changeFlight.setTicketPrice(changeFlightInfo.getTicketPrice());
            changeFlightInfoList.add(changeFlight);
        }
        return changeFlightInfoList;
    }

    /**
     * 判断航线是否匹配
     *
     * @param userChange
     * @param travelPartnerReq
     * @param passengerType
     * @return
     */
    private String matchingChangeTicketInfo(List<ChangeFlightInfo> userChange, TravelPartnerReq travelPartnerReq, String passengerType, String interFlag) {
        List<ChangeFlightInfo> reqChange = travelPartnerReq.getChangeFlightInfoList();
        List<TravelPartnerChangeFlightInfo> reqCHDChange = travelPartnerReq.getChdChangeFlightInfoList();
        //是否为婴儿和儿童
        boolean isChdOrInf = "CHD".equals(passengerType) || "INF".equals(passengerType);
        Map<String, ChangeFlightInfo> userChangeMap = userChange.stream()
                .collect(Collectors.toMap(o -> o.getFlightDirection() + o.getDepAirportCode(), o -> o));
        if (userChange.size() != reqChange.size()) {
            return "暂不支持非相同行程客票办理线上改期，请致电吉祥航空客服热线95520办理";
        }
        //只要有一个可以改期即可
        if (!userChange.stream()
                .anyMatch(ChangeFlightInfo::isAbleChange)) {
            return "未发现可改期航班，请致电吉祥航空客服热线95520办理";
        }
        for (ChangeFlightInfo reqChangeInfo : reqChange) {
            ChangeFlightInfo userChangeInfo = userChangeMap.get(reqChangeInfo.getFlightDirection() + reqChangeInfo.getDepAirportCode());
            if (userChangeInfo == null) {
                return "暂不支持非相同行程客票办理线上改期，请致电吉祥航空客服热线95520办理";
            }
            if (!reqChangeInfo.getFlightNo().equals(userChangeInfo.getFlightNo())
                    || !reqChangeInfo.getDepAirportCode().equals(userChangeInfo.getDepAirportCode())
                    || !reqChangeInfo.getArrAirportCode().equals(userChangeInfo.getArrAirportCode())
                    || !reqChangeInfo.getDepDate().equals(userChangeInfo.getDepDate())
                    || !reqChangeInfo.getDepDateTime().equals(userChangeInfo.getDepDateTime())
                    || !reqChangeInfo.getArrDate().equals(userChangeInfo.getArrDate())
                    || !reqChangeInfo.getArrDateTime().equals(userChangeInfo.getArrDateTime())) {
                return isChdOrInf ? "仅支持与成人客票相同行程的婴儿/儿童客票一同办理改期，请核对信息后重新添加" : "暂不支持非相同行程客票办理线上改期，请致电吉祥航空客服热线95520办理";
            }
            //舱等判断
            if (!reqChangeInfo.getCabinClass().equals(userChangeInfo.getCabinClass())) {
                return isChdOrInf ? "暂不支持与成人客票不同舱位的婴儿/儿童客票一同办理改期，请致电吉祥航空客服热线95520办理" : "暂不支持非相同舱位客票办理线上改期，请致电吉祥航空客服热线95520办理";
            }
            if (!isChdOrInf && !reqChangeInfo.getCabin().equals(userChangeInfo.getCabin())) {
                return "暂不支持非相同舱位客票办理线上改期，请致电吉祥航空客服热线95520办理";
            }
            //儿童必须为y,j或者和原舱位相同
            if (isChdOrInf) {
                if (!"Y".equals(userChangeInfo.getCabin()) && !"J".equals(userChangeInfo.getCabin())
                        && !reqChangeInfo.getCabin().equals(userChangeInfo.getCabin())) {
                    return "暂不支持与成人客票不同舱位的婴儿/儿童客票一同办理改期，请致电吉祥航空客服热线95520办理";
                }
            }
            //如果为成人则匹配价格
            if ("ADT".equals(passengerType) && !reqChangeInfo.getTicketPrice().equals(userChangeInfo.getTicketPrice())) {
                return "暂不支持非相同票价的同舱位客票一同办理改期，请分别查询客票办理或致电吉祥航空客服热线95520办理";
            }
        }

        //判断是否为儿童,且有儿童舱位
        if (!StringUtil.isNullOrEmpty(reqCHDChange) && "CHD".equals(passengerType)) {
            for (TravelPartnerChangeFlightInfo reqCHDChangeInfo : reqCHDChange) {
                ChangeFlightInfo userChangeInfo = userChangeMap.get(reqCHDChangeInfo.getFlightDirection());
                //必须都相同
                if (!reqCHDChangeInfo.getCabin().equals(userChangeInfo.getCabin())
                        || !reqCHDChangeInfo.getCabinClass().equals(userChangeInfo.getCabinClass())) {
                    return "暂不支持非相同舱位的儿童客票一同办理改期，请致电吉祥航空客服热线95520办理";
                }
                //价格必须相同
                if (!reqCHDChangeInfo.getTicketPrice().equals(userChangeInfo.getTicketPrice())) {
                    return "暂不支持非相同票价的同舱位客票一同办理改期，请分别查询客票办理或致电吉祥航空客服热线95520办理";
                }
            }
        }
        return null;
    }

    @InterfaceLog
    @ApiOperation(value = "订单查询改期信息", notes = "订单查询改期信息")
    @RequestMapping(value = "/getOrderTicketInfo", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp<List<TicketInfo>> getChangeTicketByOrder(@RequestBody @Validated BaseReq<OrderQuery> req, BindingResult bindingResult, HttpServletRequest request) {
        String reqId = StringUtil.newGUID() + request.getRequestURI();
        BaseResp resp = new BaseResp();
        String ip = this.getClientIP(request);
        try {
            //参数有效性检验
            if (bindingResult.hasErrors()) {
                throw new RequestParamErrorException(bindingResult.getAllErrors().get(0).getDefaultMessage());
            }
            OrderQuery orderQuery = req.getRequest();
            String channelCode = req.getChannelCode();
            String userNo = getChannelInfo(channelCode, "10");
            PtOrderChangeRequest orderChangeRequest = new PtOrderChangeRequest(HandlerConstants.VERSION, channelCode, userNo);
            orderChangeRequest.setCustomerNo(orderQuery.getFfpId());
            orderChangeRequest.setPageNo(1);
            orderChangeRequest.setPageSize(60);
            PtOrderChangeResponse ptOrderChangeResponse = quertChangeOrderList(orderChangeRequest, ip);
            if (!UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(ptOrderChangeResponse.getResultCode())) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo(ptOrderChangeResponse.getErrorInfo());
                return resp;
            }
            Map<String, AirPortInfoDto> airPortInfoMap = basicService.queryAllAirportMap(channelCode, ip);
            List<TicketInfo> ticketInfoList = ChangeObjectConvert.changeOrderToTicketInfoList(ptOrderChangeResponse.getOrderChangeBriefList(), airPortInfoMap, handConfig);
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
            resp.setObjData(ticketInfoList);
        } catch (RequestParamErrorException e) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo(e.getMessage());
            return resp;
        } catch (Exception e) {
            log.error("请求号:{}，IP地址:{}，客户端提交参数：{}，服务端异常：", reqId, ip, JsonUtil.objectToJson(req), e);
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("请求发生错误！");
        }
        return resp;
    }

    @InterfaceLog
    @ApiOperation(value = "订单号查询待改期信息", notes = "订单号查询待改期信息")
    @RequestMapping(value = "/queryChangeOrderDetail", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp<TicketInfo> queryChangeOrderDetail(@RequestBody @Validated BaseReq<OrderDetailQuery> req, BindingResult bindingResult, HttpServletRequest request) {
        String reqId = MdcUtils.getRequestId();
        BaseResp resp = new BaseResp();
        String ip = this.getClientIP(request);
        String headChannelCode = request.getHeader(HEAD_CHANNEL_CODE);
        //参数有效性检验
        if (bindingResult.hasErrors()) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
            return resp;
        }
        OrderDetailQuery orderDetailQuery = req.getRequest();
        String channelCode = req.getChannelCode();
        String userNo = getChannelInfo(channelCode, "10");
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(orderDetailQuery.getFfpId(), orderDetailQuery.getLoginKeyInfo(), channelCode);
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        try {
            String url = HandlerConstants.URL_FARE_API + HandlerConstants.SUB_QUERY_SUB_ORDER;
            PtSubOrderReq ptSubOrderReq = new PtSubOrderReq(HandlerConstants.VERSION, channelCode, userNo);
            ptSubOrderReq.setOrderNo(orderDetailQuery.getOrderNo());
            ptSubOrderReq.setChannelOrderNo(orderDetailQuery.getChannelOrderNo());
            ptSubOrderReq.setCustomerNo(orderDetailQuery.getFfpId());
            Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
            HttpResult httpResult = this.doPostClient(ptSubOrderReq, url, headMap);
            if (httpResult.isResult()) {
                if (StringUtil.isNullOrEmpty(httpResult.getResponse())) {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo("订单信息为空！");
                    return resp;
                } else {
                    try {
                        SubOrderResp subOrderResp = (SubOrderResp) JsonUtil.jsonToBean(httpResult.getResponse(), SubOrderResp.class);
                        if (!UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(subOrderResp.getResultCode())) {
                            resp.setResultCode(WSEnum.ERROR.getResultCode());
                            resp.setResultInfo("查询结果:" + subOrderResp.getErrorInfo());
                            return resp;
                        }
                        if (CollectionUtils.isEmpty(subOrderResp.getSubtOrderBaseInfoList())) {
                            resp.setResultCode(WSEnum.ERROR.getResultCode());
                            resp.setResultInfo("查询结果:查无此单!");
                            return resp;
                        }
                        Map<String, AirPortInfoDto> airportMap = basicService.queryAllAirportMap(channelCode, ip);
                        JSONObject tags = new JSONObject();
                        tags.put("IP", ip);
                        tags.put("FfpCardNo", orderDetailQuery.getFfpCardNo());
                        tags.put("ChannelCode", headChannelCode);
                        MetricLogUtil.saveMetricLog("订单改期-客票提取", tags, new BigDecimal(1));
                        resp = queryRefundApplyInfo(subOrderResp, orderDetailQuery, channelCode, userNo, airportMap, ip, localCacheService);
                    } catch (Exception ex) {
                        log.error("请求参数：{},请求号：{}，返回结果：{}，错误信息：", JsonUtil.objectToJson(req), reqId, httpResult.getResponse(), ex);
                        resp.setResultCode(WSEnum.ERROR.getResultCode());
                        resp.setResultInfo("数据转换异常！");
                        return resp;
                    }
                }
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo(httpResult.getResponse());
                return resp;
            }
        } catch (Exception e) {
            log.error("请求号:{}，IP地址:{}，客户端提交参数：{}，服务端异常：", reqId, ip, JsonUtil.objectToJson(req), e);
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("请求发生错误！");
        }
        return resp;
    }

    @InterfaceLog
    @ApiOperation(value = "改期航班查询", notes = "改期航班查询")
    @RequestMapping(value = "/queryChangeFlight", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp<ChangeFlightFareResp> queryChangeFlight(@RequestBody @Validated BaseReq<FlightFareChangeQuery> req, BindingResult bindingResult, HttpServletRequest request) {
        String reqId = StringUtil.newGUID() + request.getRequestURI();
        BaseResp<ChangeFlightFareResp> resp = new BaseResp<>();
        String ip = this.getClientIP(request);
        //参数有效性检验
        if (bindingResult.hasErrors()) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
            return resp;
        }
        FlightFareChangeQuery flightFareQuery = req.getRequest();
        String channelCode = req.getChannelCode();
        String userNo = getChannelInfo(channelCode, "10");
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(flightFareQuery.getFfpId(), flightFareQuery.getLoginKeyInfo(), channelCode);
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        int adtCount = 0;
        //乘客分组处理,便于处理各类的手续费用
        Map<String, ChangePassengerInfo> passMap = new HashMap<>();
        for (ChangePassengerInfo changePassengerInfo : flightFareQuery.getPassengerInfoList()) {
            //每个类型的乘客只保留一个  迪士新增老人运价
            if (HandlerConstants.PASSENGER_TYPE_ADT.equals(changePassengerInfo.getPassengerType())
                    && HandlerConstants.PASSENGER_TYPE_OLD.equals(changePassengerInfo.getPassengerIdentity())) {
                passMap.put(HandlerConstants.PASSENGER_TYPE_OLD, changePassengerInfo);
            } else {
                passMap.put(changePassengerInfo.getPassengerType(), changePassengerInfo);
            }
            if (HandlerConstants.PASSENGER_TYPE_ADT.equals(changePassengerInfo.getPassengerType())
                    || HandlerConstants.PASSENGER_TYPE_GMJC.equals(changePassengerInfo.getPassengerType())) {
                adtCount++;
            }
            String tktNo = changePassengerInfo.getETicketNo();
            if (StringUtils.isBlank(tktNo)) {
                tktNo = changePassengerInfo.getTicketNo();
            }
            String info = apiRedisService.getData(RedisKeyConfig.createUpIdInfo(tktNo));
            if (StringUtils.isBlank(info)) {
                throw new ServiceException("操作超时，请返回重新查询下单");
            }
            changePassengerInfo.setCertNo(info);
        }
        //无成人不可申请改期
        if (adtCount == 0) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("儿童与婴儿不可单独改期！");
            return resp;
        }
        //改期航段
        int selectCount = 0;
        for (ChangeFlightInfo changeFlightInfo : flightFareQuery.getChangeFlightInfoList()) {
            if (changeFlightInfo.isSelectedFlag()) {
                selectCount++;
            }
        }
        if (selectCount == 0) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("请先选择要改期的航班！");
            return resp;
        }
        //获取航班舱位原票价信息
        for (ChangeFlightInfo changeFlightInfo : flightFareQuery.getChangeFlightInfoList()) {
            boolean adtFlag = false, chdFlag = false, infFlag = false, gmjcFlag = false, oldFlag = false;
            for (ChangePassengerInfo changePassengerInfo : flightFareQuery.getPassengerInfoList()) {
                CabinFare cabinFare = new CabinFare();
                for (SegmentPriceInfo segmentPriceInfo : changePassengerInfo.getSegmentPriceInfoList()) {
                    //符合的航班获取对应的价格信息
                    if (segmentPriceInfo.getFlightNo().equals(changeFlightInfo.getFlightNo())) {
                        BeanUtils.copyProperties(segmentPriceInfo, cabinFare);
                        cabinFare.setPassengerType(changePassengerInfo.getPassengerType());
                        cabinFare.setCabinCode(segmentPriceInfo.getCabin());
                        cabinFare.setYQTax(segmentPriceInfo.getYQTax() == null ? 0 : segmentPriceInfo.getYQTax());
                        cabinFare.setCNTax(segmentPriceInfo.getCNTax() == null ? 0 : segmentPriceInfo.getCNTax());
                        //国际航班总的税费差额
                        cabinFare.setXtax(segmentPriceInfo.getXTax() == null ? 0 : segmentPriceInfo.getXTax());
                        //票面价 = 实际支付价+积分抵扣+优惠券抵扣金额
                        if (!StringUtil.isNullOrEmpty(flightFareQuery.getOriginOrderNo())) {
                            //订单改期
                            cabinFare.setPriceValue(segmentPriceInfo.getPricePaid() + (segmentPriceInfo.getUseScore() == null ?
                                    0 : segmentPriceInfo.getUseScore()) + (segmentPriceInfo.getCouponAmount() == null ? 0 : segmentPriceInfo.getCouponAmount()));
                        } else {
                            //客票改
                            cabinFare.setPriceValue(segmentPriceInfo.getPricePaid() + (segmentPriceInfo.getUseScore() == null ?
                                    0 : segmentPriceInfo.getUseScore()));
                        }
                        if (HandlerConstants.PASSENGER_TYPE_ADT.equals(changePassengerInfo.getPassengerType())
                                && HandlerConstants.PASSENGER_TYPE_OLD.equals(changePassengerInfo.getPassengerIdentity()) && !oldFlag) {
                            changeFlightInfo.setOldCabinFare(cabinFare);
                            oldFlag = true;
                        } else if (HandlerConstants.PASSENGER_TYPE_ADT.equals(changePassengerInfo.getPassengerType()) && !adtFlag) {
                            changeFlightInfo.setAdtCabinFare(cabinFare);
                            adtFlag = true;
                            break;
                        } else if (HandlerConstants.PASSENGER_TYPE_CHD.equals(changePassengerInfo.getPassengerType()) && !chdFlag) {
                            changeFlightInfo.setChdCabinFare(cabinFare);
                            chdFlag = true;
                            break;
                        } else if (HandlerConstants.PASSENGER_TYPE_INF.equals(changePassengerInfo.getPassengerType()) && !infFlag) {
                            changeFlightInfo.setInfCabinFare(cabinFare);
                            infFlag = true;
                            break;
                        } else if (HandlerConstants.PASSENGER_TYPE_GMJC.equals(changePassengerInfo.getPassengerType()) && !gmjcFlag) {
                            changeFlightInfo.setGmjcCabinFare(cabinFare);
                            gmjcFlag = true;
                            break;
                        }
                    }
                }
            }
        }
        String curDateStr = DateUtils.getCurrentDateTimeStr();
        List<ChangeFlightInfo> changeFlightInfoList = flightFareQuery.getChangeFlightInfoList();
        //通过订单方式查找变更手续费
        if (StringUtils.isNotBlank(flightFareQuery.getOriginOrderNo())) {
            if (HandlerConstants.TRIP_TYPE_I.equals(flightFareQuery.getTripType())) {
                getChangeFlightIFareRespBaseResp(req, channelCode, userNo, flightFareQuery, ip, changeFlightInfoList, resp, reqId);
            } else {
                getChangeFlightFareRespBaseResp(req, channelCode, userNo, flightFareQuery, ip, changeFlightInfoList, resp, reqId);
            }
        } else {
            if (HandlerConstants.TRIP_TYPE_I.equals(flightFareQuery.getTripType())) {
                getTicketFlightFareTICKETRespBaseResp(req, flightFareQuery, channelCode, userNo, ip, resp, reqId, passMap, changeFlightInfoList, curDateStr);

            } else {
                getFlightFareRespBaseResp(req, flightFareQuery, channelCode, userNo, ip, resp, reqId, passMap, changeFlightInfoList, curDateStr);
            }
        }
        // 非自愿改期的舱位CODE key:出发机场三字码+到达机场三字码，value:原舱位
        Map<String, Set<String>> trrCabinCodesMap = new HashMap<>();
        // 非自愿改期儿童的舱位CODE key:出发机场三字码+到达机场三字码，value:原儿童舱位
        Map<String, String> chdCabinCodesMap = new HashMap<>();
        boolean isFreeTicket = false;// 是否是免票兑换非自愿改期
        for (ChangePassengerInfo changePassengerInfo : flightFareQuery.getPassengerInfoList()) {
            if (CollectionUtils.isNotEmpty(changePassengerInfo.getSegmentPriceInfoList())) {
                isFreeTicket = isFreeTicket || changePassengerInfo.getSegmentPriceInfoList().stream()
                        .anyMatch(segmentPriceInfo -> handConfig.getFreeTicketCabin().equals(segmentPriceInfo.getCabin()) || handConfig.getAwardFlyFreeTicketCabin().contains(segmentPriceInfo.getCabin()));
            }
        }
        for (ChangeFlightInfo changeFlightInfo : flightFareQuery.getChangeFlightInfoList()) {
            String deptDate = flightFareQuery.getDepartureDate();
            // 返程
            if (changeFlightInfo.getDepCityCode().equals(flightFareQuery.getArrCode())) {
                deptDate = flightFareQuery.getReturnDate();
            }
            if (changeFlightInfo.isSelectedFlag()
                    && changeFlightInfo.isNotVoluntaryChange()
                    && freeChangeFee(flightFareQuery.getTrrDateLimit(), changeFlightInfo.getDepFlightDate(), deptDate, null)) {
                String airportPair = changeFlightInfo.getDepAirportCode() + changeFlightInfo.getArrCityCode();
                Set<String> cabinCodes = trrCabinCodesMap.get(airportPair);
                if (CollectionUtils.isEmpty(cabinCodes)) {
                    cabinCodes = new HashSet<>();
                }
                cabinCodes.add(changeFlightInfo.getCabin());
                for (ChangePassengerInfo passengerInfo : flightFareQuery.getPassengerInfoList()) {
                    for (SegmentPriceInfo segmentPriceInfo : passengerInfo.getSegmentPriceInfoList()) {
                        if (airportPair.equals(segmentPriceInfo.getDepAirport() + segmentPriceInfo.getArrAirport())) {
                            cabinCodes.add(segmentPriceInfo.getCabin());
                            if (PassengerTypeEnum.CHD.getPassType().equals(passengerInfo.getPassengerType())) {
                                chdCabinCodesMap.put(airportPair, segmentPriceInfo.getCabin());
                            }
                        }
                    }
                }
                trrCabinCodesMap.put(airportPair, cabinCodes);
            }
        }
        if (HandlerConstants.TRIP_TYPE_I.equals(flightFareQuery.getTripType()) && "Y".equals(handConfig.getInternationalChange())) {
            getChangeFlightFareRespBaseResp(flightFareQuery, resp, channelCode, userNo, isFreeTicket, ip);
        } else {
            getChangeDFlightFareRespBaseResp(req, channelCode, userNo, flightFareQuery, isFreeTicket, ip, resp, changeFlightInfoList, trrCabinCodesMap, chdCabinCodesMap);
        }
        return resp;
    }

    private void getChangeDFlightFareRespBaseResp(BaseReq<FlightFareChangeQuery> req, String channelCode, String userNo, FlightFareChangeQuery flightFareQuery, boolean isFreeTicket, String ip, BaseResp<ChangeFlightFareResp> resp, List<ChangeFlightInfo> changeFlightInfoList, Map<String, Set<String>> trrCabinCodesMap, Map<String, String> chdCabinCodesMap) {
        String reqId = MdcUtils.getRequestId();
        //航班查询条件
        PtQueryFlightFareRequest queryFlightFareRequest = createQueryFareRequestV20(channelCode, userNo, flightFareQuery);
        queryFlightFareRequest.setQueryXCabin(isFreeTicket ? "Y" : "N");
        queryFlightFareRequest.setSearchUse("Y");
        queryFlightFareRequest.setOriginalReqIP(ip);
        String url = HandlerConstants.URL_FARE_API + HandlerConstants.QUERY_FLIGHT_FARE_V20;
        Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
        HttpResult httpResult = HttpUtil.doPostClient(queryFlightFareRequest, url, headMap, handConfig.getReadTimeout(), handConfig.getConnectTimeout());
        if (httpResult.isResult() && StringUtils.isNotBlank(httpResult.getResponse())) {
            PtQueryFlightFareResponse res;
            try {
                res = (PtQueryFlightFareResponse) JsonUtil.jsonToBean(httpResult.getResponse(), PtQueryFlightFareResponse.class);
            } catch (Exception e) {
                log.error("请求号:{}，IP地址:{}，客户端提交参数：{}，服务端响应结果：{}，错误信息：", reqId, ip, JsonUtil.objectToJson(req), httpResult.getResponse(), e);
                throw new ServiceException("航班查询出错！");
            }
            //航班序列
            filterCabinSeq(changeFlightInfoList, res, handConfig.getCabinSequence());
            Boolean isGmJc = flightFareQuery.getPassengerInfoList().stream().allMatch(passengerInfo -> HandlerConstants.PASSENGER_TYPE_GMJC.equals(passengerInfo.getPassengerType()));
            //航班运价结果转换
            FlightQueryBO flightQueryBO = new FlightQueryBO(isGmJc ? FlightQueryTypeEnum.POLICE_REMNANTS.getType() : FlightQueryTypeEnum.CHANGE.getType());
            flightQueryBO.setIsDisneyFlag(req.getRequest().getIsDisneyFlag());
            ChangeFlightFareResp flightFareResp = AVObjectConvertV2.toFlightFareResponse(res, handConfig,
                    templeteConfig, flightQueryBO, trrCabinCodesMap, localCacheService);
            String curDate = DateUtils.getCurrentDateTimeStr();
            //过滤航司不一致的航班
            List<FlightInfo> flightInfoList = new ArrayList<>();
            for (FlightInfo flightInfo : flightFareResp.getFlightInfoList()) {
                //起飞前1小时过滤
                if (DateUtils.filterDateTimeWithZone(curDate, "8", flightInfo.getDepDateTime(), flightInfo.getDepZone(), handConfig.getCloseSellTime())) {
                    continue;
                }
                for (ChangeFlightInfo changeFlightInfo : flightFareQuery.getChangeFlightInfoList()) {
                    //出发城市和到达城市匹配
                    if (flightInfo.getDepCity().equals(changeFlightInfo.getDepCityCode()) && flightInfo.getArrCity().equals(changeFlightInfo.getArrCityCode())) {
                        String airTwoCode = changeFlightInfo.getFlightNo().substring(0, 2);
                        if (flightInfo.getFlightNo().startsWith(airTwoCode)) {
                            if (!changeFlightInfo.isChanged()) {    //未改期过的航班可以正常改期
                                filterCabinFarePrice(flightInfo, changeFlightInfoList, changeFlightInfo.isNotVoluntaryChange());
                                flightInfoList.add(flightInfo);
                            } else if (changeFlightInfo.isNotVoluntaryChange() && freeChangeFee(flightFareQuery.getTrrDateLimit(),
                                    changeFlightInfo.getDepFlightDate(), flightInfo.getFlightDate(), flightInfo.getFlightNo())
                                    && HandlerConstants.TRIP_TYPE_D.equals(flightFareQuery.getTripType())) {
                                // 改期过的航班只能进行非正常航班改期
                                filterCabinFarePrice(flightInfo, changeFlightInfoList, changeFlightInfo.isNotVoluntaryChange());
                                flightInfoList.add(flightInfo);
                            }
                        }
                    }
                }
            }
            flightFareResp.setFlightInfoList(flightInfoList);
            //改期后航班运价信息
            for (FlightInfo flightInfo : flightFareResp.getFlightInfoList()) {
                Double minDiffPrice = null;
                boolean isFirst = true;
                //待改期航班遍历
                for (ChangeFlightInfo changeFlightInfo : flightFareQuery.getChangeFlightInfoList()) {
                    if (flightInfo.getDepCity().equals(changeFlightInfo.getDepCityCode()) && flightInfo.getArrCity().equals(changeFlightInfo.getArrCityCode())) {
                        //只有选中的改期航班进行差价计算
                        if (!changeFlightInfo.isSelectedFlag()) {
                            continue;
                        }
                        // 是否是非自愿免费改期 TRR只做国内航线
                        flightInfo.setNotVoluntaryChange(HandlerConstants.TRIP_TYPE_D.equals(flightFareQuery.getTripType())
                                && changeFlightInfo.isNotVoluntaryChange()
                                && freeChangeFee(flightFareQuery.getTrrDateLimit(), changeFlightInfo.getDepFlightDate(),
                                flightInfo.getFlightDate(), flightInfo.getFlightNo()));
                        CabinFare cabinFareAdtold = changeFlightInfo.getAdtCabinFare();
                        CabinFare cabinFareGMJCold = changeFlightInfo.getGmjcCabinFare();
                        CabinFare cabinFareCHDold = changeFlightInfo.getChdCabinFare();
                        CabinFare cabinFareINFold = changeFlightInfo.getInfCabinFare();
                        CabinFare cabinFareOldold = changeFlightInfo.getOldCabinFare();
                        // X舱只支持非自愿改期
                        if (cabinFareAdtold != null && handConfig.getFreeTicketCabin().equals(cabinFareAdtold.getCabinCode())
                                && !flightInfo.isNotVoluntaryChange()) {
                            flightInfo.setCabinFareList(Lists.newArrayList());
                            continue;
                        }
                        // 儿童X舱非自愿
                        if (null != cabinFareCHDold && handConfig.getFreeTicketCabin().equals(cabinFareCHDold.getCabinCode())) {
                            if (!flightInfo.isNotVoluntaryChange()) {
                                flightInfo.setCabinFareList(Lists.newArrayList());
                                continue;
                            } else {
                                flightFareResp.setChildCabinCode(handConfig.getFreeTicketCabin());
                            }
                        }
                        //成人舱位遍历
                        List<CabinFare> cabinFareListNew = new ArrayList<>();
                        if (CollectionUtils.isNotEmpty(flightInfo.getCabinFareList())) {
                            flightInfo.setCabinFareList(AVObjectConvertV2.filterCabin(flightInfo.getCabinFareList(),
                                    flightFareQuery.getTripType(), handConfig.getYcabinLimit(), handConfig.getJcabinLimit()));
                        } else {
                            flightInfo.setCabinFareList(AVObjectConvertV2.filterCabin(flightInfo.getCabinOLDFareList(),
                                    flightFareQuery.getTripType(), handConfig.getYcabinLimit(), handConfig.getJcabinLimit()));
                        }

                        // 非自愿改期如果没有同舱位运价，直接取原始运价，由同一订单校验舱位
                        if (flightInfo.isNotVoluntaryChange()) {
                            List<CabinFare> adtCabinFares = null;
                            if (CollectionUtils.isNotEmpty(flightInfo.getCabinFareList())) {
                                adtCabinFares = Lists.newArrayList(flightInfo.getCabinFareList());
                            } else {
                                adtCabinFares = Lists.newArrayList(flightInfo.getCabinOLDFareList());
                            }

                            // X舱非自愿改期改至X舱，需校验剩余座位数量
                            if (handConfig.getFreeTicketCabin().equals(cabinFareAdtold.getCabinCode())) {
                                flightInfo.setCabinFareList(adtCabinFares.stream().filter(cabin ->
                                        handConfig.getFreeTicketCabin().equals(cabin.getCabinCode())
                                                && !"0".equals(cabin.getCabinNumber()) && !"S".equals(cabin.getCabinNumber())).collect(Collectors.toList()));
                                if(CollectionUtils.isEmpty(flightInfo.getCabinFareList())){
                                    fetchFreeCabin(flightInfo,adtCabinFares,cabinFareAdtold);
                                }
                            } else {
                                //获取同等舱位运价
                                fetchFreeCabin(flightInfo,adtCabinFares,cabinFareAdtold);
                            }
                        }
                        List<CabinFare> cabinFareList = null;
                        if (cabinFareAdtold == null && cabinFareOldold != null) {
                            cabinFareList = flightInfo.getCabinOLDFareList();
                        } else {
                            cabinFareList = flightInfo.getCabinFareList();
                        }
                        if (cabinFareAdtold != null) {
                            String oldFlight = changeFlightInfo.getDepFlightDate() + changeFlightInfo.getFlightNo() + cabinFareAdtold.getCabinCode();
                            for (CabinFare cabinFare : cabinFareList) {
                                //剔除原始的航班舱位信息
                                if (oldFlight.equals(flightInfo.getFlightDate() + flightInfo.getFlightNo() + cabinFare.getCabinCode())) {
                                    continue;
                                }
                                cabinFare.setChangeServiceCharge(cabinFareAdtold.getChangeServiceCharge());// 服务手续费
                                //IRR改期差价为0
                                if (flightInfo.isNotVoluntaryChange()) {
                                    //非自愿改期只能改同舱位
                                    if (!changeFlightInfo.getCabin().equals(cabinFare.getCabinCode())) {
                                        continue;
                                    }
                                    // 非自愿改期的运价取原运价
                                    BeanUtils.copyNotNullProperties(changeFlightInfo.getCabin(), cabinFare);
                                    cabinFare.setCnTaxDiff(0);
                                    cabinFare.setYqTaxDiff(0);
                                    cabinFare.setTicketPriceDiff(0);
                                    cabinFare.setTotalDiff(0);
                                    cabinFare.setTaxDiff(0);
                                    cabinFare.setChangeServiceCharge(0);
                                } else {
                                    if (HandlerConstants.TRIP_TYPE_D.equals(flightFareQuery.getTripType())) {
                                        cabinFare.setCnTaxDiff(cabinFare.getCNTax() - cabinFareAdtold.getCNTax() < 0 ?
                                                0 : cabinFare.getCNTax() - cabinFareAdtold.getCNTax());
                                        cabinFare.setYqTaxDiff(cabinFare.getYQTax() - cabinFareAdtold.getYQTax() < 0 ?
                                                0 : cabinFare.getYQTax() - cabinFareAdtold.getYQTax());
                                    }

                                    double couponAmount = cabinFareAdtold.getCouponAmount();
                                    //订单查询原票价(包含优惠券价格),客票查询(不优惠券价格)
                                    double priceValue = (cabinFareAdtold.getPriceValue()
                                            - (StringUtils.isNotBlank(flightFareQuery.getOriginOrderNo()) ? couponAmount : 0));
                                    cabinFare.setTicketPriceDiff(cabinFare.getPriceValue() - priceValue < 0 ? 0 : cabinFare.getPriceValue() - priceValue);

                                    cabinFare.setTotalDiff(cabinFare.getChangeServiceCharge() + cabinFare.getTaxDiff() +
                                            cabinFare.getYqTaxDiff() + cabinFare.getCnTaxDiff() + cabinFare.getTicketPriceDiff());
                                }
                                if (isFirst) {
                                    minDiffPrice = cabinFare.getTotalDiff();
                                    isFirst = false;
                                } else {
                                    if (cabinFare.getTotalDiff() < minDiffPrice && minDiffPrice > 0) {
                                        minDiffPrice = cabinFare.getTotalDiff();
                                    }
                                }
                                cabinFareListNew.add(cabinFare);
                            }
                            flightInfo.setMinDiffPrice(minDiffPrice);
                            flightInfo.setCabinFareList(cabinFareListNew);
                        }
                        if (cabinFareOldold != null) {
                            List<CabinFare> cabinOldFareListNew = new ArrayList<>();
                            for (CabinFare cabinFare : flightInfo.getCabinOLDFareList()) {
                                cabinFare.setChangeServiceCharge(cabinFareOldold.getChangeServiceCharge());// 服务手续费
                                if (HandlerConstants.TRIP_TYPE_D.equals(flightFareQuery.getTripType())) {
                                    cabinFare.setCnTaxDiff(cabinFare.getCNTax() - cabinFareOldold.getCNTax() < 0 ?
                                            0 : cabinFare.getCNTax() - cabinFareOldold.getCNTax());
                                    cabinFare.setYqTaxDiff(cabinFare.getYQTax() - cabinFareOldold.getYQTax() < 0 ?
                                            0 : cabinFare.getYQTax() - cabinFareOldold.getYQTax());
                                }

                                cabinFare.setTicketPriceDiff(cabinFare.getPriceValue() - cabinFareOldold.getPriceValue() < 0 ?
                                        0 : cabinFare.getPriceValue() - cabinFareOldold.getPriceValue());

                                cabinFare.setTotalDiff(cabinFare.getChangeServiceCharge() + cabinFare.getTaxDiff() +
                                        cabinFare.getYqTaxDiff() + cabinFare.getCnTaxDiff() + cabinFare.getTicketPriceDiff());
                                cabinOldFareListNew.add(cabinFare);
                            }
                            flightInfo.setMinDiffPrice(minDiffPrice);
                            flightInfo.setCabinOLDFareList(cabinOldFareListNew);
                        }
                        //军残警残
                        if (cabinFareGMJCold != null) {
                            List<CabinFare> cabinGMJCFareListNew = new ArrayList<>();
                            for (CabinFare gmjcCabinFare : flightInfo.getCabinGMJCFareList()) {
                                gmjcCabinFare.setChangeServiceCharge(cabinFareGMJCold.getChangeServiceCharge());
                                gmjcCabinFare.setTicketPriceDiff(gmjcCabinFare.getPriceValue() - cabinFareGMJCold.getPriceValue() < 0 ?
                                        0 : gmjcCabinFare.getPriceValue() - cabinFareGMJCold.getPriceValue());
                                if (HandlerConstants.TRIP_TYPE_D.equals(flightFareQuery.getTripType())) {
                                    gmjcCabinFare.setCnTaxDiff(gmjcCabinFare.getCNTax() - cabinFareGMJCold.getCNTax() < 0 ?
                                            0 : gmjcCabinFare.getCNTax() - cabinFareGMJCold.getCNTax());
                                    gmjcCabinFare.setYqTaxDiff(gmjcCabinFare.getYQTax() - cabinFareGMJCold.getYQTax() < 0 ?
                                            0 : gmjcCabinFare.getYQTax() - cabinFareGMJCold.getYQTax());
                                }
                                gmjcCabinFare.setTotalDiff(gmjcCabinFare.getChangeServiceCharge() + gmjcCabinFare.getTaxDiff()
                                        + gmjcCabinFare.getYqTaxDiff() + gmjcCabinFare.getCnTaxDiff() + gmjcCabinFare.getTicketPriceDiff());
                                if (isFirst) {
                                    minDiffPrice = gmjcCabinFare.getTotalDiff();
                                    isFirst = false;
                                } else {
                                    if (gmjcCabinFare.getTotalDiff() < minDiffPrice && minDiffPrice > 0) {
                                        minDiffPrice = gmjcCabinFare.getTotalDiff();
                                    }
                                }
                                cabinGMJCFareListNew.add(gmjcCabinFare);
                            }
                            flightInfo.setMinDiffPrice(minDiffPrice);
                            flightInfo.setCabinGMJCFareList(cabinGMJCFareListNew);
                            flightInfo.setCabinFareList(cabinGMJCFareListNew);
                        }
                        if (cabinFareCHDold != null || cabinFareINFold != null) {
                            List<CabinFare> cabinFareListNewCHDINF = new ArrayList<>();
                            // 非自愿改期如果没有儿童、婴儿运价，直接取原始运价，由同一订单校验舱位
                            List<CabinFare> chdCabinFares = Lists.newArrayList(flightInfo.getCabinCHDINFFareList());
                            if (cabinFareCHDold != null && flightInfo.isNotVoluntaryChange()) {
                                // X舱非自愿改期需校验X舱剩余座位数
                                if (handConfig.getFreeTicketCabin().equals(cabinFareCHDold.getCabinCode())) {
                                    chdCabinFares = chdCabinFares.stream().filter(cabin -> handConfig.getFreeTicketCabin().equals(cabin.getCabinCode())
                                            && !"0".equals(cabin.getCabinNumber()) && !"S".equals(cabin.getCabinNumber())).collect(Collectors.toList());
                                    if (CollectionUtils.isEmpty(chdCabinFares)) {
                                        flightInfo.setCabinFareList(Lists.newArrayList());
                                    }
                                    //奖励飞 i，n校验F
                                } else if (handConfig.getAwardFlyFreeTicketCabin().contains(cabinFareCHDold.getCabinCode())) {
                                    chdCabinFares = chdCabinFares.stream().filter(cabin -> handConfig.getAwardFlyFreeTicketCabin().contains(cabin.getCabinCode())
                                            && !"0".equals(cabin.getCabinNumber()) && !"S".equals(cabin.getCabinNumber())).collect(Collectors.toList());
                                    if (CollectionUtils.isEmpty(chdCabinFares)) {
                                        flightInfo.setCabinFareList(Lists.newArrayList());
                                    }
                                } else {
                                    Optional<CabinFare> containsCabinClass = chdCabinFares.stream().filter(cabin ->
                                            PassengerTypeEnum.CHD.getPassType().equals(cabin.getPassengerType()) && cabin.getCabinClass().equals(CommonUtil.getCabinClassByCabinCode(cabinFareCHDold.getCabinCode(), handConfig.getCabinClass()))).findFirst();
                                    if (containsCabinClass.isPresent()) { // 同等舱位还有未售罄
                                        Optional<CabinFare> chdOptional = chdCabinFares.stream().filter(cabin ->
                                                PassengerTypeEnum.CHD.getPassType().equals(cabin.getPassengerType()) && cabin.getCabinCode().equals(cabinFareCHDold.getCabinCode())).findFirst();
                                        if (!chdOptional.isPresent()) { // 同舱位已售罄
                                            CabinFare cabinFare = new CabinFare();
                                            BeanUtils.copyNotNullProperties(cabinFareCHDold, cabinFare);
                                            chdCabinFares.add(cabinFare);
                                        }
                                    }
                                }
                            }
                            flightInfo.setCabinCHDINFFareList(chdCabinFares.toArray(new CabinFare[chdCabinFares.size()]));
                            for (int i = 0; i < flightInfo.getCabinCHDINFFareList().length; i++) {
                                CabinFare cabinFare = flightInfo.getCabinCHDINFFareList()[i];
                                CabinFare temp = null;
                                if (HandlerConstants.PASSENGER_TYPE_CHD.equals(cabinFare.getPassengerType())) {
                                    if (cabinFareCHDold != null) {
                                        temp = cabinFareCHDold;
                                    }
                                } else {
                                    if (cabinFareINFold != null) {
                                        temp = cabinFareINFold;
                                    }
                                }
                                if (temp == null) {
                                    continue;
                                }
                                cabinFare.setChangeServiceCharge(temp.getChangeServiceCharge());
                                if (HandlerConstants.TRIP_TYPE_D.equals(flightFareQuery.getTripType())) {
                                    cabinFare.setCnTaxDiff(cabinFare.getCNTax() - temp.getCNTax() < 0 ? 0 : cabinFare.getCNTax() - temp.getCNTax());
                                    cabinFare.setYqTaxDiff(cabinFare.getYQTax() - temp.getYQTax() < 0 ? 0 : cabinFare.getYQTax() - temp.getYQTax());
                                }

                                double couponAmount = temp.getCouponAmount();
                                //订单查询原票价(包含优惠券价格),客票查询(不优惠券价格)
                                double priceValue = (temp.getPriceValue()
                                        - (StringUtils.isNotBlank(flightFareQuery.getOriginOrderNo()) ? couponAmount : 0));
                                cabinFare.setTicketPriceDiff(cabinFare.getPriceValue() - priceValue < 0 ? 0 : cabinFare.getPriceValue() - priceValue);

                                cabinFare.setTotalDiff(cabinFare.getChangeServiceCharge() + cabinFare.getTaxDiff()
                                        + cabinFare.getYqTaxDiff() + cabinFare.getCnTaxDiff() + cabinFare.getTicketPriceDiff());
                                if (flightInfo.isNotVoluntaryChange()) {
                                    //非自愿改期儿童改为同舱
                                    if (!cabinFare.getCabinCode().equals(chdCabinCodesMap.get(changeFlightInfo.getDepAirportCode() + changeFlightInfo.getArrAirportCode()))) {
                                        continue;
                                    }
                                    // 非自愿改期的运价取原运价
                                    BeanUtils.copyNotNullProperties(changeFlightInfo.getCabin(), cabinFare);
                                    cabinFare.setCnTaxDiff(0);
                                    cabinFare.setYqTaxDiff(0);
                                    cabinFare.setTicketPriceDiff(0);
                                    cabinFare.setTotalDiff(0);
                                    cabinFare.setTaxDiff(0);
                                    cabinFare.setChangeServiceCharge(0);
                                }
                                cabinFareListNewCHDINF.add(cabinFare);
                            }
                            CabinFare[] CHDINFFare = new CabinFare[cabinFareListNewCHDINF.size()];
                            flightInfo.setCabinCHDINFFareList(cabinFareListNewCHDINF.toArray(CHDINFFare));
                        }
                    }
                }
            }

            resp.setObjData(flightFareResp);
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
        } else {
            throw new ServiceException("未查询到航班运价信息！");
        }
    }

    /**
     * @description获取非自愿舱位
     **/
    private void fetchFreeCabin(FlightInfo flightInfo,List<CabinFare> adtCabinFares,CabinFare cabinFareAdtold) {
        //获取同等舱位运价
        Optional<CabinFare> containsCabinClass = adtCabinFares.stream().filter(cabin ->
                PassengerTypeEnum.ADT.getPassType().equals(cabin.getPassengerType())
                        && cabin.getCabinClass().equals(CommonUtil.getCabinClassByCabinCode(cabinFareAdtold.getCabinCode(), handConfig.getCabinClass()))).findFirst();
        if (containsCabinClass.isPresent()) { // 同等舱位还有未售罄
            Optional<CabinFare> optional = adtCabinFares.stream().filter(cabin ->
                    PassengerTypeEnum.ADT.getPassType().equals(cabin.getPassengerType()) && cabin.getCabinCode().equals(cabinFareAdtold.getCabinCode())).findFirst();
            if (!optional.isPresent()) { // 同舱位售罄
                CabinFare cabinFare = new CabinFare();
                BeanUtils.copyNotNullProperties(ObjectUtil.isNotEmpty(cabinFareAdtold) ? cabinFareAdtold : cabinFareAdtold, cabinFare);
                adtCabinFares.add(cabinFare);
            }
            flightInfo.setCabinFareList(adtCabinFares);
        }
    }

    private void getChangeFlightFareRespBaseResp(FlightFareChangeQuery flightFareQuery, BaseResp<ChangeFlightFareResp> resp, String channelCode, String userNo, boolean isFreeTicket, String ip) {
        boolean notVoluntaryChange = flightFareQuery.getChangeFlightInfoList().stream().anyMatch(ChangeFlightInfo::isNotVoluntaryChange);
//            //国际改期不支持非自愿改
        if (!notVoluntaryChange) {
            throw new ServiceException("当日无航班或航班已售磬");
        }
        //选中的改期航班
        List<ChangeFlightInfo> selectedchangeFlightList = flightFareQuery.getChangeFlightInfoList().stream().filter(ChangeFlightInfo::isSelectedFlag).collect(Collectors.toList());
        //中转航班
        List<ChangeFlightInfo> goChangeFlightInfo = selectedchangeFlightList
                .stream().filter(segmentInfo -> FlightDirection.GO.getCode().equals(segmentInfo.getFlightDirection())).collect(Collectors.toList());
        //中转航班
        List<ChangeFlightInfo> backChangeFlightInfo = selectedchangeFlightList
                .stream().filter(segmentInfo -> FlightDirection.BACK.getCode().equals(segmentInfo.getFlightDirection())).collect(Collectors.toList());
        String fareType = FareTypeEnum.SIMPLE.getFare();
        if (goChangeFlightInfo.size() > 1) {
            fareType = FareTypeEnum.ADDON.getFare();
        }
        PtQueryFlightFareRequest queryFlightFareRequest = createQueryFareRequest(channelCode, userNo, flightFareQuery, goChangeFlightInfo, backChangeFlightInfo, selectedchangeFlightList);
        queryFlightFareRequest.setQueryXCabin(isFreeTicket ? "Y" : "N");
        queryFlightFareRequest.setSearchUse("Y");
        queryFlightFareRequest.setOriginalReqIP(ip);
        String url = HandlerConstants.URL_FARE_API + HandlerConstants.QUERY_SHOP_SEARCHONE;
        Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
        HttpResult httpResult = HttpUtil.doPostClient(queryFlightFareRequest, url, headMap, handConfig.getReadTimeout(), handConfig.getConnectTimeout());
        if (!httpResult.isResult() || StringUtils.isBlank(httpResult.getResponse())) {
            throw new OperationFailedException(NETWORK_ERROR_MESSAGE);
        }
        PtQueryFlightFareResponse res;
        try {
            res = (PtQueryFlightFareResponse) JsonUtil.jsonToBean(httpResult.getResponse(), PtQueryFlightFareResponse.class);
            ChangeFlightFareResp changeFlightFareResp = AVObjectConvertV3.toFlightFareAddOnChengResponse(res, handConfig, null, localCacheService, fareType);
            List<FlightInfoComb> flightInfoCombList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(changeFlightFareResp.getTransferFlightInfoList())) {
                for (FlightInfoComb flightInfoComb : changeFlightFareResp.getTransferFlightInfoList()) {
                    boolean isHOFlightInfo = flightInfoComb.getCombFlightInfoList().stream().
                            allMatch(flightInfo -> flightInfo.getCarrierNo().startsWith(AirCompanyEnum.HO.getAirCompanyCode()));
                    //必须为吉祥航班,航段数量保持一至
                    if (isHOFlightInfo && (flightInfoComb.getCombFlightInfoList().size() == selectedchangeFlightList.size())) {
                        flightInfoCombList.add(flightInfoComb);
                    }
                }
            } else {
                resp.setObjData(changeFlightFareResp);
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
            }
            changeFlightFareResp.setTransferFlightInfoList(flightInfoCombList);
            if (CollectionUtils.isNotEmpty(changeFlightFareResp.getFlightInfoList())) {
                List<FlightInfo> flightInfoList = changeFlightFareResp.getFlightInfoList();
                flightInfoList = flightInfoList.stream()
                        .filter(flightInfo -> {
                            if (StringUtils.isNotBlank(flightInfo.getCarrierNo())) {
                                String[] carrierNos = flightInfo.getCarrierNo().split("-");
                                List<String> carrierNoList = Arrays.asList(carrierNos);
                                return carrierNoList.stream().allMatch(carrierNo -> carrierNo.startsWith(AirCompanyEnum.HO.getAirCompanyCode()));
                            }
                            return false;
                        })
                        .filter(flightInfo -> {
                            if (HandlerConstants.ROUTE_TYPE_OW.equals(flightFareQuery.getRouteType())) {
                                if (goChangeFlightInfo.size() > 1) {
                                    return FareTypeEnum.ADDON.getFare().equals(flightInfo.getFareType());
                                } else {
                                    return FareTypeEnum.SIMPLE.getFare().equals(flightInfo.getFareType());
                                }
                            } else if (HandlerConstants.ROUTE_TYPE_CT.equals(flightFareQuery.getRouteType())) {
                                return FareTypeEnum.ADDON.getFare().equals(flightInfo.getFareType());
                            } else if (HandlerConstants.ROUTE_TYPE_RT.equals(flightFareQuery.getRouteType())
                                    && (goChangeFlightInfo.size() > 1 || backChangeFlightInfo.size() > 1)) {
                                return FareTypeEnum.ADDON.getFare().equals(flightInfo.getFareType());
                            } else {
                                return FareTypeEnum.SIMPLE.getFare().equals(flightInfo.getFareType());
                            }
                        }).collect(Collectors.toList());
                //往返,返程过滤非吉祥航班
                flightInfoList.forEach(flightInfo -> {
                    if (CollectionUtils.isNotEmpty(flightInfo.getFlightInfoReturnList())) {
                        List<FlightInfo> flightInfoReturnList = flightInfo.getFlightInfoReturnList().stream()
                                .filter(flightInfo1 -> {
                                    if (StringUtils.isNotBlank(flightInfo1.getCarrierNo())) {
                                        String[] carrierNos = flightInfo1.getCarrierNo().split("-");
                                        List<String> carrierNoList = Arrays.asList(carrierNos);
                                        if (backChangeFlightInfo.size() > 1 && !(carrierNoList.size() > 1)) {
                                            return false;
                                        }
                                        return carrierNoList.stream().allMatch(carrierNo -> carrierNo.startsWith(AirCompanyEnum.HO.getAirCompanyCode()));
                                    }
                                    return false;
                                }).filter(flightInfo1 -> {
                                            if (HandlerConstants.ROUTE_TYPE_OW.equals(flightFareQuery.getRouteType())) {
                                                if (backChangeFlightInfo.size() > 1) {
                                                    return FareTypeEnum.ADDON.getFare().equals(flightInfo1.getFareType());
                                                } else {
                                                    return FareTypeEnum.SIMPLE.getFare().equals(flightInfo1.getFareType());
                                                }
                                            }
                                            if (HandlerConstants.ROUTE_TYPE_RT.equals(flightFareQuery.getRouteType())
                                                    && (goChangeFlightInfo.size() > 1 || backChangeFlightInfo.size() > 1)) {
                                                return FareTypeEnum.ADDON.getFare().equals(flightInfo1.getFareType());
                                            } else {
                                                return FareTypeEnum.SIMPLE.getFare().equals(flightInfo1.getFareType());
                                            }
                                        }
                                ).collect(Collectors.toList());

                        flightInfo.setFlightInfoReturnList(flightInfoReturnList);
                    }
                });
                //中转航班第一段延误的情况下，第二段也是要返回true的
                for (FlightInfo flightInfo : flightInfoList) {
                    flightInfo.setNotVoluntaryChange(true);
                }
                changeFlightFareResp.setFlightInfoList(flightInfoList);

            }
            List<CabinFare> adtCabinFares = selectedchangeFlightList.stream().map(ChangeFlightInfo::getAdtCabinFare).collect(Collectors.toList());
            List<CabinFare> chdCabinFares = selectedchangeFlightList.stream().map(ChangeFlightInfo::getChdCabinFare).collect(Collectors.toList());
            List<CabinFare> infCabinFares = selectedchangeFlightList.stream().map(ChangeFlightInfo::getInfCabinFare).collect(Collectors.toList());
            for (FlightInfoComb flightInfoComb : changeFlightFareResp.getTransferFlightInfoList()) {
                if (CollectionUtils.isNotEmpty(flightFareQuery.getPassengerInfoList())) {
                    List<CabinFare> adtCabinFareList = new ArrayList<>(flightInfoComb.getAdtCabinFareList());
                    List<CabinFare> chdCabinFareList = new ArrayList<>(flightInfoComb.getChdCabinFareList());
                    List<CabinFare> infCabinFareList = new ArrayList<>(flightInfoComb.getInfCabinFareList());
                    flightFareQuery.getPassengerInfoList().forEach(passengerInfo -> {
                        if (PassengerTypeEnum.ADT.getPassType().equals(passengerInfo.getPassengerType())) {
                            List<CabinFare> cabinFareAdtListNew = getCabinFares(passengerInfo, adtCabinFares, adtCabinFareList);
                            flightInfoComb.setAdtCabinFareList(cabinFareAdtListNew);
                        }
                        if (PassengerTypeEnum.CHD.getPassType().equals(passengerInfo.getPassengerType())) {
                            List<CabinFare> cabinFareListNew = getCabinFares(passengerInfo, chdCabinFares, chdCabinFareList);
                            flightInfoComb.setChdCabinFareList(cabinFareListNew);
                        }
                        if (PassengerTypeEnum.INF.getPassType().equals(passengerInfo.getPassengerType())) {
                            List<CabinFare> cabinFareListNew = getCabinFares(passengerInfo, infCabinFares, infCabinFareList);
                            flightInfoComb.setInfCabinFareList(cabinFareListNew);
                        }
                    });

                    List<String> passengerTypes = flightFareQuery.getPassengerInfoList().stream().map(ChangePassengerInfo::getPassengerType).collect(Collectors.toList());
                    if (!passengerTypes.contains(PassengerTypeEnum.ADT.getPassType())) {
                        flightInfoComb.setAdtCabinFareList(new ArrayList<>());
                    }
                    if (!passengerTypes.contains(PassengerTypeEnum.CHD.getPassType())) {
                        flightInfoComb.setChdCabinFareList(new ArrayList<>());
                    }
                    if (!passengerTypes.contains(PassengerTypeEnum.INF.getPassType())) {
                        flightInfoComb.setInfCabinFareList(new ArrayList<>());
                    }
                }
                flightInfoComb.getCombFlightInfoList().forEach(cabinFare -> cabinFare.setNotVoluntaryChange(notVoluntaryChange));

            }

            boolean soldOut = false;
            if (CollectionUtils.isNotEmpty(changeFlightFareResp.getTransferFlightInfoList())) {
                for (FlightInfoComb flightInfoComb : changeFlightFareResp.getTransferFlightInfoList()) {
                    if (CollectionUtils.isNotEmpty(flightInfoComb.getAdtCabinFareList())) {
                        soldOut = true;
                        break;
                    }
                }
            }
            if (soldOut) {
                //过滤航司不一致的航班
                resp.setObjData(changeFlightFareResp);
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
            } else {

                resp.setResultCode(WSEnum.NO_DATA.getResultCode());
                resp.setResultInfo("当日无航班或航班已售磬");
            }

        } catch (Exception e) {
            throw new OperationFailedException("航班查询出错!", e);
        }
    }

    private @NotNull List<CabinFare> getCabinFares(ChangePassengerInfo passengerInfo, List<CabinFare> adtCabinFares, List<CabinFare> adtCabinFareList) {
        List<CabinFare> cabinFareAdtListNew = new ArrayList<>();
        String cabinClass = adtCabinFares.stream().map(CabinFare::getCabinClass).collect(Collectors.joining(","));
        List<String> brandCodes = passengerInfo.getSegmentPriceInfoList().stream()
                .map(SegmentPriceInfo::getBrandCode).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        double priceValue = adtCabinFares.stream().mapToDouble(CabinFare::getPriceValue).sum();
        //返回低于原来舱位的信息
        for (CabinFare cabinFare : adtCabinFareList) {
            if (checkCabin(cabinClass, cabinFare) && (brandCodes.contains(cabinFare.getBrandCode()) ||
                    (CollectionUtils.isEmpty(brandCodes) || StringUtils.isBlank(cabinFare.getBrandCode())))) {
                cabinFare.setCnTaxDiff(0);
                cabinFare.setYqTaxDiff(0);
                cabinFare.setTicketPriceDiff(0);
                cabinFare.setTotalDiff(0);
                cabinFare.setTaxDiff(0);
                cabinFare.setChangeServiceCharge(0);
                if (adtCabinFares.size() == 4) {
                    cabinFare.setPriceValueComb((adtCabinFares.get(0).getPriceValue() + adtCabinFares.get(1).getPriceValue())
                            + "/" + (adtCabinFares.get(2).getPriceValue() + adtCabinFares.get(3).getPriceValue()));
                } else if (adtCabinFares.size() == 2) {
                    cabinFare.setPriceValueComb(adtCabinFares.get(0).getPriceValue()
                            + "/" + adtCabinFares.get(1).getPriceValue());
                } else {
                    cabinFare.setPriceValueComb(String.valueOf(priceValue));
                }
                cabinFare.setPriceValue(priceValue);
                cabinFare.setRSP(priceValue);
                cabinFareAdtListNew.add(cabinFare);
                break;
            }

        }
        return cabinFareAdtListNew;
    }


    private boolean checkCabin(String cabins, com.juneyaoair.baseclass.response.av.CabinFare cabinFare) {
        if (StringUtils.isNotBlank(cabinFare.getCabinClass())) {
            String cabinString = cabinFare.getCabinClass().replaceAll("-", ",")
                    .replaceAll("/", ",").replaceAll("C", "J");

            return cabins.equals(cabinString);
        }
        return false;
    }

    private void getTicketFlightFareTICKETRespBaseResp(BaseReq<FlightFareChangeQuery> req, FlightFareChangeQuery flightFareQuery, String channelCode, String userNo, String ip, BaseResp<ChangeFlightFareResp> resp, String reqId, Map<String, ChangePassengerInfo> passMap, List<ChangeFlightInfo> changeFlightInfoList, String curDateStr) {
        Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
        CalculateChangeRequest calculateChangeRequest = new CalculateChangeRequest(channelCode, userNo, HandlerConstants.VERSION);
        Map<String, ChangePassengerInfo> passGroup = new HashMap<>();
        for (ChangePassengerInfo changePassengerInfo : flightFareQuery.getPassengerInfoList()) {
            passGroup.put(changePassengerInfo.getPassengerType(), changePassengerInfo);
        }
        List<String> passTypeList = new ArrayList<>();
        for (String key : passGroup.keySet()) {
            passTypeList.add(key);
        }
        List<SengmentChangeDto> flightChangeList = new ArrayList();
        if (CollectionUtils.isNotEmpty(flightFareQuery.getChangeFlightInfoList())) {
            flightFareQuery.getChangeFlightInfoList().forEach(changeFlightInfo -> {
                SengmentChangeDto sengmentChangeDto = new SengmentChangeDto(changeFlightInfo.getDepCityCode(), changeFlightInfo.getArrCityCode(), changeFlightInfo.getDepFlightDate(), changeFlightInfo.getFlightNo());
                sengmentChangeDto.setArrAirport(changeFlightInfo.getArrAirportCode());
                sengmentChangeDto.setDepAirport(changeFlightInfo.getDepAirportCode());
                sengmentChangeDto.setInssueDate(req.getRequest().getIssueDate());
                flightChangeList.add(sengmentChangeDto);
            });
        }
        Map<String, String> tickerNoName = new HashMap<>();
        flightFareQuery.getPassengerInfoList().stream().forEach(changePassengerInfo -> {
            tickerNoName.put(changePassengerInfo.getETicketNo(), changePassengerInfo.getPassengerName());
        });
        //获取改期乘客证件信息
        List<String> certList = flightFareQuery.getPassengerInfoList().stream().map(ChangePassengerInfo::getCertNo).collect(Collectors.toList());
        calculateChangeRequest.setFlightChangeList(flightChangeList);
        calculateChangeRequest.setPassengerTypes(passTypeList);
        calculateChangeRequest.setChannelOrderNo(flightFareQuery.getOriginChannelOrderNo());
        calculateChangeRequest.setOrderNo(flightFareQuery.getOriginOrderNo());
        calculateChangeRequest.setFfpCardNo(flightFareQuery.getFfpCardNo());
        calculateChangeRequest.setFfpId(flightFareQuery.getFfpId());
        calculateChangeRequest.setCertificates(certList);
        calculateChangeRequest.setTickerNoName(tickerNoName);
        String queryChangeFeeUrl = HandlerConstants.URL_FARE_API + HandlerConstants.INTER_CALCULATE_TICKET_CHANGEFEE;
        HttpResult httpResult = HttpUtil.doPostClient(calculateChangeRequest, queryChangeFeeUrl, headMap, handConfig.getReadTimeout(), handConfig.getConnectTimeout());
        if (StringUtils.isNotBlank(httpResult.getResponse())) {
            CalculateChangeResponse calculateChangeResponse = (CalculateChangeResponse)
                    JsonUtil.jsonToBean(httpResult.getResponse(), CalculateChangeResponse.class);
            if (!UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(calculateChangeResponse.getResultCode())) {
                throw new ServiceException(calculateChangeResponse.getErrorInfo());
            }
            if (StringUtil.isNullOrEmpty(calculateChangeResponse.getInterChangeFeeList())) {
                throw new ServiceException("该航班暂不支持线上改期");
            }
            if (UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(calculateChangeResponse.getResultCode())) {
                for (ChangeFlightInfo changeFlightInfo : changeFlightInfoList) {
                    if (CollectionUtils.isNotEmpty(calculateChangeResponse.getInterChangeFeeList())) {
                        List<InterChangeFeeDto> interChangeFeeDtos = calculateChangeResponse.getInterChangeFeeList();
                        interChangeFeeDtos.forEach(interChangeFeeDto -> {
                            List<com.juneyaoair.baseclass.response.order.InterchangeFee.SengmentChangeDto> segmentList = interChangeFeeDto.getSegmentList();
                            if (CollectionUtils.isNotEmpty(segmentList)) {
                                segmentList.forEach(segment -> {
                                    if (changeFlightInfo.getDepAirportCode().equals(segment.getDepAirport())
                                            && changeFlightInfo.getArrAirportCode().equals(segment.getArrAirport())) {
                                        if (HandlerConstants.PASSENGER_TYPE_ADT.equals(interChangeFeeDto.getPassengerType())) {
                                            changeFlightInfo.getAdtCabinFare().setChangeServiceCharge(segment.getChangeFee().doubleValue());
                                        }
                                        if (HandlerConstants.PASSENGER_TYPE_CHD.equals(interChangeFeeDto.getPassengerType())) {
                                            changeFlightInfo.getChdCabinFare().setChangeServiceCharge(segment.getChangeFee().doubleValue());
                                        }
                                        if (HandlerConstants.PASSENGER_TYPE_INF.equals(interChangeFeeDto.getPassengerType())) {
                                            changeFlightInfo.getInfCabinFare().setChangeServiceCharge(segment.getChangeFee().doubleValue());
                                        }
                                        if (HandlerConstants.PASSENGER_TYPE_OLD.equals(interChangeFeeDto.getPassengerType())) {
                                            changeFlightInfo.getOldCabinFare().setChangeServiceCharge(segment.getChangeFee().doubleValue());
                                        }
                                        if (HandlerConstants.PASSENGER_TYPE_GMJC.equals(interChangeFeeDto.getPassengerType())) {
                                            changeFlightInfo.getGmjcCabinFare().setChangeServiceCharge(segment.getChangeFee().doubleValue());
                                        }
                                    }
                                });
                            }
                        });
                    }

                }
            }
        }
    }

    private void getFlightFareRespBaseResp(BaseReq<FlightFareChangeQuery> req, FlightFareChangeQuery flightFareQuery, String channelCode, String userNo, String ip, BaseResp<ChangeFlightFareResp> resp, String reqId, Map<String, ChangePassengerInfo> passMap, List<ChangeFlightInfo> changeFlightInfoList, String curDateStr) {
        //查询航班辅助信息
        List<com.juneyaoair.mobile.mongo.entity.FlightInfo> paramList = new ArrayList<>();
        flightFareQuery.getChangeFlightInfoList().forEach(changeFlightInfo -> {
            com.juneyaoair.mobile.mongo.entity.FlightInfo param = new com.juneyaoair.mobile.mongo.entity.FlightInfo();
            param.setFlightNo(changeFlightInfo.getFlightNo());
            param.setFlightDate(changeFlightInfo.getDepFlightDate());
            param.setDepAirport(changeFlightInfo.getDepAirportCode());
            param.setArrAirport(changeFlightInfo.getArrAirportCode());
            paramList.add(param);
        });
        Map<String, com.juneyaoair.mobile.mongo.entity.FlightInfo> flightInfoMap = new HashMap<>();
        List<com.juneyaoair.mobile.mongo.entity.FlightInfo> baseInfoList = basicService.queryBaseFlightByList(paramList);
        baseInfoList.forEach(flightInfo -> {
            flightInfoMap.put(flightInfo.getFlightNo(), flightInfo);
        });
        //获取第一个成人的客票信息
        ChangePassengerInfo changePassengerInfo = flightFareQuery.getPassengerInfoList().stream().filter(p -> HandlerConstants.PASSENGER_TYPE_ADT.equals(p.getPassengerType()) ||
                HandlerConstants.PASSENGER_TYPE_GMJC.equals(p.getPassengerType())).findFirst().orElse(null);
        PtFlightFareChangeRequest ptFlightFareChangeRequest = createPtFlightFareChangeRequestByTicket(channelCode,
                userNo, flightFareQuery.getTripType(), flightFareQuery.getChangeFlightInfoList(), flightFareQuery.getPassengerInfoList(),
                flightFareQuery.getRouteType(), flightInfoMap, flightFareQuery.getIssueDate(), flightFareQuery.getFfpCardNo(), flightFareQuery.getFfpId(), changePassengerInfo.getTicketNo());
        ptFlightFareChangeRequest.setIssueDate(flightFareQuery.getIssueDate());
        List<SegmentChange> flightChangeList = ptFlightFareChangeRequest.getFlightChangeList();
        for (SegmentChange segmentChange : flightChangeList) {
            if (flightFareQuery.getSendCode().equals(segmentChange.getDepCity())) {
                segmentChange.setChangeFlightDate(flightFareQuery.getDepartureDate());
            }
            if (flightFareQuery.getSendCode().equals(segmentChange.getArrCity())) {
                segmentChange.setChangeFlightDate(flightFareQuery.getReturnDate());
            }
        }
        String queryChangeFeeUrl = HandlerConstants.URL_FARE_API + HandlerConstants.QUERY_CHANGE_FARE;
        Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
        HttpResult httpResult = HttpUtil.doPostClient(ptFlightFareChangeRequest, queryChangeFeeUrl, headMap, handConfig.getReadTimeout(), handConfig.getConnectTimeout());
        if (httpResult.isResult()) {
            String changeFareResult = httpResult.getResponse();
            if (StringUtil.isNullOrEmpty(changeFareResult)) {
                log.info(COMMON_LOG_REQ_RESP_INFO, reqId, ip, JsonMapper.buildNormalMapper().toJson(req), JsonUtil.objectToJson(resp));
                throw new ServiceException("客票改期查询手续费返回结果空！");
            }
        } else {
            throw new ServiceException(WSEnum.NETWORK_BUSY.getResultInfo());
        }
        PtFlightFareChangeResponse ptFlightFareChangeResponse = (PtFlightFareChangeResponse) JsonUtil.jsonToBean(httpResult.getResponse(), PtFlightFareChangeResponse.class);
        if (!UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(ptFlightFareChangeResponse.getResultCode())) {
            throw new ServiceException(ptFlightFareChangeResponse.getErrorInfo());
        }
        if (StringUtil.isNullOrEmpty(ptFlightFareChangeResponse.getFlightInfoList())) {
            throw new ServiceException("该航班暂不支持线上改期");
        }
        Map<String, Fare> fareMap = ptFlightFareChangeResponse.getFareDic();//运价字典
        if (fareMap == null) {
            throw new ServiceException("运价字典信息为空！");
        }
        //根据乘客类型 计算手续费
        for (Map.Entry<String, ChangePassengerInfo> entry : passMap.entrySet()) {
            String passType = entry.getKey();
            for (ChangeFlightInfo changeFlightInfo : changeFlightInfoList) {
                for (V2FlightInfo v2FlightInfo : ptFlightFareChangeResponse.getFlightInfoList()) {
                    //匹配对应的改期航班
                    if (changeFlightInfo.getFlightNo().equals(v2FlightInfo.getFlightNo()) && changeFlightInfo.isSelectedFlag()) {
                        V2CabinFare cabinFare = null;
                        Fare fare;
                        double changeFee = -1;
                        if (HandlerConstants.PASSENGER_TYPE_ADT.equals(passType)) {
                            if (CollectionUtils.isNotEmpty(v2FlightInfo.getCabinFareList())) {
                                cabinFare = v2FlightInfo.getCabinFareList().get(0);
                                fare = fareMap.get(cabinFare.getFareKey());
                                if (HandlerConstants.TRIP_TYPE_D.equals(flightFareQuery.getTripType())) {
                                    changeFee = AVObjectConvertV2.calChangeServcieFee(fare, curDateStr,
                                            changeFlightInfo.getDepFlightDate() + " " + changeFlightInfo.getDepDateTime(), v2FlightInfo.getDepZone());
                                } else {
                                    changeFee = AVObjectConvertV2.calChangeInterServcieFee(ptFlightFareChangeResponse.getInterChangeFeeList(), changeFlightInfo, passType);
                                }
                                if (changeFee == -1) {
                                    log.info(COMMON_LOG_REQ_RESP_INFO, reqId, ip, JsonMapper.buildNormalMapper().toJson(req), JsonUtil.objectToJson(resp));
                                    throw new ServiceException("成人改期费计算错误！");
                                }
                                if (changeFlightInfo.getAdtCabinFare() != null) {
                                    changeFlightInfo.getAdtCabinFare().setChangeServiceCharge(changeFee);
                                }
                            }
                        } else if (HandlerConstants.PASSENGER_TYPE_GMJC.equals(passType)) {
                            if (changeFlightInfo.getGmjcCabinFare() != null && CollectionUtils.isNotEmpty(v2FlightInfo.getCabinGMJCFareList())) {
                                cabinFare = v2FlightInfo.getCabinGMJCFareList().get(0);
                                fare = fareMap.get(cabinFare.getFareKey());
                                if (HandlerConstants.TRIP_TYPE_D.equals(flightFareQuery.getTripType())) {
                                    changeFee = AVObjectConvertV2.calChangeServcieFee(fare, curDateStr,
                                            changeFlightInfo.getDepFlightDate() + " " + changeFlightInfo.getDepDateTime(), v2FlightInfo.getDepZone());
                                } else {
                                    changeFee = AVObjectConvertV2.calChangeInterServcieFee(ptFlightFareChangeResponse.getInterChangeFeeList(), changeFlightInfo, passType);
                                }
                                if (changeFee == -1) {
                                    log.info(COMMON_LOG_REQ_RESP_INFO, reqId, ip, JsonMapper.buildNormalMapper().toJson(req), JsonUtil.objectToJson(resp));
                                    throw new ServiceException("军残警残改期费计算错误！");
                                }
                                changeFlightInfo.getGmjcCabinFare().setChangeServiceCharge(changeFee);
                            }
                        } else {
                            for (int i = 0; i < v2FlightInfo.getCabinCHDINFFareList().size(); i++) {
                                cabinFare = v2FlightInfo.getCabinCHDINFFareList().get(i);
                                fare = fareMap.get(cabinFare.getFareKey());
                                if (HandlerConstants.PASSENGER_TYPE_INF.equals(passType) && HandlerConstants.PASSENGER_TYPE_INF.equals(fare.getPassengerType())) {
                                    if (changeFlightInfo.getInfCabinFare() != null) {
                                        if (HandlerConstants.TRIP_TYPE_D.equals(flightFareQuery.getTripType())) {
                                            changeFee = AVObjectConvertV2.calChangeServcieFee(fare, curDateStr,
                                                    changeFlightInfo.getDepFlightDate() + " " + changeFlightInfo.getDepDateTime(), v2FlightInfo.getDepZone());
                                        } else {
                                            changeFee = AVObjectConvertV2.calChangeInterServcieFee(ptFlightFareChangeResponse.getInterChangeFeeList(), changeFlightInfo, passType);
                                        }
                                        changeFlightInfo.getInfCabinFare().setChangeServiceCharge(changeFee);
                                        break;
                                    }
                                }
                                if (HandlerConstants.PASSENGER_TYPE_CHD.equals(passType) && HandlerConstants.PASSENGER_TYPE_CHD.equals(fare.getPassengerType())) {
                                    if (changeFlightInfo.getChdCabinFare() != null) {
                                        if (HandlerConstants.TRIP_TYPE_D.equals(flightFareQuery.getTripType())) {
                                            changeFee = AVObjectConvertV2.calChangeServcieFee(fare, curDateStr,
                                                    changeFlightInfo.getDepFlightDate() + " " + changeFlightInfo.getDepDateTime(), v2FlightInfo.getDepZone());
                                        } else {
                                            changeFee = AVObjectConvertV2.calChangeInterServcieFee(ptFlightFareChangeResponse.getInterChangeFeeList(), changeFlightInfo, passType);
                                        }
                                        changeFlightInfo.getChdCabinFare().setChangeServiceCharge(changeFee);
                                        break;
                                    }
                                }
                            }
                            if (changeFee == -1) {
                                log.info(COMMON_LOG_REQ_RESP_INFO, reqId, ip, JsonMapper.buildNormalMapper().toJson(req), JsonUtil.objectToJson(resp));
                                throw new ServiceException("儿童婴儿改期费计算错误！");
                            }
                        }

                    }
                }
            }
        }
    }


    private void getChangeFlightIFareRespBaseResp(BaseReq<FlightFareChangeQuery> req, String channelCode, String userNo, FlightFareChangeQuery flightFareQuery, String ip, List<ChangeFlightInfo> changeFlightInfoList, BaseResp<ChangeFlightFareResp> resp, String reqId) {
        Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
        CalculateChangeRequest calculateChangeRequest = new CalculateChangeRequest(channelCode, userNo, HandlerConstants.VERSION);
        Map<String, ChangePassengerInfo> passGroup = new HashMap<>();
        for (ChangePassengerInfo changePassengerInfo : flightFareQuery.getPassengerInfoList()) {
            passGroup.put(changePassengerInfo.getPassengerType(), changePassengerInfo);
        }
        List<String> passTypeList = new ArrayList<>();
        for (String key : passGroup.keySet()) {
            passTypeList.add(key);
        }
        List<SengmentChangeDto> flightChangeList = new ArrayList();
        if (CollectionUtils.isNotEmpty(flightFareQuery.getChangeFlightInfoList())) {
            flightFareQuery.getChangeFlightInfoList().forEach(changeFlightInfo -> {
                SengmentChangeDto sengmentChangeDto = new SengmentChangeDto(changeFlightInfo.getDepCityCode(), changeFlightInfo.getArrCityCode(), changeFlightInfo.getDepFlightDate(), changeFlightInfo.getFlightNo());
                sengmentChangeDto.setArrAirport(changeFlightInfo.getArrAirportCode());
                sengmentChangeDto.setDepAirport(changeFlightInfo.getDepAirportCode());
                flightChangeList.add(sengmentChangeDto);
            });
        }
        //获取改期乘客证件信息
        List<String> certList = flightFareQuery.getPassengerInfoList().stream().map(ChangePassengerInfo::getCertNo).collect(Collectors.toList());
        calculateChangeRequest.setFlightChangeList(flightChangeList);
        calculateChangeRequest.setPassengerTypes(passTypeList);
        calculateChangeRequest.setChannelOrderNo(flightFareQuery.getOriginChannelOrderNo());
        calculateChangeRequest.setOrderNo(flightFareQuery.getOriginOrderNo());
        calculateChangeRequest.setFfpCardNo(flightFareQuery.getFfpCardNo());
        calculateChangeRequest.setFfpId(flightFareQuery.getFfpId());
        calculateChangeRequest.setCertificates(certList);
        String queryChangeFeeUrl = HandlerConstants.URL_FARE_API + HandlerConstants.INTER_CALCULATE_CHANGEFEE;
        HttpResult httpResult = HttpUtil.doPostClient(calculateChangeRequest, queryChangeFeeUrl, headMap, handConfig.getReadTimeout(), handConfig.getConnectTimeout());
        if (StringUtils.isNotBlank(httpResult.getResponse())) {
            CalculateChangeResponse calculateChangeResponse = (CalculateChangeResponse)
                    JsonUtil.jsonToBean(httpResult.getResponse(), CalculateChangeResponse.class);
            if (!UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(calculateChangeResponse.getResultCode())) {
                throw new ServiceException(calculateChangeResponse.getErrorInfo());
            }
            if (StringUtil.isNullOrEmpty(calculateChangeResponse.getInterChangeFeeList())) {
                throw new ServiceException("该航班暂不支持线上改期");
            }
            if (UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(calculateChangeResponse.getResultCode())) {
                for (ChangeFlightInfo changeFlightInfo : changeFlightInfoList) {
                    if (CollectionUtils.isNotEmpty(calculateChangeResponse.getInterChangeFeeList())) {
                        List<InterChangeFeeDto> interChangeFeeDtos = calculateChangeResponse.getInterChangeFeeList();
                        interChangeFeeDtos.forEach(interChangeFeeDto -> {
                            List<com.juneyaoair.baseclass.response.order.InterchangeFee.SengmentChangeDto> segmentList = interChangeFeeDto.getSegmentList();
                            if (CollectionUtils.isNotEmpty(segmentList)) {
                                segmentList.forEach(segment -> {
                                    if (changeFlightInfo.getDepAirportCode().equals(segment.getDepAirport())
                                            && changeFlightInfo.getArrAirportCode().equals(segment.getArrAirport())) {
                                        if (HandlerConstants.PASSENGER_TYPE_ADT.equals(interChangeFeeDto.getPassengerType())) {
                                            changeFlightInfo.getAdtCabinFare().setChangeServiceCharge(segment.getChangeFee().doubleValue());
                                        }
                                        if (HandlerConstants.PASSENGER_TYPE_CHD.equals(interChangeFeeDto.getPassengerType())) {
                                            changeFlightInfo.getChdCabinFare().setChangeServiceCharge(segment.getChangeFee().doubleValue());
                                        }
                                        if (HandlerConstants.PASSENGER_TYPE_INF.equals(interChangeFeeDto.getPassengerType())) {
                                            changeFlightInfo.getInfCabinFare().setChangeServiceCharge(segment.getChangeFee().doubleValue());
                                        }
                                        if (HandlerConstants.PASSENGER_TYPE_OLD.equals(interChangeFeeDto.getPassengerType())) {
                                            changeFlightInfo.getOldCabinFare().setChangeServiceCharge(segment.getChangeFee().doubleValue());
                                        }
                                        if (HandlerConstants.PASSENGER_TYPE_GMJC.equals(interChangeFeeDto.getPassengerType())) {
                                            changeFlightInfo.getGmjcCabinFare().setChangeServiceCharge(segment.getChangeFee().doubleValue());
                                        }
                                    }
                                });
                            }
                        });
                    }

                }
            }
        }
    }

    private void getChangeFlightFareRespBaseResp(BaseReq<FlightFareChangeQuery> req, String channelCode, String userNo, FlightFareChangeQuery flightFareQuery, String ip, List<ChangeFlightInfo> changeFlightInfoList, BaseResp<ChangeFlightFareResp> resp, String reqId) {
        PtCalculateChangeRequest ptCalculateChangeRequest = createPtCalculateChangeRequest(channelCode, userNo, flightFareQuery);
        Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
        HttpResult httpResult = HttpUtil.doPostClient(ptCalculateChangeRequest, HandlerConstants.URL_FARE_API
                + HandlerConstants.CHANGE_FEE_CALCULATE, headMap, handConfig.getReadTimeout(), handConfig.getConnectTimeout());
        if (StringUtils.isNotBlank(httpResult.getResponse())) {
            try {
                PtCalculateChangeResponse ptCalculateChangeResponse = (PtCalculateChangeResponse)
                        JsonUtil.jsonToBean(httpResult.getResponse(), PtCalculateChangeResponse.class);
                if (UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(ptCalculateChangeResponse.getResultCode())) {
                    for (ChangeSegmentInfo changeSegmentInfo : ptCalculateChangeResponse.getChangeSegmentList()) {
                        for (ChangeFlightInfo changeFlightInfo : changeFlightInfoList) {
                            //机场匹配
                            if (changeSegmentInfo.getDepAirport().equals(changeFlightInfo.getDepAirportCode())
                                    && changeSegmentInfo.getArrAirport().equals(changeFlightInfo.getArrAirportCode())) {
                                //确认改期的航班
                                if (!changeFlightInfo.isSelectedFlag()) {
                                    continue;
                                }
                                List<ChangeFeeInfo> changeFeeInfoList = changeSegmentInfo.getChangeFeeList();
                                for (ChangeFeeInfo changeFeeInfo : changeFeeInfoList) {
                                    if (HandlerConstants.PASSENGER_TYPE_ADT.equals(changeFeeInfo.getPassengerType()) &&
                                            HandlerConstants.PASSENGER_TYPE_OLD.equals(changeFeeInfo.getPassengerIdentity())) {
                                        if (changeFlightInfo.getOldCabinFare() != null) {
                                            changeFlightInfo.getOldCabinFare().setChangeServiceCharge(changeFeeInfo.getChangeFee() == null ? 0 : changeFeeInfo.getChangeFee());
                                        }
                                    } else if (HandlerConstants.PASSENGER_TYPE_ADT.equals(changeFeeInfo.getPassengerType())) {
                                        if (changeFlightInfo.getAdtCabinFare() != null) {
                                            changeFlightInfo.getAdtCabinFare().setChangeServiceCharge(changeFeeInfo.getChangeFee() == null ? 0 : changeFeeInfo.getChangeFee());
                                        }
                                        if (changeFlightInfo.getGmjcCabinFare() != null) {
                                            changeFlightInfo.getGmjcCabinFare().setChangeServiceCharge(changeFeeInfo.getChangeFee() == null ? 0 : changeFeeInfo.getChangeFee());
                                        }
                                    } else if (HandlerConstants.PASSENGER_TYPE_CHD.equals(changeFeeInfo.getPassengerType())) {
                                        if (changeFlightInfo.getChdCabinFare() != null) {
                                            changeFlightInfo.getChdCabinFare().setChangeServiceCharge(changeFeeInfo.getChangeFee() == null ? 0 : changeFeeInfo.getChangeFee());
                                        }
                                    } else if (HandlerConstants.PASSENGER_TYPE_INF.equals(changeFeeInfo.getPassengerType())) {
                                        if (changeFlightInfo.getInfCabinFare() != null) {
                                            changeFlightInfo.getInfCabinFare().setChangeServiceCharge(changeFeeInfo.getChangeFee() == null ? 0 : changeFeeInfo.getChangeFee());
                                        }
                                    }
                                }
                            }
                        }
                    }
                } else {
                    log.info(COMMON_LOG_REQ_RESP_INFO, reqId, ip, JsonMapper.buildNormalMapper().toJson(req), JsonUtil.objectToJson(resp));
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    throw new ServiceException("订单手续费计算错误");
                }
            } catch (Exception e) {
                log.error("请求号:{}，IP地址:{}，客户端提交参数：{}，服务端响应结果：{}，错误信息：", reqId, ip, JsonMapper.buildNormalMapper().toJson(req), httpResult.getResponse(), e);
                log.info(COMMON_LOG_REQ_RESP_INFO, reqId, ip, JsonMapper.buildNormalMapper().toJson(req), JsonUtil.objectToJson(resp));
                throw new ServiceException("订单手续费计算错误");
            }
        } else {
            throw new ServiceException("订单改期查询手续费返回结果空");

        }
    }

    /**
     * 筛选舱位价格
     *
     * @param flightInfo
     * @param changeFlightInfoList
     * @param notVoluntaryChange   true-非自愿 false-自愿
     */
    private void filterCabinFarePrice(FlightInfo flightInfo, List<ChangeFlightInfo> changeFlightInfoList, boolean notVoluntaryChange) {
        if (CollectionUtils.isNotEmpty(flightInfo.getCabinFareList())) {
            changeFlightInfoList.stream().filter(changeFlightInfo ->
                    changeFlightInfo.getDepCityCode().equals(flightInfo.getDepCity()) && changeFlightInfo.getArrCityCode().equals(flightInfo.getArrCity())
            ).findFirst().ifPresent(changeFlightInfo -> {
                //过滤舱位价格高于原始价格舱位 非自愿时可以不需要此过滤
                if (!notVoluntaryChange) {
                    if (changeFlightInfo.getGmjcCabinFare() != null) {
                        List<CabinFare> filteredCabinFares = flightInfo.getCabinFareList().stream()
                                .filter(cabinFare -> cabinFare.getFareBasis().startsWith(HandlerConstants.PASSENGER_TYPE_GMJC)).collect(Collectors.toList());
                        flightInfo.setCabinGMJCFareList(filteredCabinFares);
                    } else {
                        List<CabinFare> filteredCabinFares = flightInfo.getCabinFareList().stream().filter(cabinFare ->
                                cabinFare.getPriceValue() >=
                                        (ObjectUtil.isNotEmpty(changeFlightInfo.getAdtCabinFare()) ?
                                                changeFlightInfo.getAdtCabinFare().getPriceValue() :
                                                changeFlightInfo.getOldCabinFare().getPriceValue())).collect(Collectors.toList());
                        flightInfo.setCabinFareList(filteredCabinFares);
                    }

                }
            });
        }
    }

    /**
     * 非自愿改期是否免费
     *
     * @param trrDateLimit       免费前后天数
     * @param originalFlightDate 原航班日期
     * @param changeFlightDate   改期航班日期
     * @param flightNo           航班号限制
     * @return
     */
    private boolean freeChangeFee(TrrDateLimit trrDateLimit, String originalFlightDate, String changeFlightDate, String flightNo) {
        if (trrDateLimit == null) {
            return false;
        }
        if (StringUtils.isNotBlank(trrDateLimit.getChangeDateLimit()) && trrDateLimit.getChangeDateLimit().contains(",")) {
            String[] dayLimit = trrDateLimit.getChangeDateLimit().split(",");
            long days = DateUtils.durDays(DateUtils.toDate(originalFlightDate), DateUtils.toDate(changeFlightDate));
            if (days >= 0) {//改期日期在原日期之后
                if (days > NumberUtils.toInt(dayLimit[1])) {
                    return false;
                }
            } else if (-days > NumberUtils.toInt(dayLimit[0])) {
                return false;
            }
        }
        if (StringUtils.isNotBlank(trrDateLimit.getFlightDateLimit()) && trrDateLimit.getFlightDateLimit().contains(",")) {
            String[] flightDateLimit = trrDateLimit.getFlightDateLimit().split(",");
            Date dateBegin = DateUtils.toDate(flightDateLimit[0]);
            Date dateEnd = DateUtils.toDate(flightDateLimit[1]);
            //在限制日期范围内的不可TRR改期
            if (DateUtils.compareDate(dateBegin, dateEnd, DateUtils.toDate(changeFlightDate))) {
                return false;
            }
        }
        if (StringUtils.isNotBlank(trrDateLimit.getFlightNo()) && !"*".equals(trrDateLimit.getFlightNo()) && StringUtils.isNotBlank(flightNo)) {
            List<String> flightNos = Arrays.asList(trrDateLimit.getFlightNo().split(","));
            return flightNos.contains(flightNo);
        }
        return true;
    }

    @InterfaceLog
    @ApiOperation(value = "客票生成改期订单", notes = "客票生成改期订单")
    @RequestMapping(value = "/bookTicketChangeOrder", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp bookTicketChangeOrder(@RequestBody @Validated BaseReq<TicketChangeBookingReq> req, BindingResult bindingResult, HttpServletRequest request) {
        BaseResp resp = new BaseResp<>();
        String reqId = StringUtil.newGUID() + request.getRequestURI();
        String clientIp = this.getClientIP(request);
        //注解上参数有效性检验
        if (bindingResult.hasErrors()) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
            return resp;
        }
        String channelCode = req.getChannelCode();
        TicketChangeBookingReq ticketChangeBookingReq = req.getRequest();
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(ticketChangeBookingReq.getFfpId(), ticketChangeBookingReq.getLoginKeyInfo(), channelCode);
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;

        }
        //非注解上参数有效性检验
        BaseResp<TicketChangeBookingReq> validResp = getBaseResp(req, resp);
        if (validResp != null) return validResp;
        String userNo = getChannelInfo(channelCode, "10");
        /**
         * 目前不支持的情况：
         * 往返程同时改期时，去程为非自愿改期，返程为自愿改期，且去程的改期到达时间在原返程起飞时间之后
         */
        if (HandlerConstants.FLIGHT_INTER_D.equals(ticketChangeBookingReq.getInterFlag())) {
            String checkFlightDateDesc = checkFlightDate(ticketChangeBookingReq);
            if (StringUtils.isNotBlank(checkFlightDateDesc)) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo(checkFlightDateDesc);
                return resp;
            }
        }

        String changeCouponCheck = checkChangeCoupon(ticketChangeBookingReq.getUsePassengerSegments());
        if (StringUtils.isNotBlank(changeCouponCheck)) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(changeCouponCheck);
            return resp;
        }
        //封装需要的客票改期参数
        PtTicketBookingReq ptTicketBookingReq = ChangeObjectConvert.toTicketChangeConfirmReq(channelCode, userNo, ticketChangeBookingReq, handConfig);
        ptTicketBookingReq.setOrderRequestIp(clientIp);
        String path = HandlerConstants.URL_FARE_API + HandlerConstants.TICKET_CHANGE_MULTI;
        Map<String, String> headMap = HttpUtil.getHeaderMap(clientIp, "");
        String result = this.invokePost(ptTicketBookingReq, path, headMap, handConfig.getReadTimeout(), handConfig.getConnectTimeout());
        if (StringUtil.isNullOrEmpty(result)) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("网络请求结果为空！");
            return resp;
        } else {
            TicketBookingResp ticketBookingResp;
            try {
                ticketBookingResp = (TicketBookingResp) JsonUtil.jsonToBean(result, TicketBookingResp.class);
            } catch (Exception e) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("数据格式解析异常！");
                log.error(COMMON_LOG_WITH_RESP_INFO, reqId, clientIp, result);
                return resp;
            }
            if (UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(ticketBookingResp.getResultCode())) {
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                Map<String, Object> orderMap = new HashMap<>();
                orderMap.put("payState", ticketBookingResp.getPayAmount() == 0);
                orderMap.put("orderNo", ticketBookingResp.getOrderNO());
                orderMap.put("channelOrderNo", ticketChangeBookingReq.getChannelOrderNo());
                resp.setObjData(orderMap);
            } else if ("7005".equals(ticketBookingResp.getResultCode())) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("请稍后再试...");
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo(ticketBookingResp.getErrorInfo());
            }
        }
        return resp;
    }

    private static String checkFlightDate(TicketChangeBookingReq ticketChangeBookingReq) {
        if (ticketChangeBookingReq.getFlightInfoList().size() > 1 && ticketChangeBookingReq.getOldFlightInfoList().size() > 1) {
            com.juneyaoair.baseclass.request.booking.FlightInfo back =
                    ticketChangeBookingReq.getOldFlightInfoList().stream().filter(e -> "B".equals(e.getFlightDirection()) && !e.isNotVoluntaryChange())
                            .findFirst().orElse(null);
            Date originBackFlightDay = back == null ? null : DateUtils.toDate(back.getDepDateTime(), DateUtils.YYYY_MM_DD_HH_MM_PATTERN);
            if (null != originBackFlightDay) {
                for (com.juneyaoair.baseclass.request.booking.FlightInfo flightInfo : ticketChangeBookingReq.getFlightInfoList()) {
                    if ("G".equals(flightInfo.getFlightDirection()) && flightInfo.isNotVoluntaryChange()) {
                        Date changeGoDate = DateUtils.toDate(flightInfo.getArrDateTime(), DateUtils.YYYY_MM_DD_HH_MM_PATTERN);
                        if (null != changeGoDate && changeGoDate.after(originBackFlightDay)) {
                            return "非自愿改期日期不能在原返程日期之后";
                        }
                    }
                }
            }
        }
        return null;
    }

    @InterfaceLog
    @ApiOperation(value = "机票订单生成改期订单", notes = "机票订单生成改期订单")
    @RequestMapping(value = "/bookOrderChangeOrder", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp bookOrderChangeOrder(@RequestBody @Validated BaseReq<OrderChangeBookingReq> req, BindingResult bindingResult, HttpServletRequest request) {
        BaseResp resp = new BaseResp<>();
        String reqId = StringUtil.newGUID() + request.getRequestURI();
        String clientIp = this.getClientIP(request);
        //参数有效性检验
        if (bindingResult.hasErrors()) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
            return resp;
        }
        //非注解上参数有效性检验
        BaseResp resp1 = getResp(req, resp);
        if (resp1 != null) return resp1;
        OrderChangeBookingReq orderChangeBookingReq = req.getRequest();
        String channelCode = req.getChannelCode();
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(orderChangeBookingReq.getFfpId(), orderChangeBookingReq.getLoginKeyInfo(), channelCode);
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        String userNo = getChannelInfo(channelCode, "10");
        Map<String, List<ChangePassengerInfo>> passMap = ChangeObjectConvert.toGroupPassengerType(orderChangeBookingReq.getPassengerInfoList());
        //判断成人是否都为空
        if (CollectionUtils.isEmpty(passMap.get(HandlerConstants.PASSENGER_TYPE_ADT)) && CollectionUtils.isEmpty(passMap.get(HandlerConstants.PASSENGER_TYPE_GMJC))) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("儿童、婴儿不可单独改期！");
            return resp;
        }
        if (HandlerConstants.TRIP_TYPE_D.equals(orderChangeBookingReq.getInterFlag())) {
            //检验各类运价信息是否齐全
            for (Map.Entry<String, List<ChangePassengerInfo>> entry : passMap.entrySet()) {
                for (com.juneyaoair.baseclass.request.booking.FlightInfo flightInfo : orderChangeBookingReq.getFlightInfoList()) {
                    if (HandlerConstants.PASSENGER_TYPE_ADT.equals(entry.getKey())) {
                        if (StringUtil.isNullOrEmpty(flightInfo.getCabinFareList())) {
                            resp.setResultCode(WSEnum.ERROR.getResultCode());
                            resp.setResultInfo("成人无对应的舱位运价信息");
                            return resp;
                        }
                    }
                    if (HandlerConstants.PASSENGER_TYPE_GMJC.equals(entry.getKey())) {
                        if (StringUtil.isNullOrEmpty(flightInfo.getCabinGMJCFareList())) {
                            resp.setResultCode(WSEnum.ERROR.getResultCode());
                            resp.setResultInfo("军残警残无对应的舱位运价信息");
                            return resp;
                        }
                    }
                    if (HandlerConstants.PASSENGER_TYPE_CHD.equals(entry.getKey())) {
                        List<com.juneyaoair.baseclass.request.booking.CabinFare> cabinCHDList = flightInfo.getCabinCHDINFFareList()
                                .stream().filter(cabinFareCHD -> HandlerConstants.PASSENGER_TYPE_CHD.equals(cabinFareCHD.getPassengerType()))
                                .collect(Collectors.toList());
                        if (StringUtil.isNullOrEmpty(cabinCHDList)) {
                            resp.setResultCode(WSEnum.ERROR.getResultCode());
                            resp.setResultInfo("儿童无对应的舱位运价信息");
                            return resp;
                        }
                    }
                    if (HandlerConstants.PASSENGER_TYPE_INF.equals(entry.getKey())) {
                        List<com.juneyaoair.baseclass.request.booking.CabinFare> cabinINFList = flightInfo.getCabinCHDINFFareList()
                                .stream().filter(cabinFareINF -> HandlerConstants.PASSENGER_TYPE_INF.equals(cabinFareINF.getPassengerType()))
                                .collect(Collectors.toList());
                        if (StringUtil.isNullOrEmpty(cabinINFList)) {
                            resp.setResultCode(WSEnum.ERROR.getResultCode());
                            resp.setResultInfo("婴儿无对应的舱位运价信息");
                            return resp;
                        }
                    }
                }
            }
        }


        String changeCouponCheck = checkChangeCoupon(orderChangeBookingReq.getUsePassengerSegments());
        if (StringUtils.isNotBlank(changeCouponCheck)) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(changeCouponCheck);
            return resp;
        }
        try {
            PtOrderChangeConfirmRequest ptOrderChangeConfirmRequest = null;
            if (HandlerConstants.TRIP_TYPE_D.equals(orderChangeBookingReq.getInterFlag())) {
                //封装第三方平台请求参数
                ptOrderChangeConfirmRequest = ChangeObjectConvert.toOrderChangeConfirmReq(channelCode,
                        userNo, passMap, orderChangeBookingReq);
            } else {
                ptOrderChangeConfirmRequest = ChangeObjectConvert.toOrderChangeConfirmReqV30(channelCode,
                        userNo, passMap, orderChangeBookingReq);
            }
            ptOrderChangeConfirmRequest.setOrderRequestIp(clientIp);
            String path = HandlerConstants.URL_FARE_API + HandlerConstants.TICKET_ORDER_CHANGE;
            Map<String, String> headMap = HttpUtil.getHeaderMap(clientIp, "");
            HttpResult result = this.doPostClient(ptOrderChangeConfirmRequest, path, headMap);
            if (!result.isResult() || StringUtils.isBlank(result.getResponse())) {
                throw new OperationFailedException(NETWORK_ERROR_MESSAGE);
            }
            PtOrderChangeResultResponse ptOrderChangeResultResponse = JsonUtil.fromJson(result.getResponse(), PtOrderChangeResultResponse.class);
            if (!UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(ptOrderChangeResultResponse.getResultCode())) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo(ptOrderChangeResultResponse.getErrorInfo());
            } else if ("7005".equals(ptOrderChangeResultResponse.getResultCode())) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("请稍后再试...");
            } else {
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                Map<String, Object> orderMap = new HashMap<>();
                orderMap.put("payState", ptOrderChangeResultResponse.getPayAmount() == 0);
                orderMap.put("orderNo", ptOrderChangeResultResponse.getOrderNO());
                orderMap.put("channelOrderNo", ptOrderChangeResultResponse.getChannelOrderNo());
                resp.setObjData(orderMap);
            }
        } catch (Exception e) {
            this.logError(resp, reqId, clientIp, req, e, "机票改期出现异常");
        }
        return resp;
    }

    private BaseResp getResp(BaseReq<OrderChangeBookingReq> req, BaseResp resp) {
        OrderChangeBookingReq bookingReq = req.getRequest();
        String resultInfo = null;
        for (ChangePassengerInfo passengerInfo : bookingReq.getPassengerInfoList()) {
            String tktNo = passengerInfo.getTicketNo();
            if (StringUtils.isBlank(tktNo)) {
                tktNo = passengerInfo.getETicketNo();
            }
            String info = apiRedisService.getData(RedisKeyConfig.createUpIdInfo(tktNo));
            if (StringUtils.isBlank(info)) {
                throw new ServiceException("操作超时，请返回重新查询下单");
            }
            passengerInfo.setCertNo(info);
            if (Stream.of(CertificateTypeEnum.PASSPORT,
                    CertificateTypeEnum.TAIWAN_MTP,
                    CertificateTypeEnum.HK_MACAO_MTP,
                    CertificateTypeEnum.OTHER).map(i -> i.getShowCode()).collect(Collectors.toList()).contains(passengerInfo.getCertType())) {

                if (StringUtils.isBlank(passengerInfo.getSex())) {
                    resultInfo = "性别不能为空";
                    break;
                }
                if (StringUtils.isBlank(passengerInfo.getBirthdate())) {
                    resultInfo = "出生日期不能为空";
                    break;
                }
                if (StringUtils.isBlank(passengerInfo.getNationality())) {
                    resultInfo = "国籍不能为空";
                    break;
                }
                if (StringUtils.isBlank(passengerInfo.getCertNo())) {
                    resultInfo = "证件号码发国不能为空";
                    break;
                }
                if (StringUtils.isBlank(passengerInfo.getBelongCountry())) {
                    resultInfo = "证件签发国不能为空";
                    break;
                }
                if (StringUtils.isBlank(passengerInfo.getCertValidity())) {
                    resultInfo = "证件有效期不能为空";
                    break;
                }
            }
        }
        if (StringUtils.isNotBlank(resultInfo)) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo(resultInfo);
            return resp;
        }
        return null;
    }

    private BaseResp<TicketChangeBookingReq> getBaseResp(BaseReq<TicketChangeBookingReq> req, BaseResp<TicketChangeBookingReq> resp) {
        TicketChangeBookingReq bookingReq = req.getRequest();
        String resultInfo = null;
        for (ChangePassengerInfo passengerInfo : bookingReq.getPassengerInfoList()) {
            //隐私证件信息补充
            String tktNo = passengerInfo.getTicketNo();
            if (StringUtils.isBlank(tktNo)) {
                tktNo = passengerInfo.getETicketNo();
            }
            String info = apiRedisService.getData(RedisKeyConfig.createUpIdInfo(tktNo));
            if (StringUtils.isBlank(info)) {
                throw new ServiceException("操作超时，请返回重新查询下单");
            }
            passengerInfo.setCertNo(info);
            if (Stream.of(CertificateTypeEnum.PASSPORT,
                    CertificateTypeEnum.TAIWAN_MTP,
                    CertificateTypeEnum.HK_MACAO_MTP,
                    CertificateTypeEnum.OTHER).map(i -> i.getShowCode()).collect(Collectors.toList()).contains(passengerInfo.getCertType())) {
                if (StringUtils.isBlank(passengerInfo.getSex())) {
                    resultInfo = "性别不能为空";
                    break;
                }
                if (StringUtils.isBlank(passengerInfo.getBirthdate())) {
                    resultInfo = "出生日期不能为空";
                    break;
                }
                if (StringUtils.isBlank(passengerInfo.getNationality())) {
                    resultInfo = "国籍不能为空";
                    break;
                }
                if (StringUtils.isBlank(passengerInfo.getCertNo())) {
                    resultInfo = "证件号码发国不能为空";
                    break;
                }
                if (StringUtils.isBlank(passengerInfo.getBelongCountry())) {
                    resultInfo = "证件签发国不能为空";
                    break;
                }
                if (StringUtils.isBlank(passengerInfo.getCertValidity())) {
                    resultInfo = "证件有效期不能为空";
                    break;
                }
            }
        }
        if (StringUtils.isNotBlank(resultInfo)) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo(resultInfo);
            return resp;
        }
        return null;
    }

    /**
     * 改期券使用校验
     *
     * @param usePassengerSegments
     * @return
     */
    private String checkChangeCoupon(List<UsePassengerSegment> usePassengerSegments) {
        if (CollectionUtils.isNotEmpty(usePassengerSegments)) {
            long count = usePassengerSegments.stream().filter(e -> StringUtils.isNotBlank(e.getCouponCode()))
                    .map(UsePassengerSegment::getCouponCode).distinct().count();
            if (count < usePassengerSegments.size()) {
                return "每张优惠券只能使用一次";
            }
            List<String> repeatCheck = Lists.newArrayList();
            for (UsePassengerSegment usePassengerSegment : usePassengerSegments) {
                if (repeatCheck.contains(usePassengerSegment.getUniqueSeq())) {
                    return "每个乘客、航段只能使用一张改期券";
                }
                repeatCheck.add(usePassengerSegment.getUniqueSeq());
            }
        }
        return null;
    }

    /**
     * 获取客票信息
     *
     * @param channelCode
     * @param ticketNo
     * @return
     */
    private TicketListInfoResponse queryTicket(String channelCode, String ticketNo, String passName, String clientIp) {
        String userNo = getChannelInfo(channelCode, "10");
        TicketInfoRequest ticketInfoRequest = new TicketInfoRequest(HandlerConstants.VERSION, channelCode, userNo);
        String certType = CertNoUtil.getCertTypeByCertNo(ticketNo);
        ticketInfoRequest.setCertType(certType);
        ticketInfoRequest.setPassengerName(passName);
        if ("TN".equals(certType)) {
            ticketInfoRequest.setTicketNo(ticketNo);
        } else {
            ticketInfoRequest.setCertNo(ticketNo);
            throw new CommonException(WSEnum.NOT_SUPPORT.getResultCode(), WSEnum.NOT_SUPPORT.getResultInfo());
        }
        ticketInfoRequest.setQueryType(TicketQueryTypeEnum.CHANGE.getCode());
        Map<String, String> headMap = HttpUtil.getHeaderMap(clientIp, "");
        TicketListInfoResponse ticketListInfoResponse = orderManage.getTicketInfo(ticketInfoRequest, headMap, false);
        ticketListInfoResponse.setTrrLimit(JsonUtil.fromJson(ticketListInfoResponse.getTrrDateLimit(), TrrDateLimit.class));
        return ticketListInfoResponse;
    }

    /**
     * 查询可改期订单列表
     *
     * @param orderChangeRequest
     * @return
     */
    private PtOrderChangeResponse quertChangeOrderList(PtOrderChangeRequest orderChangeRequest, String ip) {
        PtOrderChangeResponse ptOrderChangeResponse = new PtOrderChangeResponse();
        Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
        String url = HandlerConstants.URL_FARE_API + HandlerConstants.SUB_QUERY_ORDER_CHANGE_BRIEF;
        HttpResult result = this.doPostClient(orderChangeRequest, url, headMap, handConfig.getReadTimeout(), handConfig.getConnectTimeout());
        if (!result.isResult() || StringUtil.isNullOrEmpty(result.getResponse())) {
            ptOrderChangeResponse.setErrorInfo("改期列表为空！");
            ptOrderChangeResponse.setResultCode(UnifiedOrderResultEnum.FAIL.getResultCode());
        } else {
            try {
                ptOrderChangeResponse = (PtOrderChangeResponse) JsonUtil.jsonToBean(result.getResponse(), PtOrderChangeResponse.class);
            } catch (Exception ex) {
                log.error("请求参数：{},请求路径：{}，返回结果：{}，错误信息：", JsonUtil.objectToJson(orderChangeRequest), url, result, ex);
                ptOrderChangeResponse.setErrorInfo("改期数据转换异常！");
                ptOrderChangeResponse.setResultCode(UnifiedOrderResultEnum.FAIL.getResultCode());
            }
        }
        return ptOrderChangeResponse;
    }

    /**
     * 获取子订单类型
     *
     * @param oldSubOrderList
     * @param type
     * @return
     */
    private List<OrderBase> GetSubOrderBaseInfoList(List<OrderBase> oldSubOrderList, String type) {
        List<OrderBase> subOrderList = new ArrayList<>();
        for (OrderBase orderBase : oldSubOrderList) {
            if (type.equals(orderBase.getItem1())) {
                subOrderList.add(orderBase);
            }
        }
        return subOrderList;
    }

    /**
     * 查询机票子订单信息
     *
     * @param subOrderResp
     * @param orderDetailQuery
     * @param channelCode
     * @param userNo
     * @param airportMap
     * @param localCacheService
     * @return
     */
    private BaseResp queryRefundApplyInfo(SubOrderResp subOrderResp, OrderDetailQuery orderDetailQuery, String channelCode,
                                          String userNo, Map<String, AirPortInfoDto> airportMap, String clientIp, LocalCacheService localCacheService) {
        BaseResp resp = new BaseResp();
        //此处只取机票子订单
        List<OrderBase> subTiccetList = GetSubOrderBaseInfoList(subOrderResp.getSubtOrderBaseInfoList(), SubOrderTypeEnum.TICKET_ORDER.getOrdrType());
        String path = HandlerConstants.URL_FARE_API + HandlerConstants.SUB_QUERY_REFUND_APPLY;
        Map<String, String> headMap = HttpUtil.getHeaderMap(clientIp, "");
        PtRefundApplyResp reRefundResp = null;
        List<TicketInfo> ticketInfoList = new ArrayList<>();
        for (OrderBase orderBase : subTiccetList) {
            PtRefundApplyReq refundApply = new PtRefundApplyReq(HandlerConstants.VERSION,
                    channelCode, userNo, orderBase.getItem2(), orderDetailQuery.getLinkerEMail());
            HttpResult httpResult = this.doPostClient(refundApply, path, headMap, handConfig.getReadTimeout(), handConfig.getConnectTimeout());
            if (httpResult.isResult()) {
                if (StringUtils.isNotBlank(httpResult.getResponse())) {
                    PtRefundApplyResp     reRefund= (PtRefundApplyResp) JsonUtil.jsonToBean(httpResult.getResponse(), PtRefundApplyResp.class);
                    TicketInfo ticketInfo = ChangeObjectConvert.toTicketInfo(subOrderResp, reRefund, airportMap, localCacheService);
                    ticketInfo.setIssueDate(DateUtils.subDateStr(reRefund.getPassengerSegmentList().get(0).getTicketDatetime(), 10));
                    ticketInfo.setTicketOrderNo(orderBase.getItem2());
                    //获取机全部票子订单信息
                    ticketInfoList.add(ticketInfo);
                    //获取成人客票信息
                    boolean   isAdt= ticketInfo.getPassengerInfoList().stream()
                            .anyMatch(changePassengerInfo -> PassengerTypeEnum.ADT.getPassType().equals(changePassengerInfo.getPassengerType()));
                    if (isAdt){
                        reRefundResp= reRefund;
                    }
                } else {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo("订单中心无数据");
                    return resp;
                }

            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo(httpResult.getResponse());
                return resp;
            }
        }
        Map<String, List<ChangePassengerInfo>> changePassengerInfoMap = new HashMap<>();
        for (TicketInfo ticket : ticketInfoList) {
            changePassengerInfoMap.put(ticket.getTicketOrderNo(), ticket.getPassengerInfoList());
        }
        //筛选 状态为 OPEN FOR USE 的客票信息
        String ticketOrderNo = "";
        for (Map.Entry<String, List<ChangePassengerInfo>> entry : changePassengerInfoMap.entrySet()) {
            List<ChangePassengerInfo> changePassengerInfoList = entry.getValue();
            List<ChangePassengerInfo> changePassengers = changePassengerInfoList.stream().filter(changePassenger -> changePassenger.getSegmentPriceInfoList().stream()
                    .anyMatch(segmentInfo -> HandlerConstants.OPEN_FOR_USE.equals(segmentInfo.getTKTStatus()))).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(changePassengers)) {
                ticketOrderNo = entry.getKey();
                break;
            }
        }
        String finalTicketOrderNo = ticketOrderNo;
        TicketInfo ticketInfo = ticketInfoList.stream()
                .filter(ticket -> finalTicketOrderNo.equals(ticket.getTicketOrderNo())).collect(Collectors.toList()).stream()
                .findFirst().orElse(null);
        //未找到符合的客票信息
        if (ticketInfo == null) {
            resp.setResultCode(WSEnum.NO_DATA.getResultCode());
            resp.setResultInfo("暂未查询到可线上改期的客票，请致电吉祥航空客服热线95520咨询");
            return resp;
        }
        ////其余的子机票订单只保留其中的乘客信息
        ticketInfoList.forEach(ticketPo -> {
            if (!ticketInfo.getTicketOrderNo().equals(ticketPo.getTicketOrderNo())) {
                ticketInfo.getPassengerInfoList().addAll(ticketPo.getPassengerInfoList());
            }
        });
        //国内航班查询非正常航班改期信息，失败时不影响正常改期流程
        //包含婴儿乘客的订单不支持线上非自愿改期
        Optional<ChangePassengerInfo> optional = ticketInfo.getPassengerInfoList().stream()
                .filter(changePassengerInfo -> PassengerTypeEnum.INF.getPassType().equals(changePassengerInfo.getPassengerType())).findFirst();
        //儿童设置可以改期
        if (optional.isPresent()) {
            for (ChangeFlightInfo changeFlightInfo : ticketInfo.getChangeFlightInfoList()) {
                changeFlightInfo.setAbleChange(true);
            }
        }
        if (!optional.isPresent() || HandlerConstants.FLIGHT_INTER_I.equals(ticketInfo.getInterFlag())) {
            String eTicketNo = "";
            String passengerName = "";
            try {
                for (ChangePassengerInfo changePassengerInfo : ticketInfo.getPassengerInfoList()) {
                    boolean tKTStatus = changePassengerInfo.getSegmentPriceInfoList().stream()
                            .anyMatch(segmentInfo -> HandlerConstants.OPEN_FOR_USE.equals(segmentInfo.getTKTStatus()));
                    if (tKTStatus) {
                        eTicketNo = changePassengerInfo.getETicketNo();
                        passengerName = changePassengerInfo.getPassengerName();
                        break;
                    }
                }
                if (StringUtils.isEmpty(eTicketNo) || StringUtils.isEmpty(passengerName)) {
                    eTicketNo = ticketInfo.getPassengerInfoList().get(0).getETicketNo();
                    passengerName = ticketInfo.getPassengerInfoList().get(0).getPassengerName();
                }
                //查询客票信息是否存在IRR标记
                TicketListInfoResponse ticketListInfoResponse = queryTicket(channelCode, eTicketNo, passengerName, clientIp);
                if (UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(ticketListInfoResponse.getResultCode())) {
                    if (CollectionUtils.isEmpty(ticketListInfoResponse.getIBETicketInfoList())) {
                        resp.setResultCode(WSEnum.NO_DATA.getResultCode());
                        resp.setResultInfo("暂未查询到可线上改期的客票，请致电吉祥航空客服热线95520咨询");
                        return resp;
                    }
                    getIsAbleChange(localCacheService, ticketInfo, ticketListInfoResponse);
                }
            } catch (Exception e) {
                log.error("订单详情查询非正常航班改期出现异常", e);
            }
        }

        //取出成人的舱位 改期规则等信息
        Map<String, AircraftModel> aircraftModelMap = FlightUtil.toAircraftModelMap(handConfig.getAircraftModel());
        setChangeRule(ticketInfo, reRefundResp, airportMap, aircraftModelMap);
        //乘客筛选，剔除客票状态为改期的乘客以及无可用航段的乘客
        List<ChangePassengerInfo> changePassengerInfoListFilter = new ArrayList<>();
        for (ChangePassengerInfo changePassengerInfo : ticketInfo.getPassengerInfoList()) {
            //处理购保信息
            if (CollectionUtils.isNotEmpty(changePassengerInfo.getInsuranceList())) {
                changePassengerInfo.setIsBuyInsurance("Y");
            } else {
                List<InsuranceInfoNew> allInsurances = this.listTicketInsuranceInfo(changePassengerInfo.getETicketNo(), changePassengerInfo.getPassengerName(), headMap);
                if (CollectionUtils.isNotEmpty(allInsurances)) {
                    changePassengerInfo.setIsBuyInsurance("Y");
                    changePassengerInfo.setInsuranceOrderNo(allInsurances.get(0).getOrderNo());
                    changePassengerInfo.setInsuranceChannelOrderNo(allInsurances.get(0).getChannelOrderNo());
                }
            }
            boolean flag = true;
            int openCount = 0;
            //剔除改期乘客
            for (SegmentPriceInfo segmentPriceInfo : changePassengerInfo.getSegmentPriceInfoList()) {
                //已改期的
                if (HandlerConstants.EXCHANGED.equals(segmentPriceInfo.getTKTStatus())) {
                    flag = false;
                    break;
                }
                //已值机或登机的
                if (HandlerConstants.BOARDED.equals(segmentPriceInfo.getTKTStatus())
                        || HandlerConstants.CHECKED_IN.equals(segmentPriceInfo.getTKTStatus())) {
                    flag = false;
                    break;
                }
                if (HandlerConstants.OPEN_FOR_USE.equals(segmentPriceInfo.getTKTStatus())) {
                    openCount++;
                }
            }
            if (flag) {
                if (openCount > 0) {
                    changePassengerInfoListFilter.add(changePassengerInfo);
                }
            }
        }
        //如果有人可以改期,则设置航线为可改期,都能改期才可以改期
        ticketInfo.setAbleChange(ticketInfo.getChangeFlightInfoList().stream().anyMatch(ChangeFlightInfo::isAbleChange));

        if (CollectionUtils.isEmpty(changePassengerInfoListFilter)) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("此订单下无可改期的乘客");
            return resp;
        }
        //取出各类乘客类型的税费明细
        if (HandlerConstants.TRIP_TYPE_I.equals(reRefundResp.getInterFlag())) {
            Map<String, List<TaxInfo>> groupMap = new HashMap();
            List<FareTaxInfo> fareTaxInfoList = new ArrayList<>();
            for (ChangePassengerInfo changePassengerInfo : changePassengerInfoListFilter) {
                groupMap.put(changePassengerInfo.getPassengerType(), changePassengerInfo.getTaxInfoList());
            }
            if (groupMap != null && !groupMap.isEmpty()) {
                for (Map.Entry<String, List<TaxInfo>> entry : groupMap.entrySet()) {
                    Double amount = 0.0, cn = 0.0, yq = 0.0, q = 0.0;
                    for (TaxInfo taxInfo : entry.getValue()) {
                        if ("CN".equalsIgnoreCase(taxInfo.getTaxCode())) {
                            cn = taxInfo.getTaxAmount();
                        } else if ("YQ".equalsIgnoreCase(taxInfo.getTaxCode())) {
                            yq = taxInfo.getTaxAmount();
                        } else if ("Q".equalsIgnoreCase(taxInfo.getTaxCode())) {
                            q = taxInfo.getTaxAmount();
                        }
                        amount += taxInfo.getTaxAmount();
                    }
                    FareTaxInfo fareTaxInfo = new FareTaxInfo(entry.getKey(), cn, yq, q, amount - cn - yq - q);
                    fareTaxInfo.setTaxInfoList(entry.getValue());
                    fareTaxInfoList.add(fareTaxInfo);
                }
            }
            ticketInfo.setOldFareTaxInfoList(fareTaxInfoList);
        }


        /**
         * 2021-8-20
         * 添加购买权益券判断
         */
        QueryCouponInfoRequest queryCouponInfoRequest = new QueryCouponInfoRequest();
        queryCouponInfoRequest.setRequestIp(clientIp);
        queryCouponInfoRequest.setChannelCode(channelCode);
        queryCouponInfoRequest.setUserNo(userNo);
        queryCouponInfoRequest.setVersion("10");
        handlerCoupon(queryCouponInfoRequest, ticketInfo);
        //是否是迪士尼
        ticketInfo.setIsDisneyFlag(false);
        if (CollectionUtils.isNotEmpty(subOrderResp.getDisneyTicketList())) {
            ticketInfo.setIsDisneyFlag(true);
            ticketInfo.setDisneyTicketList(subOrderResp.getDisneyTicketList());
        }
        //处理订单改期，客票隐私信息
        handSensitiveInfoPass(ticketInfo.getPassengerInfoList());
        resp.setObjData(ticketInfo);
        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
        resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());


        return resp;
    }

    private void getIsAbleChange(LocalCacheService localCacheService, TicketInfo ticketInfo, TicketListInfoResponse ticketListInfoResponse) {
        for (ChangeFlightInfo changeFlightInfo : ticketInfo.getChangeFlightInfoList()) {
            for (PtSegmentInfo segmentInfo : ticketListInfoResponse.getIBETicketInfoList().get(0).getSegmentInfoList()) {
                List<PtSegmentInfo> segmentInfoList = ticketListInfoResponse.getIBETicketInfoList().get(0).getSegmentInfoList();
                //segmentInfo 中无城市信息，需要从机场中获取
                AirPortInfoDto depAirport = localCacheService.getLocalAirport(segmentInfo.getDepAirportCode());
                AirPortInfoDto arrAirport = localCacheService.getLocalAirport(segmentInfo.getArrAirportCode());

                // 3U 川航不支持改期
                if (StringUtils.isNotBlank(segmentInfo.getOperationAirline()) && "3U".contains(segmentInfo.getOperationAirline())) {
                    changeFlightInfo.setAbleChange(false);
                }
                if (depAirport.getCityCode().equals(changeFlightInfo.getDepCityCode())
                        && arrAirport.getCityCode().equals(changeFlightInfo.getArrCityCode())) {
                    changeFlightInfo.setAbleChange(true);
                    // 畅飞卡不允许改期,但如果有irr标记则可以改期
                    if (handConfig.getFreeTicketCabin().equals(changeFlightInfo.getCabin())) {
                        changeFlightInfo.setAbleChange(false);
                    }
                    if (handConfig.getAwardFlyFreeTicketCabin().contains(changeFlightInfo.getCabin()) && !segmentInfo.getIrrFlag()) {
                        changeFlightInfo.setAbleChange(false);
                    }
                }
                if (depAirport.getCityCode().equals(changeFlightInfo.getDepCityCode())
                        && arrAirport.getCityCode().equals(changeFlightInfo.getArrCityCode())) {
                    if (!segmentInfo.getIrrFlag() && HandlerConstants.FLIGHT_INTER_I.equals(ticketInfo.getInterFlag())) {
                        ticketInfo.setNotVoluntaryChange(false);
                        changeFlightInfo.setAbleChange(false);
                        segmentInfo.setNotVoluntaryDesc("自愿改期请联系客服95520处理");
                    }
                    if (segmentInfo.getIrrFlag()) {
                        TrrDateLimit trrDateLimit = ticketListInfoResponse.getTrrLimit();
                        ticketInfo.setTrrDateLimit(trrDateLimit);
                        ticketInfo.setNotVoluntaryChange(true);
                        segmentInfo.setNotVoluntaryDesc("因您的航班发生变动，可免费更改至前3天或后3天的航班");
                        changeFlightInfo.setAbleChange(segmentInfo.isOperation());
                    }
                    changeFlightInfo.setNotVoluntaryDesc(segmentInfo.getNotVoluntaryDesc());
                    changeFlightInfo.setNotVoluntaryChange(segmentInfo.getIrrFlag());
                }
                if (HandlerConstants.FLIGHT_INTER_I.equals(ticketInfo.getInterFlag())
                        && (HandlerConstants.ROUTE_TYPE_RT.equals(ticketInfo.getRouteType())
                        || HandlerConstants.ROUTE_TYPE_CT.equals(ticketInfo.getRouteType()))) {
                    boolean irrFlag = segmentInfoList.stream().anyMatch(PtSegmentInfo::getIrrFlag);
                    if (irrFlag) {
                        TrrDateLimit trrDateLimit = ticketListInfoResponse.getTrrLimit();
                        ticketInfo.setTrrDateLimit(trrDateLimit);
                        ticketInfo.setNotVoluntaryChange(true);
                        segmentInfo.setNotVoluntaryDesc("因您的航班发生变动，可免费更改至前3天或后3天的航班");
                        changeFlightInfo.setNotVoluntaryChange(true);
                        changeFlightInfo.setAbleChange(segmentInfo.isOperation());
                    } else {
                        changeFlightInfo.setAbleChange(false);
                        ticketInfo.setNotVoluntaryChange(false);
                        segmentInfo.setNotVoluntaryDesc("自愿改期请联系客服95520处理");
                        changeFlightInfo.setNotVoluntaryChange(false);
                        if (StringUtils.isNotBlank(segmentInfo.getRate()) && segmentInfo.getRate().endsWith(FareBasisEnum.BUS_FARE.getFareBasisCode())) {
                            changeFlightInfo.setAbleChange(false);
                            ticketInfo.setNotVoluntaryChange(false);
                            segmentInfo.setNotVoluntaryDesc("客票为包车机票，如需改期，请联系客服处理");
                        }
                    }
                    changeFlightInfo.setNotVoluntaryDesc(segmentInfo.getNotVoluntaryDesc());
                    boolean ticketStatus = segmentInfoList.stream().anyMatch(ptSegmentInfo ->
                            !HandlerConstants.OPEN_FOR_USE.equals(ptSegmentInfo.getTicketStatus()));
                    if (ticketStatus) {
                        changeFlightInfo.setAbleChange(false);
                    }
                }
            }
        }
    }

    private void setChangeRule(TicketInfo ticketInfo, PtRefundApplyResp reRefundResp, Map<String, AirPortInfoDto> airportMap, Map<String, AircraftModel> aircraftModelMap) {
        if (UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(reRefundResp.getResultCode())) {
            //断言条件
            Predicate<PtOrderPassengerInfo> passAnd = pass -> HandlerConstants.PASSENGER_TYPE_ADT.equals(pass.getPassengerType())
                    && !("GM".equals(pass.getCertType()) || "JC".equals(pass.getCertType()));
            PtOrderPassengerInfo adtPass = reRefundResp.getPassengerInfoList().stream().filter(passAnd).findFirst().orElse(null);
            if (adtPass != null) {
                //当前人所属的航段关联信息
                List<PtPassengerSegment> adtPassSegList = reRefundResp.getPassengerSegmentList().stream()
                        .filter(passSeg -> adtPass.getPassengerID() == passSeg.getPassengerID()).collect(Collectors.toList());
                if (!StringUtil.isNullOrEmpty(adtPassSegList)) {
                    adtPassSegList.stream().forEach(passSeg -> {
                        reRefundResp.getSegmentInfoList().stream().forEach(ptSegmentPriceInfo -> {
                            if (passSeg.getSegmentID() == ptSegmentPriceInfo.getSegmentID()) {
                                ticketInfo.getChangeFlightInfoList().stream().forEach(changeFlightInfo -> {
                                    if (changeFlightInfo.getDepAirportCode().equals(ptSegmentPriceInfo.getDepAirport())
                                            && changeFlightInfo.getArrAirportCode().equals(ptSegmentPriceInfo.getArrAirport())) {
                                        changeFlightInfo.setCabin(ptSegmentPriceInfo.getCabin());
                                        changeFlightInfo.setCabinClass(ptSegmentPriceInfo.getCabinClass());
                                        changeFlightInfo.setCabinName(CommonUtil.showCabinClassName(ptSegmentPriceInfo.getCabinClass()));
                                        changeFlightInfo.setMealCode(ptSegmentPriceInfo.getMealCode());
                                        changeFlightInfo.setTicketPrice(passSeg.getPricePaid());
                                        AirPortInfoDto deptAirPort = airportMap.get(ptSegmentPriceInfo.getDepAirport());
                                        AirPortInfoDto arrAirPort = airportMap.get(ptSegmentPriceInfo.getArrAirport());
                                        String depTimeZone = deptAirPort == null ? "8" : deptAirPort.getCityTimeZone();
                                        String arrTimeZone = arrAirPort == null ? "8" : arrAirPort.getCityTimeZone();
                                        int days = DateUtils.diffDays(ptSegmentPriceInfo.getDepDateTime().substring(0, 10),
                                                ptSegmentPriceInfo.getArrDateTime().substring(0, 10), DateUtils.YYYY_MM_DD_PATTERN);
                                        changeFlightInfo.setDays(days);
                                        changeFlightInfo.setDuration(DateUtils.calDuration(ptSegmentPriceInfo.getDepDateTime(),
                                                depTimeZone, ptSegmentPriceInfo.getArrDateTime(), arrTimeZone));
                                        changeFlightInfo.setAircraftModel(aircraftModelMap == null ? "" :
                                                (aircraftModelMap.get(ptSegmentPriceInfo.getPlaneStyle()) == null ? "" :
                                                        aircraftModelMap.get(ptSegmentPriceInfo.getPlaneStyle()).getRemark()));
                                        if (FareBasisEnum.BUS_FARE.getFareBasisCode().equals(ptSegmentPriceInfo.getPlaneStyle())
                                        &&"HKG".equals(ptSegmentPriceInfo.getArrCity())){
                                            changeFlightInfo.setArrCityName("中国香港全境");
                                            changeFlightInfo.setArrAirportName("");
                                        }
                                        if (FareBasisEnum.BUS_FARE.getFareBasisCode().equals(ptSegmentPriceInfo.getPlaneStyle())
                                                &&"HKG".equals(ptSegmentPriceInfo.getDepCity())){
                                            changeFlightInfo.setDepCityName("中国香港全境");
                                            changeFlightInfo.setDepAirportName("");
                                        }
                                        //机票订单对应的改期规则
                                        List<ChangeAndRefundRule> refundRuleList = new ArrayList<>();
                                        List<ChangeAndRefundRule> changeAndRefundRuleList = AVObjectConvertV2.
                                                toChangeRules(passSeg.getChangeRules(), ticketInfo.getInterFlag());
                                        FlightUtil.completeRule(refundRuleList, changeAndRefundRuleList);
                                        changeFlightInfo.setChangeRuleList(changeAndRefundRuleList);
                                    }
                                });
                            }
                        });
                    });
                }
            }
        }
    }

    /**
     * 航班查询条件建立
     */
    private PtQueryFlightFareRequest createQueryFareRequest(String channelCode, String userNo, FlightFareChangeQuery flightFareQuery, List<ChangeFlightInfo> goChangeFlightInfo,
                                                            List<ChangeFlightInfo> backChangeFlightInfo, List<ChangeFlightInfo> selectedchangeFlightList) {
        List<Segment> segmentList = new ArrayList<>();
        PtQueryFlightFareRequest flightFareReq = new PtQueryFlightFareRequest(
                HandlerConstants.VERSION,
                channelCode,
                userNo,
                flightFareQuery.getRouteType(),
                HandlerConstants.CURRENCY_CODE,
                HandlerConstants.LANG_CODE,
                segmentList,
                handConfig.getAvReadRedis()
        );
        flightFareReq.setQueryType("0");
        flightFareReq.setFareSource(FareSourceEnum.SEARCH_ONE.getFareSource());
        List<String> passengerTypes = flightFareQuery.getPassengerInfoList().stream().map(ChangePassengerInfo::getPassengerType).collect(Collectors.toList());
        flightFareReq.setPassengerType(passengerTypes);
        if (selectedchangeFlightList.size() == 1) {
            flightFareReq.setRouteType(HandlerConstants.ROUTE_TYPE_OW);
            for (int i = 0; i < selectedchangeFlightList.size(); i++) {
                ChangeFlightInfo changeFlightInfo = flightFareQuery.getChangeFlightInfoList().get(i);
                segmentList.add(new Segment(i, changeFlightInfo.getFlightDirection(), changeFlightInfo.getDepCityCode(), changeFlightInfo.getArrCityCode(), flightFareQuery.getDepartureDate()));
            }
        }
        if (goChangeFlightInfo.size() > 1) {
            for (int i = 1; i < goChangeFlightInfo.size(); i++) {
                ChangeFlightInfo preFlightInfo = goChangeFlightInfo.get(i - 1);
                ChangeFlightInfo curFlightInfo = goChangeFlightInfo.get(i);
                Segment segment = new Segment(0, "G", preFlightInfo.getDepCityCode(), curFlightInfo.getArrCityCode(), flightFareQuery.getDepartureDate());
                flightFareReq.setRouteType(HandlerConstants.ROUTE_TYPE_OW);
                flightFareQuery.setRouteType(HandlerConstants.ROUTE_TYPE_OW);
                segmentList.add(segment);
            }
        }
        if (backChangeFlightInfo.size() > 1) {
            for (int i = 1; i < backChangeFlightInfo.size(); i++) {
                ChangeFlightInfo preFlightInfo = backChangeFlightInfo.get(i - 1);
                ChangeFlightInfo curFlightInfo = backChangeFlightInfo.get(i);
                Segment segment = new Segment(goChangeFlightInfo.size() > 1 ? 1 : 0, "B", preFlightInfo.getDepCityCode(), curFlightInfo.getArrCityCode(),
                        flightFareQuery.getReturnDate());
                flightFareReq.setRouteType(HandlerConstants.ROUTE_TYPE_OW);
                flightFareQuery.setRouteType(HandlerConstants.ROUTE_TYPE_OW);
                segmentList.add(segment);
            }
        }
        if (backChangeFlightInfo.size() == 2 && goChangeFlightInfo.size() == 2) {
            flightFareReq.setRouteType(HandlerConstants.ROUTE_TYPE_RT);
        }
        if (HandlerConstants.ROUTE_TYPE_RT.equals(flightFareQuery.getRouteType()) && selectedchangeFlightList.size() == 2) {
            for (int i = 0; i < flightFareQuery.getChangeFlightInfoList().size(); i++) {
                ChangeFlightInfo changeFlightInfo = flightFareQuery.getChangeFlightInfoList().get(i);
                if (FlightDirection.BACK.getCode().equals(changeFlightInfo.getFlightDirection())) {

                    segmentList.add(new Segment(i, changeFlightInfo.getFlightDirection(), changeFlightInfo.getDepCityCode(), changeFlightInfo.getArrCityCode(), flightFareQuery.getReturnDate()));

                } else {
                    segmentList.add(new Segment(i, changeFlightInfo.getFlightDirection(), changeFlightInfo.getDepCityCode(), changeFlightInfo.getArrCityCode(), flightFareQuery.getDepartureDate()));
                }

            }
        }
        flightFareQuery.setChangeFlightInfoList(selectedchangeFlightList);
        flightFareReq.setTicketOutDateTime(flightFareQuery.getIssueDate());
        flightFareReq.setSegCondList(segmentList);
        flightFareReq.setChannelCustomerNo(flightFareQuery.getFfpId());
        return flightFareReq;
    }


    /**
     * 航班查询条件建立
     */
    private PtQueryFlightFareRequest createQueryFareRequestV20(String channelCode, String userNo, FlightFareChangeQuery flightFareQuery) {
        List<Segment> segmentList = new ArrayList<>();
        PtQueryFlightFareRequest flightFareReq = new PtQueryFlightFareRequest(
                HandlerConstants.VERSION,
                channelCode,
                userNo,
                flightFareQuery.getRouteType(),
                HandlerConstants.CURRENCY_CODE,
                HandlerConstants.LANG_CODE,
                segmentList,
                handConfig.getAvReadRedis()
        );
        flightFareReq.setQueryType("0");
        flightFareReq.setFareSource(FareSourceEnum.SEARCH_ONE.getFareSource());
        Segment goSegment = new Segment(0, "G", flightFareQuery.getSendCode(), flightFareQuery.getArrCode(), flightFareQuery.getDepartureDate());
        ChangeFlightInfo historyGFlightInfo = flightFareQuery.getChangeFlightInfoList().stream()
                .filter(changeFlightInfo -> flightFareQuery.getSendCode().equals(changeFlightInfo.getDepCityCode()) && flightFareQuery.getArrCode().equals(changeFlightInfo.getArrCityCode()))
                .findFirst().orElse(null);
        segmentList.add(goSegment);
        //往返航班 判断航班是否已过计划起飞时间 以及是否允许改期，不允许改期的情况下，指定航班与日期
        if (historyGFlightInfo != null && HandlerConstants.ROUTE_TYPE_RT.equals(flightFareQuery.getRouteType())) {
            LocalDateTime localDateTime = LocalDateTime.now();
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append(historyGFlightInfo.getDepFlightDate()).append(" ").append(historyGFlightInfo.getDepDateTime());
            LocalDateTime depDateTime = DateUtils.toLocalDateTime(stringBuilder.toString(), "yyyy-MM-dd HH:mm");
            if (depDateTime != null && depDateTime.isBefore(localDateTime) && !historyGFlightInfo.isAbleChange()) {
                goSegment.setFlightNo(historyGFlightInfo.getFlightNo());
                goSegment.setCabins(historyGFlightInfo.getCabin());
            }
        }
        if (HandlerConstants.ROUTE_TYPE_RT.equals(flightFareQuery.getRouteType())) {
            segmentList.add(new Segment(1, "B", flightFareQuery.getArrCode(), flightFareQuery.getSendCode(), flightFareQuery.getReturnDate()));
        }
        flightFareReq.setTicketOutDateTime(flightFareQuery.getIssueDate());
        flightFareReq.setSegCondList(segmentList);
        flightFareReq.setChannelCustomerNo(flightFareQuery.getFfpId());
        return flightFareReq;
    }

    private String genCabinCodesString(Map<String, Set<String>> trrCabinCodesMap, String cityPair) {
        StringBuilder result = new StringBuilder();
        Set<String> cabins = trrCabinCodesMap.get(cityPair);
        if (CollectionUtils.isNotEmpty(cabins)) {
            for (String cabin : cabins) {
                result.append(cabin).append(",");
            }
        }
        return result.toString();
    }

    /**
     * 客票查询改期规则信息
     *
     * @param channelCode
     * @param userNo
     * @param changeFlightInfoList
     * @param passengerInfoList
     * @param routeType
     * @param flightInfoMap
     * @return
     */
    private PtFlightFareChangeRequest createPtFlightFareChangeRequestByTicket(String channelCode, String userNo, String interFlag,
                                                                              List<ChangeFlightInfo> changeFlightInfoList, List<ChangePassengerInfo> passengerInfoList,
                                                                              String routeType, Map<String, com.juneyaoair.mobile.mongo.entity.FlightInfo> flightInfoMap, String issueDate, String ffpCardNo, String ffpId, String ticketNo) {
        PtFlightFareChangeRequest ptFlightFareChangeRequest = new PtFlightFareChangeRequest(HandlerConstants.VERSION, channelCode,
                userNo, HandlerConstants.CURRENCY_CODE, HandlerConstants.LANG_CODE);
        ptFlightFareChangeRequest.setRouteType(routeType);
        ptFlightFareChangeRequest.setInterFlag(interFlag);
        String cabinClassSet = handConfig.getCabinClass();
        List<SegmentChange> segmentChangeList = new ArrayList<>();
        for (ChangeFlightInfo changeFlightInfo : changeFlightInfoList) {
            SegmentChange segmentChange = new SegmentChange();
            //出票时间 2021-08-04
            segmentChange.setInssueDate(issueDate);
            segmentChange.setFlightDirection(changeFlightInfo.getFlightDirection());
            segmentChange.setFlightDate(changeFlightInfo.getDepFlightDate());
            segmentChange.setDepCity(changeFlightInfo.getDepCityCode());
            segmentChange.setArrCity(changeFlightInfo.getArrCityCode());
            segmentChange.setDepAirport(changeFlightInfo.getDepAirportCode());
            segmentChange.setArrAirport(changeFlightInfo.getArrAirportCode());
            segmentChange.setFlightNo(changeFlightInfo.getFlightNo());
            segmentChange.setCarrier(changeFlightInfo.getFlightNo());
            segmentChange.setDepTime(changeFlightInfo.getDepDateTime());
            segmentChange.setFtype(flightInfoMap == null ? "" : (flightInfoMap.get(changeFlightInfo.getFlightNo()) != null ? flightInfoMap.get(changeFlightInfo.getFlightNo()).getPlanType() : "320"));
            if ("321A".equals(segmentChange.getFtype())) {
                segmentChange.setFtype("321");
            }
            Set<String> set = new HashSet<>();
            //获取所有乘客的舱位信息
            List<CabinChange> cabinChangeList = new ArrayList<>();
            for (ChangePassengerInfo changePassengerInfo : passengerInfoList) {
                for (SegmentPriceInfo segmentPriceInfo : changePassengerInfo.getSegmentPriceInfoList()) {
                    if (changeFlightInfo.getFlightNo().equals(segmentPriceInfo.getFlightNo())) {
                        CabinChange cabinChange = new CabinChange();
                        cabinChange.setPassengerType(changePassengerInfo.getPassengerType());
                        if (changePassengerInfo.getPassengerType().equals("INF")) {
                            cabinChange.setCabinCode(segmentPriceInfo.getCabinClass());
                            set.add(segmentPriceInfo.getCabinClass());
                        } else {
                            set.add(segmentPriceInfo.getCabin());
                            cabinChange.setCabinCode(segmentPriceInfo.getCabin());
                        }
                        cabinChange.setCabinClass(CommonUtil.getCabinClassByCabinCode(segmentPriceInfo.getCabin(), cabinClassSet));
                        //票面价 = 实际支付票价机票价格+积分+优惠券抵扣金额
                        cabinChange.setTicketPrice(segmentPriceInfo.getPricePaid() + segmentPriceInfo.getUseScore() + segmentPriceInfo.getCouponAmount());
                        cabinChangeList.add(cabinChange);
                    }
                }
            }
            StringBuilder cabins = new StringBuilder();
            for (String s : set) {
                cabins.append(s);
                cabins.append(",");
            }
            cabins = cabins.deleteCharAt(cabins.length() - 1);
            segmentChange.setCabins(cabins.toString());
            segmentChange.setCabinChangeList(cabinChangeList);
            segmentChangeList.add(segmentChange);
        }
        //获取改期乘客证件信息
        List<String> certList = passengerInfoList.stream().map(ChangePassengerInfo::getCertNo).collect(Collectors.toList());
        ptFlightFareChangeRequest.setFlightChangeList(segmentChangeList);
        ptFlightFareChangeRequest.setFfpcardNo(ffpCardNo);
        ptFlightFareChangeRequest.setFfpId(ffpId);
        ptFlightFareChangeRequest.setTicketNo(ticketNo);
        ptFlightFareChangeRequest.setCertificates(certList);
        return ptFlightFareChangeRequest;
    }

    /**
     * 创建订单查询手续费请求参数
     *
     * @param flightFareChangeQuery
     * @return
     */
    private PtCalculateChangeRequest createPtCalculateChangeRequest(String channelCode, String userNo, FlightFareChangeQuery flightFareChangeQuery) {
        PtCalculateChangeRequest ptCalculateChangeRequest = new PtCalculateChangeRequest(channelCode, userNo, HandlerConstants.VERSION);
        Map<String, ChangePassengerInfo> passGroup = new HashMap<>();
        for (ChangePassengerInfo changePassengerInfo : flightFareChangeQuery.getPassengerInfoList()) {
            passGroup.put(changePassengerInfo.getPassengerType(), changePassengerInfo);
        }
        List<String> passTypeList = new ArrayList<>();
        for (String key : passGroup.keySet()) {
            passTypeList.add(key);
        }
        List<SegmentChange> flightChangeList = new ArrayList();
        flightChangeList.add(new SegmentChange(flightFareChangeQuery.getSendCode(), flightFareChangeQuery.getArrCode(), flightFareChangeQuery.getDepartureDate()));
        if (HandlerConstants.ROUTE_TYPE_RT.equals(flightFareChangeQuery.getRouteType())) {
            flightChangeList.add(new SegmentChange(flightFareChangeQuery.getArrCode(), flightFareChangeQuery.getSendCode(), flightFareChangeQuery.getReturnDate()));
        }
        //获取改期乘客证件信息
        List<String> certList = flightFareChangeQuery.getPassengerInfoList().stream().map(ChangePassengerInfo::getCertNo).collect(Collectors.toList());
        ptCalculateChangeRequest.setFlightChangeList(flightChangeList);
        ptCalculateChangeRequest.setPassengerTypes(passTypeList);
        ptCalculateChangeRequest.setChannelOrderNo(flightFareChangeQuery.getOriginChannelOrderNo());
        ptCalculateChangeRequest.setOrderNo(flightFareChangeQuery.getOriginOrderNo());
        ptCalculateChangeRequest.setFfpCardNo(flightFareChangeQuery.getFfpCardNo());
        ptCalculateChangeRequest.setFfpId(flightFareChangeQuery.getFfpId());
        ptCalculateChangeRequest.setCertificates(certList);
        return ptCalculateChangeRequest;
    }

    /**
     * 舱位顺序处理
     *
     * @param res
     * @param cabinSeq
     */
    private void filterCabinSeq(List<ChangeFlightInfo> changeFlightInfoList, PtQueryFlightFareResponse res, String cabinSeq) {
        if (!StringUtil.isNullOrEmpty(cabinSeq) && !StringUtil.isNullOrEmpty(res.getFlightInfoList())) {
            for (ChangeFlightInfo changeFlightInfo : changeFlightInfoList) {
                if (changeFlightInfo.getAdtCabinFare() != null) {
                    String cabinCode = changeFlightInfo.getAdtCabinFare().getCabinCode();
                    for (V2FlightInfo v2FlightInfo : res.getFlightInfoList()) {
                        if (v2FlightInfo.getDepCity().equals(changeFlightInfo.getDepCityCode()) && v2FlightInfo.getArrCity()
                                .equals(changeFlightInfo.getArrCityCode())) {
                            List<V2CabinFare> v2CabinFareList = new ArrayList<>();
                            int cabinIndex = cabinSeq.indexOf(cabinCode);
                            if (cabinIndex != -1) {
                                String varCabinSeq = cabinSeq.substring(0, cabinIndex + 1);
                                //重新过滤航班舱位信息
                                for (V2CabinFare v2CabinFare : v2FlightInfo.getCabinFareList()) {
                                    if (varCabinSeq.contains(v2CabinFare.getCabinCode())) {
                                        v2CabinFareList.add(v2CabinFare);
                                    }
                                }
                            }
                            v2FlightInfo.setCabinFareList(v2CabinFareList);
                        }
                    }
                }
            }
        }
    }

    /**
     * 获取机票所有购保信息（包括单独购保和非单独购保）
     *
     * @param ticketNo 票号
     * @return
     */
    private List<InsuranceInfoNew> listTicketInsuranceInfo(String ticketNo, String passName, Map<String, String> headMap) {
        List<InsuranceInfoNew> insuranceInfoNews = Lists.newArrayList();
        try {
            InsuranceFlightInfo insuranceFlightInfo = orderManage.getInsuranceFlightInfo(ChannelCodeEnum.MOBILE.getChannelCode(), ticketNo, passName, headMap);
            if (UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(insuranceFlightInfo.getResultCode())) {
                IBETicketInfo ibeTicketInfo = insuranceFlightInfo.getIBETicketInfoList().get(0);
                if (CollectionUtils.isNotEmpty(ibeTicketInfo.getSegmentInfoList())) {
                    ibeTicketInfo.getSegmentInfoList().forEach(segmentInfo -> {
                        if (CollectionUtils.isNotEmpty(segmentInfo.getInsuranceList())) {
                            List<InsuranceInfoNew> insuranceInfoNewList =
                                    segmentInfo.getInsuranceList().stream().filter(info -> "Success".equals(info.getInsuranceState())).collect(Collectors.toList());
                            insuranceInfoNews.addAll(insuranceInfoNewList);
                        }
                    });
                }
            }
            return insuranceInfoNews;
        } catch (Exception e) {
            return insuranceInfoNews;
        }
    }

    @Autowired
    private HandConfig handConfig;
    @Autowired
    private TempleteConfig templeteConfig;
    @Autowired
    private IBasicService basicService;
}
package com.juneyaoair.mobile.handler.controller;


import com.geetest.geeguard.sdk.GeetestLib;
import com.geetest.geeguard.sdk.enums.DigestmodEnum;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.google.gson.reflect.TypeToken;
import com.juneyaoair.appenum.ChannelCodeEnum;
import com.juneyaoair.appenum.PicTypeEnum;
import com.juneyaoair.appenum.WSEnum;
import com.juneyaoair.appenum.av.*;
import com.juneyaoair.appenum.common.AirCompanyEnum;
import com.juneyaoair.appenum.common.PlatFormEnum;
import com.juneyaoair.appenum.flight.FlightDirection;
import com.juneyaoair.appenum.member.*;
import com.juneyaoair.appenum.order.*;
import com.juneyaoair.appenum.rightcoupon.VoucherTypesEnum;
import com.juneyaoair.baseclass.InsureQueryRule;
import com.juneyaoair.baseclass.activity.MaoTaiConfig;
import com.juneyaoair.baseclass.av.common.FlightInfoComb;
import com.juneyaoair.baseclass.basicsys.request.Antifraud;
import com.juneyaoair.baseclass.basicsys.response.AirPortInfoDto;
import com.juneyaoair.baseclass.basicsys.response.PictureDto;
import com.juneyaoair.baseclass.book.request.CheckUnPayOrderReq;
import com.juneyaoair.baseclass.book.request.PtTicketBookingRequestV20;
import com.juneyaoair.baseclass.common.base.UserInfoMust;
import com.juneyaoair.baseclass.common.request.BaseReq;
import com.juneyaoair.baseclass.common.response.BaseResp;
import com.juneyaoair.baseclass.common.response.CheckUnPayOrderResp;
import com.juneyaoair.baseclass.member.response.MemberBeneficiaryDTO;
import com.juneyaoair.baseclass.member.response.MemberTagResponse;
import com.juneyaoair.baseclass.request.av.*;
import com.juneyaoair.baseclass.request.booking.WifiQuery;
import com.juneyaoair.baseclass.request.booking.*;
import com.juneyaoair.baseclass.request.crm.MemberBeneficiaryRequest;
import com.juneyaoair.baseclass.request.order.query.DeleteOrderReq;
import com.juneyaoair.baseclass.request.order.query.OrderBriefReq;
import com.juneyaoair.baseclass.request.premium.OrderDetailPremium;
import com.juneyaoair.baseclass.response.av.CarrierNoAYFlightResp;
import com.juneyaoair.baseclass.response.booking.Segment;
import com.juneyaoair.baseclass.response.booking.TicketBookingResp;
import com.juneyaoair.baseclass.response.insure.InsureInfo;
import com.juneyaoair.baseclass.response.order.comm.OrderPassengerInfo;
import com.juneyaoair.baseclass.response.order.detail.OrderDetailSegment;
import com.juneyaoair.baseclass.response.order.query.*;
import com.juneyaoair.baseclass.response.score.ScoreUseRuleResp;
import com.juneyaoair.baseclass.response.waitorder.CheckWaitOrderResp;
import com.juneyaoair.client.ClientInfo;
import com.juneyaoair.crm.IMemberCrmMemberService;
import com.juneyaoair.crm.IMemberService;
import com.juneyaoair.exception.*;
import com.juneyaoair.mobile.aop.annotation.InterfaceLog;
import com.juneyaoair.mobile.comm.service.IRedisService;
import com.juneyaoair.mobile.constants.MobileCoreBeanNames;
import com.juneyaoair.mobile.handler.HandlerConstants;
import com.juneyaoair.mobile.handler.comm.RedisKeyConfig;
import com.juneyaoair.mobile.handler.config.bean.HandConfig;
import com.juneyaoair.mobile.handler.controller.crm.login.util.CrmUtil;
import com.juneyaoair.mobile.handler.controller.service.InsuranceService;
import com.juneyaoair.mobile.handler.controller.util.CRMReqUtil;
import com.juneyaoair.mobile.handler.controller.util.FraudApiInvoker;
import com.juneyaoair.mobile.handler.controller.util.OrderObjectConvert;
import com.juneyaoair.mobile.handler.controller.util.OrderPayStateConvert;
import com.juneyaoair.mobile.handler.controller.v2.util.*;
import com.juneyaoair.mobile.handler.service.*;
import com.juneyaoair.mobile.handler.util.FileUtils;
import com.juneyaoair.mobile.handler.util.FlightUtil;
import com.juneyaoair.mobile.webservice.client.CrmWSClient;
import com.juneyaoair.mobile.webservice.client.crm.VerifyConsumeMilesResponseForClient;
import com.juneyaoair.pattern.PatternCommon;
import com.juneyaoair.thirdentity.basic.HotelProductRespDTO;
import com.juneyaoair.thirdentity.comm.request.PtApiCRMRequest;
import com.juneyaoair.thirdentity.comm.request.PtCrmMileageRequest;
import com.juneyaoair.thirdentity.comm.response.PtCRMResponse;
import com.juneyaoair.thirdentity.member.comm.CrmMemberBaseApiRequest;
import com.juneyaoair.thirdentity.member.comm.CrmMemberBaseApiResponse;
import com.juneyaoair.thirdentity.member.request.MileageAccountQueryRequest;
import com.juneyaoair.thirdentity.member.request.PtMemberDetailRequest;
import com.juneyaoair.thirdentity.member.request.QueryMemberCertificateReqDto;
import com.juneyaoair.thirdentity.member.response.*;
import com.juneyaoair.thirdentity.request.booking.PtPassengerInfo;
import com.juneyaoair.thirdentity.request.booking.PtTicketBookingReq;
import com.juneyaoair.thirdentity.request.order.query.PtDeleteOrderReq;
import com.juneyaoair.thirdentity.request.order.query.PtOrderTotalBriefReq;
import com.juneyaoair.thirdentity.request.score.ScoreUseRuleRequest;
import com.juneyaoair.thirdentity.tongdun.FinalDecisionEnum;
import com.juneyaoair.thirdentity.tongdun.response.FraudApiResponse;
import com.juneyaoair.util.OrderGateWayUrlUtil;
import com.juneyaoair.utils.StringUtil;
import com.juneyaoair.utils.http.HttpResult;
import com.juneyaoair.utils.http.HttpUtil;
import com.juneyaoair.utils.json.JsonMapper;
import com.juneyaoair.utils.json.JsonUtil;
import com.juneyaoair.utils.util.*;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import java.lang.reflect.Type;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR> by jiangmingming
 * @date 2018/12/11 11:07
 */
@RequestMapping("/newOrderService")
@RestController
public class NewOrderController extends BassController {
    @Autowired
    private IBasicService basicService;
    @Autowired
    private CrmWSClient crmClient;
    @Autowired
    private HandConfig handConfig;
    @Resource(name = MobileCoreBeanNames.API_REDIS_SERVICE)
    private IRedisService apiRedisService;
    @Autowired
    private IMemberService memberService;
    @Autowired
    private IOrderService orderService;
    @Autowired
    private InsuranceService insuranceService;
    @Autowired
    private IBeneficiaryService beneficiaryService;
    @Autowired
    private LocalCacheService localCacheService;
    @Autowired
    private ICouponService couponService;

    @Autowired
    private IMemberTagService iMemberTagService;
    @Autowired
    private GeetestService geetestService;

    @Autowired
    private IMemberCrmMemberService iMemberCrmMemberService;

    private static final String SUBORDER_FILTER_CONTENT = "CouponOrder";
    private static final String LOG_REQ_DESP = "请求号:{}，IP地址:{}，客户端提交参数：{}";
    private static final String LOG_RESP_ONE = "网络返回数据空!";
    private static final String LOG_RESP_THREE = "查询订单列表返回结果空";


    @InterfaceLog
    @ApiOperation(value = "机票订单列表", notes = "机票订单列表")
    @RequestMapping(value = "/queryOrderBrief", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public OrderTotalBriefResp queryOrderBrief(@RequestBody @Validated BaseReq<OrderBriefReq> req, BindingResult bindingResult, HttpServletRequest request) {
        OrderTotalBriefResp resp = new OrderTotalBriefResp();
        String headChannelCode = request.getHeader(HandlerConstants.TOKEN_CHANNELCODE);
        String reqId = headChannelCode + StringUtil.newGUID() + request.getRequestURI();
        String ip = getClientIP(request);
        String reqJson = JsonMapper.buildNormalMapper().toJson(req);
        try {
            if (bindingResult.hasErrors()) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setErrorInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
                return resp;
            }
            //判断登录状态
            OrderBriefReq briefReq = req.getRequest();
            boolean flag = this.checkKeyInfo(briefReq.getCustomerNo(), briefReq.getLoginKeyInfo(), req.getChannelCode());
            if (!flag) {
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                return resp;
            }
            String userNo = getChannelInfo(req.getChannelCode(), "10");
            //构建请求类
            if (briefReq.getPageNo() <= 0) {
                briefReq.setPageNo(1);
            }
            if (briefReq.getPageSize() <= 0) {
                briefReq.setPageSize(20);
            }
            PtOrderTotalBriefReq ptBrief = new PtOrderTotalBriefReq(HandlerConstants.VERSION, req.getChannelCode(), userNo);
            if (StringUtils.isNotBlank(briefReq.getState())) {
                if ("Finish".equalsIgnoreCase(briefReq.getState()) || "Cancel".equalsIgnoreCase(briefReq.getState())) {
                    ptBrief.setOrderState(briefReq.getState());
                    ptBrief.setPayState("");
                } else if ("UnPay".equalsIgnoreCase(briefReq.getState())) {
                    ptBrief.setOrderState("Booking");
                    ptBrief.setPayState(briefReq.getState());
                } else {//全部
                    ptBrief.setOrderState("");
                    ptBrief.setPayState("");
                }
            }
            ptBrief.setSubOrderTypeFilterIndex(0);
            ptBrief.setSubOrderTypeFilterContent(SUBORDER_FILTER_CONTENT);
            ptBrief.setRemoveNullType(true);
            Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
            ptBrief = createReq(ptBrief, briefReq);
            HttpResult result = doPostClient(ptBrief, HandlerConstants.URL_FARE_API + HandlerConstants.ORDER_BRIEF, headMap, handConfig.getReadTimeout(), handConfig.getConnectTimeout());
            if (result.isResult()) {
                resp = (OrderTotalBriefResp) JsonUtil.jsonToBean(result.getResponse(), OrderTotalBriefResp.class);
                OrderTotalBriefResp orderTotalBriefResp = getOrderTotalBriefResp(resp, reqId, ip);
                setInsuranceName(orderTotalBriefResp);
                SetPassNameAndTicketNo(orderTotalBriefResp);
                // 处理订单详情跳转类型
                setApiOrderDetailType(orderTotalBriefResp);
                return orderTotalBriefResp;
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo(LOG_RESP_ONE);
                return resp;
            }
        } catch (Exception e) {
            saveError("查询订单列表", MdcUtils.getRequestId(), ip, reqJson, e);
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo(LOG_RESP_THREE);
            return resp;
        }

    }

    /**
     * 处理订单详情跳转类型
     *
     * @param orderTotalBriefResp
     */
    private void setApiOrderDetailType(OrderTotalBriefResp orderTotalBriefResp) {
        if (CollectionUtils.isEmpty(orderTotalBriefResp.getOrderBriefInfoList())) {
            return;
        }
        orderTotalBriefResp.getOrderBriefInfoList().parallelStream().forEach(orderTotalBriefInfo -> {
            List<OtherOrderInfo> otherOrderInfoList = orderTotalBriefInfo.getOtherOrderInfoList();
            if (CollectionUtils.isEmpty(otherOrderInfoList)) {
                return;
            }
            for (OtherOrderInfo otherOrderInfo : otherOrderInfoList) {
                SaleCouponInfo saleCouponInfo = otherOrderInfo.getSaleCouponInfo();
                if (null == saleCouponInfo) {
                    continue;
                }
                // 权益订单类型是优选座位订单  设置订单请求跳转类型为优选座位订单
                if (VoucherTypesEnum.PAY_SEAT.getCode().equals(saleCouponInfo.getCouponSource())) {
                    orderTotalBriefInfo.setApiOrderDetailType(VoucherTypesEnum.PAY_SEAT.getCode());
                    break;
                }
            }
        });
    }

    private OrderTotalBriefResp getOrderTotalBriefResp(OrderTotalBriefResp resp, String reqId, String ip) {
        Date curDate = new Date();
        if (UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(resp.getResultCode())) {
            AtomicBoolean refundFlag = new AtomicBoolean(false);
            AtomicBoolean exchangeFlag = new AtomicBoolean(false);
            Map<Integer, SegmentInfo> formerSegmentList = resp.getFormerSegmentList();
            //将订单按照时间排序
            sortByCreateTime(resp.getOrderBriefInfoList());
            if (CollectionUtils.isNotEmpty(resp.getOrderBriefInfoList())) {
                //key为升舱，改期订单的原航段ChannelOrderNo,value为OrderSort,用于判断原订单是否包含改期，升舱
                for (OrderTotalBriefInfo orderTotalBriefInfo : resp.getOrderBriefInfoList()) {
                    //如果是B2C渠道的upgrade，一律认为是改期
                    if (orderTotalBriefInfo.getChannelCode().equals("B2C") && orderTotalBriefInfo.getOrderSort().equals("Upgrade")) {
                        orderTotalBriefInfo.setOrderSort(OrderSortEnum.Change.getOrderSort());
                    }
                    // 结合去程和返程的航段
                    //orderTotalBriefInfo.setSegmentInfoList(combineSegmentInfos(orderTotalBriefInfo.getSegmentInfoList()));
                    if (CollectionUtils.isNotEmpty(orderTotalBriefInfo.getSegmentInfoList())) {
                        for (SegmentInfo segmentInfo : orderTotalBriefInfo.getSegmentInfoList()) {
                            AirPortInfoDto depAirPort = localCacheService.getLocalAirport(segmentInfo.getDepAirport(), DateUtils.toDate(segmentInfo.getDepDateTime(), DateUtils.YYYY_MM_DD_HH_MM_PATTERN));
                            AirPortInfoDto arrAirPort = localCacheService.getLocalAirport(segmentInfo.getArrAirport(), DateUtils.toDate(segmentInfo.getDepDateTime(), DateUtils.YYYY_MM_DD_HH_MM_PATTERN));
                            segmentInfo.setDepCityName(depAirPort == null ? segmentInfo.getDepAirport() : depAirPort.getCityName());
                            segmentInfo.setArrCityName(arrAirPort == null ? segmentInfo.getArrAirport() : arrAirPort.getCityName());
                            segmentInfo.setDepAirportName(depAirPort == null ? segmentInfo.getDepAirport() : depAirPort.getAirPortName());
                            segmentInfo.setArrAirportName(arrAirPort == null ? segmentInfo.getArrAirport() : arrAirPort.getAirPortName());
                            //标记军残警残的特殊标记
                            segmentInfo.setJcspecialLabel(V2OrderObjectConvert.judgeJCSpecialLabel(segmentInfo,
                                    orderTotalBriefInfo.getOrderSort(), orderTotalBriefInfo.getPassengerInfoList()));
                        }
                    }
                    OrderPayStateEnum orderPayState = OrderPayStateConvert.convertOrderPayState(orderTotalBriefInfo.getOrderState(),
                            orderTotalBriefInfo.getPayState(), orderTotalBriefInfo.getOrderSort(), orderTotalBriefInfo.getPassengerSegmentList());
                    orderTotalBriefInfo.setOrderPayState(orderPayState.getStateCode());
                    orderTotalBriefInfo.setOrderPayStateName(orderPayState.getStateDesc());
                    // 已取消订单和
                    orderTotalBriefInfo.setShowDeleteButton(OrderPayStateEnum.Cancel.equals(orderPayState) || OrderPayStateEnum.TicketOut.equals(orderPayState));
                    //标记评价标记
                    orderTotalBriefInfo.setEvaluateBtn(V2OrderObjectConvert.judgeAbleEvaluate(orderTotalBriefInfo.getOrderSort(),
                            orderTotalBriefInfo.getOrderState(), orderTotalBriefInfo.getPayState(), orderTotalBriefInfo.getSegmentInfoList(),
                            curDate, orderTotalBriefInfo.getPassengerSegmentList()));
                    //关闭部分订单的改期按钮展示
                    closeChange(orderTotalBriefInfo);
                    //标记该订单是否包含已退票或者改期升舱的航段
                    if (CollectionUtils.isNotEmpty(orderTotalBriefInfo.getPassengerSegmentList())) {
                        AtomicInteger index = new AtomicInteger();
                        Set<OrderBriefLabelEnum> orderBriefLabelEnums = new HashSet<>();
                        for (PassengerSegment passengerSegment : orderTotalBriefInfo.getPassengerSegmentList()) {
                            OrderBriefLabelEnum orderBriefLabelEnum = OrderBriefLabelEnum.getByCode(passengerSegment.getLaterTicketOrderSort());
                            if (orderBriefLabelEnum != null) {
                                orderBriefLabelEnums.add(orderBriefLabelEnum);
                            }
                            if (StringUtils.isNotBlank(passengerSegment.getTicketState())) {
                                if (passengerSegment.getTicketState().equals(TicketStateEnum.REFUNDED)
                                        || passengerSegment.getTicketState().equals(TicketStateEnum.REFUND_APPLICATION)) {
                                    index.getAndIncrement();
                                    refundFlag.set(true);
                                    orderTotalBriefInfo.setRefundFlag(refundFlag.get());
                                    orderTotalBriefInfo.setEvaluateBtn(false);
                                    orderTotalBriefInfo.setModifyRefundTime(passengerSegment.getModifyDatetime());
                                    orderBriefLabelEnums.add(OrderBriefLabelEnum.Refund);
                                } else if (passengerSegment.getTicketState().equals("EXCHANGED")) {
                                    exchangeFlag.set(true);
                                    orderTotalBriefInfo.setExchangeFlag(exchangeFlag.get());
                                    orderTotalBriefInfo.setEvaluateBtn(false);
                                    orderTotalBriefInfo.setModifyExchangeTime(passengerSegment.getModifyDatetime());
                                }
                            }
                        }
                        // 标签展示优先级： 已退款 > 已改期 > 已升舱
                        if (orderBriefLabelEnums.contains(OrderBriefLabelEnum.Refund)) {
                            orderTotalBriefInfo.setOrderLabel(OrderBriefLabelEnum.Refund.getCode());
                        } else if (orderBriefLabelEnums.contains(OrderBriefLabelEnum.Change)) {
                            orderTotalBriefInfo.setOrderLabel(OrderBriefLabelEnum.Change.getCode());
                        } else if (orderBriefLabelEnums.contains(OrderBriefLabelEnum.Upgrade)) {
                            orderTotalBriefInfo.setOrderLabel(OrderBriefLabelEnum.Upgrade.getCode());
                        }
                        if (index.get() == orderTotalBriefInfo.getPassengerSegmentList().size()) {
                            refundFlag.set(false);
                            orderTotalBriefInfo.setRefundFlag(refundFlag.get());
                        }
                    }
                    //标记该订单中哪些航段做了升舱改期
                    if (orderTotalBriefInfo.getOrderSort().equals("Upgrade") || orderTotalBriefInfo.getOrderSort().equals(OrderSortEnum.Change.getOrderSort())) {
                        AtomicInteger index = new AtomicInteger();
                        orderTotalBriefInfo.getPassengerSegmentList().forEach(passengerSegment -> {
                            if (passengerSegment.getFormerSegmentId() != 0) {
                                if (orderTotalBriefInfo.getOrderSort().equals(OrderSortEnum.Change.getOrderSort())) {
                                    orderTotalBriefInfo.getSegmentInfoList().forEach(segmentInfo -> {
                                        SegmentInfo formerSegmentInfo = formerSegmentList.get(passengerSegment.getFormerSegmentId());
                                        if (segmentInfo.getSegmentID() == passengerSegment.getSegmentId()) {
                                            if (segmentInfo.getFlightNo().equals(formerSegmentInfo.getFlightNo()) &&
                                                    segmentInfo.getArrAirport().equals(formerSegmentInfo.getArrAirport()) &&
                                                    segmentInfo.getDepAirport().equals(formerSegmentInfo.getDepAirport()) &&
                                                    segmentInfo.getDepDateTime().equals(formerSegmentInfo.getDepDateTime()) &&
                                                    segmentInfo.getCabin().equals(formerSegmentInfo.getCabin()) &&
                                                    segmentInfo.getCabinClass().equals(formerSegmentInfo.getCabinClass())) {
                                                segmentInfo.setChangeSegment(false);
                                            } else {
                                                segmentInfo.setChangeSegment(true);
                                                index.getAndIncrement();
                                            }
                                        }
                                    });
                                } else {
                                    String cabin = formerSegmentList.get(passengerSegment.getFormerSegmentId()).getCabin();
                                    String cabinClass = formerSegmentList.get(passengerSegment.getFormerSegmentId()).getCabinClass();
                                    orderTotalBriefInfo.getSegmentInfoList().forEach(segmentInfo -> {
                                        if (segmentInfo.getSegmentID() == passengerSegment.getSegmentId()) {
                                            if (!cabinClass.equals(segmentInfo.getCabinClass())) {
                                                segmentInfo.setChangeSegment(true);
                                                segmentInfo.setFormatCabin(cabin);
                                                segmentInfo.setFormatCabinClass(cabinClass);
                                                segmentInfo.setFormatCabinClassName(transformCabinClassName(cabinClass));
                                                segmentInfo.setCabinClassName(transformCabinClassName(segmentInfo.getCabinClass()));
                                            } else {
                                                segmentInfo.setChangeSegment(false);
                                            }
                                        }
                                    });
                                }
                            }
                        });
                        if (orderTotalBriefInfo.getOrderSort().equals(OrderSortEnum.Change.getOrderSort()) && index.get() == 0) {
                            orderTotalBriefInfo.getSegmentInfoList().forEach(segmentInfo ->
                                    segmentInfo.setChangeSegment(true)
                            );
                        }
                    }
                    //先飞后付立减金需要手动扣减  20240725  暂时注释
                    /*if (null != orderTotalBriefInfo.getPayDeductAmt() && orderTotalBriefInfo.getPayDeductAmt() > 0.0 && null != orderTotalBriefInfo.getAmount()) {
                        double max = Math.max(orderTotalBriefInfo.getAmount() - orderTotalBriefInfo.getPayDeductAmt(), 0d);
                        orderTotalBriefInfo.setAmount(orderTotalBriefInfo.getTaxAmount() >= max ? orderTotalBriefInfo.getTaxAmount() : max);
                    }*/
                    //乘机人证件信息隐藏
                    if (CollectionUtils.isNotEmpty(orderTotalBriefInfo.getPassengerInfoList())) {
                        for (PtPassengerInfo ptPassengerInfo : orderTotalBriefInfo.getPassengerInfoList()) {
                            ptPassengerInfo.setCertNo(SensitiveInfoHider.hideSensitiveInfo(ptPassengerInfo.getCertNo()));
                        }
                    }
                }
            }
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            resp.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo("查询结果:" + resp.getErrorInfo());
        }
        return resp;
    }

    /**
     * 关闭部分订单改期按钮展示
     *
     * @param orderTotalBriefInfo
     */
    private void closeChange(OrderTotalBriefInfo orderTotalBriefInfo) {

        Boolean YjYs = false;
        if (CollectionUtils.isNotEmpty(orderTotalBriefInfo.getPassengerInfoList())) {
            YjYs = FareBasisEnum.YJYS_FARE.getFareBasisCode().equalsIgnoreCase(orderTotalBriefInfo.getPassengerInfoList().get(0).getPassengerIdentity());
        }
        //addon，spa不支持改期
        if ((FareTypeEnum.ADDON.getFare().equals(orderTotalBriefInfo.getFareType()) && HandlerConstants.TRIP_TYPE_D.equals(orderTotalBriefInfo.getFlightType()))
                || FareTypeEnum.SPA.getFare().equals(orderTotalBriefInfo.getFareType())
                || (FareTypeEnum.ONEWAY.getFare().equals(orderTotalBriefInfo.getFareType()) && !YjYs)
                // 单独购保不显示改期按钮
                || OrderSortEnum.Insurance.getOrderSort().equals(orderTotalBriefInfo.getOrderSort())) {
            orderTotalBriefInfo.setChangeFlag(false);
        }
        //国际机票订单关闭改期按钮
        if (HandlerConstants.TRIP_TYPE_I.equalsIgnoreCase(orderTotalBriefInfo.getFlightType()) && "N".equals(handConfig.getInternationalChange())) {
            orderTotalBriefInfo.setChangeFlag(false);
        }
        // 使用无限飞卡的关闭改期按钮
        if (orderTotalBriefInfo.getPassengerInfoList().stream().anyMatch(ptPassengerInfo ->
                StringUtils.isNotBlank(ptPassengerInfo.getUnlimitedFlyCardNo()))) {
            orderTotalBriefInfo.setChangeFlag(false);
        }

        String[] split = handConfig.getAwardFlyFreeTicketCabin().split(",");
        if (orderTotalBriefInfo.getSegmentInfoList() != null) {
            for (SegmentInfo segmentInfo : orderTotalBriefInfo.getSegmentInfoList()) {
                //奖励飞判断，如果包含I或者N，就设置为不可改期
                for (String s : split) {
                    if (segmentInfo.getCabin() != null && segmentInfo.getCabin().equals(s)) {
                        orderTotalBriefInfo.setChangeFlag(false);
                    }
                }
                //九元航班不支持,订单改期
                if (StringUtils.isNotBlank(segmentInfo.getCarrierFlightNo()) &&
                        segmentInfo.getCarrierFlightNo().startsWith("AQ")) {
                    orderTotalBriefInfo.setChangeFlag(false);
                }
            }
        }
        //使用主题卡兑换的订单关闭改期按钮
        if (StringUtils.isNotEmpty(orderTotalBriefInfo.getVoucherType()) && !orderTotalBriefInfo.isVoucherChange()) {
            orderTotalBriefInfo.setChangeFlag(false);
        }
        //第一程为直达,第二程为中转,不允许改期
        if (HandlerConstants.TRIP_TYPE_I.equalsIgnoreCase(orderTotalBriefInfo.getFlightType())) {
            //去程航班
            List<SegmentInfo> goChangeFlightInfo = orderTotalBriefInfo.getSegmentInfoList()
                    .stream().filter(segmentInfo -> FlightDirection.GO.getCode().equals(segmentInfo.getFlightDirection())).collect(Collectors.toList());
            //返程航班
            List<SegmentInfo> backChangeFlightInfo = orderTotalBriefInfo.getSegmentInfoList()
                    .stream().filter(segmentInfo -> FlightDirection.BACK.getCode().equals(segmentInfo.getFlightDirection())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(backChangeFlightInfo)) {
                if (goChangeFlightInfo.size() != backChangeFlightInfo.size()) {
                    orderTotalBriefInfo.setChangeFlag(false);
                }
            }


        }

    }

    @ApiOperation(value = "查询错购客票机票订单列表", notes = "查询错购客票机票订单列表")
    @RequestMapping(value = "/queryErrorBuyOrderTotalBrief", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp<List<ErrorBuyOrderTotalBriefResponse>> queryErrorBuyOrderTotalBrief(@RequestBody @Validated BaseReq<OrderBriefReq> req, BindingResult bindingResult, HttpServletRequest request) {
        BaseResp resp = new BaseResp();
        String reqId = MdcUtils.getRequestId();
        String ip = getClientIP(request);
        String reqJson = JsonMapper.buildNormalMapper().toJson(req);
        try {
            if (bindingResult.hasErrors()) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setErrorInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
                return resp;
            }
            //判断登录状态
            OrderBriefReq briefReq = req.getRequest();
            boolean flag = this.checkKeyInfo(briefReq.getCustomerNo(), briefReq.getLoginKeyInfo(), req.getChannelCode());
            if (!flag) {
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                return resp;
            }
            String userNo = getChannelInfo(req.getChannelCode(), "10");
            //构建请求类
            if (briefReq.getPageNo() <= 0) {
                briefReq.setPageNo(1);
            }
            if (briefReq.getPageSize() <= 0) {
                briefReq.setPageSize(20);
            }
            PtOrderTotalBriefReq ptBrief = new PtOrderTotalBriefReq(HandlerConstants.VERSION, req.getChannelCode(), userNo);
            if (!StringUtil.isNullOrEmpty(briefReq.getState())) {
                if ("Finish".equalsIgnoreCase(briefReq.getState()) || "Cancel".equalsIgnoreCase(briefReq.getState())) {
                    ptBrief.setOrderState(briefReq.getState());
                    ptBrief.setPayState("");
                } else if ("UnPay".equalsIgnoreCase(briefReq.getState())) {
                    ptBrief.setOrderState("Booking");
                    ptBrief.setPayState(briefReq.getState());
                } else {//全部
                    ptBrief.setOrderState("");
                    ptBrief.setPayState("");
                }
            }
            ptBrief.setSubOrderTypeFilterIndex(0);
            ptBrief.setSubOrderTypeFilterContent(SUBORDER_FILTER_CONTENT);
            ptBrief.setRemoveNullType(true);
            Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
            ptBrief = createReq(ptBrief, briefReq);
            HttpResult result = HttpUtil.doPostClient(ptBrief, HandlerConstants.URL_FARE_API + HandlerConstants.ORDER_ERROR_BUY_BRIEF, headMap, handConfig.getReadTimeout(), handConfig.getConnectTimeout());
            if (result.isResult()) {
                OrderTotalBriefResp orderTotalBriefResp = (OrderTotalBriefResp) JsonUtil.jsonToBean(result.getResponse(), OrderTotalBriefResp.class);
                getErrorBuyOrderTotalBriefResp(orderTotalBriefResp, ip, resp);
                return resp;
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo(LOG_RESP_ONE);
                return resp;
            }
        } catch (Exception e) {
            saveError("查询订单列表", reqId, ip, reqJson, e);
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo(LOG_RESP_THREE);
            return resp;
        }

    }

    /**
     * 获取错票列表信息
     *
     * @param totalBriefResp
     * @param ip
     * @param baseResp
     * @return
     */
    private BaseResp<List<ErrorBuyOrderTotalBriefResponse>> getErrorBuyOrderTotalBriefResp(OrderTotalBriefResp totalBriefResp, String ip, BaseResp<List<ErrorBuyOrderTotalBriefResponse>> baseResp) {
        if ("1001".equals(totalBriefResp.getResultCode())) {
            //将订单按照时间排序
            sortByCreateTime(totalBriefResp.getOrderBriefInfoList());
            List<ErrorBuyOrderTotalBriefResponse> errorBuyOrderTotalBriefResponses = new ArrayList<>();
            if (!StringUtil.isNullOrEmpty(totalBriefResp.getOrderBriefInfoList())) {
                for (OrderTotalBriefInfo orderTotalBriefInfo : totalBriefResp.getOrderBriefInfoList()) {
                    ErrorBuyOrderTotalBriefResponse buyOrderTotalBriefResponse = new ErrorBuyOrderTotalBriefResponse();
                   //“会员安心退”功能屏蔽“机+迪士尼”产品
                    if (CollectionUtils.isNotEmpty(orderTotalBriefInfo.getOtherOrderInfoList())) {
                        boolean DisneyTicket = orderTotalBriefInfo.getOtherOrderInfoList().stream().anyMatch(otherOrderInfo -> VoucherTypesEnum.DISNEYTICKET.getCode().equals(otherOrderInfo.getSaleCouponInfo().getCouponSource()));
                        if (DisneyTicket) {
                            continue;
                        }
                    }
                    List<ErrorBuyOrderTotalBriefInfo> totalBriefInfoList = new ArrayList<>();
                    if (!StringUtil.isNullOrEmpty(orderTotalBriefInfo.getSegmentInfoList())) {
                        for (SegmentInfo segmentInfo : orderTotalBriefInfo.getSegmentInfoList()) {
                            AirPortInfoDto depAirPort = localCacheService.getLocalAirport(segmentInfo.getDepAirport(), DateUtils.toDate(segmentInfo.getDepDateTime(), DateUtils.YYYY_MM_DD_HH_MM_PATTERN));
                            AirPortInfoDto arrAirPort = localCacheService.getLocalAirport(segmentInfo.getArrAirport(), DateUtils.toDate(segmentInfo.getDepDateTime(), DateUtils.YYYY_MM_DD_HH_MM_PATTERN));
                            segmentInfo.setDepCityName(depAirPort == null ? segmentInfo.getDepAirport() : depAirPort.getCityName());
                            segmentInfo.setArrCityName(arrAirPort == null ? segmentInfo.getArrAirport() : arrAirPort.getCityName());
                            segmentInfo.setDepAirportName(depAirPort == null ? segmentInfo.getDepAirport() : depAirPort.getAirPortName());
                            segmentInfo.setArrAirportName(arrAirPort == null ? segmentInfo.getArrAirport() : arrAirPort.getAirPortName());
                            ErrorBuyOrderTotalBriefInfo info = new ErrorBuyOrderTotalBriefInfo();
                            info.setOrderNo(orderTotalBriefInfo.getOrderNo());
                            info.setChannelOrderNo(orderTotalBriefInfo.getChannelOrderNo());
                            info.setArrAirportCode(segmentInfo.getArrAirport());
                            info.setArrAirPortName(segmentInfo.getArrAirportName());
                            info.setArrAirportTerminal(segmentInfo.getArrTerm());
                            info.setArrCityName(segmentInfo.getArrCityName());
                            info.setCabin(segmentInfo.getCabin());
                            info.setCabinName(getCabinName(segmentInfo.getCabin()));
                            info.setDepAirportCode(segmentInfo.getDepAirport());
                            info.setDepAirPortName(segmentInfo.getDepAirportName());
                            info.setDepAirportTerminal(segmentInfo.getDepTerm());
                            info.setDepCityName(segmentInfo.getDepCityName());
                            info.setFlightNo(segmentInfo.getFlightNo());
                            //处理机型转换
                            Map<String, AircraftModel> stringAircraftModelMap = toAircraftModelMap(handConfig.getAircraftModel());
                            if (stringAircraftModelMap != null) {
                                AircraftModel aircraftModel = stringAircraftModelMap.get(segmentInfo.getPlaneStyle());
                                if (aircraftModel != null) {
                                    info.setPlaneType(aircraftModel.getRemark());
                                } else {
                                    info.setPlaneType(segmentInfo.getPlaneStyle());
                                }
                            }
                            if (depAirPort != null && arrAirPort != null) {
                                String depCityTimeZone = depAirPort.getCityTimeZone();
                                String arrCityTimeZone = arrAirPort.getCityTimeZone();
                                //飞行时长
                                if ((!StringUtil.isNullOrEmpty(arrCityTimeZone)) &&
                                        (!StringUtil.isNullOrEmpty(depCityTimeZone)) &&
                                        (!StringUtil.isNullOrEmpty(segmentInfo.getDepDateTime())) &&
                                        (!StringUtil.isNullOrEmpty(segmentInfo.getArrDateTime()))) {
                                    //添加夏、冬令时处理
                                    if (!depCityTimeZone.equals(arrCityTimeZone)) {
                                        depCityTimeZone = FlightUtil.convertSummerOrWinterTime(depCityTimeZone, segmentInfo.getDepDateTime(), depAirPort);
                                        arrCityTimeZone = FlightUtil.convertSummerOrWinterTime(arrCityTimeZone, segmentInfo.getArrDateTime(), arrAirPort);
                                    }
                                    //飞行时长
                                    long flightTime = DateUtils.calDuration(segmentInfo.getDepDateTime(), depCityTimeZone, segmentInfo.getArrDateTime(), arrCityTimeZone);
                                    info.setFlightTime(DateUtils.formatTime(flightTime));
                                    //跨天
                                    int day = DateUtils.diffDays(segmentInfo.getDepDateTime().substring(0, 10), segmentInfo.getArrDateTime().substring(0, 10), DateUtils.YYYY_MM_DD_PATTERN);
                                    info.setDurDay(day < 0 ? 0 : day);
                                }
                            }
                            info.setDepTime(segmentInfo.getDepDateTime().split(" ")[1]);
                            info.setDepDate(segmentInfo.getDepDateTime().split(" ")[0]);
                            info.setArrayTime(segmentInfo.getArrDateTime().split(" ")[1]);
                            //周几
                            String weekStr = DateUtils.getWeekStr(DateUtils.toDate(info.getDepDate()));
                            info.setWeekDay(weekStr);
                            totalBriefInfoList.add(info);
                        }
                        buyOrderTotalBriefResponse.setTotalBriefInfoList(totalBriefInfoList);
                        errorBuyOrderTotalBriefResponses.add(buyOrderTotalBriefResponse);
                    }
                }
            }
            baseResp.setObjData(errorBuyOrderTotalBriefResponses);
            baseResp.setResultCode(WSEnum.SUCCESS.getResultCode());
            baseResp.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
        } else {
            baseResp.setResultCode(WSEnum.ERROR.getResultCode());
            baseResp.setErrorInfo("查询结果:" + totalBriefResp.getErrorInfo());
        }
        return baseResp;
    }

    //机型JSON转对象
    public static Map<String, AircraftModel> toAircraftModelMap(String aircraftModelJson) {
        try {
            Type type = new TypeToken<Map<String, AircraftModel>>() {
            }.getType();

            return (Map<String, AircraftModel>) JsonUtil.jsonToMap(aircraftModelJson, type);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 得到舱位名称
     */
    private String getCabinName(String cabinCode) {
        String cabinClass = CommonUtil.getCabinClassByCabinCode(cabinCode, handConfig.getCabinClass());
        return CommonUtil.showCabinClassName(cabinClass);
    }


    /**
     * 结合换成航班
     * 例：三亚—上海—赫尔辛基 结合成 三亚—赫尔辛基
     *
     * @param segmentInfos
     * @return
     */
    private List<SegmentInfo> combineSegmentInfos(List<SegmentInfo> segmentInfos) {
        if (CollectionUtils.isEmpty(segmentInfos)) {
            return new ArrayList<>();
        } else if (segmentInfos.size() == 1) {
            return segmentInfos;
        }
        List<SegmentInfo> result = new ArrayList<>();
        //去程
        List<SegmentInfo> goList = new ArrayList<>();
        //返程
        List<SegmentInfo> backList = new ArrayList<>();
        segmentInfos.forEach(segmentInfo -> {
            if (FlightDirection.GO.getCode().equals(segmentInfo.getFlightDirection())) {
                goList.add(segmentInfo);
            } else if (FlightDirection.BACK.getCode().equals(segmentInfo.getFlightDirection())) {
                backList.add(segmentInfo);
            }
        });
        if (CollectionUtils.isNotEmpty(goList)) {
            result.add(combineSegment(goList));
        }
        if (CollectionUtils.isNotEmpty(backList)) {
            result.add(combineSegment(backList));
        }
        return result;
    }

    private SegmentInfo combineSegment(List<SegmentInfo> segmentInfos) {
        if (CollectionUtils.isEmpty(segmentInfos)) {
            return null;
        } else if (segmentInfos.size() == 1) {
            return segmentInfos.get(0);
        } else {
            SegmentInfo first = segmentInfos.get(0);
            SegmentInfo last = segmentInfos.get(segmentInfos.size() - 1);
            first.setArrAirport(last.getArrAirport());
            first.setArrCity(last.getArrCity());
            first.setArrDateTime(last.getArrDateTime());
            return first;
        }
    }

    /**
     * 处理订单列表中保险名称
     */
    private void setInsuranceName(OrderTotalBriefResp orderTotalBriefResp) {
        if (CollectionUtils.isEmpty(orderTotalBriefResp.getOrderBriefInfoList())) {
            return;
        }
        orderTotalBriefResp.getOrderBriefInfoList().stream()
                .filter(orderTotalBriefInfo -> CollectionUtils.isNotEmpty(orderTotalBriefInfo.getInsuranceInfoList()))
                .forEach(orderTotalBriefInfo -> {
                    orderTotalBriefInfo.getInsuranceInfoList().stream()
                            .forEach(insuranceInfo -> {
                                Set<InsureInfo> insuranceList = getInsuranceList();
                                if (CollectionUtils.isEmpty(insuranceList)) {
                                    return;
                                }
                                insuranceList.stream()
                                        .filter(insureInfo -> insureInfo.getInsId().equalsIgnoreCase(insuranceInfo.getInsuranceCode()))
                                        .forEach(insureInfo -> insuranceInfo.setInsuranceName(insureInfo.getInsNm()));
                            });
                });
    }

    /**
     * 得到保险列表
     */
    private Set<InsureInfo> getInsuranceList() {
        List<InsureQueryRule> insureQueryRules = insuranceService.getInsuranceList();
        Set<InsureInfo> insureInfoList = new HashSet<>();
        insureQueryRules.forEach(insureQueryRule ->
                insureQueryRule.getInsureList().stream()
                        .filter(insureInfo -> org.apache.commons.lang3.StringUtils.isNotBlank(insureInfo.getInsId()))
                        .forEach(insureInfoList::add));
        return insureInfoList;
    }

    /**
     * 处理订单列表票号和旅客姓名
     *
     * @param orderTotalBriefResp
     */
    private void SetPassNameAndTicketNo(OrderTotalBriefResp orderTotalBriefResp) {
        if (CollectionUtils.isEmpty(orderTotalBriefResp.getOrderBriefInfoList())) {
            return;
        }
        orderTotalBriefResp.getOrderBriefInfoList().stream().
                filter(orderTotalBriefInfo -> CollectionUtils.isNotEmpty(orderTotalBriefInfo.getPassengerSegmentList()) && CollectionUtils.isNotEmpty(orderTotalBriefInfo.getPassengerInfoList()))
                .forEach(orderTotalBriefInfo -> {
                    orderTotalBriefInfo.setPassName(orderTotalBriefInfo.getPassengerInfoList().get(0).getPassengerName());
                    orderTotalBriefInfo.setTicketNo(getReplace(orderTotalBriefInfo.getPassengerSegmentList().get(0).getTicketNo()));
                });
    }

    /**
     * 去掉字符串的第一个"-"
     */
    private String getReplace(String s) {
        if (StringUtils.isBlank(s)) {
            return null;
        }
        return s.replace("-", "");
    }

    @InterfaceLog
    @ApiOperation(value = "机票取消订单删除", notes = "机票取消订单删除")
    @RequestMapping(value = "/deleteCancelOrder", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp deleteCancelOrder(@RequestBody @Validated BaseReq<DeleteOrderReq> req, BindingResult bindingResult, HttpServletRequest request) {
        BaseResp resp = new BaseResp();
        //判断登录状态
        DeleteOrderReq deleteOrderReq = req.getRequest();
        String ip = getClientIP(request);
        if (bindingResult.hasErrors()) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
            return resp;
        }
        Objects.requireNonNull(deleteOrderReq, "request数据不可为空");
        try {
            boolean flag = this.checkKeyInfo(deleteOrderReq.getCustomerNo(), deleteOrderReq.getLoginKeyInfo(), req.getChannelCode());
            if (!flag) {
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                return resp;
            }
            String userNo = getChannelInfo(req.getChannelCode(), "10");
            //构建请求类
            PtDeleteOrderReq orderReq = new PtDeleteOrderReq(HandlerConstants.VERSION, req.getChannelCode(), userNo);
            orderReq.setCustomerNo(deleteOrderReq.getCustomerNo());
            orderReq.setChannelOrderNo(deleteOrderReq.getChannelOrderNo());
            orderReq.setOrderNo(deleteOrderReq.getOrderNo());
            orderReq.setRandCode(MdcUtils.getRequestId());
            //调用接口
            Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
            HttpResult result = doPostClient(orderReq, HandlerConstants.URL_FARE_API + HandlerConstants.DELETE_CANCEL_ORDER, headMap);
            if (result.isResult()) {
                DeleteOrderResp orderResp = (DeleteOrderResp) JsonUtil.jsonToBean(result.getResponse(), DeleteOrderResp.class);
                if (orderResp.getResultCode().equals("1001")) {
                    resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                    resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                } else {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo(orderResp.getErrorInfo());
                }
                return resp;
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("网络请求异常");
                return resp;
            }

        } catch (Exception e) {
            log.error("请求号:{}，IP地址:{}，删除订单出错：", MdcUtils.getRequestId(), ip, e);
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("删除订单出错");
            return resp;
        }
    }

    @ApiOperation(value = "待出行机票订单列表", notes = "待出行机票订单列表")
    @RequestMapping(value = "/queryOrderTrvel", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public OrderTotalBriefResp queryOrderTrvel(@RequestBody BaseReq<OrderBriefReq> req, HttpServletRequest request) {
        OrderTotalBriefResp resp = new OrderTotalBriefResp();
        String reqId = StringUtil.newGUID() + "queryOrderBrief";
        String ip = getClientIP(request);
        try {
            ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
            Validator validator = factory.getValidator();
            Set<ConstraintViolation<BaseReq<OrderBriefReq>>> violations = validator.validate(req);
            if (CollectionUtils.isNotEmpty(violations)) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setErrorInfo(violations.iterator().next().getMessage());
                return resp;
            }
            //判断登录状态
            OrderBriefReq briefReq = req.getRequest();
            boolean flag = this.checkKeyInfo(briefReq.getCustomerNo(), briefReq.getLoginKeyInfo(), req.getChannelCode());
            if (!flag) {
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                return resp;
            }
            //构建请求类
            String userNo = getChannelInfo(req.getChannelCode(), "10");
            if (briefReq.getPageNo() <= 0) {
                briefReq.setPageNo(1);
            }
            if (briefReq.getPageSize() <= 0) {
                briefReq.setPageSize(20);
            }
            PtOrderTotalBriefReq ptBrief = new PtOrderTotalBriefReq(HandlerConstants.VERSION, req.getChannelCode(), userNo);
            if (StringUtils.isBlank(briefReq.getDateBegin())) {
                briefReq.setDateBegin(DateUtils.dateToString(new Date(), DateUtils.YYYY_MM_DD_PATTERN));
            }
            if (StringUtils.isBlank(briefReq.getDateEnd())) {
                Calendar calendar = Calendar.getInstance();
                calendar.add(Calendar.MONTH, 6);
                briefReq.setDateEnd(DateUtils.dateToString(calendar.getTime(), DateUtils.YYYY_MM_DD_PATTERN));
            }
            createReq(ptBrief, briefReq);
            //发送请求
            Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
            HttpResult result = doPostClient(ptBrief, HandlerConstants.URL_FARE_API + HandlerConstants.NEW_SUB_QUERY_ORDER_TRVEL, headMap);
            if (result.isResult()) {
                resp = (OrderTotalBriefResp) JsonUtil.jsonToBean(result.getResponse(), OrderTotalBriefResp.class);
                //2021-06-28 处理多个子订单重复的航段信息
                //resp.getOrderBriefInfoList().stream().flatMap()
                resp.getOrderBriefInfoList().stream().forEach(orderTotalBriefInfo -> {
                    List<SegmentInfo> segmentInfos = orderTotalBriefInfo.getSegmentInfoList().stream()
                            .collect(Collectors.toMap(SegmentInfo::getFlightNo, a -> a, (o1, o2) -> {
                                return o1;
                            })).values().stream().sorted(Comparator.comparingInt(SegmentInfo::getSegNO)).collect(Collectors.toList());
                    orderTotalBriefInfo.setSegmentInfoList(segmentInfos);
                });
                return getOrderTotalBriefResp(resp, reqId, ip);
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo(LOG_RESP_ONE);
                String reqJson = JsonUtil.objectToJson(req);
                log.info(LOG_REQ_DESP, reqId, ip, reqJson);
                return resp;
            }
        } catch (Exception e) {
            log.error("请求号:{}，IP地址:{}，" + "LOG_RESP_TWO" + "：{}", reqId, ip, e);
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo(LOG_RESP_THREE);
            return resp;
        }
    }

    @ApiOperation(value = "查询未出行航班列表（关怀通道查询航班列表使用）", notes = "查询未出行航班列表（关怀通道查询航班列表使用）")
    @RequestMapping(value = "/queryOrderTravelList", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @InterfaceLog
    public BaseResp<List<OrderTravelListResponse>> queryOrderTravelList(@RequestBody BaseReq<UserInfoMust> req, HttpServletRequest request) {
        BaseResp<List<OrderTravelListResponse>> baseResp = new BaseResp<>();
        String reqId = StringUtil.newGUID() + "queryOrderTravel";
        String ip = getClientIP(request);
        try {
            //校验参数
            this.checkRequest(req);
            //判断登录状态
            UserInfoMust userInfoMust = req.getRequest();
            //构建请求类
            String userNo = getChannelInfo(req.getChannelCode(), "10");
            PtOrderTotalBriefReq ptBrief = new PtOrderTotalBriefReq(HandlerConstants.VERSION, req.getChannelCode(), userNo);
            if (StringUtils.isBlank(ptBrief.getCreateDateBegin())) {
                ptBrief.setCreateDateBegin((DateUtils.dateToString(new Date(), DateUtils.YYYY_MM_DD_PATTERN)));
            }
            if (StringUtils.isBlank(ptBrief.getCreateDateEnd())) {
                Calendar calendar = Calendar.getInstance();
                calendar.add(Calendar.MONTH, 6);
                ptBrief.setCreateDateEnd(DateUtils.dateToString(calendar.getTime(), DateUtils.YYYY_MM_DD_PATTERN));
            }
            ptBrief.setPageNo(1);
            ptBrief.setPageSize(20);
            ptBrief.setCustomerNo(userInfoMust.getFfpId());
            //发送请求 获取近半年获取未出行订单列表
            Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
            HttpResult result = doPostClient(ptBrief, HandlerConstants.URL_FARE_API + HandlerConstants.NEW_SUB_QUERY_ORDER_TRVEL, headMap);
            if (result.isResult()) {
                OrderTotalBriefResp orderTotalBriefResp = (OrderTotalBriefResp) JsonUtil.jsonToBean(result.getResponse(), OrderTotalBriefResp.class);
                return getOrderTravelListResp(orderTotalBriefResp, ip, baseResp);
            } else {
                baseResp.setResultCode(WSEnum.ERROR.getResultCode());
                baseResp.setErrorInfo(LOG_RESP_ONE);
                String reqJson = JsonUtil.objectToJson(req);
                log.info(LOG_REQ_DESP, reqId, ip, reqJson);
                return baseResp;
            }
        } catch (Exception e) {
            log.error("请求号:{}，IP地址:{}，" + "LOG_RESP_TWO" + "：{}", reqId, ip, e);
            baseResp.setResultCode(WSEnum.ERROR.getResultCode());
            baseResp.setErrorInfo(LOG_RESP_THREE);
            return baseResp;
        }
    }

    /**
     * 查询未出行航班列表（关怀通道查询航班列表使用）
     *
     * @param totalBriefResp
     * @param ip
     * @param baseResp
     * @return
     */
    private BaseResp<List<OrderTravelListResponse>> getOrderTravelListResp(OrderTotalBriefResp totalBriefResp, String ip, BaseResp<List<OrderTravelListResponse>> baseResp) {
        if ("1001".equals(totalBriefResp.getResultCode())) {
            //将订单按照时间排序
            sortByCreateTime(totalBriefResp.getOrderBriefInfoList());
            List<OrderTravelListResponse> orderTravelListResponses = new ArrayList<>();
            if (!StringUtil.isNullOrEmpty(totalBriefResp.getOrderBriefInfoList())) {
                for (OrderTotalBriefInfo orderTotalBriefInfo : totalBriefResp.getOrderBriefInfoList()) {
                    OrderTravelListResponse orderTravelListResponse = new OrderTravelListResponse();
                    // 结合去程和返程的航段
//                    orderTotalBriefInfo.setSegmentInfoList(combineSegmentInfos(orderTotalBriefInfo.getSegmentInfoList()));
                    List<OrderTravelInfo> orderTravelInfos = new ArrayList<>();
                    if (!StringUtil.isNullOrEmpty(orderTotalBriefInfo.getSegmentInfoList())) {
                        for (SegmentInfo segmentInfo : orderTotalBriefInfo.getSegmentInfoList()) {
                            AirPortInfoDto depAirPort = localCacheService.getLocalAirport(segmentInfo.getDepAirport(), DateUtils.toDate(segmentInfo.getDepDateTime(), DateUtils.YYYY_MM_DD_HH_MM_PATTERN));
                            AirPortInfoDto arrAirPort = localCacheService.getLocalAirport(segmentInfo.getArrAirport(), DateUtils.toDate(segmentInfo.getDepDateTime(), DateUtils.YYYY_MM_DD_HH_MM_PATTERN));
                            segmentInfo.setDepCityName(depAirPort == null ? segmentInfo.getDepAirport() : depAirPort.getCityName());
                            segmentInfo.setArrCityName(arrAirPort == null ? segmentInfo.getArrAirport() : arrAirPort.getCityName());
                            segmentInfo.setDepAirportName(depAirPort == null ? segmentInfo.getDepAirport() : depAirPort.getAirPortName());
                            segmentInfo.setArrAirportName(arrAirPort == null ? segmentInfo.getArrAirport() : arrAirPort.getAirPortName());
                            OrderTravelInfo info = new OrderTravelInfo();
                            info.setOrderNo(orderTotalBriefInfo.getOrderNo());
                            info.setChannelOrderNo(orderTotalBriefInfo.getChannelOrderNo());
                            info.setArrAirportCode(segmentInfo.getArrAirport());
                            info.setArrAirPortName(segmentInfo.getArrAirportName());
                            info.setArrAirportTerminal(segmentInfo.getArrTerm());
                            info.setArrCityName(segmentInfo.getArrCityName());
                            info.setCabin(segmentInfo.getCabin());
                            info.setCabinName(getCabinName(segmentInfo.getCabin()));
                            info.setDepAirportCode(segmentInfo.getDepAirport());
                            info.setDepAirPortName(segmentInfo.getDepAirportName());
                            info.setDepAirportTerminal(segmentInfo.getDepTerm());
                            info.setDepCityName(segmentInfo.getDepCityName());
                            info.setFlightNo(segmentInfo.getFlightNo());
                            info.setSegNO(segmentInfo.getSegNO());
                            //处理机型转换
                            Map<String, AircraftModel> stringAircraftModelMap = toAircraftModelMap(handConfig.getAircraftModel());
                            if (stringAircraftModelMap != null) {
                                AircraftModel aircraftModel = stringAircraftModelMap.get(segmentInfo.getPlaneStyle());
                                if (aircraftModel != null) {
                                    info.setPlaneType(aircraftModel.getRemark());
                                } else {
                                    info.setPlaneType(segmentInfo.getPlaneStyle());
                                }
                            }
                            if (depAirPort != null && arrAirPort != null) {
                                String depCityTimeZone = depAirPort.getCityTimeZone();
                                String arrCityTimeZone = arrAirPort.getCityTimeZone();
                                //飞行时长
                                if ((!StringUtil.isNullOrEmpty(arrCityTimeZone)) &&
                                        (!StringUtil.isNullOrEmpty(depCityTimeZone)) &&
                                        (!StringUtil.isNullOrEmpty(segmentInfo.getDepDateTime())) &&
                                        (!StringUtil.isNullOrEmpty(segmentInfo.getArrDateTime()))) {
                                    //添加夏、冬令时处理
                                    if (!depCityTimeZone.equals(arrCityTimeZone)) {
                                        depCityTimeZone = FlightUtil.convertSummerOrWinterTime(depCityTimeZone, segmentInfo.getDepDateTime(), depAirPort);
                                        arrCityTimeZone = FlightUtil.convertSummerOrWinterTime(arrCityTimeZone, segmentInfo.getArrDateTime(), arrAirPort);
                                    }
                                    //飞行时长
                                    long flightTime = DateUtils.calDuration(segmentInfo.getDepDateTime(), depCityTimeZone, segmentInfo.getArrDateTime(), arrCityTimeZone);
                                    info.setFlightTime(DateUtils.formatTime(flightTime));
                                    //跨天
                                    int day = DateUtils.diffDays(segmentInfo.getDepDateTime().substring(0, 10), segmentInfo.getArrDateTime().substring(0, 10), DateUtils.YYYY_MM_DD_PATTERN);
                                    info.setDurDay(day < 0 ? 0 : day);
                                }
                            }
                            info.setDepTime(segmentInfo.getDepDateTime().split(" ")[1]);
                            info.setDepDate(segmentInfo.getDepDateTime().split(" ")[0]);
                            info.setArrayTime(segmentInfo.getArrDateTime().split(" ")[1]);
                            //周几
                            String weekStr = DateUtils.getWeekStr(DateUtils.toDate(info.getDepDate()));
                            info.setWeekDay(weekStr);
                            //处理前端响应、票号和乘客姓名
                            List<OrderTravelPassengerSegmentInfo> passengerSegmentInfoList = new ArrayList<>();
                            if (CollectionUtils.isNotEmpty(orderTotalBriefInfo.getPassengerSegmentList())) {
                                List<PassengerSegment> passengerSegments = orderTotalBriefInfo.getPassengerSegmentList().stream().filter(passengerSegment -> passengerSegment.getSegmentId() == segmentInfo.getSegmentID() && TicketStateEnum.OPEN_FOR_USE.equalsIgnoreCase(passengerSegment.getTicketState())).collect(Collectors.toList());
                                if (CollectionUtils.isNotEmpty(passengerSegments)) {
                                    passengerSegments.forEach(passengerSegment -> {
                                        OrderTravelPassengerSegmentInfo orderTravelPassengerSegmentInfo = new OrderTravelPassengerSegmentInfo();
                                        orderTravelPassengerSegmentInfo.setFlightNo(segmentInfo.getFlightNo());
                                        orderTravelPassengerSegmentInfo.setTicketNo(passengerSegment.getTicketNo());
                                        List<PtPassengerInfo> ptPassengerInfos = orderTotalBriefInfo.getPassengerInfoList().stream().filter(ptPassengerInfo -> ptPassengerInfo.getPassengerID() == passengerSegment.getPassengerId()).collect(Collectors.toList());
                                        if (CollectionUtils.isNotEmpty(ptPassengerInfos)) {
                                            ptPassengerInfos.forEach(ptPassengerInfo -> {
                                                orderTravelPassengerSegmentInfo.setTravelName(ptPassengerInfo.getPassengerName());
                                            });
                                        }
                                        passengerSegmentInfoList.add(orderTravelPassengerSegmentInfo);
                                    });
                                }
                                if (CollectionUtils.isNotEmpty(passengerSegmentInfoList)) {
                                    info.setPassengerSegmentInfoList(passengerSegmentInfoList);
                                    orderTravelInfos.add(info);
                                }
                            }
                        }
                        //2021-06-28 关怀通道获取待出行订单处理多个子订单航段重复问题
                        if (CollectionUtils.isNotEmpty(orderTravelInfos)) {
                            List<OrderTravelInfo> travelInfos = orderTravelInfos.stream()
                                    .collect(Collectors.toMap(OrderTravelInfo::getFlightNo, a -> a, (o1, o2) -> {
                                        o1.getPassengerSegmentInfoList().addAll(o2.getPassengerSegmentInfoList());
                                        return o1;
                                    })).values().stream().sorted(Comparator.comparingInt(OrderTravelInfo::getSegNO)).collect(Collectors.toList());
                            orderTravelListResponse.setTravelInfos(travelInfos);
                            orderTravelListResponses.add(orderTravelListResponse);
                        }

                    }
                }
            }
            baseResp.setObjData(orderTravelListResponses);
            baseResp.setResultCode(WSEnum.SUCCESS.getResultCode());
            baseResp.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
        } else {
            baseResp.setResultCode(WSEnum.ERROR.getResultCode());
            baseResp.setErrorInfo("查询结果:" + totalBriefResp.getErrorInfo());
        }
        return baseResp;
    }

    @ApiOperation(value = "待评价机票订单列表", notes = "待评价机票订单列表")
    @RequestMapping(value = "/queryQueryOrderNoComments", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public OrderTotalBriefResp queryQueryOrderNoComments(@RequestBody BaseReq<OrderBriefReq> req, HttpServletRequest request) {
        OrderTotalBriefResp resp = new OrderTotalBriefResp();
        String reqId = StringUtil.newGUID() + request.getRequestURI();
        String ip = getClientIP(request);
        try {
            ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
            Validator validator = factory.getValidator();
            Set<ConstraintViolation<BaseReq<OrderBriefReq>>> violations = validator.validate(req);
            if (CollectionUtils.isNotEmpty(violations)) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setErrorInfo(violations.iterator().next().getMessage());
                return resp;
            }
            //判断登录状态
            OrderBriefReq briefReq = req.getRequest();
            boolean flag = this.checkKeyInfo(briefReq.getCustomerNo(), briefReq.getLoginKeyInfo(), req.getChannelCode());
            if (!flag) {
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                return resp;
            }
            //构建请求类
            String userNo = getChannelInfo(req.getChannelCode(), "10");
            if (briefReq.getPageNo() <= 0) {
                briefReq.setPageNo(1);
            }
            if (briefReq.getPageSize() <= 0) {
                briefReq.setPageSize(20);
            }
            PtOrderTotalBriefReq ptBrief = new PtOrderTotalBriefReq(HandlerConstants.VERSION, req.getChannelCode(), userNo);
            createReq(ptBrief, briefReq);
            //发送请求
            Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
            HttpResult result = doPostClient(ptBrief, HandlerConstants.URL_FARE_API + HandlerConstants.NEW_SUB_QUERY_ORDER_COMMENT, headMap);
            if (result.isResult()) {
                resp = (OrderTotalBriefResp) JsonUtil.jsonToBean(result.getResponse(), OrderTotalBriefResp.class);
                return getOrderTotalBriefResp(resp, reqId, ip);
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo(LOG_RESP_ONE);
                String reqJson = JsonUtil.objectToJson(req);
                log.info(LOG_REQ_DESP, reqId, ip, reqJson);
                return resp;
            }
        } catch (Exception e) {
            log.error("请求号:{}，IP地址:{}，查询订单列表出错：", reqId, ip, e);
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo(LOG_RESP_THREE);
            return resp;
        }
    }

    @ApiOperation(value = "生成机票订单3.0", notes = "生成机票订单3.0")
    @RequestMapping(value = "orderBookingV3", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @InterfaceLog
    public TicketBookingResp ticketBookV3(@RequestBody @Validated TicketBookingV3Request bookReq, BindingResult bindingResult, HttpServletRequest request) {
        TicketBookingResp resp = new TicketBookingResp();
        String ip = this.getClientIP(request);
        String reqId = MdcUtils.getRequestId();
        String reqJson = JsonMapper.buildNormalMapper().toJson(bookReq);
        String headChannelCode = request.getHeader(HandlerConstants.TOKEN_CHANNELCODE);
        String clientVersion = request.getHeader(HandlerConstants.CLIENT_VERSION);
        String versionCode = request.getHeader(HandlerConstants.CLIENT_VERSION_CODE);
        String platforminfo = request.getHeader(HandlerConstants.CLIENT_PLATFORM);
        String blackBoxFrom = request.getHeader(HandlerConstants.BLACKBOX_FROM);
        if (bindingResult.hasErrors()) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
            return resp;
        }
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(bookReq.getFfpId(), bookReq.getLoginKeyInfo(), bookReq.getChannelCode());
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR_925_2.getResultCode());
            resp.setErrorInfo(WSEnum.ERROR_CHK_ERROR_925_2.getResultInfo());
            return resp;
        }
        try {
            //微信或M站
            boolean webClient = false;
            List<String> webClientList = new ArrayList<>(Arrays.asList(ChannelCodeEnum.MWEB.getChannelCode(), ChannelCodeEnum.WEIXIN.getChannelCode(), ChannelCodeEnum.WXAPP.getChannelCode(), ChannelCodeEnum.MP_ALIPAY.getChannelCode()));
            if (webClientList.contains(headChannelCode)) {
                webClient = true;
            }
            //支付宝渠道设备指纹使用的是H5，此处进行转换
            if (ChannelCodeEnum.MP_ALIPAY.getChannelCode().equals(headChannelCode)) {
                //headChannelCode = ChannelCodeEnum.WEIXIN.getChannelCode();
                blackBoxFrom = PlatFormEnum.H5.getSystemCode();
            }
            bookReq.setAppsys(platforminfo);
            //纯卡号无HO
            String memberNo = StringUtil.subStrFromStart(bookReq.getSfCardNo(), AirCompanyEnum.HO.getAirCompanyCode());
            //卡号补全HO
            String HOmemberNo = StringUtil.addPrefix(bookReq.getSfCardNo(), AirCompanyEnum.HO.getAirCompanyCode());
            bookReq.setSfCardNo(HOmemberNo);
            //20190417判断app渠道订单，如果没有版本号，返回下单失败，解决占座问题
            if (!webClient) {
                int version = VersionNoUtil.toVerInt(bookReq.getClientVersion());
                if (version < 5030000) {
                    log.error("App占座生成订单失败请求号:{},IP地址:{},客户端提交参数:{}", reqId, ip, reqJson);
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setErrorInfo("生成订单失败(" + version + ")");
                    return resp;
                }
            }
            int ver = VersionNoUtil.toMVerInt(versionCode);
            boolean useFareV30;// 是否使用V30运价
            if (ChannelCodeEnum.MOBILE.getChannelCode().equals(headChannelCode) && ver < 59000 && bookReq.getUseFareV30() == null) {
                // 5.9版本之前均使用V30运价
                useFareV30 = true;
                if (null == bookReq.getFlightInfoComb()) {
                    throw new RequestParamErrorException("航班航线列表不能为空");
                }
            } else {
                useFareV30 = null == bookReq.getUseFareV30() ? true : bookReq.getUseFareV30();
                if ((!useFareV30 && CollectionUtils.isEmpty(bookReq.getFlightInfoList())) || (useFareV30 && null == bookReq.getFlightInfoComb())) {
                    throw new RequestParamErrorException("航班航线列表不能为空");
                }
            }
            if (!webClient) {
                if (!StringUtil.isNullOrEmpty(bookReq.getClientVersion())) {
                    if (VersionNoUtil.toVerInt(bookReq.getClientVersion()) >= 5000000) {//新版的暂不使用校验
                        log.info("{}暂不使用极验验证", bookReq.getClientVersion());
                    } else {//4.1.00之后的设备需要进行验证5.0之前
                        GeetestLib gtSdk = new GeetestLib(HandlerConstants.ORDERBOOK_GEETEST_ID, HandlerConstants.ORDERBOOK_GEETEST_KEY);
                        //极验验证
                        HashMap<String, String> param = new HashMap<>();
                        DigestmodEnum digestmodEnum = DigestmodEnum.MD5;
                        param.put("digestmod", digestmodEnum.getName());
                        param.put("user_id", ip); //网站用户id
                        param.put("client_type", "h5"); //web:电脑上的浏览器；h5:手机上的浏览器，包括移动应用内完全内置的web_view；native：通过原生SDK植入APP应用的方式
                        param.put("ip_address", ip); //传输用户请求验证时所携带的IP
                        geetestService.validate(gtSdk, bookReq, param);
                    }
                }
                //是否进行设备验证
                if ("Y".equals(handConfig.getUseCheckDevice())) {
                    //验证设备信息，不存在的设备为非法设备
                    boolean checkDevice = this.checkDevice(bookReq.getDeviceId());
                    if (!checkDevice) {
                        log.error("请求号:{},此设备不合法,请求的设备号:{},ip地址:{},卡号:{}", reqId, bookReq.getDeviceId(), ip, memberNo);
                        resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                        resp.setErrorInfo("抱歉，生成订单失败， 请重新下单");
                        return resp;
                    }
                }
            }
            //下单参数检验
            checkTicketRule(bookReq, useFareV30, request, headChannelCode);
            //订单数量限制
            CheckUnPayOrderResp checkUnPayOrderResp = getCheckUnPayOrderResp(headChannelCode, bookReq.getFfpCardNo());
            if (!"1001".equals(checkUnPayOrderResp.getResultCode())) { //MOBILE
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo(checkUnPayOrderResp.getErrorInfo());
                return resp;
            }
            //查询会员信息
            String memberId = bookReq.getFfpId();
            String[] items = {
                    MemberDetailRequestItemsEnum.STATEINFO.eName,
                    MemberDetailRequestItemsEnum.BASICINFO.eName,
                    MemberDetailRequestItemsEnum.CONTACTINFO.eName,
                    MemberDetailRequestItemsEnum.CERTIFICATEINFO.eName,
                    MemberDetailRequestItemsEnum.REALVERIFYINFOS.eName};
            PtApiCRMRequest<PtMemberDetailRequest> ptApiRequest = CRMReqUtil.buildMemberDetailReq(memberNo, memberId, request, ChannelCodeEnum.MOBILE.getChannelCode(), items);
            PtCRMResponse<PtMemberDetail> ptCRMResponse = memberService.memberDetail(ptApiRequest);
            if (ptCRMResponse.getCode() != 0) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo(ptCRMResponse.getMsg());
                return resp;
            }
            // 是否实名
            boolean realName = false;
            List<MemberRealNameSummarySoaModel> realVerifyInfos = ptCRMResponse.getData().getRealVerifyInfos();
            //判断认证信息
            if (CollectionUtils.isNotEmpty(realVerifyInfos)) {
                //判断认证结果
                for (MemberRealNameSummarySoaModel realNameSummarySoaModel : realVerifyInfos) {
                    if (realNameSummarySoaModel.getStatus().equals(VerifyStatusEnum.PASS.code)) {
                        realName = true;
                        break;
                    }
                }
            }
            ClientInfo clientInfo = initClientInfo(request, bookReq.getChannelCode(), bookReq.getFfpId(), bookReq.getFfpCardNo());
            //使用积分，优惠券的判断条件
            if (!checkCouponScore(clientInfo, bookReq, headChannelCode, versionCode, ptCRMResponse, resp, memberNo)) {
                return resp;
            }
            boolean isUnlimitedCard = isUnlimitedCard(bookReq);
            //同盾校验
            //5.3版本使用同盾
            /*
              http://zentao.juneyaoair.com/zentao/task-view-3512.html
              会员级别大于等于金卡时不做风控
              http://zentao.juneyaoair.com/zentao/task-view-3513.html
              畅飞卡不做风控
             */
            if ("Y".equals(handConfig.getUseTongDun()) && !isUnlimitedCard) {
                if (StringUtil.isNullOrEmpty(bookReq.getAppsys())) {
                    bookReq.setAppsys("");
                }
                String buyMobile = "";
                String buyEmail = "";
                MemberBasicInfoSoaModel basicInfo = ptCRMResponse.getData().getBasicInfo();
                String buyName = CRMReqUtil.getMemberName(basicInfo);
                List<MemberContactSoaModel> contactSoaModelList = ptCRMResponse.getData().getContactInfo();
                //联系方式
                if (!StringUtil.isNullOrEmpty(contactSoaModelList)) {
                    for (MemberContactSoaModel con : contactSoaModelList) {
                        //手机号
                        if (con.getContactType() == ContactTypeEnum.MOBILE.getCode()) {
                            buyMobile = con.getContactNumber();
                        }
                        //邮箱
                        if (con.getContactType() == ContactTypeEnum.EMAIL.getCode()) {
                            buyEmail = con.getContactNumber();
                        }
                    }
                }
                Map<String, String> params = FraudApiInvoker.createOrderParam(bookReq, headChannelCode, blackBoxFrom, ip, buyName, buyMobile, buyEmail, useFareV30);
                if (params != null) {
                    Antifraud antifraud = new Antifraud(bookReq.getFfpId(), memberNo, ip, params);
                    FraudApiResponse fraudApiResponse = basicService.antifraud(headChannelCode, antifraud);
                    if (fraudApiResponse.getSuccess()) {
                        if (fraudApiResponse.getFinal_decision().equalsIgnoreCase(FinalDecisionEnum.REJECT.getCode())) {
                            resp.setResultCode(WSEnum.TONGDUN_FAIL_TRADE.getResultCode());
                            resp.setErrorInfo(WSEnum.TONGDUN_FAIL_TRADE.getResultInfo());
                            return resp;
                        }
                        ip = StringUtils.isNotBlank(fraudApiResponse.getVirtualIp()) ? fraudApiResponse.getVirtualIp() : ip;
                    }
                }
            } else {
                log.info("请求未进行同盾验证，渠道订单号：{}", bookReq.getChannelOrderNo());
            }
            bookReq.setOrderRequestIp(ip);
            if (!StringUtil.isNullOrEmpty(clientVersion)) {
                if (StringUtil.isNullOrEmpty(bookReq.getChannelPrivateInfo())) {
                    bookReq.setChannelPrivateInfo(clientVersion);
                } else {
                    bookReq.setChannelPrivateInfo(bookReq.getChannelPrivateInfo() + ";" + clientVersion);
                }
            }
            // 国际本人 或 国际实名开关是否打开
            boolean personInternalSelfFlag = "Y".equals(handConfig.getPersonInternalSelfFlag());
            boolean personInternalRealNameFlag = "Y".equals(handConfig.getPersonInternalRealNameFlag());
            // 非国际本人开发打开情况本人验证
            if (!personInternalSelfFlag) {
                // 清除本人标记重新判断
                bookReq.getPassengerInfoList().forEach(passengerInfo -> passengerInfo.setOwner(false));
                // 判断旅客是否是本人
                markOwnPassenger(bookReq.getPassengerInfoList(), ptCRMResponse, bookReq.getFlightFareType(), request, headChannelCode, memberId);
            }
            if (personInternalSelfFlag || personInternalRealNameFlag) {
                // 获取全部城市
                Set<String> cityCodeSet = Sets.newHashSet();
                bookReq.getFlightInfoList().forEach(flightInfo -> {
                    cityCodeSet.add(flightInfo.getDepCity());
                    cityCodeSet.add(flightInfo.getArrCity());
                });
                // 获取航线信息
                Segment segment = localCacheService.getSegment(cityCodeSet.toArray(new String[]{}));
                if (HandlerConstants.TRIP_TYPE_I.equals(segment.getSegmentType())) {
                    // 需要实名 且 未实名
                    if (personInternalRealNameFlag && !realName) {
                        throw new CommonException(WSEnum.NO_REAL_NAME.getResultCode(), "您还未实名认证，请先实名认证后再预订");
                    }
                    // 成人只能本人
                    if (personInternalSelfFlag) {
                        // 成人个数
                        int adtCount = 0;
                        boolean errorFlag = false;
                        for (PassengerInfo passengerInfo : bookReq.getPassengerInfoList()) {
                            // 成人
                            if (PassengerTypeEnum.ADT.getPassType().equals(passengerInfo.getPassengerType())) {
                                adtCount++;
                                // 非本人
                                if (!Boolean.TRUE.equals(passengerInfo.isOwner())) {
                                    errorFlag = true;
                                    break;
                                }
                            }
                        }
                        // 校验不通过 成人不为1 总人数超过三人 提示错误
                        if (errorFlag || adtCount != 1 || bookReq.getPassengerInfoList().size() > 3) {
                            throw new CommonException(WSEnum.ERROR_REQUEST_PARAMS.getResultCode(), handConfig.getCommonPersonWarmRemind(segment.getSegmentType()));
                        }
                    }
                }
            }
            // 免票订单只允许本人购买
            boolean isFreeTicketOrder = isFreeTicketOrder(bookReq);
            // 一单多券场景
            boolean isMultiCoupon = "1".equals(bookReq.getCouponScene());
            if (isFreeTicketOrder && !isMultiCoupon && !FlightQueryTypeEnum.THEME_CARD.getType().equals(bookReq.getFlightFareType())) {
                if (StringUtils.isBlank(bookReq.getCouponCode())) {
                    resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                    resp.setErrorInfo("请使用全额抵扣优惠券购买免票机票");
                    return resp;
                }
                if (bookReq.getPassengerInfoList().size() > 1) {
                    resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                    resp.setErrorInfo("全额抵扣优惠券仅能兑换一张机票");
                    return resp;
                }
                if (!bookReq.getPassengerInfoList().get(0).isOwner() && !bookReq.getPassengerInfoList().get(0).isBeneficiary()) {
                    resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                    resp.setErrorInfo("全额抵扣优惠券仅限客户本人及受益人使用");
                    return resp;
                }

                if (bookReq.getPassengerInfoList().get(0).isBeneficiary() && !bookReq.getPassengerInfoList().get(0).isValid()) {
                    resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                    resp.setErrorInfo("乘机人信息与已生效的受益人信息不一致，请核实");
                    return resp;
                }
            }
            String isNewProcessInsurance = "N";
            // 6.1.2版本使用新版保险购买流程
            if ((!ChannelCodeEnum.MOBILE.getChannelCode().equals(headChannelCode) || ver >= 61200) && "Y".equalsIgnoreCase(handConfig.getIsNewProcessInsurance())) {
                isNewProcessInsurance = bookReq.getPassengerInfoList().stream().anyMatch(passengerInfo -> "Y".equalsIgnoreCase(passengerInfo.getIsBuyInsurance())) ? "Y" : "N";
            }
            Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
            //下单接口
            if (useFareV30) {
                PtTicketBookingReq bookRequest = AVObjectConvertV2.toPlatformReq(bookReq, getChannelInfo(bookReq.getChannelCode(), "10"), headChannelCode);
                bookRequest.setDesignatedClassFlag(isFreeTicketOrder ? "Y" : "N");
                bookRequest.setIsNewProcessInsurance(isNewProcessInsurance);
                //MWEB渠道的航班查询切换
                if (ChannelCodeEnum.MWEB.getChannelCode().equals(headChannelCode)) {
                    bookRequest.setChannelCode(handConfig.getMwebOrderChannel());
                    bookRequest.setUserNo(getChannelInfo(handConfig.getMwebOrderChannel(), "10"));
                }
                String url = OrderGateWayUrlUtil.useGateWay(handConfig.getUseOpenApiAddress(), HandlerConstants.RESERVE_BOOK_MULTIPLE) + HandlerConstants.RESERVE_BOOK_MULTIPLE;
                HttpResult serviceResult = doPostClient(bookRequest, url, headMap, handConfig.getReadTimeout(), handConfig.getConnectTimeout());
                resp = AVObjectConvertV2.dealBookingResult(serviceResult, resp, reqId);
            } else {
                TicketBookingRequest bookRequest = new TicketBookingRequest();
                BeanUtils.copyNotNullProperties(bookReq, bookRequest);
                AtomicBoolean needRealName = new AtomicBoolean(false);
                PtTicketBookingRequestV20 unitOrderRequest = V2OrderObjectConvert.toTicketOrderBookReq(bookRequest, getChannelInfo(bookReq.getChannelCode(), "10"), resp, needRealName);
                if (needRealName.get() && !realName) {
                    resp.setResultCode(WSEnum.NO_REAL_NAME.getResultCode());
                    resp.setErrorInfo("您还未实名认证，请先实名认证后再预订");
                    return resp;
                }
                if (unitOrderRequest == null) {
                    return resp;
                }
                //MWEB渠道的航班查询切换为微信查询
                if (ChannelCodeEnum.MWEB.getChannelCode().equals(headChannelCode)) {
                    unitOrderRequest.setChannelCode(handConfig.getMwebOrderChannel());
                    unitOrderRequest.setUserNo(getChannelInfo(handConfig.getMwebOrderChannel(), "10"));
                }
                unitOrderRequest.setIsNewProcessInsurance(isNewProcessInsurance);
                String url = OrderGateWayUrlUtil.useGateWay(handConfig.getUseOpenApiAddress(), HandlerConstants.TICKET_BOOK_V20) + HandlerConstants.TICKET_BOOK_V20;
                HttpResult orderResult = doPostClient(unitOrderRequest, url, headMap, handConfig.getReadTimeout(), handConfig.getConnectTimeout());
                resp = AVObjectConvertV2.dealBookingResult(orderResult, resp, reqId);
                if (WSEnum.SUCCESS.getResultCode().equals(resp.getResultCode())) {
                    this.saveCreateOrderCache(resp.getOrderNO());
                }
                return resp;
            }
        } catch (ServiceException e) {
            log.error("预订机票出现异常！请求参数{}", bookReq, e);
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(e.getMessage());
        } catch (ExpectableException e) {
            log.error("预订机票出现异常！请求参数{}", bookReq, e);
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo(e.getMessage());
        } catch (CommonException ce) {
            resp.setResultCode(ce.getResultCode());
            resp.setErrorInfo(ce.getErrorMsg());
        } catch (Exception e) {
            log.error("预订机票出现异常！请求参数{}", bookReq, e);
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo(WSEnum.ERROR.getResultInfo());
        }
        return resp;
    }

    /**
     * 标记本人旅客
     *
     * @param ptCRMResponse
     */
    private void markOwnPassenger(List<PassengerInfo> passengerInfoList, PtCRMResponse<PtMemberDetail> ptCRMResponse, String flightFareType, HttpServletRequest request, String channelCode, String ffpId) {
        List<MemberCertificateResModel> certList = getMemberCertificateResModels(request, channelCode, ffpId);
        passengerInfoList.forEach(passengerInfo -> {
            if (null != ptCRMResponse.getData() && CollectionUtils.isNotEmpty(certList)) {
                for (MemberCertificateResModel certificateSoaModelV2 : certList) {
                    if (StringUtils.isNotBlank(certificateSoaModelV2.getCertificateNumber())) {
                        CertificateTypeEnum certificateType = CertificateTypeEnum.checkName(certificateSoaModelV2.getCertificateType());
                        CertificateTypeEnum passengerCertificateType = CertificateTypeEnum.checkShowCode(passengerInfo.getCertType());
                        if (null != certificateType && null != passengerCertificateType && certificateType.getShowCode().equals(passengerCertificateType.getShowCode())
                                && certificateSoaModelV2.getCertificateNumber().equalsIgnoreCase(passengerInfo.getCertNo())) {
                            MemberBasicInfoSoaModel memberInfo = ptCRMResponse.getData().getBasicInfo();
                            // 身份证匹配姓名
                            if ((CertificateTypeEnum.ID_CARD.equals(certificateType)
                                    || CertificateTypeEnum.HMT_ID_CARD.equals(certificateType)
                                    || CertificateTypeEnum.TW_ID_CARD.equals(certificateType)
                                    || CertificateTypeEnum.OFFICER_CARD.equals(certificateType)
                                    || CertificateTypeEnum.AP.equals(certificateType))
                                    && (memberInfo.getCLastName() + memberInfo.getCFirstName()).equals(passengerInfo.getPassengerName())) {
                                passengerInfo.setOwner(true);
                                break;
                            } else if (!CertificateTypeEnum.ID_CARD.equals(certificateType)
                                    && !CertificateTypeEnum.HMT_ID_CARD.equals(certificateType)
                                    && !CertificateTypeEnum.TW_ID_CARD.equals(certificateType)
                                    && !CertificateTypeEnum.OFFICER_CARD.equals(certificateType)
                                    && !CertificateTypeEnum.AP.equals(certificateType)
                                    && (memberInfo.getELastName() + "/" + memberInfo.getEFirstName()).equals(passengerInfo.getPassengerName())) {
                                if (FlightQueryTypeEnum.FREE_TICKET.getType().equals(flightFareType) || FlightQueryTypeEnum.BUSINESS_FLY_FREE_TICKET.getType().equals(flightFareType)) {
                                    String birthDay = DateUtils.timeStampToDateStr(memberInfo.getBirthDay());
                                    if (passengerInfo.getBirthdate().equals(birthDay)) {
                                        passengerInfo.setOwner(true);
                                    }
                                } else {
                                    passengerInfo.setOwner(true);
                                }
                                // 其他证件匹配英文姓名

                                break;
                            }
                        }
                    }
                }
            }
        });
    }

    private List<MemberCertificateResModel> getMemberCertificateResModels(HttpServletRequest request, String channelCode, String ffpId) {
        CrmMemberBaseApiRequest<QueryMemberCertificateReqDto> queryCertRequest = buildCommCrmMemberReq(request, channelCode);
        QueryMemberCertificateReqDto queryCertDto = new QueryMemberCertificateReqDto();
        queryCertDto.setMemberId(Integer.parseInt(ffpId));
        queryCertRequest.setData(queryCertDto);
        CrmMemberBaseApiResponse<QueryMemberCertificateResDto> queryCertResponse = iMemberCrmMemberService.queryMemberCertificate(queryCertRequest);
        if (queryCertResponse.getCode() == 0) {
            return queryCertResponse.getData().getCertificateList();
        }
        return Collections.emptyList();
    }

    /**
     * 创建订单缓存
     */
    private void saveCreateOrderCache(String orderNo) {
        this.apiRedisService.putData(RedisKeyConfig.CREATE_TICKET_ORDER_CACHE + orderNo, orderNo, 2 * 3600L);
    }

    /**
     * 多程航班下单
     * 不支持军残警残、免票等特殊票购买
     * 不支持青年特惠、会员专享等特殊运价票购买
     * 不支持积分优惠券，不可购买保险wifi等增值服务
     * 可购买邮递行程单
     *
     * @param req
     * @param request
     * @return
     */
    @RequestMapping(value = "multipleFlightOrderBook", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @ApiOperation(value = "多程航班下单", notes = "多程航班下单")
    @InterfaceLog
    public TicketBookingResp multipleFlightOrderBook(@RequestBody BaseReq<MultipleFlightBookRequest> req, HttpServletRequest request) {
        TicketBookingResp resp = new TicketBookingResp();
        String ip = this.getClientIP(request);
        String reqId = StringUtil.newGUID() + request.getRequestURI();
        try {
            //校验参数
            this.checkRequest(req);
            boolean webClient = false;//微信或M站
            MultipleFlightBookRequest bookReq = req.getRequest();
            //老M站
            if (ChannelCodeEnum.MWEB.getChannelCode().equals(req.getChannelCode()) || ChannelCodeEnum.WEIXIN.getChannelCode().equals(req.getChannelCode())) {
                if (!this.checkToken(bookReq.getWebChannelCodeToken(), bookReq.getWebChannelCode(), "")) {
                    resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                    resp.setErrorInfo("生成订单异常!" + bookReq.getWebChannelCode());
                    return resp;
                }
                webClient = true;
            }
            //新版M站
            String platforminfo = request.getHeader(HandlerConstants.CLIENT_PLATFORM);
            String blackBoxFrom = request.getHeader(HandlerConstants.BLACKBOX_FROM);
            bookReq.setAppsys(platforminfo);
            String realChannelCode = request.getHeader(HandlerConstants.TOKEN_CHANNELCODE);
            //纯卡号无HO
            String memberNo = StringUtil.subStrFromStart(bookReq.getSfCardNo(), AirCompanyEnum.HO.getAirCompanyCode());
            //卡号补全HO
            String HOmemberNo = StringUtil.addPrefix(bookReq.getSfCardNo(), AirCompanyEnum.HO.getAirCompanyCode());
            bookReq.setSfCardNo(HOmemberNo);
            if (ChannelCodeEnum.MWEB.getChannelCode().equals(realChannelCode) ||
                    ChannelCodeEnum.WEIXIN.getChannelCode().equals(realChannelCode) || ChannelCodeEnum.WXAPP.getChannelCode().equals(realChannelCode)) {
                webClient = true;
            }
            //20190417判断app渠道订单，如果没有版本号，返回下单失败，解决占座问题
            if (!webClient) {
                int version = VersionNoUtil.toVerInt(req.getClientVersion());
                if (version < 6000000) {
                    String reqJson = JsonMapper.buildNormalMapper().toJson(bookReq);
                    log.error("App占座生成订单失败请求号:{},IP地址:{},客户端提交参数:{}", reqId, ip, reqJson);
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setErrorInfo("生成订单失败(" + version + ")");
                    return resp;
                }
            }
            if (!webClient) {
                //是否进行设备验证
                if (HandlerConstants.IS_CHECK_DEVICEID.equals("Y")) {
                    //验证设备信息，不存在的设备为非法设备
                    boolean checkDevice = this.checkDevice(bookReq.getDeviceId());
                    if (!checkDevice) {
                        log.error("请求号:{},此设备不合法,请求的设备号:{},ip地址:{},卡号:{}", reqId, bookReq.getDeviceId(), ip, memberNo);
                        resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                        resp.setErrorInfo("生成订单失败!");
                        return resp;
                    }
                }
            }
            //下单参数检验
            checkTicketRule(bookReq);
            //查询会员信息
            String memberId = bookReq.getFfpId();
            String[] items = {
                    MemberDetailRequestItemsEnum.STATEINFO.eName,
                    MemberDetailRequestItemsEnum.BASICINFO.eName,
                    MemberDetailRequestItemsEnum.CONTACTINFO.eName,
                    MemberDetailRequestItemsEnum.CERTIFICATEINFO.eName};
            PtApiCRMRequest<PtMemberDetailRequest> ptApiRequest = CRMReqUtil.buildMemberDetailReq(memberNo, memberId, request, ChannelCodeEnum.MOBILE.getChannelCode(), items);
            PtCRMResponse<PtMemberDetail> ptCRMResponse = memberService.memberDetail(ptApiRequest);
            if (ptCRMResponse.getCode() != 0) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo(ptCRMResponse.getMsg());
                return resp;
            }
            //同盾校验  2021-02-07 金卡及以上会员不调用同盾风控
            int levelCode = NumberUtils.toInt(ptCRMResponse.getData().getStateInfo().getMemberLevelCode());
            //5.3版本使用同盾
            if ("Y".equals(handConfig.getUseTongDun()) && levelCode < NumberUtils.toInt(MemberLevelEnum.Golden.getLevelCode())) {
                if (StringUtil.isNullOrEmpty(bookReq.getAppsys())) {
                    bookReq.setAppsys("");
                }
                String buyMobile = "";
                String buyEmail = "";
                MemberBasicInfoSoaModel basicInfo = ptCRMResponse.getData().getBasicInfo();
                String buyName = CRMReqUtil.getMemberName(basicInfo);
                List<MemberContactSoaModel> contactSoaModelList = ptCRMResponse.getData().getContactInfo();
                //联系方式
                if (!StringUtil.isNullOrEmpty(contactSoaModelList)) {
                    for (MemberContactSoaModel con : contactSoaModelList) {
                        //手机号
                        if (con.getContactType() == ContactTypeEnum.MOBILE.getCode()) {
                            buyMobile = con.getContactNumber();
                        }
                        //邮箱
                        if (con.getContactType() == ContactTypeEnum.EMAIL.getCode()) {
                            buyEmail = con.getContactNumber();
                        }
                    }
                }
                FraudApiResponse fraudApiResponse = FraudApiInvoker
                        .multipleFlightOrderRiskControl(bookReq, blackBoxFrom, req.getClientVersion(), realChannelCode, ip, buyName, buyMobile, buyEmail);
                if (fraudApiResponse.getSuccess() && fraudApiResponse.getFinal_decision().equalsIgnoreCase(FinalDecisionEnum.REJECT.getCode())) {
                    resp.setResultCode(WSEnum.TONGDUN_FAIL_TRADE.getResultCode());
                    resp.setErrorInfo(WSEnum.TONGDUN_FAIL_TRADE.getResultInfo());
                    return resp;
                }
            } else {
                log.debug("请求未进行同盾验证，渠道订单号：{}，会员号：{}", bookReq.getChannelOrderNo(), bookReq.getFfpCardNo());
            }
            bookReq.setOrderRequestIp(ip);
            if (!StringUtil.isNullOrEmpty(req.getClientVersion())) {
                if (StringUtil.isNullOrEmpty(bookReq.getChannelPrivateInfo())) {
                    bookReq.setChannelPrivateInfo(req.getClientVersion());
                } else {
                    bookReq.setChannelPrivateInfo(bookReq.getChannelPrivateInfo() + ";" + req.getClientVersion());
                }
            }
            PtTicketBookingReq ptTicketBookingReq = MultipleFlightBookOrderConvert
                    .toMultipleFlightOrderBookReq(bookReq, req.getChannelCode(), this.getChannelInfo(req.getChannelCode(), "10"));
            //MWEB渠道的航班查询切换为微信查询
            if (ChannelCodeEnum.MWEB.getChannelCode().equals(realChannelCode)) {
                ptTicketBookingReq.setChannelCode(ChannelCodeEnum.WEIXIN.getChannelCode());
                ptTicketBookingReq.setUserNo(getChannelInfo(ChannelCodeEnum.WEIXIN.getChannelCode(), "10"));
            }
            HttpResult serviceResult = doPostClient(ptTicketBookingReq, HandlerConstants.URL_FARE_API + HandlerConstants.RESERVE_BOOK_MULTIPLE);
            resp = AVObjectConvertV2.dealBookingResult(serviceResult, resp, reqId);
        } catch (ExpectableException e) {
            log.error("多程下单出现异常，请求号{}，IP {}， 请求参数{}", reqId, ip, req, e);
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(e.getMessage());
        } catch (Exception e) {
            log.error("多程下单出现异常，请求号{}，IP {}， 请求参数{}", reqId, ip, req, e);
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo("糟糕，发生意外了");
        }
        return resp;
    }

    /**
     * 判断是否是免票兑换航线,更新 包含奖励飞判断
     *
     * @return
     */
    private boolean isFreeTicketOrder(TicketBookingV3Request bookingV3Request) {
        List<FlightInfo> flightInfoList = bookingV3Request.getFlightInfoList();
        if (null != bookingV3Request.getFlightInfoComb() && CollectionUtils.isNotEmpty(bookingV3Request.getFlightInfoComb().getAdtCabinFareList())) {
            //
            boolean isAllHOAirLine = bookingV3Request.getFlightInfoComb().getCombFlightInfoList().stream().allMatch(flightInfo -> flightInfo.getCarrierNo().startsWith("HO"));
            //
            for (com.juneyaoair.baseclass.response.av.CabinFare cabinFare : bookingV3Request.getFlightInfoComb().getAdtCabinFareList()) {
                // 包含免票舱位即为免票订单
                if ((StringUtils.isNotBlank(cabinFare.getCabinCode()) && cabinFare.getCabinCode().contains(handConfig.getFreeTicketCabin()))
                        || (StringUtils.isNotBlank(cabinFare.getCabinComb()) && cabinFare.getCabinComb().contains(handConfig.getFreeTicketCabin()))) {
                    return true;
                }
                // 判断是否为奖励飞免票:
                //  1.为吉祥航班
                //  2.为吉祥奖励飞免票舱位 I,N
                boolean isAwardFly = Arrays.stream(handConfig.getAwardFlyFreeTicketCabin().split(","))
                        .anyMatch(str -> (StringUtils.isNotBlank(cabinFare.getCabinCode()) && cabinFare.getCabinCode().contains(str))
                                || (StringUtils.isNotBlank(cabinFare.getCabinComb()) && cabinFare.getCabinComb().contains(str)));
                if (isAwardFly && isAllHOAirLine) {
                    return true;
                }
            }
        } else if (CollectionUtils.isNotEmpty(flightInfoList)) {
            for (FlightInfo flightInfo : flightInfoList) {
                boolean isAllHOAirLine = flightInfoList.stream().allMatch(flight -> flight.getCarrierNo().startsWith("HO"));
                if (CollectionUtils.isNotEmpty(flightInfo.getCabinFareList())) {
                    for (CabinFare cabinFare : flightInfo.getCabinFareList()) {
                        // 包含免票舱位即为免票订单
                        if (((StringUtils.isNotBlank(cabinFare.getCabinCode()) && cabinFare.getCabinCode().contains(handConfig.getFreeTicketCabin()))
                                || (StringUtils.isNotBlank(cabinFare.getCabinComb()) && cabinFare.getCabinComb().contains(handConfig.getFreeTicketCabin())))) {
                            if (!PackageTypeEnum.THEME_CARD.getPackType().equals(cabinFare.getCabinType())) {
                                return true;
                            }
                        }
                        // 判断是否包含 奖励飞免票
                        boolean isAwardFly = Arrays.stream(handConfig.getAwardFlyFreeTicketCabin().split(","))
                                .anyMatch(str -> (StringUtils.isNotBlank(cabinFare.getCabinCode()) && cabinFare.getCabinCode().contains(str))
                                        || (StringUtils.isNotBlank(cabinFare.getCabinComb()) && cabinFare.getCabinComb().contains(str)));
                        if (isAwardFly && isAllHOAirLine) {
                            return true;
                        }
                    }
                }
            }
        }
        return false;
    }

    /**
     * 判断是否是畅飞卡订单
     *
     * @param bookingV3Request 下单请求参数
     * @return 是否是畅飞卡订单
     */
    private boolean isUnlimitedCard(TicketBookingV3Request bookingV3Request) {
        if (HandlerConstants.TRIP_TYPE_I.equals(bookingV3Request.getInterFlag())) {
            return false;
        }
        for (FlightInfo flightInfo : bookingV3Request.getFlightInfoList()) {
            if (CollectionUtils.isNotEmpty(flightInfo.getCabinFareList())) {
                for (CabinFare cabinFare : flightInfo.getCabinFareList()) {
                    if (PackageTypeEnum.UNLIMITED_FARE_V2.getPackType().equals(cabinFare.getCabinType())) {
                        return true;
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(flightInfo.getCabinCHDINFFareList())) {
                for (CabinFare cabinFare : flightInfo.getCabinCHDINFFareList()) {
                    if (PackageTypeEnum.UNLIMITED_FARE_V2.getPackType().equals(cabinFare.getCabinType())) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    /**
     * 使用积分优惠券券的判断条件
     *
     * @param bookReq
     */
    private boolean checkCouponScore(ClientInfo clientInfo, TicketBookingV3Request bookReq, String realChannelCode, String versionCode, PtCRMResponse<PtMemberDetail> ptCRMResponse, TicketBookingResp resp, String memberNo) {
        if ((bookReq.getUseScoreTotal() != null && bookReq.getUseScoreTotal() > 0)
                || StringUtils.isNotBlank(bookReq.getCouponCode())) {
            //判断实名认证状态
            boolean accmflag = CrmUtil.judgeRealNameStatus(ptCRMResponse.getData().getRealVerifyInfos());
            //优惠券使用检验条件
            if (StringUtils.isNotBlank(bookReq.getCouponCode())) {
                if ("Y".equals(handConfig.getCheckRealName())) {
                    if (!accmflag) {
                        resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                        resp.setErrorInfo("您是未实名用户，请先完成实名认证。");
                        return false;
                    }
                }
                boolean checkFlag = couponService.checkCouponLimit(memberNo, ptCRMResponse, "Order");
                if (!checkFlag) {
                    resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                    resp.setErrorInfo("该优惠券不符合使用条件");
                    return false;
                }
            }
            //积分使用检验
            if (bookReq.getUseScoreTotal() != null && bookReq.getUseScoreTotal() > 0) {
                //小额免密状态
                String smallExemptOpen = ptCRMResponse.getData().getStateInfo().getIsSmallExemptPwd();
                boolean scoreFreeLimit = "Y".equals(smallExemptOpen) ? true : false;
                BaseResp checkResp = orderService.checkFreeScoreLimit(bookReq.getFfpId(), bookReq.getChannelCode(), getChannelInfo(bookReq.getChannelCode(), "40"), bookReq.getUseScoreTotal(), bookReq.getUseScorePassCheck(), accmflag, scoreFreeLimit);
                if (!WSEnum.SUCCESS.getResultCode().equals(checkResp.getResultCode())) {
                    resp.setResultCode(checkResp.getResultCode());
                    resp.setErrorInfo(checkResp.getResultInfo());
                    return false;
                }
                //预定机票接口 增加积分使用风控
                if (orderService.checkScoreUseRule(clientInfo, bookReq.getChannelCode())) {
                    resp.setResultCode(WSEnum.NO_REAL_NAME.getResultCode());
                    resp.setErrorInfo("非常抱歉，当前账户存在安全风险，通过实名认证后才可使用积分，请前往实名认证");
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * 下单参数检验
     *
     * @param bookReq
     */
    private void checkTicketRule(TicketBookingV3Request bookReq, boolean useFareV30, HttpServletRequest request, String headChannelCode) throws ServiceException {
        boolean isGMJC = false;//是否包含军残警残证
        List<PassengerInfo> adtJcList = new ArrayList<>();
        List<PassengerInfo> intList = new ArrayList<>();
        boolean isNotAdtCabin = false;
        boolean isNotChdCabin = false;
        boolean isNotInfCabin = false;
        List<String> passengerTypes = bookReq.getPassengerInfoList().stream().map(PassengerInfo::getPassengerType).collect(Collectors.toList());
        if (useFareV30) {
            if (CollectionUtils.isNotEmpty(passengerTypes)) {
                if (bookReq.getFlightInfoComb() != null) {
                    if (passengerTypes.contains(HandlerConstants.PASSENGER_TYPE_ADT)) {
                        if (CollectionUtils.isNotEmpty(bookReq.getFlightInfoComb().getAdtCabinFareList())) {
                            isNotAdtCabin = bookReq.getFlightInfoComb().getAdtCabinFareList().stream().allMatch(cabinFare -> "0".equals(cabinFare.getCabinNumber())
                                    || StringUtils.isEmpty(cabinFare.getCabinNumber()));
                        }

                    }
                    if (passengerTypes.contains(HandlerConstants.PASSENGER_TYPE_CHD)) {
                        if (CollectionUtils.isNotEmpty(bookReq.getFlightInfoComb().getChdCabinFareList())) {
                            isNotChdCabin = bookReq.getFlightInfoComb().getChdCabinFareList().stream().allMatch(cabinFare -> "0".equals(cabinFare.getCabinNumber())
                                    || StringUtils.isEmpty(cabinFare.getCabinNumber()));
                        }

                    }
                    if (passengerTypes.contains(HandlerConstants.PASSENGER_TYPE_INF)) {
                        if (CollectionUtils.isNotEmpty(bookReq.getFlightInfoComb().getInfCabinFareList())) {
                            isNotInfCabin = bookReq.getFlightInfoComb().getInfCabinFareList().stream().allMatch(cabinFare -> "0".equals(cabinFare.getCabinNumber())
                                    || StringUtils.isEmpty(cabinFare.getCabinNumber()));
                        }
                    }
                }
            } else {
                if (CollectionUtils.isNotEmpty(passengerTypes)) {
                    if (passengerTypes.contains(HandlerConstants.PASSENGER_TYPE_ADT)) {
                        if (CollectionUtils.isNotEmpty(bookReq.getFlightInfoList())) {
                            isNotAdtCabin = bookReq.getFlightInfoList().stream().allMatch(flightInfo -> flightInfo.getCabinFareList()
                                    .stream().filter(cabinFare -> HandlerConstants.PASSENGER_TYPE_ADT.equals(cabinFare.getPassengerType()))
                                    .allMatch(cabinFare -> "0".equals(cabinFare.getCabinNumber()) || StringUtils.isEmpty(cabinFare.getCabinNumber())));
                        }
                    }
                }
                if (passengerTypes.contains(HandlerConstants.PASSENGER_TYPE_CHD)) {
                    if (CollectionUtils.isNotEmpty(bookReq.getFlightInfoList())) {
                        isNotChdCabin = bookReq.getFlightInfoList().stream().allMatch(flightInfo -> flightInfo.getCabinCHDINFFareList().stream()
                                .filter(cabinFare -> HandlerConstants.PASSENGER_TYPE_CHD.equals(cabinFare.getPassengerType()))
                                .allMatch(cabinFare -> "0".equals(cabinFare.getCabinNumber()) || StringUtils.isEmpty(cabinFare.getCabinNumber())));
                    }
                }
                if (passengerTypes.contains(HandlerConstants.PASSENGER_TYPE_INF)) {
                    if (CollectionUtils.isNotEmpty(bookReq.getFlightInfoList())) {
                        isNotInfCabin = bookReq.getFlightInfoList().stream().allMatch(flightInfo -> flightInfo.getCabinCHDINFFareList().stream()
                                .filter(cabinFare -> HandlerConstants.PASSENGER_TYPE_INF.equals(cabinFare.getPassengerType()))
                                .allMatch(cabinFare -> "0".equals(cabinFare.getCabinNumber()) || StringUtils.isEmpty(cabinFare.getCabinNumber())));
                    }
                }
            }
        }
        if (isNotAdtCabin || isNotChdCabin || isNotInfCabin) {
            throw new ServiceException("对不起，该舱位已经被其他旅客预订完，请重新查询信息!");
        }
        //军残警残身份证验证
        for (PassengerInfo passenger : bookReq.getPassengerInfoList()) {
            if ("JC".equals(passenger.getCertType()) || "GM".equals(passenger.getCertType())) {
                passenger.setGjCertType("NI");
                passenger.setPassengerType("GMJC");//传给统一订单的乘客类型
                isGMJC = true;
                String patternStr = PatternCommon.ID_NUMBER;//正则表达式
                Pattern pattern = Pattern.compile(patternStr);
                Matcher matcher = pattern.matcher(passenger.getGjCertNo());
                if (!matcher.matches()) {
                    throw new ServiceException("身份证号不符合规范!");
                }
                adtJcList.add(passenger);
            } else if (CertificateTypeEnum.ID_CARD.getShowCode().equals(passenger.getCertType()) && passenger.isOwner()) {
                //会员本人身份证验证姓名包含中文
                Pattern p = Pattern.compile("[\u4e00-\u9fa5]");
                Matcher m = p.matcher(passenger.getPassengerName());
                if (!m.find()) {
                    throw new ServiceException("您的姓名信息读取失败，为保证您的正常出行，请尽快联系吉祥航空客服热线95520修改姓名信息。");
                }
            } else if (CertificateTypeEnum.HMT_ID_CARD.getShowCode().equals(passenger.getCertType())) {
                //港澳台居民居住证验证姓名包含中文
                Pattern p = Pattern.compile("[\u4e00-\u9fa5]");
                Matcher m = p.matcher(passenger.getPassengerName());
                if (!m.find()) {
                    throw new ServiceException("港澳居民居住证类型乘机人姓名必须是中文姓名，请修改乘机人信息或者升级新版app进行修改");
                }
            } else if (CertificateTypeEnum.BC.getShowCode().equals(passenger.getCertType())) {
                String depDate = getDepDateString(bookReq, useFareV30);
                int bcAge = DateUtils.getAgeByBirthIncludeBirthDay(
                        passenger.getBirthdate(), depDate, DateUtils.YYYY_MM_DD_PATTERN);
                if (bcAge >= 16) {
                    throw new ServiceException("出生医学证明仅支持未年满16周岁的旅客乘机使用，已超出年龄请更换其他证件");
                }

            } else if (CertificateTypeEnum.HK_MACAO_PASS.getShowCode().equals(passenger.getCertType())) {
                if (useFareV30) {
                    boolean busFlight = bookReq.getFlightInfoComb().getCombFlightInfoList().stream().anyMatch(flightInfo -> FareBasisEnum.BUS_FARE.getFareBasisCode().equals(flightInfo.getFType()));
                    if (busFlight) {
                        throw new ServiceException("深港通产品，不支持港澳通行证下单");
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(bookReq.getFlightInfoComb().getAdtCabinFareList())) {
                // 运价基础
                String fareBasis = bookReq.getFlightInfoComb().getAdtCabinFareList().get(0).getFareBasis();
                if (fareBasis.endsWith(FareBasisEnum.YJYS_FARE.getFareBasisCode())) {
                    MemberTagResponse memberTagResponse = iMemberTagService.queryMemberTag(request, headChannelCode, bookReq.getFfpId(), "YDZ", false);
                    if (!memberTagResponse.getIsEffective()) {
                        throw new ServiceException("请先进行优待证认证，认证路径：我的-个人主页-优待证认证");
                    }
                    passenger.setPassengerIdentity("YJ");
                }

            }
            //留学生运价 调整附件路径
            if (SpecialFareTypeEnum.STU.getCode().equals(passenger.getPassengerIdentity()) && CollectionUtils.isNotEmpty(passenger.getPassengerIdentityUrl())) {
                if (FileUtils.useMinio(handConfig.getUseMinioType(), PicTypeEnum.STUDENT.getCode())) {
                    String host = handConfig.getEndpoint() + "/" + handConfig.getBucket();
                    List<String> newList = new ArrayList<>();
                    for (String url : passenger.getPassengerIdentityUrl()) {
                        newList.add(host + url);
                    }
                    passenger.setPassengerIdentityUrl(newList);
                }
            }


            //调整乘机人乘客类型
            if (useFareV30) {
                int size = bookReq.getFlightInfoComb().getCombFlightInfoList().size();
                passenger.setPassengerType(OrderObjectConvert.adjustPassType(passenger.getBirthdate(), bookReq.getFlightInfoComb().getCombFlightInfoList().get(size - 1).getFlightDate()));
                if (HandlerConstants.PASSENGER_TYPE_INF.equals(passenger.getPassengerType())) {
                    intList.add(passenger);
                }
            } else {
                int size = bookReq.getFlightInfoList().size();
                passenger.setPassengerType(OrderObjectConvert.adjustPassType(passenger.getBirthdate(), bookReq.getFlightInfoList().get(size - 1).getFlightDate()));
            }
            //主题卡参数
            if ("Theme".equals(passenger.getVoucherType())) {
                if (StringUtils.isBlank(passenger.getVoucherNo())) {
                    throw new ServiceException("仅吉祥多次套卡兑换");
                } else {
                    passenger.setVoucherResource(passenger.getCouponType());
                }

            }
        }
        //军残警残舱位检验
        String depDateG = "";//去程航班日期
        String depDateB = "";//返程航班日期
        int flightNum = 0;
        if (useFareV30) {
            for (com.juneyaoair.baseclass.response.av.FlightInfo flightInfo : bookReq.getFlightInfoComb().getCombFlightInfoList()) {
                flightNum++;
                if ("G".equals(flightInfo.getFlightDirection())) {//去程
                    depDateG = flightInfo.getFlightDate();
                } else if ("B".equals(flightInfo.getFlightDirection())) {//返程
                    depDateB = flightInfo.getFlightDate();
                }
                if (StringUtil.isNullOrEmpty(flightInfo.getCabinGMJCFareList()) && isGMJC) {
                    throw new ServiceException("该航班暂不支持军残警残票!");
                }
            }
        } else {
            for (FlightInfo flightInfo : bookReq.getFlightInfoList()) {
                flightNum++;
                if ("G".equals(flightInfo.getFlightDirection())) {//去程
                    depDateG = flightInfo.getFlightDate();
                } else if ("B".equals(flightInfo.getFlightDirection())) {//返程
                    depDateB = flightInfo.getFlightDate();
                }
                if (StringUtil.isNullOrEmpty(flightInfo.getCabinGMJCFareList()) && isGMJC) {
                    throw new ServiceException("该航班暂不支持军残警残票!");
                }
            }
        }
        //2018-06-07 15:24:30 检验军残警残不可携带婴儿
        if (isGMJC && !StringUtil.isNullOrEmpty(intList)) {
            for (PassengerInfo inf : intList) {
                String adtName = inf.getAdtNameToInf();//婴儿绑定的成人姓名
                String handPhone = inf.getHandphoneNo();//婴儿绑定的成人手机号
                for (PassengerInfo adtJc : adtJcList) {
                    if (adtName.equals(adtJc.getPassengerName()) && handPhone.equals(adtJc.getHandphoneNo())) {
                        throw new ServiceException("军警残等特殊旅客无法携带婴儿，请重新选择乘机人!");
                    }
                }
            }
        }
        //重复证件信息检验
        repeatCertCheck(bookReq.getPassengerInfoList());
        //校验wifi取件日期是否与航班日期一致
        checkWifiDate(bookReq, depDateG, depDateB, flightNum);
    }

    private String getDepDateString(TicketBookingV3Request bookReq, boolean useFareV30) {
        String depDate = "";//去程航班日期
        if (useFareV30) {
            for (com.juneyaoair.baseclass.response.av.FlightInfo flightInfo : bookReq.getFlightInfoComb().getCombFlightInfoList()) {
                if ("G".equals(flightInfo.getFlightDirection())) {//去程
                    depDate = flightInfo.getFlightDate();
                }
            }
        } else {
            for (FlightInfo flightInfo : bookReq.getFlightInfoList()) {
                if ("G".equals(flightInfo.getFlightDirection())) {//去程
                    depDate = flightInfo.getFlightDate();
                }
            }
        }
        return depDate;
    }

    /**
     * 多程航班购买校验
     *
     * @param bookReq
     * @throws ServiceException
     */
    private void checkTicketRule(MultipleFlightBookRequest bookReq) throws ServiceException {
        List<PassengerInfo> intList = new ArrayList<>();
        //军残警残身份证验证
        for (PassengerInfo passenger : bookReq.getPassengerInfoList()) {
            //调整乘机人乘客类型
            int size = bookReq.getFlightInfoCombList().get(bookReq.getFlightInfoCombList().size() - 1).getCombFlightInfoList().size();
            passenger.setPassengerType(OrderObjectConvert.adjustPassType(passenger.getBirthdate(),
                    bookReq.getFlightInfoCombList().get(bookReq.getFlightInfoCombList().size() - 1).getCombFlightInfoList().get(size - 1).getFlightDate()));
            if (HandlerConstants.PASSENGER_TYPE_INF.equals(passenger.getPassengerType())) {
                intList.add(passenger);
            }
        }
        //重复证件信息检验
        repeatCertCheck(bookReq.getPassengerInfoList());
    }

    /**
     * 检验wifi日期
     *
     * @param bookReq
     */
    private void checkWifiDate(TicketBookingV3Request bookReq, String depDateG, String depDateB, int flightNum) throws ServiceException {
        if (bookReq.getBuyWifi() && !StringUtil.isNullOrEmpty(bookReq.getWifiQueryList())) {
            WifiQuery wifiQuery = bookReq.getWifiQueryList().get(0);
            String takeDate = wifiQuery.getTakeDate();//取件日期
            String returnDate = wifiQuery.getReturnDate();//还件日期
            try {
                takeDate = takeDate.substring(0, 10);
                returnDate = returnDate.substring(0, 10);
            } catch (Exception e) {
                log.error("字符串截取出错:{}", e.getMessage());
                throw new ServiceException("WIFI取还件日期异常!");
            }
            if (!depDateG.equals(takeDate)) {//校验出发日期和取件日期
                log.info("日期不一致订单中航班日期:{},WIFI取件日期:{}", depDateG, takeDate);
                throw new ServiceException("航班日期与WIFI取件日期不一致!");
            }
            if (flightNum == 2 && !depDateB.equals(returnDate)) {//如果是往返程还需校验还件日期
                //校验返程出发日期和还件日期
                log.info("日期不一致订单中航班日期:{},与WIFI还件日期:{}", depDateB, returnDate);
                throw new ServiceException("航班日期与WIFI还件日期不一致!");
            }
        }
    }

    /**
     * 乘机人重复证件检验
     *
     * @param passengerInfoList
     */
    private void repeatCertCheck(List<PassengerInfo> passengerInfoList) throws ServiceException {
        Map<String, List<PassengerInfo>> passMap = OrderObjectConvert.passGroupByCertNo(passengerInfoList);
        if (passMap != null && !passMap.isEmpty()) {
            for (Map.Entry<String, List<PassengerInfo>> entry : passMap.entrySet()) {
                if (!StringUtil.isNullOrEmpty(entry.getKey())
                        && !StringUtil.isNullOrEmpty(entry.getValue())
                        && !CertificateTypeEnum.BC.getShowCode().equals(entry.getValue().get(0).getCertType())
                        && entry.getValue().size() > 1) {
                    throw new ServiceException(entry.getValue().stream().map(passengerInfo -> passengerInfo.getPassengerName()).collect(Collectors.joining(",")) + "证件号重复，请核对修改后再选择");
                }
            }
        }
    }

    @InterfaceLog
    @ApiOperation(value = "查询积分使用规则", notes = "此方法目前微信小程序在使用")
    @RequestMapping(value = "/queryScoreRuleV3", method = RequestMethod.POST)
    public ScoreUseRuleResp queryScoreRuleV3(@RequestBody @Validated QueryScoreRuleV3Request bookReq, BindingResult bindingResult, HttpServletRequest request) {
        ScoreUseRuleResp resp = new ScoreUseRuleResp();
        String ip = this.getClientIP(request);
        resp.setScoreRiskFlag(false);
        if (bindingResult.hasErrors()) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
            return resp;
        }
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(bookReq.getFfpId(), bookReq.getLoginKeyInfo(), bookReq.getChannelCode());
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        if (FareTypeEnum.SPA.getFare().equals(bookReq.getFlightInfoComb().getFareType())) {
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            resp.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
            return resp;
        }
        //查询会员当前级别
        String[] items = {MemberDetailRequestItemsEnum.BASICINFO.eName, MemberDetailRequestItemsEnum.REALVERIFYINFOS.eName,
                MemberDetailRequestItemsEnum.CERTIFICATEINFO.eName};
        PtApiCRMRequest ptApiCRMRequest = CRMReqUtil.buildMemberDetailReq(bookReq.getFfpCardNo(), bookReq.getFfpId(), request, bookReq.getChannelCode(), items);
        PtCRMResponse<PtMemberDetail> ptCRMResponse = memberService.memberDetail(ptApiCRMRequest);
        if (ptCRMResponse.getCode() != 0) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setErrorInfo("未查询到会员信息");
            return resp;
        }
        String cacheData = apiRedisService.getData(RedisKeyConfig.createOrderPass(bookReq.getChannelOrderNo()));
        if (StringUtils.isNotBlank(cacheData)) {
            List<OrderPassengerInfo> orderPassengerInfoList = JsonUtil.fromJson(cacheData, new TypeToken<List<OrderPassengerInfo>>() {
            });
            if (CollectionUtils.isNotEmpty(orderPassengerInfoList)) {
                for (PassengerInfo passengerInfo : bookReq.getPassengerInfoList()) {
                    OrderPassengerInfo orderPassengerInfo = orderPassengerInfoList.stream().filter(param -> passengerInfo.getPassengerID() == param.getPassengerID()).findFirst().orElse(null);
                    if (orderPassengerInfo != null) {
                        passengerInfo.setCertNo(orderPassengerInfo.getCertNo());
                    }
                }
            }
        }
        boolean accmflag = false;
        PtMemberDetail ptMemberDetail = ptCRMResponse.getData();
        List<MemberRealNameSummarySoaModel> memberRealNameSummarySoaModelList = ptMemberDetail.getRealVerifyInfos();
        if (CollectionUtils.isNotEmpty(memberRealNameSummarySoaModelList)) {
            //获取最新的认证记录
            MemberRealNameSummarySoaModel memberRealNameSummarySoaModel = memberRealNameSummarySoaModelList.stream().max(Comparator.comparing(MemberRealNameSummarySoaModel::getVerifyDate)).orElse(null);
            if (memberRealNameSummarySoaModel != null && VerifyStatusEnum.PASS.code.equals(memberRealNameSummarySoaModel.getStatus())) {
                accmflag = true;
            }
        }
        if (!accmflag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setErrorInfo(LOG_RESP_ONE);
            return resp;
        }
        bookReq.setOrderRequestIp(ip);
        //是否使用受益人制度
        if (null != DateUtils.toDate(handConfig.getLimitScoreUseDate()) && DateUtils.toDate(handConfig.getLimitScoreUseDate()).before(new Date())) {
            PtCrmMileageRequest ptCrmMileageRequest = buildCommCrmReq(request, bookReq.getChannelCode());
            ptCrmMileageRequest.setData(new MemberBeneficiaryRequest(Integer.parseInt(bookReq.getFfpId()), Collections.singletonList("A"), null));
            List<PassengerInfo> benefitPassengers = Lists.newArrayList();
            try {
                List<MemberBeneficiaryDTO> beneficiaryDTOList = this.beneficiaryService.listEffectiveBeneficiaryInfoRecord(ptCrmMileageRequest, ip);
                beneficiaryDTOList.forEach(memberBeneficiaryDTO -> {
                    List<PassengerInfo> passengerInfos = bookReq.getPassengerInfoList().stream().filter(passengerInfo -> {
                        // 本人或者受益人可用积分
                        return memberBeneficiaryDTO.getCertificate().stream()
                                .anyMatch(certificate -> certificate.getCtype() == CertificateTypeEnum.checkShowCode(passengerInfo.getCertType()).getCode()
                                        && certificate.getCnumber().equals(passengerInfo.getCertNo()));
                    }).collect(Collectors.toList());
                    benefitPassengers.addAll(passengerInfos);
                });
                benefitPassengers.addAll(bookReq.getPassengerInfoList().stream().filter(passengerInfo -> RightCouponConvert.isOwnPass(ptCRMResponse, passengerInfo)).collect(Collectors.toList()));
            } catch (Exception e) {
                log.error("查询受益人出现异常", e);
            }
            // 只请求受益人
            bookReq.setPassengerInfoList(benefitPassengers.stream().distinct().collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(bookReq.getPassengerInfoList())) {
            //2021-02-22 判断茅台航线不能使用积分
            boolean maotaiCabin = filterMaotaiCabin(handConfig, bookReq.getFlightInfoComb());
            if (!maotaiCabin) {
                ScoreUseRuleRequest scoreUseRuleRequest = AVObjectConvertV2.toScoreUseRuleRequest(bookReq, getChannelInfo(bookReq.getChannelCode(), "10"));
                HttpResult serviceResult = doPostClient(scoreUseRuleRequest, HandlerConstants.URL_FARE_OPEN_API + HandlerConstants.SUB_SCORE_USE_RULE);
                if (null == serviceResult || !serviceResult.isResult() || StringUtils.isBlank(serviceResult.getResponse())) {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setErrorInfo("查询可用积分网络错误");
                    return resp;
                }
                resp = (ScoreUseRuleResp) JsonUtil.jsonToBean(serviceResult.getResponse(), ScoreUseRuleResp.class);
                if (UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(resp.getResultCode())) {
                    resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                    resp.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
                } else if ("该航线不享受使用积分".equals(resp.getErrorInfo())) {
                    resp.setResultCode(WSEnum.CANNOT_USE_SCORE.getResultCode());
                }
            } else {
                resp.setResultCode(WSEnum.CANNOT_USE_SCORE.getResultCode());
                resp.setErrorInfo("茅台专享舱位不支持使用积分抵扣票款");
            }
        } else {
            resp.setResultCode(WSEnum.CANNOT_USE_SCORE.getResultCode());
            resp.setErrorInfo("当前乘机人不支持使用积分");
        }
        String channelCode = bookReq.getChannelCode();
        //返回客户可用积分
        if (StringUtils.isNotBlank(bookReq.getFfpCardNo())) {
            String clientPwd = getClientPwd(channelCode);
            ptApiCRMRequest = CRMReqUtil.buildCommReq(channelCode, clientPwd, bookReq.getFfpId(), "", ip);
            MileageAccountQueryRequest mileageAccountQueryRequest = new MileageAccountQueryRequest();
            items = new String[]{MileageAccountRequestItemsEnum.TOTALBILL.eName};
            mileageAccountQueryRequest.setMemberCardNo(bookReq.getFfpCardNo());
            mileageAccountQueryRequest.setRequestItems(items);
            ptApiCRMRequest.setData(mileageAccountQueryRequest);
            PtCRMResponse<MileageAccountQueryResponse> mileageAccountQueryResponsePtCRMResponse = memberService.mileageAccountQuery(ptApiCRMRequest);
            if (mileageAccountQueryResponsePtCRMResponse.getCode() == 0) {
                resp.setUserScore(mileageAccountQueryResponsePtCRMResponse.getData().getTotalBill().getAvailableMiles());
            }
        } else {
            VerifyConsumeMilesResponseForClient accmClient = crmClient
                    .getAccmInfo(Long.parseLong(bookReq.getFfpId()), channelCode, getChannelInfo(channelCode, "40"));
            if (accmClient.getMessageHeader().getErrorCode().equals("S000")) {
                resp.setUserScore(Integer.parseInt(accmClient.getMemberRedeemInfo().getAvailableMiles()));
            } else {
                resp.setUserScore(0);
            }
        }
        if (CollectionUtils.isNotEmpty(bookReq.getPremiumList())) {
            List<OrderDetailPremium> detailPremiums = bookReq.getPremiumList().stream()
                    .filter(p -> !VoucherTypesEnum.DISNEYTICKET.getCode().equals(p.getProductType())).collect(Collectors.toList());
            Double sum = detailPremiums.stream().mapToDouble(OrderDetailPremium::getAllPremiumPrice).sum();
            int max = resp.getScoreUseMax() + sum.intValue();
            resp.setScoreUseMax(max);
        }

        String headChannelCode = request.getHeader(HandlerConstants.TOKEN_CHANNELCODE);
        String versionCode = request.getHeader(HandlerConstants.CLIENT_VERSION_CODE);
        ClientInfo clientInfo = initClientInfo(request, channelCode, bookReq.getFfpId(), bookReq.getFfpCardNo());
        if ((ChannelCodeEnum.MOBILE.getChannelCode().equalsIgnoreCase(headChannelCode) && VersionNoUtil.toMVerInt(versionCode) >= 61200)
                || ChannelCodeEnum.MWEB.getChannelCode().equalsIgnoreCase(headChannelCode)
                || ChannelCodeEnum.WXAPP.getChannelCode().equalsIgnoreCase(headChannelCode)) {
            //判断账号可用积分
            int usableScore = 0;
            if (resp.getUserScore() >= resp.getScoreUseMin()) {
                if (resp.getUserScore() >= resp.getScoreUseMax()) {
                    usableScore = resp.getScoreUseMax();
                } else {
                    usableScore = resp.getUserScore();
                }
            } else {
                usableScore = resp.getScoreUseMin();
            }
            //积分使用规则增加风控
            if (WSEnum.SUCCESS.getResultCode().equals(resp.getResultCode()) && resp.getUserScore() > 0 && usableScore > 0) {
                if (orderService.checkScoreUseRule(clientInfo, channelCode)) {
                    resp.setScoreRiskFlag(true);
                    resp.setScoreRiskDesc("非常抱歉，当前账户存在安全风险，通过实名认证后才可使用积分，是否前往实名认证？");
                }
            }
        }
        //查询是否设置消费密码
        resp.setMemberPasswordState(ptMemberDetail.getBasicInfo().getIsSetConsumePwd() ? "Y" : "N");
        return resp;
    }

    /**
     * 过滤茅台特殊舱位,茅台仓位不能使用积分和优惠券
     *
     * @param handConfig
     * @return
     */
    private boolean filterMaotaiCabin(HandConfig handConfig, FlightInfoComb flightInfoComb) {
        MaoTaiConfig outMaoTaiConfig = handConfig.getOutMaoTaiConfig();
        MaoTaiConfig inMaoTaiConfig = handConfig.getInMaoTaiConfig();
        if (outMaoTaiConfig != null) {
            String startDateStr = outMaoTaiConfig.getStartDate();
            String endDateStr = outMaoTaiConfig.getEndDate();
            //活动时间比较
            Date date = new Date();
            Date startDate = DateUtils.toDate(startDateStr, DateUtils.YYYY_MM_DD_PATTERN);
            Date endDate = DateUtils.toDate(endDateStr, DateUtils.YYYY_MM_DD_PATTERN);
            if (date.getTime() >= startDate.getTime() && date.getTime() < endDate.getTime()) {
                for (com.juneyaoair.baseclass.response.av.FlightInfo flightInfo : flightInfoComb.getCombFlightInfoList()) {
                    //航班号匹配
                    if (StringUtils.isNotBlank(outMaoTaiConfig.getFlightNo()) && outMaoTaiConfig.getFlightNo().contains(flightInfo.getFlightNo())) {
                        //航班时间
                        String flightStartDateStr = outMaoTaiConfig.getFlightStartDate();
                        String flightEndDateStr = outMaoTaiConfig.getFlightEndDate();
                        Date flightStartDate = DateUtils.toDate(flightStartDateStr, DateUtils.YYYY_MM_DD_PATTERN);
                        Date flightEndDate = DateUtils.toDate(flightEndDateStr, DateUtils.YYYY_MM_DD_PATTERN);
                        Date curFlightDate = DateUtils.toDate(flightInfo.getFlightDate(), DateUtils.YYYY_MM_DD_PATTERN);
                        if (curFlightDate.getTime() >= flightStartDate.getTime() && curFlightDate.getTime() < flightEndDate.getTime()) {
                            //舱位匹配
                            if (StringUtils.isNotBlank(outMaoTaiConfig.getCabins())
                                    && checkCabin(outMaoTaiConfig.getCabins(), flightInfoComb.getAdtCabinFareList().get(0))
                                    && !outMaoTaiConfig.isUseScore()) {
                                return true;
                            }
                        }

                    }
                }

            }
        }
        if (inMaoTaiConfig != null) {
            String startDateStr = inMaoTaiConfig.getStartDate();
            String endDateStr = inMaoTaiConfig.getEndDate();
            //活动时间比较
            Date date = new Date();
            Date startDate = DateUtils.toDate(startDateStr, DateUtils.YYYY_MM_DD_PATTERN);
            Date endDate = DateUtils.toDate(endDateStr, DateUtils.YYYY_MM_DD_PATTERN);
            if (date.getTime() >= startDate.getTime() && date.getTime() < endDate.getTime()) {
                for (com.juneyaoair.baseclass.response.av.FlightInfo flightInfo : flightInfoComb.getCombFlightInfoList()) {
                    //航班号匹配
                    if (StringUtils.isNotBlank(inMaoTaiConfig.getFlightNo()) && inMaoTaiConfig.getFlightNo().contains(flightInfo.getFlightNo())) {
                        //航班时间
                        String flightStartDateStr = inMaoTaiConfig.getFlightStartDate();
                        String flightEndDateStr = inMaoTaiConfig.getFlightEndDate();
                        Date flightStartDate = DateUtils.toDate(flightStartDateStr, DateUtils.YYYY_MM_DD_PATTERN);
                        Date flightEndDate = DateUtils.toDate(flightEndDateStr, DateUtils.YYYY_MM_DD_PATTERN);
                        Date curFlightDate = DateUtils.toDate(flightInfo.getFlightDate(), DateUtils.YYYY_MM_DD_PATTERN);
                        if (curFlightDate.getTime() >= flightStartDate.getTime() && curFlightDate.getTime() < flightEndDate.getTime()) {
                            //舱位匹配
                            if (StringUtils.isNotBlank(inMaoTaiConfig.getCabins())
                                    && checkCabin(inMaoTaiConfig.getCabins(), flightInfoComb.getAdtCabinFareList().get(0))
                                    && !inMaoTaiConfig.isUseScore()) {
                                return true;
                            }
                        }

                    }
                }

            }
        }
        return false;
    }

    private boolean checkCabin(String cabins, com.juneyaoair.baseclass.response.av.CabinFare cabinFare) {
        if (StringUtils.isNotBlank(cabinFare.getCabinCode())) {
            return cabins.contains(cabinFare.getCabinCode());
        }
        if (StringUtils.isNotBlank(cabinFare.getCabinComb())) {
            String[] cabinArray = cabinFare.getCabinComb().replaceAll("-", ",").replaceAll("/", ",").split(",");
            for (int i = 0; i < cabinArray.length; i++) {
                if (cabins.contains(cabinArray[i])) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 航班列表、订单详情页酒店广告展示
     *
     * @param req
     * @param request
     * @return
     */
    @RequestMapping(value = "flightAdvertisementQuery", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @ApiOperation(value = "航班广告查询", notes = "航班广告查询")
    @InterfaceLog(level = InterfaceLog.LogLevel.DEBUG)
    public BaseResp flightAdvertisementQuery(@RequestBody BaseReq<FlightAdvertisementQueryReq> req, HttpServletRequest request) {
        BaseResp<List<PictureDto>> resp = new BaseResp<>();
        String ip = this.getClientIP(request);
        String reqId = StringUtil.newGUID() + request.getRequestURI();
        try {
            //校验参数
            this.checkRequest(req);
            List<OrderDetailSegment> segmentInfos = req.getRequest().getSegmentInfos();
            String flightDate = segmentInfos.get(0).getArrDateTime();
            String arrCityCode = segmentInfos.get(0).getArrCity();
            // 判断是否往返 出发城市与到达城市对调即为往返
            for (int i = 0; i < segmentInfos.size() - 1; i++) {
                if (segmentInfos.get(i).getDepCity().equals(segmentInfos.get(i + 1).getArrCity())
                        && segmentInfos.get(i).getArrCity().equals(segmentInfos.get(i + 1).getDepCity())) {
                    flightDate = segmentInfos.get(i).getArrDateTime();
                    arrCityCode = segmentInfos.get(i).getArrCity();
                    break;
                }
                // 不是往返航线取最后的航线
                flightDate = segmentInfos.get(i).getArrDateTime();
                arrCityCode = segmentInfos.get(i).getArrCity();
            }
            flightDate = DateUtils.dateToString(DateUtils.toDate(flightDate, DateUtils.YYYY_MM_DD_HH_MM_PATTERN), DateUtils.YYYY_MM_DD_PATTERN);
            List<HotelProductRespDTO> hotelProductRespDTOS = basicService
                    .queryHotelProductList(request.getHeader(HEAD_CHANNEL_CODE), ip, req.getRequest().getLocation(), flightDate, Collections.singletonList(arrCityCode));
            List<PictureDto> pictureDtos = V2OrderObjectConvert.hotelProduct2PictureDto(hotelProductRespDTOS);
            resp.setObjData(pictureDtos);
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
        } catch (Exception e) {
            this.logError(resp, reqId, ip, req, e);
        }
        return resp;
    }


    /**
     * 芬兰航线数据出境同意书
     *
     * @param req
     * @param request
     * @return
     */
    @RequestMapping(value = "carrierNoAYFlight", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @ApiOperation(value = "芬兰航线出境同意书", notes = "芬兰航线数据出境同意书")
    @InterfaceLog(level = InterfaceLog.LogLevel.DEBUG)
    public BaseResp<CarrierNoAYFlightResp> carrierNoAYFlight(@RequestBody BaseReq<CarrierNoAYFlightReq> req, HttpServletRequest request, BindingResult bindingResult) {
        BaseResp<CarrierNoAYFlightResp> resp = new BaseResp<>();
        if (bindingResult.hasErrors()) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
            return resp;
        }
        CarrierNoAYFlightResp carrierNoAYFlightResp = new CarrierNoAYFlightResp();

        if (req.getRequest().getArrCodeList().contains("HEL")) {
            carrierNoAYFlightResp.setAYLeaveCountryText(handConfig.getAYLeaveCountryText());
        }
        resp.setObjData(carrierNoAYFlightResp);
        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
        resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
        return resp;
    }


    /**
     * 售罄候补服务文案
     *
     * @param req
     * @param bindingResult
     * @return
     */
    @RequestMapping(value = "flightWaitingText", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @ApiOperation(value = "售罄候补服务文案", notes = "售罄候补服务文案")
    @InterfaceLog(level = InterfaceLog.LogLevel.DEBUG)
    public BaseResp<CarrierNoAYFlightResp> FlightWaitingText(@RequestBody BaseReq req, BindingResult bindingResult) {
        BaseResp<CarrierNoAYFlightResp> resp = new BaseResp<>();
        if (bindingResult.hasErrors()) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
            return resp;
        }
        CarrierNoAYFlightResp carrierNoAYFlightResp = new CarrierNoAYFlightResp();
        carrierNoAYFlightResp.setAYLeaveCountryText(handConfig.getFlightWaitingText());
        resp.setObjData(carrierNoAYFlightResp);
        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
        resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
        return resp;
    }


    /**
     * 订单数量限制
     *
     * @param req
     * @param bindingResult
     * @return
     */
    @RequestMapping(value = "checkUnPayOrder", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @ApiOperation(value = "订单数量限制", notes = "订单数量限制")
    @InterfaceLog(level = InterfaceLog.LogLevel.DEBUG)
    public BaseResp checkUnPayOrder(@RequestBody BaseReq<UserInfoMust> req, BindingResult bindingResult) {
        BaseResp resp = new BaseResp();
        if (bindingResult.hasErrors()) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
            return resp;
        }
        UserInfoMust userInfoMust = req.getRequest();
        CheckUnPayOrderResp checkUnPayOrderResp = getCheckUnPayOrderResp(req.getChannelCode(), userInfoMust.getFfpCardNo());
        if ("1001".equals(checkUnPayOrderResp.getResultCode())) { //MOBILE
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
        } else {
            if (WSEnum.ERROR.getResultCode().equals(checkUnPayOrderResp.getResultCode())) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo(checkUnPayOrderResp.getErrorInfo());
            } else {
                resp.setResultCode(WSEnum.LOGOUT_VERIFY_ERROR.getResultCode());
                resp.setResultInfo(checkUnPayOrderResp.getErrorInfo());
            }

        }
        return resp;
    }

    private CheckUnPayOrderResp getCheckUnPayOrderResp(String channelCode, String ffpCardNo) {
        CheckUnPayOrderReq checkUnPayOrderReq = new CheckUnPayOrderReq();
        checkUnPayOrderReq.setChannelCode(channelCode);
        checkUnPayOrderReq.setUserNo(getChannelInfo(channelCode, "10"));
        checkUnPayOrderReq.setChannelNo(channelCode);
        checkUnPayOrderReq.setFfpCardCode(ffpCardNo);
        HttpResult result = this.doPostClient(checkUnPayOrderReq, HandlerConstants.URL_FARE_API + HandlerConstants.CHECK_UN_PAY_ORDER);
        if (!result.isResult() || StringUtils.isBlank(result.getResponse())) {
            CheckUnPayOrderResp checkUnPayOrderResp = new CheckUnPayOrderResp();
            checkUnPayOrderResp.setResultCode(WSEnum.ERROR.getResultCode());
            return checkUnPayOrderResp;
        } else {
            CheckUnPayOrderResp checkUnPayOrderResp = (CheckUnPayOrderResp) JsonUtil.jsonToBean(result.getResponse(), CheckUnPayOrderResp.class);
            return checkUnPayOrderResp;
        }

    }

    /**
     * 国内机票候补校验
     *
     * @param req
     * @param request
     * @return
     */
    @RequestMapping(value = "checkWaitOrder", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @ApiOperation(value = "国内机票候补校验", notes = "国内机票候补校验")
    @InterfaceLog(level = InterfaceLog.LogLevel.DEBUG)
    public BaseResp checkWaitOrder(@RequestBody BaseReq<CheckWaitOrderReq> req, HttpServletRequest request, BindingResult bindingResult) {
        BaseResp resp = new BaseResp<>();
        if (bindingResult.hasErrors()) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
            return resp;
        }

        PtApiCRMRequest<PtMemberDetailRequest> detaiReq = buildCommReq(request, req.getChannelCode(), String.valueOf(req.getRequest().getFfpId()), "");
        PtMemberDetailRequest ptMemberDetailRequest = new PtMemberDetailRequest();
        ptMemberDetailRequest.setCardNO(req.getRequest().getFfpCardNo());
        String[] items = {MemberDetailRequestItemsEnum.REALVERIFYINFOS.eName};
        ptMemberDetailRequest.setRequestItems(items);
        detaiReq.setData(ptMemberDetailRequest);
        PtCRMResponse<PtMemberDetail> detailPtCRMResponse = memberService.memberDetail(detaiReq);
        if (detailPtCRMResponse.getCode() != 0) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo(detailPtCRMResponse.getMsg());
            return resp;
        }

        MemberRealNameSummarySoaModel memberRealNameSummarySoaModel = CrmUtil.getNewRealNamePassSummary(detailPtCRMResponse.getData().getRealVerifyInfos());
        if (memberRealNameSummarySoaModel == null) {
            resp.setResultCode(WSEnum.NO_REAL_NAME.getResultCode());
            resp.setErrorInfo(WSEnum.NO_REAL_NAME.getResultInfo());
            return resp;
        }
        CheckWaitOrderReqest checkWaitOrderReqest = new CheckWaitOrderReqest();
        CheckWaitOrderReq checkWaitOrderReq = req.getRequest();
        BeanUtils.copyProperties(checkWaitOrderReq, checkWaitOrderReqest);
        checkWaitOrderReqest.setFFPId(checkWaitOrderReq.getFfpId());
        checkWaitOrderReqest.setSfCardNo(checkWaitOrderReq.getFfpCardNo());
        checkWaitOrderReqest.setChannelCode(ChannelCodeEnum.MOBILE.getChannelCode());

        checkWaitOrderReq.setVersion("10");
        HttpResult serviceResult = doPostClient(checkWaitOrderReqest, HandlerConstants.URL_FARE_OPEN_API + HandlerConstants.ORDER_CHECK_WAIT_ORDER);

        if (null == serviceResult || !serviceResult.isResult() || StringUtils.isBlank(serviceResult.getResponse())) {
            resp.setResultCode(WSEnum.NETWORK_ERROR.getResultCode());
            resp.setErrorInfo("查询可用积分网络错误");
            return resp;
        }
        CheckWaitOrderResp checkWaitOrderResp = (CheckWaitOrderResp) JsonUtil.jsonToBean(serviceResult.getResponse(), CheckWaitOrderResp.class);
        if ("1001".equals(checkWaitOrderResp.getResultCode())) {
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
        } else {
            if (!"1003".equals(checkWaitOrderResp.getResultCode())) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo(checkWaitOrderResp.getErrorInfo());
            }
        }


        return resp;
    }


    private void sortByCreateTime(List<OrderTotalBriefInfo> orderBriefInfoList) {
        Collections.sort(orderBriefInfoList, (a, b) -> DateUtils.toDate(b.getCreateDatetime(), "yyyy-MM-dd HH:mm:ss").compareTo(DateUtils.toDate(a.getCreateDatetime(), "yyyy-MM-dd HH:mm:ss")));
    }

    /**
     * 构建订单列表请求参数
     *
     * @param ptBrief
     * @param briefReq
     * @return
     */
    private PtOrderTotalBriefReq createReq(PtOrderTotalBriefReq ptBrief, OrderBriefReq briefReq) {
        ptBrief.setCreateDateBegin(briefReq.getDateBegin());
        ptBrief.setCreateDateEnd(briefReq.getDateEnd());
        if (StringUtils.isBlank(ptBrief.getCreateDateBegin())) {
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.MONTH, -6);
            ptBrief.setCreateDateBegin(DateUtils.dateToString(calendar.getTime(), DateUtils.YYYY_MM_DD_PATTERN));
        }
        if (StringUtils.isBlank(ptBrief.getCreateDateEnd())) {
            ptBrief.setCreateDateEnd(DateUtils.dateToString(new Date(), DateUtils.YYYY_MM_DD_PATTERN));
        }
        ptBrief.setPageSize(briefReq.getPageSize());
        ptBrief.setPageNo(briefReq.getPageNo());
        ptBrief.setCustomerNo(briefReq.getCustomerNo());
        return ptBrief;
    }

    /**
     * 舱位等级转换名字
     */
    public String transformCabinClassName(String cabinClass) {
        if ("C".equals(cabinClass)) {
            return "公务舱";
        } else if ("F".equals(cabinClass)) {
            return "超值头等舱";
        } else if ("J".equals(cabinClass)) {
            return "公务舱";
        } else {
            return "经济舱";
        }
    }

    //取crm接口客户端密码
    private String getClientPwd(String channelCode) {
        return getChannelInfo(channelCode, "40");
    }

}

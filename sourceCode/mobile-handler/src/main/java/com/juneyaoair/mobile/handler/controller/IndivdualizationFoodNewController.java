package com.juneyaoair.mobile.handler.controller;

import com.alibaba.fastjson.JSONObject;
import com.geetest.geeguard.sdk.GeetestLib;
import com.juneyaoair.appenum.WSEnum;
import com.juneyaoair.appenum.geetest.GeetestTypeEnum;
import com.juneyaoair.baseclass.basicsys.response.AirPortInfoDto;
import com.juneyaoair.baseclass.common.request.BaseReq;
import com.juneyaoair.baseclass.common.response.BaseResp;
import com.juneyaoair.baseclass.individualizationSetMeal.request.IndividualizationSetMealReq;
import com.juneyaoair.baseclass.individualizationSetMeal.request.idvmMealAddReq.IdvmMealAddReq;
import com.juneyaoair.baseclass.individualizationSetMeal.request.idvmMealAddReq.MealData;
import com.juneyaoair.baseclass.individualizationSetMeal.request.idvmMealCancelApply.IdvmMealCancelApply;
import com.juneyaoair.baseclass.individualizationSetMeal.response.*;
import com.juneyaoair.baseclass.individualizationSetMeal.response.idvmMealAddResp.AddMealResp;
import com.juneyaoair.baseclass.individualizationSetMeal.response.idvmMealQueryResp.IdvmMealQueryApiResp;
import com.juneyaoair.baseclass.individualizationSetMeal.response.idvmMealQueryResp.MealQueryResp;
import com.juneyaoair.baseclass.individualizationSetMeal.response.idvmMealQueryResp.MealType;
import com.juneyaoair.baseclass.request.av.AircraftModel;
import com.juneyaoair.baseclass.request.geetest.Geetest;
import com.juneyaoair.exception.CommonException;
import com.juneyaoair.mobile.aop.annotation.InterfaceLog;
import com.juneyaoair.mobile.aop.annotation.NotDuplicate;
import com.juneyaoair.mobile.comm.service.IRedisService;
import com.juneyaoair.mobile.constants.MobileCoreBeanNames;
import com.juneyaoair.mobile.handler.HandlerConstants;
import com.juneyaoair.mobile.handler.comm.RedisKeyConfig;
import com.juneyaoair.mobile.handler.config.bean.HandConfig;
import com.juneyaoair.mobile.handler.controller.v2.util.CommonUtil;
import com.juneyaoair.mobile.handler.manage.OrderManage;
import com.juneyaoair.mobile.handler.service.GeetestService;
import com.juneyaoair.mobile.handler.service.IBasicService;
import com.juneyaoair.mobile.handler.service.LocalCacheService;
import com.juneyaoair.mobile.handler.util.FlightUtil;
import com.juneyaoair.mobile.handler.util.GeetestUtil;
import com.juneyaoair.mobile.mongo.entity.FlightInfo;
import com.juneyaoair.pattern.PatternCommon;
import com.juneyaoair.thirdentity.request.speedRefund.TicketInfoRequest;
import com.juneyaoair.thirdentity.response.detr.PtIBETicketInfo;
import com.juneyaoair.thirdentity.response.detr.PtSegmentInfo;
import com.juneyaoair.thirdentity.response.speedRefund.TicketListInfoResponse;
import com.juneyaoair.utils.StringUtil;
import com.juneyaoair.utils.http.HttpResult;
import com.juneyaoair.utils.http.HttpUtil;
import com.juneyaoair.utils.json.JsonMapper;
import com.juneyaoair.utils.json.JsonUtil;
import com.juneyaoair.utils.util.DateUtils;
import com.juneyaoair.utils.util.MetricLogUtil;
import com.juneyaoair.utils.util.SensitiveInfoHider;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.juneyaoair.utils.util.BeanUtils.copyProperties;

/**
 * @Classname IndivdualizationFoodNewController
 * @Description 餐食新接口
 * @Date 2019/8/2 10:42
 * @Created by yzh
 */
@RequestMapping("individualizationNew")
@RestController
@Api(value = "个性化餐食接口", description = "个性化餐食接口")
@Slf4j
public class IndivdualizationFoodNewController extends BassController {
    @Autowired
    private IBasicService basicService;
    @Autowired
    private HandConfig handConfig;
    @Autowired
    private LocalCacheService localCacheService;
    @Autowired
    private GeetestService geetestService;
    @Autowired
    private OrderManage orderManage;
    @Resource(name = MobileCoreBeanNames.API_REDIS_SERVICE)
    private IRedisService redisService;

    /*
     * 根据证件号/票号和乘机人姓名查询航班信息(包含个性化餐食信息)
     **/
    @InterfaceLog
    @ApiOperation("航班列表信息查询")
    @RequestMapping(value = "queryFlightInfo", method = RequestMethod.POST)
    public BaseResp<List<IndividualizationFlightInfoResp>> queryFlightInfo(@RequestBody BaseReq<IndividualizationSetMealReq> baseReq, HttpServletRequest httpServletRequest) {
        //个性化航班信息响应
        BaseResp<List<IndividualizationFlightInfoResp>> baseResp = new BaseResp<>();
        String ip = this.getClientIP(httpServletRequest);
        String headChannelCode = httpServletRequest.getHeader(HEAD_CHANNEL_CODE);
        //得到具体请求参数
        IndividualizationSetMealReq reqData = baseReq.getRequest();
        //票号不能为空
        if (StringUtils.isBlank(reqData.getTicketNo())) {
            baseResp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            baseResp.setResultInfo("票号/证件号不能为空");
            return baseResp;
        }
        //旅客姓名不能为空
        if (StringUtils.isBlank(reqData.getPassName())) {
            baseResp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            baseResp.setResultInfo("旅客姓名不能为空");
            return baseResp;
        }
        if (StringUtils.isAnyBlank(reqData.getFlightNo())) {
            throw new IllegalArgumentException("航班信息不可为空");
        }
        //极验验证操作
        GeetestLib gtSdk = GeetestUtil.initGeetest(headChannelCode, GeetestTypeEnum.TICKET.getGeetestType());
        HashMap<String, String> param = new HashMap<>();
        param.put("user_id", ip); //网站用户id  设备号
        param.put("client_type", reqData.getClient_type()); //web:电脑上的浏览器；h5:手机上的浏览器，包括移动应用内完全内置的web_view；native：通过原生SDK植入APP应用的方式
        param.put("ip_address", ip); //传输用户请求验证时所携带的IP
        Geetest geetest = new Geetest(reqData.getGeetest_challenge(), reqData.getGeetest_validate(), reqData.getGeetest_seccode());
        geetestService.validateMd5(gtSdk, geetest, param);
        JSONObject tags = new JSONObject();
        tags.put("IP", ip);
        tags.put("FfpCardNo", reqData.getFfpCardNo());
        tags.put("ChannelCode", headChannelCode);
        MetricLogUtil.saveMetricLog("个性化餐食-客票提取", tags, new BigDecimal(1));
        //查询所有航班信息
        List<IndividualizationFlightInfoResp> flightList = getFlightInfoNew(httpServletRequest, baseReq);
        //如果没查到数据
        if (CollectionUtils.isEmpty(flightList)) {
            baseResp.setResultCode(WSEnum.NO_DATA.getResultCode());
            baseResp.setResultInfo(WSEnum.NO_DATA.getResultInfo());
            return baseResp;
        }
        boolean check = flightList.stream().anyMatch(flightInfo -> {
            if (reqData.getFlightNo().equals(flightInfo.getFlightNo())) {
                return true;
            } else {
                return false;
            }
        });
        if (!check) {
            baseResp.setResultCode(WSEnum.NO_DATA.getResultCode());
            baseResp.setResultInfo("未查询到符合条件的客票");
            return baseResp;
        }
        //响应集合
        List<IndividualizationFlightInfoResp> newList = new ArrayList<>();
        //时间解析格式
        SimpleDateFormat sdf = new SimpleDateFormat(DateUtils.YYYY_MM_DD_HH_MM_SS_PATTERN);
        SimpleDateFormat sdf2 = new SimpleDateFormat(DateUtils.YYYY_MM_DD_HH_MM_PATTERN);
        SimpleDateFormat sdf3 = new SimpleDateFormat("HH:mm");
        //得到机场信息
        for (IndividualizationFlightInfoResp resp : flightList) {
            //如果有效票
            if (resp.getTicketStatus().equals("CHECKED IN") || resp.getTicketStatus().equals("OPEN FOR USE")) {
                try {
                    //设置出发日期
                    resp.setDepTime(resp.getDepTime().indexOf('-') > -1 ? resp.getDepTime() : (resp.getDepDate() + " " + resp.getDepTime()));
                    //出发机场信息
                    AirPortInfoDto depAirPortInfo = localCacheService.getLocalAirport(resp.getDepAirportCode(), resp.getDepDate());
                    //到达机场信息
                    AirPortInfoDto arrAirPortInfo = localCacheService.getLocalAirport(resp.getArrAirportCode(), resp.getDepDate());
                    //出发机场名
                    resp.setDepAirPortName(depAirPortInfo.getAirPortName());
                    //到达机场名
                    resp.setArrAirPortName(arrAirPortInfo.getAirPortName());
                    FlightInfo flightInfo = new FlightInfo();
                    //设置到达机场三字码
                    flightInfo.setArrAirport(resp.getArrAirportCode());
                    //出发机场三字码
                    flightInfo.setDepAirport(resp.getDepAirportCode());
                    //出发日期
                    flightInfo.setFlightDate(resp.getDepDate());
                    //航班号
                    flightInfo.setFlightNo(resp.getFlightNo());
                    //批量查询航班信息
                    List<FlightInfo> queryBaseFlight = basicService.queryFlightInfo(flightInfo);
                    if (CollectionUtils.isNotEmpty(queryBaseFlight)) {
                        flightInfo = queryBaseFlight.get(0);
                        //设置到达时间
                        resp.setArrayTime(flightInfo.getFlightArrDate() + " " + flightInfo.getArrDateTime().substring(0, 2) + ":" + flightInfo.getArrDateTime().substring(2));
                        //飞行时长
                        resp.setFlightTime(sdf2.parse(flightInfo.getArrDateChinaTime()).getTime() - sdf2.parse(flightInfo.getDepDateChinaTime()).getTime());
                        //机型
                        Map<String, AircraftModel> stringAircraftModelMap = FlightUtil.toAircraftModelMap(handConfig.getAircraftModel());
                        if (stringAircraftModelMap != null) {
                            resp.setPlanType(stringAircraftModelMap.get(flightInfo.getPlanType()).getRemark());
                        } else {
                            resp.setPlanType(flightInfo.getPlanType());
                        }
                        //周几
                        String weekStr = DateUtils.getWeekStr(DateUtils.toDate(resp.getDepDate()));
                        resp.setWeekDay(weekStr);
                        //出发到达时分
                        resp.setDepHour(sdf3.format(sdf.parse(resp.getDepTime())));
                        resp.setArrHour(sdf3.format(sdf2.parse(resp.getArrayTime())));
                        //舱位名称
                        resp.setCabinName(getCabinName(resp.getCabin()));
                        //票号
                        resp.setTicketNo(getReplace(resp.getTicketNo()));
                    }
                    Date now = new Date();
                    Date depDate = sdf.parse(resp.getDepTime());
                    Calendar calendar = Calendar.getInstance();
                    calendar.setTime(depDate);
                    //截至时间 航班起飞前一天14点之前
                    String lastTime = calendar.get(Calendar.YEAR) + "-" + (calendar.get(Calendar.MONTH) + 1) + "-" + (calendar.get(Calendar.DAY_OF_MONTH) - 1) + " " + FOURTEEN_OCLOCK;
                    //格式化截止日期
                    Date orderDate = sdf.parse(lastTime);
                    //如果未到截止日期
                    if (now.before(orderDate)) {
                        //调用旅服网接口判断是否有餐食可选
                        reqData.setFlightNo(resp.getFlightNo());
                        reqData.setDepartureStation(resp.getDepAirportCode());
                        reqData.setCabinType(resp.getCabin());
                        reqData.setFlightDate(flightInfo.getFlightDate());
                        MealQueryResp mealQueryResp = getMealQueryResp(reqData);
                        //如果查询成功
                        if (mealQueryResp != null && mealQueryResp.getData() != null && mealQueryResp.getCode().equals("0")) {
                            if (CollectionUtils.isEmpty(mealQueryResp.getData())) {
                                //无可选餐食
                                resp.setSubmitYn("2");
                            } else {
                                //封装旅客服务网查询是否重复申请请求
                                JSONObject request = new JSONObject();
                                JSONObject postJson = new JSONObject();
                                request.put(FLIGHT_NO, resp.getFlightNo());
                                request.put(FLIGHT_DATE, resp.getDepDate());
                                request.put(ARRIVAL_STATION, resp.getArrAirportCode());
                                request.put(DEPARTURE_STATION, resp.getDepAirportCode());
                                request.put(TICKET_NO, resp.getTicketNo());
                                postJson.put("data", request);
                                postJson.put(MEMBER_ID, reqData.getFfpCardNo());
                                postJson.put(INFT_CODE, "1111");
                                //调用旅客服务网查询
                                String passResult = this.passagerPost(postJson, HandlerConstants.URL_PASSAGER_API + "/passenger/idvmMealPassenger/judge");
                                JSONObject jsonObject = JSONObject.parseObject(passResult);
                                if (null == jsonObject.getJSONObject("data")) {
                                    //未选餐
                                    resp.setSubmitYn("0");
                                } else {
                                    //已选餐
                                    resp.setSubmitYn("1");
                                }
                            }
                        } else {
                            //如果data无数据，则无餐食可选
                            resp.setSubmitYn("2");
                        }
                    } else {
                        //已到截止日期
                        resp.setSubmitYn("2");
                    }
                    newList.add(resp);
                } catch (Exception e) {
                    log.error(LOG_ERR, e);
                    baseResp.setResultCode(WSEnum.ERROR.getResultCode());
                    baseResp.setResultInfo(WSEnum.ERROR.getResultInfo());
                    return baseResp;
                }
            }
        }
        //判空
        if (CollectionUtils.isEmpty(newList)) {
            baseResp.setResultCode(WSEnum.NO_DATA.getResultCode());
            baseResp.setResultInfo("当前查询无可选餐食航班");
            return baseResp;
        }
        //排序
        newList.sort(Comparator.comparing(IndividualizationFlightInfoResp::getDepTime));
        newList.sort(Comparator.comparing(IndividualizationFlightInfoResp::getSubmitYn));
        //响应结果集
        baseResp.setObjData(newList);
        baseResp.setResultCode(WSEnum.SUCCESS.getResultCode());
        return baseResp;
    }


    /**
     * 查询可选餐食列表新接口
     */
    @InterfaceLog
    @ApiOperation("可选餐食列表查询")
    @RequestMapping(value = "queryMealList", method = RequestMethod.POST)
    public BaseResp<IdvmMealQueryApiResp> queryMealList(@RequestBody BaseReq<IndividualizationSetMealReq> baseReq) {
        //请求数据
        IndividualizationSetMealReq reqData = baseReq.getRequest();
        //响应数据
        BaseResp<IdvmMealQueryApiResp> respData = new BaseResp<>();

        //数据校验 航班日期
        if (StringUtils.isBlank(reqData.getFlightDate())) {
            respData.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            respData.setResultInfo("航班日期不能为空");
            return respData;
        }
        //航班号
        if (StringUtils.isBlank(reqData.getFlightNo())) {
            respData.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            respData.setResultInfo("航班号不能为空");
            return respData;
        }
        //始发站 同一航班可能有两段，必须确认始发站
        if (StringUtils.isBlank(reqData.getDepartureStation())) {
            respData.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            respData.setResultInfo("出发站不能为空");
            return respData;
        }
        //舱位
        if (StringUtils.isBlank(reqData.getCabinType())) {
            respData.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            respData.setResultInfo("舱位信息不能为空");
            return respData;
        }

        try {
            //调用旅客服务网查询
            MealQueryResp mealQueryResp = getMealQueryResp(reqData);
            //响应正常
            if (mealQueryResp != null && mealQueryResp.getCode().equals("0") && mealQueryResp.getData().size() != 0) {
                //查询成功
                IdvmMealQueryApiResp idvmMealQueryApiResp = new IdvmMealQueryApiResp();
                idvmMealQueryApiResp.setMealTab(mealQueryResp.getData());
                //判断有几段餐食
                int mealCount = mealQueryResp.getData().get(0).getMealNewList().size();
                switch (mealCount) {
                    case 1:
                        idvmMealQueryApiResp.setFlightCategory("1");
                        break;
                    case 2:
                        idvmMealQueryApiResp.setFlightCategory("2");
                        break;
                    case 0:
                        idvmMealQueryApiResp.setFlightCategory("0");
                        break;
                    default:
                        idvmMealQueryApiResp.setFlightCategory("0");
                }
                //舱位
                if (ECON_CABIN.equals(getCabinName(reqData.getCabinType()))) {
                    idvmMealQueryApiResp.setCabinType("2");
                } else if (BUSS_CABIN.equals(getCabinName(reqData.getCabinType()))) {
                    idvmMealQueryApiResp.setCabinType("1");
                }
                //餐种集合
                List<MealType> mealTypeList = new ArrayList<>();
                //将两段特殊餐食分组处理
                idvmMealQueryApiResp.getMealTab().stream()
                        .filter(mealTab -> !"3".equals(mealTab.getMealCate()))
                        .forEach(mealTab -> mealTab.getMealNewList()
                                .forEach(idvMealNew -> {
                                    mealTypeList.add(new MealType(idvMealNew.getMealType(), idvMealNew.getMealTypeName()));
                                }));
                //设置idvMeal里的meal mealTypeName
                idvmMealQueryApiResp.getMealTab().stream()
                        .forEach(mealTab -> mealTab.getMealNewList().stream()
                                .forEach(idvMealNew -> idvMealNew.getMealList().stream()
                                        .forEach(idvmMeal -> {
                                            idvmMeal.setMealTypeName(idvMealNew.getMealTypeName());
                                            idvmMeal.setMealTypes(mealTypeList);
                                            idvmMeal.setMealCate(mealTab.getMealCate());
                                        })));
                respData.setResultCode(WSEnum.SUCCESS.getResultCode());
                respData.setObjData(idvmMealQueryApiResp);
            } else {
                //查询失败
                respData.setResultCode(WSEnum.ERROR.getResultCode());
                respData.setResultInfo(WSEnum.ERROR.getResultInfo());
            }
        } catch (Exception e) {
            respData.setResultCode(WSEnum.ERROR.getResultCode());
            respData.setResultInfo(WSEnum.ERROR.getResultInfo());
            return respData;
        }
        return respData;
    }


    /**
     * 新增餐食申请
     **/
    @InterfaceLog
    @ApiOperation("提交餐食申请")
    @RequestMapping(value = "submitApply", method = RequestMethod.POST)
    @NotDuplicate
    public BaseResp submitApply(@RequestBody BaseReq<IdvmMealAddReq> baseReq) {
        //响应数据
        BaseResp resp = new BaseResp<>();
        //请求数据
        IdvmMealAddReq reqData = baseReq.getRequest();
        //Validation校验数据
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<BaseReq>> violations = validator.validate(baseReq);
        if (CollectionUtils.isNotEmpty(violations)) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo(violations.iterator().next().getMessage());
            return resp;
        }
        //校验数据
        if (StringUtils.isBlank(reqData.getFfpId())) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        if (StringUtils.isBlank(baseReq.getChannelCode())) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        //终端号
        if (baseReq.getChannelCode().equals("MWEB")) {
            baseReq.setChannelCode(MOBILE);
        }
        //校验身份
        boolean loginFlag = this.checkKeyInfo(reqData.getFfpId(), reqData.getLoginKeyInfo(), baseReq.getChannelCode());
        if (!loginFlag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        //去除票号中的"-"
        reqData.setTicketNo(getReplace(reqData.getTicketNo()));
        //将证件号的"-"去除
        reqData.setPassIdcard(getReplace(reqData.getPassIdcard()));

        //判断是否超过截止时间
        FlightInfo flightInfo = new FlightInfo();
        flightInfo.setFlightNo(reqData.getFlightNo());
        flightInfo.setFlightDate(reqData.getFlightDate());
        flightInfo.setDepAirport(reqData.getDepartureStation());
        flightInfo.setArrAirport(reqData.getArrivalStation());
        Boolean outDeadline = this.isBeforeDeadline(flightInfo);
        if (!outDeadline) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("已超过餐食申请截止时间，不可申请");
            return resp;
        }
        //两段特殊餐食
        if (reqData.getBookMealList().size() == 2 && !reqData.getBookMealList().get(0).getMealCate().equals("3")) {
            //得到餐种列表
            List<MealType> mealTypes = reqData.getBookMealList().get(0).getMealTypes();
            //餐食数据列表
            List<MealData> bookMealList = reqData.getBookMealList();
            //餐食mealType数据
            MealData mealDataOne = bookMealList.get(0);
            MealData mealDataTwo = bookMealList.get(1);
            MealType mealTypeOne = mealTypes.get(0);
            MealType mealTypeTwo = mealTypes.get(1);
            mealDataOne.setMealType(mealTypeOne.getMealType());
            mealDataOne.setMealTypeName(mealTypeOne.getMealTypeName());
            mealDataTwo.setMealType(mealTypeTwo.getMealType());
            mealDataTwo.setMealTypeName(mealTypeTwo.getMealTypeName());
        }

        //封装旅服网请求数据
        JSONObject jsonObject = new JSONObject();
        JSONObject postJson = new JSONObject();
        jsonObject.put("accountContact",reqData.getAccountContact());
        jsonObject.put(DEPARTURE_STATION,reqData.getDepartureStation());
        jsonObject.put(CABIN_TYPE, CommonUtil.getCabinClassByCabinCode(reqData.getCabinType(), handConfig.getCabinClass()));
        jsonObject.put(ARRIVAL_STATION,reqData.getArrivalStation());
        jsonObject.put(FLIGHT_DATE,reqData.getFlightDate());
        jsonObject.put(FLIGHT_NO,reqData.getFlightNo());
        jsonObject.put(FFP_ID,reqData.getFfpId());
        String idCard = redisService.getData(RedisKeyConfig.createUpIdInfo(reqData.getTicketNo()));
        if(StringUtils.isBlank(idCard)){
            throw new CommonException(WSEnum.OPERATION_TIMEOUT.getResultCode(),WSEnum.OPERATION_TIMEOUT.getResultInfo());
        }
        jsonObject.put("passIdcard",idCard);
        jsonObject.put("passName",reqData.getPassName());
        jsonObject.put("remark",reqData.getRemark());
        jsonObject.put(TICKET_NO,reqData.getTicketNo());
        jsonObject.put("bookMealList",reqData.getBookMealList());
        jsonObject.put("endLimitDate",reqData.getEndLimitDate());
        postJson.put("data",jsonObject);
        postJson.put(MEMBER_ID, reqData.getFfpCardNo());
        postJson.put(INFT_CODE, "1111");
        //调用旅服网新增餐食
        String result = this.passagerPost(postJson, HandlerConstants.URL_PASSAGER_API + "/passenger/idvmMealPassenger/addIdvmMealPassengerNew");
        //解析result
        AddMealResp addMealResp = JsonMapper.buildNormalMapper().fromJson(result, AddMealResp.class);
        //判断响应结果 "msg":"操作成功!","code":0,"data":true
        if ("0".equals(addMealResp.getCode()) && "true".equals(addMealResp.getData())) {
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(addMealResp.getMsg());
        }
        return resp;
    }

    /**
     * 取消餐食申请
     */
    @ApiOperation("取消餐食申请")
    @RequestMapping(value = "cancelApply", method = RequestMethod.POST)
    public BaseResp cancelApply(@RequestBody BaseReq<IdvmMealCancelApply> baseReq) {
        //请求
        IdvmMealCancelApply req = baseReq.getRequest();
        //响应
        BaseResp baseResp = new BaseResp<>();
        //判空检验
        if (StringUtils.isBlank(baseReq.getChannelCode())) {
            baseResp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            baseResp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return baseResp;
        }
        if (StringUtils.isBlank(req.getFfpId())) {
            baseResp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            baseResp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return baseResp;
        }
        //渠道号
        if (baseReq.getChannelCode().equals("MWEB")) {
            baseReq.setChannelCode(MOBILE);
        }
        //身份校验
        boolean flag = this.checkKeyInfo(req.getFfpId(), req.getLoginKeyInfo(), baseReq.getChannelCode());
        if (!flag) {
            baseResp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            baseResp.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return baseResp;
        }
        //判空校验
        if (StringUtils.isBlank(req.getArrivalStation()) || StringUtils.isBlank(req.getDepartureStation())
                || StringUtils.isBlank(req.getFlightDate()) || StringUtils.isBlank(req.getFlightNo())) {
            baseResp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            baseResp.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return baseResp;
        }
        //查询航班具体信息
        FlightInfo flightInfo = new FlightInfo();
        flightInfo.setArrAirport(req.getArrivalStation());
        flightInfo.setDepAirport(req.getDepartureStation());
        flightInfo.setFlightDate(req.getFlightDate());
        flightInfo.setFlightNo(req.getFlightNo());
        Boolean beforeDeadline = isBeforeDeadline(flightInfo);
        //已超时
        if (!beforeDeadline) {
            baseResp.setResultCode(WSEnum.ERROR.getResultCode());
            baseResp.setResultInfo("已超过餐食取消截止时间，不可取消");
            return baseResp;
        }
        //旅服务请求数据
        JSONObject jsonObject = new JSONObject();
        JSONObject postJson = new JSONObject();
        postJson.put(MEMBER_ID, req.getFfpCardNo());
        postJson.put(INFT_CODE, "1111");
        boolean cancelFlag = true;
        for (String id : req.getId()) {
            jsonObject.put("id", id);
            postJson.put("data", jsonObject);
            //调用旅客服务取消餐食申请
            String result = this.passagerPost(postJson, HandlerConstants.URL_PASSAGER_API + "/passenger/idvmMealPassenger/deleteIdvmMealPassenger");
            //解析result
            AddMealResp addMealResp = JsonMapper.buildNormalMapper().fromJson(result, AddMealResp.class);
            //如果任意一条取消失败
            if (!("0".equals(addMealResp.getCode()) && "true".equals(addMealResp.getData()))) {
                cancelFlag = false;
            }
        }
        if (cancelFlag) {
            baseResp.setResultCode(WSEnum.SUCCESS.getResultCode());
        } else {
            baseResp.setResultCode(WSEnum.ERROR.getResultCode());
            baseResp.setResultInfo("取消餐食申请失败，请联系人工客服");
        }
        return baseResp;
    }

    /**
     * 更改餐食申请
     */
    @InterfaceLog
    @ApiOperation("更改餐食申请")
    @RequestMapping(value = "updateApply", method = RequestMethod.POST)
    public BaseResp updateApply(@RequestBody BaseReq<IdvmMealAddReq> baseReq) {
        BaseResp baseResp = new BaseResp();
        //请求数据
        IdvmMealAddReq req = baseReq.getRequest();
        //用户身份校验
        if (StringUtils.isBlank(req.getFfpId())) {
            baseResp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            baseResp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return baseResp;
        }
        if (StringUtils.isBlank(baseReq.getChannelCode())) {
            baseResp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            baseResp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return baseResp;
        }
        //终端号
        if (baseReq.getChannelCode().equals("MWEB")) {
            baseReq.setChannelCode(MOBILE);
        }
        //校验身份
        boolean loginFlag = this.checkKeyInfo(req.getFfpId(), req.getLoginKeyInfo(), baseReq.getChannelCode());
        if (!loginFlag) {
            baseResp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            baseResp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return baseResp;
        }
        //去除票号中的"-"
        req.setTicketNo(getReplace(req.getTicketNo()));
        //将证件号的"-"去除
        req.setPassIdcard(getReplace(req.getPassIdcard()));
        //判断是否超过截止日期
        FlightInfo flightInfo = new FlightInfo();
        flightInfo.setFlightNo(req.getFlightNo());
        flightInfo.setFlightDate(req.getFlightDate());
        flightInfo.setDepAirport(req.getDepartureStation());
        flightInfo.setArrAirport(req.getArrivalStation());
        Boolean beforeDeadline = isBeforeDeadline(flightInfo);
        if (!beforeDeadline) {
            baseResp.setResultCode(WSEnum.ERROR.getResultCode());
            baseResp.setResultInfo("已超过餐食申请截止时间，不可申请");
            return baseResp;
        }
        //封装旅客服务网请求数据
        JSONObject postJson = new JSONObject();
        postJson.put(MEMBER_ID, req.getFfpCardNo());
        postJson.put(INFT_CODE, "1111");
        JSONObject jsonObject = new JSONObject();
        jsonObject.put(FLIGHT_NO,req.getFlightNo());
        jsonObject.put(FLIGHT_DATE,req.getFlightDate());
        jsonObject.put(DEPARTURE_STATION,req.getDepartureStation());
        jsonObject.put(ARRIVAL_STATION,req.getArrivalStation());
        jsonObject.put("passName",req.getPassName());
        String idCard = redisService.getData(RedisKeyConfig.createUpIdInfo(req.getTicketNo()));
        if(StringUtils.isBlank(idCard)){
            throw new CommonException(WSEnum.OPERATION_TIMEOUT.getResultCode(),WSEnum.OPERATION_TIMEOUT.getResultInfo());
        }
        jsonObject.put("passIdcard",req.getPassIdcard());
        jsonObject.put("accountContact",req.getAccountContact());
        jsonObject.put(FFP_ID,req.getFfpId());
        jsonObject.put(CABIN_TYPE,req.getCabinType());
        jsonObject.put(TICKET_NO,req.getTicketNo());
        jsonObject.put("remark",req.getRemark());
        //封装每一段餐食数据
        boolean updateFlag = true;
        for (MealData mealData : req.getBookMealList()) {
            jsonObject.put("id", mealData.getId());
            jsonObject.put(IDVM_MEAL_NAME, mealData.getIdvmMealName());
            jsonObject.put("idvmMealImage", mealData.getIdvmMealImage());
            jsonObject.put("idvmMealCode", mealData.getIdvmMealCode());
            jsonObject.put("dealStatus", mealData.getDealStatus());
            jsonObject.put("mealCategory", mealData.getMealCategory());
            postJson.put("data", jsonObject);
            String result = this.passagerPost(postJson, HandlerConstants.URL_PASSAGER_API + "/passenger/idvmMealPassenger/updateIdvmMealPassenger");
            //解析响应
            AddMealResp addMealResp = JsonMapper.buildNormalMapper().fromJson(result, AddMealResp.class);
            //是否更新成功
            if (!("0".equals(addMealResp.getCode()) && "true".equals(addMealResp.getData()))) {
                updateFlag = false;
            }
        }
        if (updateFlag) {
            baseResp.setResultCode(WSEnum.SUCCESS.getResultCode());
        } else {
            baseResp.setResultCode(WSEnum.ERROR.getResultCode());
            baseResp.setResultInfo("取消餐食申请失败，请联系人工客服");
        }
        return baseResp;
    }

    /**
     * 根据ffpId查询餐食申请记录
     */
    @ApiOperation("查询餐食申请记录")
    @RequestMapping(value = "queryApply", method = RequestMethod.POST)
    public BaseResp<List<IdvmMealApplyListResp>> queryApply(@RequestBody BaseReq<IndividualizationSetMealReq> baseReq, HttpServletRequest request) {
        //请求响应
        BaseResp<List<IdvmMealApplyListResp>> resp = new BaseResp<>();
        String ip = this.getClientIP(request);
        IndividualizationSetMealReq reqData = baseReq.getRequest();
        //校验数据
        if (StringUtils.isBlank(reqData.getFfpId())) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        if (StringUtils.isBlank(baseReq.getChannelCode())) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        //终端号
        if (baseReq.getChannelCode().equals("MWEB")) {
            baseReq.setChannelCode(MOBILE);
        }
        //校验身份
        boolean loginFlag = this.checkKeyInfo(reqData.getFfpId(), reqData.getLoginKeyInfo(), baseReq.getChannelCode());
        if (!loginFlag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        //封装旅客服务网查询数据
        JSONObject jsonObject = new JSONObject();
        JSONObject postJson = new JSONObject();
        jsonObject.put(FFP_ID, reqData.getFfpId());
        postJson.put("data", jsonObject);
        postJson.put(MEMBER_ID, reqData.getFfpCardNo());
        postJson.put(INFT_CODE, "1111");
        //发起请求
        String result = this.passagerPost(postJson, HandlerConstants.URL_PASSAGER_API + "/passenger/idvmMealPassenger/selectByFfpid");
        IndividualizationApplyResultResp passagerResp = JsonMapper.buildNonNullMapper().fromJson(result, IndividualizationApplyResultResp.class);
        if ("0".equals(passagerResp.getCode())) {
            //旅客服务网查询得到的数据
            List<IndividualizationApplyInfoResp> passagerRespData = passagerResp.getData();
            if (CollectionUtils.isEmpty(passagerRespData)) {
                resp.setResultCode(WSEnum.NO_DATA.getResultCode());
                resp.setResultInfo(WSEnum.NO_DATA.getResultInfo());
                return resp;
            }
            try {
                SimpleDateFormat sdf2 = new SimpleDateFormat(DateUtils.YYYY_MM_DD_HH_MM_PATTERN);
                SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");
                //用来存放处理后的旅客服务网响应数据
                List<IndividualizationApplyInfoResp> newData = new ArrayList<>();
                for (IndividualizationApplyInfoResp infoResp : passagerRespData) {
                    //如果航班时间信息中没有年月日信息
                    if (infoResp.getTakeOffTime().indexOf('-') < 0) {
                        infoResp.setTakeOffTime(infoResp.getFlightDate() + " " + infoResp.getTakeOffTime());
                    }
                    if (infoResp.getPlanArrTime().indexOf('-') < 0) {
                        infoResp.setPlanArrTime(infoResp.getFlightDate() + " " + infoResp.getPlanArrTime());
                    }
                    //拿到当前时间与起飞时间
                    Date now = new Date();
                    Date depDate = sdf2.parse(infoResp.getTakeOffTime());
                    Calendar calendar = Calendar.getInstance();
                    calendar.setTime(depDate);
                    //申请截止时间
                    String lastTime = calendar.get(Calendar.YEAR) + "-" + (calendar.get(Calendar.MONTH) + 1) + "-" + (calendar.get(Calendar.DAY_OF_MONTH) - 1) + " " + FOURTEEN_OCLOCK;
                    Date orderDate = sdf2.parse(lastTime);
                    //已经超过最后的预约时间
                    if (now.after(orderDate)) {
                        infoResp.setTimeOutYn("1");
                    } else {
                        infoResp.setTimeOutYn("0");
                    }
                    //到达时间早于起飞时间，即到达已是第二天，毫秒值加上一天的时间
                    if (sdf2.parse(infoResp.getPlanArrTime()).before(sdf2.parse(infoResp.getTakeOffTime()))) {
                        infoResp.setFlightTime(sdf2.parse(infoResp.getPlanArrTime()).getTime() - sdf2.parse(infoResp.getTakeOffTime()).getTime() + (60 * 60 * 24 * 1000));
                        //到达时间加上一天
                        calendar = new GregorianCalendar();
                        calendar.setTime(sdf2.parse(infoResp.getPlanArrTime()));
                        calendar.add(Calendar.DATE, 1);
                        infoResp.setPlanArrTime(sdf2.format(calendar.getTime()));
                    } else {
                        infoResp.setFlightTime(sdf2.parse(infoResp.getPlanArrTime()).getTime() - sdf2.parse(infoResp.getTakeOffTime()).getTime());
                    }
                    newData.add(infoResp);
                }
                //将相同的航班分组
                Map<String, List<IndividualizationApplyInfoResp>> sameFlightApply = newData.stream().collect(Collectors.groupingBy(applyInfo -> applyInfo.getFlightNo() + applyInfo.getFlightDate() + applyInfo.getDepartureStation() + applyInfo.getDealStatus() + applyInfo.getPassName() + applyInfo.getBookId()));
                //响应数据
                ArrayList<IdvmMealApplyListResp> respData = new ArrayList<>();
                for (List<IndividualizationApplyInfoResp> apply : sameFlightApply.values()) {
                    IdvmMealApplyListResp mealApplyListResp = new IdvmMealApplyListResp();
                    ArrayList<MealInfo> mealInfos = new ArrayList<>();
                    for (IndividualizationApplyInfoResp applyInfoResp : apply) {
                        //餐食相关数据
                        MealInfo mealInfo = new MealInfo();
                        mealInfo.setDealStatus(applyInfoResp.getDealStatus());
                        mealInfo.setId(applyInfoResp.getId());
                        mealInfo.setIdvmMealCode(applyInfoResp.getIdvmMealCode());
                        mealInfo.setIdvmMealName(applyInfoResp.getIdvmMealName());
                        mealInfo.setIdvmMealImage(applyInfoResp.getIdvmMealImage());
                        mealInfo.setMealCategory(applyInfoResp.getMealCategory());
                        mealInfos.add(mealInfo);
                    }
                    //将非餐食信息设置到响应数据中
                    copyProperties(apply.get(0), mealApplyListResp);
                    mealApplyListResp.setMealInfos(mealInfos);
                    //得到机场信息
                    //出发机场相关信息
                    AirPortInfoDto depAirPortInfo = localCacheService.getLocalAirport(mealApplyListResp.getDepartureStation(), mealApplyListResp.getDepDate());
                    //到达机场相关信息
                    AirPortInfoDto arrAirPortInfo = localCacheService.getLocalAirport(mealApplyListResp.getArrivalStation(), mealApplyListResp.getDepDate());
                    //设置出发机场名称
                    mealApplyListResp.setDepartureStationName(depAirPortInfo.getAirPortName());
                    //设置出发城市名
                    mealApplyListResp.setDepCityName(depAirPortInfo.getCityName());
                    //设置到达机场名称
                    mealApplyListResp.setArrivalStationName(arrAirPortInfo.getAirPortName());
                    //设置到达城市名
                    mealApplyListResp.setArrCityName(arrAirPortInfo.getCityName());
                    //查询航站楼信息
                    FlightInfo flightInfo = new FlightInfo();
                    flightInfo.setFlightDate(mealApplyListResp.getFlightDate());
                    flightInfo.setDepAirport(mealApplyListResp.getDepartureStation());
                    flightInfo.setArrAirport(mealApplyListResp.getArrivalStation());
                    flightInfo.setFlightNo(mealApplyListResp.getFlightNo());
                    List<FlightInfo> flightInfoList = basicService.queryFlightInfo(flightInfo);
                    if (CollectionUtils.isNotEmpty(flightInfoList)) {
                        //设置出发航站楼
                        mealApplyListResp.setDepAirportTerminal(flightInfoList.get(0).getDepAirportTerminal());
                        //设置到达机场航站楼
                        mealApplyListResp.setArrAirportTerminal(flightInfoList.get(0).getArrAirportTerminal());
                        //处理机型转换
                        Map<String, AircraftModel> stringAircraftModelMap = FlightUtil.toAircraftModelMap(handConfig.getAircraftModel());
                        if (stringAircraftModelMap != null) {
                            mealApplyListResp.setAcType(stringAircraftModelMap.get(flightInfoList.get(0).getPlanType()).getRemark());
                        }
                    }
                    //周几
                    String weekStr = DateUtils.getWeekStr(DateUtils.toDate(mealApplyListResp.getFlightDate()));
                    mealApplyListResp.setWeakDay(weekStr);
                    //出发到达时分
                    mealApplyListResp.setDepHour(sdf.format(sdf2.parse(mealApplyListResp.getTakeOffTime())));
                    mealApplyListResp.setArrHour(sdf.format(sdf2.parse(mealApplyListResp.getPlanArrTime())));
                    //舱位名称
                    mealApplyListResp.setCabinName(getCabinName(mealApplyListResp.getCabinType()));
                    //乘客姓名
                    mealApplyListResp.setPassengerName(mealApplyListResp.getPassName());
                    //是否已起飞
                    String takeOffFlag = new Date().before(sdf2.parse(mealApplyListResp.getTakeOffTime())) ? "0" : "1";
                    mealApplyListResp.setHaTakenOff(takeOffFlag);
                    //航班日期
                    mealApplyListResp.setDepDate(mealApplyListResp.getFlightDate());
                    //機型描述
                    mealApplyListResp.setPlanType(mealApplyListResp.getAcType());
                    //證件號
                    redisService.putData(RedisKeyConfig.createUpIdInfo(mealApplyListResp.getTicketNo()),mealApplyListResp.getPassIdcard(),10*60L);
                    mealApplyListResp.setIdNo(SensitiveInfoHider.hideMiddleSensitiveInfo(mealApplyListResp.getPassIdcard()));
                    //证件类型
                    Pattern patternOne = Pattern.compile(PatternCommon.ID_NUMBER);
                    Matcher matcherOne = patternOne.matcher(mealApplyListResp.getPassIdcard());
                    if (matcherOne.matches()) {
                        mealApplyListResp.setIdTypeName("身份证");
                    } else {
                        mealApplyListResp.setIdTypeName("证件号");
                    }
                    mealApplyListResp.setDepAirportCode(mealApplyListResp.getDepartureStation());
                    mealApplyListResp.setArrAirportCode(mealApplyListResp.getArrivalStation());
                    mealApplyListResp.setDepAirPortName(mealApplyListResp.getDepartureStationName());
                    mealApplyListResp.setArrAirPortName(mealApplyListResp.getArrivalStationName());
                    mealApplyListResp.setCabin(mealApplyListResp.getCabinType());
                    respData.add(mealApplyListResp);
                }
                //排序
                respData.sort(Comparator.comparing(IdvmMealApplyListResp::getTakeOffTime));
                ArrayList<IdvmMealApplyListResp> respDataNew = new ArrayList<>();
                //已取消的餐食申请
                List<IdvmMealApplyListResp> cancelApply = respData.stream().filter(apply -> CANCEL_STATE.equals(apply.getMealInfos().get(0).getDealStatus())).collect(Collectors.toList());
                //成功航班未到起飞的申请
                List<IdvmMealApplyListResp> successApply = respData.stream().filter(apply -> !CANCEL_STATE.equals(apply.getMealInfos().get(0).getDealStatus())).filter(apply -> "0".equals(apply.getHaTakenOff())).collect(Collectors.toList());
                //成功航班已起飞的申请
                List<IdvmMealApplyListResp> outTimeApply = respData.stream().filter(apply -> !CANCEL_STATE.equals(apply.getMealInfos().get(0).getDealStatus())).filter(apply -> "1".equals(apply.getHaTakenOff())).collect(Collectors.toList());
                respDataNew.addAll(successApply);
                respDataNew.addAll(cancelApply);
                respDataNew.addAll(outTimeApply);
                resp.setObjData(respDataNew);
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            } catch (ParseException e) {
                log.error(LOG_ERR, e);
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("未查询到申请记录");
                return resp;
            }
        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("未查询到申请记录");
        }
        return resp;
    }


    @ApiOperation("查询首页轮播图列表")
    @RequestMapping(value = "queryImgList", method = RequestMethod.POST)
    public BaseResp<List<MealImage>> queryImgList(@RequestBody BaseReq baseReq) {
        BaseResp<List<MealImage>> resp = new BaseResp<>();
        //校验请求
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<BaseReq>> violations = validator.validate(baseReq);
        if (CollectionUtils.isNotEmpty(violations)) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo(violations.iterator().next().getMessage());
            return resp;
        }
        //读取餐食图片信息列表
        List<MealImage> mealImageList = handConfig.getMealImageList();
        resp.setObjData(mealImageList);
        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
        return resp;
    }

    @InterfaceLog
    @ApiOperation("更改时查询航班信息")
    @RequestMapping(value = "queryUpdateFlight", method = RequestMethod.POST)
    public BaseResp<IdvmMealApplyListResp> queryUpdateFlight(@RequestBody BaseReq<IdvmMealApplyListResp> baseReq, HttpServletRequest httpServletRequest) {
        String clientIp = this.getClientIP(httpServletRequest);
        //响应
        BaseResp<IdvmMealApplyListResp> resp = new BaseResp<>();
        //请求
        IdvmMealApplyListResp reqData = baseReq.getRequest();
        //查询
        //调用统一订单查询航班信息
        TicketInfoRequest ticketInfoRequest = new TicketInfoRequest(HandlerConstants.VERSION, baseReq.getChannelCode(), getChannelInfo(baseReq.getChannelCode(), "10"));
        ticketInfoRequest.setCertType("TN");
        ticketInfoRequest.setTicketNo(reqData.getTicketNo());
        ticketInfoRequest.setPassengerName(reqData.getPassengerName());
        Map<String, String> headMap = HttpUtil.getHeaderMap(clientIp, "");
        TicketListInfoResponse ticketListInfoResponse = orderManage.getTicketInfo(ticketInfoRequest, headMap);
        List<PtIBETicketInfo> ibeTicketInfoList = ticketListInfoResponse.getIBETicketInfoList();
        PtIBETicketInfo ibeTicketInfo = ibeTicketInfoList.get(0);
        //乘客类型
        switch (ibeTicketInfo.getPassengerType()) {
            case "PASSENGER_ADULT":
                reqData.setPassengerType("0");
                break;
            case "PASSENGER_CHILD":
                reqData.setPassengerType("1");
                break;
            case "PASSENGER_INFANT":
                reqData.setPassengerType("2");
                break;
            case "PASSENGER_CHILD_UNACCOMPANIED":
                reqData.setPassengerType("1");
                break;
            default:
                reqData.setPassengerType("0");
                break;
        }
        resp.setObjData(reqData);
        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
        return resp;
    }

    /**
     * 调用旅客服务网查询可选餐食数据
     */
    private MealQueryResp getMealQueryResp(IndividualizationSetMealReq reqData) {
        //构建查询条件
        JSONObject jsonObject = new JSONObject();
        JSONObject postJson = new JSONObject();
        //航班号
        jsonObject.put(FLIGHT_NO, reqData.getFlightNo());
        //航班日期
        jsonObject.put(FLIGHT_DATE, reqData.getFlightDate());
        //舱位
        jsonObject.put(CABIN_TYPE, CommonUtil.getCabinClassByCabinCode(reqData.getCabinType(), handConfig.getCabinClass()));
        //始发航站
        jsonObject.put(DEPARTURE_STATION, reqData.getDepartureStation());
        //b2c
        jsonObject.put(REQUEST_RESOURES, "B2C");
        postJson.put(DATA, jsonObject);
        postJson.put(MEMBER_ID, reqData.getFfpCardNo());
        postJson.put(INFT_CODE, "1111");
        //调用旅客服务网查询数据
        String passagerResp = this.passagerPost(postJson, HandlerConstants.URL_PASSAGER_API + "/passenger/idvmProvideMeal/selectSpecialMealNew");
        //解析数据
        return (MealQueryResp) JsonMapper.buildNonNullMapper().fromJson(passagerResp, MealQueryResp.class);
    }

    /**
     * 去掉字符串的第一个"-"
     */
    private String getReplace(String s) {
        if (StringUtils.isBlank(s)) {
            return null;
        }
        return s.replace("-", "");
    }

    /**
     * 通过票号/证件号查询航班具体信息，结果已将经停航线分段（统一订单）
     */
    private List<IndividualizationFlightInfoResp> getFlightInfoNew(HttpServletRequest httpServletRequest, BaseReq<IndividualizationSetMealReq> baseReq) {
        //得到请求地址
        String ip = this.getClientIP(httpServletRequest);
        IndividualizationSetMealReq reqData = baseReq.getRequest();
        //返回数据
        List<IndividualizationFlightInfoResp> mList = null;
        //身份证校验
        Pattern patternOne = Pattern.compile(PatternCommon.ID_NUMBER);
        Matcher matcherOne = patternOne.matcher(reqData.getTicketNo());
        //票号校验
        Pattern patternTwo = Pattern.compile(PatternCommon.TICKET_NO);
        Matcher matcherTwo = patternTwo.matcher(reqData.getTicketNo());
        //护照校验
        Pattern patternThree = Pattern.compile(PatternCommon.PASSPORT_NO);
        Matcher matcherThree = patternThree.matcher(reqData.getTicketNo());
        //调用统一订单查询航班信息
        TicketInfoRequest ticketInfoRequest = new TicketInfoRequest(HandlerConstants.VERSION, baseReq.getChannelCode(), getChannelInfo(baseReq.getChannelCode(), "10"));
        //输入的是身份证
     /*   if (matcherOne.matches()){
            ticketInfoRequest.setCertType(CertificateTypeEnum.ID_CARD.getShowCode());
            ticketInfoRequest.setCertNo(reqData.getTicketNo());
            throw new CommonException(WSEnum.NOT_SUPPORT.getResultCode(), WSEnum.NOT_SUPPORT.getResultInfo());
        }*/
        //输入的是护照
     /*   if (matcherThree.matches()){
            ticketInfoRequest.setCertType(CertificateTypeEnum.PASSPORT.getShowCode());
            ticketInfoRequest.setCertNo(reqData.getTicketNo());
            throw new CommonException(WSEnum.NOT_SUPPORT.getResultCode(), WSEnum.NOT_SUPPORT.getResultInfo());
        }*/
        //输入的是票号
        if (matcherTwo.matches()) {
            ticketInfoRequest.setCertType("TN");
            ticketInfoRequest.setTicketNo(reqData.getTicketNo());
        } else {
            throw new CommonException(WSEnum.NOT_SUPPORT.getResultCode(), WSEnum.NOT_SUPPORT.getResultInfo());
        }
        ticketInfoRequest.setPassengerName(reqData.getPassName());
        Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
        TicketListInfoResponse ticketListInfoResponse = orderManage.getTicketInfo(ticketInfoRequest, headMap);
        List<PtIBETicketInfo> ibeTicketInfoList = ticketListInfoResponse.getIBETicketInfoList();
        //得到机场信息
        //值机选座接口返回的数据，可能包含中转航线
        List<IndividualizationFlightInfoResp> list = new ArrayList<>();
        for (PtIBETicketInfo ibeTicketInfo : ibeTicketInfoList) {
            for (PtSegmentInfo segmentInfo : ibeTicketInfo.getSegmentInfoList()) {
                //设置城市代码
                String depAirportCode = segmentInfo.getDepAirportCode();
                String arrAirportCode = segmentInfo.getArrAirportCode();
                AirPortInfoDto depAirportInfo = localCacheService.getLocalAirport(depAirportCode, segmentInfo.getDepDate());
                AirPortInfoDto arrAirportInfo = localCacheService.getLocalAirport(arrAirportCode, segmentInfo.getDepDate());
                segmentInfo.setDepCity(depAirportInfo.getCityCode());
                segmentInfo.setArrCity(arrAirportInfo.getCityCode());
                //设置航班日期
                segmentInfo.setDepTime(segmentInfo.getDepTime() + ":00");
                segmentInfo.setDepDate(segmentInfo.getDepTime().substring(0, 10));
                IndividualizationFlightInfoResp mealResp = getIndividualizationFlightInfoResp(ibeTicketInfo, segmentInfo);
                //校验票号姓名
                String passName = reqData.getPassName().toUpperCase();
                //正则表达式
                String patternStr = passName + "(\\s*(CHD)?\\s*(\\(CHILD\\))?\\s*|\\s*(MR)?\\s*|\\s*(MS)?\\s*|\\s*(INF.+)?\\s*)";
                Pattern pattern = Pattern.compile(patternStr);
                Matcher matcher = pattern.matcher(ibeTicketInfo.getPassengerName());
                if (matcher.matches() || passName.equalsIgnoreCase(ibeTicketInfo.getPassengerName())) {
                    list.add(mealResp);
                }
            }
        }
        List<IndividualizationFlightInfoResp> nList = getIndividualizationFlightInfoResps(baseReq, ip, list);
        if (CollectionUtils.isEmpty(nList)) {
            mList = list;
        } else {
            mList = nList;
        }
        return mList;
    }

    private IndividualizationFlightInfoResp getIndividualizationFlightInfoResp(PtIBETicketInfo ibeTicketInfo, PtSegmentInfo segmentInfo) {
        IndividualizationFlightInfoResp mealResp = new IndividualizationFlightInfoResp();
        mealResp.setTicketNo(ibeTicketInfo.getTicketNo());
        mealResp.setPassengerName(ibeTicketInfo.getPassengerName());
        //乘客类型
        switch (ibeTicketInfo.getPassengerType()) {
            case "PASSENGER_ADULT":
                mealResp.setPassengerType("0");
                break;
            case "PASSENGER_CHILD":
                mealResp.setPassengerType("1");
                break;
            case "PASSENGER_INFANT":
                mealResp.setPassengerType("2");
                break;
            case "PASSENGER_CHILD_UNACCOMPANIED":
                mealResp.setPassengerType("1");
                break;
            default:
                mealResp.setPassengerType("0");
                break;
        }
        mealResp.setDepAirportCode(segmentInfo.getDepAirportCode());
        mealResp.setArrAirportCode(segmentInfo.getArrAirportCode());
        mealResp.setDepCity(segmentInfo.getDepCity());
        mealResp.setArrCity(segmentInfo.getArrCity());
        mealResp.setDepDate(segmentInfo.getDepDate());
        mealResp.setDepTime(segmentInfo.getDepTime());
        mealResp.setFlightNo(segmentInfo.getFlightNo());
        mealResp.setCabin(segmentInfo.getCabin());
        mealResp.setTicketStatus(segmentInfo.getTicketStatus());
        mealResp.setDepAirportTerminal(segmentInfo.getDepAirportTerminal());
        mealResp.setArrAirportTerminal(segmentInfo.getArrAirportTerminal());
        mealResp.setSegmentStatus(segmentInfo.getSegmentStatus());
        mealResp.setAirLine(segmentInfo.getAirline());
        redisService.putData(RedisKeyConfig.createUpIdInfo(ibeTicketInfo.getTicketNo()),ibeTicketInfo.getIdentityInfoList().get(0).getIdNo(),10*60L);
        mealResp.setIdNo(SensitiveInfoHider.hideMiddleSensitiveInfo(ibeTicketInfo.getIdentityInfoList().get(0).getIdNo()));
        mealResp.setIdType(ibeTicketInfo.getIdentityInfoList().get(0).getIdType());
        return mealResp;
    }

    /**
     * 将中转航段分段
     *
     * @param baseReq
     * @param ip
     * @param list
     * @return
     */
    private List<IndividualizationFlightInfoResp> getIndividualizationFlightInfoResps(BaseReq<IndividualizationSetMealReq> baseReq, String ip, List<IndividualizationFlightInfoResp> list) {
        //得到机场信息
        List<IndividualizationFlightInfoResp> nList = new ArrayList<>();
        //对list进行处理,如果是经停航班，将list里的航段拆为两段ab,bc
        list.stream().filter(l -> l.getTicketStatus().equals("CHECKED IN") || l.getTicketStatus().equals("OPEN FOR USE")).forEach(l -> {
            //出发机场信息
            AirPortInfoDto depAirportInfo = localCacheService.getLocalAirport(l.getDepAirportCode(), l.getDepDate());
            //到达机场信息
            AirPortInfoDto arrAirportInfo = localCacheService.getLocalAirport(l.getArrAirportCode(), l.getDepDate());
            //到达城市名称
            l.setArrCityName(arrAirportInfo.getCityName());
            //出发城市名称
            l.setDepCityName(depAirportInfo.getCityName());
            //证件类型
            if ("NI".equals(l.getIdType())) {
                l.setIdTypeName("身份证");
            } else {
                l.setIdTypeName("证件号");
            }
            JSONObject json = new JSONObject();
            JSONObject jsonObject = new JSONObject();
            //航班号
            jsonObject.put(FLIGHT_NO, l.getFlightNo());
            //始发时间
            jsonObject.put(FLIGHT_DATE, l.getDepTime().substring(0, 10));
            //ip
            json.put("ip", ip);
            //版本
            json.put("version", HandlerConstants.NEW_CHECKIN_SEAT_VERSION);
            //终端
            json.put(CHANNEL_CODE, baseReq.getChannelCode());
            json.put("request", jsonObject);

            //调用基本航班信息接口查询航班具体信息，以此判断其是否为经停航班
            HttpResult cityResult = this.doPostClient(json, HandlerConstants.BASIC_INFO_URL + HandlerConstants.BASIC_INFO_FLIGHTINFO, null, 80000, 80000);
            //结果合法
            if (cityResult.isResult() && !StringUtil.isNullOrEmpty(cityResult.getResponse())) {
                //得到航线响应对象
                FlightLineResp flightLineResp = (FlightLineResp) JsonUtil.jsonToBean(cityResult.getResponse(), FlightLineResp.class);
                //响应正常
                if ("10001".equals(flightLineResp.getResultCode())) {
                    //当前航班具体信息集合
                    List<FlightLineResp.FlightInfoDTO> infoDTOList = flightLineResp.getResult();

                    //如果集合size大于一，说明当前航班号当天有多条航线
                    if (infoDTOList.size() > 1) {
                        //构建航班信息原子类
                        AtomicReference<FlightLineResp.FlightInfoDTO> flightInfoDTO = new AtomicReference<>();

                        infoDTOList.forEach(i -> {
                            //如果当前航线匹配
                            if (i.getDepAirport().equals(l.getDepAirportCode()) && i.getArrAirport().equals(l.getArrAirportCode())) {
                                flightInfoDTO.set(i);
                            }
                        });

                        //如果为经停航班
                        if ("Y".equalsIgnoreCase(flightInfoDTO.get().getIsStop())) {
                            //将两段航线信息添加进响应集合
                            SimpleDateFormat sdf2 = new SimpleDateFormat(DateUtils.YYYY_MM_DD_HH_MM_PATTERN);
                            infoDTOList.forEach(i -> {
                                IndividualizationFlightInfoResp flightInfoResp = new IndividualizationFlightInfoResp();
                                copyProperties(l, flightInfoResp);
                                if (i.getDepAirport().equals(flightInfoResp.getDepAirportCode()) && !i.getArrAirport().equals(flightInfoResp.getArrAirportCode())) {
                                    //ab段
                                    AirPortInfoDto airPortInfo = localCacheService.getLocalAirport(i.getArrAirport(), i.getFlightDate());
                                    //到达城市名
                                    flightInfoResp.setArrCityName(airPortInfo.getCityName());
                                    flightInfoResp.setArrCity(airPortInfo.getCityCode());
                                    //到达机场航站楼
                                    flightInfoResp.setArrAirportTerminal(flightInfoDTO.get().getStopAirportTerminal());
                                    flightInfoResp.setArrAirportCode(airPortInfo.getAirPortCode());
                                    flightInfoResp.setArrAirPortName(airPortInfo.getAirPortName());
                                    flightInfoResp.setArrayTime(i.getFlightArrDate() + " " + i.getArrDateTime().substring(0, 2) + ":" + i.getArrDateTime().substring(2) + ":00");
                                    try {
                                        flightInfoResp.setFlightTime(sdf2.parse(flightInfoResp.getArrayTime()).getTime() - sdf2.parse(flightInfoResp.getDepTime()).getTime());
                                    } catch (ParseException e) {
                                        log.error(LOG_ERR, e);
                                    }
                                    nList.add(flightInfoResp);
                                }
                                if (i.getArrAirport().equals(flightInfoResp.getArrAirportCode()) && !i.getDepAirport().equals(flightInfoResp.getDepAirportCode())) {
                                    //bc段
                                    AirPortInfoDto airPortInfo = localCacheService.getLocalAirport(i.getDepAirport(), i.getFlightDate());
                                    AirPortInfoDto arrAirPortInfo = localCacheService.getLocalAirport(i.getArrAirport(), i.getFlightDate());
                                    //出发城市名
                                    flightInfoResp.setDepCityName(airPortInfo.getCityName());
                                    //到达城市名
                                    flightInfoResp.setArrCityName(arrAirPortInfo.getCityName());
                                    flightInfoResp.setDepCity(airPortInfo.getCityCode());
                                    flightInfoResp.setDepAirportTerminal(flightInfoDTO.get().getStopAirportTerminal());
                                    flightInfoResp.setDepAirportCode(airPortInfo.getAirPortCode());
                                    flightInfoResp.setDepAirPortName(airPortInfo.getAirPortName());
                                    flightInfoResp.setDepTime(i.getFlightDate() + " " + i.getDepDateTime().substring(0, 2) + ":" + i.getDepDateTime().substring(2) + ":00");
                                    flightInfoResp.setArrayTime(i.getFlightArrDate() + " " + i.getArrDateTime().substring(0, 2) + ":" + i.getArrDateTime().substring(2) + ":00");
                                    flightInfoResp.setArrAirportTerminal(i.getArrAirportTerminal());
                                    flightInfoResp.setArrAirportCode(i.getArrAirport());
                                    flightInfoResp.setArrAirPortName(arrAirPortInfo.getAirPortName());
                                    try {
                                        flightInfoResp.setFlightTime(sdf2.parse(flightInfoResp.getArrayTime()).getTime() - sdf2.parse(flightInfoResp.getDepTime()).getTime());
                                    } catch (ParseException e) {
                                        log.error(LOG_ERR, e);
                                    }
                                    nList.add(flightInfoResp);
                                }
                            });
                        }
                        //当前航班号当天只有一条航线
                    } else {
                        nList.add(l);
                    }
                }
            }
        });
        return nList;
    }

    /**
     * 根据航班信息判断是否超过截止日期
     */
    private Boolean isBeforeDeadline(FlightInfo flightInfo) {
        List<FlightInfo> flightInfos = basicService.queryFlightInfo(flightInfo);
        if (CollectionUtils.isNotEmpty(flightInfos)) {
            SimpleDateFormat sdf = new SimpleDateFormat(DateUtils.YYYY_MM_DD_HH_MM_PATTERN);
            flightInfo = flightInfos.get(0);
            String depTime = flightInfo.getDepDateChinaTime();
            Date now = new Date();
            try {
                Date depDate = sdf.parse(depTime);
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(depDate);
                //截止日期
                String lastTime = calendar.get(Calendar.YEAR) + "-" + (calendar.get(Calendar.MONTH) + 1) + "-" + (calendar.get(Calendar.DAY_OF_MONTH) - 1) + " " + FOURTEEN_OCLOCK;
                Date deadline = sdf.parse(lastTime);
                //返回是否超过截止日期
                return now.before(deadline);
            } catch (ParseException e) {
                log.error(LOG_ERR, e);
            }
        }
        return false;
    }

    /**
     * 得到舱位名称
     */
    private String getCabinName(String cabinCode) {
        String cabinCode1 = CommonUtil.getCabinClassByCabinCode(cabinCode, handConfig.getCabinClass());
        return CommonUtil.showCabinClassName(cabinCode1);
    }

    private static final String MOBILE = "MOBILE";

    private static final String FLIGHT_NO = "flightNo";

    private static final String FLIGHT_DATE = "flightDate";

    private static final String FOURTEEN_OCLOCK = "14:00:00";

    private static final String ARRIVAL_STATION = "arrivalStation";

    private static final String DEPARTURE_STATION = "departureStation";

    private static final String MEMBER_ID = "memberId";

    private static final String INFT_CODE = "inftCode";

    private static final String IDVM_MEAL_NAME = "idvmMealName";

    private static final String FFP_ID = "ffpid";

    private static final String CHANNEL_CODE = "channelCode";

    private static final String REQUEST_RESOURES = "resoures";

    private static final String CABIN_TYPE = "cabinType";

    private static final String DATA = "data";

    private static final String ECON_CABIN = "经济舱";
    private static final String BUSS_CABIN = "公务舱";
    private static final String LOG_ERR = "【个性化餐食】系统异常{}";
    private static final String TICKET_NO = "ticketNo";
    private static final String CANCEL_STATE = "2";
    private static final String TICKETNO = "TicketNo";
    private static final String VERSION = "Version";
    private static final String USERNO = "UserNo";
    private static final String CERTNO = "CertNo";
    private static final String CERTTYPE = "CertType";
}

package com.juneyaoair.mobile.handler.controller.service.impl;

import com.alibaba.fastjson.JSON;
import com.juneyaoair.appenum.WSEnum;
import com.juneyaoair.appenum.order.MerchantPaymentEnum;
import com.juneyaoair.baseclass.common.response.BaseResp;
import com.juneyaoair.baseclass.response.payment.paysetting.Bank;
import com.juneyaoair.exception.CommonException;
import com.juneyaoair.exception.ServiceException;
import com.juneyaoair.mobile.handler.HandlerConstants;
import com.juneyaoair.mobile.handler.config.bean.HandConfig;
import com.juneyaoair.mobile.handler.controller.service.IPaymentService;
import com.juneyaoair.pattern.PatternCommon;
import com.juneyaoair.utils.http.HttpResult;
import com.juneyaoair.utils.http.HttpUtil;
import com.juneyaoair.utils.json.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2019/7/22  19:25.
 */
@Service("paymentService")
@Slf4j
public class PaymentServiceImpl implements IPaymentService {
    @Autowired
    private HandConfig handConfig;
    @Override
    public BaseResp doPay(Map<String, String> map) {
        BaseResp baseResp = new BaseResp();
        try {
            HttpResult httpResult = HttpUtil.doPayPost(HandlerConstants.URL_PAY, map);
            if (httpResult.isResult()) {
                if (JsonUtil.isGoodJson(httpResult.getResponse())) {
                    Map<String, String> payResult = (Map<String, String>) JsonUtil.jsonToMap(httpResult.getResponse());
                    baseResp.setResultCode(WSEnum.SUCCESS.getResultCode());
                    baseResp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                    baseResp.setObjData(payResult);
                } else {
                    Pattern pattern = Pattern.compile(PatternCommon.HTML, Pattern.MULTILINE);
                    Matcher matcher = pattern.matcher(httpResult.getResponse());
                    if (matcher.matches()) {
                        baseResp.setResultCode(WSEnum.SUCCESS.getResultCode());
                        baseResp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                        Map<String, String> payResult = new HashMap<>();
                        payResult.put("isHtml", "Y");
                        payResult.put("html", httpResult.getResponse());
                        baseResp.setObjData(payResult);
                    } else {
                        baseResp.setResultCode(WSEnum.ERROR.getResultCode());
                        baseResp.setResultInfo("未知的文档格式！");
                    }
                }
            } else {
                baseResp.setResultCode(WSEnum.ERROR.getResultCode());
                baseResp.setResultInfo("支付网络异常！");
            }
        } catch (Exception e) {
            String req = JsonUtil.objectToJson(map);
            log.error("[支付发生异常错误]：{},异常请求参数：{}", e, req);
            baseResp.setResultCode(WSEnum.ERROR.getResultCode());
            baseResp.setResultInfo("支付发生异常！");
        }
        return baseResp;
    }

    @Override
    public BaseResp doPay4Mini(Map<String, String> map) {
        BaseResp baseResp = new BaseResp();
        try {
            HttpResult httpResult = HttpUtil.doPayPost(HandlerConstants.URL_PAY, map);
            if (httpResult.isResult()) {
                if (JsonUtil.isGoodJson(httpResult.getResponse())) {
                    Map<String, Object> payResult = (Map<String, Object>) JsonUtil.jsonToMap(httpResult.getResponse());
                    if (StringUtils.isNotEmpty((String)payResult.get("resultData"))) {
                        payResult.put("resultData", JSON.parseObject(StringEscapeUtils.unescapeJava((String) payResult.get("resultData"))));
                    }
                    baseResp.setResultCode(WSEnum.SUCCESS.getResultCode());
                    baseResp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                    baseResp.setObjData(payResult);
                } else {
                    Pattern pattern = Pattern.compile(PatternCommon.HTML, Pattern.MULTILINE);
                    Matcher matcher = pattern.matcher(httpResult.getResponse());
                    if (matcher.matches()) {
                        baseResp.setResultCode(WSEnum.SUCCESS.getResultCode());
                        baseResp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                        Map<String, String> payResult = new HashMap<>();
                        payResult.put("isHtml", "Y");
                        payResult.put("html", httpResult.getResponse());
                        baseResp.setObjData(payResult);
                    } else {
                        baseResp.setResultCode(WSEnum.ERROR.getResultCode());
                        baseResp.setResultInfo("未知的文档格式！");
                    }
                }
            } else {
                baseResp.setResultCode(WSEnum.ERROR.getResultCode());
                baseResp.setResultInfo("支付网络异常！");
            }
        } catch (Exception e) {
            String req = JsonUtil.objectToJson(map);
            log.error("[支付发生异常错误]：{},异常请求参数：{}", e, req);
            baseResp.setResultCode(WSEnum.ERROR.getResultCode());
            baseResp.setResultInfo("支付发生异常！");
        }
        return baseResp;
    }

    @Override
    public BaseResp doAsyncPay(Map<String, String> map) {
        BaseResp baseResp = new BaseResp();
        try {
            HttpResult httpResult = HttpUtil.doPayPost(HandlerConstants.URL_ASYNC_PAY, map);
            if (httpResult.isResult()) {
                Pattern pattern = Pattern.compile(PatternCommon.HTML, Pattern.MULTILINE);
                Matcher matcher = pattern.matcher(httpResult.getResponse());
                if (matcher.matches()) {
                    baseResp.setResultCode(WSEnum.SUCCESS.getResultCode());
                    baseResp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                    Map<String, String> payResult = new HashMap<>();
                    payResult.put("isHtml", "Y");
                    payResult.put("html", httpResult.getResponse());
                    baseResp.setObjData(payResult);
                } else if (JsonUtil.isGoodJson(httpResult.getResponse())) {
                    Map<String, String> payResult = (Map<String, String>) JsonUtil.jsonToMap(httpResult.getResponse());
                    baseResp.setResultCode(WSEnum.SUCCESS.getResultCode());
                    baseResp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                    baseResp.setObjData(payResult);
                } else {
                    baseResp.setResultCode(WSEnum.ERROR.getResultCode());
                    baseResp.setResultInfo("未知的文档格式！");
                }
            } else {
                baseResp.setResultCode(WSEnum.ERROR.getResultCode());
                baseResp.setResultInfo("支付网络异常！");
            }
        } catch (Exception e) {
            String req = JsonUtil.objectToJson(map);
            log.error("[支付发生异常错误]：{},异常请求参数：{}", e, req);
            baseResp.setResultCode(WSEnum.ERROR.getResultCode());
            baseResp.setResultInfo("支付发生异常！");
        }
        return baseResp;
    }

    @Override
    public List<Bank> queryBankList(String merchantPayment, String channelCode, String orderType) {
        if(StringUtils.isBlank(merchantPayment)){
            merchantPayment = MerchantPaymentEnum.JX.name();
        }
        //检验商户类型配置
        MerchantPaymentEnum merchantPaymentEnum = MerchantPaymentEnum.checkEnum(merchantPayment);
        Map<String,List<Bank>> channelBankMap;
        if(merchantPaymentEnum.name().equals(MerchantPaymentEnum.JX.name())){
            channelBankMap = handConfig.getPayConfigsV2();
        }else if(merchantPaymentEnum.name().equals(MerchantPaymentEnum.JN.name())){
            channelBankMap = handConfig.getPayJnConfigsV2();
        }else{
            log.info("{}暂无对应的商户支付配置",merchantPayment);
            throw new ServiceException("暂无对应的商户支付配置");
        }
        //检查商户配置
        if(channelBankMap == null || channelBankMap.isEmpty()){
            log.info("{}暂无对应的支付配置",merchantPayment);
            throw new ServiceException("暂无对应的支付配置");
        }
        //检查支付渠道配置
        List<Bank> bankList = channelBankMap.get(channelCode);
        if(CollectionUtils.isEmpty(bankList)){
            log.info("{}暂无对应的渠道配置",channelCode);
            throw new ServiceException("暂无对应的渠道配置");
        }
        //检查订单类型符合条件
        List<Bank> newBankList = bankList.stream().filter(bank -> CollectionUtils.isNotEmpty(bank.getSuitOrderType()) && bank.getSuitOrderType().contains(orderType)).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(newBankList)){
            log.info("{}暂无对应的订单渠道配置",orderType);
            throw new ServiceException("暂无对应的订单渠道配置");
        }
        return newBankList;
    }
}

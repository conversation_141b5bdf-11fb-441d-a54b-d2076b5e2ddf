package com.juneyaoair.mobile.handler.controller.service;

import com.juneyaoair.baseclass.common.response.BaseResp;
import com.juneyaoair.baseclass.response.payment.paysetting.Bank;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 第三方支付服务
 * @date 2019/7/22  18:39.
 */
public interface IPaymentService {
    /**
     * 同步支付方式
     * @param map
     * @return
     */
    BaseResp doPay(Map<String, String> map);

    /**
     * 微信小程序同步支付方式
     * @param map
     * @return
     */
    BaseResp doPay4Mini(Map<String, String> map);

    /**
     * 异步支付方式
     * @param map
     * @return
     */
    BaseResp doAsyncPay(Map<String, String> map);

    /**
     * 查询支付方式渲染
     * @param merchantPayment  商户
     * @param channelCode 渠道
     * @see com.juneyaoair.appenum.ChannelCodeEnum
     * @param orderType 订单类型
     * @see com.juneyaoair.appenum.order.OrderPayEnumType
     * @return
     */
    List<Bank> queryBankList(String merchantPayment,String channelCode,String orderType);
}

package com.juneyaoair.mobile.handler.controller;

import com.juneyaoair.appenum.WSEnum;
import com.juneyaoair.appenum.member.MemberDetailRequestItemsEnum;
import com.juneyaoair.baseclass.basicsys.response.AirPortInfoDto;
import com.juneyaoair.baseclass.common.request.BaseReq;
import com.juneyaoair.baseclass.common.request.BasicBaseReq;
import com.juneyaoair.baseclass.common.response.BaseResp;
import com.juneyaoair.baseclass.member.response.MytripInfoResponse;
import com.juneyaoair.baseclass.request.av.AircraftModel;
import com.juneyaoair.baseclass.request.flightDynamic.*;
import com.juneyaoair.baseclass.response.flightdynamic.*;
import com.juneyaoair.crm.IMemberService;
import com.juneyaoair.exception.RequestParamErrorException;
import com.juneyaoair.mobile.aop.annotation.InterfaceLog;
import com.juneyaoair.mobile.handler.HandlerConstants;
import com.juneyaoair.mobile.handler.bean.travel.BoardInfoResultDTO;
import com.juneyaoair.mobile.handler.config.bean.HandConfig;
import com.juneyaoair.mobile.handler.controller.crm.login.util.CrmUtil;
import com.juneyaoair.mobile.handler.controller.util.CRMReqUtil;
import com.juneyaoair.mobile.handler.sdk.TravellerHttpApi;
import com.juneyaoair.mobile.handler.service.*;
import com.juneyaoair.mobile.handler.util.FlightUtil;
import com.juneyaoair.mobile.mongo.entity.FlightInfo;
import com.juneyaoair.thirdentity.bigdata.request.PtFlightStatusReq;
import com.juneyaoair.thirdentity.comm.request.PtApiCRMRequest;
import com.juneyaoair.thirdentity.comm.response.PtCRMResponse;
import com.juneyaoair.thirdentity.member.request.PtMemberDetailRequest;
import com.juneyaoair.thirdentity.member.response.MemberBasicInfoSoaModel;
import com.juneyaoair.thirdentity.member.response.PtMemberDetail;
import com.juneyaoair.utils.AESUtil;
import com.juneyaoair.utils.EncoderHandler;
import com.juneyaoair.utils.StringUtil;
import com.juneyaoair.utils.http.HttpResult;
import com.juneyaoair.utils.json.JsonMapper;
import com.juneyaoair.utils.json.JsonUtil;
import com.juneyaoair.utils.util.DateUtils;
import com.juneyaoair.utils.util.MdcUtils;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@RequestMapping("/flightDynamic")
@RestController
@Slf4j
public class NewFlightDynamicController extends BassController {
    private static final boolean TRUE = true;
    private static final boolean FALSE = false;
    @Autowired
    private IBasicService basicService;

    @Autowired
    private HandConfig handConfig;

    @Autowired
    private LocalCacheService localCacheService;

    @Autowired
    private IMemberService memberService;

    @Autowired
    private TravellerHttpApi travellerHttpApi;


    @Autowired
    private TravellerApiService travellerApiService;

    @Autowired
    private CheckInSeatService checkInSeatService;

    @RequestMapping(value = "/queryNewFlightDynamic", method = RequestMethod.POST)
    @InterfaceLog
    public BaseResp<List<NewFlightInfo>> queryNewFlightDynamic(@RequestBody BaseReq<NewFlightStatusRequest> baseReq, HttpServletRequest request) {
        NewFlightStatusRequest req = baseReq.getRequest();
        if (req.getQueryType().equals("1")) {
            String regex = "(HO)[0-9]{4}";
            if (!req.getFlightNo().toUpperCase().matches(regex)) {
                req.setFlightNo("HO" + req.getFlightNo());
            } else {
                req.setFlightNo(req.getFlightNo().toUpperCase());
            }
        }
        BaseResp<List<NewFlightInfo>> baseResp = new BaseResp<>();
        if (StringUtils.isBlank(req.getQueryType())) {
            baseResp.setResultCode(WSEnum.ERROR.getResultCode());
            baseResp.setResultInfo("请传入查询类型");
            return baseResp;
        }
        PtFlightStatusReq ptFlightStatusReq = new PtFlightStatusReq();
        if (StringUtils.isBlank(req.getFlightDate())) {
            baseResp.setResultCode(WSEnum.ERROR.getResultCode());
            baseResp.setResultInfo("航班日期不能为空");
            return baseResp;
        }
        if ("1".equals(req.getQueryType())) {
            if (StringUtils.isBlank(req.getFlightNo())) {
                baseResp.setResultCode(WSEnum.ERROR.getResultCode());
                baseResp.setResultInfo("航班号不能为空");
                return baseResp;
            }
            ptFlightStatusReq.setFlightNo(req.getFlightNo());
            ptFlightStatusReq.setDepartureAirport(req.getDepartureAirport());
            ptFlightStatusReq.setArrivalAirport(req.getArrivalAirport());
        } else if ("2".equals(req.getQueryType())) {
            if (StringUtils.isBlank(req.getDepartureCity())) {
                baseResp.setResultCode(WSEnum.ERROR.getResultCode());
                baseResp.setResultInfo("出发城市不能为空");
                return baseResp;
            }
            if (StringUtils.isBlank(req.getArrivalCity())) {
                baseResp.setResultCode(WSEnum.ERROR.getResultCode());
                baseResp.setResultInfo("到达城市不能为空");
                return baseResp;
            }
            ptFlightStatusReq.setDepartureCityCode(req.getDepartureCity());
            ptFlightStatusReq.setArrivalCityCode(req.getArrivalCity());
        } else {
            baseResp.setResultCode(WSEnum.ERROR.getResultCode());
            baseResp.setResultInfo("查询类型有误");
            return baseResp;
        }
        boolean queryAttention = false;
        if (null != (baseReq.getRequest().getFfpId())) {
            queryAttention = true;
        }
        String ip = this.getClientIP(request);
        ptFlightStatusReq.setFlightDateLocal(req.getFlightDate());
        ptFlightStatusReq.setIp(ip);
        try {
            FlightInfoResponse resp = travellerHttpApi.searchFlightDynamicsInfo(ptFlightStatusReq, null);
            if (WSEnum.SUC000000.getResultCode().equals(resp.getCode())) {
                List<NewFlightInfo> data = resp.getData();
                List<NewFlightInfo> newList = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(data)) {
                    for (NewFlightInfo flightInfoResponse : data) {
                        if (getCompareTo(flightInfoResponse.getOntimeRate())) {
                            flightInfoResponse.setOntimeRate("--");
                        }
                        String conState = null;
                        if (queryAttention) {
                            conState = this.attention(baseReq, flightInfoResponse.getFlight_date(), flightInfoResponse.getFlight_no(), flightInfoResponse.getDeparture_airport(), flightInfoResponse.getArrival_airport(), ip);
                        }
                        NewFlightInfo info = this.bigdataFlightInfo(flightInfoResponse, ip, flightInfoResponse.getFlight_no(), flightInfoResponse.getFlight_date());
                        info.setConcert(conState);
                        SimpleDateFormat simpleDateFormat2 = new SimpleDateFormat("yyyy-MM-dd");
                        Date parse = simpleDateFormat2.parse(flightInfoResponse.getSta());
                        if (StringUtils.isNotEmpty(flightInfoResponse.getSta())) {
                            if (flightInfoResponse.getDepTimeZone() == flightInfoResponse.getArrTimeZone() || flightInfoResponse.getDepTimeZone() != 8) {
                                Date date = new Date();
                                String format = simpleDateFormat2.format(date);
                                Date date1 = simpleDateFormat2.parse(format);
                                if (date1.before(parse) || date1.equals(parse)) {
                                    flightInfoResponse.setShowAttentionButton(TRUE);
                                } else {
                                    flightInfoResponse.setShowAttentionButton(FALSE);
                                }
                            } else if (flightInfoResponse.getArrTimeZone() == 2) {
                                Date date = new Date();
                                TimeZone timeZone = TimeZone.getTimeZone("Europe/Helsinki");
                                DateFormat format = new SimpleDateFormat("yyyy-MM-dd");
                                format.setTimeZone(timeZone);
                                String format1 = format.format(date);
                                Date parse1 = format.parse(format1);
                                Date parse2 = simpleDateFormat2.parse(flightInfoResponse.getSta());
                                if (parse1.before(parse2) || parse1.equals(parse2)) {
                                    flightInfoResponse.setShowAttentionButton(TRUE);
                                } else {
                                    flightInfoResponse.setShowAttentionButton(FALSE);
                                }
                            } else {
                                flightInfoResponse.setShowAttentionButton(TRUE);
                            }
                        }
                        newList.add(info);
                    }
                }
                ShareFlightStatusRequest  shareFlightStatusRequest =new ShareFlightStatusRequest();
                BeanUtils.copyProperties(req,shareFlightStatusRequest);
                shareFlightStatusRequest.setSign(EncoderHandler.encodeByMD5(shareFlightStatusRequest.getFlightNo()+shareFlightStatusRequest.getFlightDate()+shareFlightStatusRequest.getDepartureAirport()));
                String   orderDetailString  = JsonUtil.objectToJson(shareFlightStatusRequest);
                String  shareSign = AESUtil.encrypt(orderDetailString,  HandlerConstants.DEFAULT_TOKEN.substring(HandlerConstants.DEFAULT_TOKEN.length() - 16));
                baseResp.setSign(shareSign);
                //两段行程合并展示
                if (baseReq.getRequest().isTravelMark()) {
                    List<NewFlightInfo> collect = newList.stream()
                            .filter(s -> (s.getDeparture_airport().equalsIgnoreCase(baseReq.getRequest().getDepartureAirport()) && s.getArrival_airport().equalsIgnoreCase(baseReq.getRequest().getArrivalAirport())))
                            .collect(Collectors.toList());
                    if (collect.isEmpty()) {
                        NewFlightInfo info = new NewFlightInfo();
                        for (NewFlightInfo objDatum : newList) {
                            if (objDatum.getDeparture_airport().equalsIgnoreCase(baseReq.getRequest().getDepartureAirport())) {
                                info.setFlight_no(objDatum.getFlight_no());
                                info.setFlight_date(objDatum.getFlight_date());
                                if (!getCompareTo(objDatum.getOntimeRate())) {
                                    info.setOntimeRate(objDatum.getOntimeRate());
                                } else {
                                    info.setOntimeRate("--");
                                }
                                info.setCheckinTable(objDatum.getCheckinTable());
                                info.setBoardGate(objDatum.getBoardGate());
                                info.setDeptWeatherPic(objDatum.getDeptWeatherPic());
                                info.setDepAirPortName(objDatum.getDepAirPortName());
                                info.setDeparture_airport(objDatum.getDeparture_airport());
                                info.setDeparture_city(objDatum.getDeparture_city());
                                info.setFlightHTerminal(objDatum.getFlightHTerminal());
                                info.setDepStandGate(objDatum.getDepStandGate());
                                info.setDepTimeZone(objDatum.getDepTimeZone());
                                info.setStd(objDatum.getStd());
                                info.setAtd(objDatum.getAtd());
                                info.setEtd(StringUtils.isNotBlank(objDatum.getEtd()) ? objDatum.getEtd() : objDatum.getStd());
                                info.setDeptWeather(objDatum.getDeptWeather());
                                info.setDeptTemp(objDatum.getDeptTemp());
                                info.setDeptpm(objDatum.getDeptpm());
                            }
                            if (objDatum.getArrival_airport().equalsIgnoreCase(baseReq.getRequest().getArrivalAirport())) {
                                info.setArr_bridge(objDatum.getArr_bridge());
                                info.setArrAirPortName(objDatum.getArrAirPortName());
                                info.setArrival_airport(objDatum.getArrival_airport());
                                info.setArrival_city(objDatum.getArrival_city());
                                info.setFlightTerminal(objDatum.getFlightTerminal());
                                info.setArrStandGate(objDatum.getArrStandGate());
                                info.setArrTimeZone(objDatum.getArrTimeZone());
                                info.setSta(objDatum.getSta());
                                info.setAta(objDatum.getAta());
                                info.setEta(StringUtils.isNotBlank(objDatum.getEta()) ? objDatum.getEta() : objDatum.getSta());
                                info.setDestWeatherPic(objDatum.getDestWeatherPic());
                                info.setDestWeather(objDatum.getDestWeather());
                                info.setDestTemp(objDatum.getDestTemp());
                                info.setDestpm(objDatum.getDestpm());
                                info.setBaggageID(objDatum.getBaggageID());
                                info.setReachexit(objDatum.getReachexit());
                            }
                        }
                        List<NewFlightInfo> newFlightInfoList = new ArrayList<>();
                        if (!StringUtils.isBlank(info.getStd()) && !StringUtils.isBlank(info.getSta()) && !StringUtils.isBlank(String.valueOf(info.getDepTimeZone())) && !StringUtils.isBlank(String.valueOf(info.getArrTimeZone()))) {
                            String flyTime = DateUtils.getFlyTime(info.getStd(), String.valueOf(info.getDepTimeZone()), info.getSta(), String.valueOf(info.getArrTimeZone()));
                            info.setFlyTimeLength(flyTime);
                        }
                        //将到达城市的时区转化为出发城市的时区获取预计跨天数
                        int etTimeOver = getEtTimeOver(info.getStd(), info.getSta(), info.getEtd(), info.getEta(), info.getDepTimeZone(), info.getArrTimeZone());
                        info.setEtTimeOver(etTimeOver);
                        //经停航班状态处理
                        info.setFlight_status(getFlightStatus(newList, baseReq));
                        //经停航班关注处理
                        info.setConcert(this.attention(baseReq, info.getFlight_date(), info.getFlight_no(), info.getDeparture_airport(), info.getArrival_airport(), ip));
                        newFlightInfoList.add(info);
                        baseResp.setObjData(newFlightInfoList);
                        baseResp.setResultCode(WSEnum.SUCCESS.getResultCode());
                        return baseResp;
                    }
                    int etTimeOver = getEtTimeOver(collect.get(0).getStd(), collect.get(0).getSta(), collect.get(0).getEtd(), collect.get(0).getEta(), collect.get(0).getDepTimeZone(), collect.get(0).getArrTimeZone());
                    collect.get(0).setEtTimeOver(etTimeOver);
                    // 如果是行程列表的行程详情需要查询是否展示电子登机牌入口
                    if (req.isTravelMark() && CollectionUtils.isNotEmpty(newList)) {
                        if (StringUtils.isNotBlank(req.getOperationLocation())) {
                            newList.get(0).setShowBoardingPass(true);
                        } else {
                            // 查询选座信息，出错不影响结果展示
                            try {
                                /*BaseRequestDTO<QueryTourRequestDTO> baseRequest = new BaseRequestDTO<>();
                                baseRequest.setIp(ip);
                                baseRequest.setChannelCode(getChannelInfo(baseReq.getChannelCode(), "50"));
                                baseRequest.setVersion(HandlerConstants.NEW_CHECKIN_SEAT_VERSION);
                                QueryTourRequestDTO queryTourRequestDTO = new QueryTourRequestDTO();
                                queryTourRequestDTO.setWithOrder(false);
                                queryTourRequestDTO.setCertificateNo(req.getTicketNo());
                                queryTourRequestDTO.setPassengerName(req.getPassengerName());
                                queryTourRequestDTO.setQueryType("SEAT");
                                baseRequest.setRequest(queryTourRequestDTO);
                                HttpResult seatResult = this.doPostClient(baseRequest, HandlerConstants.NEW_CHECKIN_SELECT_URL + HandlerConstants.QUERY_TOUR);
                                if (seatResult.isResult() && StringUtils.isNotBlank(seatResult.getResponse())) {
                                    BaseResultDTO<QueryTourResponseDTO> res = JsonUtil.fromJson(seatResult.getResponse(), new TypeToken<BaseResultDTO<QueryTourResponseDTO>>() {
                                    }.getType());
                                    if (res.getResultCode().equals("1001") && null != res.getResult() && CollectionUtils.isNotEmpty(res.getResult().getTours())) {
                                        res.getResult().getTours().forEach(flightTourDTO -> {
                                            if (null != flightTourDTO.getFlightNo() && flightTourDTO.getFlightNo().equals(newList.get(0).getFlight_no())
                                                    && null != flightTourDTO.getFlightDate() && flightTourDTO.getFlightDate().equals(newList.get(0).getFlight_date())) {
                                                newList.get(0).setShowBoardingPass(StringUtils.isNotBlank(flightTourDTO.getAsrSeatNo())
                                                        || StringUtils.isNotBlank(flightTourDTO.getCheckInSeatNo()));
                                            }
                                        });
                                    }
                                }*/
                            } catch (Exception e) {
                                log.error("查询行程选座信息出现异常", e);
                            }
                        }
                    }

                    baseResp.setResultCode(WSEnum.SUCCESS.getResultCode());
                    baseResp.setObjData(collect);
                    return baseResp;
                }
                Collections.sort(newList);
                baseResp.setResultCode(WSEnum.SUCCESS.getResultCode());
                baseResp.setObjData(newList);
                return baseResp;
            } else {
                baseResp.setResultCode(WSEnum.ERROR.getResultCode());
                baseResp.setResultInfo(resp.getMsg());
                return baseResp;
            }
        } catch (Exception e) {
            log.info("查询航班动态信息异常，错误信息为：{}", e.getMessage(), e);
            baseResp.setResultCode(WSEnum.ERROR.getResultCode());
            baseResp.setResultInfo("网络异常");
            return baseResp;
        }
    }


    @RequestMapping(value = "/queryShareFlightDynamic", method = RequestMethod.POST)
    @InterfaceLog
    public BaseResp<List<ShareFlightInfo>> queryShareFlightDynamic(@RequestBody @Validated  ShareSign req, BindingResult bindingResult,HttpServletRequest request) {
        BaseResp<List<ShareFlightInfo>> baseResp = new BaseResp<>();
       try {
           if (bindingResult.hasErrors()) {
               baseResp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
               baseResp.setResultInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
               return baseResp;
           }
        String decryptResult = AESUtil.decrypt(req.getSign(), HandlerConstants.DEFAULT_TOKEN.substring(HandlerConstants.DEFAULT_TOKEN.length() - 16), 16);
        ShareFlightStatusRequest   shareFlightStatusRequest=JsonUtil.fromJson(decryptResult,ShareFlightStatusRequest.class);
        String ip = this.getClientIP(request);
        String  sign =EncoderHandler.encodeByMD5(shareFlightStatusRequest.getFlightNo()+shareFlightStatusRequest.getFlightDate()+shareFlightStatusRequest.getDepartureAirport());
        if (!shareFlightStatusRequest.getSign().equals(sign)){
            baseResp.setResultCode(WSEnum.ERROR.getResultCode());
            baseResp.setResultInfo("签名不正确!");
            return baseResp;
        }
        PtFlightStatusReq ptFlightStatusReq = new PtFlightStatusReq();
        if (StringUtils.isBlank(shareFlightStatusRequest.getFlightDate())) {
            baseResp.setResultCode(WSEnum.ERROR.getResultCode());
            baseResp.setResultInfo("航班日期不能为空");
            return baseResp;
        }
           //查询用户保存的证件信息
           String[] items = {MemberDetailRequestItemsEnum.BASICINFO.eName
                   , MemberDetailRequestItemsEnum.CERTIFICATEINFO.eName
                   , MemberDetailRequestItemsEnum.REALVERIFYINFOS.eName};
           PtApiCRMRequest<PtMemberDetailRequest> ptApiRequestCert = CRMReqUtil.buildMemberDetailReq(shareFlightStatusRequest.getFfpCardNo(), shareFlightStatusRequest.getFfpId(), request, "MOBILE", items);
           PtCRMResponse<PtMemberDetail> ptCRMResponse = memberService.memberDetail(ptApiRequestCert);
           if (ptCRMResponse.getCode() != 0) {
               baseResp.setResultCode(WSEnum.ERROR.getResultCode());
               baseResp.setResultInfo("未查询到会员信息(" + ptCRMResponse.getCode() + ")");
               return baseResp;
           }
           //判断实名
           if (!CrmUtil.judgeRealNameStatus(ptCRMResponse.getData().getRealVerifyInfos())) {
               baseResp.setResultCode(WSEnum.NO_REAL_NAME.getResultCode());
               baseResp.setResultInfo(WSEnum.NO_REAL_NAME.getResultInfo());
               return baseResp;
           }
           MemberBasicInfoSoaModel memberBasicInfoSoaModel = ptCRMResponse.getData().getBasicInfo();

         ptFlightStatusReq.setFlightDateLocal(shareFlightStatusRequest.getFlightDate());
         ptFlightStatusReq.setFlightNo(shareFlightStatusRequest.getFlightNo());
         ptFlightStatusReq.setDepartureAirport(shareFlightStatusRequest.getDepartureAirport());
         ptFlightStatusReq.setArrivalAirport(shareFlightStatusRequest.getArrivalAirport());
         ptFlightStatusReq.setIp(ip);
           List<ShareFlightInfo> newList = new ArrayList<>();
            FlightInfoResponse resp = travellerHttpApi.searchFlightDynamicsInfo(ptFlightStatusReq, null);
            if (WSEnum.SUC000000.getResultCode().equals(resp.getCode())) {
                List<NewFlightInfo> data = resp.getData();
                if (CollectionUtils.isNotEmpty(data)) {
                    for (NewFlightInfo flightInfoResponse : data) {
                        if (getCompareTo(flightInfoResponse.getOntimeRate())) {
                            flightInfoResponse.setOntimeRate("--");
                        }
                        ShareFlightInfo  shareFlightInfo=new ShareFlightInfo();
                        NewFlightInfo info = this.bigdataFlightInfo(flightInfoResponse, ip, flightInfoResponse.getFlight_no(), flightInfoResponse.getFlight_date());
                        BeanUtils.copyProperties(info, shareFlightInfo);
                        SimpleDateFormat simpleDateFormat2 = new SimpleDateFormat("yyyy-MM-dd");
                        Date parse = simpleDateFormat2.parse(flightInfoResponse.getSta());
                        if (StringUtils.isNotEmpty(flightInfoResponse.getSta())) {
                            if (flightInfoResponse.getDepTimeZone() == flightInfoResponse.getArrTimeZone() || flightInfoResponse.getDepTimeZone() != 8) {
                                Date date = new Date();
                                String format = simpleDateFormat2.format(date);
                                Date date1 = simpleDateFormat2.parse(format);
                                if (date1.before(parse) || date1.equals(parse)) {
                                    flightInfoResponse.setShowAttentionButton(TRUE);
                                } else {
                                    flightInfoResponse.setShowAttentionButton(FALSE);
                                }
                            } else if (flightInfoResponse.getArrTimeZone() == 2) {
                                Date date = new Date();
                                TimeZone timeZone = TimeZone.getTimeZone("Europe/Helsinki");
                                DateFormat format = new SimpleDateFormat("yyyy-MM-dd");
                                format.setTimeZone(timeZone);
                                String format1 = format.format(date);
                                Date parse1 = format.parse(format1);
                                Date parse2 = simpleDateFormat2.parse(flightInfoResponse.getSta());
                                if (parse1.before(parse2) || parse1.equals(parse2)) {
                                    flightInfoResponse.setShowAttentionButton(TRUE);
                                } else {
                                    flightInfoResponse.setShowAttentionButton(FALSE);
                                }
                            } else {
                                flightInfoResponse.setShowAttentionButton(TRUE);
                            }
                        }
                        newList.add(shareFlightInfo);
                    }
                }
                baseResp.setResultCode(WSEnum.SUCCESS.getResultCode());
                baseResp.setObjData(newList);
            } else {
                baseResp.setResultCode(WSEnum.ERROR.getResultCode());
                baseResp.setResultInfo(resp.getMsg());
            }

           //查询大数据接口 /traveller/ticket/info 证件信息
           List<MytripInfoResponse> listInfo = new ArrayList<>();
           if (null != ptCRMResponse.getData() && CollectionUtils.isNotEmpty(ptCRMResponse.getData().getCertificateInfo())) {
               List<List<MytripInfoResponse>> collect1 = ptCRMResponse.getData().getCertificateInfo().parallelStream()
                       .map(s -> travellerApiService.getInfoByCert(s, ip, shareFlightStatusRequest.getFfpCardNo(), memberBasicInfoSoaModel))
                       .collect(Collectors.toList());
               for (List<MytripInfoResponse> o : collect1) {
                   listInfo.addAll(o);
               }
           }
           for (ShareFlightInfo shareFlightInfo:newList){
               listInfo.stream().forEach(mytripInfoResponse -> {
                   if (shareFlightInfo.getFlight_no().equals(mytripInfoResponse.getFlightNo())
                   &&shareFlightInfo.getFlight_date().equals(mytripInfoResponse.getFlightDate())
                   &&shareFlightInfo.getArrival_airport().equals(mytripInfoResponse.getArrAirport())
                   &&shareFlightInfo.getDeparture_airport().equals(mytripInfoResponse.getDepAirport())){
                       BoardInfoResultDTO boardInfoResult = checkInSeatService.queryFlightBoardInAirport(mytripInfoResponse.getFlightNo(),
                               mytripInfoResponse.getFlightDate(), mytripInfoResponse.getDepAirport(), mytripInfoResponse.getArrAirport());
                       if (null != boardInfoResult) {
                           shareFlightInfo.setBoardingDateTime(boardInfoResult.getBoardingDateTime());
                       }
                       shareFlightInfo.setCardNo(mytripInfoResponse.getCardNo());
                       shareFlightInfo.setDocumentType(mytripInfoResponse.getDocumentType());
                       shareFlightInfo.setState(mytripInfoResponse.getState());
                       shareFlightInfo.setCabin(mytripInfoResponse.getCabin());
                       shareFlightInfo.setCabinClass(mytripInfoResponse.getCabinClass());
                       shareFlightInfo.setTravelName(mytripInfoResponse.getTravelName());
                       shareFlightInfo.setBaggageAllowance(mytripInfoResponse.getBaggageAllowance());
                       shareFlightInfo.setHand_luggage(mytripInfoResponse.getHand_luggage());
                       shareFlightInfo.setCheckinweight_total(mytripInfoResponse.getCheckinweight_total());
                       shareFlightInfo.setDateDiff(mytripInfoResponse.getDateDiff());
                       shareFlightInfo.setDepTerminal(mytripInfoResponse.getDepTerminal());
                       shareFlightInfo.setArrTerminal(mytripInfoResponse.getArrTerminal());
                       shareFlightInfo.setEtd(mytripInfoResponse.getEtd());
                       shareFlightInfo.setEta(mytripInfoResponse.getEta());
                       shareFlightInfo.setDateOver(mytripInfoResponse.getDateOver());
                       shareFlightInfo.setPlanArrTime(mytripInfoResponse.getPlanArrTime());
                       shareFlightInfo.setPlanDepTime(mytripInfoResponse.getPlanDepTime());
                       shareFlightInfo.setEmd_baggage(mytripInfoResponse.getEmd_baggage());
                       shareFlightInfo.setEmd_baggage_unit(mytripInfoResponse.getEmd_baggage_unit());
                       shareFlightInfo.setStopPlace(mytripInfoResponse.getStopPlace());
                       shareFlightInfo.setStop(mytripInfoResponse.isStop());
                       shareFlightInfo.setCarrierFlag(mytripInfoResponse.isCarrierFlag());
                       shareFlightInfo.setFlightNoIconList(mytripInfoResponse.getFlightNoIconList());
                       shareFlightInfo.setSeatNo(mytripInfoResponse.getSeatNo());
                       shareFlightInfo.setTicketType(mytripInfoResponse.getTicketType());
                       shareFlightInfo.setWeekDate(mytripInfoResponse.getWeekDate());
                   }
               });
           }

           return baseResp;
        } catch (Exception e) {
            log.info("查询航班动态信息异常，错误信息为：{}", e.getMessage(), e);
            baseResp.setResultCode(WSEnum.ERROR.getResultCode());
            baseResp.setResultInfo("网络异常");
            return baseResp;
        }
    }

    /**
     * @Description:
     * @Param: No such property: code for class: Script1
     * @return: 函数式风格查询 先查关注列表，在查航线信息
     * @Author: zhangwanli
     * @Date: 2019/7/18
     */
    @ApiOperation(value = "queryAttention",tags = "查询关注航班列表")
    @RequestMapping(value = "/queryAttention", method = RequestMethod.POST)
    public BaseResp<List<NewFlightInfo>> queryAttention(@RequestBody @Validated BasicBaseReq<AttentionFlightRequest> requestBaseResp, BindingResult bindingResult, HttpServletRequest request) {
        BaseResp<List<NewFlightInfo>> baseResp = new BaseResp<>();
        String ip = this.getClientIP(request);
        if (bindingResult.hasErrors()) {
            throw new RequestParamErrorException(bindingResult.getAllErrors().get(0).getDefaultMessage());
        }
        AttentionFlightRequest attentionFlightRequest = requestBaseResp.getRequest();
        List<FollowAirLineInfo> data = basicService.queryAttentionFlightList(requestBaseResp.getChannelCode(), attentionFlightRequest.getFfpId(), attentionFlightRequest.getFfpCardNo(), ip);
        try {
            List<NewFlightInfo> newFlightInfos = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(data)) {
                List<BaseResp<List<NewFlightInfo>>> collect = data.parallelStream()
                        .map(a -> queryDynamic(a.getFlightNo(), a.getFlightDate(), ip, a.getConcern()))
                        .collect(Collectors.toList());
                //数据存储
                if (!collect.isEmpty()) {
                    for (int j = 0; j < collect.size(); j++) {
                        if (!collect.get(j).getObjData().isEmpty()) {
                            //展示信息完全的航班
                            newFlightInfos.addAll(collect.get(j).getObjData());
                        }
                    }
                    baseResp.setObjData(newFlightInfos);
                }
            }
        } catch (Exception e) {
            log.info("查询基础服务航班动态信息报错，异常信息为：{}", e.getMessage(), e);
            baseResp.setResultCode(WSEnum.ERROR.getResultCode());
            baseResp.setResultInfo("网络异常");
            return baseResp;
        }
        baseResp.setResultCode(WSEnum.SUCCESS.getResultCode());
        baseResp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
        return baseResp;
    }

    /**
     * @Description:
     * @Param: No such property: code for class: Script1
     * @return: 增加关注航线
     * @Author: zhangwanli
     * @Date: 2019/7/18
     */
    @ApiOperation(value = "addAttention",tags = "关注航班")
    @RequestMapping(value = "/addAttention", method = RequestMethod.POST)
    public BaseResp addAttention(@RequestBody @Validated BasicBaseReq<AddCancelAttentionRequest> requestBaseResp, BindingResult bindingResult, HttpServletRequest request) {
        if (bindingResult.hasErrors()) {
            throw new RequestParamErrorException(bindingResult.getAllErrors().get(0).getDefaultMessage());
        }
        String ip = this.getClientIP(request);
        AddCancelAttentionRequest addCancelAttentionRequest = requestBaseResp.getRequest();
        AttentionFlightParam attentionFlightParam = new AttentionFlightParam();
        attentionFlightParam.setFlightDate(addCancelAttentionRequest.getFlightDate());
        attentionFlightParam.setFlightNo(addCancelAttentionRequest.getFlightNo());
        attentionFlightParam.setDepAirportCode(addCancelAttentionRequest.getDepAirportCode());
        attentionFlightParam.setArrAirportCode(addCancelAttentionRequest.getArrAirportCode());
        basicService.addAttentionFlight(requestBaseResp.getChannelCode(), String.valueOf(addCancelAttentionRequest.getFfpId()), addCancelAttentionRequest.getFfpCardNo(), ip, attentionFlightParam);
        return BaseResp.success();
    }

    /**
     * @Description:
     * @Param: No such property: code for class: Script1
     * @return: 取消关注航线
     * @Author: zhangwanli
     * @Date: 2019/7/18
     */
    @ApiOperation(value = "取消关注航线")
    @RequestMapping(value = "/cancelAttention", method = RequestMethod.POST)
    public BaseResp cancelAttention(@RequestBody @Validated BasicBaseReq<AddCancelAttentionRequest> requestBaseResp, BindingResult bindingResult,HttpServletRequest request) {
        if (bindingResult.hasErrors()) {
            throw new RequestParamErrorException(bindingResult.getAllErrors().get(0).getDefaultMessage());
        }
        AddCancelAttentionRequest addCancelAttentionRequest = requestBaseResp.getRequest();
        if(StringUtils.isAnyBlank(addCancelAttentionRequest.getDepAirportCode(),addCancelAttentionRequest.getArrAirportCode())){
            throw new RequestParamErrorException("机场信息不可为空");
        }
        BaseResp<Object> baseResp = new BaseResp<>();
        requestBaseResp.setIp(this.getClientIP(request));
        //基础服务接口版本号
        requestBaseResp.setVersion(HandlerConstants.BASIC_INFO_VERSION);
        try {
            HttpResult result = this.doPostClient(requestBaseResp, HandlerConstants.BASIC_INFO_URL + HandlerConstants.ATTENTION_CANCEL_AIRLINE);
            if (!result.isResult()) {
                baseResp.setResultCode(WSEnum.ERROR.getResultCode());
                baseResp.setResultInfo(result.getResponse());
                return baseResp;
            }
            if (!StringUtil.isNullOrEmpty(result.getResponse())) {
                FollowAirLineResponse followResponse = JsonMapper.buildNonNullMapper().fromJson(result.getResponse(), FollowAirLineResponse.class);
                if (null != followResponse && "10001".equals(followResponse.getResultCode())) {
                    baseResp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                    baseResp.setResultCode(WSEnum.SUCCESS.getResultCode());
                    return baseResp;
                }
            }
            baseResp.setResultInfo("网络异常");
            baseResp.setResultCode(WSEnum.ERROR.getResultCode());
        } catch (Exception e) {
            log.error("{}删除航班动态信息报错，错误信息为：", MdcUtils.getRequestId(), e);
            baseResp.setResultCode(WSEnum.ERROR.getResultCode());
            baseResp.setResultInfo("取消关注航班发生未知异常");
            return baseResp;
        }
        return baseResp;
    }

    /**
     * @Description:
     * @Param: No such property: code for class: Script1
     * @return: 航班号 航班日期查询详情接口
     * @Author: zhangwanli
     * @Date: 2019/7/18
     */
    private BaseResp<List<NewFlightInfo>> queryDynamic(String flightNo, String flightDate, String ip, String concert) {
        BaseResp<List<NewFlightInfo>> baseResp = new BaseResp<>();
        PtFlightStatusReq ptFlightStatusReq = new PtFlightStatusReq();
        ptFlightStatusReq.setFlightNo(flightNo);
        ptFlightStatusReq.setFlightDateLocal(flightDate);
        ptFlightStatusReq.setIp(ip);
        try {
            FlightInfoResponse resp = travellerHttpApi.searchFlightDynamicsInfo(ptFlightStatusReq, null);
            if (WSEnum.SUC000000.getResultCode().equals(resp.getCode())) {
                List<NewFlightInfo> data = resp.getData();
                List<NewFlightInfo> newList = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(data)) {
                    for (NewFlightInfo flightInfoResponse : data) {
                        NewFlightInfo newFlightInfo = this.bigdataFlightInfo(flightInfoResponse, ip, flightNo, flightDate);
                        //是否关注  1 关注  2 未关注
                        newFlightInfo.setConcert(concert);
                        SimpleDateFormat simpleDateFormat2 = new SimpleDateFormat("yyyy-MM-dd");
                        Date parse = simpleDateFormat2.parse(flightInfoResponse.getStd());
                        if (StringUtils.isNotEmpty(flightInfoResponse.getStd())) {
                            if (flightInfoResponse.getDepTimeZone() == flightInfoResponse.getArrTimeZone() || flightInfoResponse.getArrTimeZone() == 2) {
                                Date date = new Date();
                                String format = simpleDateFormat2.format(date);
                                Date date1 = simpleDateFormat2.parse(format);
                                if (date1.before(parse) || date1.equals(parse)) {
                                    flightInfoResponse.setShowAttentionButton(TRUE);
                                } else {
                                    flightInfoResponse.setShowAttentionButton(FALSE);
                                }
                            } else if (flightInfoResponse.getArrTimeZone() == 2) {
                                Date date = new Date();
                                TimeZone timeZone = TimeZone.getTimeZone("Europe/Helsinki");
                                DateFormat format = new SimpleDateFormat("yyyy-MM-dd");
                                format.setTimeZone(timeZone);
                                String format1 = format.format(date);
                                Date parse1 = format.parse(format1);
                                Date parse2 = simpleDateFormat2.parse(flightInfoResponse.getSta());
                                if (parse1.before(parse2) || parse1.equals(parse2)) {
                                    flightInfoResponse.setShowAttentionButton(TRUE);
                                } else {
                                    flightInfoResponse.setShowAttentionButton(FALSE);
                                }
                            } else {
                                flightInfoResponse.setShowAttentionButton(true);
                            }
                        }
                        if (flightInfoResponse.isShowAttentionButton()) {
                            newList.add(newFlightInfo);
                        }
                    }
                }
                //航班时间排序显示
                SimpleDateFormat sdf = new SimpleDateFormat(DateUtils.YYYY_MM_DD_HH_MM_PATTERN);
                for (int i = 0; i < newList.size() - 1; i++) {
                    for (int j = 0; j < newList.size() - 1; j++) {
                        if (sdf.parse(newList.get(j).getStd()).after(sdf.parse(newList.get(j + 1).getStd()))) {
                            NewFlightInfo order = newList.get(j);
                            newList.set(j, newList.get(j + 1));
                            newList.set(j + 1, order);
                        }
                    }
                }
                baseResp.setResultCode(WSEnum.SUCCESS.getResultCode());
                baseResp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                baseResp.setObjData(newList);
                return baseResp;
            } else {
                baseResp.setResultCode(WSEnum.ERROR.getResultCode());
                baseResp.setResultInfo(resp.getMsg());
                return baseResp;
            }
        } catch (Exception e) {
            log.error("查询航班动态信息报错，错误信息为：{}", e.getMessage(), e);
            baseResp.setResultCode(WSEnum.ERROR.getResultCode());
            baseResp.setResultInfo("网络异常");
            return baseResp;
        }
    }


    /**
     * @Description:
     * @Param: No such property: code for class: Script1
     * @return: 接口拆解  大数据查询
     * @Author: zhangwanli
     * @Date: 2019/7/18
     */
    private NewFlightInfo bigdataFlightInfo(NewFlightInfo flightInfoResponse, String ip, String argFlightNo, String argFlightDate) {
        //查询机型时 入参对象
        FlightInfo flightInfo = new FlightInfo();
        //大数据参数对象
        //航班延误时间换算
        SimpleDateFormat sdf = new SimpleDateFormat(DateUtils.YYYY_MM_DD_HH_MM_PATTERN);
        AirPortInfoDto deptAirPoty = localCacheService.getLocalAirport(flightInfoResponse.getDeparture_airport());
        AirPortInfoDto arrAirPort = localCacheService.getLocalAirport(flightInfoResponse.getArrival_airport());
        //航班状态
        if ("0".equals(flightInfoResponse.getAdjust_type())) {
            flightInfoResponse.setFlight_status("取消");
        }
        //前置起飞机场 城市
        if (StringUtils.isNotBlank(flightInfoResponse.getPre_departure_airport()) && null != localCacheService.getLocalAirport(flightInfoResponse.getPre_departure_airport())) {
            AirPortInfoDto preDeptAirPoty = localCacheService.getLocalAirport(flightInfoResponse.getPre_departure_airport());
            flightInfoResponse.setPre_depAirPortName(preDeptAirPoty.getAirPortName());
            flightInfoResponse.setPre_departure_city(preDeptAirPoty.getCityName());
        }
        if (StringUtils.isNotBlank(flightInfoResponse.getPre_arrival_airport()) && null != localCacheService.getLocalAirport(flightInfoResponse.getPre_arrival_airport())) {
            AirPortInfoDto preArrAirPort = localCacheService.getLocalAirport(flightInfoResponse.getPre_arrival_airport());
            flightInfoResponse.setPre_arrAirPortName(preArrAirPort.getAirPortName());
            flightInfoResponse.setPre_arrival_city(preArrAirPort.getCityName());
        }
        if (StringUtils.isNotBlank(flightInfoResponse.getDivert_airport()) && null != localCacheService.getLocalAirport((flightInfoResponse.getDivert_airport()))) {
            AirPortInfoDto divertAirPort = localCacheService.getLocalAirport(flightInfoResponse.getDivert_airport());
            if (null != divertAirPort) {
                flightInfoResponse.setDivert_airPortName(divertAirPort.getCityName() + divertAirPort.getAirPortName());
            }
        }

        //飞行时长 行程页面
        if (!StringUtils.isBlank(flightInfoResponse.getStd()) && !StringUtils.isBlank(flightInfoResponse.getSta()) && null != deptAirPoty && null != arrAirPort) {
            String flyTime = DateUtils.getFlyTime(flightInfoResponse.getStd(), deptAirPoty.getCityTimeZone(), flightInfoResponse.getSta(), arrAirPort.getCityTimeZone());
            flightInfoResponse.setFlyTimeLength(flyTime);
        }

        if (!StringUtils.isBlank(flightInfoResponse.getSta()) && (flightInfoResponse.getSta().indexOf('-') < 0)) {
            flightInfoResponse.setSta(flightInfoResponse.getFlight_date() + " " + flightInfoResponse.getSta());
        }
        String std = flightInfoResponse.getStd().indexOf('-') < 0 ? flightInfoResponse.getFlight_date() + " "
                + flightInfoResponse.getStd() : flightInfoResponse.getStd();
        //计算延误的毫秒值
        try {
            if ("延误".equals(flightInfoResponse.getFlight_status())) {
                Long delayTime = new Date().getTime() - sdf.parse(std).getTime();
                if (delayTime > 0) {
                    flightInfoResponse.setDelayTime(delayTime);
                }
            }
        } catch (ParseException e) {
            log.error("时间转换异常，预飞时间为：{}", std, e);
        }
        if (!StringUtils.isBlank(flightInfoResponse.getStd()) && (flightInfoResponse.getStd().indexOf('-') < 0)) {
            flightInfoResponse.setStd(flightInfoResponse.getFlight_date() + " " + flightInfoResponse.getStd());
        }
        if (!StringUtils.isBlank(flightInfoResponse.getAta()) && (flightInfoResponse.getAta().indexOf('-') < 0)) {
            flightInfoResponse.setAta(flightInfoResponse.getFlight_date() + " " + flightInfoResponse.getAta());
        }
        if (!StringUtils.isBlank(flightInfoResponse.getAtd()) && (flightInfoResponse.getAtd().indexOf('-') < 0)) {
            flightInfoResponse.setAtd(flightInfoResponse.getFlight_date() + " " + flightInfoResponse.getAtd());
        }

        if (!StringUtils.isBlank(flightInfoResponse.getPre_sta()) && (flightInfoResponse.getPre_sta().indexOf('-') < 0)) {
            flightInfoResponse.setPre_sta(flightInfoResponse.getFlight_date() + " " + flightInfoResponse.getPre_sta());
        }
        if (!StringUtils.isBlank(flightInfoResponse.getPre_std()) && (flightInfoResponse.getPre_std().indexOf('-') < 0)) {
            flightInfoResponse.setPre_std(flightInfoResponse.getFlight_date() + " " + flightInfoResponse.getPre_std());
        }

        if (!StringUtils.isBlank(flightInfoResponse.getPre_ata()) && (flightInfoResponse.getPre_ata().indexOf('-') < 0)) {
            flightInfoResponse.setPre_ata(flightInfoResponse.getFlight_date() + " " + flightInfoResponse.getPre_ata());
        }
        if (!StringUtils.isBlank(flightInfoResponse.getPre_atd()) && (flightInfoResponse.getPre_atd().indexOf('-') < 0)) {
            flightInfoResponse.setPre_atd(flightInfoResponse.getFlight_date() + " " + flightInfoResponse.getPre_atd());
        }
        flightInfoResponse.setDepAirPortName(null != deptAirPoty ? deptAirPoty.getAirPortName() : "");
        flightInfoResponse.setDeparture_city_code(null != deptAirPoty ? deptAirPoty.getCityCode() : "");
        flightInfoResponse.setDeparture_city(null != deptAirPoty ? deptAirPoty.getCityName() : "");
        flightInfoResponse.setArrAirPortName(null != arrAirPort ? arrAirPort.getAirPortName() : "");
        flightInfoResponse.setArrival_city_code(null != arrAirPort ? arrAirPort.getCityCode() : "");
        flightInfoResponse.setArrival_city(null != arrAirPort ? arrAirPort.getCityName() : "");
        if (!StringUtils.isBlank(flightInfoResponse.getDeptTemp())) {
            flightInfoResponse.setDeptTemp(flightInfoResponse.getDeptTemp().replaceAll("C\\((-)?[0-9]+F\\)", "℃"));
        }
        if (!StringUtils.isBlank(flightInfoResponse.getDestTemp())) {
            flightInfoResponse.setDestTemp(flightInfoResponse.getDestTemp().replaceAll("C\\((-)?[0-9]+F\\)", "℃"));
        }
        flightInfo.setArrAirport(flightInfoResponse.getArrival_airport());
        flightInfo.setDepAirport(flightInfoResponse.getDeparture_airport());
        flightInfo.setFlightDate(flightInfoResponse.getFlight_date());
        flightInfo.setFlightNo(flightInfoResponse.getFlight_no());
        List<FlightInfo> queryBaseFlight = basicService.queryFlightInfo(flightInfo);
        if (CollectionUtils.isNotEmpty(queryBaseFlight)) {
            flightInfo = queryBaseFlight.get(0);
            Map<String, AircraftModel> aircraftModelMap = FlightUtil.toAircraftModelMap(handConfig.getAircraftModel());
            AircraftModel aircraftModel = FlightUtil.convertAircraftModel(aircraftModelMap, flightInfo.getPlanType());
            flightInfoResponse.setAirPortInfo("机型" + flightInfo.getPlanType());
            if (aircraftModel != null) {
                flightInfoResponse.setAirPortType(aircraftModel.getRemark());
            }

        }
        FlightWeatherInfoEnum deptWeather = FlightWeatherInfoEnum.formatWeatherStatus(flightInfoResponse.getDeptWeather());
        FlightWeatherInfoEnum destWeather = FlightWeatherInfoEnum.formatWeatherStatus(flightInfoResponse.getDestWeather());
        flightInfoResponse.setDeptWeatherPic(deptWeather.getWeatherPictureUrl());
        flightInfoResponse.setDestWeatherPic(destWeather.getWeatherPictureUrl());
        if (StringUtils.isNotBlank(flightInfoResponse.getPre_flight_no())) {
            //存在前序航班，并且前序航班状态为空
            PtFlightStatusReq ptFlightStatusReq = new PtFlightStatusReq();
            ptFlightStatusReq.setFlightNo(flightInfoResponse.getPre_flight_no());
            ptFlightStatusReq.setDepartureAirport(flightInfoResponse.getPre_departure_airport());
            ptFlightStatusReq.setArrivalAirport(flightInfoResponse.getPre_arrival_airport());
            ptFlightStatusReq.setFlightDateLocal(flightInfoResponse.getFlight_date());
            ptFlightStatusReq.setIp(ip);
            FlightInfoResponse preFlight = travellerHttpApi.searchFlightDynamicsInfo(ptFlightStatusReq, null);
            if (WSEnum.SUC000000.getResultCode().equals(preFlight.getCode())) {
                List<NewFlightInfo> newFlightInfos = preFlight.getData();
                if (CollectionUtils.isNotEmpty(newFlightInfos)) {
                    NewFlightInfo newFlightInfo = newFlightInfos.get(0);
                    if ("0".equals(newFlightInfo.getAdjust_type())) {
                        flightInfoResponse.setPre_flight_status("取消");
                    } else {
                        flightInfoResponse.setPre_flight_status(newFlightInfo.getFlight_status());
                    }
                    flightInfoResponse.setPre_etd(newFlightInfo.getEtd());
                    flightInfoResponse.setPre_eta(newFlightInfo.getEta());
                }
            }
        }
        //经停航班查询
        int cancelAll = 0;
        String flightDate = "";
        if (StringUtils.isNotBlank(flightInfoResponse.getStopover_station())) {
            AirPortInfoDto stopoverAirPort = localCacheService.getLocalAirport(flightInfoResponse.getStopover_station());
            //机场信息
            if (null != stopoverAirPort) {
                flightInfo.setArrAirport(flightInfoResponse.getStopover_station());
                flightInfo.setDepAirport(flightInfoResponse.getDeparture_airport());
                flightInfo.setFlightDate(argFlightDate);
                flightInfo.setFlightNo(argFlightNo);
                queryBaseFlight = basicService.queryFlightInfo(flightInfo);
                if (CollectionUtils.isNotEmpty(queryBaseFlight)) {
                    flightInfo = queryBaseFlight.get(0);
                    flightInfoResponse.setStopovert_airPortName(stopoverAirPort.getCityName() +
                            stopoverAirPort.getAirPortName() +
                            (StringUtils.isBlank(flightInfo.getArrAirportTerminal()) ?
                                    "" : flightInfo.getArrAirportTerminal()));
                } else {
                    flightInfoResponse.setStopovert_airPortName(stopoverAirPort.getCityName() +
                            stopoverAirPort.getAirPortName());
                }
            }
            //A-B段查询有没有备降信息
            PtFlightStatusReq ptFlightStatusReq = new PtFlightStatusReq();
            ptFlightStatusReq.setFlightNo(flightInfoResponse.getFlight_no());
            ptFlightStatusReq.setDepartureAirport(flightInfoResponse.getDeparture_airport());
            ptFlightStatusReq.setArrivalAirport(flightInfoResponse.getStopover_station());
            ptFlightStatusReq.setFlightDateLocal(flightInfoResponse.getFlight_date());
            ptFlightStatusReq.setIp(ip);
            FlightInfoResponse aToBs = travellerHttpApi.searchFlightDynamicsInfo(ptFlightStatusReq, null);
            if (WSEnum.SUC000000.getResultCode().equals(aToBs.getCode())) {
                List<NewFlightInfo> newFlightInfos = aToBs.getData();
                if (CollectionUtils.isNotEmpty(newFlightInfos)) {
                    NewFlightInfo newFlightInfo = newFlightInfos.get(0);
                    //A-B存在备降
                    if (!StringUtils.isBlank(newFlightInfo.getDivert_airport())) {
                        flightInfoResponse.setDivert_airport1(newFlightInfo.getDivert_airport());
                        flightInfoResponse.setDivert_weather1(newFlightInfo.getDivert_weather());
                        flightInfoResponse.setDivert_temp1(newFlightInfo.getDivert_temp());
                        flightInfoResponse.setDivert_pm1(newFlightInfo.getDivert_pm());
                        flightInfoResponse.setDivert_airPortName1(null == localCacheService.getLocalAirport(newFlightInfo.getDivert_airport()) ? "" : localCacheService.getLocalAirport(newFlightInfo.getDivert_airport()).getCityName() +
                                localCacheService.getLocalAirport(newFlightInfo.getDivert_airport()).getAirPortName());
                        flightInfoResponse.setDivert_ata1(newFlightInfo.getDivert_ata());
                        flightInfoResponse.setDivert_eta1(newFlightInfo.getDivert_eta());
                        flightInfoResponse.setDivert_etd1(newFlightInfo.getDivert_etd());
                        flightInfoResponse.setDivert_atd1(newFlightInfo.getDivert_atd());
                    }
                    //行李转盘
                    flightInfoResponse.setStopovert_baggageID(newFlightInfo.getBaggageID());
                    flightInfoResponse.setStopovert_Temp(StringUtils.isBlank(newFlightInfo.getDestTemp()) ?
                            "" : newFlightInfo.getDestTemp().replaceAll("C\\([0-9]+F\\)", "℃"));
                    flightInfoResponse.setStopovert_weather(newFlightInfo.getDestWeather());
                    flightInfoResponse.setStopovert_pm(newFlightInfo.getDestpm());
                    FlightWeatherInfoEnum stopovertWeather = FlightWeatherInfoEnum.formatWeatherStatus(flightInfoResponse.getStopovert_weather());
                    flightInfoResponse.setStopovertWeatherPic(stopovertWeather.getWeatherPictureUrl());
                    //经停站的到达时间补充
                    //计划到达
                    if (!StringUtils.isBlank(newFlightInfo.getEta())) {
                        //存在预到
                        flightInfoResponse.setStopover_eta(newFlightInfo.getEta());
                    } else {
                        if (StringUtils.isBlank(flightInfoResponse.getStopover_eta())) {
                            flightInfoResponse.setStopover_eta(newFlightInfo.getSta());
                        }
                    }
                    //实际到达
                    if (StringUtils.isBlank(flightInfoResponse.getStopover_ata())) {
                        flightInfoResponse.setStopover_ata(newFlightInfo.getAta());
                    }
                    //如果a-b段有延误原因
                    if (StringUtils.isNotBlank(newFlightInfo.getDelay_reason()) && "延误".equals(flightInfoResponse.getFlight_status())) {
                        flightInfoResponse.setDelay_reason(newFlightInfo.getDelay_reason());
                    }
                    if ("取消".equals(newFlightInfo.getFlight_status())) {
                        cancelAll++;
                    }
                }
            }
            //B-C段信息不全
            ptFlightStatusReq = new PtFlightStatusReq();
            ptFlightStatusReq.setFlightNo(flightInfoResponse.getFlight_no());
            ptFlightStatusReq.setFlightDateLocal(flightInfoResponse.getFlight_date());
            ptFlightStatusReq.setDepartureAirport(flightInfoResponse.getStopover_station());
            ptFlightStatusReq.setArrivalAirport(flightInfoResponse.getArrival_airport());
            ptFlightStatusReq.setIp(ip);
            FlightInfoResponse bToCs = travellerHttpApi.searchFlightDynamicsInfo(ptFlightStatusReq, null);
            if (WSEnum.SUC000000.getResultCode().equals(bToCs.getCode())) {
                List<NewFlightInfo> newFlightInfos = bToCs.getData();
                if (CollectionUtils.isNotEmpty(newFlightInfos)) {
                    NewFlightInfo newFlightInfo = newFlightInfos.get(0);
                    //B-C存在备降
                    if (!StringUtils.isBlank(newFlightInfo.getDivert_airport())) {
                        flightInfoResponse.setDivert_airport2(newFlightInfo.getDivert_airport());
                        flightInfoResponse.setDivert_weather2(newFlightInfo.getDivert_weather());
                        flightInfoResponse.setDivert_temp2(newFlightInfo.getDivert_temp());
                        flightInfoResponse.setDivert_pm2(newFlightInfo.getDivert_pm());
                        flightInfoResponse.setDivert_airPortName2(null == localCacheService.getLocalAirport(newFlightInfo.getDivert_airport()) ? "" : localCacheService.getLocalAirport(newFlightInfo.getDivert_airport()).getCityName() +
                                localCacheService.getLocalAirport(newFlightInfo.getDivert_airport()).getAirPortName());
                        flightInfoResponse.setDivert_ata2(newFlightInfo.getDivert_ata());
                        flightInfoResponse.setDivert_eta2(newFlightInfo.getDivert_eta());
                        flightInfoResponse.setDivert_etd2(newFlightInfo.getDivert_etd());
                        flightInfoResponse.setDivert_atd2(newFlightInfo.getDivert_atd());
                    }
                    //B-C缺少信息
                    flightInfoResponse.setStopovert_boardGate(newFlightInfo.getBoardGate());
                    flightInfoResponse.setStopovert_checkinTable(newFlightInfo.getCheckinTable());
                    flightInfoResponse.setStopovert_Temp(StringUtils.isBlank(newFlightInfo.getDeptTemp()) ? "" :
                            newFlightInfo.getDeptTemp().replaceAll("C\\([0-9]+F\\)", "℃"));
                    flightInfoResponse.setStopovert_weather(newFlightInfo.getDeptWeather());
                    flightInfoResponse.setStopovert_pm(newFlightInfo.getDeptpm());
                    FlightWeatherInfoEnum stopovertWeather = FlightWeatherInfoEnum.formatWeatherStatus(flightInfoResponse.getStopovert_weather());
                    flightInfoResponse.setStopovertWeatherPic(stopovertWeather.getWeatherPictureUrl());
                    //经停站的起飞时间补充
                    if (!StringUtils.isBlank(newFlightInfo.getEtd())) {
                        flightInfoResponse.setStopover_etd(newFlightInfo.getEtd());
                    } else {
                        if (StringUtils.isBlank(flightInfoResponse.getStopover_etd())) {
                            flightInfoResponse.setStopover_etd(newFlightInfo.getStd());
                        }
                    }
                    if (StringUtils.isBlank(flightInfoResponse.getStopover_atd())) {
                        flightInfoResponse.setStopover_atd(newFlightInfo.getAtd());
                    }
                    //如果b-c段有延误原因，则a-c段也有延误原因
                    if(StringUtils.isNotBlank(newFlightInfo.getDelay_reason())){
                        flightInfoResponse.setDelay_reason(newFlightInfo.getDelay_reason());
                    }
                    if ("取消".equals(newFlightInfo.getFlight_status())) {
                        cancelAll++;
                    }
                }
            }
            if (cancelAll == 2) {
                flightInfoResponse.setIs_interline("N");
            }
            //有经停，时间转换
            AirPortInfoDto stopAirPort = localCacheService.getLocalAirport(flightInfoResponse.getStopover_station());
            if (null != stopAirPort && !StringUtils.isBlank(stopAirPort.getCityTimeZone().trim())) {
                int timeZone = Integer.parseInt(stopAirPort.getCityTimeZone().trim());
                flightInfoResponse.setStopTimeZone(timeZone);
                //不是中国地区需要转换时间
                if (timeZone != 8) {
                    //经停航班的预计/计划起飞时间
                    flightDate = flightInfoResponse.getStopover_etd();
                    flightInfoResponse.setStopover_etd(flightDate);
                    if (!StringUtils.isBlank(flightInfoResponse.getStopover_atd())) {
                        //经停航班的实际起飞时间
                        flightDate = flightInfoResponse.getStopover_atd();
                        flightInfoResponse.setStopover_atd(flightDate);
                    }
                    //经停航班的计划/预计降落时间
                    flightDate = flightInfoResponse.getStopover_eta();
                    flightInfoResponse.setStopover_eta(flightDate);
                    if (!StringUtils.isBlank(flightInfoResponse.getStopover_ata())) {
                        //经停航班的实际降落时间
                        flightDate = flightInfoResponse.getStopover_ata();
                        flightInfoResponse.setStopover_ata(flightDate);
                    }
                }
            }
            //第一段备降地时间修改
            AirPortInfoDto divertAirPort1 = localCacheService.getLocalAirport(flightInfoResponse.getDivert_airport1());
            if (null != divertAirPort1 && !StringUtils.isBlank(divertAirPort1.getCityTimeZone().trim())) {
                int timeZone1 = Integer.parseInt(divertAirPort1.getCityTimeZone().trim());
                //非中国地区需要转换时间
                if (timeZone1 != 8) {
                    //经停航班前半段备降预计/计划起飞时间
                    flightDate = flightInfoResponse.getDivert_etd1();
                    flightInfoResponse.setDivert_etd1(flightDate);
                    if (!StringUtils.isBlank(flightInfoResponse.getDivert_atd1())) {
                        //经停航班前半段实际起飞时间
                        flightDate = flightInfoResponse.getDivert_atd1();
                        flightInfoResponse.setDivert_atd1(flightDate);
                    }
                    //经停航班前半段计划/预计降落时间
                    flightDate = flightInfoResponse.getDivert_eta1();
                    flightInfoResponse.setDivert_eta1(flightDate);
                    if (!StringUtils.isBlank(flightInfoResponse.getDivert_ata1())) {
                        //经停航班前半段实际降落时间
                        flightDate = flightInfoResponse.getDivert_ata1();
                        flightInfoResponse.setDivert_ata1(flightDate);
                    }
                }
            }
            //第二段备降地时间修改
            AirPortInfoDto divertAirPort2 = localCacheService.getLocalAirport(flightInfoResponse.getDivert_airport2());
            if (null != divertAirPort2 && !StringUtils.isBlank(divertAirPort2.getCityTimeZone().trim())) {
                int timeZone2 = Integer.parseInt(divertAirPort2.getCityTimeZone().trim());
                if (timeZone2 != 8) {
                    //经停航班后半段备降预计/计划起飞时间
                    flightDate = flightInfoResponse.getDivert_etd2();
                    flightInfoResponse.setDivert_etd2(flightDate);
                    if (!StringUtils.isBlank(flightInfoResponse.getDivert_atd2())) {
                        //经停航班后半段备降实际起飞时间
                        flightDate = flightInfoResponse.getDivert_atd2();
                        flightInfoResponse.setDivert_atd2(flightDate);
                    }
                    //经停航班后半段备降预计/计划降落时间
                    flightDate = flightInfoResponse.getDivert_eta2();
                    flightInfoResponse.setDivert_eta2(flightDate);
                    if (!StringUtils.isBlank(flightInfoResponse.getDivert_ata2())) {
                        //经停航班后半段备降实际降落时间
                        flightDate = flightInfoResponse.getDivert_ata2();
                        flightInfoResponse.setDivert_ata2(flightDate);
                    }
                }
            }
        } else if (StringUtils.isNotBlank(flightInfoResponse.getDivert_airport())) {
            //直接的备降，时间转换
            AirPortInfoDto divertAirPort = localCacheService.getLocalAirport(flightInfoResponse.getDivert_airport());
            if (null != divertAirPort && !StringUtils.isBlank(divertAirPort.getCityTimeZone().trim())) {
                int timeZone = Integer.parseInt(divertAirPort.getCityTimeZone().trim());
                if (timeZone != 8) {
                    //备降航班预计/计划起飞时间
                    flightDate = flightInfoResponse.getDivert_etd();
                    flightInfoResponse.setDivert_etd(flightDate);
                    if (!StringUtils.isBlank(flightInfoResponse.getDivert_atd())) {
                        //备降航班实际起飞时间
                        flightDate = flightInfoResponse.getDivert_atd();
                        flightInfoResponse.setDivert_atd(flightDate);
                    }
                    //备降航班预计/计划降落时间
                    flightDate = flightInfoResponse.getDivert_eta();
                    flightInfoResponse.setDivert_eta(flightDate);
                    if (!StringUtils.isBlank(flightInfoResponse.getDivert_ata())) {
                        //备降航班实际降落时间
                        flightDate = flightInfoResponse.getDivert_ata();
                        flightInfoResponse.setDivert_ata(flightDate);
                    }
                }
            }
        }
        if (null != deptAirPoty && !StringUtils.isBlank(deptAirPoty.getCityTimeZone().trim())) {
            int depTimeZone = Integer.parseInt(deptAirPoty.getCityTimeZone().trim());
            flightInfoResponse.setDepTimeZone(depTimeZone);
            if (depTimeZone != 8) {
                //航班计划起飞时间
                flightDate = flightInfoResponse.getStd();
                flightInfoResponse.setStd(flightDate);
                if (!StringUtils.isBlank(flightInfoResponse.getEtd())) {
                    //航班预计起飞时间
                    flightDate = flightInfoResponse.getEtd();
                    flightInfoResponse.setEtd(flightDate);
                }
                if (!StringUtils.isBlank(flightInfoResponse.getAtd())) {
                    //航班实际起飞时间
                    flightDate = flightInfoResponse.getAtd();
                    flightInfoResponse.setAtd(flightDate);
                }
            }
        }
        if (null != arrAirPort && !StringUtils.isBlank(arrAirPort.getCityTimeZone().trim())) {
            int arrTimeZone = Integer.parseInt(arrAirPort.getCityTimeZone().trim());
            flightInfoResponse.setArrTimeZone(arrTimeZone);
            if (arrTimeZone != 8) {
                //计划到达时间
                flightDate = flightInfoResponse.getSta();
                flightInfoResponse.setSta(flightDate);
                if (!StringUtils.isBlank(flightInfoResponse.getEta())) {
                    //预计到达时间
                    flightDate = flightInfoResponse.getEta();
                    flightInfoResponse.setEta(flightDate);
                }
                if (!StringUtils.isBlank(flightInfoResponse.getAta())) {
                    //实际到达时间
                    flightDate = flightInfoResponse.getAta();
                    flightInfoResponse.setAta(flightDate);
                }
            }
        }
        return flightInfoResponse;
    }


    /**
     * @Description:
     * @Param: No such property: code for class: Script1
     * @return: 单独查询航班是否关注 ffpId flightNo flightDate
     * @Author: zhangwanli
     * @Date: 2019/7/19
     */
    public String attention(BaseReq<NewFlightStatusRequest> requestBase, String flightDate, String flightNo, String depAirportCode, String arrAirportCode, String ip) {
        BasicBaseReq<AttentionFlightRequest> requestBaseResp = new BasicBaseReq<>();
        AttentionFlightRequest attentionDto = new AttentionFlightRequest();
        requestBaseResp.setIp(ip);
        requestBaseResp.setVersion(HandlerConstants.BASIC_INFO_VERSION);
        requestBaseResp.setChannelCode(requestBase.getChannelCode());
        attentionDto.setFfpId(String.valueOf(requestBase.getRequest().getFfpId()));
        attentionDto.setFfpCardNo(requestBase.getRequest().getFfpCardNo());
        attentionDto.setFlightNo(flightNo);
        attentionDto.setFlightDate(flightDate);
        attentionDto.setDepartureAirport(depAirportCode);
        attentionDto.setArrivalAirport(arrAirportCode);
        requestBaseResp.setRequest(attentionDto);
        String attentionState = null;
        HttpResult result = this.doPostClient(requestBaseResp, HandlerConstants.BASIC_INFO_URL + HandlerConstants.ATTENTION_FLIGHT_AIRLINE);
        if (!result.isResult()) {
            return attentionState;
        }
        if (!StringUtil.isNullOrEmpty(result.getResponse())) {
            FollowAirLineResponse followResponse = JsonMapper.buildNonNullMapper().fromJson(result.getResponse(), FollowAirLineResponse.class);
            if (null != followResponse && "10001".equals(followResponse.getResultCode()) && null != followResponse.getResult() && !followResponse.getResult().isEmpty()) {
                List<FollowAirLineInfo> data = followResponse.getResult();
                if ("1".equals(data.get(0).getConcern())) {
                    attentionState = "1";
                }
            }
        }
        return attentionState;
    }

    private int getEtTimeOver(String std, String sta, String etd, String eta, int deptZone, int arrZone) {
        int etTimeOver = 0;

        String dept = String.valueOf(deptZone);
        String arr = String.valueOf(arrZone);
        if (!StringUtil.isNullOrEmpty(etd) && !StringUtil.isNullOrEmpty(eta)) {
            Date date = DateUtils.toTargetDate(eta, arr, dept);
            String dateString = DateUtils.getDateString(date);
            int etTime = DateUtils.dateDiff(etd, dateString, "yyyy-MM-dd") - 1;
            etTimeOver = etTime < 0 ? 0 : etTime;
        } else if (!StringUtil.isNullOrEmpty(std) && !StringUtil.isNullOrEmpty(sta)) {
            Date date = DateUtils.toTargetDate(sta, arr, dept);
            String dateString = DateUtils.getDateString(date);
            int etTime = DateUtils.dateDiff(std, dateString, "yyyy-MM-dd") - 1;
            etTimeOver = etTime < 0 ? 0 : etTime;
        }

        return etTimeOver;
    }

    /**
     * @Description: 经停航班状态设置
     * @Param: No such property: code for class: Script1
     * @return:
     * @Author: zhangwanli
     * @Date: 2019/11/14
     */
    private String getFlightStatus(List<NewFlightInfo> flightInfo, BaseReq<NewFlightStatusRequest> requestBaseResp) {
        String stopFlightStatus = null;
        //参数非null
        if (CollectionUtils.isNotEmpty(flightInfo) && null != requestBaseResp && null != requestBaseResp.getRequest()) {
            for (NewFlightInfo objDatum : flightInfo) {
                //找到第一段航班
                if (objDatum.getDeparture_airport().equalsIgnoreCase(requestBaseResp.getRequest().getDepartureAirport())) {
                    stopFlightStatus = objDatum.getFlight_status();
                    for (NewFlightInfo flightInfos : flightInfo) {
                        //找到第二段航班
                        if (flightInfos.getArrival_airport().equalsIgnoreCase(requestBaseResp.getRequest().getArrivalAirport())) {
                            if ("到达".equalsIgnoreCase(stopFlightStatus) && "计划".equalsIgnoreCase(objDatum.getFlight_status())) {
                                stopFlightStatus = "经停中";
                                return stopFlightStatus;
                            } else if ("计划".equalsIgnoreCase(objDatum.getFlight_status())) {
                                return stopFlightStatus;
                            } else {
                                stopFlightStatus = objDatum.getFlight_status();
                                return stopFlightStatus;
                            }
                        }
                    }
                }
            }
        }
        return stopFlightStatus;
    }

    private boolean getCompareTo(String param) {
        boolean result = false;
        if (!StringUtil.isNullOrEmpty(param)) {
            if (param.length() > 5 && handConfig.getOnTimeRate().length() > 5) {
                String substring = param.substring(0, 5);
                String substring1 = handConfig.getOnTimeRate().substring(0, 5);
                BigDecimal dataA = new BigDecimal(substring);
                BigDecimal dataB = new BigDecimal(substring1);
                int i = dataA.compareTo(dataB);
                if (i < 0) {
                    result = true;
                }
            } else {
                result = true;
            }
        }
        return result;
    }

}
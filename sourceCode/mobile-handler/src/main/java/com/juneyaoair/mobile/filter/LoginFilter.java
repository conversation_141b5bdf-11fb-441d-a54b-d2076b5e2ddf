package com.juneyaoair.mobile.filter;

import com.google.gson.reflect.TypeToken;
import com.juneyaoair.appenum.ChannelCodeEnum;
import com.juneyaoair.appenum.WSEnum;
import com.juneyaoair.appenum.member.MemberLevelEnum;
import com.juneyaoair.baseclass.common.base.DeviceInfo;
import com.juneyaoair.baseclass.common.request.BaseReq;
import com.juneyaoair.baseclass.common.response.BaseResp;
import com.juneyaoair.baseclass.member.comm.ThirdAccountInfo;
import com.juneyaoair.baseclass.member.response.LoginResponse;
import com.juneyaoair.baseclass.response.crm.MemberLoginResponse;
import com.juneyaoair.exception.CommonException;
import com.juneyaoair.mobile.external.request.sso.LoginResult;
import com.juneyaoair.mobile.external.request.sso.UserInfo;
import com.juneyaoair.mobile.external.service.AuthService;
import com.juneyaoair.mobile.handler.config.bean.HandConfig;
import com.juneyaoair.mobile.interceptor.bean.MobileTokenUserInfo;
import com.juneyaoair.mobile.interceptor.bean.MobileVerifyTokenResp;
import com.juneyaoair.mobile.interceptor.utils.TokenUtils;
import com.juneyaoair.utils.IPUtil;
import com.juneyaoair.utils.http.ReqUtil;
import com.juneyaoair.utils.json.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.WebApplicationContext;
import org.springframework.web.context.support.WebApplicationContextUtils;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletOutputStream;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @description 登陆后过滤敏感信息
 * @date 2018/10/8  13:34.
 */
@Slf4j
@Component
public class LoginFilter implements Filter {

    private static final String UTF_8 = "UTF-8";

    private AuthService authService;

    @Autowired
    private HandConfig handConfig;

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        log.info("【LoginFilter过滤器】系统自动初始化拦截过滤器..........");
        WebApplicationContext context = WebApplicationContextUtils
                .getRequiredWebApplicationContext(filterConfig.getServletContext());
        this.authService = context.getBean(AuthService.class);
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        HttpServletRequest httpServletRequest = (HttpServletRequest) request;
        HttpServletResponse httpServletResponse = (HttpServletResponse) response;
        String uri = httpServletRequest.getRequestURI();
        String contentType = request.getContentType();
        String ip = IPUtil.getIpAddr(httpServletRequest);
        // 非json请求 放行
        if (!ReqUtil.isJsonReq(httpServletRequest)) {
            log.info("【LoginFilter过滤器】,IP地址:{},请求路径{},contentType:{},非指定的json请求", ip, uri, contentType);
            chain.doFilter(request, response);
            return;
        }
        String channelCode = httpServletRequest.getHeader("channelCode");
        String clientVersion = httpServletRequest.getHeader("clientVersion");
        try (ServletOutputStream out = response.getOutputStream()){
            //请求request处理
            JsonParameterRequestWrapper requestWrapper = new JsonParameterRequestWrapper((HttpServletRequest) request);

            String bodyMessage = requestWrapper.getBodyMessage();
            BaseReq<DeviceInfo> baseReq = (BaseReq<DeviceInfo>) JsonUtil.jsonToBean(bodyMessage, new TypeToken<BaseReq<DeviceInfo>>() {
            }.getType());
            //返回参数处理
            ResponseWrapper responseWrapper = new ResponseWrapper(httpServletResponse);
            chain.doFilter(requestWrapper, responseWrapper);
            //取返回的json串
            byte[] bytes = responseWrapper.getBytes();
            String result = new String(bytes, UTF_8);
            //返回给前端显示数据
            BaseResp<LoginResponse> loginResponseResp = (BaseResp<LoginResponse>) JsonUtil.jsonToBean(result, new TypeToken<BaseResp<LoginResponse>>() {
            }.getType());
            // 非返回成功 直接返回数据
            if (!WSEnum.SUCCESS.getResultCode().equals(loginResponseResp.getResultCode())) {
                out.write(result.getBytes(UTF_8));
                return;
            }
            //获取用户信息
            LoginResponse loginResponse = loginResponseResp.getObjData();
            MemberLoginResponse memberLoginResponse = getMemberLoginResponse(loginResponse.getMemberLoginResponse());
            String token;
            // 2025-06-30 SSO TOKEN改造 MOBILE渠道且当前版本小于启用版本保持原逻辑
            if (!TokenUtils.getSsoTokenFlag(channelCode, clientVersion)) {
                //根据request获取用户的设备id
                String deviceId = getDeviceIdIdByDeviceInfo(baseReq);
                // 账号状态校验，判断用户账号是否没有被封禁
                MobileVerifyTokenResp mobileVerifyTokenResp = TokenUtils.verifyIsClosedNewest(String.valueOf(memberLoginResponse.getId()), memberLoginResponse.getMemberID(), (HttpServletRequest) request);
                if (!mobileVerifyTokenResp.getCode().equals("success")) {
                    BaseResp baseResp = new BaseResp();
                    baseResp.setResultCode(WSEnum.TONGDUN_FAIL_LOGIN.getResultCode());
                    baseResp.setResultInfo(mobileVerifyTokenResp.getMessage());
                    baseResp.setErrorInfo(mobileVerifyTokenResp.getMessage());
                    out.write(JsonUtil.objectToJson(baseResp).getBytes(UTF_8));
                    return;
                }
                // 根据用户id，会员卡号，设备id保存到生成token，并根据用户id和设备id保存到redis中
                MobileTokenUserInfo mobileTokenUserInfo = new MobileTokenUserInfo();
                mobileTokenUserInfo.setUserId(memberLoginResponse.getId());
                mobileTokenUserInfo.setMemberId(memberLoginResponse.getMemberID());
                mobileTokenUserInfo.setDeviceId(deviceId == null ? "" : deviceId);
                token = TokenUtils.mobileCreateTokenAndSaveRedis(channelCode, mobileTokenUserInfo);
            } else {
                // 生成token需要使用的对象
                UserInfo userInfo = new UserInfo();
                userInfo.setFfpId(String.valueOf(memberLoginResponse.getId()));
                userInfo.setFfpNo(memberLoginResponse.getMemberID());
                ThirdAccountInfo thirdAccountInfo = loginResponse.getThirdPartInfo();
                if (thirdAccountInfo != null) {
                    // 微信渠道
                    if (ChannelCodeEnum.WXAPP.getChannelCode().equals(channelCode) && ChannelCodeEnum.CHECKIN.getChannelCode().equals(channelCode)) {
                        userInfo.setOpenId(thirdAccountInfo.getOpenId());
                    }
                    // 支付宝渠道
                    else if (ChannelCodeEnum.MP_ALIPAY.getChannelCode().equals(channelCode) ||
                            ChannelCodeEnum.ALIPAY_PLUG.getChannelCode().equals(channelCode) ||
                            ChannelCodeEnum.ALIPAY_WINDOW.getChannelCode().equals(channelCode)) {
                        userInfo.setUserId(thirdAccountInfo.getUid());
                    }
                }
                LoginResult loginResult = authService.getToken(channelCode, userInfo);
                // 校验是否封禁
                if (!loginResult.isAccountStatus()) {
                    BaseResp baseResp = new BaseResp();
                    baseResp.setResultCode(WSEnum.INVALID_TOKEN.getResultCode());
                    baseResp.setResultInfo("该账号已封禁");
                    baseResp.setErrorInfo("该账号已封禁");
                    response.getOutputStream().write(JsonUtil.objectToJson(baseResp).getBytes(StandardCharsets.UTF_8));
                    return;
                }
                token = loginResult.getAccessToken();
            }
            memberLoginResponse.setToken(token);
            loginResponse.setToken("");
            loginResponse.setExpiryTime("");
            loginResponse.setMemberLoginResponse(memberLoginResponse);
            //header存token
            responseWrapper.setHeader("token", token);
            responseWrapper.setHeader("Access-Control-Expose-Headers", "token");
            log.info("【LoginFilter过滤器】改动后数据：" + JsonUtil.objectToJson(loginResponseResp));
            out.write(JsonUtil.objectToJson(loginResponseResp).getBytes(UTF_8));
        } catch (CommonException ce) {
            BaseResp resp = setResult(ce.getResultCode(), ce.getErrorMsg());
            sendResp(response, resp);
        } catch (Exception e) {
            //filter异常的时候
            log.error("【LoginFilter过滤器】,IP地址:{},请求路径{},发生异常信息", ip, uri, e);
            BaseResp resp = setResult(WSEnum.ERROR.getResultCode(), "请求异常！");
            sendResp(response, resp);
        }
    }

    private MemberLoginResponse getMemberLoginResponse(MemberLoginResponse originMemberInfo) {
        MemberLoginResponse memberLoginResponse = new MemberLoginResponse();
        memberLoginResponse.setName(originMemberInfo.getName());
        memberLoginResponse.setTitle(originMemberInfo.getTitle());
        memberLoginResponse.setLevelName(MemberLevelEnum.findLevelNameByLevelCode(originMemberInfo.getMemberLevelCode()));
        memberLoginResponse.setMemberLevelCode(originMemberInfo.getMemberLevelCode());
        memberLoginResponse.setcLastName(originMemberInfo.getcLastName());
        memberLoginResponse.setcFirstName(originMemberInfo.getcFirstName());
        memberLoginResponse.seteFirstName(originMemberInfo.geteFirstName());
        memberLoginResponse.seteLastName(originMemberInfo.geteLastName());
        memberLoginResponse.setMemberID(originMemberInfo.getMemberID());
        memberLoginResponse.setMemberTel(originMemberInfo.getMemberTel());
        memberLoginResponse.setSex(originMemberInfo.getSex());
        memberLoginResponse.setMemberEmail(originMemberInfo.getMemberEmail());
        //TODO 为了兼容新老手机网站，暂时返回这些参数
        memberLoginResponse.setId(originMemberInfo.getId());
        memberLoginResponse.setLoginKeyInfo(originMemberInfo.getLoginKeyInfo());
        memberLoginResponse.setCustomerCertificateInfos(originMemberInfo.getCustomerCertificateInfos());
        return memberLoginResponse;
    }

    /**
     * 根据request获取设备id
     *
     * @param baseReq
     * @return 设备id
     * @throws IOException
     */
    private String getDeviceIdIdByDeviceInfo(BaseReq<DeviceInfo> baseReq) throws IOException {
        DeviceInfo deviceInfo = baseReq.getRequest();
        return deviceInfo.getDeviceId();
    }

    @Override
    public void destroy() {
        log.info("【LoginFilter过滤器】系统自动销毁拦截过滤器..........");
    }

    /**
     * 设置输出的结果
     *
     * @param msg
     * @return
     */
    private BaseResp setResult(String code, String msg) {
        BaseResp resp = new BaseResp();
        resp.setResultCode(code);
        resp.setResultInfo(msg);
        return resp;
    }

    /**
     * 输出
     *
     * @param response
     * @param obj
     * @throws IOException
     */
    private void sendResp(ServletResponse response, BaseResp obj) throws IOException {
        response.setContentType("application/json; charset=utf-8");
        PrintWriter writer = response.getWriter();
        writer.print(JsonUtil.objectToJson(obj));
        writer.close();
        response.flushBuffer();
    }

}

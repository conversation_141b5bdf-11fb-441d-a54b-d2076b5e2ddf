package com.juneyaoair.mobile.external.request.sso;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Description; 保存token中用户信息
 * @Version: V1.0
 * created by
 */
@Data
public class UserInfo {

    @NotNull(message = "请求时间戳不能为空")
    @ApiModelProperty(value = "请求时间戳（毫秒值）", required = true)
    private Long timestamp;

    @NotBlank(message = "会员ID不能为空")
    @ApiModelProperty(value = "会员ID", required = true)
    private String ffpId;

    @NotBlank(message = "会员卡号不能为空")
    @ApiModelProperty(value = "会员卡号", required = true)
    private String ffpNo;

    @ApiModelProperty(value = "用户openId(如：微信openId)")
    private String openId;

    @ApiModelProperty(value = "用户userId(如：支付宝userId)")
    private String userId;

}

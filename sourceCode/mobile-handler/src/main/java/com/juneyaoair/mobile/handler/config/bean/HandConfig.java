package com.juneyaoair.mobile.handler.config.bean;

import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.juneyaoair.baseclass.DCEPayment.common.DCEPayConfig;
import com.juneyaoair.baseclass.activity.ActivityConfig;
import com.juneyaoair.baseclass.activity.MaoTaiConfig;
import com.juneyaoair.baseclass.activity.SmsTemplate;
import com.juneyaoair.baseclass.antCloud.config.AntConfig;
import com.juneyaoair.baseclass.av.common.*;
import com.juneyaoair.baseclass.baggagestandard.common.BaggageStandardConfig;
import com.juneyaoair.baseclass.captcha.CaptchaSendIntervalConfig;
import com.juneyaoair.baseclass.common.BookCert;
import com.juneyaoair.baseclass.common.HeadTitleInfo;
import com.juneyaoair.baseclass.common.base.*;
import com.juneyaoair.baseclass.common.response.ImageResponse;
import com.juneyaoair.baseclass.common.response.ProModule;
import com.juneyaoair.baseclass.flight.common.FlightReminderV2;
import com.juneyaoair.baseclass.individualizationSetMeal.response.MealImage;
import com.juneyaoair.baseclass.insurance.apollo.InsuranceConfig;
import com.juneyaoair.baseclass.member.comm.AssociateMemberInfo;
import com.juneyaoair.baseclass.member.comm.CoBrandedCardInfo;
import com.juneyaoair.baseclass.member.comm.LogoutPageInfoConfig;
import com.juneyaoair.baseclass.member.comm.VerifyChannel;
import com.juneyaoair.baseclass.member.response.rightsDescriptionResponse;
import com.juneyaoair.baseclass.request.order.cancel.CancelOrderReason;
import com.juneyaoair.baseclass.request.order.refund.query.RefundType;
import com.juneyaoair.baseclass.reservation.ReservationConfig;
import com.juneyaoair.baseclass.response.av.BrandRightDto;
import com.juneyaoair.baseclass.response.av.FlightQueryTypeDto;
import com.juneyaoair.baseclass.response.av.MultipleFlightTransferConfig;
import com.juneyaoair.baseclass.response.av.ThemeFlightDetails;
import com.juneyaoair.baseclass.response.console.ThemeSwitchDate;
import com.juneyaoair.baseclass.response.coupons.CouponDefCity;
import com.juneyaoair.baseclass.response.newCheckinSeat.CheckInPolicyConfig;
import com.juneyaoair.baseclass.response.payment.PayMethod;
import com.juneyaoair.baseclass.response.payment.paysetting.Bank;
import com.juneyaoair.baseclass.response.prepaymentBaggage.PrepaymentBaggageDocumentResp;
import com.juneyaoair.baseclass.response.prepaymentBaggage.Pricing;
import com.juneyaoair.baseclass.response.taolx.ParentRegion;
import com.juneyaoair.baseclass.response.taolx.TourDest;
import com.juneyaoair.baseclass.response.taolx.VersionMember;
import com.juneyaoair.baseclass.response.wallet.WalletTextVo;
import com.juneyaoair.baseclass.risk.FraudEscapeConfig;
import com.juneyaoair.baseclass.studentVerify.common.StudentVerifyConfig;
import com.juneyaoair.baseclass.tencentcloud.common.TencentCloudAccount;
import com.juneyaoair.baseclass.transferaccommodation.common.TransferAccommodationConfig;
import com.juneyaoair.baseclass.unlimit.UnlimitedCard2Config;
import com.juneyaoair.baseclass.unlimit.UpgradeCardV2Config;
import com.juneyaoair.mobile.handler.config.JjscConfig;
import com.juneyaoair.mobile.limiter.RateLimiterInfo;
import com.juneyaoair.thirdentity.passengers.common.CertType;
import com.juneyaoair.utils.json.JsonUtil;
import io.github.resilience4j.ratelimiter.RateLimiter;
import io.github.resilience4j.ratelimiter.RateLimiterConfig;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Created by yaocf on 2018/4/18  14:33.
 */
@Data
@Component("handConfig")
public class HandConfig {
    private static final Logger logger = LoggerFactory.getLogger(HandConfig.class);
    @Value("${support_airPort_QR:SHA,PVG}")
    private String support_airPort_QR;//支持二维码的机场城市
    @Value("${aircraftModel:{321:{aircraftCode:321,remark:空客A321(中)},320:{aircraftCode:320,remark:空客A320(中)},789:{aircraftCode:789,remark:波音787(大)}}}")
    private String aircraftModel;  //机型列表
    @Value("${airCompany:{HO:{airComCode:HO,airComName:吉祥,airIcon:https://mediaws.juneyaoair.com/upload/icon/air/ho.png}, MU:{airComCode:MU,airComName:东航,airIcon:https://mediaws.juneyaoair.com/upload/icon/air/mu.png,airComText:该航班由东方航空公司实际承运,吉祥航空股份有限公司缔约承运},3U:{airComCode:3U,airComName:川航,airIcon:https://mediaws.juneyaoair.com/upload/icon/air/mu.png,airComText:该航班由四川航空实际承运,吉祥航空股份有限公司缔约承运}}}")
    private String airCompany;//航司列表
    @Value("${overWrite:Y}")
    private String overWrite;//是否重写天天特价链接
    @Value("${bankNameList:中国银行}")
    private String bankNameList;//银行列表
    @Value("${useGeetestLogin:Y}")
    private String useGeetestLogin;//是否使用极验登录验证
    /**
     * 是否使用极验在线服务
     */
    @Value("${useGeetesOnline:Y}")
    private String useGeetesOnline;
    /**
     * 极验云服务状态检查
     */
    @Value("${byPassStatusUrl:http://bypass.geetest.com/v1/bypass_status.php}")
    private String byPassStatusUrl;
    @Value("${showQuestion:N}")
    private String showQuestion;//行程入口是否显示问卷
    @Value("${blackAndWhite:N}")
    private String blackAndWhite;//黑白屏切换开关
    @ApolloJsonValue("${iosTechnicalDate:{\"startTime\":\"2022-04-19 20:30:00\",\"endTime\":\"2022-04-19 21:00:00\"}}")
    private ThemeSwitchDate iosTechnicalDate;//需要切换黑白屏的日期
    @ApolloJsonValue("${technicalDate:{\"startTime\":\"2022-04-19 20:30:00\",\"endTime\":\"2022-04-19 21:00:00\"}}")
    private ThemeSwitchDate technicalDate;//安卓需要切换黑白屏的日期
    /**
     * 支付时限设置
     */
    @Value("${timeLimit:20}")
    private int timeLimit;
    /**
     * 实名认证上传限制
     */
    @ApolloJsonValue("${authPhotoLimit:{\"accessType\":\"authPhotoLimit\",\"ipLimit\":{\"limitDay\":{\"frequency\":86400,\"accessLimit\":6},\"limitForever\":{\"frequency\":0,\"accessLimit\":50},\"limitMin\":{\"frequency\":0,\"accessLimit\":0}},\"deviceLimit\":{\"limitDay\":{\"frequency\":86400,\"accessLimit\":6},\"limitForever\":{\"frequency\":0,\"accessLimit\":50},\"limitMin\":{\"frequency\":0,\"accessLimit\":0}},\"userLimit\":{\"limitDay\":{\"frequency\":86400,\"accessLimit\":10},\"limitForever\":{\"frequency\":0,\"accessLimit\":60},\"limitMin\":{\"frequency\":0,\"accessLimit\":0}}}}")
    private AccessLimit authPhotoLimit;

    @ApolloJsonValue("${authFilesLimit:{\"accessType\":\"authFilesLimit\",\"ipLimit\":{\"limitDay\":{\"frequency\":86400,\"accessLimit\":6},\"limitForever\":{\"frequency\":0,\"accessLimit\":50},\"limitMin\":{\"frequency\":0,\"accessLimit\":0}},\"deviceLimit\":{\"limitDay\":{\"frequency\":86400,\"accessLimit\":6},\"limitForever\":{\"frequency\":0,\"accessLimit\":50},\"limitMin\":{\"frequency\":0,\"accessLimit\":0}},\"userLimit\":{\"limitDay\":{\"frequency\":86400,\"accessLimit\":10},\"limitForever\":{\"frequency\":0,\"accessLimit\":60},\"limitMin\":{\"frequency\":0,\"accessLimit\":0}}}}")
    private AccessLimit authFilesLimit;

    @ApolloJsonValue("${vipLoungeList:[{\"vipLoungeTitle\":\"贵宾休息室指南\",\"vipLounge\":\"上海浦东机场吉祥航空86号贵宾室\",\"businessHours\":\"05：30起至航班运行结束\",\"describePosition\":\"T2航站楼C80登机口旁\",\"priceUrl\":\"https://mediaws.juneyaoair.com/upload/lounge/lounge.png\"}]}")
    private List<VipLounges> vipLoungeList;

    @ApolloJsonValue("${airportCodeList:[{\"airportCode\":\"PVG\",\"cabinClass\":\"J\"}]}")
    private List<AirportAll> airportCodeList;
    /**
     * 签证-国家-申请人类型-材料
     */
    @Value("${visaMaterial:{\"日本\":{\"2\":{\"desc\":\"在职人员\",\"linkUrl\":\"https://mediaws.juneyaoair.com/visa/jp/job.zip\"},\"3\":{\"desc\":\"自由职业\",\"linkUrl\":\"https://mediaws.juneyaoair.com/visa/jp/free.zip\"},\"4\":{\"desc\":\"退休人员\",\"linkUrl\":\"https://mediaws.juneyaoair.com/visa/jp/retire.zip\"},\"5\":{\"desc\":\"在校学生\",\"linkUrl\":\"https://mediaws.juneyaoair.com/visa/jp/school.zip\"},\"6\":{\"desc\":\"学龄前儿童\",\"linkUrl\":\"https://mediaws.juneyaoair.com/visa/jp/children.zip\"}},\"泰国\":{\"2\":{\"desc\":\"在职人员\",\"linkUrl\":\"https://mediaws.juneyaoair.com/visa/th/th.zip\"},\"3\":{\"desc\":\"自由职业\",\"linkUrl\":\"https://mediaws.juneyaoair.com/visa/th/th.zip\"},\"4\":{\"desc\":\"退休人员\",\"linkUrl\":\"https://mediaws.juneyaoair.com/visa/th/th.zip\"},\"5\":{\"desc\":\"在校学生\",\"linkUrl\":\"https://mediaws.juneyaoair.com/visa/th/th.zip\"},\"6\":{\"desc\":\"学龄前儿童\",\"linkUrl\":\"https://mediaws.juneyaoair.com/visa/th/th.zip\"}},\"台湾\":{\"2\":{\"desc\":\"在职人员\",\"linkUrl\":\"https://mediaws.juneyaoair.com/visa/tw/tw.zip\"},\"3\":{\"desc\":\"自由职业\",\"linkUrl\":\"https://mediaws.juneyaoair.com/visa/tw/tw.zip\"},\"4\":{\"desc\":\"退休人员\",\"linkUrl\":\"https://mediaws.juneyaoair.com/visa/tw/tw.zip\"},\"5\":{\"desc\":\"在校学生\",\"linkUrl\":\"https://mediaws.juneyaoair.com/visa/tw/tw.zip\"},\"6\":{\"desc\":\"学龄前儿童\",\"linkUrl\":\"https://mediaws.juneyaoair.com/visa/tw/tw.zip\"}},\"新加坡\":{\"2\":{\"desc\":\"在职人员\",\"linkUrl\":\"https://mediaws.juneyaoair.com/visa/sg/sg.zip\"},\"3\":{\"desc\":\"自由职业\",\"linkUrl\":\"https://mediaws.juneyaoair.com/visa/sg/sg.zip\"},\"4\":{\"desc\":\"退休人员\",\"linkUrl\":\"https://mediaws.juneyaoair.com/visa/sg/sg.zip\"},\"5\":{\"desc\":\"在校学生\",\"linkUrl\":\"https://mediaws.juneyaoair.com/visa/sg/sg.zip\"},\"6\":{\"desc\":\"学龄前儿童\",\"linkUrl\":\"https://mediaws.juneyaoair.com/visa/sg/sg.zip\"}},\"菲律宾\":{\"2\":{\"desc\":\"在职人员\",\"linkUrl\":\"https://mediaws.juneyaoair.com/visa/ph/ph.zip\"},\"3\":{\"desc\":\"自由职业\",\"linkUrl\":\"https://mediaws.juneyaoair.com/visa/ph/ph.zip\"},\"4\":{\"desc\":\"退休人员\",\"linkUrl\":\"https://mediaws.juneyaoair.com/visa/ph/ph.zip\"},\"5\":{\"desc\":\"在校学生\",\"linkUrl\":\"https://mediaws.juneyaoair.com/visa/ph/ph.zip\"},\"6\":{\"desc\":\"学龄前儿童\",\"linkUrl\":\"https://mediaws.juneyaoair.com/visa/ph/ph.zip\"}},\"芬兰\":{\"2\":{\"desc\":\"在职人员\",\"linkUrl\":\"https://mediaws.juneyaoair.com/visa/fi/fi.zip\"},\"3\":{\"desc\":\"自由职业\",\"linkUrl\":\"https://mediaws.juneyaoair.com/visa/fi/fi.zip\"},\"4\":{\"desc\":\"退休人员\",\"linkUrl\":\"https://mediaws.juneyaoair.com/visa/fi/fi.zip\"},\"5\":{\"desc\":\"在校学生\",\"linkUrl\":\"https://mediaws.juneyaoair.com/visa/fi/fi.zip\"},\"6\":{\"desc\":\"学龄前儿童\",\"linkUrl\":\"https://mediaws.juneyaoair.com/visa/fi/fi.zip\"}},\"缅甸\":{\"2\":{\"desc\":\"在职人员\",\"linkUrl\":\"https://mediaws.juneyaoair.com/visa/mm/mm.zip\"},\"3\":{\"desc\":\"自由职业\",\"linkUrl\":\"https://mediaws.juneyaoair.com/visa/mm/mm.zip\"},\"4\":{\"desc\":\"退休人员\",\"linkUrl\":\"https://mediaws.juneyaoair.com/visa/mm/mm.zip\"},\"5\":{\"desc\":\"在校学生\",\"linkUrl\":\"https://mediaws.juneyaoair.com/visa/mm/mm.zip\"},\"6\":{\"desc\":\"学龄前儿童\",\"linkUrl\":\"https://mediaws.juneyaoair.com/visa/mm/mm.zip\"}}}}")
    private String visaMaterial;
    /**
     * 实名认证照是否验证设备信息
     */
    @Value("${authPhotoCheckDevice:Y}")
    private String authPhotoCheckDevice;

    /**
     * 客户端请求允许的时间误差 单位 秒
     */
    @Value("${accessDiffLimit:180}")
    private int accessDiffLimit;
    @Value("${cabinClass:{\"F\":\"F,P,O\",\"J\":\"J,C,D,A,R,I\",\"Y\":\"Y,B,M,U,H,Q,V,W,S,T,Z,E,G,L,N,X\"}}")
    private String cabinClass;
    /**
     * 连接超时时间 单位毫秒
     */
    @Value("${connectTimeout:5000}")
    private int connectTimeout;
    /**
     * 数据读取超时时间 单位毫秒
     */
    @Value("${readTimeout:80000}")
    private int readTimeout;
    /**
     * 航班查询时是否使用redis
     */
    @Value("${avReadRedis:Y}")
    private String avReadRedis;
    /**
     * 是否使用同盾
     */
    @Value("${useTongDun:Y}")
    private String useTongDun;
    /**
     * 发送短信是否使用同盾
     */
    @Value("${smsUseTongDun:Y}")
    private String smsUseTongDun;
    /**
     * 发送短信是否使用极验
     */
    @Value("${smsUseGeetest:Y}")
    private String smsUseGeetest;
    /**
     * 是否使用候补过滤
     */
    @Value("${useWaitingLimit:Y}")
    private String useWaitingLimit;
    /**
     * 微信小程序配置
     */
    @Value("${wxSmallAppId:wx534a6f359f266888}")
    private String wxSmallAppId;
    @Value("${wxSmallSecret:9524accccad6985f67163c97a2f5be11}")
    private String wxSmallSecret;

    @Value("${appId:b8025e81-95db-48fd-8e7a-ad2c73136b4e}")
    private String appId;
    /**
     * 值机小程序
     */
    @Value("${ciSmallAppId:wx1730b842792e22ac}")
    private String ciSmallAppId;
    @Value("${ciSmallSecret:be602d6a74bd1a3a75e5c7cf298a20ae}")
    private String ciSmallSecret;
    /**
     * 微信公众号配置
     */
    @Value("${wxPublicAppId:wx42f49ced634e312b}")
    private String wxPublicAppId;
    @Value("${wxPublicSecret:7830f9555363c2e338f893048c008448}")
    private String wxPublicSecret;
    /**
     * Y舱展示个数
     */
    @Value("${ycabinLimit:3}")
    private int ycabinLimit;

    /**
     * 改期时Y舱展示数量
     */
    @Value("${changeYCabinLimit:5}")
    private int changeYCabinLimit;
    /**
     * J舱展示个数
     */
    @Value("${jcabinLimit:2}")
    private int jcabinLimit;
    /**
     * 预定支持的证件类型
     */
    @ApolloJsonValue("${bookPersonCert:{\"international\":\"PP\",\"domestic\":\"NI,ORI,MTP,PP,NIPP,MIL,GM,JC\",\"outland\":\"HTPP,PP,ORI,TPP,MTP\"}}")
    private BookCert bookPersonCert;


    @ApolloJsonValue("${transferProcessConfig:}")
    private TransferProcess transferProcessConfig;

    /**
     * 舱位序列表
     */
    @Value("${cabinSequence:J,C,D,A,R,I,Y,B,M,U,H,Q,V,W,S,T,Z,E,K,G,L,N,X}")
    private String cabinSequence;
    /**
     * 打印的日志记录
     */
    @Value("${restLogExclude:orderServiceOrderBooking}")
    private String restLogExclude;
    /**
     * 国内支持升舱券的舱位
     */
    @Value("${supportUpCouponCabin:D}")
    private String supportUpCouponCabin;
    /**
     * 国际支持升舱券的舱位
     */
    @Value("${supportUpCouponCabinI:R}")
    private String supportUpCouponCabinI;
    /**
     * 无限升舱卡升舱舱位
     */
    @Value("${unlimitedUpClassCabin:J}")
    private String unlimitedUpClassCabin;
    /**
     * M站登陆凭证有效期，默认15天
     */
    @Value("${tokenExpiress:1296000}")
    private long tokenExpiress;
    /**
     * 预约wifi设置的限制天数
     */
    @Value("${wifiLimitDay:10}")
    private int wifiLimitDay;
    /**
     * 是否支持虚拟支付，默认是关闭的
     */
    @Value("${supportVirtualPayment:N}")
    private String supportVirtualPayment;
    /**
     * 热门目的地推荐
     */
    @ApolloJsonValue("${hotDest:{\"modulerName\":\"热门目的地\",\"moreUrl\":\"\",\"proList\":[{\"destCode\":\"SIN\",\"destName\":\"新加坡\",\"keyword\":\"新加坡\",\"destPicUrl\":\"https://mediaws.juneyaoair.com/weixin/hotdest/SIN.png\",\"params\":\"\"}]}}")
    private ProModule<TourDest> hotDest;
    /**
     * 热门目的地更多
     */
    @ApolloJsonValue("${hotRegion:{\"regionList\":[{\"regionName\":\"日本\",\"countryRegionList\":[{\"regionName\":\"日本\",\"tourDestList\":[{\"destName\":\"大阪\"},{\"destName\":\"冲绳\"},{\"destName\":\"东京\"},{\"destName\":\"札幌\"},{\"destName\":\"名古屋\"}]}]}]}}")
    private ParentRegion hotRegion;

    /**
     * 免票天数
     */
    @Value("${maxDays:0}")
    private int maxDays;

    /**
     * 我的页面钱包文案配置
     */
    @ApolloJsonValue("${walletTextList:[{\"startDate\":\"2022-02-14 10:00:00\",\"endDate\":\"2022-02-14 10:00:00\",\"title\":\"吉祥钱包\",\"text\":\"开户有惊喜，积分当钱花\",\"iconUrl\":\"开户有惊喜，积分当钱花\"}]}")
    private List<WalletTextVo> walletTextList;

    /**
     * 先飞后付文案
     */
    @Value("${payAfterFlyRemarks:注: 以实际申请额度为准}")
    private String payAfterFlyRemarks;

    /**
     * 自动办理值机文案
     */
    @Value("${autoCheckInTip:我们将自动为您办理值机，请留意办理结果的短信通知}")
    private String autoCheckInTip;
    /**
     * 未开通网值文案
     */
    @Value("${notOpenCheckInTip:请提前至柜台办理值机}")
    private String notOpenCheckInTip;

    /**
     *是否支持国际改期
     */
    @Value("${internationalChange:Y}")
    private String internationalChange;

    /**
     * 国际品牌运价权益
     */
    @ApolloJsonValue("${brandRightDtoList:[{\"rightCode\":\"059\",\"serviceState\":\"F\",\"description\":\"包含免费改期\"}]}")
    private List<BrandRightDto> brandRightDtoList;

    /**
     * 退票页面提示信息
     */
    @Value("${refundMessage:温馨提示：<br>\n" +
            "1.此入口为使用吉祥航空多次卡兑换的机票订单退款而非多次卡本身退款。<br>\n" +
            "2.使用多次卡兑换机票订单后如无法成行，应至少在航班起飞日提前3天（含）退票，可退回本次支付的机场建设费及燃油费，同时为您恢复当次权益。例如11月10日起飞的航班旅客需在11月7日23:59之前完成退票。<br>\n" +
            "3.若未出行且未在规定时间前办理退票手续，视为弃程。弃程3次多次卡即作废（非旅客原因导致的弃程除外），不做退款处理。<br>\n" +
            "4多次卡兑换的机票订单行程中如包含多人，需共同完成退票申请，仅退部分旅客机票的情形将导致该订单内其余旅客无法成行。<br>\n" +
            "5.第一次出行机票出票成功后，多次卡进入正式激活状态，不可再申请该多次卡本身的自愿退款，但仍可依照上述规则进行机票订单的退款。<br>}")
    private String refundMessage;

    /**
     * 开通网值，未开放自动值机文案
     */
    @Value("${openCheckInTip:欢迎乘坐吉祥航空，如有疑问请联系客服95520}")
    private String openCheckInTip;
    @ApolloJsonValue("${tripTicketIpLimit:{\"limitDay\":{\"frequency\":86400,\"accessLimit\":30},\"limitForever\":{\"frequency\":0,\"accessLimit\":60},\"limitMin\":{\"frequency\":180,\"accessLimit\":10}}}")
    private LimitCount tripTicketIpLimit;
    /**
     * 机+X产品说明
     * 01 机票优惠券  02-机上WIFI券
     */
    @Value("${packagePro:{\"A1\":{\"proCode\":\"A1\",\"proDesc\":\"100元机票优惠券\",\"proDetail\":\"1、发放方式：旅客购买机票优惠券打包产品成功后，系统将在旅客实际行程结束后第二日、并未发生自愿退票/非自愿退票/自愿签转的情况，发放一张100元机票优惠券至旅客的会员账户。旅客可在我的-权益券中查看100元机票优惠券。\\n2、有效期：发放至日起1年内可使用。\",\"proUrl\":\"http://***********:8080/events/giftCertificateDetail/index.html?type=1\"},\"A2\":{\"proCode\":\"A2\",\"proDesc\":\"50M机上WIFI\",\"proDetail\":\"1、发放方式：旅客购买机上WiFi打包产品成功后，系统自动发放一张50M机上WiFi流量券至旅客的会员账户。旅客可在我的-权益券中查看机上WiFi流量券。\\n2、有效期：机上WiFi流量券自购票之日起生效，有效期至航班起飞当日24:00。\\n3、预约兑换规则：机上WiFi流量券仅适用购票人本人于当前购票航班兑换使用，航班起飞前10天可进行预约。若航班起飞后未使用，则机上WiFi流量券失效。\\n3、使用方式\\n第一步：点击流量券上的预约按钮，进入机上WiFi预约页面。填写购票时预留的证件号/客票票号、乘机人姓名及手机号，查询可预约使用的航班。\\n第二步：选择预约使用机上WiFi的航班，再次确认航班及乘机人的相关信息，若账户中有多张流量券时，可叠加兑换使用。\\n第三步：预约成功后，吉祥航空发送短信通知\",\"proUrl\":\"http://***********:8080/events/giftCertificateDetail/index.html?type=2\"}}}")
    private String packagePro;
    /**
     * 会员专享赠送规则
     */
    @Value("${additionRuleId:2}")
    private String additionRuleId;

    /**
     * 普通升舱不允许升舱改期舱位
     */
    @Value("${notAllowedChangeCabin:G}")
    private String notAllowedChangeCabin;

    /**
     * 无限升舱卡不允许升舱改期舱位
     */
    @Value("${unlimitedUpNotAllowedCabin:N}")
    private String unlimitedUpNotAllowedCabin;

    /**
     * 是否展示wifi舱位
     * Y-展示 N-不展示
     */
    @Value("${saleWifiCabin:N}")
    private String saleWifiCabin;
    /**
     * cuss用航延证明加密secret
     */
    @Value("${cussFlightChangeSecret:test}")
    private String cussFlightChangeSecret;
    /**
     * 线上保险配置
     */
    @Value("${insureQueryRules:[{\"tripTypes\":[\"D\"],\"excludeFareTypes\":[\"Addon\",\"SPA\"],\"insureList\":[{\"InsId\":\"*********\",\"InsNm\":\"太保国内延误险\",\"InsCd\":\"5000001\",\"InsDesc\":\"最高赔付300元\",\"InsDescURL\":\"/clause/service_insure.jsp?id=*********\",\"insDescPdfURL\":\"https://mediaws.juneyaoair.com/upload/clause/taibaoguineiyanwuxian2019.pdf\",\"InsAmt\":600,\"InsTax\":30,\"insTaxDesc\":\"30元/人/航段\",\"IsAdt\":\"Y\",\"IsChd\":\"Y\",\"IsInf\":\"N\",\"IsAdtMust\":\"N\",\"IsChdMust\":\"N\",\"IsInfMust\":\"N\",\"PriceType\":\"ONE\",\"IsDefault\":\"N\",\"IsSingleBuy\":\"N\",\"IsAvailable\":\"Y\",\"refundAble\":true,\"insSellPoint\":[\"延误自动理赔\",\"3小时赔付300元\"],\"insImgURL\":\"https://mediaws.juneyaoair.com/upload/insure/taibaoguoneiyanwu.png\",\"insBuyNm\":\"吉祥国内航班延误险（太保)\",\"isMultSpec\":\"N\"},{\"InsId\":\"*********\",\"InsNm\":\"太保综合险\",\"InsCd\":\"7000001\",\"InsDesc\":\"最高赔付300万元\",\"InsDescURL\":\"/clause/service_insure.jsp?id=*********\",\"insDescPdfURL\":\"https://mediaws.juneyaoair.com/upload/clause/taibaozonghexian2019.pdf\",\"InsAmt\":3103600,\"InsTax\":30,\"insTaxDesc\":\"30元/人/航段\",\"IsAdt\":\"Y\",\"IsChd\":\"Y\",\"IsInf\":\"N\",\"IsAdtMust\":\"N\",\"IsChdMust\":\"N\",\"IsInfMust\":\"N\",\"PriceType\":\"ONE\",\"IsDefault\":\"N\",\"IsSingleBuy\":\"N\",\"IsAvailable\":\"Y\",\"refundAble\":true,\"insSellPoint\":[\"航班延误保障\",\"行李保障\",\"证件遗失\"],\"insImgURL\":\"https://mediaws.juneyaoair.com/upload/insure/taibaoguoneizonghe.png\",\"insBuyNm\":\"吉祥综合险（太保）\",\"isMultSpec\":\"N\"},{\"InsId\":\"*********\",\"InsNm\":\"人保航意险\",\"InsCd\":\"1000000\",\"InsDesc\":\"最高赔付260万元\",\"InsDescURL\":\"/clause/service_insure.jsp?id=*********\",\"insDescPdfURL\":\"https://mediaws.juneyaoair.com/upload/clause/renbaohangyixian.pdf\",\"InsAmt\":3103600,\"InsTax\":30,\"insTaxDesc\":\"40元/人/航段\",\"IsAdt\":\"Y\",\"IsChd\":\"Y\",\"IsInf\":\"N\",\"IsAdtMust\":\"N\",\"IsChdMust\":\"N\",\"IsInfMust\":\"N\",\"PriceType\":\"ONE\",\"IsDefault\":\"N\",\"IsSingleBuy\":\"N\",\"IsAvailable\":\"Y\",\"refundAble\":true,\"insSellPoint\":[\"意外事故保障\"],\"insImgURL\":\"https://mediaws.juneyaoair.com/upload/insure/renbaoguoneihangyi.png\",\"insBuyNm\":\"吉祥航意险（人保)\",\"isMultSpec\":\"N\"}]},{\"flightTypes\":[\"OW\",\"RT\"],\"tripTypes\":[\"I\"],\"excludeFareTypes\":[\"Addon\",\"SPA\"],\"insureList\":[{\"InsId\":\"*********\",\"InsNm\":\"太保国际延误险\",\"InsCd\":\"5000000\",\"InsDesc\":\"最高赔付300元\",\"InsDescURL\":\"/clause/service_insure.jsp?id=*********\",\"insDescPdfURL\":\"https://mediaws.juneyaoair.com/upload/clause/taibaoguojiyanwuxian2019.pdf\",\"InsAmt\":600,\"InsTax\":35,\"insTaxDesc\":\"35元/人/航段\",\"IsAdt\":\"Y\",\"IsChd\":\"Y\",\"IsInf\":\"N\",\"IsAdtMust\":\"N\",\"IsChdMust\":\"N\",\"IsInfMust\":\"N\",\"PriceType\":\"ONE\",\"IsDefault\":\"N\",\"IsSingleBuy\":\"N\",\"IsAvailable\":\"Y\",\"refundAble\":true,\"insSellPoint\":[\"起飞延误\",\"主动理赔\"],\"insImgURL\":\"https://mediaws.juneyaoair.com/upload/insure/taibaoguojiyanwu.png\",\"insBuyNm\":\"吉祥国际航班延误险（太保)\",\"isMultSpec\":\"N\"},{\"InsId\":\"*********\",\"InsNm\":\"太保国际退票险\",\"InsCd\":\"6000000\",\"InsDesc\":\"最高赔付1500元\",\"InsDescURL\":\"/clause/service_insure.jsp?id=*********\",\"insDescPdfURL\":\"https://mediaws.juneyaoair.com/upload/clause/taibaoguojituipiaoxian2019.pdf\",\"InsAmt\":1500,\"InsTax\":40,\"insTaxDesc\":\"40元/人/航段\",\"IsAdt\":\"Y\",\"IsChd\":\"Y\",\"IsInf\":\"N\",\"IsAdtMust\":\"N\",\"IsChdMust\":\"N\",\"IsInfMust\":\"N\",\"PriceType\":\"ONE\",\"IsDefault\":\"N\",\"IsSingleBuy\":\"N\",\"IsAvailable\":\"Y\",\"refundAble\":false,\"insSellPoint\":[\"机票损失金额的65%赔付\"],\"insImgURL\":\"https://mediaws.juneyaoair.com/upload/insure/taibaoguojituipiao.png\",\"insBuyNm\":\"吉祥国际退票险（太保)\",\"isMultSpec\":\"N\"},{\"InsNm\":\"人保亚洲旅行险\",\"InsDesc\":\"保障项目丰富，提供全方位保护\",\"InsDescURL\":\"/clause/service_insure.jsp?id=*********\",\"insDescPdfURL\":\"https://mediaws.juneyaoair.com/upload/clause/renbaoyazhou.pdf\",\"InsAmt\":500000,\"insTaxDesc\":\"40元起\",\"IsAdt\":\"Y\",\"IsChd\":\"Y\",\"IsInf\":\"N\",\"IsAdtMust\":\"N\",\"IsChdMust\":\"N\",\"IsInfMust\":\"N\",\"PriceType\":\"TWO\",\"IsDefault\":\"N\",\"IsSingleBuy\":\"N\",\"IsAvailable\":\"Y\",\"refundAble\":false,\"IsFlexiblePrice\":\"Y\",\"insSellPoint\":[\"旅行变更保障\",\"行李保障\",\"紧急救援\"],\"insImgURL\":\"https://mediaws.juneyaoair.com/upload/insure/renbaoyazhoulvxing.png\",\"insBuyNm\":\"吉祥亚洲旅行险（人保）\",\"InsuranceInfo\":[{\"insuranceCode\":\"*********\",\"insuranceNumber\":1,\"insuranceAmount\":120,\"insuranceSpec\":\"基础计划\",\"priceRule\":[{\"day\":7,\"price\":40},{\"day\":10,\"price\":55},{\"day\":15,\"price\":70},{\"day\":22,\"price\":94},{\"day\":30,\"price\":120}]},{\"insuranceCode\":\"*********\",\"insuranceNumber\":1,\"insuranceAmount\":140,\"insuranceSpec\":\"全面计划\",\"priceRule\":[{\"day\":7,\"price\":52},{\"day\":10,\"price\":68},{\"day\":15,\"price\":85},{\"day\":22,\"price\":108},{\"day\":30,\"price\":140}]}],\"isMultSpec\":\"Y\"},{\"InsNm\":\"人保欧洲旅行险\",\"InsDesc\":\"欧洲畅游，后顾无忧\",\"InsDescURL\":\"/clause/service_insure.jsp?id=*********\",\"insDescPdfURL\":\"https://mediaws.juneyaoair.com/upload/clause/renbaoouzhou.pdf\",\"InsAmt\":1000000,\"insTaxDesc\":\"65元起\",\"IsAdt\":\"Y\",\"IsChd\":\"Y\",\"IsInf\":\"N\",\"IsAdtMust\":\"N\",\"IsChdMust\":\"N\",\"IsInfMust\":\"N\",\"PriceType\":\"TWO\",\"IsDefault\":\"N\",\"IsSingleBuy\":\"N\",\"IsAvailable\":\"Y\",\"refundAble\":false,\"IsFlexiblePrice\":\"Y\",\"insSellPoint\":[\"申根过签\",\"航班延误\",\"旅行变更多项保障\"],\"insImgURL\":\"https://mediaws.juneyaoair.com/upload/insure/renbaoouzhoulvxing.png\",\"insBuyNm\":\"吉祥欧洲旅行险（人保）\",\"InsuranceInfo\":[{\"insuranceCode\":\"*********\",\"insuranceNumber\":1,\"insuranceAmount\":155,\"insuranceSpec\":\"基础计划\",\"priceRule\":[{\"day\":7,\"price\":65},{\"day\":10,\"price\":88},{\"day\":15,\"price\":103},{\"day\":22,\"price\":121},{\"day\":30,\"price\":155}]},{\"insuranceCode\":\"*********\",\"insuranceNumber\":1,\"insuranceAmount\":170,\"insuranceSpec\":\"全面计划\",\"priceRule\":[{\"day\":7,\"price\":85},{\"day\":10,\"price\":93},{\"day\":15,\"price\":115},{\"day\":22,\"price\":138},{\"day\":30,\"price\":170}]}],\"isMultSpec\":\"Y\"}]}]}")
    private String insureQueryRules;
    /**
     * 特殊城市行李配置
     */
    @ApolloJsonValue("${baggageStandardConfigList:[{\"cityList\":[\"HEL\"],\"cabinList\":[\"Y\",\"B\",\"M\"],\"baggageStandardList\":[{\"flightType\":\"I\",\"passengerType\":\"ADT\",\"luggageType\":\"1\",\"cabinGrade\":\"Y\",\"cabinClassName\":\"经济舱\",\"countLimit\":1,\"perWeightLimit\":\"10\",\"sizeLimit\":\"三边之和不超过115CM\",\"freeWeight\":0,\"chargeStandard\":null,\"destination\":\"3\",\"memberLevel\":null,\"comments\":\"赫尔辛基经济舱YBM随身行李\"},{\"flightType\":\"I\",\"passengerType\":\"ADT\",\"luggageType\":\"2\",\"cabinGrade\":\"Y\",\"cabinClassName\":\"经济舱\",\"countLimit\":2,\"perWeightLimit\":\"23\",\"sizeLimit\":\"三边之和不超过158CM\",\"freeWeight\":0,\"chargeStandard\":null,\"destination\":\"3\",\"memberLevel\":\"3\",\"comments\":\"赫尔辛基经济舱YBM托运行李\"}]},{\"cityList\":[\"HEL\"],\"cabinList\":[\"J\",\"C\",\"D\",\"A\",\"R\"],\"baggageStandardList\":[{\"flightType\":\"I\",\"passengerType\":\"ADT\",\"luggageType\":\"1\",\"cabinGrade\":\"J\",\"cabinClassName\":\"公务舱\",\"countLimit\":2,\"perWeightLimit\":\"10\",\"sizeLimit\":\"三边之和不超过115CM\",\"freeWeight\":0,\"chargeStandard\":null,\"destination\":\"3\",\"memberLevel\":null,\"comments\":\"赫尔辛基公务舱随身行李\"},{\"flightType\":\"I\",\"passengerType\":\"ADT\",\"luggageType\":\"2\",\"cabinGrade\":\"J\",\"cabinClassName\":\"公务舱\",\"countLimit\":3,\"perWeightLimit\":\"23\",\"sizeLimit\":\"三边之和不超过158CM\",\"freeWeight\":0,\"chargeStandard\":null,\"destination\":\"3\",\"memberLevel\":\"3\",\"comments\":\"赫尔辛基公务舱托运行李\"}]},{\"cityList\":[\"HEL\"],\"cabinList\":[\"U\",\"H\",\"Q\",\"V\",\"W\",\"S\",\"T\",\"Z\",\"E\",\"G\",\"K\"],\"baggageStandardList\":[{\"flightType\":\"I\",\"passengerType\":\"ADT\",\"luggageType\":\"1\",\"cabinGrade\":\"Y\",\"cabinClassName\":\"经济舱\",\"countLimit\":1,\"perWeightLimit\":\"10\",\"sizeLimit\":\"三边之和不超过115CM\",\"freeWeight\":0,\"chargeStandard\":null,\"destination\":\"3\",\"memberLevel\":null,\"comments\":\"赫尔辛基普通经济舱随身行李\"},{\"flightType\":\"I\",\"passengerType\":\"ADT\",\"luggageType\":\"2\",\"cabinGrade\":\"Y\",\"cabinClassName\":\"经济舱\",\"countLimit\":1,\"perWeightLimit\":\"23\",\"sizeLimit\":\"三边之和不超过158CM\",\"freeWeight\":0,\"chargeStandard\":null,\"destination\":\"3\",\"memberLevel\":\"3\",\"comments\":\"赫尔辛基普通经济舱托运行李\"}]},{\"cabinList\":[\"Y\",\"B\",\"M\",\"U\",\"H\",\"Q\",\"V\",\"W\",\"S\",\"T\",\"Z\",\"E\",\"L\"],\"baggageStandardList\":[{\"flightType\":\"I\",\"passengerType\":\"ADT\",\"luggageType\":\"1\",\"cabinGrade\":\"Y\",\"cabinClassName\":\"经济舱\",\"countLimit\":1,\"perWeightLimit\":\"10\",\"sizeLimit\":\"三边之和不超过115CM\",\"freeWeight\":0,\"chargeStandard\":null,\"destination\":\"3\",\"memberLevel\":null,\"comments\":\"普通经济舱随身携带行李\"},{\"flightType\":\"I\",\"passengerType\":\"ADT\",\"luggageType\":\"2\",\"cabinGrade\":\"Y\",\"cabinClassName\":\"经济舱\",\"countLimit\":1,\"perWeightLimit\":\"23\",\"sizeLimit\":\"三边之和不超过158CM\",\"freeWeight\":0,\"chargeStandard\":null,\"destination\":\"3\",\"memberLevel\":\"3\",\"comments\":\"普通经济舱免费托运行李\"}],\"excludeCountryNoList\":[\"JP\",\"KR\",\"SG\"],\"excludeCityList\":[\"HEL\"]},{\"cabinList\":[\"J\",\"C\",\"D\",\"A\",\"R\"],\"baggageStandardList\":[{\"flightType\":\"I\",\"passengerType\":\"ADT\",\"luggageType\":\"1\",\"cabinGrade\":\"J\",\"cabinClassName\":\"公务舱\",\"countLimit\":2,\"perWeightLimit\":\"10\",\"sizeLimit\":\"三边之和不超过115CM\",\"freeWeight\":0,\"chargeStandard\":null,\"destination\":\"3\",\"memberLevel\":null,\"comments\":\"普通公务舱随身携带行李\"},{\"flightType\":\"I\",\"passengerType\":\"ADT\",\"luggageType\":\"2\",\"cabinGrade\":\"J\",\"cabinClassName\":\"公务舱\",\"countLimit\":2,\"perWeightLimit\":\"32\",\"sizeLimit\":\"三边之和不超过158CM\",\"freeWeight\":0,\"chargeStandard\":null,\"destination\":\"3\",\"memberLevel\":\"3\",\"comments\":\"普通公务舱免费托运行李\"}],\"excludeCityList\":[\"HEL\"]},{\"countryNoList\":[\"JP\",\"KR\",\"SG\"],\"cabinList\":[\"Y\",\"B\",\"M\",\"U\",\"H\",\"Q\",\"V\",\"W\",\"S\",\"T\",\"Z\",\"E\"],\"baggageStandardList\":[{\"flightType\":\"I\",\"passengerType\":\"ADT\",\"luggageType\":\"1\",\"cabinGrade\":\"Y\",\"cabinClassName\":\"经济舱\",\"countLimit\":1,\"perWeightLimit\":\"10\",\"sizeLimit\":\"三边之和不超过115CM\",\"freeWeight\":0,\"chargeStandard\":null,\"destination\":\"3\",\"memberLevel\":null,\"comments\":\"日韩经济舱随身携带行李\"},{\"flightType\":\"I\",\"passengerType\":\"ADT\",\"luggageType\":\"2\",\"cabinGrade\":\"Y\",\"cabinClassName\":\"经济舱\",\"countLimit\":2,\"perWeightLimit\":\"23\",\"sizeLimit\":\"三边之和不超过158CM\",\"freeWeight\":0,\"chargeStandard\":null,\"destination\":\"3\",\"memberLevel\":\"3\",\"comments\":\"日韩经济舱免费托运行李\"}]}]}")
    private List<BaggageStandardConfig> baggageStandardConfigList;
    /**
     * 洲际航线配置
     */
    @Value("${intercontinentalCities:HEL}")
    private String intercontinentalCities;
    /**
     * 下个线上M站版本 如12100
     */
    @Value("${mwebOnlineVer:17200}")
    private String mwebOnlineVer;
    /**
     * 下个线上小程序版本 如11000
     */
    @Value("${miniOnlineVer:12000}")
    private String miniOnlineVer;
    /**
     * 新的支付配置方式
     */
    @ApolloJsonValue("${payConfigsV2:}")
    private Map<String,List<Bank>> payConfigsV2;

    /**
     * 吉宁文化支付配置
     */
    @Value("${payJnConfigs:[{\"bankInfo\":[{\"bankList\":[{\"bank\":\"ALIPAY\",\"bankName\":\"支付宝支付\",\"gateway\":\"56600\",\"gatewayType\":\"6\",\"key\":\"50ab8^eef9610c130895305b71be5#ef\",\"payChannel\":\"MOBILE\"},{\"bank\":\"WEIXINPAY\",\"bankName\":\"微信支付\",\"gateway\":\"31610\",\"gatewayType\":\"6\",\"key\":\"50ab8^eef9610c130895305b71be5#ef\",\"payChannel\":\"MOBILE\"}],\"orderType\":\"D\"},{\"bankList\":[{\"bank\":\"ALIPAY\",\"bankName\":\"支付宝支付\",\"gateway\":\"56600\",\"gatewayType\":\"6\",\"key\":\"50ab8^eef9610c130895305b71be5#ef\",\"payChannel\":\"MOBILE\"},{\"bank\":\"WEIXINPAY\",\"bankName\":\"微信支付\",\"gateway\":\"31610\",\"gatewayType\":\"6\",\"key\":\"50ab8^eef9610c130895305b71be5#ef\",\"payChannel\":\"MOBILE\"}],\"orderType\":\"I\"},{\"bankList\":[{\"bank\":\"ALIPAY\",\"bankName\":\"支付宝支付\",\"gateway\":\"56600\",\"gatewayType\":\"6\",\"key\":\"50ab8^eef9610c130895305b71be5#ef\",\"payChannel\":\"MOBILE\"},{\"bank\":\"WEIXINPAY\",\"bankName\":\"微信支付\",\"gateway\":\"31610\",\"gatewayType\":\"6\",\"key\":\"50ab8^eef9610c130895305b71be5#ef\",\"payChannel\":\"MOBILE\"}],\"orderType\":\"Q\"},{\"bankList\":[{\"bank\":\"ALIPAY\",\"bankName\":\"支付宝支付\",\"gateway\":\"56600\",\"gatewayType\":\"6\",\"key\":\"50ab8^eef9610c130895305b71be5#ef\",\"payChannel\":\"MOBILE\"},{\"bank\":\"WEIXINPAY\",\"bankName\":\"微信支付\",\"gateway\":\"31610\",\"gatewayType\":\"6\",\"key\":\"50ab8^eef9610c130895305b71be5#ef\",\"payChannel\":\"MOBILE\"}],\"orderType\":\"O\"},{\"bankList\":[{\"bank\":\"ALIPAY\",\"bankName\":\"支付宝支付\",\"gateway\":\"56601\",\"gatewayType\":\"6\",\"key\":\"50ab8^eef9610c130895305b71be5#ef\",\"payChannel\":\"MOBILE\"},{\"bank\":\"WEIXINPAY\",\"bankName\":\"微信支付\",\"gateway\":\"31615\",\"gatewayType\":\"6\",\"key\":\"50ab8^eef9610c130895305b71be5#ef\",\"payChannel\":\"MOBILE\"}],\"orderType\":\"GatewayUpgrade\"}],\"channelNo\":\"MWEB\"}]}")
    private String payJnConfigs;
    @ApolloJsonValue("${payJnConfigsV2:}")
    private Map<String,List<Bank>> payJnConfigsV2;

    /**
     * 支付配置的缓存时间
     */
    @Value("${payCacheTime:3600}")
    private long payCacheTime;

    /**
     * 个性化餐食首页轮播图
     */
    @ApolloJsonValue("${mealImageList:[{\"mealDesc\":\"传统上海汤面配上香喷喷红亮亮诱人大排，再来一口纯正原汤，机上能品尝到如此本帮特色，真是实实在在一碗吉祥好面\",\"mealImg\":\"https://mediaws.juneyaoair.com/upload/meal/chuantong.jpg\",\"mealName\":\"传统上海面\"},{\"mealDesc\":\"精选优质虾仁为馅，佐以肉糜，纯手工的鸡汤配上这一个个大云吞，紫菜、蛋皮和香菜凸显这碗咸鲜味美的鸡汤云吞\",\"mealImg\":\"https://mediaws.juneyaoair.com/upload/meal/jingxuan.jpg\",\"mealName\":\"精选优质虾仁\"},{\"mealDesc\":\"烤鸡色泽红亮配上豆腐的麻辣味厚,细嫩鲜香，这一口着实让人流连忘返，还在为朴素无味的飞机餐发愁吗，快来感受这一口诱惑吧\",\"mealImg\":\"https://mediaws.juneyaoair.com/upload/meal/kaoji.jpg\",\"mealName\":\"烤鸡\"},{\"mealDesc\":\" 西式的奶酪和培根做成的法式乳蛋饼，辅助着西兰花、烤制小番茄、培根。营养丰富、色彩搭配相得益彰，唤醒您美好的一天\",\"mealImg\":\"https://mediaws.juneyaoair.com/upload/meal/xishidenai.jpg\",\"mealName\":\"西式的奶酪\"}]}")
    private List<MealImage> mealImageList;
    /**
     * 往返特惠优惠券配置
     */
    @Value("${roundTripCouponCode:}")
    private String roundTripCouponCode;
    /**
     * 是否打印av查询日志，默认不打印N
     */
    @Value("${showAvlog:N}")
    private String showAvlog;
    /**
     * 积分免密支付额度 默认20
     */
    @Value("${scoreFreeLimit:20}")
    private int scoreFreeLimit;
    /**
     * 华瑞提供给吉祥的公钥
     * 测试：0448a45a40c83b598bbed1d27ae9fde1f2f70b93fae0ae125a556129baaf45642d0c7c19634fdf0b3917318c8e889d1c3839c30a2aa1b7fcbfa64d1067df5d3302
     * 正式：04e3efb65ac392fa32aa27f5ba6ae3d18d95f00fe94de2523c783420e1d3cb2cf5440874b5e210e5afc980046ee49c30578132f268168f6de1e22dc41157960571
     */
    @Value("${huaRuiPublicKey:04e3efb65ac392fa32aa27f5ba6ae3d18d95f00fe94de2523c783420e1d3cb2cf5440874b5e210e5afc980046ee49c30578132f268168f6de1e22dc41157960571}")
    private String huaRuiPublicKey;
    /**
     * 华瑞提供的URL
     */
    @Value("${huaRuiUrl:https://loan.shrbank.com:443/JXHH/user?titlestyle=6&title=航旅贷}")
    private String huaRuiUrl;
    /**
     * 华瑞提供的渠道ID
     */
    @Value("${channelId:jixiang}")
    private String channelId;
    /**
     * 可申请中转住宿的航线配置
     */
    @ApolloJsonValue("${transferAccommodationConfigs:[{\"midCityCode\":\"SHA\",\"arrCityCode\":\"HEL\",\"minDurHour\":6}]}")
    private List<TransferAccommodationConfig> transferAccommodationConfigs;
    /**
     * 运价标签配置
     */
    @ApolloJsonValue("${labelInfoConfig:{\"commonLabelInfos\":[{\"labelName\":\"手提+托运行李\",\"labelUrl\":\"\",\"labelType\":\"norommal\",\"labelNum\":1,\"pictureUrl\":\"https://mediaws.juneyaoair.com/upload/icon/cabin/<EMAIL>\"},{\"labelName\":\"含餐\",\"labelUrl\":\"\",\"labelType\":\"normal\",\"labelNum\":2,\"pictureUrl\":\"https://mediaws.juneyaoair.com/upload/icon/cabin/<EMAIL>\"},{\"labelName\":\"快速出票\",\"labelUrl\":\"\",\"labelType\":\"normal\",\"labelNum\":3,\"pictureUrl\":\"https://mediaws.juneyaoair.com/upload/icon/cabin/<EMAIL>\"}],\"businessClassLabelInfoList\":[{\"labelName\":\"额外行李额\",\"labelUrl\":\"\",\"labelType\":\"normal\",\"labelNum\":1,\"pictureUrl\":\"https://mediaws.juneyaoair.com/upload/icon/cabin/<EMAIL>\"},{\"labelName\":\"优先登机\",\"labelUrl\":\"\",\"labelType\":\"normal\",\"labelNum\":2,\"pictureUrl\":\"https://mediaws.juneyaoair.com/upload/icon/cabin/<EMAIL>\"},{\"labelName\":\"宽敞座椅\",\"labelUrl\":\"\",\"labelType\":\"normal\",\"labelNum\":3,\"pictureUrl\":\"https://mediaws.juneyaoair.com/upload/icon/cabin/<EMAIL>\"},{\"labelName\":\"精美餐食\",\"labelUrl\":\"\",\"labelType\":\"normal\",\"labelNum\":4,\"pictureUrl\":\"https://mediaws.juneyaoair.com/upload/icon/cabin/<EMAIL>\"}],\"brandLabelInfos\":[{\"labelName\":\"无免费行李额\",\"labelUrl\":\"\",\"labelType\":\"normal\",\"labelNum\":1,\"pictureUrl\":\"https://mediaws.juneyaoair.com/upload/icon/cabin/<EMAIL>\"},{\"labelName\":\"不支持退改\",\"labelUrl\":\"\",\"labelType\":\"normal\",\"labelNum\":2,\"pictureUrl\":\"https://mediaws.juneyaoair.com/upload/icon/cabin/<EMAIL>\"},{\"labelName\":\"无积分\",\"labelUrl\":\"\",\"labelType\":\"normal\",\"labelNum\":3,\"pictureUrl\":\"https://mediaws.juneyaoair.com/upload/icon/cabin/<EMAIL>\"}],\"brandLabel\":{\"labelName\":\"吉祥品牌运价\",\"labelUrl\":\"\",\"labelType\":\"normal\",\"labelNum\":0,\"pictureUrl\":\"https://mediaws.juneyaoair.com/upload/icon/cabin/<EMAIL>\"},\"adtUnlimitedFlyLabelInfos\":[{\"labelName\":\"最高全额抵扣成人+儿童票款\",\"labelUrl\":\"\",\"labelType\":\"normal\",\"labelNum\":1,\"pictureUrl\":\"https://mediaws.juneyaoair.com/upload/icon/cabin/<EMAIL>\"},{\"labelName\":\"餐食\",\"labelUrl\":\"\",\"labelType\":\"normal\",\"labelNum\":2,\"pictureUrl\":\"https://mediaws.juneyaoair.com/upload/icon/cabin/<EMAIL>\"},{\"labelName\":\"手提+托运行李\",\"labelUrl\":\"\",\"labelType\":\"normal\",\"labelNum\":3,\"pictureUrl\":\"https://mediaws.juneyaoair.com/upload/icon/cabin/<EMAIL>\"}],\"chdUnlimitedFlyLabelInfos\":[{\"labelName\":\"全额抵扣儿童票款\",\"labelUrl\":\"\",\"labelType\":\"normal\",\"labelNum\":1,\"pictureUrl\":\"https://mediaws.juneyaoair.com/upload/icon/cabin/<EMAIL>\"},{\"labelName\":\"餐食\",\"labelUrl\":\"\",\"labelType\":\"normal\",\"labelNum\":2,\"pictureUrl\":\"https://mediaws.juneyaoair.com/upload/icon/cabin/<EMAIL>\"},{\"labelName\":\"手提+托运行李\",\"labelUrl\":\"\",\"labelType\":\"normal\",\"labelNum\":3,\"pictureUrl\":\"https://mediaws.juneyaoair.com/upload/icon/cabin/<EMAIL>\"}],\"highRebateLabelInfos\":[{\"labelName\":\"手提+托运行李\",\"labelUrl\":\"\",\"labelType\":\"norommal\",\"labelNum\":1,\"pictureUrl\":\"https://mediaws.juneyaoair.com/upload/icon/cabin/<EMAIL>\"},{\"labelName\":\"含餐\",\"labelUrl\":\"\",\"labelType\":\"normal\",\"labelNum\":2,\"pictureUrl\":\"https://mediaws.juneyaoair.com/upload/icon/cabin/<EMAIL>\"},{\"labelName\":\"全额行程单\",\"labelUrl\":\"\",\"labelType\":\"normal\",\"labelNum\":3,\"pictureUrl\":\"https://mediaws.juneyaoair.com/upload/icon/cabin/<EMAIL>\"}]}}")
    private LabelInfoConfig labelInfoConfig;
    /**
     * 标签字典配置
     **/
    @ApolloJsonValue("${labelInfoMap:{\"lowCarbon\":[{\"labelName\":\"按需用餐奖励积分\",\"labelUrl\":\"https://m.juneyaoair.com/minimalistMeals/index.html#/?barStyle=0\",\"labelType\":\"normal\"}]}}")
    private Map<String,List<LabelInfo>> labelInfoMap;
    /**
     * 品牌运价规则
     */
    @Value("${brandPriceRule:{\"saleChannel\":{\"title\":\"销售渠道\",\"description\":\"APP/官网/客服中心等吉祥航空直销渠道，以及携程OTA平台；\"},\"applicableRoute\":{\"title\":\"适用航线\",\"description\":\"目前仅适用上海到泰国的所有航线，包括上海\\u003d清迈/上海\\u003d曼谷/上海\\u003d普吉岛（单程及往返均适用）；\"},\"applicableCabin\":{\"title\":\"适用舱位\",\"description\":\"经济舱{cabin}舱；\"},\"freeBaggage\":{\"title\":\"免费行李额\",\"description\":\"无，即0PC；\"},\"refundRule\":{\"title\":\"退改规则\",\"description\":\"客票一旦出票，不支持退改签。非自愿情况，按正常客票非自愿退改规则处理；\"},\"memberBenefits\":{\"title\":\"会员权益\",\"description\":\"积分/航段累计规则不变，即累计1次定级航段，无积分。\"}}}")
    private String brandPriceRule;
    /**
     * 品牌运价航线设置
     */
    @ApolloJsonValue("${brandRouteCabinConfig:{\"brandPriceCabins\":[\"K\"],\"internationalRoute\":{\"TH\":[]}}}")
    private BrandRouteCabinConfig brandRouteCabinConfig;
    /**
     * 年度账单授权访问IP
     */
    @Value("${billAuthIp:}")
    private String billAuthIp;
    /**
     * 线上开放的认证方式
     */
    @ApolloJsonValue("${supportVerifyChannel:[{\"verifyCode\":\"ZhiFuBao\",\"verifyDesc\":\"支付宝认证\"}]}")
    private List<VerifyChannel> supportVerifyChannel;


    /**
     * 线上开放的认证方式
     */
    @ApolloJsonValue("${payMethod:[{\"payMethodName\":\"华瑞银行先飞后付\",\"describtion\":\"先飞后付立减\"},{\"payMethodName\":\"建设银行白金卡MOBILE(建设银行白金卡MOBILE)\",\"describtion\":\"吉祥龙卡优惠\"}]}")
    private List<PayMethod> payMethod;

    /**
     * 线上开放的注销账号身份认证方式
     */
    @ApolloJsonValue("${supportLogoutVerifyChannel:[{\"verifyCode\":\"memberLogoutSms\",\"verifyDesc\":\"短信验证码认证\",\"businessList\":[\"N\"]},{\"verifyCode\":\"memberLogoutEmail\",\"verifyDesc\":\"邮箱验证码认证\",\"businessList\":[\"E\"]}]}")
    private List<VerifyChannel> supportLogoutVerifyChannel;
    /**
     * 支付宝人脸认证配置
     */
    @ApolloJsonValue("${antConfig:{\"baseUrl\":\"https://prodapigw.cloud.alipay.com\",\"key\":\"LTAI4FmVAMWUoD2wXW8qvdzt\",\"secret\":\"******************************\"}}")
    private AntConfig antConfig;
    @Value("${applyRange:官网、APP等直营渠道}")
    private String usingChannels; //券使用规则中的使用渠道
    @Value("${applyRange:吉祥航空实际承运的国内航线使用}")
    private String applyRange; //券使用规则中的适用范围
    /**
     * 是否使用代理
     * Y-使用  默认不使用
     */
    @Value("${useProxy:N}")
    private String useProxy;
    /**
     * 使用代理的URL
     */
    @ApolloJsonValue("${useProxyUrl:[]}")
    private List<String> useProxyUrl;
    /**
     * 是否使用默认的首页导航ICON
     * Y-表示启用默认配置  N-不启用
     */
    @Value("${useDefaultIcon:N}")
    private String useDefaultIcon;
    /**
     * 升舱券默认城市对
     */
    @ApolloJsonValue("${upCouponDefCity:{arrCityCode:PEK,arrCity:北京,depCity:上海,depCityCode:SHA}}")
    private CouponDefCity upCouponDefCity;
    /**
     * 改期券默认城市对
     */
    @ApolloJsonValue("${changeCouponDefCity:{arrCityCode:PEK,arrCity:北京,depCity:上海,depCityCode:SHA}}")
    private CouponDefCity changeCouponDefCity; //券使用规则中的适用范围
    /**
     * 航班查询密钥
     */
    @Value("${avSecret:X8ofQWCp5xJd&2e5l@*60M%2rTEI$vtW}")
    private String avSecret;
    /**
     * 腾讯云服务
     */
    @ApolloJsonValue("${tencentCloudAccount:{secretId:AKIDxlR1Rh5eMrGVZS5iGermWHsIWYP5at0x,secretKey:xlNp5A3nNn13ub7RZXLwLdljZDh195uZ}}")
    private TencentCloudAccount tencentCloudAccount;
    /**
     * 腾讯云服务使用的app_id,WBappID
     */
    @Value("${tencentCloudAppId:IDA9Ud8M}")
    private String tencentCloudAppId;

    @ApolloJsonValue("${licenseMap:{\"android\":\"SD8zEZ9kl4PKrC34IbDjDkYIwmAIN6tjrO+nbN8SmiwrwtwAuTX90WC4PtnCkrURH49WF5szfLTt+KR5fE3rcUGfyISW7cUjt1QFArYscmvsLQgxEauWyXuYbmlwRuyb5Ra2WU6t4qszkpi/8V5SsST9R+PwqgDLCLSj5JE2wOwPd4Q22l/bTwGD/hI+4SfrLWkwj/Pb4xrJWWSN0rOZuzE/HL7yojKdamXUawGJR/07EYe9K8BYWatv/ToZbQ8/D5a7SeJrPlWYuxBIjlEJP48dGXxhQYy4K4kBXHkA8nSRZsVqEaf/aJO/eB7GXuq54bMM5zHr52ChoKb9hidJWQ==\",\"harmony\":\"oWxLt1xJmA3f1sRtPJwbY2PLSAdlfP46eOnf6Ug//BSuDF40cJoVL+9UUfHwStdYs6QEDV1cYTSlM9QXA4QDWjpDdjS+cxEHvnN4M6muQpk5PcdhqSng7mYR5My75YCZonFQYGl2z/0l6atL3huX9OHzR3Ed34xA8tMTDeuWLUuD8g5urzaMdy+T5hpwcM3EK5jlU88WIWW+PoSASty84b0PkKfWnuhtY8cR5ZNznnQiDqm/rXZ999IE26hlg2j7w7ViurRuxSLDtWK6tG7FIsO1Yrq0bsUiNcsSGMcOLrkvMxAmJlT9Hi8zECYmVP0eLzMQJiZU/R7ZOgrqNhFQq4sOoqritFT1uELIK5fCmNgoE0ixRRjMXECM3Ow1rw1P\"}}")
    private Map<String, String> licenseMap;

    /**
     * 实体卡申请使用
     * 会员级别有效期的有效范围
     */
    @Value("${validRang:{minValue:300,maxValue:400}}")
    private String validRang;
    /**
     * 不能办理值机选座，即后台尚未开放选座功能
     */
    @Value("${closedCheckInTips:航班暂未开放值机选座，请您在航班起飞前90分钟至人工柜台办理}")
    private String closedCheckInTips;

    /**
     * 登机口升舱座位图开放配置
     * Map<String , String> 类型
     * key 为机型 value 为枚举 : new 仅开放新座位图; old 仅开放老座位图; both: 两者均开放; neither: 两者均不开放;
     * key不存在时默认均开放
     */
    @Value("${gateUpgradeSeatMapCfg:{}}")
    private String gateUpgradeSeatMapCfg;
    /**
     * 改期订单退款规则
     */
    @Value("${changeOrderRefundRule:<div class=\"rulesInfo\"><div class=\"title\">已改期过的订单，暂不支持在线改期</div><dl class=\"first-dl\"><dt>改期后自愿退票：</dt><dd>1.客票变更后发生自愿退票，所收变更手续费不退;</dd><dd>2.客票变更后如发生自愿退票，客票差价部分按变更后新订座舱位的退票手续费标准办理，原票价按变更前订座舱位退票手续费标准办理，合计收取退票费。</dd></dl><dl><dt>改期后非自愿退票:</dt><dd>1.客票变更后退票发生非自愿退票，所收变更手续费不退;</dd><dd>2.客票变更后如发生非自愿退票，应先退还旅客升舱后的客票差价（即：补差金额），再按“票价计算”栏标注的原舱位的退票规定以自愿退票办理，以原票价计算退票费（即：升舱后的客票差价票款全退，原舱位票款按舱位规定扣除退票手续费后退款）</dd></dl></div>}")
    private String changeOrderRefundRule;
    /**
     * 升舱订单退款规则
     */
    @Value("${updateOrderRefundRule:<div class=\"rulesInfo\"><div class=\"title\">自助升舱退改规则</div><dl class=\"first-dl\"><dt>1.自愿退票，变更</dt><dd>升舱成功后，旅客自愿办理变更，机票将按实际升舱后的公务舱舱位规定执行变更。</dd><dd>付费升舱<br>若在线付费升舱成功，旅客自愿办理退票，升舱补差的费用按实际升舱后的公务舱舱位规定执行退票。</dd><dd>升舱券升舱<br>若使用升舱券升舱，升舱券不退回，升舱前的经济舱客票费用部分按原经济舱舱位规定执行退票。</dd></dl><dl><dt>2.非自愿退票，变更，签转</dt><dd>升舱成功后，旅客办理非自愿退票，变更，签转，可按值机升舱后的公务舱舱位规定执行。</dd><dd>付费升舱<br>若在线付费升舱成功，客票发生非自愿变更、签转，可按实际升舱后的公务舱相关规定进行保障。</dd><dd>升舱券升舱<br>若使用升舱券升舱，客票发生非自愿退票，则将升舱券退回至旅客账户，有效期不变。升舱前的原经济舱客票，按原舱位客票规定办理非自愿退票手续。</dd></dl></div>}")
    private String updateOrderRefundRule;

    @ApolloJsonValue("${cancelOrderReasonConfig:{\"Normal\":[{\"dataId\":\"0\",\"reason\":\"未选择取消原因\",\"isDefault\":true,\"isShow\":false},{\"dataId\":\"1\",\"reason\":\"信息填写错误，重新订票\",\"isDefault\":false,\"isShow\":true},{\"dataId\":\"2\",\"reason\":\"出行计划变动\",\"isDefault\":false,\"isShow\":true},{\"dataId\":\"3\",\"reason\":\"机票价格发生变化\",\"isDefault\":false,\"isShow\":true},{\"dataId\":\"4\",\"reason\":\"发现更便宜的机票\",\"isDefault\":false,\"isShow\":true},{\"dataId\":\"5\",\"reason\":\"改乘其他交通工具\",\"isDefault\":false,\"isShow\":true},{\"dataId\":\"6\",\"reason\":\"其他原因\",\"isDefault\":false,\"isShow\":true}]}}")
    private Map<String, List<CancelOrderReason>> cancelOrderReasonConfig;

    @ApolloJsonValue("${refundTypes:[{\"dataId\":\"1\",\"title\":\"自愿退票\",\"description\":\"自身原因无法出行\",\"isVoluntaryRefund\":true,\"needAttachment\":false,\"maxAttachmentNum\":6},{\"dataId\":\"2\",\"title\":\"非自愿退票\",\"description\":\"航空公司原因无法成行、乘机人因病（或身故）无法成行、自然灾害等\",\"preInformation\":\"旅客因病要求退票，需提供二级（含）以上医院（或相当于该级别的医疗机构）出具的医生诊断证明，免收退票手续费。患病旅客的陪伴人员要求退票，应与患病旅客同时办理退票手续，免收退票手续费。每一患病旅客最多可申请办理2名陪伴人员的免手续费退票。\",\"sufInformation\":\"当您提交的非自愿退票审核未通过时，您的机票会按照实际审核结果收取对应的退票费用，同时取消您的机票座位。\",\"isVoluntaryRefund\":false,\"needAttachment\":true,\"maxAttachmentNum\":6}]}")
    private List<RefundType> refundTypes;

    @ApolloJsonValue("${youthFareLabel:{\"labelInfo\":{\"labelName\":\"青年特惠\",\"labelUrl\":\"\",\"labelType\":\"activity\",\"labelNum\":0,\"pictureUrl\":\"https://mediaws.juneyaoair.com/upload/icon/av/qlnth.png\"},\"labelDetail\":{\"title\":\"青年特惠运价规则\",\"detail\":[{\"title\":\"适用群体\",\"description\":\"18岁-24周岁，予以旅客经济舱全价3%的直减优惠价格\",\"icon\":\"https://mediaws.juneyaoair.com/upload/icon/av/icon_channel.png\"},{\"title\":\"航班日期\",\"description\":\"即日起-2021年12月31日（含）\",\"icon\":\"https://mediaws.juneyaoair.com/upload/icon/av/icon_date.png\"},{\"title\":\"适用舱位\",\"description\":\"U\\\\H\\\\Q\\\\V\\\\W\\\\S\\\\T\\\\Z\\\\E\",\"icon\":\"https://mediaws.juneyaoair.com/upload/icon/av/icon_seat.png\"},{\"title\":\"适用航线\",\"description\":\"吉祥航空国内航班，不包含代码共享航班，不包含上海=临汾往返航班\",\"icon\":\"https://mediaws.juneyaoair.com/upload/icon/av/icon_plane.png\"},{\"title\":\"销售渠道\",\"description\":\"95520、官网、直属售票处、OTA代理\",\"icon\":\"https://mediaws.juneyaoair.com/upload/icon/av/icon_channel.png\"}]}}}")
    private LabelAndRuleInfo youthFareLabel;

    /**
     * 会员专享运价标签
     */
    @ApolloJsonValue("${memberFareLabel:{\"labelInfo\":{\"labelName\":\"会员专享\",\"labelUrl\":\"\",\"labelType\":\"activity\",\"labelNum\":0,\"pictureUrl\":\"https://mediaws.juneyaoair.com/upload/icon/av/memberFare.png\"},\"labelDetail\":{\"title\":\"会员专享运价规则\",\"detail\":[{\"title\":\"适用人群\",\"description\":\"吉祥航空实名制注册会员\",\"icon\":\"https://mediaws.juneyaoair.com/upload/icon/av/icon_channel.png\"},{\"title\":\"使用规则\",\"description\":\"预订会员专享价产品时，订单乘机人中需包含该会员账号本人。\",\"icon\":\"https://mediaws.juneyaoair.com/upload/icon/av/icon_date.png\"},{\"title\":\"适用航线\",\"description\":\"查询航班时显示有会员专享价标签的吉祥航空承运国内航班。\",\"icon\":\"https://mediaws.juneyaoair.com/upload/icon/av/icon_plane.png\"}]}}}")
    private LabelAndRuleInfo memberFareLabel;


    /**
     * 迪士尼专享运价标签
     */
    @ApolloJsonValue("${disneyFareLabel:{\"labelInfo\":{\"labelName\":\"迪士尼运价\",\"labelUrl\":\"\",\"labelType\":\"activity\",\"labelNum\":0,\"pictureUrl\":\"https://mediaws.juneyaoair.com/upload/icon/av/disneyFare.png\"},\"labelDetail\":{\"title\":\"会员专享运价规则\",\"detail\":[{\"title\":\"适用人群\",\"description\":\"吉祥航空实名制注册会员\",\"icon\":\"https://mediaws.juneyaoair.com/upload/icon/av/icon_channel.png\"},{\"title\":\"使用规则\",\"description\":\"预订会员专享价产品时，订单乘机人中需包含该会员账号本人。\",\"icon\":\"https://mediaws.juneyaoair.com/upload/icon/av/icon_date.png\"},{\"title\":\"适用航线\",\"description\":\"查询航班时显示有会员专享价标签的吉祥航空承运国内航班。\",\"icon\":\"https://mediaws.juneyaoair.com/upload/icon/av/icon_plane.png\"}]}}}")
    private LabelAndRuleInfo  disneyFareLabel;

    /**
     * 多人特惠运价标签
     */
    @ApolloJsonValue("${multiDiscountFareLabel:{\"labelInfo\":{\"labelName\":\"多人特惠\",\"labelUrl\":\"\",\"labelType\":\"activity\",\"labelNum\":0,\"pictureUrl\":\"https://mediaws.juneyaoair.com/upload/icon/av/multiDiscount.png\"},\"labelDetail\":{\"title\":\"多人特惠\",\"detail\":[{\"title\":\"适用条件\",\"description\":\"吉祥航空实名制注册会员\",\"icon\":\"https://mediaws.juneyaoair.com/upload/icon/av/icon_channel.png\"},{\"title\":\"适用舱位\",\"description\":\"经济舱适用舱位：Q\\\\V\\\\W\\\\S\\\\T, 不包含其他舱位。\",\"icon\":\"https://mediaws.juneyaoair.com/upload/icon/av/icon_date.png\"},{\"title\":\"使用规则\",\"description\":\"1，吉祥航空实际承运的航班限 2-9 名乘机人（婴儿不计）预订国内单程、国内往返程航班。\\n2，本运价不允许与其他销售产品、其他运价组合使用。\\n3，本运价不适用于团队、儿童、婴儿等特殊旅客。\",\"icon\":\"https://mediaws.juneyaoair.com/upload/icon/av/icon_plane.png\"}]}}}")
    private LabelAndRuleInfo multiDiscountFareLabel;

    /**
     * 机+接送机运价标签
     */
    @ApolloJsonValue("${airportTransferFareLabel:{\"labelInfo\":{\"labelName\":\"机+接送机\",\"labelUrl\":\"\",\"labelType\":\"activity\",\"labelNum\":0,\"pictureUrl\":\"https://mediaws.juneyaoair.com/upload/icon/jixiangchuxing.png\"},\"labelDetail\":{\"title\":\"吉享出行产品规则\",\"detail\":[{\"title\":\"预订须知\",\"description\":\"1.产品内容：本产品含1张价值150元的接送机抵扣券，在吉祥航空接送机频道中预订接送机服务时抵扣对应金额的面值。<br/>             2.适用人群：本产品仅适用于成人旅客，不适用于儿童、团队及婴儿旅客。<br/>             3.接送机券有效期：自发放之日起15天内有效。券过期未使用，不退款。<br/>             4.适用航线：查询航班时显示有“吉享出行”标签的吉祥航空实际承运国内航班，不含包机及代码共享航班。<br/>             5.积分累积：旅客实际使用“吉享出行”客票后，按照承运舱位累积积分及航段。<br/>             6.发票开具：机票使用后的28天内可申请机票产品电子发票或行程单。\",\"icon\":\"https://mediaws.juneyaoair.com/upload/icon/av/icon_channel.png\"},{\"title\":\"使用方式\",\"description\":\"1.旅客购买本机票产品后，产品中的接送机券会在购票后2小时内自动派发至预订人本人会员账户，可通过吉祥航空APP-我的-优惠券查询使用。<br/>             2.请根据行程提前至少3小时进行约车，以确保接送机服务的正常提供。<br/>             3.使用渠道：旅客可通过查看吉祥航空APP中接送机券或打开短信中的兑换链接，使用接送机券预订服务，支付时使用优惠券抵扣对应金额。仅限吉祥航空接送机频道中预订接送机服务时使用，适用城市和范围以实际系统查询结果为准。             <br/>4.使用规则：产品内的接送机券需一次性使用，不拆分、不找零。每张优惠券限用一次，每个订单仅支持使用一张优惠券，不支持叠加使用。             <br/>5.接送机券仅限单次行程使用，用于抵扣接送机账单中的对应金额，抵扣剩余部分不再返还，不足部分需补足。如订单金额小于或等于接送机券面值，旅客无需支付额外费用，接送机券超额部分不退还或兑现；如订单金额大于接送机券面值，旅客需补足剩余订单金额。\",\"icon\":\"https://mediaws.juneyaoair.com/upload/icon/av/icon_date.png\"},{\"title\":\"退改须知\",\"description\":\"1.本机票产品一经售出，接送机券不支持单独退款。<br/>             2.含接送机券订单一经提交，视为券已使用，超时未支付或支付后取消订单，券不予退还或退款。<br/>             3.如接送机券过期未使用，不退款。<br/>             4.客票自愿改期及退票，改期费、舱位差价、退票费按客票票面价计算。<br/>             5.客票非自愿改期及退票，不收取手续费。<br/>             6.客票发生自愿退改签、升舱换开，接送机券作废。<br/>             7.发生退款时，若使用了接送机券，则扣除券面值金额后再进行退款计算\",\"icon\":\"https://mediaws.juneyaoair.com/upload/icon/av/icon_plane.png\"}]}}}")
    private LabelAndRuleInfo airportTransferFareLabel;

    /**
     * 优享行李运价
     */
    @ApolloJsonValue("${baggageFareLabel:{\"labelInfo\":{\"labelName\":\"优享行李\",\"labelUrl\":\"\",\"labelType\":\"activity\",\"labelNum\":0,\"pictureUrl\":\"https://mediaws.juneyaoair.com/upload/icon/av/baggageFare.png\"},\"labelDetail\":{\"title\":\"行李优享\",\"detail\":[{\"title\":\"产品介绍\",\"description\":\"旅客预定“行李无忧”产品可获得40kg免费托运行李额，单件体积不超过40×60×100CM。\",\"icon\":\"https://mediaws.juneyaoair.com/upload/icon/av/icon_channel.png\"},{\"title\":\"适用航班\",\"description\":\"吉祥航空实际承运的国内航班（不含港澳台地区），不包含东航互售航班。\",\"icon\":\"https://mediaws.juneyaoair.com/upload/icon/av/icon_plane.png\"},{\"title\":\"适用人群\",\"description\":\"成人旅客，儿童、婴儿、关爱旅客不适用。\",\"icon\":\"https://mediaws.juneyaoair.com/upload/icon/av/icon_channel.png\"},{\"title\":\"航班日期\",\"description\":\"2021年1月1日~2021年12月31日\",\"icon\":\"https://mediaws.juneyaoair.com/upload/icon/av/icon_date.png\"},{\"title\":\"适用舱位\",\"description\":\"B/M/U/H/Q/V/W/S/T/Z/E/K/L，具体以实际销售舱位为准。\",\"icon\":\"https://mediaws.juneyaoair.com/upload/icon/av/icon_seat.png\"},{\"title\":\"退票，变更和签转规定\",\"description\":\"1、自愿退票：如旅客自愿退票：退票提交申请后行李额自动失效。<br>2、自愿变更：线上办理客票自愿变更，溢价行李额自动失效。<br>3、自愿签转：不允许自愿签转外航。<br>4、非自愿退票：旅客提交非自愿退票，客票按实际支付金额全额退还。<br>5、非自愿变更：线上办理客票非自愿变更，溢价行李额自动失效。<br>6、非自愿签转：溢价行李额自动失效。<br>如有问题请拨打95520处理。\",\"icon\":\"https://mediaws.juneyaoair.com/upload/icon/av/icon_channel.png\"}]}}}")
    private LabelAndRuleInfo baggageFareLabel;

    /** 无免费托运行李 */
    @ApolloJsonValue("${noFreeBaggageFareLabel:{\"labelInfo\":{\"labelName\":\"低碳特惠\",\"labelNum\":0,\"labelType\":\"activity\",\"labelUrl\":\"\",\"pictureUrl\":\"https://mediaws.juneyaoair.com/upload/icon/av/icon_dt.png\"},\"labelDetail\":{\"detail\":[{\"description\":\"无免费托运行李。\",\"icon\":\"https://mediaws.juneyaoair.com/upload/icon/av/icon_channel.png\",\"title\":\"低碳特惠行李规则：\"}],\"title\":\"低碳特惠运价规则\"}}}")
    private LabelAndRuleInfo noFreeBaggageFareLabel;


    /**
     * 首乘会员价
     */
    @ApolloJsonValue("${firstRideFareLabel:{\"labelInfo\":{\"labelName\":\"首乘会员价\",\"labelUrl\":\"\",\"labelType\":\"activity\",\"labelNum\":0,\"pictureUrl\":\"https://mediaws.juneyaoair.com/upload/icon/av/scth.png\"},\"labelDetail\":{\"title\":\"行李优享\",\"detail\":[{\"title\":\"产品介绍\",\"description\":\"旅客预定“行李无忧”产品可获得40kg免费托运行李额，单件体积不超过40×60×100CM。\",\"icon\":\"https://mediaws.juneyaoair.com/upload/icon/av/icon_channel.png\"},{\"title\":\"适用航班\",\"description\":\"吉祥航空实际承运的国内航班（不含港澳台地区），不包含东航互售航班。\",\"icon\":\"https://mediaws.juneyaoair.com/upload/icon/av/icon_plane.png\"},{\"title\":\"适用人群\",\"description\":\"成人旅客，儿童、婴儿、关爱旅客不适用。\",\"icon\":\"https://mediaws.juneyaoair.com/upload/icon/av/icon_channel.png\"},{\"title\":\"航班日期\",\"description\":\"2021年1月1日~2021年12月31日\",\"icon\":\"https://mediaws.juneyaoair.com/upload/icon/av/icon_date.png\"},{\"title\":\"适用舱位\",\"description\":\"B/M/U/H/Q/V/W/S/T/Z/E/K/L，具体以实际销售舱位为准。\",\"icon\":\"https://mediaws.juneyaoair.com/upload/icon/av/icon_seat.png\"},{\"title\":\"退票，变更和签转规定\",\"description\":\"1、自愿退票：如旅客自愿退票：退票提交申请后行李额自动失效。<br>2、自愿变更：线上办理客票自愿变更，溢价行李额自动失效。<br>3、自愿签转：不允许自愿签转外航。<br>4、非自愿退票：旅客提交非自愿退票，客票按实际支付金额全额退还。<br>5、非自愿变更：线上办理客票非自愿变更，溢价行李额自动失效。<br>6、非自愿签转：溢价行李额自动失效。<br>如有问题请拨打95520处理。\",\"icon\":\"https://mediaws.juneyaoair.com/upload/icon/av/icon_channel.png\"}]}}}")
    private LabelAndRuleInfo firstRideFareLabel;


    /**
     * 航班预订申请每个账号最大申请数量
     */
    @ApolloJsonValue("${reservationConfig:{\"receiverEmails\":[\"<EMAIL>\",\"<EMAIL>\"],\"maxApplyTime\":1}}")
    private ReservationConfig reservationConfig;

    /**
     * 会员秒杀舱位
     */
    @Value("${memberSecKillCabin:L}")
    private String memberSecKillCabin;

    /**
     * 会员秒杀标识
     */
    @Value("${memberSecKillTourCode:}")
    private String memberSecKillTourCode;

    /**
     * 会员秒杀运价标签
     */
    @ApolloJsonValue("${memberSecKillFareLabel:{\"labelInfo\":{\"labelName\":\"会员秒杀\",\"labelUrl\":\"\",\"labelType\":\"activity\",\"labelNum\":0,\"pictureUrl\":\"https://mediaws.juneyaoair.com/upload/icon/icon_seckill.png\"},\"labelDetail\":{\"title\":\"会员秒杀运价规则\",\"detail\":[{\"title\":\"适用人群\",\"description\":\"吉祥航空实名制注册会员\",\"icon\":\"https://mediaws.juneyaoair.com/upload/icon/av/icon_channel.png\"},{\"title\":\"使用规则\",\"description\":\"预订会员秒杀运价产品时，订单乘机人中需要包含该会员账号本人。\",\"icon\":\"https://mediaws.juneyaoair.com/upload/icon/av/icon_date.png\"},{\"title\":\"适用航线\",\"description\":\"查询航班时显示有会员秒杀价标签的吉祥航空承运国内航班。\",\"icon\":\"https://mediaws.juneyaoair.com/upload/icon/av/icon_plane.png\"}]}}}")
    private LabelAndRuleInfo memberSecKillFareLabel;

    /**
     * @Description: 准点率的配置 格式 80.00%
     * @Param: No such property: code for class: Script1
     * @return:
     * @Author:
     * @Date: 2020/4/8
     */
    @Value("${onTimeRate:80.00%}")
    private String onTimeRate;

    /**
     * 是否检验实名状态
     */
    @Value("${checkRealName:N}")
    private String checkRealName;



    /**
     * 免票运价查询页文案
     */
    @Value("${freeTicketDocument:<div class=\"tips\"><div class=\"tit\">温馨提示：</div><p class=\"info\">1.免票以电子兑换码的形式发放到医护人员本人的吉祥账户中，每一兑换码，对应一套往返程客票，不可兑换两程单程；</p><p class=\"info\">2.免票兑换券仅限客户本人使用，不可转赠，暂定为X舱，税费自理；</p><p class=\"info\">3.免票有效期三年，节假日可用，过期作废不补；</p><p class=\"info\">4.退改规则：已兑换客票前端支持退票操作，特殊情况可反馈至客户中心人工处理 ；</p><p class=\"info\">5.升舱规则：兑换客票原则上支持使用所赠升舱券，支持往返客票选择一程升舱，升舱后客票原则上不可变更，不可参与机上升舱、竞价升舱等其他升舱产品；</p><p class=\"info\">6.线上已兑完客票，可拨打95520联系客服</p></div>}")
    private String freeTicketDocument;

    /**
     * 免票兑换固定舱位
     * 奖励飞免票舱位
     */
    @Value("${awardFlyFreeTicketCabin:I,N}")
    private String awardFlyFreeTicketCabin;


    /**
     * 免票兑换固定舱位
     * 电商OPEN票
     */
    @Value("${openFreeTicketCabin:X}")
    private String openFreeTicketCabin;

    /**
     * 免票兑换固定舱位
     * 畅飞卡兑换舱位
     */
    @Value("${freeTicketCabin:X}")
    private String freeTicketCabin;

    /**
     * 免票兑换固定舱位
     * 公务舱免票舱位
     */
    @Value("${businessFlyFreeTicketCabin:I}")
    private String businessFlyFreeTicketCabin;

    /**
     * 奖励飞免票 运价查询页文案
     */
    @Value("${awardFlyFreeTicketDocument:<div class=\"tips\"><div class=\"tit\">温馨提示：</div><p class=\"info\">1.免票以电子兑换码的形式发放到本人的吉祥航空账户中，每一张100%机票抵扣券对应一套往返程客票，不可兑换两程单程；</p><p class=\"info\">2.需至少在航班计划起飞前2小时在吉祥航空APP中完成客票兑换；</p><p class=\"info\">3.兑换码兑换成100%机票抵扣券后，仅限客户本人使用，不可转赠，公务舱为I舱，经济舱为N舱，税费自理；</p><p class=\"info\">4.退改规则：已兑客票可自愿改期（拨打95520客户中心操作改期，只可改期至机票抵扣券可用时间段内，不可使用改期券），不可退票；</p><p class=\"info\">5.升舱规则：已兑客票不支持升舱券，不可参与机上升舱、竞价升舱等其他升舱产品；</p><p class=\"info\">6.已兑客票不享受超售补偿、航班延误补偿；</p><p class=\"info\">7.成人、儿童、婴儿同一兑换标准；需特殊服务的旅客不能使用免票；</p><p class=\"info\">8.兑换码发放后，无论激活与否，均不支持退。发出后若有盗用、遗失等，我司概不负责；100%往返机票票款抵用券逾期作废，不退不补；</p><p class=\"info\">每个航班限量兑换，每个航班设置10个可使用100%往返机票票款抵用券的座位，特殊情况可反馈至95520客户中心处理。</p></div>}")
    private String awardFlyFreeTicketDocument;
    /**
     * 公务舱免票
     */
    @Value("${businessFreeTicketDocument:}")
    private String businessFreeTicketDocument;

    /**
     * 奖励飞日历，查询天数。
     */
    @Value("${awareFlyPeriodQueryDay:30}")
    private Integer awareFlyPeriodQueryDay;

    /**
     * 奖励飞日历查询，是否使用redis。使用：Y 不使用：N
     */
    @Value("${awareFlyPeriodUserRedis:Y}")
    private String awareFlyPeriodUserRedis;

    @ApolloJsonValue("${logoutPageInfoConfig:{\"title\":\"注销后，您将放弃以下权益：\",\"content\":\"您可以永久注销自己的吉祥账号，注销后原账号将无法登录，原注册手机号及邮箱不可用于注册新账号。\\n原账号的身份信息、账户信息、优惠券、权益券、积分、吉豆等会员权益将被清空且无法恢复。\",\"title2\":\"您需要满足以下条件，方可注销账号：\",\"items\":[\"账户不存在未完成的订单\"]}}")
    private LogoutPageInfoConfig logoutPageInfoConfig;





    @ApolloJsonValue("${coBrandedCardInfoList:[{\"name\":\"吉祥航空-民生银行  联名信用卡\",\"pictureAddress\":\"1\",\"items\":[\"豪华卡\",\"精英卡\",\"标准卡\"],\"info\":\"消费可累积吉祥航空积分\",\"pageUrl\":\"1\"},{\"name\":\"吉祥航空-浦发银行  联名信用卡\",\"pictureAddress\":\"1\",\"items\":[\"尊享卡\",\"悦享卡\"],\"info\":\"消费可累积吉祥航空积分\",\"pageUrl\":\"1\"},{\"name\":\"吉祥航空-建设银行  联名信用卡\",\"pictureAddress\":\"1\",\"items\":[\"白金卡\",\"金卡\"],\"info\":\"消费可累积吉祥航空积分\",\"pageUrl\":\"1\"},{\"name\":\"吉祥航空-兴业银行  联名信用卡\",\"pictureAddress\":\"1\",\"items\":[\"尊享卡\",\"悦享卡\"],\"info\":\"消费可累积吉祥航空积分\",\"pageUrl\":\"1\"},{\"name\":\"吉祥航空-农业银行  联名信用卡\",\"pictureAddress\":\"1\",\"items\":[\"白金卡\",\"金卡\"],\"info\":\"消费可累积吉祥航空积分\",\"pageUrl\":\"1\"}]}")
    private List<CoBrandedCardInfo> coBrandedCardInfoList;


    @ApolloJsonValue("${associateMemberInfoList:[{\"name\":\"吉祥航空-民生银行  联名信用卡\",\"pictureAddress\":\"1\",\"pageUrl\":\"1\"},{\"name\":\"吉祥航空-浦发银行  联名信用卡\",\"pictureAddress\":\"1\",\"pageUrl\":\"1\"},{\"name\":\"吉祥航空-建设银行  联名信用卡\",\"pictureAddress\":\"1\",\"pageUrl\":\"1\"},{\"name\":\"吉祥航空-兴业银行  联名信用卡\",\"pictureAddress\":\"1\",\"pageUrl\":\"1\"},{\"name\":\"吉祥航空-农业银行  联名信用卡\",\"pictureAddress\":\"1\",\"pageUrl\":\"1\"}]}")
    private List<AssociateMemberInfo> associateMemberInfoList;
    /**
     * 授权访问IP
     */
    @Value("${authIp:127.0.0.1}")
    private String authIp;

    /**
     * 东方优行客服电话
     */
    @Value("${protocolTrafficCustomerServicePhone:021-64264686}")
    private String protocolTrafficCustomerServicePhone;
    /**
     * 自有未绑定无限卡提示
     */
    @Value("${ownerUpunlimitTip:您购买的无限升舱卡尚未完成绑定，请尽快前往”我的-权益券“完成绑定}")
    private String ownerUpunlimitTip;
    /**
     * 领取未绑定无限卡提示
     */
    @Value("${givingUpunlimitTip:好友赠送了您一张无限升舱卡，前往”我的-权益券“完成绑定后即可使用}")
    private String givingUpunlimitTip;

    @ApolloJsonValue("${activityConfigList:[{\"activityName\":\"无限升舱卡（全额抵扣升舱费）\",\"activityStartDate\":\"2020-07-04\",\"activityEndDate\":\"2020-12-31\",\"activityType\":\"flight\",\"activityIcon\":\"https://mediaws.juneyaoair.com/upload/icon/air/ho.png\",\"activityOpenUrl\":\"\",\"activityLabel\":\"热卖\"}]}")
    private List<ActivityConfig> activityConfigList;

    @ApolloJsonValue("${smsTempleList:[{\"couponType\":\"coupon\",\"content\":\"#name#送您一张吉祥航空优惠券，请在吉祥航空APP中登录/注册您的账户，在“我的优惠券”中查看，点击领取：#url#\"},{\"couponType\":\"rightcoupon\",\"content\":\"#name#送您一张吉祥航空权益券，请在吉祥航空APP中登录/注册您的账户，在“我的权益券”中查看，点击领取：#url#\",\"noSuitSource\":[\"UpgradeUnlimited\",\"ChildUnlimitedFly\",\"AdultUnlimitedFly\"]},{\"couponType\":\"rightcoupon\",\"content\":\"#name#赠送您一张“无限升舱卡”，领取成功后，请您尽快前往“吉祥航空App-我的-权益券”完成绑定，绑定后即可使用。如有疑问请致电95520。点击领取：#url#\",\"suitSource\":[\"UpgradeUnlimited\"]},{\"couponType\":\"rightcoupon\",\"content\":\"#name#赠送您一张“儿童畅飞卡”，领取成功后，请您尽快前往“吉祥航空App-我的-权益券”完成绑定，绑定后即可使用。如有疑问请致电95520。点击领取：#url#\",\"suitSource\":[\"ChildUnlimitedFly\"]},{\"couponType\":\"rightcoupon\",\"content\":\"#name#赠送您一张“吉祥畅飞卡”，领取成功后，请您尽快前往“吉祥航空App-我的-权益券”完成绑定，绑定后即可使用。如有疑问请致电95520。点击领取：#url#\",\"suitSource\":[\"AdultUnlimitedFly\"]}]}")
    private List<SmsTemplate> smsTempleList;

    @Value("${sms4DigitPayment:您本次交易的短信密码为[#code#]，订单编号为[#orderNo#]。}")
    private String sms4DigitPayment;

    @ApolloJsonValue("${HKMCTWRegions:[HKG, MFM, TPE, KHH]}")
    private List<String> HKMCTWRegions;

    /**
     * 升舱无限卡售卖开关
     */
    @ApolloJsonValue("${unLimitConfigList:[{\"activityId\":\"eb0c9b3cf4f14267b531dc84b17dad77\",\"activityName\":\"无限升舱卡\",\"activityStartDate\":\"2020-07-10 10:00\",\"activityEndDate\":\"2020-12-31 23:59\",\"activityType\":\"UpgradeUnlimited\"}]}")
    private List<ActivityConfig> unLimitConfigList;

    @ApolloJsonValue("${bankConfigList:[{\"bankCode\":\"BC\",\"iconUrl\":\"https://mediaws.juneyaoair.com/upload/icon/bank/BCM.png\"}]}")
    private List<DCEPayConfig> bankConfigList;

    @Value("${unlimitedCardTongDun:Y}")
    private String unlimitedCardTongDun;

    /**
     * 优惠券使用规则文案
     */
    @Value("${couponUseRuleDocument:<div class=\"rule-item\">1.优惠券仅限吉祥航空注册会员账户使用。</div><div class=\"rule-item\">2.每个订单仅能使用一张优惠券，不可多张优惠券同时使用，若订单中有多个航段，则优惠券按照各航段票面价格比例分摊。 </div> <div class=\"rule-item\">3.每种优惠券每个会员仅限领取一张，同一身份证号、手机号、邮箱均视为同一吉祥会员。 </div> <div class=\"rule-item\">4.优惠券不可提现，且仅可支付机票价格，不可支付机场建设费、变更费、升舱费等其他费用。 </div><div class=\"rule-item\">5.未支付订单，在订单取消后优惠券仍可在有效期内继续使用，若已支付，不可再次使用。 </div><div class=\"rule-item\">6.客票变更及退票：使用优惠券后发生任何自愿/非自愿退改时，优惠券作废，不可再次使用；使用优惠券所购客票变更时需同时收取票面差额与变更费，需补足优惠券抵扣金额，所使用的优惠券将自动作废；航班发生不正常时，使用优惠券所购客票进行签转、变更、退票后，所使用优惠券将自动作废。 </div><div class=\"rule-item\">7.吉祥航空发放所有优惠券严禁转让或出售，若发生违规行为，吉祥航空有权取消此用户的活动资格，并保留追究其法律责任的权利。</div><div class=\"rule-item\">8.活动服务热线：021-95520。 </div>}")
    private String couponUseRuleDocument;

    /**
     * 开放一单多券后使用新的优惠券规则文案
     */
    @Value("${couponUseRuleDocumentNew:<div class=\"rule-item\">\n" +
            "    1.优惠券仅限吉祥航空已实名注册的会员账户使用。\n" +
            "</div>\n" +
            "<div class=\"rule-item\">\n" +
            "    2.每张订单最多可使用优惠券张数以实际规则为准；若订单中有多个航段，则优惠券按照各航段票面价格比例分摊。 </div>\n" +
            "<div class=\"rule-item\">3.每种优惠券最多可领取数量以实际规则为准；同一身份证号、手机号邮箱均视为同一吉祥会员。</div>\n" +
            "<div class=\"rule-item\">4.优惠券不可提现，且仅可支付机票价格，不可支付机场建设费、变更费、升舱费等其他费用。</div>\n" +
            "<div class=\"rule-item\">5.未支付订单，在订单取消后优惠券仍可在有效期内继续使用若已支付，不可再次使用。</div>\n" +
            "<div class=\"rule-item\">\n" +
            "    6.客票变更及退票:使用优惠券后发生任何自愿/非自愿退改时，优惠券作废，不可再次使用:使用优惠券所购客票变更时需同时收取票面差额与变更费，需补足优惠券抵扣金额，所使用的优惠券将自动作废；航班发生不正常时，使用优惠券所购的客票进行签转、变更、退票后，所使用优惠券将自动作废。\n" +
            "</div>\n" +
            "<div class=\"rule-item\">7.吉祥航空发放所有优惠券严禁转让或出售，若发生违规行为，吉祥航空有权取消此用户的活动资格，并保留追究其法律责任的权利。</div>\n" +
            "<div class=\"rule-item\">8.积分累积与舱位和实际支付费用有关，使用优惠券会改变实际支付费用，从而对积分累积产生影响；积分累积规则详见《会员手册》及各优惠券的实际规定。</div>\n" +
            "<div class=\"rule-item\">9.活动服务热线:021-95520。</div>}")
    private String couponUseRuleDocumentNew;

    @ApolloJsonValue("${transferTips:{\"title\":\"中转预订须知\",\"detail\":[{\"description\":\"1.我司暂不支持行李直挂，请您在中转机场重新办理托运及乘机手续，请注意留出足够时间换乘，以免耽误您的行程。\"},{\"description\":\"2.当您预订联程航班，前一段发生非自愿变更时，后段可免费变更。\"},{\"description\":\"3.中转航班退票时，未使用航段需同时提交退票。\"}]}}")
    private LabelDetail transferTips;

    /**
     * 深港通航班中转提示
     */
    @ApolloJsonValue("${busTransferTips:{\"title\":\"中转预订须知\",\"detail\":[{\"description\":\"1.适用范围：适用于吉祥航空实际承运的 上海=深圳 单程航班 + 深圳至香港单程接送机服务组合产品。\"},{\"description\":\"2.购买范围：该产品仅限吉祥航空实名认证会员本人购买。\"},{\"description\":\"3.适用旅客：仅限成人旅客，儿童及婴儿不适用。\"},{\"description\":\"4.航班日期：即日起-2025年10月29日\"},{\"description\":\"5.服务内容：接送机券仅可抵扣“深圳宝安机场=香港接送机”服务，但不可抵扣旅客出入关或路程中因旅客原因产生的其他费用。部分热门日期可能存在补差，请务必与供应商确认后再进行预约。\"},{\"description\":\"6.服务及抵扣次数：每张客票包含 1 次“深圳宝安机场=香港”接送机服务抵扣券，不可重复叠加使用。\"},{\"description\":\"7.接送机服务提供方：由深圳市天行家科技有限公司（飞泊通）提供专业跨境接送服务。\"},{\"description\":\"8.预约流程：航班起飞前3天在订单页面展示接送机服务抵用券码及服务预约入口，请点击服务预约入口添加飞泊通专属客服微信，由专属客服为您完成预约。（至少提前 1 个工作日）\"},{\"description\":\"9.特殊情况处理：如遇预约失败，服务商将主动联系改期或全额退款\"},{\"description\":\"10.服务流程：出行当日请提前到达约定的汇合点，汇合后请主动出示服务凭证，验证无误后司机将依照预约内容提供接送机服务。\"},{\"description\":\"11.额外等待服务费：免费等待时间 1 小时，超时按 200 元 / 小时收取服务费\"},{\"description\":\"12.退订：①退订流程：客票退票时，接送机服务同步失效，退票手续费以航空公司退改规定为准。。②当天退订：因乘机人出行日程变更而导致无法成行的情况，在航班起飞前24小时内退订，将额外征收800元用车取消手续费。非旅客原因导致的退订或24小时外取消用车，不额外征收用车取消手续费。\"},{\"description\":\"13.改期流程：①改期流程：如机票改期，需至少提前 1天完成包车行程修改，请主动联系飞泊通客服申请。②当天改期：因乘机人出行日程变更导致无法成行的情况，起飞时间24小时内改期，用车服务将无法一同改期，由此产生的相关损失需由乘机人承担。③客票签转：吉祥深港通产品不可签转外航。\"},{\"description\":\"14.产品发票：旅客实际成行后，可致电客服中心95520申请开具电子发票，发票内容为：运输服务国际机票款，发票备注为“吉祥深港通”，发票抬头可选择个人或公司抬头，发票金额为您在吉祥航空 APP 购买“吉祥深港通”实付金额，服务补差金额可在飞泊通 APP 开具相应产品发票。\"},{\"description\":\"15.其他注意事项：（1）除法律另有规定情形外，吉祥航空针对产品购买者的合同责任以退还实收产品价格为限。因该产品的合约履行过程导致的相关争议，双方应协商解决。协商无法解决的情形，应提请上海市上海浦东新区人民法院诉讼解决。（2）除违反先前双方已生效的合约条款、承诺/要约的情形外，吉祥航空对于尚不完善的产品规则保留解释、优化、公布生效等必要权利，相关变更一经公布生效，对吉祥航空及产品购买者双方具有同等约束力。（3）在进行不正常航班处置时，若旅客发生在航空器上强占座位、辱骂殴打他人、妨碍机组正常履行职责、霸占航空器、破坏机上设施设备等扰乱公共秩序、危害公共安全的行为，借助投诉或索赔谋取不正当利益的，则次卡自动失效。（4）购买者在此请再次确认知悉并理解“吉祥深港通”产品规则、其他规定及提示的可能出现的各类情况/风险，并确认同意上述产品规则全部内容。（5）部分节假日包车服务需要补交差价。具体补差额度为：1月26日（腊月廿七）补差200元；1月27日（腊月廿八）补差200元；1月28日（除夕）补差400元；1月29日（初一）补差300元；1月30日（初二）补差200元；1月31日（初三）补差200元；2月4日（初七）香港-深圳补差100元；2月5日（初八）香港-深圳补差100元；9月30日（国庆前1日）深圳-香港补差100元；10月1日（国庆当日   1.深圳-香港补差100元；10月6日（国庆回程高峰）香港-深圳补差100元；10月7日（国庆回程高峰）香港-深圳补差100元。\"}]}}")
    private LabelDetail busTransferTips;

    @ApolloJsonValue("${transferITips:{\"title\":\"中转预订须知\",\"detail\":[{\"description\":\"1.我司暂不支持行李直挂，请您在中转机场重新办理托运及乘机手续，请注意留出足够时间换乘，以免耽误您的行程。\"},{\"description\":\"2.当您预订联程航班，前一段发生非自愿变更时，后段可免费变更。\"},{\"description\":\"3.中转航班退票时，未使用航段需同时提交退票。\"}]}}")
    private LabelDetail transferITips;

    /**
     * 通程中转提示
     */
    @ApolloJsonValue("${throughCheckTransferTips:{\"title\":\"中转预订须知\",\"detail\":[{\"description\":\"1.当您预订中转航班，前一段航班发生非自愿变更时，后段航班可免费变更。\"},{\"description\":\"2.中转航班退票时，未使用的联程航段需同时提交退票。\"},{\"description\":\"3.国内通程航班享受保障：一次支付、一次值机、一次安检、行李直挂、全程无忧。服务保障因机场而异，请以现场实际情况为准。\"},{\"description\":\"4.行李直挂的定义：当您在出发地办理行李托运时，可直接将行李托运至最终目的地，在中转过程中无需提取行李。\"},{\"description\":\"5.预订（G+G）中转舱位运价时，乘机人需包含会员本人。\"}]}}")
    private LabelDetail throughCheckTransferTips;

    /**
     * 行李直挂提示
     */
    @ApolloJsonValue("${baggageThroughTransferTips:{\"title\":\"中转预订须知\",\"detail\":[{\"description\":\"1.当您预订中转航班，前一段航班发生非自愿变更时，后段航班可免费变更。\"},{\"description\":\"2.中转航班退票时，未使用的联程航段需同时提交退票。\"},{\"description\":\"3.该航班已支持行李直挂。具体情况请以现场为准。\"},{\"description\":\"4.行李直挂的定义：当您在出发地办理行李托运时，可直接将行李托运至最终目的地，在中转过程中无需提取行李。\"},{\"description\":\"5.预订（G+G）中转舱位运价时，乘机人需包含会员本人。\"}]}}")
    private LabelDetail baggageThroughTransferTips;

    /**
     * 2021-05-21 行李直达航班中转须知
     */
    @ApolloJsonValue("${baggageDirectTransferTips:{\"title\":\"中转预订须知\",\"detail\":[{\"description\":\"1.此中转航班支持办理行李直挂及全程一次值机服务。您可在始发地机场柜台直接办理两段航班的值机手续；您的托运行李将栓挂吉祥航空专用中转行李牌，无需在中转机场提取行李再次托运。\"},{\"description\":\"2.当您预订联程航班，前一段发生非自愿变更时，后段可免费变更。\"},{\"description\":\"3.中转航班退票时，未使用航段需同时提交退票。\"}]}}")
    private LabelDetail baggageDirectTransferTips;

    @ApolloJsonValue("${multipleFlightTransferConfig:{\"filterCabinClass\":false,\"sameAirportIntervals\":3600000,\"sameCityDifAirportIntervals\":3600000,\"differentCityIntervals\":3600000}}")
    private MultipleFlightTransferConfig multipleFlightTransferConfig;

    /**
     * 东方优行提示信息
     */
    @Value("${airportProtocolMessage:目前可预订接机服务的城市有：广州、深圳、杭州；送机服务所有城市暂未开通，敬请关注后续通知！所有可预订的城市，均以服务商的通知为准，我们谢谢您的理解与支持！如有任何问题，请致电东方优行客服电话021-64264686。}")
    private String airportProtocolMessage;

    @Value("${geetestOneLoginRiskLevelThreshold:7}")
    private int geetestOneLoginRiskLevelThreshold;

    /**
     * 高舱高返运价标签
     */
    @Value("${highRebateFareLabel:{\"labelInfo\":{\"labelName\":\"高舱高返\",\"labelUrl\":\"\",\"labelType\":\"activity\",\"labelNum\":0,\"pictureUrl\":\"https://mediaws.juneyaoair.com/upload/icon/av/highRebateFare.png\"},\"labelDetail\":{\"title\":\"差旅惠选产品规则\",\"detail\":[{\"title\":\"预订规则\",\"description\":\"1.旅客预订航班时，选择购买“差旅惠选”产品可获赠改期券，改期券自付款之日起15天内有效</br>2.在乘机人完成航空出行后三个工作日内，购票人账户将得到相同舱位溢价部分60%的等额积分返还</br>3.若航班延误三个小时及以上，购票人账户将额外获得100积分/张的补偿\",\"icon\":\"https://mediaws.juneyaoair.com/upload/icon/av/icon_rule.png\"},{\"title\":\"适用舱位\",\"description\":\"U\\\\H\\\\Q\\\\V\\\\W\\\\S\\\\T\\\\Z\\\\E\\\\K\\\\L，具体以实际销售舱位为准\",\"icon\":\"https://mediaws.juneyaoair.com/upload/icon/av/icon_seat.png\"},{\"title\":\"适用航线\",\"description\":\"吉祥航空国内航班（不含港澳台地区），不包含代码共享航班和包机航线\",\"icon\":\"https://mediaws.juneyaoair.com/upload/icon/av/icon_plane.png\"},{\"title\":\"积分累积\",\"description\":\"标题为“积分累积”时动态替换此字段:购票人账户获赠#score#积分奖励\",\"icon\":\"https://mediaws.juneyaoair.com/upload/icon/av/icon_score.png\"},{\"title\":\"销售渠道\",\"description\":\"APP、微信公众号\",\"icon\":\"https://mediaws.juneyaoair.com/upload/icon/av/icon_channel.png\"}]}}}")
    private String highRebateFareLabel;

    /**
     * wifi运价标签
     */
    @ApolloJsonValue("${wifiLabelAndRuleInfo:{\"labelInfo\":{\"labelName\":\"WIFI专享\",\"labelUrl\":\"\",\"labelType\":\"activity\",\"labelNum\":0,\"pictureUrl\":\"https://mediaws.juneyaoair.com/upload/icon/av/highRebateFare.png\"},\"labelDetail\":{\"title\":\"WIFI专享产品规则\",\"detail\":[{\"title\":\"预订规则\",\"description\":\"1.旅客预订航班时，选择购买“差旅惠选”产品可获赠改期券，改期券自付款之日起15天内有效</br>2.在乘机人完成航空出行后三个工作日内，购票人账户将得到相同舱位溢价部分60%的等额积分返还</br>3.若航班延误三个小时及以上，购票人账户将额外获得100积分/张的补偿\",\"icon\":\"https://mediaws.juneyaoair.com/upload/icon/av/icon_rule.png\"},{\"title\":\"适用舱位\",\"description\":\"U\\\\H\\\\Q\\\\V\\\\W\\\\S\\\\T\\\\Z\\\\E\\\\K\\\\L，具体以实际销售舱位为准\",\"icon\":\"https://mediaws.juneyaoair.com/upload/icon/av/icon_seat.png\"},{\"title\":\"适用航线\",\"description\":\"吉祥航空国内航班（不含港澳台地区），不包含代码共享航班和包机航线\",\"icon\":\"https://mediaws.juneyaoair.com/upload/icon/av/icon_plane.png\"},{\"title\":\"积分累积\",\"description\":\"标题为“积分累积”时动态替换此字段:购票人账户获赠#score#积分奖励\",\"icon\":\"https://mediaws.juneyaoair.com/upload/icon/av/icon_score.png\"},{\"title\":\"销售渠道\",\"description\":\"APP、微信公众号\",\"icon\":\"https://mediaws.juneyaoair.com/upload/icon/av/icon_channel.png\"}]}}}")
    private LabelAndRuleInfo wifiLabelAndRuleInfo;

    @ApolloJsonValue("${wifiTravelPrivilege:{\"Name\":\"WIFI专享\",\"Description\":\"购票享机上WIFI放心用\",\"Type\":\"wifi\",\"Number\":0,\"sortPriority\":0}}")
    private TravelPrivilege wifiTravelPrivilege;

    /**
     * 随心飞舱位展示航班时间
     */
    @Value("${unlimitedFlyDateLimit:2021-01-20}")
    private String unlimitedFlyDateLimit;

    /**
     * 展示随心飞时间开始
     */
    @Value("${showUnlimitedFlyDateBegin:2020-08-18 09:30:00}")
    private String showUnlimitedFlyTimeBegin;

    /**
     * 是否开启受益人制度
     * Y/N
     */
    @Value("${limitScoreUseDate:2020-08-18}")
    private String limitScoreUseDate;

    /**
     * 积分使用风控开始时间 2020-09-10
     */
    @Value("${limitScoreRiskUseDate:2020-09-28}")
    private String limitScoreRiskUseDate;
    /**
     * 值机选座查询是否开启同盾验证
     * Y/N
     */
    @Value("${checkinSeatTongDun:N}")
    private String checkinSeatTongDun;

    @Value("${freeTicket5DayLimit:5}")
    private int freeTicket5DayLimit;

    @Value("${wxappSwitch:N}")
    private String wxappSwitch;
    /**
     * 是否查询航班推荐
     */
    @Value("${useFlightRecommend:Y}")
    private String useFlightRecommend;


    /**
     * 接口日志配置
     * Key: 请求路径开头
     * Value: 日志级别，全大写
     * 没配置的请求默认打info级别
     */
    @ApolloJsonValue("${httpLogConfig:{\"http://10.12.3.46:9032\":\"DEBUG\",\"http://oneorder.juneyaoair.com:9031\":\"DEBUG\",\"https://horder.hoair.cn\":\"DEBUG\"}}")
    private Map<String, String> httpLogConfig;

    @Value("${mwebOrderChannel:WEIXIN}")
    private String mwebOrderChannel;

    /**
     * 运价查询中转联程显示最大航班数
     */
    @Value("${oneWayFareFlightNumber: 4}")
    private int oneWayFareFlightNumber;

    @Value("${isNewProcessInsurance:N}")
    private String isNewProcessInsurance;

    /**
     * 意见建议走哪个系统
     * P：旅客服务网，O：统一订单
     */
    @Value("${suggestionSystem:P}")
    private String suggestionSystem;

    /**
     * 是否开启ip限制
     */
    @Value("${foreverLimitSwitch: Y}")
    private String foreverLimitSwitch;

    /**
     * 先飞后付白名单 PAF payAfterFly
     */
    @Value("${whiteListOfPAF: }")
    private String whiteListOfPAF;

    @ApolloJsonValue("${captchaSendIntervalConfigs:[]}")
    private List<CaptchaSendIntervalConfig> captchaSendIntervalConfigs;

    /**
     * 茅台出港配置
     */
    @ApolloJsonValue("${outMaoTaiConfig:{\"startDate\":\"2021-12-09\",\"endDate\":\"2022-01-01\",\"flightStartDate\":\"2021-12-12\",\"flightEndDate\":\"2022-01-01\",\"flightNo\":\"HO1086\",\"cabins\":\"L\",\"labelAndRuleInfo\":{\"labelInfo\":{\"labelName\":\"茅台专享\",\"labelUrl\":\"\",\"labelType\":\"activity\",\"labelNum\":0,\"pictureUrl\":\"https://mediaws.juneyaoair.com/upload/icon/av/maotaizhuanxiang.png\"},\"labelDetail\":{\"title\":\"茅台专享产品规则\",\"detail\":[{\"title\":\"预订规则\",\"description\":\"1、旅客预订茅台航线航班时，选择购买“茅台专享”专属舱位产品并实际乘机抵达航班目的地后，即可在“茅台机场”APP填写相关信息参与预约购酒活动<br>2、活动期间，旅客购买“茅台专享”专属舱位产品不可使用积分、优惠券抵扣票款，“茅台专享”舱位不享受积分、定级航段、续级航段奖励，同时不参与吉祥航空官方同期其他活动，包含但不仅限于加速飞/快速升金等会员活动<br>3、茅台专享购酒活动仅限成人旅客参与，未成年旅客（未满18周岁）不可参与本活动<br>4、购买“茅台专享”专属舱位产品后，建议您不要使用升舱、自愿改期等可能变更舱位等级的服务，以免影响您的购酒权益<br>5、茅台酒销售活动须知及特别注意事项以茅台机场公众号为准\",\"icon\":\"https://mediaws.juneyaoair.com/upload/icon/av/icon_rule.png\"},{\"title\":\"适用舱位\",\"description\":\"经济舱L舱，具体以实际销售舱位为准\",\"icon\":\"https://mediaws.juneyaoair.com/upload/icon/av/icon_seat.png\"},{\"title\":\"适用航班\",\"description\":\"吉祥航空实际承运茅台机场出港航班，具体以产品实际销售航班为准\",\"icon\":\"https://mediaws.juneyaoair.com/upload/icon/av/icon_plane.png\"},{\"title\":\"销售渠道\",\"description\":\"吉祥航空APP\",\"icon\":\"https://mediaws.juneyaoair.com/upload/icon/av/icon_channel.png\"}]}}}}")
    private MaoTaiConfig outMaoTaiConfig;
    /**
     * 茅台进港配置
     */
    @ApolloJsonValue("${inMaoTaiConfig:{\"startDate\":\"2021-12-16\",\"endDate\":\"2022-01-01\",\"flightStartDate\":\"2021-12-19\",\"flightEndDate\":\"2022-01-01\",\"flightNo\":\"HO1085\",\"cabins\":\"L\",\"labelAndRuleInfo\":{\"labelInfo\":{\"labelName\":\"茅台专享\",\"labelUrl\":\"\",\"labelType\":\"activity\",\"labelNum\":0,\"pictureUrl\":\"https://mediaws.juneyaoair.com/upload/icon/av/maotaizhuanxiang.png\"},\"labelDetail\":{\"title\":\"茅台专享产品规则\",\"detail\":[{\"title\":\"预订规则\",\"description\":\"1、旅客预订茅台航线航班时，选择购买“茅台专享”专属舱位产品并实际乘机抵达航班目的地后，即可在“茅台机场”APP填写相关信息参与预约购酒活动<br>2、活动期间，旅客购买“茅台专享”专属舱位产品不可使用积分、优惠券抵扣票款，“茅台专享”舱位不享受积分、定级航段、续级航段奖励，同时不参与吉祥航空官方同期其他活动，包含但不仅限于加速飞/快速升金等会员活动<br>3、茅台专享购酒活动仅限成人旅客参与，未成年旅客（未满18周岁）不可参与本活动<br>4、购买“茅台专享”专属舱位产品后，建议您不要使用升舱、自愿改期等可能变更舱位等级的服务，以免影响您的购酒权益<br>5、茅台酒销售活动须知及特别注意事项以茅台机场公众号为准\",\"icon\":\"https://mediaws.juneyaoair.com/upload/icon/av/icon_rule.png\"},{\"title\":\"适用舱位\",\"description\":\"经济舱L舱，具体以实际销售舱位为准\",\"icon\":\"https://mediaws.juneyaoair.com/upload/icon/av/icon_seat.png\"},{\"title\":\"适用航班\",\"description\":\"吉祥航空实际承运茅台机场进港航班，具体以产品实际销售航班为准\",\"icon\":\"https://mediaws.juneyaoair.com/upload/icon/av/icon_plane.png\"},{\"title\":\"销售渠道\",\"description\":\"吉祥航空APP\",\"icon\":\"https://mediaws.juneyaoair.com/upload/icon/av/icon_channel.png\"}]}}}}")
    private MaoTaiConfig inMaoTaiConfig;

    @Value("${compensateFWRedirectB2CUrl:http://www.juneyaoair.com/pages/MyAccount/flightSafeAliPay.aspx}")
    private String compensateFWRedirectB2CUrl;

    @Value("${compensateFWRedirectPayUrl:http://epay.juneyaoair.com:86/AlipayAuth/AlipayGetAuthUserInfo.aspx}")
    private String compensateFWRedirectPayUrl;

    @Value("${realNameCheckUrl:http://epay.juneyaoair.com:86/AlipayAuth/AlipayGetAuthUserInfoSync.aspx}")
    private String realNameCheckUrl;

    @Value("${compensateGateWayNo:A0111}")
    private String compensateGateWayNo;

    @Value("${compensateApiRedirectUrl:https://m.juneyaoair.com/server/compensate/redirect2Epay}")
    private String compensateApiRedirectUrl;

    @Value("${tripCertChannelCodes:MOBILE,B2C,MWEB,WEIXIN,CallCenter,UMETRIP}")
    private String tripCertChannelCodes;

    /**
     * 畅飞卡2.0限制
     */
    @ApolloJsonValue("${unlimitedCard2Config:{\"showCabinTimeBegin\":\"2020-12-07 00:00:00\",\"flightTimeBegin\":\"2021-01-21 00:00:00\",\"flightTimeEnd\":\"2021-06-30 23:59:59\",\"bCardUnusableFlightTimeBegin\":\"2021-02-01 00:00:00\",\"bCardUnusableFlightTimeEnd\":\"2021-02-28 23:59:59\",\"freeTicket3DayLimit\":3,\"payConfigUseCache\":true,\"availDateFly\":\"2021-01-21至2021-06-30?(不含2021-02-01至2021-02-28)\",\"availDateFlySF\":\"2021-01-21至2021-06-30\",\"sfTimeShowDoctorFreeTicket\":false,\"useCacheQueryProduct\":true,\"cardSaleTimeBegin\":\"2020-12-01 10:00:00\",\"noShowDay\":3}}")
    private UnlimitedCard2Config unlimitedCard2Config;

    /**
     * 机上购物经济舱允许餐食升级的餐食类型
     */
    @ApolloJsonValue("${shoppingBoradMealType:[L, D, B, S, M]}")
    private List<String> shoppingBoradMealType;

    /**
     * 机上购物经济舱允许餐食升级的餐食类型
     */
    @ApolloJsonValue("${shoppingBoradDepAirportCode:[PVG]}")
    private List<String> shoppingBoradDepAirportCode;

    @ApolloJsonValue("${gmjcLimitLine:[\"SHAWMT\", \"WMTSHA\"]}")
    private List<String> gmjcLimitLine;

    @ApolloJsonValue("${voucherRefundChannelCodes:[\"MOBILE\",\"WEIXIN\"]}")
    private List<String> voucherRefundChannelCodes;

    @Value("${tripCertOwnSaleCheck:Y}")
    private String tripCertOwnSaleCheck;

    @Value("${huazhuHotelRefundTimeLimit:2021-01-17 23:59:59}")
    private String huazhuHotelRefundTimeLimit;

    @ApolloJsonValue("${checkInPolicyConfig:{}}")
    private CheckInPolicyConfig checkInPolicyConfig;

    @ApolloJsonValue("${preferredSeatAgreements:{\"checkInSeatAgreements\":[{\"name\":\"优选座位产品协议\",\"url\":\"https://mediaws.juneyaoair.com/upload/clause/juneyaoairseat.pdf\"}]}}")
    private CheckInPolicyConfig preferredSeatAgreements;
    /**
     * 清除产品缓存
     */
    @Value("${cleanProductCache:}")
    private String cleanProductCache;

    /**
     * 升舱卡2.0配置
     */
    @ApolloJsonValue("${upgradeCardV2Config:{\"cardSaleTimeBegin\":\"2020-12-24 10:00:00\",\"yearCardFlightTimeBegin\":\"2021-01-01 00:00:00\",\"yearCardFlightTimeEnd\":\"2021-12-31 23:59:59\",\"unusableTimeBegin\":\"2021-02-01 00:00:00\",\"unusableTimeEnd\":\"2021-02-01 00:00:00\"}}")
    private UpgradeCardV2Config upgradeCardV2Config;

    @ApolloJsonValue("${fraudEscapeConfig:{\"minMemberLevel\":4,\"ipWhiteList\":[]}}")
    private FraudEscapeConfig fraudEscapeConfig;

    @ApolloJsonValue("${studentVerifyConfig:}")
    private StudentVerifyConfig studentVerifyConfig;


    /**
     * 对外接口密钥
     */
    @Value("${apiKey:gr9H97OanBO8RnqqrC19dfzPFFd5004k}")
    private String apiKey;

    /**
     * 行李直达配置
     */
    @ApolloJsonValue("${baggageDirectConfig:{\"airlines\":[{\"depAirport\":\"CGQ\",\"arrAirport\":\"PVG\"},{\"depAirport\":\"SHE\",\"arrAirport\":\"PVG\"},{\"depAirport\":\"TAO\",\"arrAirport\":\"PVG\"},{\"depAirport\":\"CIF\",\"arrAirport\":\"PVG\"},{\"depAirport\":\"DOY\",\"arrAirport\":\"PVG\"},{\"depAirport\":\"YZY\",\"arrAirport\":\"PVG\"},{\"depAirport\":\"ACX\",\"arrAirport\":\"PVG\"},{\"depAirport\":\"LPF\",\"arrAirport\":\"PVG\"},{\"depAirport\":\"XFN\",\"arrAirport\":\"PVG\"},{\"depAirport\":\"CIH\",\"arrAirport\":\"PVG\"},{\"depAirport\":\"SHE\",\"arrAirport\":\"NKG\"},{\"depAirport\":\"CIF\",\"arrAirport\":\"NKG\"}],\"labelInfo\":{\"labelName\":\"行李直达\",\"labelUrl\":\"https://m.juneyaoair.com/flights/index.html#/luggageDirect\",\"labelType\":\"main\",\"labelNum\":0}}}")
    private BaggageDirectConfig baggageDirectConfig;

    /**
     * 假期候补车票 发送短信内容
     */
    @Value("${holidayReserveFightSmsContent:尊敬的旅客：您所申请的%s由%s到%s的航线已为您登记，如有可候补座位，我们将在航班起飞前一天的13:00前通过电话联系您。如您在该时间节点前未收到回电，则代表您所申请的航线无可候补座位。如有疑问，请您咨询吉祥航空客服热线：95520。感谢您的理解及支持。}")
    private String holidayReserveFightSmsContent;

    /**
     * 假期候补车票 规定城市三字码
     */
    @Value("${holidayReserveFlightCityList:SHA/XIY/KWL/CTU/KWE/HAK/SYX/CSX/TYN/LJG/DYG}")
    private String holidayReserveFlightCityList;

    /**
     * 版本号列表
     */
    @ApolloJsonValue("${versionMember:{\"versionMembers\":[\"18700\",\"18100\",\"18900\"]}}")
    private VersionMember versionMember;

    /**
     * 版本号控制
     */
    @Value("${versionControl:6.7.9}")
    private String versionControl;
    /**
     * 2021-05-24 国际品牌运价查询运价类型
     */
    @Value("${interBrandFareType:S}")
    private String interBrandFareType;

    /**
     * 2021-06-24 需要国际品牌运价查询的城市 国家代码
     */
    @ApolloJsonValue("${interBrandFareCountryCodes:[GB, FI]}")
    private List<String> interBrandFareCountryCodes;

    /**
     * 2021-07-23 打包券订单开具电子发票活动编号
     */
    @ApolloJsonValue("${invoiceActivityNos:[3844e162-c450-4ff9-94fc-7258d52588ed, 30f56918-45de-40b7-bf22-39eea2524c3b]}")
    private List<String> invoiceActivityNos;

    /**
     * 移动端redis有效期，24小时
     */
    @Value("${mobileRedisExpireTimeSeconds:86400}")
    private long mobileRedisExpireTimeSeconds;

    /**
     * 移动端token有效期，15天
     */
    @Value("${mobileTokenExpireTimeMilli:1296000000}")
    private long mobileTokenExpireTimeMilli;

    @ApolloJsonValue("${prepaymentBaggageDocumentResp:{\"standard\":\"中国国内(不含港澳台地区)\",\"useMode\":\"文案文案\",\"purchaseNotes\":\"文案文案\",\"other\":\"文案文案\"}}")
    private PrepaymentBaggageDocumentResp prepaymentBaggageDocumentResp;

    /**
     * 发送短信次数（总发送次数非操作错误次数）
     */
    @ApolloJsonValue("${sendSmsLimit:{\"limitDay\":{\"frequency\":86400,\"accessLimit\":30},\"limitForever\":{\"frequency\":0,\"accessLimit\":60},\"limitMin\":{\"frequency\":180,\"accessLimit\":10}}}")
    private LimitCount sendSmsLimit;

    /**
     * 特殊城市行李配置
     */
    @ApolloJsonValue("${flightReminderV2List:[]}")
    private List<FlightReminderV2> flightReminderV2List;

    /**
     * 是否开放查询弹窗
     */
    @Value("${openAppAdvert:Y}")
    private String openAppAdvert;
    /**
     * 控制页面API是否发送请求
     */
    @ApolloJsonValue("${apiLimitList:[]}")
    private List<ApiLimit> apiLimitList;

    /**
     * 是否关闭特殊接口，默认不关闭
     */
    @Value("${closed:N}")
    private String closed;
    /**
     * 旅游度假频道开关  Y-打开查询，N-关闭查询
     */
    @Value("${holidaySwitch:Y}")
    private String holidaySwitch;

    /**
     * 留学生温馨提示
     */
    @Value("${studentTips:<div class=\"tips\"><div class=\"tit\">注意事项：</div><p class=\"info\">1.留学生专享机票仅限在校留学生本人实名注册后预订国际客票时使用，不得为他人预定机票。</p > <p class=\"info\">2.使用留学生专享机票时，需要持有乘机人的留学生资质证明。例：目的地国家/地区学生签证或学生证。</p ><p class=\"info\">3.预定留学生专享机票的旅客，享受与普通国际客票旅客同等服务。</p ><p class=\"info\">4.预定留学生运价的旅客需随身携带有效目的地国家/地区学生签证(购票下单时的乘机人证件信息须与留学生资质证明一致)，吉祥航空及其联运航空公司有权在值机和登机过程中查验。否则，吉祥航空及其联运航空公司有权根据出入境相关要求，拒绝旅客登机，造成的后果由旅客自行负责。</p ></div>}")
    private String studentTips;
    /**
     * 主题卡温馨提示
     */
    @Value("${themeTips:<div class=\"tips\"><div class=\"tit\">机票兑换：</div><p class=\"info\">您需在航班起飞日期前3天（含当天）完成客票兑换。例：如果要预定3月10日起飞的航班，您需在3月7日23:59之前完成兑换 。主题卡兑换舱位为X舱，数量有限，先到先得，兑完为止，具体以查询页面显示为准。</p> <div class=\"tit\">机票退改：</div><p class=\"info\">使用主题卡出行的旅客，不得自愿改期及签转，如需办理退票，须至少在航班计划起飞前三天（含）提交退票申请，仅退还未使用航段税费。</p><div class=\"tit\">弃程限额：</div><p class=\"info\">如发生四次兑换机票后未乘坐或未在规定时限内未办理退票则计入4次弃程限额（即座位虚耗，未实际乘机或未在规定时限内退票）；如遇航班延误或取消时，可按不正常航班退改规定免费退改（不支持签转），不计入弃程限额。</p></div>}")
    private String themeTips;

    /**
     * 主题卡温馨提示
     */
    @ApolloJsonValue("${themeCardIssueList:[]}")
    private List<ThemeCardIssue> themeCardIssueList;

    /**
     * 留学生申请材料
    */
 @Value("${material:<h3><div class=\"tips\"><div class=\"tit\">产品详情</div><p class=\"info\"></h3><br/>" +
                      "<div class=\"tips\"><div class=\"tit\">1、产品定价：</div><p class=\"info\">（1）国际段E舱留学生专享舱位价¥15000；</p > <p class=\"info\">（2）国内段B舱中转舱位¥500；</p >" +
                      "<div class=\"tips\"><div class=\"tit\">2、适用航班：吉祥航空实际承运的上海/南京-大阪国际航班，或包含上海/南京-大阪国际航段的吉祥实际承运的国内联程航段，例如：</div><p class=\"info\">HO1333（PVG-KIX）E舱；</p > <p class=\"info\">HO1112（SZX-PVG）B舱+ HO1333（PVG-KIX）E舱；</p >"+
                      "<div class=\"tips\"><div class=\"tit\">3、中转地中转时间：上海/南京中转时间24小时以内；</div><p class=\"info\">"+
                      "<div class=\"tips\"><div class=\"tit\">4、适用人群：日本大学/专门学校/语言学校入学的中国国籍留学生；</div><p class=\"info\">"+
                      "<div class=\"tips\"><div class=\"tit\">5、销售日期：2022年4月15日-2022年5月31日</div><p class=\"info\">"+
                      "<div class=\"tips\"><div class=\"tit\">6、销售日期：2022年4月15日-2022年5月31日</div><p class=\"info\">"+
                      "<div class=\"tips\"><div class=\"tit\">7、行李额：免费2件（每件23KG）托运行李+1件23KG内的免费行李额；</div><p class=\"info\"><br/>"+
         "<h3><div class=\"tips\"><div class=\"tit\">退改规则</div><p class=\"info\"></h3>"+
         "<div class=\"tips\"><div class=\"tit\">1、留学生运价产品在起飞前72小时可免费变更至5月31日（含）前有空余座位的其他日期同航线航班，72小时以内改期按票面20%收取改期费，航班起飞后改期费按票面的50%收取改期费；</div><p class=\"info\">"+
         "<div class=\"tips\"><div class=\"tit\">2、可变更的舱位为原舱位；</div><p class=\"info\">"+
         "<div class=\"tips\"><div class=\"tit\">3、退票：在航班起飞前退票手续费为原价50%，航班起飞后退票手续费为原价80%：</div><p class=\"info\"><br/>"+
         "<h3><div class=\"tips\"><div class=\"tit\">购买须知</div><p class=\"info\"></h3>"+
         "<div class=\"tips\"><div class=\"tit\">1、申请预订流程：</div><p class=\"info\">（1）留学生填写并提交预订申请后，吉祥航空将联系旅客告知航班信息与订座编码；</p > <p class=\"info\">（2）留学生需向学校等接受机构提交航班申请信息。学校将收集留学生信息并提交至外国留学生支援中心；</p > <p class=\"info\">（3）外国留学生支援中心将审核通过的留学生信息给予吉祥航空。吉祥航空客服中心将根据审核结果与航班可利用座位情况和旅客联系确认。在确认无误且成功支付后将完成出票等服务。</p > <p class=\"info\">（4）留学生需将已预约信息通报至学校等接收机构。</p >"+
         "<div class=\"tips\"><div class=\"tit\">2、该产品不在机场现场销售。"+
         "<div class=\"tips\"><div class=\"tit\">3、购买该产品的旅客需随身携带学生身份证明，包括但不限于有效的学生签证、学生证、入学通知书、学生居留证明等。"+
         "</div>}")
 private String material;
    /**
     * 主题卡舱位
     */
    @Value("${themeCabin:X}")
    private String themeCabin;

    /**
     * 主题卡购买天数限制
     */
    @Value("${themeBuyDayLimit:3}")
    private int themeBuyDayLimit;

    /**
     * 积分活动规则
     */
    @ApolloJsonValue("${crmRuleList:[]}")
    private List<ScoreCrmRule> crmRuleList;


    /**
     * 随身行李
     */
    @ApolloJsonValue("${handLuggageList:[{\"handLuggageDesc\":\"10kg\",\"tripType\":\"D\",\"cabinClass\":\"Y\"},{\"handLuggageDesc\":\"10kg\",\"tripType\":\"D\",\"cabinClass\":\"J\"},{\"handLuggageDesc\":\"10kg\",\"tripType\":\"I\",\"cabinClass\":\"Y\"},{\"handLuggageDesc\":\"10kg\",\"tripType\":\"I\",\"cabinClass\":\"J\"}]}")
    private List<HandLuggage> handLuggageList;

    /**
     * 会员积分发放公钥
     */
    @Value("${crmPublicKey:BIe9eKJotB6ZYgdobQJf7hybvcST3hfJnRQ/m90REI2EhKhoh5YwRXNY3O+bfJmZ18rrmlKX4euJnw+YqeseGeI=}")
    private String crmPublicKey;

    /**
     * 会员积分发放签名salt
     */
    @Value("${crmSalt:juneyaoair}")
    private String crmSalt;

    /**
     * 主题卡类型
     */
    @ApolloJsonValue("${themeCouponList:[ThemeHotPotCoupon,ThemeOutHikeCoupon,ThemeVermicelliCoupon]}")
    private List<String> themeCouponList;

    /**
     * 主题卡名称对应
     */
    @Value("${themeCabinLabel:{\"ThemeHotPotCoupon\": {\"themeCode\": \"ThemeHotPotCoupon\",\"themeName\": \"吉祥火锅卡\",\"remark\": \"火锅卡兑换专享舱位\",\"flightStartTime\":\"2022-03-02\",\"flightEndTime\":\"2022-04-15\"},\"ThemeOutHikeCoupon\": { \"themeCode\": \"ThemeOutHikeCoupon\",\"themeName\": \"吉祥踏青卡\",\"remark\": \"踏青卡兑换专享舱位\",\"flightStartTime\":\"2022-2-28\",\"flightEndTime\":\"2022-03-15\"}, \"ThemeVermicelliCoupon\": {\"themeCode\": \"ThemeVermicelliCoupon\",\"themeName\": \"吉祥嗦粉卡\",\"remark\": \"嗦粉卡兑换专享舱位\",\"flightStartTime\":\"2022-2-28\",\"flightEndTime\":\"2022-03-15\" }}}")
    private String themeCabinLabel;



    /**
     * 直播活动类型
     */
    @ApolloJsonValue("${queryActivityList:[EconSingleCoupon,BusiSingleCoupon]}")
    private List<String> queryActivityList;

    /**
     * 银联卡支付页面新增服务协议配置
     */
    @Value("${payGatewayPageUrl:{\"22409\":{\"gatewayName\":\"农业银行\",\"jumpUrl\":\"https://www.spdbccc.com.cn/zh/wap/miniSite/1991/8832c51/hzf1.html?titlestyle=4&title=浦发银行吉祥航空联名卡\"}}}")
    private String  payGatewayPageUrl;


    /**
     * 会员星级查询是否开启:开启Y,关闭N 默认关闭
     */
    @Value("${memberStarQueryOpen:N}")
    private String memberStarQueryOpen;

    /**
     * 是否开启个人消息:开启Y,关闭N 默认开启
     */
    @Value("${queryPush:Y}")
    private String queryPush;
    /**
     * 是否开启个人未读消息统计:开启Y,关闭N 默认开启
     */
    @Value("${queryCountPush:Y}")
    private String queryCountPush;

    /**
     * 国际票是否只展示本人及儿童婴儿开关
     */
    @Value("${personInternalSelfFlag:Y}")
    private String personInternalSelfFlag;

    /**
     * 一单多券开关
     */
    @Value("${openMultiCouponFlag:Y}")
    private String openMultiCouponFlag;

    /**
     * 家庭账户版本
     */
    @Value("${isFamilyAccount:Y}")
    private String isFamilyAccount;

    @Value("${isFamilyAccountVer:79100}")
    private int isFamilyAccountVer;

    /**
     * 国际客票改期开关
     */
    @Value("${changeTicketIFlag:Y}")
    private String changeTicketIFlag;

    /**
     * 国际票是否实名开关开关
     */
    @Value("${personInternalRealNameFlag:Y}")
    private String personInternalRealNameFlag;

    @Value("${personDomesticRealNameFlag:Y}")
    private String personDomesticRealNameFlag;


    @Value("${busFareLabelUrl:https://mediaws.juneyaoair.com/upload/clause/%E2%80%9C%E5%90%89%E7%A5%A5%E6%B7%B1%E6%B8%AF%E9%80%9A%E2%80%9D%E4%BA%A7%E5%93%81%E4%B8%9A%E5%8A%A1%E8%A7%84%E5%88%99.pdf}")
    private String busFareLabelUrl;

    /**
     * 可使用证件类型
     */
    @ApolloJsonValue("${useCertTypeMap:{\"CN\":[{\"CertDesc\":\"身份证\",\"CertType\":\"NI\"},{\"CertDesc\":\"护照\",\"CertType\":\"PP\"},{\"CertDesc\":\"港澳居民居住证\",\"CertType\":\"HMT\"},{\"CertDesc\":\"台湾居民来往大陆通行证\",\"CertType\":\"MTP\"},{\"CertDesc\":\"港澳居民来往内地通行证\",\"CertType\":\"ORI\"},{\"CertDesc\":\"外国人永久居留身份证\",\"CertType\":\"NIPP\"},{\"CertDesc\":\"其它证件\",\"CertType\":\"CC\"}],\"DEFAULT\":[{\"CertDesc\":\"护照\",\"CertType\":\"PP\"}],\"HK\":[{\"CertDesc\":\"护照\",\"CertType\":\"PP\"},{\"CertDesc\":\"港澳通行证\",\"CertType\":\"HTPP\"},{\"CertDesc\":\"港澳居民来往内地通行证\",\"CertType\":\"ORI\"}],\"HK_MO\":[{\"CertDesc\":\"护照\",\"CertType\":\"PP\"},{\"CertDesc\":\"港澳通行证\",\"CertType\":\"HTPP\"},{\"CertDesc\":\"港澳居民来往内地通行证\",\"CertType\":\"ORI\"}],\"MO\":[{\"CertDesc\":\"护照\",\"CertType\":\"PP\"},{\"CertDesc\":\"港澳通行证\",\"CertType\":\"HTPP\"},{\"CertDesc\":\"港澳居民来往内地通行证\",\"CertType\":\"ORI\"}],\"TW\":[{\"CertDesc\":\"护照\",\"CertType\":\"PP\"},{\"CertDesc\":\"台湾居民来往大陆通行证\",\"CertType\":\"MTP\"},{\"CertDesc\":\"台湾通行证\",\"CertType\":\"TPP\"}]}}")
    private Map<String, List<CertType>> useCertTypeMap;

    /**
     * 常用乘机人 航线提醒消息
     */
    @ApolloJsonValue("${commonPersonWarmRemindMap:{\"I\":\"国际客票仅限实名注册会员本人携带最多2位儿童旅客预订\"}}")
    private Map<String, String> commonPersonWarmRemindMap;


    /**
     * 航班查询类型
     */
    @ApolloJsonValue("${flightQueryTypeMap:{\"BUSINESS_FLY_FREE_TICKET\":{\"cabinCode\":\"\",\"days\":\"100\"}}}")
    private Map<String, FlightQueryTypeDto> flightQueryTypeMap;


    /**
     * 常用乘机人 航线提醒消息
     */
    @ApolloJsonValue("${districtTipsTextMap:{\"HKG\":\"中国大陆籍乘客往来港澳请使用港澳通行证，若使用护照出行需同时持有7天内前往第三国或者地区的机票。\",\"MFM\":\"中国大陆籍乘客往来港澳请使用港澳通行证，若使用护照出行需同时持有7天内前往第三国或者地区的机票。\",\"TPE\":\"此行程建议使用台湾通行证，使用此证件出行，请先确认是否能正常登机。\"}}")
    private Map<String, String> districtTipsTextMap;


    /**
     * 设置仓位退改费高低
     */
    @ApolloJsonValue("${returnFeeDescMap:{\"J\":\"退改费用低\",\"C\":\"退改费用低\",\"D\":\"退改费用低\",\"A\":\"退改费用低\",\"R\":\"退改费用低\",\"B\":\"退改费用低\",\"M\":\"退改费用低\",\"U\":\"退改费用低\",\"H\":\"退改费用高\",\"Q\":\"退改费用高\",\"V\":\"退改费用高\",\"W\":\"退改费用高\",\"S\":\"退改费用高\",\"T\":\"退改费用高\",\"Z\":\"退改费用高\",\"E\":\"退改费用高\",\"K\":\"退改费用高\",\"L\":\"退改费用高\",\"Y\":\"退改费用低\",\"J/Y\":\"退改费用低\"}}")
    private Map<String, String> returnFeeDescMap;



    /** 在线客服地址 */
   @Value("${onlineCustomerService:https://ocs.juneyaoair.com/out/ClientWeb/index.html?}")
   private String onlineCustomerService;
    /**
     * 新产品上线之前，只返回指定的资源ID
     */
    @ApolloJsonValue("${oldProductList:[aed8180e7e564601a411d6e9d60ddb08]}")
    private List<String> oldProductList;



    /** M站查询按钮icon */
    @ApolloJsonValue("${mButtonIcon:[{\"channelCode\":\"MOBILE\",\"iconUrl\":\"\"},{\"channelCode\":\"WXAPP\",\"iconUrl\":\"\"},{\"channelCode\":\"MWEB\",\"iconUrl\":\"\"}]}")
    private  List<ImageResponse> mButtonIcon;


    /** M站查询首页更多链接 */
    @ApolloJsonValue("${locationAddresses:[{\"picLocation\":\"POSITION_PREFERENTIAL_RIGHTS\",\"url\":\"\"},{\"picLocation\":\"ACTIVITY_ZONE\",\"url\":\"http://***********:80/upload/noticeInfos/c1611cc5-81d5-4875-a3cb-d3a91507cce1.png\"},{\"picLocation\":\"POSITION_MORE_PRODUCT\",\"url\":\"\"}]}")
    private  List<LocationAddress> locationAddresses;

    /**
     * 获取指定的可使用证件类型
     *
     * @param segmentType
     * @return
     */
    public List<CertType> getUseCertTypeList(String segmentType) {
        if (StringUtils.isBlank(segmentType)) {
            segmentType = "CN";
        }
        List<CertType> certTypes = useCertTypeMap.get(segmentType);
        if (CollectionUtils.isNotEmpty(certTypes)) {
            return certTypes;
        }
        certTypes = useCertTypeMap.get("DEFAULT");
        return CollectionUtils.isEmpty(certTypes) ? new ArrayList<>() : certTypes;
    }

    /**
     * 获取常用乘机人 航线提醒消息
     *
     * @param segmentType
     * @return
     */
    public String getCommonPersonWarmRemind(String segmentType) {
        if (null == commonPersonWarmRemindMap || commonPersonWarmRemindMap.isEmpty()) {
            return null;
        }
        return commonPersonWarmRemindMap.get(segmentType);
    }
    /**
     * 行李购买切换新产品时间点
     *
     */
    @Value("${newBaggageProductEnableTime:\"2022-06-10 00:00:00\"}")
    private String newBaggageProductEnableTime;
    /**
     * 额外行李销售定价及限额
     * default:
     * //    [
     * //    {
     * //        "scopeMessage": "0-999",
     * //            "scopeMin": 0,
     * //            "scopeMax": 999,
     * //            "onlinePrice": 11,
     * //            "offlinePrice": 12,
     * //            "perOrderWeightLimit": 30
     * //    },
     * //    {
     * //        "scopeMessage": "1000-1999",
     * //            "scopeMin": 1000,
     * //            "scopeMax": 1999,
     * //            "onlinePrice": 14,
     * //            "offlinePrice": 15,
     * //            "perOrderWeightLimit": 30
     * //    },
     * //    {
     * //        "scopeMessage": "2000及以上",
     * //            "scopeMin": 2000,
     * //            "scopeMax": 999999,
     * //            "onlinePrice": 18,
     * //            "offlinePrice": 20,
     * //            "perOrderWeightLimit": 30
     * //    }
     * //    ]
     *
     * @source: 吉祥航发〔2022〕124号 关于发布《吉祥航空行李产品规定》的通知
     */
    @ApolloJsonValue("${extraBaggagePricing:[{\"scopeMessage\":\"0-999\",\"scopeMin\":0,\"scopeMax\":999,\"onlinePrice\":11,\"offlinePrice\":12,\"perOrderWeightLimit\":30},{\"scopeMessage\":\"1000-1999\",\"scopeMin\":1000,\"scopeMax\":1999,\"onlinePrice\":14,\"offlinePrice\":15,\"perOrderWeightLimit\":30},{\"scopeMessage\":\"2000及以上\",\"scopeMin\":2000,\"scopeMax\":999999,\"onlinePrice\":18,\"offlinePrice\":20,\"perOrderWeightLimit\":30}]}")
    private List<Pricing> extraBaggagePricing;


    /**
     * 获取积分新旧接口开关
     */
    @Value("${integralSwitch:N}")
    private String integralSwitch;

    /**
     * 企业链接微信配置
     */
    @Value("${serviceLink:https://work.weixin.qq.com/kfid/kfcd542aca27a6f508f}")
    private String serviceLink;

    /**
     * 权益券文案描述
     */
    @ApolloJsonValue("${\"rightsDescriptionMap\":{\"8877eae32ae44be8b8926be827fedc85\":{\"rightsName\":\"积分兑换\",\"rightsContent\":\"会员可使用会员账户中的积分为本人及已生效的受益人名单中的人购买机票,也可用于其他途径(如积分商城兑换礼品、部分权益券等)。兑换吉祥航空奖励机票,可选择全额积分,或积分加现金方式支付。\"},\"7973c36599e242ec8a76dbee4f1f6c46\":{\"rightsName\":\"积分累积\",\"rightsContent\":\"会员通过乘坐吉祥航空实际承运的航班并且选择指定舱位,根据积分累积规则获取的积分。您也可以选择吉祥航空合作伙伴提供的产品和服务累积积分。如意俱乐部积分累积以点为计算单位,进行结算时每1点积分可抵扣1元人民币的现金价值,或每8点积分抵扣1美金。\"},\"03b95b03c5cb466591887e26e594f6f2\":{\"rightsName\":\"额外免费行李额10公斤\",\"rightsContent\":\"搭乘由吉祥航空实际承运且挂HO代号航班时,在原购客票免费行李托运标准规定之外,可享额外免费行李额,行李标准如下:计重制行李运输航线:在购票舱位的免费行李额基础上将再获增10公斤免费行李额。每件行李体积不超过100厘米额外免费行李x60厘米x40厘米,单件行李不超过50KG。额10公斤计件制行李运输航线:在购票舱位的免费行李额基础上将增加1件免费行李。每件行李重量限额为23KG,每件行李三边之和不得超过158厘米。国内航线使用计重制行李额标准,国际(地区)航线使用计件制行李额标准。\"},\"5f6630ffabc34f3ba5aee7fef81e6e49\":{\"rightsName\":\"投诉优先受理\",\"rightsContent\":\"在旅途中若遇到任何让您不满的事情,我们将为您提供优先于普通会员的投诉受理服务。\"},\"46e834ab341b4d9fb611c41d6fa51275\":{\"rightsName\":\"贵宾会员专线\",\"rightsContent\":\"享贵宾会员专线服务: 4001195520。\"},\"9be7dbf4fe7c4ea683f1756afa5e620b\":{\"rightsName\":\"优先登机\",\"rightsContent\":\"在条件许可的航站并且航班靠桥时,您将优先于普通会员获得优先登机服务。\"},\"50ec28907823441ebe97a7056f1afbfc\":{\"rightsName\":\"优先办理值机\",\"rightsContent\":\"当选择搭乘吉祥航空航班时,无论您购买的是何种舱位等级的机票,均可在公务舱柜台或贵宾会员专设柜台办理乘机手续。\"},\"58fb025a4b8a4cf4b2c5c46966ff7d8e\":{\"rightsName\":\"优先办理值机\",\"rightsContent\":\"当选择搭乘吉祥航空航班时,无论您购买的是何种舱位等级的机票,均可在公务舱柜台或贵宾会员专设柜台办理乘机手续,同时还可携一名搭乘当日吉祥航空航班或由吉祥航空实际承运航班的同行旅客一同办理手续。\"},\"ec90d7151dc642089fd27dc461f292a0\":{\"rightsName\":\"优先登机\",\"rightsContent\":\"在条件许可的航站并且航班靠桥时,您将优先于银卡会员及普通会员获得优先登机服务。\"},\"9bd675b8983b4c968dcc6bebede8bc4b\":{\"rightsName\":\"优先登机\",\"rightsContent\":\"在条件许可的航站,您将优先于银卡会员及普通会员获得优先登机服务。\"},\"a6b98c8221d94fdf9e39de2e3629c53d\":{\"rightsName\":\"优先办理值机\",\"rightsContent\":\"当选择搭乘吉祥航空航班时,无论您购买的是何种舱位等级的机票,均可在公务舱柜台或贵宾会员专设柜台办理乘机手续。\"},\"aa7e154f03074497a647ba8d01b0d9d0\":{\"rightsName\":\"优先托运行李\",\"rightsContent\":\"我们将尽量保证您的行李在交运时,拴挂优先标识,以便您下机后在最短的时间内提取行李。\"},\"18e8236c084f49d5b584cc8c02e87fe0\":{\"rightsName\":\"优先托运行李\",\"rightsContent\":\"我们将尽量保证您的行李在交运时,拴挂优先标识,以便您下机后在最短的时间内提取行李。\"},\"60c0768f35a345719f5da8ad4c3dd5a6\":{\"rightsName\":\"遇不正常航班（享公务舱旅客标准）\",\"rightsContent\":\"当您遇到航班不正常时,将享受等同于公务舱旅客的地面食宿服务标准。\"},\"817b25a3eb3c4db7b13a255c2d6885c4\":{\"rightsName\":\"专享VIP车\",\"rightsContent\":\"航班停靠远机位时,您可享内场VIP车或摆渡车服务;本权益受机场条件或服务能力限制,部分站点可能无法提供。\"},\"4e6e368d229f4d06a2ce27558f037dd5\":{\"rightsName\":\"额外积分奖励20%\",\"rightsContent\":\"每次乘坐吉祥航班指定舱位后,除累积飞行积分外,您还可以按实际购票舱位获得20%的额外积分奖励。该积分作为非定级积分,与飞行积分同时进入您的账户。\"},\"788d2e438ed74113b47f24555fdcb31e\":{\"rightsName\":\"额外免费行李额20公斤\",\"rightsContent\":\"搭乘由吉祥航空实际承运且挂HO代号航班时,在原购客票免费行李托运标准规定之外,可享额外免费行李额,行李标准如下:计重制行李运输航线:在购票舱位的免费行李额基础上将再获增10公斤免费行李额。每件行李体积不超过100厘米额外免费行李x60厘米x40厘米,单件行李不超过50KG。额20公斤计件制行李运输航线:在购票舱位的免费行李额基础上将增加1件免费行李。每件行李重量限额为23KG,每件行李三边之和不得超过158厘米。国内航线使用计重制行李额标准,国际(地区)航线使用计件制行李额标准。\"},\"573b0cd38ab946cb94ddeba6de5899d2\":{\"rightsName\":\"额外免费行李额30公斤\",\"rightsContent\":\"搭乘由吉祥航空实际承运且挂HO代号航班时,在原购客票免费行李托运标准规定之外,可享额外免费行李额,行李标准如下:计重制行李运输航线:在购票舱位的免费行李额基础上将再获增10公斤免费行李额。每件行李体积不超过100厘米额外免费行李x60厘米x40厘米,单件行李不超过50KG。额30公斤计件制行李运输航线:在购票舱位的免费行李额基础上将增加1件免费行李。每件行李重量限额为23KG,每件行李三边之和不得超过158厘米。国内航线使用计重制行李额标准,国际(地区)航线使用计件制行李额标准。\"},\"ea5ff05dfe5a421aa5814baadab4a89f\":{\"rightsName\":\"公务舱休息室\",\"rightsContent\":\"当选择搭乘吉祥航空航班时,无论您购买的是何种舱位等级的机票,均可在公务舱休息室候机。\"},\"424d8f7355a048dfbd685e2350188265\":{\"rightsName\":\"公务舱休息室\",\"rightsContent\":\"当选择搭乘吉祥航空航班时,无论您购买的是何种舱位等级的机票,均可在公务舱休息室候机。\"},\"97697460dd314dfeaa91e9a35ce3bb66\":{\"rightsName\":\"额外积分奖励10%\",\"rightsContent\":\"每次乘坐吉祥航班指定舱位后,除累积飞行积分外,您还可以按实际购票舱位获得10%的额外积分奖励。该积分作为非定级积分,与飞行积分同时进入您的账户。\"},\"39522868b6c6435db10b64d83349bc8d\":{\"rightsName\":\"额外积分奖励15%\",\"rightsContent\":\"每次乘坐吉祥航班指定舱位后,除累积飞行积分外,您还可以按实际购票舱位获得15%的额外积分奖励。该积分作为非定级积分,与飞行积分同时进入您的账户。\"}}}")
    private Map<String, rightsDescriptionResponse> rightsDescriptionMap;
    /** 选座值机结果分享背景图片URL */
    @Value("${juneyaoair.config.seatShareImgUrl:}")
    private String seatShareImgUrl;

    /**
     * 是否保存设备信息 默认保存 Y
     */
    @Value("${saveDeviceInfoFlag:Y}")
    private String saveDeviceInfoFlag;

    @Value("${verticalReduction:华瑞银行先飞后付}")
    private String verticalReduction;

    @Value("${PAFDesc:先飞后付立减}")
    private String PAFDesc;

    /**
     * <AUTHOR>
     * @Description 建行联名卡支付名称
     * @Date 11:22 2024/9/3
     **/
    @Value("${CCBPay:建设银行白金卡MOBILE(建设银行白金卡MOBILE)}")
    private String CCBPay;

    /**
     * <AUTHOR>
     * @Description 建行联名卡支付描述
     * @Date 11:22 2024/9/3
     **/
    @Value("${CCBPayDesc:吉祥龙卡优惠}")
    private String CCBPayDesc;

    /**
     * 统一订单网关地址
     */
    @Value("${oneOrderUrl:https://horder.hoair.cn}")
    private String oneOrderUrl;

    /**
     * 使用openAPI的地址
     */
    @ApolloJsonValue("${useOpenApiAddress:[\"/Reserve/TicketBookV20\",\"/Reserve/TicketBookMultiple\"]}")
    private List<String> useOpenApiAddress;

    /**
     * 短信平台地址
     */
    @Value("${smsUrl:http://p-dubbo-srv.juneyaoair.com:8080/dubboconsumer/servlet/SmsServlet}")
    private String smsUrl;

    @ApolloJsonValue("${insuranceConfigList:{}}")
    private List<InsuranceConfig> insuranceConfigList;

    @Value("${rewriteFlag:false}")
    private String rewriteFlag;

    /**
     * 黑名单注册渠道
     */
    @Value("${blackRegisterChannel:}")
    private String blackRegisterChannel;
    /**
     * 自有实名渠道
     */
    @Value("${ownRealChannel:Face,Photo,Tencent,Unionpay,ZhiFuBao,AliFace}")
    private String ownRealChannel;
    /**
     * 实名与操作的耗时时间 单位S
     */
    @Value("${useTime:180}")
    private int useTime;
    /**
     * Photo实名与操作的耗时时间 单位S
     */
    @Value("${photoUseTime:604800}")
    private int photoUseTime;

    /**
     * 头部文案
     */
    @ApolloJsonValue("${headTitleInfo:{}}")
    private HeadTitleInfo headTitleInfo;

    /** 是否使用消息平台发送短信 */
    @Value("${com.juneyaoair.config.messageFlag:true}")
    public boolean messageFlag;
    @Value("${com.juneyaoair.messagePlatform.push}")
    private String messagePlatformPush;
    @Value("${com.juneyaoair.messagePlatform.channelCode:}")
    private String messagePlatformChannelCode;
    @Value("${com.juneyaoair.messagePlatform.channelSecret:}")
    private String messagePlatformChannelSecret;
    @Value("${com.juneyaoair.messagePlatform.smsAisleNumber:}")
    private String messagePlatformSmsAisleNumber;
    @Value("${com.juneyaoair.messagePlatform.wechatPublicAisleNumber:}")
    private String messagePlatformWechatPublicAisleNumber;

    /**
     * 数据平台dsop地址
     */
    @Value("${dsopUrl:https://tdsop.juneyaoair.com}")
    private String dsopUrl;

    /**
     * 数据平台APPID
     */
    @Value("${dataAppId:20211013897911765336064000}")
    private String dataAppId;

    /**
     * 数平台开发者私钥
     */
    @Value("${privateKeyIsv:MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQDc9zf9vpPzSEXOuKVHEtjhK/mPEXxvv2HGJbud8Wj2EEGc6K7uvPQGMqF7QlLNZPhkXH9wJ8SqoOYfdCFWiOuUq3Sl/yRdkVYO/iWYMn4itjWOrYDqOiP1M5h6ZHva1+4HY5PPJ+z8eUjyqXmJAD4ZKh/2rqYLl7G64yO5TeQGJJkgbHGvS4IRjlky92rEH43b1XP/qfmPOdDlex8p6lRAgSWThOdTKLBwBwKGPpGkGWw8BVWO+zQJjb/N3auJSYEdk2h2FYUvsGzv85uap2FudXXBEOh1lcDVIcLWNg2/dFPPnrAw6Y4ORCyN9Q2iA7y+JHez6MMimxkhp9vpr/kVAgMBAAECggEAUkVVuotHqNAi9an4F7NjvsrRuzwGedDnBZCs/0yQCPXaiCJ2Z3tA3EHjr/HsxH6woSVzaX3VErafl9x0QTTvicYgw2Fw18myIhjsRHOgVSpEXKArHIqTMsuIHSHmn3/AJ04jDYFPEl3SFE+XabQGavYtOnqiZjSPvwatGWRv3vz19HcRDZXnHkk6vTCBUuTsVuJeJDY/CDEBtl7bJEXwjiXVxhuUXIIly8wNi/xfgMC3Gaypwz1lMOihcGqFcb7u/YOvWTej3UK5vi5a4+P6Q7jHjCLeVxUzvLXFWQDdwSuRq4GG65XgyWoURurndGMnz/pPG1Zb877VttlSJcNPAQKBgQD9UoS40Nv+TT7hB2Dwxn9FluAnfCYhat5C2hUn04hh/rjIjfj+RvsJiO6MgrDSiPWZxqoosdlFwRGNOjNP+hP84I8BuGryxTsVUpFKAtP2Ctt8MhEoddWPoIan+XNLyZ8cfOhX3uBQWVDRW0ioOHt8Y9K2hkAgU0sWPYh8NrHRVQKBgQDfTSTxwbtOuHDnuHwAXCv49OQfzczkp1TfdCCyXaRLqd0NnPGeiXNSbUAVUzHOM6fuVyS7Hgp/wNrDXiY9wtf3vmEVZE0HrdE5xI7NKGtamyRv6aPfPisyE8M2fQ5ke1ULb9hCVwApf1Sir6puK2KrT5s8PEIBnoenGEu4jLyIwQKBgQCAE+uUwcyou4dyINfrhICuv4Sfkg55UN4FMNwHYdUPrsjgZZ+heGPOQ1RQ6HbcS3DZie9YWes/cEkefXGaGxdmAGdteD9idUPVGQTqRZnDSWQFXA7+xOzwDDpozR91DwCKV8DlRINnPHE3GI8Ytk4A6zuLGz9q2JIbMYtDOigXbQKBgQDKqeFV6VZuY8dVB4LDcOQX12sT0lybSaMmCueB+qsaYNhXepFwKZ+Yt77pm9AgLZHEJyMXEHTG7emeVtyuQSBxoT/LgnTG+2Jg45wXZw/H8/Zl+87W1j2t/XG3Clj9qVHOR3wm+X8UKpRN8tDjnEy43k8tpCLkBVVougw/OrwCAQKBgF1Ba04v8ThqQLdlvEPXDQbhwUEuueNNNrxpl3EM4KNU15eghd07/dXI9ZjPwvB9XuP/m1PP9jX9xQ4N9zbrtSFixepQXTFS8rZcIN9CXRO/696cmpSNz1y9tYsZcSqSDZpZ1wF6QcHFlmFw2yi6IiKKfqDAWfPmqZXMh8ekhQvk}")
    private String privateKeyIsv;
    /**
     * 忽略的航线组合
     */
    @Value("${ignoreAirline:PVGPKXPKXNGO}")
    private String ignoreAirline;
    /**
     * 按照航线属性关闭同盾 D-表示国内不使用同盾
     */
    @Value("${switchTongdunDorI:D}")
    private String switchTongdunDorI;

    /**
     *  芬兰航线出境同意书文案
     */
    @Value("${aYLeaveCountryText:}")
    private String  aYLeaveCountryText;

    /**
     *  售罄候补服务文案
     */
    @Value("${flightWaitingText:}")
    private String  flightWaitingText;

    /**
     * 学生认证的年龄限制
     */
    @Value("${ageLimit:24}")
    private Integer ageLimit;

    /**
     * 会员零星  星级规则id
     */
    @Value("${memberStarRuleId:303}")
    private String memberStarRuleId;

    /**
     * 运价日历查询  查询天数
     */
    @Value("${queryDays:30}")
    private int queryDays;

    /**
     * 支付宝新用户立减参数
     */
    @Value("${aliPayArgument:merchantpromo_tag}")
    private String aliPayArgument;

    /**
     * 机票订单支付时限控制 单位 ms
     */
    @Value("${orderRemainTime:1200000}")
    private long orderRemainTime;

    /**
     * 查询舱位产品排序
     */
    @Value("${cabinSortJsonStr:{\"MEMBER_FARE\":3,\"YOUTH_FARE\":2,\"HIGH_REBATE\":1}}")
    private String cabinSortJsonStr;


    /**
     * 是否启用dsop平台接口
     */
    @Value("${useDsop:N}")
    private String useDsop;

    /**
     * 会员发送短信每自然日次数 （ 值需>0  0:代表不限制 ）
     */
    @ApolloJsonValue("${sendCheckVerifyCodeByFfp:{\"externalExchangeCoupon\": 5}}")
    private Map<String, Integer> sendCheckVerifyCodeByFfp;
    /**
     * 证件照上传限制
     */
    @ApolloJsonValue("${uploadCheckVerifyCodeByFfp:{\"default\": 20}}")
    private Map<String, Integer> uploadCheckVerifyCodeByFfp;

    /** 校验许可访问次数 */
    @ApolloJsonValue("${checkAccessCount:{}}")
    private Map<String, Integer> checkAccessCount;

    /**
     * hoaesKey HO AES的密钥KEY 16/24/32位
     */
    @Value("${hoAesKey:cEKD02sia6YWm3vIRbkj9t1UpLMr5ZHB}")
    private String hoAesKey;
    /**
     * hoSm4Key HO SM4的密钥KEY 16位 此密码为与前端的公布密钥，适用于前端加密，后端解密或后端加密，前端解密
     */
    @Value("${hoSm4Key:8sjZW5QeTJtCfcrW}")
    private String hoSm4Key;
    /**
     * hoSm4KeyPrivate HO SM4的密钥KEY 16位 此密码为系统独享，不对外公布；返回给前端加密报文，后端自行解密
     */
    @Value("${hoSm4KeyPrivate:Uq9JEbRyXPRQU6wq}")
    private String hoSm4KeyPrivate;


    @ApolloJsonValue("${ThemeFlightDetailsMap:{\"genshin\":{\"themeName\":\"《原神》主题飞机执飞\",\"themeText\":\"点击了解《原神》主题飞机\",\"themeImageUrl\":\"https://mediaws.juneyaoair.com/upload/icon/av/ys.png\",\"arrowImageUrl\":\"https://mediaws.juneyaoair.com/upload/icon/av/arrow.png\",\"themeTextColour\":\"#479EDE\",\"adjustUrl\":\"https://t-activity.hoair.cn/2023events/ysGame/index.html?title=%E5%8E%9F%E7%A5%9E%E4%B8%89%E5%91%A8%E5%B9%B4%E4%B8%BB%E9%A2%98%E8%88%AA%E7%8F%AD&titlestyle=4\"}}}")
    private Map<String, ThemeFlightDetails> themeFlightDetailsMap;

    @ApolloJsonValue("${downloadUrlList:[\"https://mediaws.juneyaoair.com\"]}")
    private List<String> downloadUrlList;

    /**
     * 是否启用设备检查
     */
    @Value("${useCheckDevice:N}")
    private String useCheckDevice;

    /** 令牌配置 */
    @ApolloJsonValue("${juneyaoair.config.limiterInfoMap}")
    private Map<String, RateLimiterInfo> limiterInfoMap;
    public void setLimiterInfoMap(Map<String, RateLimiterInfo> limiterInfoMap) {
        this.limiterInfoMap = limiterInfoMap;
        this.rateLimiterMap = new ConcurrentHashMap<>();
    }

    //minio相关配置
    @Value("${ossMinioEndpoint:https://minio.juneyaoair.com}")
    private String endpoint;
    @Value("${ossMinioAccessKey:minioadmin}")
    private String accessKey;
    @Value("${ossMinioSecretKey:minioadmin}")
    private String secretKey;
    @Value("${ossMiniBucket:app}")
    private String bucket;
    /**
     * 使用minio的上传证件照方式
     */
    @ApolloJsonValue("${ossUseMinioType:[\"IdPhotoImage\",\"studentAuthImage\"]}")
    private List<String> useMinioType;

    @ApolloJsonValue("${labelRuleInfoList:[{\"ruleName\":\"赠送休息室\",\"ruleUrl\":\"https://mediaws.juneyaoair.com/upload/message/202405/html/mobile_8f313be367084ae595c94169da1caa2e_1715675014847.html?&titlestyle=4&title=洲际航线赠送休息室\",\"startDate\":\"2024-05-22\",\"endDate\":\"2025-03-27\",\"flightStartDate\":\"2024-05-22\",\"flightEndDate\":\"2025-03-27\",\"depCity\":\"\",\"depCountry\":\"CN/JP/KR/SG/TH/ID/MY\",\"depRegion\":\"EU\",\"arrCity\":\"\",\"arrCountry\":\"\",\"arrRegion\":\"EU\",\"transferAirport\":\"PVG\",\"carryCompany\":\"HO\",\"dateLimit\":\"overnight,sameDay\"},{\"ruleName\":\"赠送休息室\",\"ruleUrl\":\"https://mediaws.juneyaoair.com/upload/message/202405/html/mobile_8f313be367084ae595c94169da1caa2e_1715675014847.html?&titlestyle=4&title=洲际航线赠送休息室\",\"startDate\":\"2024-05-22\",\"endDate\":\"2025-03-27\",\"flightStartDate\":\"2024-05-22\",\"flightEndDate\":\"2025-03-27\",\"depCity\":\"\",\"depCountry\":\"\",\"depRegion\":\"EU\",\"arrCity\":\"\",\"arrCountry\":\"CN/JP/KR/SG/TH/ID/MY\",\"arrRegion\":\"EU\",\"transferAirport\":\"PVG\",\"carryCompany\":\"HO\",\"dateLimit\":\"overnight,sameDay\"}]}")
    private List<LabelRuleInfo> labelRuleInfoList;
    /**
     * 关闭销售时间 分钟
     */
    @Value("${closeSellTime:40}")
    private long closeSellTime;


    /**
     * 关闭销售提示时间 分钟
     */
    @Value("${closeSellPromptTime:65}")
    private long closeSellPromptTime;

    /**
     * 下线的url地址
     */
    @ApolloJsonValue("${offUrlList:[\"/sendSms/sendSmsByBatch\"]}")
    private Set<String> offUrlList;
    /**
     * 不进行拦截的白名单url
     */
    @ApolloJsonValue("${whiteUrlList:[\"/flightDynamic/queryNewFlightDynamic\"]}")
    private Set<String> whiteUrlList;
    /**
     * 最低支持的移动客户端版本
     */
    @Value("${minMobileVersion:7.4.0}")
    private String minMobileVersion;

    /**
     * 信用飞切换开关
     */
    @Value("${xinfeiChange:N}")
    private String xinfeiChange;
    @Value("${xinfeiUrl:https://m.xinfei.cn/platform/flying-loan?cnlPdCode=ACS002000079&inner_app=xyf01_fxd&utm_source=QD-CPS-XYF01-BD-JXHK01}")
    private String xinfeiUrl;

    /** 退票申请ADA校验开关 */
    @Value("${refundApplyAdaFlag:Y}")
    private String refundApplyAdaFlag;

    @ApolloJsonValue("${checkLicense.map:{}}")
    private Map<String, Integer> checkLicenseMap;


    /**
     * 由中转系统查询中转城市
     */
    @Value("${useTransferCityFlag:Y}")
    private String useTransferCityFlag;
    /**
     * 老的B2C接口
     */
    @Value("${b2cHandUrl:http://b2cmng.juneyaoair.com:88}")
    private String b2cHandUrl;

    /** 候补运价展示时间 */
    @Value("${waitFareMinute:120}")
    private int waitFareMinute;

    @ApolloJsonValue("${specialFareQuery.map:{}}")
    private Map<String,SpecialFareQuery> specialFareQueryMap;
    /** 主题航班查询开关 */
    @Value("${searchTheme:true}")
    private boolean searchTheme;

    @ApolloJsonValue("${jjscConfig:{}}")
    private JjscConfig jjscConfig;
    /**
     * @description 清理价格日历缓存的响应码
     **/
    @ApolloJsonValue("${clearPriceCode:[\"1001\"]}")
    private List<String> clearPriceCode;
    /**
     * @description 不显示联名卡渠道
     **/
    @ApolloJsonValue("${notShowCreditCardAdChannel:[\"MOBILE\"]}")
    private List<String> notShowCreditCardAdChannel;

    private Map<String, RateLimiter> rateLimiterMap = new ConcurrentHashMap<>();
    public RateLimiter getRateLimiter(String typeCode) {
        RateLimiter rateLimiter = rateLimiterMap.get(typeCode);
        if (null == rateLimiter) {
            /*
                timeoutDuration: 当请求被限流时，线程等待的时间
                limitRefreshPeriod: 令牌刷新的周期（period）
                limitForPeriod: 在每个（period）周期内的令牌数
             */
            RateLimiterInfo rateLimiterInfo = this.getLimiterInfoMap().get(typeCode);
            if (null == rateLimiterInfo) {
                return null;
            }
            // 生成RateLimiterConfig
            RateLimiterConfig config = RateLimiterConfig.custom()
                    .timeoutDuration(Duration.ofMillis(rateLimiterInfo.getTimeoutDuration()))
                    .limitRefreshPeriod(Duration.ofSeconds(rateLimiterInfo.getLimitRefreshPeriod()))
                    .limitForPeriod(rateLimiterInfo.getLimitForPeriod())
                    .build();
            rateLimiter = RateLimiter.of(typeCode, config);
            rateLimiterMap.put(typeCode, rateLimiter);
        }
        return rateLimiter;
    }


    @PostConstruct
    void initialize() {
        logger.info("HandConfig is initialized as {}", toString());
    }

    @Override
    public String toString() {
        return JsonUtil.objectToJson(this);
    }

    public List<LabelInfo> getLabelInfo(String key){
        if(labelInfoMap == null){
            return new ArrayList<>();
        }
        return labelInfoMap.get(key);
    }
}

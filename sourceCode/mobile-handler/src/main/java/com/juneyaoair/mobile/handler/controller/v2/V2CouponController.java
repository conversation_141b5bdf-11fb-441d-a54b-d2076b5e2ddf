package com.juneyaoair.mobile.handler.controller.v2;

import com.google.common.collect.Lists;
import com.google.gson.reflect.TypeToken;
import com.juneyaoair.appenum.ChannelCodeEnum;
import com.juneyaoair.appenum.WSEnum;
import com.juneyaoair.appenum.av.PackageTypeEnum;
import com.juneyaoair.appenum.coupon.CouponStateEnum;
import com.juneyaoair.appenum.member.ContactTypeEnum;
import com.juneyaoair.appenum.member.MemberDetailRequestItemsEnum;
import com.juneyaoair.appenum.order.YouHuiCouponSourceEnum;
import com.juneyaoair.appenum.order.OrderCouponStateEnum;
import com.juneyaoair.appenum.order.UnifiedOrderResultEnum;
import com.juneyaoair.appenum.rightcoupon.ProductCouponTypesEnum;
import com.juneyaoair.appenum.rightcoupon.VoucherTypesEnum;
import com.juneyaoair.baseclass.activity.ActivityConfig;
import com.juneyaoair.baseclass.basicsys.response.AirLineInfoDepCityDto;
import com.juneyaoair.baseclass.basicsys.response.AirPortInfoDto;
import com.juneyaoair.baseclass.basicsys.response.CityInfoDto;
import com.juneyaoair.baseclass.change.ChangePassengerInfo;
import com.juneyaoair.baseclass.common.base.UserInfoMust;
import com.juneyaoair.baseclass.common.request.BaseReq;
import com.juneyaoair.baseclass.common.response.BaseResp;
import com.juneyaoair.baseclass.coupon.request.CouponQuery;
import com.juneyaoair.baseclass.coupon.request.v2.*;
import com.juneyaoair.baseclass.coupon.response.v2.UpCouponPro;
import com.juneyaoair.baseclass.newcoupon.bean.BookProductInfo;
import com.juneyaoair.baseclass.newcoupon.bean.BookResourceInfo;
import com.juneyaoair.baseclass.newcoupon.bean.ResourcePriceDetail;
import com.juneyaoair.baseclass.newcoupon.req.CouponProductBuyRequest;
import com.juneyaoair.baseclass.newcoupon.req.CouponProductQueryRequestDto;
import com.juneyaoair.baseclass.newcoupon.resp.CouponProductBuyResponse;
import com.juneyaoair.baseclass.request.booking.FlightInfo;
import com.juneyaoair.baseclass.request.booking.PassengerInfo;
import com.juneyaoair.baseclass.request.booking.QueryAvailChangeCouponRequest;
import com.juneyaoair.baseclass.request.coupons.QueryConponByCodeRequest;
import com.juneyaoair.baseclass.request.coupons.QueryMyProductCouponReq;
import com.juneyaoair.baseclass.request.coupons.ThemeCouponBindingRequest;
import com.juneyaoair.baseclass.response.coupons.*;
import com.juneyaoair.baseclass.response.payment.PaymentResp;
import com.juneyaoair.baseclass.salecoupon.request.ProductInfoBuy;
import com.juneyaoair.baseclass.salecoupon.request.ProductQuery;
import com.juneyaoair.baseclass.transferaccommodation.request.BaseCouponOrderRequestDto;
import com.juneyaoair.baseclass.transferaccommodation.resonse.BaseCouponOrderIdentity;
import com.juneyaoair.baseclass.transferaccommodation.resonse.BaseCouponOrderResponseDto;
import com.juneyaoair.baseclass.unlimit.UnlimitedCard2Config;
import com.juneyaoair.baseclass.unlimit.UpgradeCardV2Config;
import com.juneyaoair.baseclass.upclass.UpFlightInfo;
import com.juneyaoair.baseclass.upclass.request.UpClassBookOrderReq;
import com.juneyaoair.crm.IMemberService;
import com.juneyaoair.exception.OperationFailedException;
import com.juneyaoair.exception.ServiceException;
import com.juneyaoair.mobile.aop.annotation.InterfaceLog;
import com.juneyaoair.mobile.aop.annotation.NotDuplicate;
import com.juneyaoair.mobile.comm.service.IRedisService;
import com.juneyaoair.mobile.constants.MobileCoreBeanNames;
import com.juneyaoair.mobile.core.bean.dto.AirLineArrCityDto;
import com.juneyaoair.mobile.core.bean.dto.AirLineDepCityDto;
import com.juneyaoair.mobile.handler.HandlerConstants;
import com.juneyaoair.mobile.handler.comm.RedisKeyConfig;
import com.juneyaoair.mobile.handler.config.bean.HandConfig;
import com.juneyaoair.mobile.handler.controller.BassController;
import com.juneyaoair.mobile.handler.controller.crm.login.util.CrmUtil;
import com.juneyaoair.mobile.handler.controller.service.ThemeCardService;
import com.juneyaoair.mobile.handler.controller.util.CRMReqUtil;
import com.juneyaoair.mobile.handler.controller.util.CouponObjectConvert;
import com.juneyaoair.mobile.handler.controller.util.FraudApiInvoker;
import com.juneyaoair.mobile.handler.controller.util.VirtualPaymentConvert;
import com.juneyaoair.mobile.handler.controller.v2.util.CabinUpObjectConvert;
import com.juneyaoair.mobile.handler.controller.v2.util.ChangeObjectConvert;
import com.juneyaoair.mobile.handler.controller.v2.util.RightCouponConvert;
import com.juneyaoair.mobile.handler.manage.OrderManage;
import com.juneyaoair.mobile.handler.service.IBasicService;
import com.juneyaoair.mobile.handler.service.IMemberCacheService;
import com.juneyaoair.mobile.handler.service.IOrderService;
import com.juneyaoair.mobile.handler.service.IProductCacheService;
import com.juneyaoair.mobile.mongo.entity.AirlineMileage;
import com.juneyaoair.mobile.mongo.service.flightinfo.IFlightDistanceService;
import com.juneyaoair.pattern.PatternCommon;
import com.juneyaoair.thirdentity.boardingpass.req.BoardingCheckBuyRequestDto;
import com.juneyaoair.thirdentity.comm.request.Header;
import com.juneyaoair.thirdentity.comm.request.PtApiCRMRequest;
import com.juneyaoair.thirdentity.comm.request.PtRequest;
import com.juneyaoair.thirdentity.comm.response.PtCRMResponse;
import com.juneyaoair.thirdentity.comm.response.PtResponse;
import com.juneyaoair.thirdentity.member.request.PtMemberDetailRequest;
import com.juneyaoair.thirdentity.member.response.MemberBasicInfoSoaModel;
import com.juneyaoair.thirdentity.member.response.MemberContactSoaModel;
import com.juneyaoair.thirdentity.member.response.PtMemberDetail;
import com.juneyaoair.thirdentity.request.booking.PtSegmentInfo;
import com.juneyaoair.thirdentity.request.coupon.CouponQueryRequest;
import com.juneyaoair.thirdentity.request.coupon.PtQueryConponByCodeRequest;
import com.juneyaoair.thirdentity.salecoupon.common.*;
import com.juneyaoair.thirdentity.salecoupon.request.*;
import com.juneyaoair.thirdentity.salecoupon.response.*;
import com.juneyaoair.thirdentity.salecoupon.v2.common.DateLimits;
import com.juneyaoair.thirdentity.salecoupon.v2.common.Product;
import com.juneyaoair.thirdentity.salecoupon.v2.common.ProductInfo;
import com.juneyaoair.thirdentity.salecoupon.v2.common.ResourceBase;
import com.juneyaoair.thirdentity.salecoupon.v2.request.ProductQueryRequestDto;
import com.juneyaoair.thirdentity.salecoupon.v2.request.PtBookProductInfo;
import com.juneyaoair.thirdentity.salecoupon.v2.request.PtCreateOrderRequest;
import com.juneyaoair.thirdentity.salecoupon.v2.response.PtBaseCouponOrderIdentity;
import com.juneyaoair.thirdentity.tongdun.FinalDecisionEnum;
import com.juneyaoair.thirdentity.tongdun.response.FraudApiResponse;
import com.juneyaoair.utils.EncoderHandler;
import com.juneyaoair.utils.StringUtil;
import com.juneyaoair.utils.http.HttpResult;
import com.juneyaoair.utils.http.HttpUtil;
import com.juneyaoair.utils.json.JsonMapper;
import com.juneyaoair.utils.json.JsonUtil;
import com.juneyaoair.utils.util.BeanUtils;
import com.juneyaoair.utils.util.DateUtils;
import com.juneyaoair.utils.util.MdcUtils;
import com.juneyaoair.utils.util.VersionNoUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2018/11/9  17:15.
 */
@RequestMapping("/v2/coupon")
@RestController
@Api(value = "V2CouponController", tags = {"权益券服务"})
public class V2CouponController extends BassController {
    @Autowired
    private IFlightDistanceService flightDistanceService;
    @Autowired
    private HandConfig handConfig;
    @Resource(name = MobileCoreBeanNames.API_REDIS_SERVICE)
    private IRedisService apiRedisService;
    @Autowired
    private OrderManage orderManage;
    @Autowired
    private IMemberService memberService;
    @Autowired
    private IProductCacheService productCacheService;

    private static final String LOG_RESP = "【可售升舱券产品下单】请求号：{}，IP地址:{},客户端提交参数：{}，响应耗时：{},服务端响应结果：{}";
    private static final String LOG_ERROR = "数据异常！";

    @Autowired
    private IMemberCacheService memberCacheService;

    @Autowired
    private ThemeCardService   themeCardService;

    /**
     * @param req
     * @param servletRequest
     * @return
     * @see #queryUpCouponPro
     * @deprecated 建议使用下面方法
     */
    @Deprecated
    @ApiOperation(value = "查询升舱优惠券（旧）", notes = "查询升舱优惠券（旧）")
    @RequestMapping(value = "/queryAvailUpCoupon", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp<List<AvailCoupon>> queryAvailUpCoupon(@RequestBody BaseReq<CouponQuery> req, HttpServletRequest servletRequest) {
        String reqId = StringUtil.newGUID() + "_queryAvailUpCoupon";
        BaseResp resp = new BaseResp();
        String ip = this.getClientIP(servletRequest);
        String reqJson = JsonMapper.buildNormalMapper().toJson(req);
        log.info("请求号:{}，IP地址:{}，客户端提交参数：{}", reqId, ip, reqJson);
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<BaseReq>> violations = validator.validate(req);
        if (CollectionUtils.isNotEmpty(violations)) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo(violations.iterator().next().getMessage());
            return resp;
        }
        CouponQuery couponQuery = req.getRequest();
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(couponQuery.getFfpId(), couponQuery.getLoginKeyInfo(), req.getChannelCode());
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        AirlineMileage airlineMileage = null;
        if (!StringUtil.isNullOrEmpty(couponQuery.getFlightInfoList())) {
            //航距无需关注出发到达的方向
            FlightInfo flightInfo = couponQuery.getFlightInfoList().get(0);
            airlineMileage = flightDistanceService.queryAirlineMileage(flightInfo.getDepCity(), flightInfo.getArrCity());
        }
        //封装请求参数
        CouponQueryRequest couponQueryRequest = createPtCouponQuery(couponQuery, req.getChannelCode());
        couponQueryRequest.setAirlineMileage(airlineMileage == null ? 0 : Integer.valueOf(airlineMileage.getMileage()));
        String url = HandlerConstants.URL_FARE_API + HandlerConstants.AVAIL_UPGRADE_COUPON;
        Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
        String result = this.invokePost(couponQueryRequest, url, headMap, handConfig.getReadTimeout(), handConfig.getConnectTimeout());
        List<AvailCoupon> availCouponList = new ArrayList<>();
        if (StringUtil.isNullOrEmpty(result)) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("网络请求返回结果空");
        } else {
            AvailCouponsResponse availCouponsResponse;
            try {
                availCouponsResponse = (AvailCouponsResponse) JsonUtil.jsonToBean(result, AvailCouponsResponse.class);
                if (UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(availCouponsResponse.getResultCode())) {
                    resp.setObjData(availCouponsResponse.getAvailCouponList());
                } else {
                    resp.setObjData(availCouponList);
                }
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
            } catch (Exception e) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("数据格式解析异常！");
                log.error("请求号:{}，IP地址:{}，服务端响应结果：{}", reqId, ip, JsonUtil.objectToJson(resp));
                return resp;
            }
        }
        return resp;
    }

    @ApiOperation(value = "查询可售升舱券产品", notes = "查询可售升舱券产品")
    @RequestMapping(value = "queryUpCouponPro", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp queryUpCouponPro(@RequestBody BaseReq<CouponPro> req, HttpServletRequest request) {
        BaseResp resp = new BaseResp();
        String headChannelCode = request.getHeader(HandlerConstants.TOKEN_CHANNELCODE);
        String versionCode = request.getHeader(HandlerConstants.CLIENT_VERSION_CODE);
        String reqId = headChannelCode + versionCode + StringUtil.newGUID() + request.getRequestURI();
        String ip = this.getClientIP(request);
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<BaseReq<CouponPro>>> violations = validator.validate(req);
        if (CollectionUtils.isNotEmpty(violations)) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo(violations.iterator().next().getMessage());
            return resp;
        }
        String ver = "v1";
        //5.9.1之前使用v1的处理方式
        if (ChannelCodeEnum.MOBILE.getChannelCode().equals(headChannelCode)) {
            if (VersionNoUtil.toMVerInt(versionCode) >= 59300) {
                ver = "v2";
            }
        } else {
            ver = "v2";
        }
        try {
            CouponPro couponPro = req.getRequest();
            String channelCode = req.getChannelCode();
            String userNo = this.getChannelInfo(channelCode, "10");
            if ("v1".equals(ver)) {
                if (couponPro.isUseDefaultAirline()) {
                    couponPro.setDepCityCode("SHA");
                    couponPro.setArrCityCode("SYX");
                }
                if (StringUtil.isNullOrEmpty(couponPro.getDepCityCode()) || StringUtil.isNullOrEmpty(couponPro.getArrCityCode())) {
                    resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                    resp.setResultInfo("出发城市到达城市不能为空");
                    return resp;
                }
            }
            CouponProductQueryRequestDto couponProductQueryRequestDto = new CouponProductQueryRequestDto("Upgrade", HandlerConstants.VERSION, channelCode, userNo, 1, 10);
            if (!StringUtil.isNullOrEmpty(couponPro.getFfpId())) {
                //验证用户查询是否正常
                boolean flag = this.checkKeyInfo(couponPro.getFfpId(), couponPro.getLoginKeyInfo(), req.getChannelCode());
                if (!flag) {
                    resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                    resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                    return resp;
                }
                couponProductQueryRequestDto.setFfpId(couponPro.getFfpId());
                couponProductQueryRequestDto.setFfpCardNo(couponPro.getFfpCardNo());
            }
            if (couponPro.getCouponType() == 1) {
                couponProductQueryRequestDto.setLimitedType("Unlimited");
            }
            //根据
            String url = HandlerConstants.URL_FARE_API + HandlerConstants.COUPON_QUERY_PRODUCT_V2;
            Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
            couponProductQueryRequestDto.setSingleBookCondition(couponProductQueryRequestDto.createSingleBookCondition(couponPro.getDepCityCode(), couponPro.getArrCityCode()));
            couponProductQueryRequestDto.setSortCondition(couponProductQueryRequestDto.createDefaultSortCondition());
            HttpResult result = this.doPostClient(couponProductQueryRequestDto, url, headMap, handConfig.getReadTimeout(), handConfig.getConnectTimeout());
            if (result.isResult()) {
                if (!StringUtil.isNullOrEmpty(result.getResponse())) {
                    PtCouponProductQueryResponseDto ptCouponProductQueryResponseDto = (PtCouponProductQueryResponseDto) JsonUtil.jsonToBean(result.getResponse(), PtCouponProductQueryResponseDto.class);
                    if (UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(ptCouponProductQueryResponseDto.getResultCode())) {
                        if (StringUtil.isNullOrEmpty(ptCouponProductQueryResponseDto.getProductList())) {
                            resp.setResultCode(WSEnum.ERROR.getResultCode());
                            resp.setResultInfo("您选择的航线升舱券已售罄，您可补差价升舱~");
                        } else {
                            Map<String, CityInfoDto> cityInfoMap = basicService.queryAllCityMap(channelCode, ip);
                            if ("v1".equals(ver)) {
                                UpCouponPro upCouponPro = formatCouponPro(ptCouponProductQueryResponseDto.getProductList(),
                                        cityInfoMap, couponPro.getDepCityCode(), couponPro.getArrCityCode(), VoucherTypesEnum.UPGRADE.getCode());
                                resp.setObjData(upCouponPro);
                            }
                            if ("v2".equals(ver)) {
                                Map<String, List<UpCouponPro>> upProMap = formatCouponProV2(ptCouponProductQueryResponseDto.getProductList(),
                                        cityInfoMap, couponPro.getCouponType());
                                List<UpCouponPro> upCouponProListD = upProMap.get("domestic");
                                List<UpCouponPro> upCouponProListI = upProMap.get("region");
                                List<UpCouponPro> upCouponProListU = upProMap.get("productList");
                                if (1 == couponPro.getCouponType()) {
                                    if (CollectionUtils.isEmpty(upCouponProListU)) {
                                        resp.setResultCode(WSEnum.ERROR.getResultCode());
                                        resp.setResultInfo("您选择的航线升舱券已售罄，您可补差价升舱~");
                                    } else {
                                        //检验活动开放日期
                                        UpCouponProductInfo upCouponProductInfo = upCouponProListU.get(0).getProductInfo();
                                        List<ActivityConfig> activityConfigList = handConfig.getUnLimitConfigList();
                                        if (CollectionUtils.isNotEmpty(activityConfigList)) {
                                            ActivityConfig activityConfig = activityConfigList.stream().filter(config -> upCouponProductInfo.getProductId().equals(config.getActivityId())).findFirst().orElse(null);
                                            if (activityConfig != null) {
                                                if (VoucherTypesEnum.UPGRADEUNLIMITED.getCode().equals(activityConfig.getActivityType())) {
                                                    upCouponProductInfo.setUnLimitedUpgrade("yes");
                                                    Date curDate = new Date();
                                                    Date startDate = DateUtils.toDate(activityConfig.getActivityStartDate(), DateUtils.YYYY_MM_DD_HH_MM_PATTERN);
                                                    Date endDate = DateUtils.toDate(activityConfig.getActivityEndDate(), DateUtils.YYYY_MM_DD_HH_MM_PATTERN);
                                                    if (curDate.before(startDate)) {
                                                        resp.setResultCode(WSEnum.ERROR.getResultCode());
                                                        resp.setResultInfo("活动暂未开始，敬请期待！");
                                                        return resp;
                                                    }
                                                    if (curDate.after(endDate)) {
                                                        resp.setResultCode(WSEnum.ERROR.getResultCode());
                                                        resp.setResultInfo("活动已结束，期待您下次再来！");
                                                        return resp;
                                                    }
                                                }
                                            }else{
                                                resp.setResultCode(WSEnum.ERROR.getResultCode());
                                                resp.setResultInfo("活动已结束，期待您下次再来！");
                                                return resp;
                                            }
                                        }
                                        resp.setObjData(upProMap);
                                        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                                        resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                                    }
                                } else {
                                    if (CollectionUtils.isEmpty(upCouponProListD) && CollectionUtils.isEmpty(upCouponProListI)) {
                                        resp.setResultCode(WSEnum.ERROR.getResultCode());
                                        resp.setResultInfo("您选择的航线升舱券已售罄，您可补差价升舱~");
                                    } else {
                                        resp.setObjData(upProMap);
                                        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                                        resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                                    }
                                }
                            }
                        }
                    } else {
                        resp.setResultCode(WSEnum.ERROR.getResultCode());
                        if (ptCouponProductQueryResponseDto.getErrorInfo().indexOf("未查到产品信息") > -1) {
                            resp.setResultInfo("您选择的航线升舱券已售罄，您可补差价升舱~");
                        } else {
                            resp.setResultInfo(ptCouponProductQueryResponseDto.getErrorInfo());
                        }
                        if (UnifiedOrderResultEnum.OUT_OF_STOCK.getResultCode().equals(ptCouponProductQueryResponseDto.getResultCode())) {
                            //库存不足处理
                            resp.setResultInfo("您选择的航线升舱券已售罄，请换其它方式~");
                        }
                    }
                    return resp;
                } else {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo("产品信息返回数据空！");
                    return resp;
                }
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo(result.getResponse());
                return resp;
            }
        } catch (Exception e) {
            StackTraceElement stackTraceElement = e.getStackTrace()[0];
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(LOG_ERROR + stackTraceElement.getLineNumber());
            log.error("【可售升舱券产品查询】请求号:{},请求参数：{}，错误行数：{}，错误信息：", reqId, JsonMapper.buildNormalMapper().toJson(req), stackTraceElement.getLineNumber(), e);
            return resp;
        }
    }

    private Map<String, List<UpCouponPro>> formatCouponProV2(List<UpCouponProductInfo> productList, Map<String, CityInfoDto> cityInfoMap, int couponType) {
        Map<String, List<UpCouponPro>> upProMap = new HashMap<>();
        //产品按照价格排序
        sortProductByPrice(productList);
        if (couponType == 1) {
            List<UpCouponPro> filterProductList = new ArrayList<>();
            for (UpCouponProductInfo upCouponProductInfo : productList) {
                if ("yes".equals(upCouponProductInfo.getUnLimitedUpgrade())) {
                    UpCouponPro upCouponPro = convertUpCouponProductInfo(upCouponProductInfo, cityInfoMap);
                    UpgradeCardV2Config upgradeCardV2Config = handConfig.getUpgradeCardV2Config();
                    UpCouponProductInfo productInfo = upCouponPro.getProductInfo();
                    if(productInfo != null){
                        //设置无限升舱卡15周年版订单详情页可用日期，不可用日期
                        productInfo.setFlightAvailDate(upgradeCardV2Config.getYearCardFlightTimeBegin() + "至" + upgradeCardV2Config.getYearCardFlightTimeEnd() );
                        productInfo.setFlightUnAvailDate("(不含"+ upgradeCardV2Config.getUnusableTimeBegin().substring(0, 10) + "至" + upgradeCardV2Config.getUnusableTimeEnd().substring(0, 10)+")");
                        filterProductList.add(upCouponPro);
                    }
                }
            }
            upProMap.put("productList", filterProductList);
        } else {
            List<UpCouponPro> upCouponProListD = new ArrayList<>();
            List<UpCouponPro> upCouponProListI = new ArrayList<>();
            for (UpCouponProductInfo upCouponProductInfo : productList) {
                UpCouponPro upCouponPro = convertUpCouponProductInfo(upCouponProductInfo, cityInfoMap);
                //判断升舱券是国内还是国际
                if ("DOMESTIC".equals(upCouponProductInfo.getIsIntl())) {
                    upCouponProListD.add(upCouponPro);
                } else if ("INTL".equals(upCouponProductInfo.getIsIntl())) {
                    upCouponProListI.add(upCouponPro);
                }
            }
            upProMap.put("domestic", upCouponProListD);
            upProMap.put("region", upCouponProListI);
        }
        return upProMap;
    }

    @ApiOperation(value = "创建升舱券产品订单", notes = "创建升舱券产品订单")
    @RequestMapping(value = "bookUpCouponProOrder", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp<Map<String, Object>> bookUpCouponProOrder(@RequestBody BaseReq<BuyCouponOrder> req, HttpServletRequest request) {
        long t1 = System.currentTimeMillis();
        BaseResp resp = new BaseResp();
        String headChannelCode = request.getHeader(HandlerConstants.TOKEN_CHANNELCODE);
        String versionCode = request.getHeader(HandlerConstants.CLIENT_VERSION_CODE);
        String platform = request.getHeader(HandlerConstants.CLIENT_PLATFORM);
        String reqId = headChannelCode + versionCode + StringUtil.newGUID() + request.getRequestURI();
        String ip = this.getClientIP(request);
        String reqJson = JsonMapper.buildNormalMapper().toJson(req);
        saveReqInfo("可售升舱券产品下单", reqId, ip, reqJson);
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<BaseReq>> violations = validator.validate(req);
        if (CollectionUtils.isNotEmpty(violations)) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo(violations.iterator().next().getMessage());
            String reqStr = JsonMapper.buildNormalMapper().toJson(req);
            String respStr = JsonUtil.objectToJson(resp);
            log.info(LOG_RESP, reqId, ip, reqStr, System.currentTimeMillis() - t1, respStr);
            return resp;
        }
        //判断 invitationCode  长度不能大于50，并且校验输入格式数字和字母
        String invitationCode = req.getRequest().getInvitationCode();
        this.checkInvitationCode(resp, invitationCode);
        if ("10003".equals(resp.getResultCode())) {
            return resp;
        }
        try {
            BuyCouponOrder buyCouponOrder = req.getRequest();
            if (buyCouponOrder == null) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo("缺少请求参数");
                String reqStr = JsonMapper.buildNormalMapper().toJson(req);
                String respStr = JsonUtil.objectToJson(resp);
                log.info(LOG_RESP, reqId, ip, reqStr, System.currentTimeMillis() - t1, respStr);
                return resp;
            }
            //验证用户查询是否正常
            boolean flag = this.checkKeyInfo(buyCouponOrder.getFfpId(), buyCouponOrder.getLoginKeyInfo(), req.getChannelCode());
            if (!flag) {
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                return resp;
            }
            //检验活动开放日期
            UpCouponProductInfo upCouponProductInfo = buyCouponOrder.getProductInfo();
            List<ActivityConfig> activityConfigList = handConfig.getUnLimitConfigList();
            if (CollectionUtils.isNotEmpty(activityConfigList)) {
                ActivityConfig activityConfig = activityConfigList.stream().filter(config -> upCouponProductInfo.getProductId().equals(config.getActivityId())).findFirst().orElse(null);
                if (activityConfig != null) {
                    if (VoucherTypesEnum.UPGRADEUNLIMITED.getCode().equals(activityConfig.getActivityType())) {
                        upCouponProductInfo.setUnLimitedUpgrade("yes");
                        Date curDate = new Date();
                        Date startDate = DateUtils.toDate(activityConfig.getActivityStartDate(), DateUtils.YYYY_MM_DD_HH_MM_PATTERN);
                        Date endDate = DateUtils.toDate(activityConfig.getActivityEndDate(), DateUtils.YYYY_MM_DD_HH_MM_PATTERN);
                        if (curDate.before(startDate)) {
                            resp.setResultCode(WSEnum.ERROR.getResultCode());
                            resp.setResultInfo("活动暂未开始，敬请期待！");
                            return resp;
                        }
                        if (curDate.after(endDate)) {
                            resp.setResultCode(WSEnum.ERROR.getResultCode());
                            resp.setResultInfo("活动已结束，期待您下次再来！");
                            return resp;
                        }
                    }
                }
            }
            if ("yes".equalsIgnoreCase(upCouponProductInfo.getUnLimitedUpgrade())) {
                if (buyCouponOrder.getSaleCount() > 20) {
                    resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                    resp.setResultInfo("最多可购买20份");
                    return resp;
                }
            } else {
                if (buyCouponOrder.getSaleCount() > 5) {
                    resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                    resp.setResultInfo("最多可购买5份");
                    return resp;
                }
            }
            String channelCode = req.getChannelCode();
            //微信渠道暂时无产品查询时，查询时变换为MOBILE
            if (ChannelCodeEnum.WEIXIN.getChannelCode().equals(req.getChannelCode())) {
                channelCode = ChannelCodeEnum.MOBILE.getChannelCode();
            }
            String userNo = getChannelInfo(channelCode, "10");
            String mobile = null;
            String email = null;
            String accountName = null;
            //没有手机号
            if (StringUtils.isBlank(buyCouponOrder.getMobilePhone())) {
                String[] items = {MemberDetailRequestItemsEnum.CONTACTINFO.eName, MemberDetailRequestItemsEnum.BASICINFO.eName};
                PtApiCRMRequest<PtMemberDetailRequest> ptApiRequest = CRMReqUtil.buildMemberDetailReq(buyCouponOrder.getFfpCardNo(), buyCouponOrder.getFfpId(), request, channelCode, items);
                PtCRMResponse<PtMemberDetail> ptCRMResponse = memberService.memberDetail(ptApiRequest);
                if (ptCRMResponse.getCode() == 0) {
                    MemberContactSoaModel memberContactSoaModel = CRMReqUtil.getContactInfo(ptCRMResponse.getData().getContactInfo(), ContactTypeEnum.MOBILE.getCode());
                    if (memberContactSoaModel != null) {
                        buyCouponOrder.setMobilePhone(memberContactSoaModel.getContactNumber());
                        mobile = memberContactSoaModel.getContactNumber();
                    }
                    MemberContactSoaModel emailInfo = CRMReqUtil.getContactInfo(ptCRMResponse.getData().getContactInfo(), ContactTypeEnum.EMAIL.getCode());
                    if (emailInfo != null) {
                        email = emailInfo.getContactNumber();
                    }
                    MemberBasicInfoSoaModel basicInfo = ptCRMResponse.getData().getBasicInfo();
                    if (null != basicInfo) {
                        accountName = basicInfo.getCLastName() + basicInfo.getEFirstName();
                    }
                } else {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo(ptCRMResponse.getMsg());
                    return resp;
                }
            }
            //同盾参数
            if ("Y".equals(handConfig.getUnlimitedCardTongDun()) && "yes".equals(buyCouponOrder.getProductInfo().getUnLimitedUpgrade())) {
                FraudApiResponse fraudApiResponse = FraudApiInvoker.unlimitedUpgradeBuyRiskControl(buyCouponOrder.getBlackBox(), buyCouponOrder.getFfpCardNo(), platform, headChannelCode,
                        ip, mobile, email, accountName, buyCouponOrder.getProductInfo().getProductName(), VoucherTypesEnum.UPGRADEUNLIMITED.getCode(), buyCouponOrder.getSaleCount());
                if (fraudApiResponse.getSuccess() && fraudApiResponse.getFinal_decision().equalsIgnoreCase(FinalDecisionEnum.REJECT.getCode())) {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo("活动太火爆了，请稍后重试");
                    log.info("同盾拦截：用户卡号:{} 同盾响应:{}", buyCouponOrder.getFfpCardNo(), JsonUtil.objectToJson(fraudApiResponse));
                    return resp;
                }
            }
            //转换请求参数
            CouponProductBuyRequest couponProductBuyRequest = createCouponProBuyReq(buyCouponOrder, channelCode, userNo);
            //使用积分
            if (buyCouponOrder.getScore() > 0) {
                //积分金额大于订单的总金额
                if (new BigDecimal(buyCouponOrder.getScore()).compareTo(couponProductBuyRequest.getTotalPrice()) > 0) {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo("积分金额大于订单总金额");
                    String reqStr = JsonMapper.buildNormalMapper().toJson(req);
                    String respStr = JsonUtil.objectToJson(resp);
                    log.info(LOG_RESP, reqId, ip, reqStr, System.currentTimeMillis() - t1, respStr);
                    return resp;
                }
                //验证消费密码
                resp = orderService.checkFreeScoreLimit(buyCouponOrder.getFfpCardNo(), buyCouponOrder.getFfpId(), channelCode, getChannelInfo(channelCode, "40"), buyCouponOrder.getScore(), buyCouponOrder.getSalePwd(), request);
                if (!WSEnum.SUCCESS.getResultCode().equals(resp.getResultCode())) {
                    return resp;
                }
            }
            String url = HandlerConstants.URL_FARE_API + HandlerConstants.COUPON_BUY_PRODUCT_V2;
            Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
            HttpResult result = this.doPostClient(couponProductBuyRequest, url, headMap, handConfig.getReadTimeout(), handConfig.getConnectTimeout());
            if (result.isResult() && !StringUtil.isNullOrEmpty(result.getResponse())) {
                CouponProductBuyResponse couponProductBuyResponse = (CouponProductBuyResponse) JsonUtil.jsonToBean(result.getResponse(), CouponProductBuyResponse.class);
                if (UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(couponProductBuyResponse.getResultCode())) {
                    resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                    resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                    Map<String, Object> orderInfo = new HashMap<>();
                    orderInfo.put("orderNo", couponProductBuyResponse.getOrderNo());
                    orderInfo.put("channelOrderNo", couponProductBuyResponse.getChannelOrderNo());
                    orderInfo.put("payState", false);
                    //0元同步支付
                    if (buyCouponOrder.getScore() > 0 && new BigDecimal(buyCouponOrder.getScore()).compareTo(couponProductBuyRequest.getTotalPrice()) == 0) {
                        String key = getChannelInfo(req.getChannelCode(), "20");
                        String postUrl = HandlerConstants.URL_PAY;
                        Map<String, String> parametersMap = VirtualPaymentConvert.payment0(req.getChannelCode(), couponProductBuyResponse.getOrderNo(), couponProductBuyResponse.getChannelOrderNo(), key, "CouponType", "", "O");
                        parametersMap.put("UseScore", String.valueOf(buyCouponOrder.getScore()));
                        HttpResult payResult = doPayPost(postUrl, parametersMap);
                        PaymentResp paymentResp;
                        if (payResult.isResult()) {
                            String paymentInfo = payResult.getResponse().trim();
                            log.info("请求号:{}，IP地址:{}，0元支付结果：{}", reqId, ip, paymentInfo);
                            paymentResp = (PaymentResp) JsonUtil.jsonToBean(paymentInfo, PaymentResp.class);
                            //虚拟支付成功
                            if (paymentResp != null && "1001".equals(paymentResp.getRespCode())) {
                                orderInfo.put("payState", true);
                                resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                            } else {
                                resp.setResultCode(WSEnum.ERROR.getResultCode());
                                resp.setResultInfo("权益券购买支付失败！");
                            }
                        } else {
                            resp.setResultCode(WSEnum.ERROR.getResultCode());
                            resp.setResultInfo("支付请求出错");
                        }
                    }
                    resp.setObjData(orderInfo);
                } else {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo(couponProductBuyResponse.getErrorInfo());
                }
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("下单结果返回数据空！");
            }
            String reqStr = JsonMapper.buildNormalMapper().toJson(req);
            String respStr = JsonUtil.objectToJson(resp);
            log.info(LOG_RESP, reqId, ip, reqStr, System.currentTimeMillis() - t1, respStr);
            return resp;
        } catch (Exception e) {
            StackTraceElement stackTraceElement = e.getStackTrace()[0];
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(LOG_ERROR + stackTraceElement.getLineNumber());
            log.error("【可售升舱券产品下单】请求号:{},请求参数：{}，错误行数：{}，错误信息：", reqId, JsonMapper.buildNormalMapper().toJson(req), stackTraceElement.getLineNumber(), e);
            log.info("【可售升舱券产品下单】IP地址:{},客户端提交参数：{}，响应耗时：{},服务端响应结果：{}", ip, JsonMapper.buildNormalMapper().toJson(req), System.currentTimeMillis() - t1, JsonUtil.objectToJson(resp));
            return resp;
        }
    }

    @ApiOperation(value = "升舱产品航线查询", notes = "升舱产品航线查询")
    @RequestMapping(value = "upProflightLine", method = RequestMethod.POST)
    public BaseResp<List<AirLineDepCityDto>> getFlightLine(@RequestBody BaseReq<FlightLineQuest> req, HttpServletRequest request) {
        BaseResp<List<AirLineDepCityDto>> resp = new BaseResp<>();
        String ip = this.getClientIP(request);
        String realChannelCode = request.getHeader(HandlerConstants.TOKEN_CHANNELCODE);
        try {
            FlightLineQuest flightLineQuest = req.getRequest();
            String channelCode = req.getChannelCode();
            if (flightLineQuest != null && !StringUtil.isNullOrEmpty(flightLineQuest.getFfpId())) {
                boolean flag = this.checkKeyInfo(flightLineQuest.getFfpId(), flightLineQuest.getLoginKeyInfo(), channelCode);
                if (!flag) {
                    resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                    resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                    return resp;
                }
            }
            String searchTypes = VoucherTypesEnum.UPGRADE.getCode();
            if (flightLineQuest != null && StringUtils.isNotBlank(flightLineQuest.getSearchTypes())) {
                searchTypes = flightLineQuest.getSearchTypes();
            }
            List<AirLineDepCityDto> newlist = new ArrayList<>();//新的航线
            List<AirLineInfoDepCityDto> list = basicService.selectAllAirline();
            resp.setChannelCode(channelCode);
            String userNo = this.getChannelInfo(channelCode, "10");
            if (!StringUtil.isNullOrEmpty(list)) {
                //查询资源限制航班
                String url = HandlerConstants.URL_FARE_API + HandlerConstants.COUPON_PRO_LIMIT;
                Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
                PtCouponProductLimitRequestDto ptCouponProductLimitRequestDto = new PtCouponProductLimitRequestDto(HandlerConstants.VERSION, req.getChannelCode(), userNo, searchTypes);
                HttpResult result = this.doPostClient(ptCouponProductLimitRequestDto, url, headMap, handConfig.getReadTimeout(), handConfig.getConnectTimeout());
                if (result.isResult() && !StringUtil.isNullOrEmpty(result.getResponse())) {
                    PtCouponProductLimitResponseDto ptCouponProductLimitResponseDto = (PtCouponProductLimitResponseDto) JsonUtil.jsonToBean(result.getResponse(), PtCouponProductLimitResponseDto.class);
                    if (UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(ptCouponProductLimitResponseDto.getResultCode()) && !StringUtil.isNullOrEmpty(ptCouponProductLimitResponseDto.getAirlines())) {
                        //all航线列表
                        for (AirLineInfoDepCityDto airLineDepCityDto : list) {
                            List<AirLineArrCityDto> arrnewlist = new ArrayList<>();
                            // 遍历到达城市
                            airLineDepCityDto.getAirline().stream().forEach(airLineArrCityDto -> {
                                //支持的航线产品列表
                                for (PtCouponProductLimitResponseDto.Airline airline : ptCouponProductLimitResponseDto.getAirlines()) {
                                    //出发城市
                                    if (airline.getDepCityCode().equals(airLineDepCityDto.getCityCode()) && airLineArrCityDto.getCityCode().equals(airline.getArrCityCode())) {
                                        //匹配到达航线
                                        AirLineArrCityDto lineArrCityDto = new AirLineArrCityDto();
                                        BeanUtils.copyProperties(airLineArrCityDto, lineArrCityDto);
                                        arrnewlist.add(lineArrCityDto);
                                    }
                                }
                            });
                            if (!StringUtil.isNullOrEmpty(arrnewlist)) {
                                AirLineDepCityDto lineDepCityDto = new AirLineDepCityDto();
                                BeanUtils.copyProperties(airLineDepCityDto, lineDepCityDto);
                                lineDepCityDto.setAirline(arrnewlist);
                                newlist.add(lineDepCityDto);
                            }
                        }
                        if (!StringUtil.isNullOrEmpty(newlist)) {
                            apiRedisService.putData(RedisKeyConfig.COMMON_UP_AIRLINE, JsonUtil.objectToJson(newlist), 60L * 24 * 7);
                        }
                    }
                }
            }
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
            resp.setObjData(newlist);
            return resp;
        } catch (Exception e) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR.getResultInfo());
            return resp;
        }
    }

    @ApiOperation(value = "升舱优惠券使用检验", notes = "升舱优惠券使用检验")
    @RequestMapping(value = "checkAvailUpClassCoupon", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp<Object> checkAvailUpClassCoupon(@RequestBody BaseReq<UpClassBookOrderReq> req, HttpServletRequest request) {
        BaseResp resp = new BaseResp();
        long t1 = System.currentTimeMillis();
        String reqId = StringUtil.newGUID() + request.getRequestURI();
        String ip = this.getClientIP(request);
        //参数有效性检验
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<BaseReq>> violations = validator.validate(req);
        if (CollectionUtils.isNotEmpty(violations)) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo(violations.iterator().next().getMessage());
            String reqStr = JsonMapper.buildNormalMapper().toJson(req);
            String respStr = JsonUtil.objectToJson(resp);
            log.info("【升舱优惠券使用检验】请求号：{}，IP地址:{},客户端提交参数：{}，响应耗时：{},服务端响应结果：{}", reqId, ip, reqStr, System.currentTimeMillis() - t1, respStr);
            return resp;
        }
        try {
            UpClassBookOrderReq upClassBookOrderReq = req.getRequest();
            String channelCode = req.getChannelCode();
            boolean flag = this.checkKeyInfo(upClassBookOrderReq.getFfpId(), upClassBookOrderReq.getLoginKeyInfo(), channelCode);
            if (!flag) {
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                return resp;
            }
            PtCouponProductUseCheckRequestDto ptCouponProductUseCheckRequestDto = createPtCouponProductUseCheckRequest(upClassBookOrderReq, channelCode);
            String url = HandlerConstants.URL_FARE_API + HandlerConstants.COUPON_CHECK_PRODUCT_V2;
            Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
            HttpResult result = this.doPostClient(ptCouponProductUseCheckRequestDto, url, headMap, handConfig.getReadTimeout(), handConfig.getConnectTimeout());
            if (result.isResult() && !StringUtil.isNullOrEmpty(result.getResponse())) {
                PtCouponProductUseCheckResponseDto ptCouponProductUseCheckResponseDto = (PtCouponProductUseCheckResponseDto) JsonUtil.jsonToBean(result.getResponse(), PtCouponProductUseCheckResponseDto.class);
                if (UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(ptCouponProductUseCheckResponseDto.getResultCode())) {
                    resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                    resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                } else {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo(ptCouponProductUseCheckResponseDto.getErrorInfo());
                }
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("权益券检验结果返回数据空！");
            }
            String reqStr = JsonMapper.buildNormalMapper().toJson(req);
            String respStr = JsonUtil.objectToJson(resp);
            log.info("【升舱优惠券使用检验】请求号：{}，IP地址:{},客户端提交参数：{}，响应耗时：{},服务端响应结果：{}", reqId, ip, reqStr, System.currentTimeMillis() - t1, respStr);
            return resp;
        } catch (Exception e) {
            StackTraceElement stackTraceElement = e.getStackTrace()[0];
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(LOG_ERROR + stackTraceElement.getLineNumber());
            log.error("【升舱优惠券使用检验】请求号:{},请求参数：{}，错误行数：{}，错误信息：", reqId, JsonMapper.buildNormalMapper().toJson(req), stackTraceElement.getLineNumber(), e);
            log.info("【升舱优惠券使用检验】请求号:{},IP地址:{},客户端提交参数：{}，响应耗时：{},服务端响应结果：{}", reqId, ip, JsonMapper.buildNormalMapper().toJson(req), System.currentTimeMillis() - t1, JsonUtil.objectToJson(resp));
            return resp;
        }
    }

    @ApiOperation(value = "查询可用升舱优惠券", notes = "查询可用升舱优惠券")
    @RequestMapping(value = "queryAvailUpClassCoupon", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @InterfaceLog
    public BaseResp<List<AvailCoupon>> queryAvailUpClassCoupon(@RequestBody @Validated BaseReq<UpClassBookOrderReq> req, BindingResult bindingResult, HttpServletRequest request) {
        String reqId = StringUtil.newGUID() + request.getRequestURI();
        BaseResp resp = new BaseResp();
        String ip = this.getClientIP(request);
        String headChannelCode = request.getHeader(HEAD_CHANNEL_CODE);
        if (bindingResult.hasErrors()) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
            return resp;
        }
        UpClassBookOrderReq couponQuery = req.getRequest();
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(couponQuery.getFfpId(), couponQuery.getLoginKeyInfo(), req.getChannelCode());
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        //封装请求参数

        String channelCode = req.getChannelCode();
        UpFlightInfo upFlightInfo = couponQuery.getUpFlightInfo();
        String certNo = apiRedisService.getData(RedisKeyConfig.createUpIdInfo(couponQuery.getTicketNo()));
        if (StringUtils.isBlank(certNo)) {
            resp.setResultCode(WSEnum.OPERATION_TIMEOUT_50016.getResultCode());
            resp.setResultInfo("操作超时，请返回上一层级重新查询");
            return resp;
        }
        PtCouponProductGetRequestDto ptCouponProductGetRequestDto = CabinUpObjectConvert.createCouponProductGetRequestDto(upFlightInfo, channelCode, couponQuery.getFfpId(), couponQuery.getFfpCardNo());
        List<PassengerInfo> passengerInfoList = upFlightInfo.getPassengerInfoList();
        if(CollectionUtils.isNotEmpty(passengerInfoList)){
            ptCouponProductGetRequestDto.setPassengerName(passengerInfoList.get(0).getPassengerName());
            ptCouponProductGetRequestDto.setIdNbr(certNo);
        }
        String url = HandlerConstants.URL_FARE_API + HandlerConstants.COUPON_MY_PRODUCT_V2;
        Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
        HttpResult result = this.doPostClient(ptCouponProductGetRequestDto, url, headMap, handConfig.getReadTimeout(), handConfig.getConnectTimeout());
        if (result.isResult() && !StringUtil.isNullOrEmpty(result.getResponse())) {
            PtCouponProductGetResponseDto ptCouponProductGetResponseDto = (PtCouponProductGetResponseDto) JsonUtil.jsonToBean(result.getResponse(), PtCouponProductGetResponseDto.class);
            if (UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(ptCouponProductGetResponseDto.getResultCode())) {
                Map<String, CityInfoDto> cityInfoMap = basicService.queryAllCityMap(channelCode, ip);
                Map<String, AirPortInfoDto> airPortInfoMap = basicService.queryAllAirportMap(channelCode, ip);
                // 升舱只有一个乘客
                PassengerInfo passengerInfo = upFlightInfo.getPassengerInfoList().get(0);
                // 港澳台地区
                boolean HKMCTWRegion = false;
                if (HandlerConstants.TRIP_TYPE_I.equals(upFlightInfo.getInterFlag()) &&
                        (handConfig.getHKMCTWRegions().contains(upFlightInfo.getDeptCityCode())
                                || handConfig.getHKMCTWRegions().contains(upFlightInfo.getArrCityCode()))) {
                    HKMCTWRegion = true;
                }
                boolean unlimitedUpClass = false;
                //缓存五分钟，是否可用2021版无限升舱卡
                String key = RedisKeyConfig.UNLIMITED_UPGRADE + couponQuery.getFfpId() + ":" + couponQuery.getTicketNo().replaceAll("-", "");
                String unlimitedUpFlag = apiRedisService.getData(key);
                if(StringUtils.isNotBlank(unlimitedUpFlag) && "Y".equals(unlimitedUpFlag)){
                        unlimitedUpClass = true;
                }else{
                    int supportUnlimitCard = V2UpClassController.supportUpgradeCard(headChannelCode, NumberUtils.toInt(req.getVersionCode()));
                    if (supportUnlimitCard > 0 && !handConfig.getFreeTicketCabin().equals(upFlightInfo.getCabin())) {
                        UpgradeCardV2Config upgradeCardV2Config = handConfig.getUpgradeCardV2Config();
                        Date flightTime = DateUtils.toDate(upFlightInfo.getFlightDate(), DateUtils.YYYY_MM_DD_PATTERN);
                        // 2021版升舱卡 只能升国内舱位
                        if (supportUnlimitCard >= 2) {
                            boolean upClassCardAvailable = false;
                            try {
                                upClassCardAvailable = orderManage.unlimitedUpClassAvailable(couponQuery.getFfpId(), couponQuery.getFfpCardNo(),
                                        channelCode, this.getChannelInfo(channelCode, "10"), this.getChannelInfo(channelCode, "40"),
                                        passengerInfo.getCertType(), passengerInfo.getCertNo(), ip, HKMCTWRegion, passengerInfo.getPassengerName());
                            } catch (Exception e) {
                                log.error("查询无限升舱卡出现异常", e);
                            }
                            if (upClassCardAvailable) {
                                // 在升舱卡时间范围内
                                Date unusableTimeBegin = DateUtils.toDate(upgradeCardV2Config.getUnusableTimeBegin(), DateUtils.YYYY_MM_DD_HH_MM_SS_PATTERN);
                                Date unusableTimeEnd = DateUtils.toDate(upgradeCardV2Config.getUnusableTimeEnd(), DateUtils.YYYY_MM_DD_HH_MM_SS_PATTERN);
                                // 不在春运期间可用升舱卡
                                if (null != unusableTimeBegin && null != unusableTimeEnd && (flightTime.getTime() <= unusableTimeBegin.getTime()
                                        || flightTime.getTime() >= unusableTimeEnd.getTime())) {
                                    unlimitedUpClass = true;
                                }
                            }
                        }
                    }
                }
                List<AvailCoupon> availCouponList = RightCouponConvert.formatAvailCouponList("R", ptCouponProductGetResponseDto.getVouchers(), cityInfoMap, airPortInfoMap, handConfig);
                if (unlimitedUpClass) {
                    // 只展示无限升舱卡
                    availCouponList = availCouponList.stream().filter(availCoupon -> availCoupon.isBound() && availCoupon.isUnlimitedUpClass()).collect(Collectors.toList());
                } else {
                    availCouponList = availCouponList.stream().filter(availCoupon -> !availCoupon.isUnlimitedUpClass()).collect(Collectors.toList());
                }
                // 小程序 和 APP6.0.0 版本之前不展示不可用升舱券
                if (ChannelCodeEnum.MOBILE.getChannelCode().equals(headChannelCode) && NumberUtils.toInt(req.getVersionCode()) < 60000
                        || ChannelCodeEnum.WXAPP.getChannelCode().equals(request.getHeader(HEAD_CHANNEL_CODE))) {
                    availCouponList = availCouponList.stream().filter(AvailCoupon::getIsAvailable).collect(Collectors.toList());
                }
                //按照有效期排序
                if (!StringUtil.isNullOrEmpty(availCouponList)) {
                    sortEndUseDate(availCouponList);
                }
                resp.setObjData(availCouponList);
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo(ptCouponProductGetResponseDto.getErrorInfo());
            }
        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("可用升舱券返回数据空！");
        }
        return resp;
    }

    @ApiOperation(value = "查询权益劵/卡数量", notes = "查询权益劵/卡数量")
    @RequestMapping(value = "queryMyProductCouponCount", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @InterfaceLog
    public BaseResp queryMyProductCouponCount(@RequestBody @Validated BaseReq<QueryMyProductCouponReq> req, BindingResult bindingResult, HttpServletRequest request){
        BaseResp  resp = new BaseResp();
        String ip = this.getClientIP(request);
        if (bindingResult.hasErrors()) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
            return resp;
        }
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(req.getRequest().getFfpId(), req.getRequest().getLoginKeyInfo(), req.getChannelCode());
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        QueryMyProductCouponCountResponse ptCouponProductGetResponseDto = orderManage.getQueryMyProductCouponCount(req.getRequest(), ip);
        QueryMyProductCouponCountRsep  queryMyProductCouponCountRsep  = new  QueryMyProductCouponCountRsep();
        queryMyProductCouponCountRsep.setFfpCardNo(req.getRequest().getFfpCardNo());
        queryMyProductCouponCountRsep.setFfpId(req.getRequest().getFfpId());
        if ("1001".equals(ptCouponProductGetResponseDto.getResultCode())){
            queryMyProductCouponCountRsep.setRecordCount(ptCouponProductGetResponseDto.getRecordCount());
            resp.setObjData(queryMyProductCouponCountRsep);
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
        }else {
            queryMyProductCouponCountRsep.setRecordCount(0);
            resp.setObjData(queryMyProductCouponCountRsep);
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR.getResultInfo());
        }
        return  resp;
    }



    @ApiOperation(value = "查询权益劵/卡列表", notes = "查询权益劵/卡列表")
    @RequestMapping(value = "queryMyProductCoupon", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @InterfaceLog
    public BaseResp QueryMyProductCoupon(@RequestBody @Validated BaseReq<QueryMyProductCouponReq> req, BindingResult bindingResult, HttpServletRequest request){
        BaseResp  resp = new BaseResp();
        String reqId = MdcUtils.getRequestId();
        String ip = this.getClientIP(request);
        //封装请求参数
        String channelCode = req.getChannelCode();
        if (bindingResult.hasErrors()) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
            return resp;
        }
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(req.getRequest().getFfpId(), req.getRequest().getLoginKeyInfo(), req.getChannelCode());
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        try {
            QueryMyProductCouponCountRequest  queryMyProductCouponCountRequest =  createQueryMyProductCouponCountRequest(req.getRequest(),ip);
            String url = HandlerConstants.URL_FARE_OPEN_API + HandlerConstants.COUPON_MY_PRODUCT_V2;
            Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
            HttpResult result = this.doPostClient(queryMyProductCouponCountRequest, url, headMap, handConfig.getReadTimeout(), handConfig.getConnectTimeout());
            if (result.isResult() && !StringUtil.isNullOrEmpty(result.getResponse())) {
                PtCouponProductGetResponseDto ptCouponProductGetResponseDto = (PtCouponProductGetResponseDto) JsonUtil.jsonToBean(result.getResponse(), PtCouponProductGetResponseDto.class);
                if (UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(ptCouponProductGetResponseDto.getResultCode())) {
                    Map<String, CityInfoDto> cityInfoMap = basicService.queryAllCityMap(channelCode, ip);
                    Map<String, AirPortInfoDto> airPortInfoMap = basicService.queryAllAirportMap(channelCode, ip);
                    List<AvailCoupon> availCouponList = RightCouponConvert.formatAvailCouponList(req.getRequest().getAvailableStatus(), ptCouponProductGetResponseDto.getVouchers(), cityInfoMap, airPortInfoMap, handConfig);
                    if ("Card".equals(req.getRequest().getRuleModel())){
                        ThemeCouponBindingRequest themeCouponBindingRequest  = new ThemeCouponBindingRequest();
                        themeCouponBindingRequest.setFfpId(req.getRequest().getFfpId());
                        themeCouponBindingRequest.setFfpCardNo(req.getRequest().getFfpCardNo());
                        themeCouponBindingRequest.setChannelCode(channelCode);
                       List<String>  proNums=availCouponList.stream().map(AvailCoupon::getProNum).collect(Collectors.toList());
                        themeCouponBindingRequest.setProductNumList(proNums);
                        List<String> list = new ArrayList<>();
                        list.addAll(handConfig.getThemeCouponList());
                        themeCouponBindingRequest.setThemeCouponTypeList(list);
                        String COUPON_URL = HandlerConstants.URL_COUPON_API + HandlerConstants.ACCOUNT_BIND;
                        HttpResult couponResult = this.doPostClient(themeCouponBindingRequest, COUPON_URL, headMap, handConfig.getReadTimeout(), handConfig.getConnectTimeout());
                        if (couponResult.isResult() && !StringUtil.isNullOrEmpty(couponResult.getResponse())) {
                            ThemeCouponBindingResponse themeCouponBindingResponse = (ThemeCouponBindingResponse) JsonUtil.jsonToBean(couponResult.getResponse(), ThemeCouponBindingResponse.class);
                            if (WSEnum.SUCCESS.getResultCode().equals(themeCouponBindingResponse.getResultCode())){
                                List<BindDetails>      details =    themeCouponBindingResponse.getBindDetails();
                                if (CollectionUtils.isNotEmpty(details)){
                                    availCouponList.stream().forEach(availCoupon -> {
                                        details.stream().forEach(detail->{
                                            if (availCoupon.getProNum().equals(detail.getProductNum())){
                                                Integer finalThemeBoundBut = getInteger(detail);
                                                availCoupon.setThemeBoundBut(finalThemeBoundBut);
                                            }
                                        });

                                    });
                                }

                            }
                        }
                    } else  {
                        //贵宾休息室券增加实名认证
                        String[] items = {MemberDetailRequestItemsEnum.BASICINFO.eName
                                , MemberDetailRequestItemsEnum.CERTIFICATEINFO.eName
                                , MemberDetailRequestItemsEnum.REALVERIFYINFOS.eName};
                        PtApiCRMRequest<PtMemberDetailRequest> ptApiRequestCert = CRMReqUtil.buildMemberDetailReq(req.getRequest().getFfpCardNo(), req.getRequest().getFfpId(), request, channelCode, items);
                        PtCRMResponse<PtMemberDetail> ptCRMResponse = memberService.memberDetail(ptApiRequestCert);
                        Boolean  realNameStatus=CrmUtil.judgeRealNameStatus(ptCRMResponse.getData().getRealVerifyInfos());
                        //设置实名状态
                        availCouponList.stream().forEach(availCoupon -> {availCoupon.setRealNameStatus(realNameStatus);});
                    }
                    resp.setObjData(availCouponList);
                    resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                    resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                }
            }else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("数据返回为空");
            }
        }catch (Exception e){
            saveError("查询权益劵/卡列表", reqId, ip, JsonMapper.buildNormalMapper().toJson(req), e);
        }

        return  resp;
    }

    @NotNull
    private static Integer getInteger(BindDetails bindDetail) {
        Integer themeBoundBut =4;
        if (bindDetail.getAdultSurplusBindTime()>0&&bindDetail.getChildSurplusBindTime()>0){
            themeBoundBut=1;
        }
        if (bindDetail.getAdultSurplusBindTime()>0&&bindDetail.getChildSurplusBindTime()==0) {
            themeBoundBut=2;
        }
        if (bindDetail.getAdultSurplusBindTime()==0&&bindDetail.getChildSurplusBindTime()>0) {
            themeBoundBut=3;
        }
        Integer finalThemeBoundBut = themeBoundBut;
        return finalThemeBoundBut;
    }

    //使用截止日期排序
    private void sortEndUseDate(List<AvailCoupon> availCouponList) {
        Collections.sort(availCouponList, Comparator.comparing(AvailCoupon::getIsAvailable).reversed().thenComparing(AvailCoupon::getEndData));
    }





    /**
     * 查询权益劵/卡列表请求参数
     *
     * @return
     */

    private QueryMyProductCouponCountRequest createQueryMyProductCouponCountRequest(QueryMyProductCouponReq queryMyProductCouponReq, String ip) {
        QueryMyProductCouponCountRequest queryMyProductCouponCountRequest = new QueryMyProductCouponCountRequest();
        queryMyProductCouponCountRequest.setVersion("10");
        queryMyProductCouponCountRequest.setChannelCode(ChannelCodeEnum.MOBILE.getChannelCode());
        queryMyProductCouponCountRequest.setRemoteAddr(ip);
        queryMyProductCouponCountRequest.setFfpId(queryMyProductCouponReq.getFfpId());
        queryMyProductCouponCountRequest.setFfpCardNo(queryMyProductCouponReq.getFfpCardNo());
        queryMyProductCouponCountRequest.setRuleModel(queryMyProductCouponReq.getRuleModel());
        queryMyProductCouponCountRequest.setAvailableStatus(queryMyProductCouponReq.getAvailableStatus());
        queryMyProductCouponCountRequest.setUserNo("10001");
        queryMyProductCouponCountRequest.setPageNo(1);
        queryMyProductCouponCountRequest.setPageSize(100);
        List<String> stateList =new ArrayList<String>();

        List<String> voucherTypes =null;
        if ("Card".equals(queryMyProductCouponReq.getRuleModel())){
            if ("Active".equals(queryMyProductCouponReq.getAvailableStatus())){
                stateList.add(OrderCouponStateEnum.Not.getStateCode());
                stateList.add(OrderCouponStateEnum.Used.getStateCode());
                stateList.add(OrderCouponStateEnum.Available.getStateCode());
                stateList.add(OrderCouponStateEnum.Appointment.getStateCode());
                stateList.add(OrderCouponStateEnum.Giving.getStateCode());
            }
            if ("Expired".equals(queryMyProductCouponReq.getAvailableStatus())){
                stateList.add(OrderCouponStateEnum.Overdue.getStateCode());
                stateList.add(OrderCouponStateEnum.WittenOff.getStateCode());
                stateList.add(OrderCouponStateEnum.GiveAway.getStateCode());
                stateList.add(OrderCouponStateEnum.Refund.getStateCode());
            }
            List<String> productCouponType=new ArrayList<>();
            productCouponType.addAll(handConfig.getThemeCouponList());
            voucherTypes=productCouponType;
        }else {
            if("Not".equals(queryMyProductCouponReq.getAvailableStatus())){
                stateList.add(OrderCouponStateEnum.Not.getStateCode());
                stateList.add(OrderCouponStateEnum.Appointment.getStateCode());
            }
            if ("Used".equals(queryMyProductCouponReq.getAvailableStatus())){
                stateList.add(OrderCouponStateEnum.Used.getStateCode());
                stateList.add(OrderCouponStateEnum.Giving.getStateCode());
                stateList.add(OrderCouponStateEnum.WittenOff.getStateCode());
            }
            if ("Expired".equals(queryMyProductCouponReq.getAvailableStatus())){
                stateList.add(OrderCouponStateEnum.Overdue.getStateCode());
                stateList.add(OrderCouponStateEnum.GiveAway.getStateCode());
                stateList.add(OrderCouponStateEnum.Refund.getStateCode());
            }
            voucherTypes = ProductCouponTypesEnum.getAllProductCouponTypeCodes();
        }

        queryMyProductCouponCountRequest.setCouponState(stateList);
        queryMyProductCouponCountRequest.setVoucherTypes(voucherTypes);
        return queryMyProductCouponCountRequest;
    }

    /**
     * 可用升舱券请求参数
     *
     * @param couponQuery
     * @param channelCode
     * @return
     */

    private CouponQueryRequest createPtCouponQuery(CouponQuery couponQuery, String channelCode) {
        String userNo = getChannelInfo(channelCode, "10");
        List<PtSegmentInfo> segmentInfoList = new ArrayList<>();
        CouponQueryRequest couponQueryReq = new CouponQueryRequest(
                HandlerConstants.VERSION,
                channelCode,
                userNo,
                couponQuery.getFfpId(),
                couponQuery.getFfpCardNo(),
                "R",
                segmentInfoList
        );
        //航班处理
        if (!StringUtil.isNullOrEmpty(couponQuery.getFlightInfoList())) {
            for (FlightInfo flightInfo : couponQuery.getFlightInfoList()) {
                PtSegmentInfo segmentInfo = new PtSegmentInfo();
                BeanUtils.copyProperties(flightInfo, segmentInfo);
                segmentInfo.setCabin(flightInfo.getCabinFareList().get(0).getCabinCode());
                segmentInfo.setCabinClass(flightInfo.getCabinFareList().get(0).getCabinClass());
                segmentInfoList.add(segmentInfo);
            }
        }
        couponQueryReq.setCouponSource(YouHuiCouponSourceEnum.HOUP.coupoSource);
        return couponQueryReq;
    }

    /**
     * 封装升舱券产品返回结果
     *
     * @param productList
     * @return
     */
    private UpCouponPro formatCouponPro(List<UpCouponProductInfo> productList, Map<String, CityInfoDto> cityInfoMap, String deptCityCode, String arrCityCode, String voucherType) {
        //产品按照价格排序
        sortProductByPrice(productList);
        //取价格最低的产品
        UpCouponProductInfo productInfo = productList.get(0);
        String depCityName = cityInfoMap.get(deptCityCode).getCityName();
        String arrCityName = cityInfoMap.get(arrCityCode).getCityName();
        if (VoucherTypesEnum.RESCHEDULECOUPON.getCode().equals(voucherType)) {
            // 改期券名称中间为空格
            productInfo.setProductName("改期券 " + depCityName + "-" + arrCityName);
        } else {
            productInfo.setProductName("升舱券—" + depCityName + "-" + arrCityName);
        }
        productInfo.setDepCityCode(deptCityCode);
        productInfo.setArrCityCode(arrCityCode);
        UpCouponPro upCouponPro = convertUpCouponProductInfo(productInfo, cityInfoMap);
        upCouponPro.setDepCityCode(deptCityCode);
        upCouponPro.setArrCityCode(arrCityCode);
        upCouponPro.setDepCityName(depCityName);
        upCouponPro.setArrCityName(arrCityName);
        return upCouponPro;
    }

    private UpCouponPro convertUpCouponProductInfo(UpCouponProductInfo productInfo, Map<String, CityInfoDto> cityInfoMap) {
        UpCouponPro upCouponPro = new UpCouponPro();
        upCouponPro.setProductInfo(productInfo);//原始产品信息
        //航线限制处理
        List<List<String>> routeList = RightCouponConvert.applyOtherAirLine(productInfo.getBookingLimit(), cityInfoMap);
        upCouponPro.setOtherApplyRoutes(routeList);
        ResourceInfo resourceInfo = productInfo.getResourceList().get(0);
        upCouponPro.setTags(StringUtil.isNullOrEmpty(resourceInfo.getTags()) ? new ArrayList<>() : resourceInfo.getTags());
        upCouponPro.setImgs(StringUtil.isNullOrEmpty(resourceInfo.getImgs()) ? new ArrayList<>() : resourceInfo.getImgs());
        upCouponPro.setRightValidityDays(productInfo.getVaildityDays());
        upCouponPro.setStandardPrice(resourceInfo.getStandardPrice());
        upCouponPro.setDescription(resourceInfo.getDescription());
        upCouponPro.setUseMode(resourceInfo.getUseMode());
        upCouponPro.setPurchaseNotes(resourceInfo.getPurchaseNotes());
        upCouponPro.setRefundRules(resourceInfo.getRefundRules());
        Date curDate = new Date();
        upCouponPro.setStartDate(DateUtils.convertDateToString(curDate, "yyyy-MM-dd"));
        if (productInfo.getVaildityDays() > 0) {
            upCouponPro.setEndDate(DateUtils.convertDateToString(DateUtils.addOrLessDay(curDate, productInfo.getVaildityDays()), "yyyy-MM-dd"));
        } else {
            upCouponPro.setEndDate(productInfo.getVaildityDate());
        }
        return upCouponPro;
    }

    /**
     * 产品价格排序
     *
     * @param productList
     */
    private void sortProductByPrice(List<UpCouponProductInfo> productList) {
        Collections.sort(productList, Comparator.comparingDouble(a -> a.getMinPrice()));
    }

    /**
     * 下单请求参数
     *
     * @param buyCouponOrder
     * @param channelCode
     * @param userNo
     * @return
     */
    private CouponProductBuyRequest createCouponProBuyReq(BuyCouponOrder buyCouponOrder, String channelCode, String userNo) {
        UpCouponProductInfo productInfo = buyCouponOrder.getProductInfo();
        CouponProductBuyRequest couponProductBuyRequest = new CouponProductBuyRequest(HandlerConstants.VERSION, channelCode, userNo);
        couponProductBuyRequest.setChannelOrderNo(buyCouponOrder.getChannelOrderNo());
        couponProductBuyRequest.setFfpId(buyCouponOrder.getFfpId());
        couponProductBuyRequest.setFfpCardNo(buyCouponOrder.getFfpCardNo());
        couponProductBuyRequest.setBuyType("yes".equalsIgnoreCase(productInfo.getUnLimitedUpgrade()) ? VoucherTypesEnum.UPGRADEUNLIMITED.getCode() : "");
        BookProductInfo bookProductInfo = new BookProductInfo();
        bookProductInfo.setProductId(productInfo.getProductId());
        bookProductInfo.setProductType(productInfo.getProductType());
        bookProductInfo.setProductName(productInfo.getProductName());
        int size = productInfo.getResourceList().size();
        BookResourceInfo[] bookResourceInfos = new BookResourceInfo[size];
        BigDecimal total;
        for (int i = 0; i < size; i++) {
            ResourceInfo resourceInfo = productInfo.getResourceList().get(i);
            BookResourceInfo bookResourceInfo = new BookResourceInfo();
            bookResourceInfo.setResourceId(resourceInfo.getResourceId());
            bookResourceInfo.setResourceType(resourceInfo.getResourceType());
            //价格明细
            ResourcePriceDetail resourcePriceDetail = new ResourcePriceDetail();
            resourcePriceDetail.setPriceType("Unit");
            resourcePriceDetail.setPriceUnit("Person");
            resourcePriceDetail.setSalePrice(productInfo.getMinPrice());//销售价
            resourcePriceDetail.setUnitNumber(1.0);
            resourcePriceDetail.setBookingCount(buyCouponOrder.getSaleCount());
            ResourcePriceDetail[] resourcePriceDetails = new ResourcePriceDetail[1];
            resourcePriceDetails[0] = resourcePriceDetail;
            bookResourceInfo.setResourcePriceDetails(resourcePriceDetails);
            Date curDate = new Date();
            Date diffDate = DateUtils.dateAddOrLessSecond(curDate, 60 * 3);  //防止时间误差，有效期往后推迟三分钟
            bookResourceInfo.setUseStartDate(DateUtils.convertDateToString(diffDate, DateUtils.YYYY_MM_DD_HH_MM_SS_PATTERN));
            if (productInfo.getVaildityDays() > 0) {
                bookResourceInfo.setUseEndDate(DateUtils.convertDateToString(DateUtils.addOrLessDay(diffDate, productInfo.getVaildityDays()), DateUtils.YYYY_MM_DD_HH_MM_SS_PATTERN));
            } else {
                if (StringUtils.isNotBlank(productInfo.getVaildityDate())) {
                    bookResourceInfo.setUseEndDate(productInfo.getVaildityDate() + " 23:59:59");
                }
            }
            bookResourceInfo.setDepAirportCode(productInfo.getDepCityCode());//城市三字码
            bookResourceInfo.setArrAirportCode(productInfo.getArrCityCode());////城市三字码
            bookResourceInfos[i] = bookResourceInfo;
        }
        total = BigDecimal.valueOf(productInfo.getMinPrice()).multiply(BigDecimal.valueOf(buyCouponOrder.getSaleCount()));
        BigDecimal actualPaid = total.subtract(BigDecimal.valueOf(buyCouponOrder.getScore()));
        bookProductInfo.setResources(bookResourceInfos);
        List<BookProductInfo> productInfoList = new ArrayList<>();
        productInfoList.add(bookProductInfo);
        couponProductBuyRequest.setProducts(productInfoList);
        couponProductBuyRequest.setTotalPrice(total);
        couponProductBuyRequest.setPayAmount(actualPaid);
        couponProductBuyRequest.setUseScore(buyCouponOrder.getScore());
        couponProductBuyRequest.setLinkerMobile(buyCouponOrder.getMobilePhone());
        couponProductBuyRequest.setLinker("");
        //优惠券邀请码
        couponProductBuyRequest.setInvitationCode(buyCouponOrder.getInvitationCode());
        return couponProductBuyRequest;
    }

    /**
     * 创建升舱权益券检验请求参数
     *
     * @param upClassBookOrderReq
     * @param channelCode
     * @return
     */
    private PtCouponProductUseCheckRequestDto createPtCouponProductUseCheckRequest(UpClassBookOrderReq upClassBookOrderReq, String channelCode) {
        PtCouponProductUseCheckRequestDto ptCouponProductUseCheckRequestDto = new PtCouponProductUseCheckRequestDto(HandlerConstants.VERSION, channelCode, upClassBookOrderReq.getFfpId(), upClassBookOrderReq.getFfpCardNo(), upClassBookOrderReq.getUpFlightInfo().getUpCouponCode());
        List<PtCouponCheckSegmentInfo> ptCouponCheckSegmentInfoList = new ArrayList<>();
        for (int i = 0; i < upClassBookOrderReq.getUpFlightInfo().getFlightInfoList().size(); i++) {
            com.juneyaoair.baseclass.upclass.common.FlightInfo flightInfo = upClassBookOrderReq.getUpFlightInfo().getFlightInfoList().get(i);
            PtCouponCheckSegmentInfo ptCouponCheckSegmentInfo = new PtCouponCheckSegmentInfo();
            ptCouponCheckSegmentInfo.setSegNO(i);
            ptCouponCheckSegmentInfo.setFlightDirection(flightInfo.getFlightDirection());
            ptCouponCheckSegmentInfo.setFlightNo(flightInfo.getFlightNo());
            ptCouponCheckSegmentInfo.setDepDateTime(flightInfo.getDepDateTime());
            ptCouponCheckSegmentInfo.setArrDateTime(flightInfo.getArrDateTime());
            ptCouponCheckSegmentInfo.setDepAirport(flightInfo.getDepAirport());
            ptCouponCheckSegmentInfo.setArrAirport(flightInfo.getArrAirport());
            ptCouponCheckSegmentInfo.setCabin(flightInfo.getCabinCode());
            ptCouponCheckSegmentInfoList.add(ptCouponCheckSegmentInfo);
        }
        ptCouponProductUseCheckRequestDto.setSegmentInfoList(ptCouponCheckSegmentInfoList);
        ptCouponProductUseCheckRequestDto.setVoucherNo(upClassBookOrderReq.getUpFlightInfo().getUpCouponCode());
        return ptCouponProductUseCheckRequestDto;
    }

    @ApiOperation(value = "查询可售改期券产品", notes = "查询可售改期券产品")
    @RequestMapping(value = "queryChangeCouponPro", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp queryChangeCouponPro(@RequestBody BaseReq<CouponPro> req, HttpServletRequest request) {
        BaseResp resp = new BaseResp();
        String reqId = StringUtil.newGUID() + request.getRequestURI();
        String ip = this.getClientIP(request);
        try {
            this.checkRequest(req);
            String ver = "v2";
            CouponPro couponPro = req.getRequest();
            String channelCode = req.getChannelCode();
            //微信渠道暂时无产品查询时，查询时变换为MOBILE
            if (ChannelCodeEnum.WEIXIN.getChannelCode().equals(req.getChannelCode())) {
                channelCode = ChannelCodeEnum.MOBILE.getChannelCode();
            }
            String userNo = this.getChannelInfo(channelCode, "10");
            CouponProductQueryRequestDto couponProductQueryRequestDto = new CouponProductQueryRequestDto("RescheduleCoupon", HandlerConstants.VERSION, channelCode, userNo, 1, 10);
            if (!StringUtil.isNullOrEmpty(couponPro.getFfpId())) {
                //验证用户查询是否正常
                boolean flag = this.checkKeyInfo(couponPro.getFfpId(), couponPro.getLoginKeyInfo(), req.getChannelCode());
                if (!flag) {
                    resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                    resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                    return resp;
                }
                couponProductQueryRequestDto.setFfpId(couponPro.getFfpId());
                couponProductQueryRequestDto.setFfpCardNo(couponPro.getFfpCardNo());
            }
            dealCouponResp(couponPro, couponProductQueryRequestDto, ip, resp, ver);
        } catch (Exception e) {
            this.logError(resp, reqId, ip, req, e, "查询可售改期券产品出现异常");
        }
        return resp;
    }

    private void dealCouponResp(CouponPro couponPro, CouponProductQueryRequestDto couponProductQueryRequestDto, String ip, BaseResp resp, String ver) {
        Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
        couponProductQueryRequestDto.setSingleBookCondition(couponProductQueryRequestDto.createSingleBookCondition(couponPro.getDepCityCode(), couponPro.getArrCityCode()));
        couponProductQueryRequestDto.setSortCondition(couponProductQueryRequestDto.createDefaultSortCondition());
        HttpResult result = this.doPostClient(couponProductQueryRequestDto, HandlerConstants.URL_FARE_API + HandlerConstants.COUPON_QUERY_PRODUCT_V2, headMap, handConfig.getReadTimeout(), handConfig.getConnectTimeout());
        if (result.isResult()) {
            if (!StringUtil.isNullOrEmpty(result.getResponse())) {
                PtCouponProductQueryResponseDto ptCouponProductQueryResponseDto = (PtCouponProductQueryResponseDto) JsonUtil.jsonToBean(result.getResponse(), PtCouponProductQueryResponseDto.class);
                if (UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(ptCouponProductQueryResponseDto.getResultCode())) {
                    if (StringUtil.isNullOrEmpty(ptCouponProductQueryResponseDto.getProductList())) {
                        resp.setResultCode(WSEnum.ERROR.getResultCode());
                        resp.setResultInfo("暂无可售产品！");
                    } else {
                        Map<String, CityInfoDto> cityInfoMap = basicService.queryAllCityMap(couponProductQueryRequestDto.getChannelCode(), ip);
                        if ("v1".equals(ver)) {
                            UpCouponPro upCouponPro = formatCouponPro(ptCouponProductQueryResponseDto.getProductList(), cityInfoMap,
                                    couponPro.getDepCityCode(), couponPro.getArrCityCode(), VoucherTypesEnum.RESCHEDULECOUPON.getCode());
                            resp.setObjData(upCouponPro);
                            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                            resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                        }
                        if ("v2".equals(ver)) {
                            Map<String, List<UpCouponPro>> upProMap = formatCouponProV2(ptCouponProductQueryResponseDto.getProductList(),
                                    cityInfoMap, couponPro.getCouponType());
                            List<UpCouponPro> upCouponProListD = upProMap.get("domestic");
                            List<UpCouponPro> upCouponProListI = upProMap.get("region");
                            List<UpCouponPro> upCouponProListU = upProMap.get("productList");
                            if (1 == couponPro.getCouponType()) {
                                if (CollectionUtils.isEmpty(upCouponProListU)) {
                                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                                    resp.setResultInfo("您选择的航线改期券已售罄，您可补差价改期~");
                                } else {
                                    resp.setObjData(upProMap);
                                    resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                                    resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                                }
                            } else {
                                if (CollectionUtils.isEmpty(upCouponProListD) && CollectionUtils.isEmpty(upCouponProListI)) {
                                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                                    resp.setResultInfo("您选择的航线改期券已售罄，您可补差价改期~");
                                } else {
                                    resp.setObjData(upProMap);
                                    resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                                    resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                                }
                            }
                        }


                    }
                } else if (UnifiedOrderResultEnum.OUT_OF_STOCK.getResultCode().equals(ptCouponProductQueryResponseDto.getResultCode())) {
                    //库存不足处理
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo("您选择的航线改期券已售罄，请换其它方式~");
                } else {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo("已售罄");
                }
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("产品信息返回数据空！");
            }
        }
    }

    @RequestMapping(value = "/bookChangeCoupon", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @ApiOperation(value = "购买改期券", notes = "购买改期券")
    @NotDuplicate
    public BaseResp recordQuery(@RequestBody BaseReq<BuyCouponOrder> req, HttpServletRequest request) {
        long t1 = System.currentTimeMillis();
        BaseResp<Object> resp = new BaseResp<>();
        String ip = this.getClientIP(request);
        String reqId = StringUtil.newGUID() + request.getRequestURI();
        try {
            //校验参数
            this.checkRequest(req);
            //判断 invitationCode  长度不能大于50，并且校验输入格式数字和字母
            String invitationCode = req.getRequest().getInvitationCode();
            this.checkInvitationCode(resp, invitationCode);
            if ("10003".equals(resp.getResultCode())) {
                return resp;
            }
            //使用积分
            BuyCouponOrder buyCouponOrder = req.getRequest();
            UpCouponProductInfo productInfo = buyCouponOrder.getProductInfo();
            BigDecimal totalPrice = BigDecimal.valueOf(productInfo.getMinPrice()).multiply(BigDecimal.valueOf(buyCouponOrder.getSaleCount()));
            if (buyCouponOrder.getScore() > 0) {
                //积分金额大于订单的总金额
                if (buyCouponOrder.getScore() > totalPrice.doubleValue()) {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo("积分金额大于订单总金额");
                    String reqStr = JsonMapper.buildNormalMapper().toJson(req);
                    String respStr = JsonUtil.objectToJson(resp);
                    log.info(LOG_RESP, reqId, ip, reqStr, System.currentTimeMillis() - t1, respStr);
                    return resp;
                }
                //验证消费密码
                resp = orderService.checkFreeScoreLimit(buyCouponOrder.getFfpCardNo(), buyCouponOrder.getFfpId(), req.getChannelCode(),
                        getChannelInfo(req.getChannelCode(), "40"), buyCouponOrder.getScore(), buyCouponOrder.getSalePwd(), request);
                if (!WSEnum.SUCCESS.getResultCode().equals(resp.getResultCode())) {
                    return resp;
                }
            }
            //转换请求参数
            BaseCouponOrderRequestDto<BoardingCheckBuyRequestDto> requestDto = genBaseRequest(req, request);
            BoardingCheckBuyRequestDto buyRequestDto = new BoardingCheckBuyRequestDto();
            requestDto.setRequest(buyRequestDto);
            BookProductInfo bookProductInfo = new BookProductInfo();
            buyRequestDto.setBookProductInfo(bookProductInfo);
            buyRequestDto.setPhoneNo(req.getRequest().getMobilePhone());
            buyRequestDto.setChannelOrderNo(req.getRequest().getChannelOrderNo());
            buyRequestDto.setUseScore(buyCouponOrder.getScore() + "");
            buyRequestDto.setDepAirportCode(productInfo.getDepCityCode());
            buyRequestDto.setArrAirportCode(productInfo.getArrCityCode());
            buyRequestDto.setCurrency("CNY");
            //优惠券邀请码
            buyRequestDto.setInvitationCode(buyCouponOrder.getInvitationCode());
            bookProductInfo.setProductId(productInfo.getProductId());
            bookProductInfo.setProductType(productInfo.getProductType());
            bookProductInfo.setProductName(productInfo.getProductName());
            int size = productInfo.getResourceList().size();
            BookResourceInfo[] bookResourceInfos = new BookResourceInfo[size];
            for (int i = 0; i < size; i++) {
                ResourceInfo resourceInfo = productInfo.getResourceList().get(i);
                BookResourceInfo bookResourceInfo = new BookResourceInfo();
                bookResourceInfo.setResourceId(resourceInfo.getResourceId());
                bookResourceInfo.setResourceType(resourceInfo.getResourceType());
                //价格明细
                ResourcePriceDetail resourcePriceDetail = new ResourcePriceDetail();
                resourcePriceDetail.setPriceType("Unit");
                resourcePriceDetail.setPriceUnit("Person");
                //销售价，单价由元转化为分
                resourcePriceDetail.setSalePrice(BigDecimal.valueOf(productInfo.getMinPrice()).multiply(BigDecimal.valueOf(100)).doubleValue());
                resourcePriceDetail.setUnitNumber(1.0);
                resourcePriceDetail.setBookingCount(buyCouponOrder.getSaleCount());
                ResourcePriceDetail[] resourcePriceDetails = new ResourcePriceDetail[1];
                resourcePriceDetails[0] = resourcePriceDetail;
                bookResourceInfo.setResourcePriceDetails(resourcePriceDetails);
                Date curDate = new Date();
                Date diffDate = DateUtils.dateAddOrLessSecond(curDate, 60 * 3);  //防止时间误差，有效期往后推迟三分钟
                bookResourceInfo.setUseStartDate(DateUtils.convertDateToString(diffDate, DateUtils.YYYY_MM_DD_HH_MM_SS_PATTERN));
                bookResourceInfo.setUseEndDate(DateUtils.convertDateToString(DateUtils.addOrLessDay(diffDate, productInfo.getVaildityDays()), DateUtils.YYYY_MM_DD_HH_MM_SS_PATTERN));
                bookResourceInfo.setDepAirportCode(productInfo.getDepCityCode());//城市三字码
                bookResourceInfo.setArrAirportCode(productInfo.getArrCityCode());////城市三字码
                bookResourceInfos[i] = bookResourceInfo;
            }
            // 单位：分
            BigDecimal actualPaid = totalPrice.subtract(BigDecimal.valueOf(buyCouponOrder.getScore()))
                    .multiply(BigDecimal.valueOf(100)).setScale(0, BigDecimal.ROUND_HALF_UP);
            bookProductInfo.setResources(bookResourceInfos);
            buyRequestDto.setTotalAmount(actualPaid.intValue() + "");
            Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
            HttpResult result = this.doPostClient(requestDto, HandlerConstants.URL_FARE_API + HandlerConstants.CHANGE_COUPON_CREATE_ORDER, headMap);
            if (!result.isResult() || StringUtils.isBlank(result.getResponse())) {
                throw new OperationFailedException(NETWORK_ERROR_MESSAGE);
            }
            BaseCouponOrderResponseDto<BaseCouponOrderIdentity> responseDto = JsonUtil.fromJson(result.getResponse(),
                    new TypeToken<BaseCouponOrderResponseDto<BaseCouponOrderIdentity>>() {
                    }.getType());
            if (UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(responseDto.getResultCode())
                    && responseDto.getResult() != null) {
                Map<String, Object> orderInfo = new HashMap<>();
                orderInfo.put("orderNo", responseDto.getResult().getOrderNo());
                orderInfo.put("channelOrderNo", responseDto.getResult().getOrderChannelOrderNo());
                orderInfo.put("payState", false);
                //0元同步支付
                if (buyCouponOrder.getScore() > 0 && actualPaid.doubleValue() == 0) {
                    String key = getChannelInfo(req.getChannelCode(), "20");

                    String postUrl = HandlerConstants.URL_PAY;
                    Map<String, String> parametersMap = VirtualPaymentConvert.payment0(req.getChannelCode(),
                            responseDto.getResult().getOrderNo(), responseDto.getResult().getOrderChannelOrderNo(), key,
                            "CouponType", "", "O");
                    parametersMap.put("UseScore", String.valueOf(buyCouponOrder.getScore()));
                    log.info("请求号:{}，IP地址:{}，0元支付请求：{}", reqId, ip, JsonUtil.objectToJson(parametersMap));
                    long timestamp = System.currentTimeMillis();
                    HttpResult payResult = doPayPost(postUrl, parametersMap);
                    log.info("请求号:{}，IP地址:{}，耗时：{}毫秒, 0元支付结果：{}", reqId, ip, System.currentTimeMillis() - timestamp, payResult.getResponse());
                    PaymentResp paymentResp;
                    if (payResult.isResult()) {
                        String paymentInfo = payResult.getResponse().trim();
                        paymentResp = (PaymentResp) JsonUtil.jsonToBean(paymentInfo, PaymentResp.class);
                        //虚拟支付成功
                        if (paymentResp != null && "1001".equals(paymentResp.getRespCode())) {
                            orderInfo.put("payState", true);
                            resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                        } else {
                            resp.setResultCode(WSEnum.ERROR.getResultCode());
                            resp.setResultInfo("权益券购买支付失败！");
                        }
                    } else {
                        resp.setResultCode(WSEnum.ERROR.getResultCode());
                        resp.setResultInfo("支付请求出错");
                    }
                }
                resp.setObjData(orderInfo);
                resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                return resp;
            } else {
                throw new OperationFailedException(responseDto.getErrorInfo());
            }
        } catch (Exception e) {
            this.logError(resp, reqId, ip, req, e);
        }
        return resp;
    }

    /**
     * 查询可用改期券
     *
     * @param req
     * @param request
     * @return
     */
    @InterfaceLog
    @RequestMapping(value = "queryAvailChangeCoupon", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @ApiOperation(value = "查询可用改期券", notes = "查询可用改期券")
    public BaseResp queryAvailChangeCoupon(@RequestBody BaseReq<QueryAvailChangeCouponRequest> req, HttpServletRequest request) {
        BaseResp<List<ChangeAvailableCoupon>> resp = new BaseResp<>();
        String ip = this.getClientIP(request);
        String reqId = MdcUtils.getRequestId();
        QueryAvailChangeCouponRequest couponRequest = req.getRequest();
        try {
            //校验参数
            this.checkRequest(req);
            for(ChangePassengerInfo changePassengerInfo:couponRequest.getPassengerInfoList()){
                String tktNo = changePassengerInfo.getTicketNo();
                if (StringUtils.isBlank(tktNo)) {
                    tktNo = changePassengerInfo.getETicketNo();
                }
                String info = apiRedisService.getData(RedisKeyConfig.createUpIdInfo(tktNo));
                if (StringUtils.isBlank(info)) {
                    throw new ServiceException("操作超时，请返回重新查询下单");
                }
                changePassengerInfo.setCertNo(info);
            }
            String channelCode = req.getChannelCode();
            PtCouponProductGetRequestDto requestDto = ChangeObjectConvert.createProductGetRequestDto(couponRequest, channelCode);
            Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
            HttpResult httpResult = this.doPostClient(requestDto, HandlerConstants.URL_FARE_API + HandlerConstants.COUPON_MY_PRODUCT_V2, headMap);
            if (!httpResult.isResult() || StringUtils.isBlank(httpResult.getResponse())) {
                throw new OperationFailedException(NETWORK_ERROR_MESSAGE);
            }
            //获取会员详情
            String[] items = {MemberDetailRequestItemsEnum.CERTIFICATEINFO.eName, MemberDetailRequestItemsEnum.BASICINFO.eName,
                    MemberDetailRequestItemsEnum.ADDRESSINFOS.eName, MemberDetailRequestItemsEnum.CONTACTINFO.eName};
            PtMemberDetailRequest ptMemberDetailRequest = new PtMemberDetailRequest();
            ptMemberDetailRequest.setRequestItems(items);
            Header header = buildHeader(request, req.getRequest().getFfpId(), "");
            PtApiCRMRequest ptApiCRMRequest = new PtApiCRMRequest();
            ptApiCRMRequest.setHeader(header);
            ptApiCRMRequest.setChannel(channelCode);
            ptApiCRMRequest.setChannelPwd(getChannelInfo(channelCode, "40"));
            ptApiCRMRequest.setData(ptMemberDetailRequest);
            PtCRMResponse<PtMemberDetail> ptCRMResponse = memberService.memberDetail(ptApiCRMRequest);
            PtCouponProductGetResponseDto ptCouponProductGetResponseDto = (PtCouponProductGetResponseDto) JsonUtil.jsonToBean(httpResult.getResponse(), PtCouponProductGetResponseDto.class);
            if (UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(ptCouponProductGetResponseDto.getResultCode())) {
                if(ptCRMResponse.getCode() == 0){
                    Map<String, CityInfoDto> cityInfoMap = basicService.queryAllCityMap(channelCode, ip);
                    Map<String, AirPortInfoDto> airPortInfoMap = basicService.queryAllAirportMap(channelCode, ip);
                    List<AvailCoupon> availCouponList = RightCouponConvert.formatAvailCouponList(CouponStateEnum.R.getState(), ptCouponProductGetResponseDto.getVouchers(), cityInfoMap, airPortInfoMap, handConfig);
                    List<ChangeAvailableCoupon> changeAvailableCoupons = Lists.newArrayList();
                    if (CollectionUtils.isNotEmpty(availCouponList)) {
                        for (AvailCoupon availCoupon : availCouponList) {
                            ChangeAvailableCoupon changeAvailableCoupon = new ChangeAvailableCoupon();
                            BeanUtils.copyNotNullProperties(availCoupon, changeAvailableCoupon);
                                changeAvailableCoupon.setUsePassengerSegments(RightCouponConvert.genUsePassengerSegment(availCoupon,
                                        couponRequest.getPassengerInfoList(), couponRequest.getFlightInfoList(), couponRequest.getChangeFlightInfoList(), cityInfoMap, ptCRMResponse));
                                if (CollectionUtils.isNotEmpty(changeAvailableCoupon.getUsePassengerSegments())) {
                                    changeAvailableCoupons.add(changeAvailableCoupon);
                                }
                        }
                    }

                    List<ChangeAvailableCoupon> rescheduleTypeNullAvailableCoupons = Lists.newArrayList();
                    List<ChangeAvailableCoupon> rescheduleTypeNotNullAvailableCoupons = Lists.newArrayList();
                    if (changeAvailableCoupons.size()>0){
                        changeAvailableCoupons.stream().forEach(changeAvailableCoupon -> {
                            //旧的数据中存在RescheduleType为null的情况=
                            //不可用的过滤,不显示
                            if (changeAvailableCoupon.getRescheduleType()==null){
                                rescheduleTypeNullAvailableCoupons.add(changeAvailableCoupon);
                            }else{
                                rescheduleTypeNotNullAvailableCoupons.add(changeAvailableCoupon);
                            }

                        });
                    }
                    if (rescheduleTypeNotNullAvailableCoupons.size()>0){
                         //通用改期券
                        List<ChangeAvailableCoupon>   universalCoupons =Lists.newArrayList();
                        //限时改期券
                        List<ChangeAvailableCoupon>   limitedTimeCoupons=Lists.newArrayList();
                        List<ChangeAvailableCoupon>   newlimitedTimeCoupons=Lists.newArrayList();
                        List<ChangeAvailableCoupon>   newuniversalCoupons=Lists.newArrayList();
                        //区分通用改期券和限时改期券
                        rescheduleTypeNotNullAvailableCoupons.stream().forEach(changeAvailableCoupon -> {
                            if ("1".equals(changeAvailableCoupon.getRescheduleType())){
                                limitedTimeCoupons.add(changeAvailableCoupon);
                            }
                            if ("0".equals(changeAvailableCoupon.getRescheduleType())){
                                universalCoupons.add(changeAvailableCoupon);
                            }
                        });
                        //判断限时改期券到期时间小于或等于5个自然日
                        if (limitedTimeCoupons.size()>0){
                            limitedTimeCoupons.forEach(changeAvailableCoupon -> {
                                if (DateUtils.dateDiff(DateUtils.toDate(changeAvailableCoupon.getEndData(),"yyyy-MM-dd"),new Date())<=5) {
                                    changeAvailableCoupon.setExpiration(true);
                                }else {
                                    changeAvailableCoupon.setExpiration(false);
                                }
                            });

                            newlimitedTimeCoupons= limitedTimeCoupons.stream().sorted(Comparator.comparing(ChangeAvailableCoupon::getEndData)).collect(Collectors.toList());
                        }
                        //判断通用改期券到期时间小于或等于5个自然日
                       if (universalCoupons.size()>0){
                           universalCoupons.forEach(changeAvailableCoupon -> {
                               if (DateUtils.dateDiff(DateUtils.toDate(changeAvailableCoupon.getEndData(),"yyyy-MM-dd"),new Date())<=5) {
                                   changeAvailableCoupon.setExpiration(true);
                               }else {
                                   changeAvailableCoupon.setExpiration(false);
                               }
                           });

                           newuniversalCoupons= universalCoupons.stream().sorted(Comparator.comparing(ChangeAvailableCoupon::getEndData)).collect(Collectors.toList());
                       }

                       if (newuniversalCoupons.size()>0&&newuniversalCoupons!=null){
                            newlimitedTimeCoupons.addAll(newuniversalCoupons);

                       }
                        if (rescheduleTypeNullAvailableCoupons.size()>0){
                            newlimitedTimeCoupons.addAll(rescheduleTypeNullAvailableCoupons);
                        }
                        resp.setObjData(newlimitedTimeCoupons);
                    }else {
                        resp.setObjData(changeAvailableCoupons);
                    }

                    resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                    resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                }else{
                    throw new OperationFailedException("查询会员信息出错");
                }
            } else {
                throw new OperationFailedException(ptCouponProductGetResponseDto.getErrorInfo());
            }
        } catch (Exception e) {
            this.logError(resp, reqId, ip, req, e);
        }
        return resp;
    }

    private BaseCouponOrderRequestDto genBaseRequest(BaseReq<? extends UserInfoMust> req, HttpServletRequest request) {
        BaseCouponOrderRequestDto<?> requestDto = new BaseCouponOrderRequestDto<>();
        requestDto.setChannelCode(req.getChannelCode());
        requestDto.setFfpCardNo(req.getRequest().getFfpCardNo());
        requestDto.setFfpId(req.getRequest().getFfpId());
        requestDto.setRequestIp(this.getClientIP(request));
        requestDto.setVersion("10");
        requestDto.setUserNo(this.getChannelInfo(req.getChannelCode(), "10"));
        return requestDto;
    }

    //根据券码查询优惠券状态信息
    @RequestMapping(value = "/queryCouponByCode", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @ApiOperation(value = "根据券码查询优惠券状态信息", notes = "根据券码查询优惠券状态信息")
    @ResponseBody
    public Object queryCouponByCode(@RequestBody QueryConponByCodeRequest queryConponByCodeRequest, HttpServletRequest request) {
        CouponByCodeResponse resp = new CouponByCodeResponse();
        String reqId = StringUtil.newGUID() + "_queryCouponByCode";
        String ip = this.getClientIP(request);
        String respStr = JsonUtil.objectToJson(queryConponByCodeRequest);
        log.info("请求号:{}，IP地址:{}，客户端提交参数:{}", reqId, ip, respStr);
        //参数检验
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<QueryConponByCodeRequest>> violations = validator.validate(queryConponByCodeRequest);
        if (null != violations && violations.size() > 0) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(violations.iterator().next().getMessage());
            return resp;
        }
        //签名检验
        String source = queryConponByCodeRequest.getCouponCode() + queryConponByCodeRequest.getTimestamp() + HandlerConstants.ACCESSSECRET;
        String code = EncoderHandler.encodeByMD5(source);
        if (!queryConponByCodeRequest.getSign().equals(code)) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo("信息验证错误");
            return resp;
        }
        if (ChannelCodeEnum.MWEB.getChannelCode().equals(queryConponByCodeRequest.getChannelCode())) {
            queryConponByCodeRequest.setChannelCode(ChannelCodeEnum.MOBILE.getChannelCode());
        }
        String channelCode = queryConponByCodeRequest.getChannelCode();
        String userNo = getChannelInfo(channelCode, "10");
        PtQueryConponByCodeRequest ptQueryConponByCodeRequest = CouponObjectConvert.toPtQueryConponByCodeRequest(queryConponByCodeRequest, userNo);
        HttpResult serviceResult = doPost(ptQueryConponByCodeRequest, HandlerConstants.URL_FARE + HandlerConstants.QUERY_COUPON_BY_CODE);
        if (null != serviceResult && serviceResult.isResult()) {
            try {
                resp = (CouponByCodeResponse) JsonUtil.jsonToBean(serviceResult.getResponse(), CouponByCodeResponse.class);
                resp.setReqId(reqId);
                if (resp.getResultCode().equals("1001") && !StringUtil.isNullOrEmpty(resp.getCouponList())) {
                    resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                    resp.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
                } else {
                    resp.setResultCode(WSEnum.NO_DATA.getResultCode());
                    resp.setErrorInfo(WSEnum.NO_DATA.getResultInfo());
                }
                log.info("请求号:{}，响应结果:{}", reqId, JsonUtil.objectToJson(resp));
                return resp;
            } catch (Exception e) {
                log.error("请求号:{}，查询出错:{}", reqId, e.getMessage());
                resp.setReqId(reqId);
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo("查询返回结果空");
                return resp;
            }
        } else {
            resp.setReqId(reqId);
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo("查询网络出错");
            return resp;
        }
    }

    //根据券码查询券信息
    @ResponseBody
    @RequestMapping(value = "queryRightCouponByCode", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @ApiOperation(value = "根据券码查询券信息", notes = "根据券码查询券信息")
    public CouponByCodeResponse queryRightCouponByCode(@RequestBody QueryConponByCodeRequest queryConponByCodeRequest, HttpServletRequest request) {
        CouponByCodeResponse resp = new CouponByCodeResponse();
        String reqId = StringUtil.newGUID() + request.getRequestURI();
        String ip = this.getClientIP(request);
        String respStr = JsonUtil.objectToJson(queryConponByCodeRequest);
        log.info("请求号:{}，IP地址:{}，客户端提交参数:{}", reqId, ip, respStr);
        //参数检验
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<QueryConponByCodeRequest>> violations = validator.validate(queryConponByCodeRequest);
        if (null != violations && violations.size() > 0) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(violations.iterator().next().getMessage());
            return resp;
        }
        //签名检验
        String source = queryConponByCodeRequest.getCouponCode() + queryConponByCodeRequest.getTimestamp() + HandlerConstants.ACCESSSECRET;
        String code = EncoderHandler.encodeByMD5(source);
        if (!queryConponByCodeRequest.getSign().equals(code)) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo("信息验证错误");
            return resp;
        }
        if (ChannelCodeEnum.MWEB.getChannelCode().equals(queryConponByCodeRequest.getChannelCode())) {
            queryConponByCodeRequest.setChannelCode(ChannelCodeEnum.MOBILE.getChannelCode());
        }
        PtCouponProductGetRequestDto ptCouponProductGetRequestDto = createCouponProductGetRequestDto(queryConponByCodeRequest, queryConponByCodeRequest.getChannelCode());
        String url = HandlerConstants.URL_FARE_API + HandlerConstants.COUPON_MY_PRODUCT_V2;
        Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
        HttpResult result = this.doPostClient(ptCouponProductGetRequestDto, url, headMap);
        if (result.isResult()) {
            PtCouponProductGetResponseDto ptCouponProductGetResponseDto = (PtCouponProductGetResponseDto) JsonUtil.jsonToBean(result.getResponse(), PtCouponProductGetResponseDto.class);
            if (UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(ptCouponProductGetResponseDto.getResultCode())) {
                Map<String, CityInfoDto> cityInfoMap = basicService.queryAllCityMap(queryConponByCodeRequest.getChannelCode(), ip);
                Map<String, AirPortInfoDto> airPortInfoMap = basicService.queryAllAirportMap(queryConponByCodeRequest.getChannelCode(), ip);
                List<CouponByCode> availCouponList = formatAvailCouponList(ptCouponProductGetResponseDto.getVouchers(), cityInfoMap, airPortInfoMap);
                resp.setCouponList(availCouponList);
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo(ptCouponProductGetResponseDto.getErrorInfo());
            }
        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo("返回结果空");
        }
        return resp;
    }

    /**
     * 我的权益券请求参数
     *
     * @param queryConponByCodeRequest
     * @param channelCode
     * @return
     */
    private PtCouponProductGetRequestDto createCouponProductGetRequestDto(QueryConponByCodeRequest queryConponByCodeRequest, String channelCode) {
        PtCouponProductGetRequestDto ptCouponProductGetRequestDto = new PtCouponProductGetRequestDto(HandlerConstants.VERSION, channelCode);
        String[] stateArr = {OrderCouponStateEnum.Giving.getStateCode()};
        List<String> stateList = Arrays.asList(stateArr);
        String[] voucherTypes = {VoucherTypesEnum.UPGRADE.getCode(), VoucherTypesEnum.BAGGAGE.getCode(), VoucherTypesEnum.LOUNGE.getCode()};
        ptCouponProductGetRequestDto.setVoucherTypes(Arrays.asList(voucherTypes));
        ptCouponProductGetRequestDto.setCouponState(stateList);
        List<String> vouchernoList = new ArrayList<>();
        String[] coupons = queryConponByCodeRequest.getCouponCode().split("_");
        for (int i = 0; i < coupons.length; i++) {
            vouchernoList.add(coupons[i]);
        }
        ptCouponProductGetRequestDto.setVoucherNos(vouchernoList);
        ptCouponProductGetRequestDto.setFfpId("");
        ptCouponProductGetRequestDto.setFfpCardNo("");
        ptCouponProductGetRequestDto.setPageNo(1);
        ptCouponProductGetRequestDto.setPageSize(10);
        return ptCouponProductGetRequestDto;
    }

    /**
     * 转换返回结果
     *
     * @param voucherInfoList
     * @param cityInfoMap
     * @param airPortInfoMap
     * @return
     */
    private List<CouponByCode> formatAvailCouponList(List<MainVoucherInfo> voucherInfoList, Map<String, CityInfoDto> cityInfoMap, Map<String, AirPortInfoDto> airPortInfoMap) {
        List<CouponByCode> availCouponList = new ArrayList<>();
        if (StringUtil.isNullOrEmpty(voucherInfoList)) {
            return availCouponList;
        }
        //主券信息
        voucherInfoList.stream().forEach(mainVoucherInfo -> {
            CouponByCode availCoupon = new CouponByCode();
            VoucherInfo voucherInfo = mainVoucherInfo.getVoucherInfos().get(0);//资源信息默认取第一个为主资源信息
            ResourceInfo resourceInfo = voucherInfo.getVoucherDetail();
            availCoupon.setCouponType("");
            availCoupon.setCouponSource(resourceInfo.getResourceType());
            availCoupon.setCouponNo(voucherInfo.getVoucherNo());
            availCoupon.setUseMode(resourceInfo.getUseMode());
            availCoupon.setUsedStEndDt(voucherInfo.getActivateTime() + "至" + voucherInfo.getExpireTime());
            availCoupon.setUsedLimit(voucherInfo.getRuleRemark() == null ? "" : voucherInfo.getRuleRemark());
            availCoupon.setCouponName(resourceInfo.getProductName());
            if (voucherInfo.getVoucherType().matches(PatternCommon.TOTALTYPE)){
            List<Product>  products = themeCardService.selectProduct(availCoupon.getCouponSource());
            if (CollectionUtils.isNotEmpty(products)) {
                String availDatestr = products.get(0).getStartAvailDate() + "至" + products.get(0).getEndAvailDate()+"?(具体使用时间见详细规则)";
                availCoupon.setAvailDate(availDatestr);
            }
                availCoupon.setTotalType(PackageTypeEnum.THEME_COUPON.getPackType());
            }
            //升舱券
            if (VoucherTypesEnum.UPGRADE.getCode().equals(voucherInfo.getVoucherType())) {
                EquityUnitsUsage equityUnitsUsage = voucherInfo.getEquityUnitsUsage();
                if (equityUnitsUsage != null) {
                    if (equityUnitsUsage.getDepCity() != null && equityUnitsUsage.getArrCity() != null) {
                        CityInfoDto depCityInfo = cityInfoMap.get(equityUnitsUsage.getDepCity());
                        CityInfoDto arrCityInfo = cityInfoMap.get(equityUnitsUsage.getArrCity());
                        availCoupon.setCouponName("升舱券—" + depCityInfo.getCityName() + "-" + arrCityInfo.getCityName());
                    }
                }
                availCoupon.setUnLimitedUpgrade(voucherInfo.getUnLimitedUpgrade());
                availCoupon.setLimitBindingDate(voucherInfo.getLimitBindingDate());
                availCoupon.setLimitBindingStatus(voucherInfo.getLimitBindingStatus());
            } else if (VoucherTypesEnum.LOUNGE.getCode().equals(voucherInfo.getVoucherType())) {
                EquityUnitsUsage equityUnitsUsage = voucherInfo.getEquityUnitsUsage();
                if (equityUnitsUsage != null) {
                    if (airPortInfoMap != null && resourceInfo.getLoungeExt() != null) {
                        AirPortInfoDto airPortInfo = airPortInfoMap.get(resourceInfo.getLoungeExt().getAirportCode());
                        availCoupon.setCouponName("贵宾休息室券—" + airPortInfo.getAirPortName());
                    }
                }
            } else if (VoucherTypesEnum.BAGGAGE.getCode().equals(voucherInfo.getVoucherType())) {//行李
                availCoupon.setCouponName("逾重行李券");
                BaggageExtInfo baggageExtInfo = resourceInfo.getBaggageExt();
                if (baggageExtInfo != null) {
                    if ("DOMESTIC".equals(baggageExtInfo.getIsIntl())) {
                        availCoupon.setCouponName("逾重行李券—" + "国内(" + baggageExtInfo.getBaggageValue() + baggageExtInfo.getBaggageUnit() + ")");
                    } else if ("INTL".equals(resourceInfo.getBaggageExt().getIsIntl())) {
                        availCoupon.setCouponName("逾重行李券—" + "国际及港澳台(" + baggageExtInfo.getProductName() + ")");
                    } else {
                        availCoupon.setCouponName("逾重行李券");
                    }
                }
            }
            //附赠券信息处理
            if (mainVoucherInfo.getVoucherInfos().size() > 1) {
                //过滤出固包行李信息
                voucherInfo = mainVoucherInfo.getVoucherInfos().stream().filter(vInfo -> VoucherTypesEnum.BAGGAGE.getCode().equals(vInfo.getVoucherType())).findFirst().orElse(null);
                resourceInfo = voucherInfo.getVoucherDetail();
                if (voucherInfo != null) {
                    availCoupon.setCouponSource(VoucherTypesEnum.BAGGAGE.getCode());
                    for (int i = 0; i < mainVoucherInfo.getVoucherInfos().size(); i++) {
                        VoucherInfo v = mainVoucherInfo.getVoucherInfos().get(i);
                        if (voucherInfo.getVoucherNo().equals(v.getVoucherNo())) {
                            continue;
                        }
                        if (VoucherTypesEnum.BAGGAGE.getCode().equals(voucherInfo.getVoucherType())) {
                            BaggageExtInfo baggageExt = resourceInfo.getBaggageExt();
                            if (baggageExt != null) {
                                OnboardWifiExtInfo onboardWifiExt = v.getVoucherDetail().getOnboardWifiExt();
                                if (onboardWifiExt != null) {
                                    availCoupon.setCouponName(baggageExt.getBaggageValue() + baggageExt.getBaggageUnit() + "行李券(赠" + (int) onboardWifiExt.getNetFlow() + "MB机上WiFi)");
                                }
                                break;
                            }
                        }
                    }
                }
            }
            availCouponList.add(availCoupon);
        });
        return availCouponList;
    }

    @ApiOperation(value = "升舱券默认城市对", notes = "升舱券默认城市对")
    @RequestMapping(value = "/upCouponDefCity", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp<CouponDefCity> queryUpCouponDefCity(@RequestBody BaseReq<String> req, HttpServletRequest request) {
        BaseResp<CouponDefCity> resp = new BaseResp();
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<BaseReq>> violations = validator.validate(req);
        if (CollectionUtils.isNotEmpty(violations)) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo(violations.iterator().next().getMessage());
            return resp;
        }
        String queryType = req.getRequest();
        if ("upCoupon".equals(queryType)) {
            //城市对数据
            resp.setObjData(handConfig.getUpCouponDefCity());
        } else if ("changeCoupon".equals(queryType)) {
            //城市对数据
            resp.setObjData(handConfig.getChangeCouponDefCity());
        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR_REQUEST_PARAMS.getResultInfo());
            return resp;
        }
        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
        resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
        return resp;
    }

    @ApiOperation(value = "查询产品信息", notes = "查询产品信息")
    @RequestMapping(value = "/queryProductInfoList", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @InterfaceLog
    public BaseResp<List<ProductInfo>> queryProductInfoList(@RequestBody BaseReq<ProductQuery> req, HttpServletRequest request) {
        BaseResp resp = new BaseResp();
        String headChannelCode = request.getHeader(HandlerConstants.TOKEN_CHANNELCODE);
        String versionCode = request.getHeader(HandlerConstants.CLIENT_VERSION_CODE);
        String reqId = headChannelCode + versionCode + StringUtil.newGUID() + request.getRequestURI();
        String ip = this.getClientIP(request);
        this.validateRequest(req);
        ProductQuery productQuery = req.getRequest();
        if (!StringUtil.isNullOrEmpty(productQuery.getFfpId())) {
            //验证用户查询是否正常
            boolean flag = this.checkKeyInfo(productQuery.getFfpId(), productQuery.getLoginKeyInfo(), req.getChannelCode());
            if (!flag) {
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                return resp;
            }
        }
        String channelCode = req.getChannelCode();
        String userNo = this.getChannelInfo(channelCode, "10");
        Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
        List<ProductInfo> productInfoList = new ArrayList<>();
        UnlimitedCard2Config unlimitedCard2Config = handConfig.getUnlimitedCard2Config();
        if(unlimitedCard2Config.isUseCacheQueryProduct() && (VoucherTypesEnum.UNLIMITED_FLY_V2.getCode().equals(productQuery.getProductType())
                    || VoucherTypesEnum.UNLIMITED_FLY_V2_SF.getCode().equals(productQuery.getProductType()))){
            productInfoList = productCacheService.obtainProductCache(productQuery.getProductType(), req.getChannelCode(), ip);
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
            resp.setObjData(productInfoList);
            return resp;
        }
        PtRequest ptRequest = createProductQuery(channelCode, userNo, productQuery);
        PtResponse<List<ProductInfo>> ptResponse = orderManage.queryProducts(ptRequest, headMap);
        if (UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(ptResponse.getResultCode())) {
            if (CollectionUtils.isNotEmpty(ptResponse.getResult())) {
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                productInfoList = ptResponse.getResult().parallelStream().map(productInfo -> createProductInfoSign(productInfo)).collect(Collectors.toList());
                resp.setObjData(productInfoList);
            } else {
                resp.setResultCode(WSEnum.NO_DATA.getResultCode());
                resp.setResultInfo("暂无可售产品");
            }
        } else {
            if(UnifiedOrderResultEnum.R1005.getResultCode().equals(ptResponse.getResultCode())){
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo(ptResponse.getErrorInfo());
            }else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("小吉正在努力搬砖中，请您稍后再试");
            }
        }
        return resp;
    }

    private ProductInfo createProductInfoSign(ProductInfo productInfo) {
        ResourceBase resourceBase = productInfo.getResourceInfo();
        List<Product> productList = productInfo.getProducts();
        if (CollectionUtils.isNotEmpty(productList)) {
            productList.stream().forEach(product -> {
                product.setAvailDate("");
                product.setUnAvailDate("");
                product.setSign(createProSign(resourceBase, product));
                if(VoucherTypesEnum.UNLIMITED_FLY_V2.getCode().equalsIgnoreCase(product.getProductType())
                        || VoucherTypesEnum.UNLIMITED_FLY_V2_SF.getCode().equalsIgnoreCase(product.getProductType()) ){
                    product.setAvailPrdStockFlag("no".equals(product.getStockFlag()) || ("yes".equals(product.getStockFlag()) && product.getAvailPrdStock() > 0));
                    product.setAvailPrdStock(0);
                    product.setAvailDate(dateLimitFilter(product.getProductRuleLimit().getFlightDate().getSuit(),1));
                    if(VoucherTypesEnum.UNLIMITED_FLY_V2.getCode().equalsIgnoreCase(product.getProductType())){
                        String unAvailDate = dateLimitFilter(product.getProductRuleLimit().getFlightDate().getNotSuit(), 0);
                        product.setUnAvailDate(StringUtils.isNotBlank(unAvailDate) ? "(不含"+unAvailDate+")" :"");
                    }
                }
            });
        }
        return productInfo;
    }

    /**
     * 获取航班日期时间范围
     *
     * @param dateLimits
     * @return
     */
    public static String dateLimitFilter(List<DateLimits> dateLimits, int limitType) {
        if (CollectionUtils.isNotEmpty(dateLimits)) {
            for (DateLimits dateLimit : dateLimits) {
                //可用
                if (limitType == 1) {
                    return dateLimit.getStartDate()+"至"+dateLimit.getEndDate();
                }else if(limitType == 0){
                    //不可用
                    return dateLimit.getStartDate()+"至"+dateLimit.getEndDate();
                }
            }
        }
        return "";
    }

    private String createProSign(ResourceBase resourceBase, Product product) {
        String originalKey = product.createSignField(resourceBase.getResourceId());
        return EncoderHandler.encodeByMD5(originalKey);
    }

    private PtRequest createProductQuery(String channelCode, String userNo, ProductQuery productQuery) {
        PtRequest ptRequest = new PtRequest(HandlerConstants.VERSION, channelCode, userNo);
        ptRequest.setFfpId(productQuery.getFfpId());
        ptRequest.setFfpCardNo(productQuery.getFfpCardNo());
        ProductQueryRequestDto productQueryRequestDto = new ProductQueryRequestDto();
        productQueryRequestDto.setSearchTypes(productQuery.getProductType());
        ptRequest.setRequest(productQueryRequestDto);
        return ptRequest;
    }

    @ApiOperation(value = "创建下单产品信息", notes = "创建下单产品信息")
    @RequestMapping(value = "/bookProOrder", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @InterfaceLog
    public BaseResp<Map<String, Object>> bookProOrder(@RequestBody BaseReq<BuyProOrder> req, HttpServletRequest request) {
        long t1 = System.currentTimeMillis();
        BaseResp resp = new BaseResp();
        String headChannelCode = request.getHeader(HandlerConstants.TOKEN_CHANNELCODE);
        String versionCode = request.getHeader(HandlerConstants.CLIENT_VERSION_CODE);
        String platform = request.getHeader(HandlerConstants.CLIENT_PLATFORM);
        String reqId = headChannelCode + versionCode + StringUtil.newGUID() + request.getRequestURI();
        String ip = this.getClientIP(request);
        String reqJson = JsonMapper.buildNormalMapper().toJson(req);
        this.validateRequest(req);
        BuyProOrder buyProOrder = req.getRequest();
        try {
            if (buyProOrder == null) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo("缺少请求参数");
                String reqStr = JsonMapper.buildNormalMapper().toJson(req);
                String respStr = JsonUtil.objectToJson(resp);
                log.error(LOG_RESP, reqId, ip, reqStr, System.currentTimeMillis() - t1, respStr);
                return resp;
            }
            //判断 invitationCode  长度不能大于50，并且校验输入格式数字和字母
            String invitationCode = req.getRequest().getInvitationCode();
            this.checkInvitationCode(resp, invitationCode);
            if ("10003".equals(resp.getResultCode())) {
                return resp;
            }
            //验证用户查询是否正常
            boolean flag = this.checkKeyInfo(buyProOrder.getFfpId(), buyProOrder.getLoginKeyInfo(), req.getChannelCode());
            if (!flag) {
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                return resp;
            }
            //检验活动开放日期
            ProductInfoBuy productInfoBuy = buyProOrder.getProductInfoBuy();
            Product product = productInfoBuy.getProduct();
            List<ActivityConfig> activityConfigList = handConfig.getUnLimitConfigList();
            if (CollectionUtils.isNotEmpty(activityConfigList)) {
                ActivityConfig activityConfig = activityConfigList.stream().filter(config -> product.getProductNum().equals(config.getActivityId())).findFirst().orElse(null);
                if (activityConfig != null) {
                    Date curDate = new Date();
                    Date startDate = DateUtils.toDate(activityConfig.getActivityStartDate(), DateUtils.YYYY_MM_DD_HH_MM_PATTERN);
                    Date endDate = DateUtils.toDate(activityConfig.getActivityEndDate(), DateUtils.YYYY_MM_DD_HH_MM_PATTERN);
                    if (curDate.before(startDate)) {
                        resp.setResultCode(WSEnum.ERROR.getResultCode());
                        resp.setResultInfo("活动暂未开始，敬请期待！");
                        return resp;
                    }
                    if (curDate.after(endDate)) {
                        resp.setResultCode(WSEnum.ERROR.getResultCode());
                        resp.setResultInfo("活动已结束，期待您下次再来！");
                        return resp;
                    }
                }
            }
            //验证产品信息
            String sign = createProSign(productInfoBuy.getResourceInfo(),product);
            if(!product.getSign().equals(sign)){
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setResultInfo("请重新查询后下单！");
                return resp;
            }
            String channelCode = req.getChannelCode();
            String userNo = getChannelInfo(channelCode, "10");
            String mobile = null;
            String email = null;
            String accountName = null;
            //没有手机号
            if (StringUtils.isBlank(buyProOrder.getMobilePhone())) {
                String[] items = {MemberDetailRequestItemsEnum.CONTACTINFO.eName, MemberDetailRequestItemsEnum.BASICINFO.eName};
                PtApiCRMRequest<PtMemberDetailRequest> ptApiRequest = CRMReqUtil.buildMemberDetailReq(buyProOrder.getFfpCardNo(), buyProOrder.getFfpId(), request, channelCode, items);
                PtCRMResponse<PtMemberDetail> ptCRMResponse = memberService.memberDetail(ptApiRequest);
                if (ptCRMResponse.getCode() == 0) {
                    MemberContactSoaModel memberContactSoaModel = CRMReqUtil.getContactInfo(ptCRMResponse.getData().getContactInfo(), ContactTypeEnum.MOBILE.getCode());
                    if (memberContactSoaModel != null) {
                        buyProOrder.setMobilePhone(memberContactSoaModel.getContactNumber());
                        mobile = memberContactSoaModel.getContactNumber();
                    }
                    MemberContactSoaModel emailInfo = CRMReqUtil.getContactInfo(ptCRMResponse.getData().getContactInfo(), ContactTypeEnum.EMAIL.getCode());
                    if (emailInfo != null) {
                        email = emailInfo.getContactNumber();
                    }
                    MemberBasicInfoSoaModel basicInfo = ptCRMResponse.getData().getBasicInfo();
                    if (null != basicInfo) {
                        accountName = basicInfo.getCLastName() + basicInfo.getEFirstName();
                    }
                } else {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo(ptCRMResponse.getMsg());
                    return resp;
                }
            }
            //同盾参数
            if ("Y".equals(handConfig.getUnlimitedCardTongDun()) && (VoucherTypesEnum.CHILD_UNLIMITED_FLY.getCode().equals(product.getProductType())||VoucherTypesEnum.ADT_UNLIMITED_FLY.getCode().equals(product.getProductType()))) {
                FraudApiResponse fraudApiResponse = FraudApiInvoker.unlimitedUpgradeBuyRiskControl(buyProOrder.getBlackBox(), buyProOrder.getFfpCardNo(), platform, headChannelCode,
                        ip, mobile, email, accountName, product.getProductName(), product.getProductType(), buyProOrder.getSaleCount());
                if (fraudApiResponse.getSuccess() && fraudApiResponse.getFinal_decision().equalsIgnoreCase(FinalDecisionEnum.REJECT.getCode())) {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo("活动太火爆了，请稍后重试");
                    log.info("同盾拦截：用户卡号:{} 同盾响应:{}", buyProOrder.getFfpCardNo(), JsonUtil.objectToJson(fraudApiResponse));
                    return resp;
                }
            }
            //转换请求参数
            PtCreateOrderRequest ptCreateOrderRequest = createOrderParam(buyProOrder);
            PtRequest ptRequest = new PtRequest(HandlerConstants.VERSION, channelCode, userNo);
            ptRequest.setRequestIp(ip);
            ptRequest.setSearchType(productInfoBuy.getProduct().getProductType());
            ptRequest.setRequest(ptCreateOrderRequest);
            //使用积分
            if (buyProOrder.getScore() > 0) {
                //积分金额大于订单的总金额
                if (BigDecimal.ZERO.compareTo(ptCreateOrderRequest.getTotalAmount()) > 0) {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo("积分金额大于订单总金额");
                    String reqStr = JsonMapper.buildNormalMapper().toJson(req);
                    String respStr = JsonUtil.objectToJson(resp);
                    log.info(LOG_RESP, reqId, ip, reqStr, System.currentTimeMillis() - t1, respStr);
                    return resp;
                }
                //验证消费密码
                resp = orderService.checkFreeScoreLimit(buyProOrder.getFfpCardNo(), buyProOrder.getFfpId(), channelCode, getChannelInfo(channelCode, "40"), buyProOrder.getScore(), buyProOrder.getSalePwd(), request);
                if (!WSEnum.SUCCESS.getResultCode().equals(resp.getResultCode())) {
                    return resp;
                }
            }
            Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
            //根据类型动态调整下单链接地址
            String url = HandlerConstants.URL_FARE_API+HandlerConstants.PRODUCT_CREATE;
            url = url.replace("${resourceType}",productInfoBuy.getProduct().getProductType());
            if(VoucherTypesEnum.UNLIMITED_FLY_V2.getCode().equalsIgnoreCase(productInfoBuy.getProduct().getProductType()) || VoucherTypesEnum.UNLIMITED_FLY_V2_SF.getCode().equalsIgnoreCase(productInfoBuy.getProduct().getProductType()) ){
                url = HandlerConstants.URL_FARE_API+HandlerConstants.PRODUCT_CREATE_V10;
            }
            PtResponse<PtBaseCouponOrderIdentity> ptResponse = orderManage.createProductOrder(ptRequest,headMap,url);
            if (UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(ptResponse.getResultCode())) {
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                Map<String, Object> orderInfo = new HashMap<>();
                PtBaseCouponOrderIdentity ptBaseCouponOrderIdentity = ptResponse.getResult();
                orderInfo.put("orderNo", ptBaseCouponOrderIdentity.getOrderNo());
                orderInfo.put("channelOrderNo", ptBaseCouponOrderIdentity.getOrderChannelOrderNo());
                orderInfo.put("payState", false);
                //0元同步支付
                if (buyProOrder.getScore() > 0 && BigDecimal.ZERO.compareTo(ptCreateOrderRequest.getTotalAmount()) == 0) {
                    String key = getChannelInfo(req.getChannelCode(), "20");
                    String postUrl = HandlerConstants.URL_PAY;
                    Map<String, String> parametersMap = VirtualPaymentConvert.payment0(req.getChannelCode(), ptBaseCouponOrderIdentity.getOrderNo(), ptBaseCouponOrderIdentity.getOrderChannelOrderNo(), key, "CouponType", "", "O");
                    parametersMap.put("UseScore", String.valueOf(buyProOrder.getScore()));
                    HttpResult payResult = doPayPost(postUrl, parametersMap);
                    PaymentResp paymentResp;
                    if (payResult.isResult()) {
                        String paymentInfo = payResult.getResponse().trim();
                        log.info("请求号:{}，IP地址:{}，0元支付结果：{}", reqId, ip, paymentInfo);
                        paymentResp = (PaymentResp) JsonUtil.jsonToBean(paymentInfo, PaymentResp.class);
                        //虚拟支付成功
                        if (paymentResp != null && "1001".equals(paymentResp.getRespCode())) {
                            orderInfo.put("payState", true);
                            resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                        } else {
                            resp.setResultCode(WSEnum.ERROR.getResultCode());
                            resp.setResultInfo("权益券购买支付失败！");
                        }
                    } else {
                        resp.setResultCode(WSEnum.ERROR.getResultCode());
                        resp.setResultInfo("支付请求出错");
                    }
                }
                resp.setObjData(orderInfo);
            } else if(UnifiedOrderResultEnum.R1005.getResultCode().equals(ptResponse.getResultCode())){
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo(ptResponse.getErrorInfo());
            }else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("小吉正在努力搬砖中，请您稍后再试");
            }
            return resp;
        } catch (Exception e) {
            this.logError(resp, reqId, ip, req, e, "糟糕，出现意外了");
            return resp;
        }
    }

    private PtCreateOrderRequest createOrderParam(BuyProOrder buyProOrder) {
        ProductInfoBuy productInfoBuy = buyProOrder.getProductInfoBuy();
        ResourceBase resourceInfo = productInfoBuy.getResourceInfo();
        Product product = productInfoBuy.getProduct();
        PtCreateOrderRequest ptCreateOrderRequest = new PtCreateOrderRequest();
        ptCreateOrderRequest.setFfpId(buyProOrder.getFfpId());
        ptCreateOrderRequest.setFfpCardNo(buyProOrder.getFfpCardNo());
        ptCreateOrderRequest.setChannelOrderNo(buyProOrder.getChannelOrderNo());
        //减去积分之后的实际支付金额
        BigDecimal total = BigDecimal.valueOf(product.getSalePrice()).multiply(BigDecimal.valueOf(buyProOrder.getSaleCount()));
        BigDecimal actualPaid = total.subtract(BigDecimal.valueOf(buyProOrder.getScore()));
        ptCreateOrderRequest.setTotalAmount(actualPaid.multiply(BigDecimal.valueOf(100)));
        ptCreateOrderRequest.setCurrency(HandlerConstants.CURRENCY_CODE);
        ptCreateOrderRequest.setPhoneNo(buyProOrder.getMobilePhone());
        ptCreateOrderRequest.setUseScore(buyProOrder.getScore());
        ptCreateOrderRequest.setRefundRule(resourceInfo.getRefundRules());
        ptCreateOrderRequest.setProductName(product.getProductName());
        List<PtBookProductInfo> ptBookProductInfoList = new ArrayList<>();
        PtBookProductInfo ptBookProductInfo = new PtBookProductInfo();
        ptBookProductInfo.setProductId(product.getProductId());
        ptBookProductInfo.setProductSku(product.getProductSku());
        ptBookProductInfo.setBookingId(product.getBookingId());
        ptBookProductInfo.setProductNum(product.getProductNum());
        ptBookProductInfo.setRuleId(product.getRuleId());
        ptBookProductInfo.setResourceType(product.getProductType());
        ptBookProductInfo.setStandardPrice(product.getStandardPrice());
        ptBookProductInfo.setSalePrice(product.getSalePrice());
        ptBookProductInfo.setBookingCount(buyProOrder.getSaleCount());
        ptBookProductInfoList.add(ptBookProductInfo);
        ptCreateOrderRequest.setBookProductInfo(ptBookProductInfoList);
        return ptCreateOrderRequest;
    }

    private PtCreateOrderRequest createOrderParam(BookUnlimitedCardReqDto buyProOrder, ProductInfo productInfo) {
        ResourceBase resourceInfo = productInfo.getResourceInfo();
        Product product = productInfo.getProducts().get(0);
        PtCreateOrderRequest ptCreateOrderRequest = new PtCreateOrderRequest();
        ptCreateOrderRequest.setFfpId(buyProOrder.getFfpId());
        ptCreateOrderRequest.setFfpCardNo(buyProOrder.getFfpCardNo());
        ptCreateOrderRequest.setChannelOrderNo(buyProOrder.getChannelOrderNo());
        //减去积分之后的实际支付金额
        BigDecimal total = BigDecimal.valueOf(product.getSalePrice()).multiply(BigDecimal.valueOf(buyProOrder.getSaleCount()));
        BigDecimal actualPaid = total.subtract(BigDecimal.valueOf(buyProOrder.getScore()));
        ptCreateOrderRequest.setTotalAmount(actualPaid.multiply(BigDecimal.valueOf(100)));
        ptCreateOrderRequest.setCurrency(HandlerConstants.CURRENCY_CODE);
        ptCreateOrderRequest.setPhoneNo(buyProOrder.getMobilePhone());
        ptCreateOrderRequest.setUseScore(buyProOrder.getScore());
        ptCreateOrderRequest.setRefundRule(resourceInfo.getRefundRules());
        ptCreateOrderRequest.setProductName(product.getProductName());
        List<PtBookProductInfo> ptBookProductInfoList = new ArrayList<>();
        PtBookProductInfo ptBookProductInfo = new PtBookProductInfo();
        ptBookProductInfo.setProductId(product.getProductId());
        ptBookProductInfo.setProductSku(product.getProductSku());
        ptBookProductInfo.setBookingId(product.getBookingId());
        ptBookProductInfo.setProductNum(product.getProductNum());
        ptBookProductInfo.setRuleId(product.getRuleId());
        ptBookProductInfo.setResourceType(product.getProductType());
        ptBookProductInfo.setStandardPrice(product.getStandardPrice());
        ptBookProductInfo.setSalePrice(product.getSalePrice());
        ptBookProductInfo.setBookingCount(buyProOrder.getSaleCount());
        ptBookProductInfoList.add(ptBookProductInfo);
        ptCreateOrderRequest.setBookProductInfo(ptBookProductInfoList);
        return ptCreateOrderRequest;
    }

    @ApiOperation(value = "创建下单产品信息", notes = "创建下单产品信息")
    @RequestMapping(value = "/bookUnlimitedCard", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @InterfaceLog
    public BaseResp<Map<String, Object>> bookUnlimitedCard(@RequestBody BaseReq<BookUnlimitedCardReqDto> req, HttpServletRequest request) {
        BaseResp resp = new BaseResp();
        String headChannelCode = request.getHeader(HandlerConstants.TOKEN_CHANNELCODE);
        String versionCode = request.getHeader(HandlerConstants.CLIENT_VERSION_CODE);
        String platform = request.getHeader(HandlerConstants.CLIENT_PLATFORM);
        String reqId = headChannelCode + versionCode + StringUtil.newGUID() + request.getRequestURI();
        String ip = this.getClientIP(request);
        try {
            this.checkRequest(req);
            String invitationCode = req.getRequest().getInvitationCode();
            this.checkInvitationCode(resp, invitationCode);
            if ("10003".equals(resp.getResultCode())) {
                return resp;
            }
            BookUnlimitedCardReqDto bookRequest = req.getRequest();
            if(StringUtils.isNotBlank(bookRequest.getProductType()) && (VoucherTypesEnum.UNLIMITED_FLY_V2.getCode().equals(bookRequest.getProductType())
                    || VoucherTypesEnum.UNLIMITED_FLY_V2_SF.getCode().equals(bookRequest.getProductType()))){
                UnlimitedCard2Config unlimitedCard2Config = handConfig.getUnlimitedCard2Config();
                Date saleTimeBegin = DateUtils.toDate(unlimitedCard2Config.getCardSaleTimeBegin(), DateUtils.YYYY_MM_DD_HH_MM_SS_PATTERN);
                Date saleTimeEnd = DateUtils.toDate(unlimitedCard2Config.getCardSaleTimeEnd(), DateUtils.YYYY_MM_DD_HH_MM_SS_PATTERN);
                Date now = new Date();
                if (null != saleTimeBegin && now.getTime() <= saleTimeBegin.getTime()) {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo("活动暂未开始，敬请期待！");
                    return resp;
                }
                if (null != saleTimeEnd && now.getTime() >= saleTimeEnd.getTime()) {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo("活动已结束，期待您下次再来！");
                    return resp;
                }
            } else if(StringUtils.isNotBlank(bookRequest.getProductType()) && VoucherTypesEnum.UnlimitUpgradeYear.getCode().equals(bookRequest.getProductType())){
                UpgradeCardV2Config upgradeCardV2Config = handConfig.getUpgradeCardV2Config();
                Date saleTimeBegin = DateUtils.toDate(upgradeCardV2Config.getCardSaleTimeBegin(), DateUtils.YYYY_MM_DD_HH_MM_SS_PATTERN);
                Date now = new Date();
                if (null != saleTimeBegin && now.getTime() <= saleTimeBegin.getTime()) {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo("活动暂未开始，敬请期待！");
                    return resp;
                }
            } else{
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("不支持的操作类型！");
                return resp;
            }

            Boolean realName = memberCacheService.queryMemberRealName(req.getRequest().getFfpId(), req.getChannelCode());
            if (true != realName) {
                resp.setResultCode(WSEnum.NO_REAL_NAME.getResultCode());
                resp.setResultInfo(WSEnum.NO_REAL_NAME.getResultInfo());
                return resp;
            }

            List<ProductInfo> productInfos = productCacheService.obtainProductCache(bookRequest.getProductType(), req.getChannelCode(), ip);
            ProductInfo product = null;
            if (CollectionUtils.isNotEmpty(productInfos)) {
                product = productInfos.get(0);
            }
            if (product == null || CollectionUtils.isEmpty(product.getProducts())) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("未查询到产品信息，购买失败");
                return resp;
            }
            String channelCode = req.getChannelCode();
            String userNo = getChannelInfo(channelCode, "10");
            String mobile = null;
            String email = null;
            String accountName = null;
            //没有手机号
            if (StringUtils.isBlank(bookRequest.getMobilePhone())) {
                String[] items = {MemberDetailRequestItemsEnum.CONTACTINFO.eName, MemberDetailRequestItemsEnum.BASICINFO.eName};
                PtApiCRMRequest<PtMemberDetailRequest> ptApiRequest = CRMReqUtil.buildMemberDetailReq(bookRequest.getFfpCardNo(), bookRequest.getFfpId(), request, channelCode, items);
                PtCRMResponse<PtMemberDetail> ptCRMResponse = memberService.memberDetail(ptApiRequest);
                if (ptCRMResponse.getCode() == 0) {
                    MemberContactSoaModel memberContactSoaModel = CRMReqUtil.getContactInfo(ptCRMResponse.getData().getContactInfo(), ContactTypeEnum.MOBILE.getCode());
                    if (memberContactSoaModel != null) {
                        bookRequest.setMobilePhone(memberContactSoaModel.getContactNumber());
                        mobile = memberContactSoaModel.getContactNumber();
                    }
                    MemberContactSoaModel emailInfo = CRMReqUtil.getContactInfo(ptCRMResponse.getData().getContactInfo(), ContactTypeEnum.EMAIL.getCode());
                    if (emailInfo != null) {
                        email = emailInfo.getContactNumber();
                    }
                    MemberBasicInfoSoaModel basicInfo = ptCRMResponse.getData().getBasicInfo();
                    if (null != basicInfo) {
                        accountName = basicInfo.getCLastName() + basicInfo.getEFirstName();
                    }
                } else {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo(ptCRMResponse.getMsg());
                    return resp;
                }
            }
            //同盾参数
            if ("Y".equals(handConfig.getUnlimitedCardTongDun()) && (VoucherTypesEnum.UNLIMITED_FLY_V2.getCode().equals(bookRequest.getProductType())
                    || VoucherTypesEnum.UNLIMITED_FLY_V2_SF.getCode().equals(bookRequest.getProductType())
                    || VoucherTypesEnum.UnlimitUpgradeYear.getCode().equalsIgnoreCase(bookRequest.getProductType()))) {
                VoucherTypesEnum voucherTypesEnum = VoucherTypesEnum.getTypeByCode(bookRequest.getProductType());
                FraudApiResponse fraudApiResponse = FraudApiInvoker.unlimitedUpgradeBuyRiskControl(bookRequest.getBlackBox(), bookRequest.getFfpCardNo(), platform, headChannelCode,
                        ip, mobile, email, accountName, voucherTypesEnum.getName(), voucherTypesEnum.getCode(), bookRequest.getSaleCount());
                if (fraudApiResponse.getSuccess() && fraudApiResponse.getFinal_decision().equalsIgnoreCase(FinalDecisionEnum.REJECT.getCode())) {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo("活动太火爆了，请稍后重试");
                    log.info("同盾拦截：用户卡号:{} 同盾响应:{}", bookRequest.getFfpCardNo(), JsonUtil.objectToJson(fraudApiResponse));
                    return resp;
                }
            }
            //转换请求参数
            PtCreateOrderRequest ptCreateOrderRequest = createOrderParam(bookRequest, product);
            PtRequest ptRequest = new PtRequest(HandlerConstants.VERSION, channelCode, userNo);
            ptRequest.setRequestIp(ip);
            ptRequest.setSearchType(bookRequest.getProductType());
            ptRequest.setRequest(ptCreateOrderRequest);
            //使用积分
            if (bookRequest.getScore() > 0) {
                //积分金额大于订单的总金额
                if (BigDecimal.ZERO.compareTo(ptCreateOrderRequest.getTotalAmount()) > 0) {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo("积分金额大于订单总金额");
                    return resp;
                }
                //验证消费密码
                resp = orderService.checkFreeScoreLimit(bookRequest.getFfpCardNo(), bookRequest.getFfpId(), channelCode, getChannelInfo(channelCode, "40"), bookRequest.getScore(), bookRequest.getSalePwd(), request);
                if (!WSEnum.SUCCESS.getResultCode().equals(resp.getResultCode())) {
                    return resp;
                }
            }
            Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
            //根据类型动态调整下单链接地址
            String url = HandlerConstants.URL_FARE_API+HandlerConstants.PRODUCT_CREATE;
            url = url.replace("${resourceType}",bookRequest.getProductType());
            if(VoucherTypesEnum.UNLIMITED_FLY_V2.getCode().equalsIgnoreCase(bookRequest.getProductType())
                    || VoucherTypesEnum.UNLIMITED_FLY_V2_SF.getCode().equalsIgnoreCase(bookRequest.getProductType())
                    || VoucherTypesEnum.UnlimitUpgradeYear.getCode().equalsIgnoreCase(bookRequest.getProductType())){
                url = HandlerConstants.URL_FARE_API+HandlerConstants.PRODUCT_CREATE_V10;
            }
            PtResponse<PtBaseCouponOrderIdentity> ptResponse = orderManage.createProductOrder(ptRequest,headMap,url);
            if (UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(ptResponse.getResultCode())) {
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                Map<String, Object> orderInfo = new HashMap<>();
                PtBaseCouponOrderIdentity ptBaseCouponOrderIdentity = ptResponse.getResult();
                orderInfo.put("orderNo", ptBaseCouponOrderIdentity.getOrderNo());
                orderInfo.put("channelOrderNo", ptBaseCouponOrderIdentity.getOrderChannelOrderNo());
                orderInfo.put("payState", false);
                //0元同步支付
                if (bookRequest.getScore() > 0 && BigDecimal.ZERO.compareTo(ptCreateOrderRequest.getTotalAmount()) == 0) {
                    String key = getChannelInfo(req.getChannelCode(), "20");
                    String postUrl = HandlerConstants.URL_PAY;
                    Map<String, String> parametersMap = VirtualPaymentConvert.payment0(req.getChannelCode(), ptBaseCouponOrderIdentity.getOrderNo(), ptBaseCouponOrderIdentity.getOrderChannelOrderNo(), key, "CouponType", "", "O");
                    parametersMap.put("UseScore", String.valueOf(bookRequest.getScore()));
                    HttpResult payResult = doPayPost(postUrl, parametersMap);
                    PaymentResp paymentResp;
                    if (payResult.isResult()) {
                        String paymentInfo = payResult.getResponse().trim();
                        log.info("请求号:{}，IP地址:{}，0元支付结果：{}", reqId, ip, paymentInfo);
                        paymentResp = (PaymentResp) JsonUtil.jsonToBean(paymentInfo, PaymentResp.class);
                        //虚拟支付成功
                        if (paymentResp != null && "1001".equals(paymentResp.getRespCode())) {
                            orderInfo.put("payState", true);
                            resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                        } else {
                            resp.setResultCode(WSEnum.ERROR.getResultCode());
                            resp.setResultInfo("权益券购买支付失败！");
                        }
                    } else {
                        resp.setResultCode(WSEnum.ERROR.getResultCode());
                        resp.setResultInfo("支付请求出错");
                    }
                }
                resp.setObjData(orderInfo);
            } else if(UnifiedOrderResultEnum.R1005.getResultCode().equals(ptResponse.getResultCode())){
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo(ptResponse.getErrorInfo());
            }else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("小吉正在努力搬砖中，请您稍后再试");
            }
            return resp;
        } catch (Exception e) {
            this.logError(resp, reqId, ip, req, e, "糟糕，出现意外了");
            return resp;
        }
    }

    @Autowired
    private IOrderService orderService;
    @Autowired
    private IBasicService basicService;
}

package com.juneyaoair.mobile.handler.comm;

import com.juneyaoair.baseclass.response.order.comm.OrderPassengerInfo;
import com.juneyaoair.mobile.SystemConstants;
import com.juneyaoair.mobile.handler.controller.util.SystemUtil;
import com.juneyaoair.utils.json.JsonUtil;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @description redis常用key
 * @date 2018/8/20  10:56.
 */
public class RedisKeyConfig {

    public static final String GLOBAL_KEY_B2CTASK = "B2CTASK:";
    /**
     * 行程单配送方式
     */
    public static final String TRIP_KEY = SystemUtil.envRedisDir() + "common:travelItineraryPro";
    /**
     * 商城推荐
     */
    public static final String MALL_KEY = SystemUtil.envRedisDir() + "common:hotmallpro";
    /**
     * 省市代码
     */
    public static final String PROVINCE_CITY_KEY = SystemUtil.envRedisDir() + "common:provincCity";
    /**
     * 候补航班限制
     */
    public static final String WAITING_LIMIT_KEY = SystemUtil.envRedisDir() + "common:waitingLimit";

    /**
     * 共飞城市
     */
    public static final String COMMON_FLYING_CITY = SystemUtil.envRedisDir() + "common:commonCity";
    /**
     * 会员基础地址信息
     */
    public static final String COMMON_CRM_ADDRESS = SystemUtil.envRedisDir() + "common:crmBaseAddress";
    public static final String COMMON_CRM_ADDRESS_COUNTRY = SystemUtil.envRedisDir() + "common:crmBaseAddress:";
    /**
     * 升舱产品售卖航线
     */
    public static final String COMMON_UP_AIRLINE = SystemUtil.envRedisDir() + "common:upProAirLine";
    /**
     * 首页热门目的地
     * HOT_DEST_VERSION
     */
    public static final String COMMON_HOT_DEST = SystemUtil.envRedisDir() + "common:hotDest";

    /**
     * 首页吉祥甄选
     */
    public static final String COMMON_HOT_COMMODITY = SystemUtil.envRedisDir() + "common:hotCommodity";
    public static final String COMMON_HOT_COMMODITY_V2 = SystemUtil.envRedisDir() + "common:hotCommodityV2";

    /**
     * 航线基础信息
     */
    public static final String COMMON_DEP_ARR_AIRLINE = SystemUtil.envRedisDir() + "common:deparrAirline:";
    /**
     * 航班不含税低价
     */
    public static final String COMMON_FLIGHT_MIN_PRICE = SystemUtil.envRedisDir() + "common:flightMinPrice:";
    /**
     * 航班含税低价
     */
    public static final String COMMON_FLIGHT_TAXINFO = SystemUtil.envRedisDir() + "common:flightTaxMinPrice:";

    /**
     * 半年内（183天）航班运价信息
     */
    public static final String COMMON_FLIGHT_HALFYEAR = SystemUtil.envRedisDir() + "common:flightHalfYearPrice:";

    /**
     * 往返特惠价
     */
    public static final String COMMON_FLIGHT_ROUND = SystemUtil.envRedisDir() + "common:flightRoundMinPrice:";
    /**
     * 免票兑换日历
     * 此缓存由TaskJob维护
     */
    public static final String COMMON_FREE_TICKET_FLIGHT = GLOBAL_KEY_B2CTASK + "common:freeticket:";
    /**
     * 奖励飞免票
     * 缓存由TaskJob维护
     */
    //删除前缀,根据当前环境来添加前缀，因任务模块没有添加前缀，所以暂不添加    public static final String COMMON_AWARD_FLY_FREE_TICKET = SystemUtil.envRedisDir() + GLOBAL_KEY_B2CTASK + "common:awardFlyFreeTicket:";
    public static final String COMMON_AWARD_FLY_FREE_TICKET = GLOBAL_KEY_B2CTASK + "common:awardFlyFreeTicket:";
    /**
     * 限制集合
     */
    public static final String LIMIT_INFO = SystemUtil.envRedisDir() + "limit:";
    /**
     * 国家缓存
     */
    public static final String COMMON_COUNTRYS = SystemUtil.envRedisDir() + "common:countrys";
    /**
     * 会员app额外赠送积分规则0
     */
    public static final String AV_OBJECT_CONVERT_V2_ACTIVITY = SystemUtil.envRedisDir() + "AdditionalScoreGive:Activity_rule_";
    /**
     * 支付方式目录
     */
    public static final String PAY_METHOD_DIR = SystemUtil.envRedisDir() + "PAYMETHODFORM:";
    /**
     * 支付配置目录
     */
    public static final String PAY_METHOD_CONFIG_DIR = SystemUtil.envRedisDir() + "PAYMETHOD:";
    /**
     * 所有的支持支付方式
     */
    public static final String ALL_PAY_METHOD = SystemConstants.REDIS_KEY_PREFIX + SystemUtil.envRedisDir() + "ALL_PAYMETHOD:";
    /**
     * 服务选项配置目录
     */
    public static final String MIDDLE_TAB_SERVICE = SystemUtil.envRedisDir() + "common:middleTabService:";
    /**
     * 小程序授权登录信息
     */
    public static final String MINI_REDIS_TABLE = SystemUtil.envRedisDir() + "USERTOKEN";

    /**
     * 支付配置目录
     */
    public static final String AIRPORT_CHECKIN_ETIME = SystemUtil.envRedisDir() + "AIRPORTCHECKINETIME";

    /**
     * 常规token标识
     */
    private static final String REDIS_REGULAR_TOKEN_KEY = "REGULAR_TOKEN";

    /**
     * 创建登录token key
     *
     * @param channelCode
     * @param ffpCardNo
     * @return
     */
    public static String createTokenKey(String channelCode, String ffpCardNo) {
        return RedisKeyConfig.REDIS_REGULAR_TOKEN_KEY + ":" + channelCode + ":" + ffpCardNo;
    }

    /**
     * 过渡token标识
     */
    private static final String REDIS_OLD_TOKEN_KEY = "OLD_TOKEN";

    public static String createOldTokenKey(String channelCode, String ffpCardNo) {
        return RedisKeyConfig.REDIS_OLD_TOKEN_KEY + ":" + channelCode + ":" + ffpCardNo;
    }

    /**
     * 支付宝实名认证缓存certifyId
     */
    public static final String ALI_FACE = SystemUtil.envRedisDir() + "ALIFACE:";
    /**
     * 腾讯云实名认证缓存bizToken
     */
    public static final String TENCENT_AUTH = SystemUtil.envRedisDir() + "TENCENT_AUTH:";
    /**
     * 查看会员权益 会员权益集合对象
     */
    public static final String NEW_MEMBER_RIGHTS_KEY = SystemUtil.envRedisDir() + "new_member_rights";

    /**
     * <AUTHOR>
     * @Description 企业会员权益
     * @Date 14:01 2024/7/2
     **/
    public static final String MEMBER_COMPANY_RIGHTS_KEY = SystemUtil.envRedisDir() + "member_company_rights";

    /**
     * 城市基础信息
     */
    public static final String REDIS_CITY_INFO_CD = SystemUtil.envRedisDir() + "cityInfoJson";
    /**
     * 城市基础信息 hash
     */
    public static final String REDIS_CITY_INFO_HASH = SystemUtil.envRedisDir() + "cityInfoHash";
    /**
     * 错误城市信息
     */
    public static final String REDIS_CITY_INFO_ERROR = SystemUtil.envRedisDir() + "errorCityInfo:";
    /**
     * 机场基础信息
     */
    public static final String REDIS_AIRPORT_INFO_CD = SystemUtil.envRedisDir() + "airportInfoJson";
    /**
     * 机场基础信息 hash
     */
    public static final String REDIS_AIRPORT_INFO_HASH = SystemUtil.envRedisDir() + "airportInfoHash";
    /**
     * 航线信息缓存
     */
    public static final String REDIS_AIRLINE_INFO_CD = SystemUtil.envRedisDir() + "airLineInfoJsonZip";
    /**
     * 航线信息缓存 hash
     */
    public static final String REDIS_AIRLINE_INFO_HASH = SystemUtil.envRedisDir() + "airLineInfoHashZip";
    /**
     * 城市，机场查询
     */
    public static final String CACHE_CITY_AIRPORT = SystemConstants.REDIS_KEY_PREFIX + SystemUtil.envRedisDir() + "searchCityAirportInfo";
    /**
     * 运价查询缓存
     */
    public static final String FLIGHT_ADVERTISEMENT = SystemUtil.envRedisDir() + "FlightAdvertisement";
    /**
     * 腾讯云服务 key信息
     */
    public static final String TENCENT_CLOUD_ACCESS_TOKEN = GLOBAL_KEY_B2CTASK + "TENCENT_CLOUD_ACCESS_TOKEN";
    public static final String TENCENT_CLOUD_SIGN_TICKET_REDIS = GLOBAL_KEY_B2CTASK + "TENCENT_CLOUD_SIGN_TICKET_REDIS";
    public static final String TRAVEL_ITINERARY_EMAIL_TEMPLATE = "TRAVEL_ITINERARY_EMAIL_TEMPLATE";
    public static final String TRAVEL_ITINERARY_EMAIL_TEMPLATE_FOR_ENGLISH = "TRAVEL_ITINERARY_EMAIL_TEMPLATE_FOR_ENGLISH";
    public static final String TRAVEL_ITINERARY_SEGMENT_TEMPLATE = "TRAVEL_ITINERARY_SEGMENT_TEMPLATE";
    public static final String TRAVEL_ITINERARY_ENGLISH_SEGMENT_TEMPLATE = "TRAVEL_ITINERARY_ENGLISH_SEGMENT_TEMPLATE";

    /**
     * 手机短信
     */
    public static final String CAPTCHA_INTERVAL = SystemUtil.envRedisDir() + "captcha_interval:";

    /**
     * 查询产品缓存
     */
    public static final String QUERY_PRODUCT_CACHE = SystemUtil.envRedisDir() + "product:";

    /**
     * 会员实名认证状态
     */
    public static final String QUERY_MEMBER_VERIFYSTATUS = SystemUtil.envRedisDir() + "memberVerifyStatus:";
    /**
     * 电子补偿TOKEN
     */
    public static final String EL_COMPENSTATATION_TOKEN = "EL_COMPENSTATATION_TOKEN:WEIXIN";

    /**
     * <AUTHOR>
     * @Description 电子补偿REDIS缓存前缀（目前仅用于支付宝）
     * @Date 15:44 2025/2/17
     **/
    public static final String EL_COMPENSATE_CACHE_ID = "EL_COMPENSATE_CACHE_ID_";

    /**
     * 会员中心会员信息缓存 2021-01-21
     */
    public static final String MEMBER_CENTER_MEMBER_INFO = SystemUtil.envRedisDir() + "memberCenter:memberInfo:";

    /**
     * 会员中心会员积分 2021-01-21
     */
    public static final String MEMBER_CENTER_MEMBER_SCORE = SystemUtil.envRedisDir() + "memberCenter:memberScore:";

    /**
     * 会员中心会员航段 2021-01-21
     */
    public static final String MEMBER_CENTER_MEMBER_SEGMENT = SystemUtil.envRedisDir() + "memberCenter:memberSegment:";

    /**
     * 会员中心保升级 2021-01-21
     */
    public static final String MEMBER_CENTER_MEMBER_UPANDPROGRADE = SystemUtil.envRedisDir() + "memberCenter:memberUpAndProGrade:";

    /**
     * 会员中心优惠券数量 2021-01-21
     */
    public static final String MEMBER_CENTER_MEMBER_COUPONTOTAL = SystemUtil.envRedisDir() + "memberCenter:memberCouponTotal:";

    /**
     * 会员中心权益券数量 2021-01-21
     */
    public static final String MEMBER_CENTER_MEMBER_RIGHT_COUPON_TOTAL = SystemUtil.envRedisDir() + "memberCenter:memberRightCouponTotal:V2:";

    /**
     * 值机选座赠送优惠券缓存
     * 值机选座完成时记录，赠送优惠券时校验
     */
    public static final String SEND_COUPON_CHECK = SystemUtil.envRedisDir() + "check:sendCoupon:";
    /**
     * 移动端校验用token标识
     */
    private static final String REDIS_MOBILE_LOGIN_TOKEN = "MOBILE_LOGIN_TOKEN:";

    public static String createMobileTokenKey(String ffpCardNo) {
        return REDIS_MOBILE_LOGIN_TOKEN + ffpCardNo;
    }

    /**
     *
     */
    public static final String EVENT_COUPON_AWARD = SystemUtil.envRedisDir() + "event_coupon_award:";
    public static final String FFP_USER_EVENT_COUPON_INFO = SystemUtil.envRedisDir() + "ffp_user_event_coupon_info:";

    /**
     * 生成赠送优惠券的redis用于校验
     *
     * @param busineessNo 业务号 值机-票号 支付方完成-订单号
     * @param ffpCardNo
     * @return
     */
    public static String genCheckSendCouponKey(String busineessNo, String ffpCardNo) {
        return SEND_COUPON_CHECK + busineessNo + ffpCardNo;
    }

    /**
     * 创建机票订单缓存
     */
    public static final String CREATE_TICKET_ORDER_CACHE = SystemUtil.envRedisDir() + "createOrderCache:ticketOrder:";

    public static final String SHORT_SENTENCE_TAG = SystemUtil.envRedisDir() + "shortSentenceTag";
    /**
     * 查询国际品牌运价权益列表 2021-05-19
     */
    public static final String QUREY_BRAND_Right = SystemUtil.envRedisDir() + "brandRight:";
    /**
     * 2021-09-07 该票号对应下的行程是否可使用无线升舱卡
     */
    public static final String UNLIMITED_UPGRADE = SystemUtil.envRedisDir() + "unlimitedUpgrade:";

    /**
     * 缓存实名信息
     */
    public static final String MEMBER_REAL = SystemUtil.envRedisDir() + "memberReal:";
    /**
     * 主题卡缓存航线
     */
    public static final String THEME_AIRLINE = SystemUtil.envRedisDir() + "themeAirline:";

    /**
     * APP 底部导航栏缓存
     * 2022-12-14
     */
    public static final String NAVIGATION_BAR = SystemUtil.envRedisDir() + "navigationBar:";

    public static final String MAP_RESERVE_AIRLINE = SystemUtil.envRedisDir() + "mapReserveAirline:";

    /**
     * 消息平台TOKEN缓存
     */
    public static final String MESSAGE_PLATFORM_TOKEN = GLOBAL_KEY_B2CTASK + "messagePlatform:token:";

    /**
     * 基于会员发送短信次数
     */
    public static final String SEND_VERIFY_CODE_FFP_COUNT = SystemConstants.REDIS_KEY_PREFIX + "sendVerifyCodeFfpCount:";
    /**
     * 基于会员上传附件次数限制
     */
    public static final String UPLOAD_VERIFY_CODE_FFP_COUNT = SystemConstants.REDIS_KEY_PREFIX + "uploadVerifyCodeFfpCount:";

    /**
     * 校验许可访问次数
     */
    public static final String CHECK_ACCESS_COUNT = SystemConstants.REDIS_KEY_PREFIX + "checkAccessCount:";
    /**
     * 账户操作类型
     */
    public static final String ACCOUNT_OPERATION = SystemUtil.envRedisDir() + "accountOperation:";
    /**
     * 实名状态可信标记
     */
    public static final String ACCOUNT_REAL_STATUS = SystemUtil.envRedisDir() + "accountRealStatus:";
    /**
     * 实名操作标记
     */
    public static final String ACCOUNT_REAL_METHOD = SystemUtil.envRedisDir() + "accountRealMethod:";

    /**
     * 短信SMS缓存
     */
    public static final String SMS_REDIS = SystemUtil.envRedisDir() + "SMS:";

    /**
     *
     * @param phone 注意此手机号一般情况下不含国际码
     * @param operation
     * @return
     */
    public static String createSmsKey(String phone,String operation){
        return SMS_REDIS + phone + operation;
    }
    /**
     * 会员操作状态标记位
     */
    public static final String FFP_OPERATION_STATUS = SystemUtil.envRedisDir() + "FFP_OPERATION_STATUS:";
    /**
     * 查询升舱订单信息
     */
    public static final String QUERY_UPGRADE_ORDER = SystemUtil.envRedisDir() + "QUERY_UPGRADE_ORDER:";
    /** 许可校验 */
    public static final String CHECK_LICENSE = SystemConstants.REDIS_KEY_PREFIX + "checkLicense:";


    /**
     * 生茶客票查询缓存
     *
     * @param operation
     * @param ticket
     * @return
     */
    public static String createTicketCacheKey(String operation, String ticket) {
        ticket = ticket.replace("-", "");
        if (StringUtils.isBlank(operation)) {
            return SystemUtil.envRedisDir() + ticket;
        } else {
            return SystemUtil.envRedisDir() + operation + ":" + ticket;
        }

    }

    /**
     * 保险航班缓存信息
     */
    public static String createInsuranceFlightInfoPassKey(String passName, String tktNo) {
        tktNo = tktNo.replace("-", "");
        return SystemUtil.envRedisDir() + "InsuranceFlightInfoPass:" + passName + ":" + tktNo;
    }

    public static String createInsuranceFlightInfoKey(String tktNo) {
        return SystemUtil.envRedisDir() + "InsuranceFlightInfo:" + tktNo;
    }


    /***
     * 创建人脸验证时使用的缓存key
     * @param realChannelCode
     * @param ffpCardNo
     * @param idNo
     * @return
     */
    public static String createTencentAuth(String realChannelCode, String ffpCardNo, String idNo) {
        return createTencentAuth(realChannelCode, ffpCardNo, idNo, "");
    }

    /**
     * 创建人脸验证时使用的缓存key
     *
     * @param realChannelCode 真实渠道
     * @param ffpCardNo       会员卡号
     * @param idNo            证件信息
     * @param operation       操作类型
     * @return
     */
    public static String createTencentAuth(String realChannelCode, String ffpCardNo, String idNo, String operation) {
        String key = RedisKeyConfig.TENCENT_AUTH + realChannelCode + ":" + ffpCardNo + ":" + idNo;
        if (StringUtils.isNotBlank(operation)) {
            key = key + ":" + operation;
        }
        return key;
    }

    /**
     * 账号封禁状态 1：封禁  2：正常
     */
    private static final String CHECK_IS_CLOSED = SystemUtil.envRedisDir() + "check:isClosed:";

    public static String createAccountStateKey(String ffpId, String ffpCardNo) {
        return RedisKeyConfig.CHECK_IS_CLOSED + ffpCardNo + "-" + ffpId;
    }

    /**
     * 航班不正常结果缓存
     */
    private static final String FLIGHT_CHANGE_CACHE = SystemUtil.envRedisDir() + "flightChange:";

    public static String createFlightChangeKey(String reqId) {
        return RedisKeyConfig.FLIGHT_CHANGE_CACHE + reqId;
    }

    /**
     * 创建证件照重置消费密码缓存
     *
     * @return
     */
    public static String createPhotoConsumerPasswd(String ffpCardNo, String veryChannel) {
        return SystemUtil.envRedisDir() + "PhotoConsumerPasswd:" + ffpCardNo + ":" + veryChannel;
    }

    /**
     * 创建改期升舱客票关键信息
     *
     * @param tktNo
     * @return
     */
    public static String createUpIdInfo(String tktNo) {
        tktNo = tktNo.replace("-", "");
        return SystemUtil.envRedisDir() + "tktNo:" + tktNo;
    }

    /**
     * 创建客票关键信息
     * @param funcDir 功能模块
     * @param tktNo
     * @return
     */
    public static final String TICKET_VERY_DIR = "ticketVeryNew:";
    public static String createTicketVeryInfo(String funcDir,String tktNo) {
        tktNo = tktNo.replace("-", "");
        return SystemUtil.envRedisDir() + funcDir +"tktNo:" + tktNo;
    }

    /**
     * 会员证件信息
     */
    private static final String MEMBER_CERTIFICATE = SystemUtil.envRedisDir() + "memberCertificate:";
    public static String createMemberCertificateKey(String ffpId, String ffpCardNo) {
        return RedisKeyConfig.MEMBER_CERTIFICATE + ffpCardNo + "-" + ffpId;
    }

    /**
     *  航线标签缓存
     */
    public static final String COMMON_AIRLINE_LABEL = SystemUtil.envRedisDir() + "commonAirlineLabel";

    /**
     * @description  订单乘机人缓存
     * <AUTHOR>
     * @date 2024/11/20 18:43
     * @param channelOrderNo
     * @return String
     **/
    public static String createOrderPass(String channelOrderNo){
        return SystemUtil.envRedisDir()  +"channelOrderNo:" + channelOrderNo;
    }
    /**
     * @description 吉简餐食缓存key
     **/
    public static String createLowCarbonKey(String flightDate){
        return SystemUtil.envRedisDir()  +"lowCarbon:"+flightDate ;
    }
}



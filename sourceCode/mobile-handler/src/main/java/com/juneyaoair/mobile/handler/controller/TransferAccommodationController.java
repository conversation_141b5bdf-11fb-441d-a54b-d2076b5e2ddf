package com.juneyaoair.mobile.handler.controller;

import com.google.common.collect.Lists;
import com.google.gson.reflect.TypeToken;
import com.juneyaoair.appenum.ChannelCodeEnum;
import com.juneyaoair.appenum.WSEnum;
import com.juneyaoair.appenum.member.CertificateTypeEnum;
import com.juneyaoair.appenum.order.UnifiedOrderResultEnum;
import com.juneyaoair.appenum.ticket.TicketQueryTypeEnum;
import com.juneyaoair.baseclass.basicsys.response.AirPortInfoDto;
import com.juneyaoair.baseclass.common.base.UserInfoMust;
import com.juneyaoair.baseclass.common.request.BaseReq;
import com.juneyaoair.baseclass.common.response.BaseResp;
import com.juneyaoair.baseclass.request.av.AircraftModel;
import com.juneyaoair.baseclass.transferaccommodation.request.*;
import com.juneyaoair.baseclass.transferaccommodation.resonse.*;
import com.juneyaoair.exception.CommonException;
import com.juneyaoair.exception.OperationFailedException;
import com.juneyaoair.exception.RequestParamErrorException;
import com.juneyaoair.mobile.aop.annotation.InterfaceLog;
import com.juneyaoair.mobile.handler.HandlerConstants;
import com.juneyaoair.mobile.handler.config.bean.HandConfig;
import com.juneyaoair.mobile.handler.controller.util.IdentityInfoUtil;
import com.juneyaoair.mobile.handler.controller.v2.util.CommonUtil;
import com.juneyaoair.mobile.handler.service.IBasicService;
import com.juneyaoair.mobile.handler.service.LocalCacheService;
import com.juneyaoair.mobile.handler.service.RouteLabelService;
import com.juneyaoair.mobile.handler.service.impl.RouteLabelServiceImpl;
import com.juneyaoair.mobile.handler.util.CertNoUtil;
import com.juneyaoair.mobile.handler.util.FlightUtil;
import com.juneyaoair.mobile.mongo.entity.FlightInfo;
import com.juneyaoair.pattern.PatternCommon;
import com.juneyaoair.thirdentity.request.speedRefund.TicketInfoRequest;
import com.juneyaoair.thirdentity.response.detr.IdentityInfo;
import com.juneyaoair.thirdentity.response.detr.PtIBETicketInfo;
import com.juneyaoair.thirdentity.response.detr.PtSegmentInfo;
import com.juneyaoair.thirdentity.response.speedRefund.TicketListInfoResponse;
import com.juneyaoair.utils.StringUtil;
import com.juneyaoair.utils.http.HttpResult;
import com.juneyaoair.utils.http.HttpUtil;
import com.juneyaoair.utils.json.JsonUtil;
import com.juneyaoair.utils.util.BeanUtils;
import com.juneyaoair.utils.util.DateUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 中转住宿服务
 * @date 2019/8/12  16:52.
 */
@RequestMapping("/transfer")
@RestController
@Api(value = "中转住宿服务", tags = "中转住宿服务")
public class TransferAccommodationController extends BassController {

    @Autowired
    private HandConfig handConfig;
    @Autowired
    private IBasicService basicService;
    @Autowired
    private LocalCacheService localCacheService;

    @Autowired
    private RouteLabelService routeLabelService;

    @Autowired
    private RouteLabelServiceImpl routeLabelServiceimpl;

    private static final String NO_DATA_MESSAGE = "未查询到可申请中转住宿的航班，如有疑问可联系客服";

    private static final String DEP_TIME = "depTime";
    private static final String TRANSFER_FLIGHT_INFO = "transferFlightInfo";

    @ApiOperation(value = "客票查询联程航班", notes = "客票查询联程航班")
    @RequestMapping(value = "/queryTranFlightByTicket", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @InterfaceLog
    public BaseResp queryTranFlightByTicket(@RequestBody @Validated BaseReq<QueryTransferFlightsRequest> req, BindingResult bindingResult, HttpServletRequest request) {
        String ip = this.getClientIP(request);
        BaseResp<List<QueryTransferFlightsResponse>> resp = new BaseResp<>();
        this.checkRequest(req, bindingResult);
        TicketListInfoResponse ticketListInfoResponse = this.queryTicket(req.getChannelCode(), req.getRequest().getTicketNo(), req.getRequest().getPassName(), ip);
        if (!UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(ticketListInfoResponse.getResultCode())) {
            throw new OperationFailedException(NO_DATA_MESSAGE);
        }
        List<PtIBETicketInfo> ticketInfoList = Lists.newArrayList();
        for (PtIBETicketInfo ptIBETicketInfo : ticketListInfoResponse.getIBETicketInfoList()) {
            String patternStr = req.getRequest().getPassName().toUpperCase() + PatternCommon.PASS_NAME_REGEX;//正则表达式
            Pattern pattern = Pattern.compile(patternStr);
            Matcher matcher = pattern.matcher(ptIBETicketInfo.getPassengerName());
            if (matcher.matches()) {
                ticketInfoList.add(ptIBETicketInfo);
            }
        }
        if (CollectionUtils.isEmpty(ticketInfoList)) {
            throw new OperationFailedException(NO_DATA_MESSAGE);
        }

        //

        List<QueryTransferFlightsResponse> queryTransferFlightsResponses = Lists.newArrayList();
        for (PtIBETicketInfo ptIBETicketInfo : ticketInfoList) {
            List<PtSegmentInfo> segmentInfoList = Lists.newArrayList();//航段信息
            // 往返或者单程
            String routeType = ptIBETicketInfo.getDstCity().equals(ptIBETicketInfo.getOrgCity())
                    ? HandlerConstants.ROUTE_TYPE_RT : HandlerConstants.ROUTE_TYPE_OW;
            segmentInfoList.addAll(ptIBETicketInfo.getSegmentInfoList());
            //联程往返的订单
            if (StringUtils.isNotBlank(ptIBETicketInfo.getFollowTicketNo())) {
                String followTicketNo = fetchFollowTicketNo(ptIBETicketInfo.getFollowTicketNo());
                TicketListInfoResponse ticketListInfoResponse2 = this.queryTicket(req.getChannelCode(), followTicketNo, req.getRequest().getPassName(), ip);
                if (UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(ticketListInfoResponse2.getResultCode())
                        && CollectionUtils.isNotEmpty(ticketListInfoResponse2.getIBETicketInfoList())) {
                    for (PtIBETicketInfo ibeTicketInfo : ticketListInfoResponse2.getIBETicketInfoList()) {
                        if (CollectionUtils.isNotEmpty(ibeTicketInfo.getSegmentInfoList())) {
                            segmentInfoList.addAll(ibeTicketInfo.getSegmentInfoList());
                        }
                    }
                }
            }
            if (CollectionUtils.isEmpty(segmentInfoList) || segmentInfoList.size() < 2) {
                continue;
            }
            if (HandlerConstants.ROUTE_TYPE_RT.equals(routeType) && segmentInfoList.size() < 4) {
                continue;
            }
            // 航段map，key为航线
            Map<Integer, List<PtSegmentInfo>> segmentInfoMap = new HashMap<>();
            if (HandlerConstants.ROUTE_TYPE_RT.equals(routeType)) {
                int batchSize = segmentInfoList.size() / 2;
                List<PtSegmentInfo> goList = Lists.newArrayList();
                List<PtSegmentInfo> backList = Lists.newArrayList();
                for (int i = 0; i < segmentInfoList.size(); i++) {
                    if (i < batchSize) {
                        goList.add(segmentInfoList.get(i));
                    } else {
                        backList.add(segmentInfoList.get(i));
                    }
                }
                segmentInfoMap.put(0, goList);
                segmentInfoMap.put(1, backList);
            } else {
                segmentInfoMap.put(0, segmentInfoList);
            }

            segmentInfoMap.forEach((key, segmentList) -> {
                boolean canApply = false;
                for (int i = 0; i < segmentList.size() - 1; i++) {
                    PtSegmentInfo segmentInfo1 = segmentList.get(i);
                    PtSegmentInfo segmentInfo2 = segmentList.get(i + 1);
                    try {
                        canApply = routeLabelServiceimpl.transitAccommodationApplicable(segmentInfo1, segmentInfo2);
                    } catch (Exception e) {
                        log.error("根据配置过滤可申请中转住宿航段出现异常", e);
                    }
                }
                if (canApply) {
                    QueryTransferFlightsResponse queryTransferFlightsResponse = new QueryTransferFlightsResponse();
                    StringBuilder route = new StringBuilder();
                    StringBuilder routeCode = new StringBuilder();
                    queryTransferFlightsResponse.setTicketNo(ptIBETicketInfo.getTicketNo());
                    TransferPassengerInfo passengerInfo = new TransferPassengerInfo();
                    passengerInfo.setPassengerName(ptIBETicketInfo.getPassengerName());
                    List<IdentityInfo> identityInfos = Lists.newArrayList();
                    IdentityInfo identityInfo = IdentityInfoUtil.getIdentityInfo(ptIBETicketInfo.getIdentityInfoList(), CertificateTypeEnum.PASSPORT.getShowCode());
                    identityInfos.add(identityInfo);
                    passengerInfo.setIdentityInfos(identityInfos);
                    switch (ptIBETicketInfo.getPassengerType()) {
                        case "CHD":
                            passengerInfo.setPassengerType("儿童");
                            break;
                        case "INF":
                            passengerInfo.setPassengerType("婴儿");
                            break;
                        default:
                            passengerInfo.setPassengerType("成人");
                    }
                    queryTransferFlightsResponse.setPassengerInfo(passengerInfo);
                    List<TransferFlightInfo> transferFlightInfos = Lists.newArrayList();
                    for (int i = 0; i < segmentList.size(); i++) {
                        PtSegmentInfo segmentInfo = segmentList.get(i);
                        AirPortInfoDto depAirportInfo = localCacheService.getLocalAirport(segmentInfo.getDepAirportCode());
                        AirPortInfoDto arrAirportInfo = localCacheService.getLocalAirport(segmentInfo.getArrAirportCode());
                        route.append(depAirportInfo.getCityName()).append("—");
                        routeCode.append(depAirportInfo.getCityCode()).append("—");
                        if (i == 0) {
                            queryTransferFlightsResponse.setFlightNo(segmentInfo.getFlightNo());
                            queryTransferFlightsResponse.setDepCityCode(depAirportInfo.getCityCode());
                            queryTransferFlightsResponse.setDepCityName(depAirportInfo.getCityName());
                            queryTransferFlightsResponse.setDepTime(segmentInfo.getDepTime());
                            queryTransferFlightsResponse.setDepAirportName(depAirportInfo.getAirPortName());
                            queryTransferFlightsResponse.setDepTerminal(segmentInfo.getDepAirportTerminal());
                        } else if (i == segmentList.size() - 1) {
                            queryTransferFlightsResponse.setArrCityName(arrAirportInfo.getCityName());
                            queryTransferFlightsResponse.setArrCityCode(arrAirportInfo.getCityCode());
                            queryTransferFlightsResponse.setArrTime(segmentInfo.getArrTime());
                            route.append(arrAirportInfo.getCityName());
                            routeCode.append(arrAirportInfo.getCityCode());
                        }
                        segmentInfo.setDepCity(depAirportInfo.getCityCode());
                        segmentInfo.setDepCityName(depAirportInfo.getCityCode());
                        segmentInfo.setDepAirportName(depAirportInfo.getAirPortName());
                        segmentInfo.setArrCity(arrAirportInfo.getCityCode());
                        segmentInfo.setArrCityName(arrAirportInfo.getCityName());
                        segmentInfo.setArrAirportName(arrAirportInfo.getAirPortName());
                        transferFlightInfos.add(genTransferFlightInfoFromSegment(segmentInfo, depAirportInfo.getCityTimeZone(),
                                arrAirportInfo.getCityTimeZone(), i + 1));
                    }
                    queryTransferFlightsResponse.setTransferFlightInfos(transferFlightInfos);
                    queryTransferFlightsResponse.setRouteCode(routeCode.toString());
                    queryTransferFlightsResponse.setRoute(route.toString());
                    queryTransferFlightsResponses.add(queryTransferFlightsResponse);
                }
            });
        }
        // 限制起飞前48小时内的申请
        List<QueryTransferFlightsResponse> finalQueryTransferFlightsResponses = queryTransferFlightsResponses;
        finalQueryTransferFlightsResponses =
                finalQueryTransferFlightsResponses.stream().filter(queryTransferFlightsResponse -> {
                    LocalDateTime now = LocalDateTime.now();
                    LocalDateTime depTime = LocalDateTime.parse(queryTransferFlightsResponse.getDepTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"));
                    return now.isBefore(depTime.minusHours(48));
                }).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(finalQueryTransferFlightsResponses)) {
            throw new OperationFailedException(NO_DATA_MESSAGE);
        }
        fetchApplyStatus(finalQueryTransferFlightsResponses, req, request);
        resp.setObjData(finalQueryTransferFlightsResponses);
        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
        resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
        return resp;
    }


    private TransferFlightInfo genTransferFlightInfoFromSegment(PtSegmentInfo segmentInfo, String depZone, String arrZone, int index) {
        TransferFlightInfo transferFlightInfo = new TransferFlightInfo();
        BeanUtils.copyNotNullProperties(segmentInfo, transferFlightInfo);
        String depTime = segmentInfo.getDepTime();
        String arrTime = segmentInfo.getArrTime();
        transferFlightInfo.setHasWifi("N");
        transferFlightInfo.setCabinName(CommonUtil.showCabinClassName(CommonUtil.
                getCabinClassByCabinCode(segmentInfo.getCabin(), handConfig.getCabinClass())));
        return getTransferFlightInfo(index, transferFlightInfo, depTime, depZone, arrTime, arrZone, segmentInfo);
    }

    private TransferFlightInfo getTransferFlightInfo(int index, TransferFlightInfo transferFlightInfo, String depTime,
                                                     String depZone, String arrTime, String arrZone, PtSegmentInfo segmentInfo) {
        transferFlightInfo.setDuration(DateUtils.calDuration(depTime, depZone, arrTime, arrZone));
        transferFlightInfo.setDurDay(DateUtils.diffDaysWithHour(depTime, depZone, arrTime, arrZone));
        transferFlightInfo.setFlightDate(depTime.substring(0, depTime.length() - 5));
        transferFlightInfo.setWeek(DateUtils.getWeekStr(DateUtils.toDate(depTime, DateUtils.YYYY_MM_DD_HH_MM_PATTERN)));
        FlightInfo flightInfo = queryFlightInfo(transferFlightInfo.getFlightDate(), segmentInfo.getFlightNo(), segmentInfo.getDepAirportCode(), segmentInfo.getArrAirportCode());
        if (flightInfo != null) {
            Map<String, AircraftModel> aircraftModelMap = FlightUtil.toAircraftModelMap(handConfig.getAircraftModel());
            AircraftModel aircraftModel = FlightUtil.convertAircraftModel(aircraftModelMap, flightInfo.getPlanType());
            transferFlightInfo.setPlaneType(StringUtils.isNotBlank(aircraftModel.getRemark()) ? aircraftModel.getRemark() : "");
            if ("789".equals(flightInfo.getPlanType())) {
                transferFlightInfo.setHasWifi("Y");
            }
        }
        transferFlightInfo.setIndex(index);
        return transferFlightInfo;
    }

    @ApiOperation(value = "确认提交中转住宿", notes = "确认提交中转住宿")
    @RequestMapping(value = "/bookTranOrder", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @InterfaceLog
    public BaseResp bookTranOrder(@RequestBody @Validated BaseReq<BookTransferOrderRequest> req, BindingResult bindingResult, HttpServletRequest request) {
        BaseResp resp = new BaseResp();
        this.checkRequest(req, bindingResult);
        BaseCouponOrderRequestDto requestDto = genBaseRequest(req, request);
        TransferHouseRequestDto transferHouseRequestDto = new TransferHouseRequestDto();
        BookTransferOrderRequest bookTrainOrderRequest = req.getRequest();
        TransferPassengerInfo passengerInfo = bookTrainOrderRequest.getFlightInfo().getPassengerInfo();
        if (passengerInfo == null || CollectionUtils.isEmpty(passengerInfo.getIdentityInfos())) {
            throw new RequestParamErrorException("旅客信息不能为空");
        }
        IdentityInfo identityInfo = passengerInfo.getIdentityInfos().get(0);
        transferHouseRequestDto.setCertNo(identityInfo.getIdNo());
        transferHouseRequestDto.setCertType(identityInfo.getIdType());
        transferHouseRequestDto.setChannelOrderNo(bookTrainOrderRequest.getChannelOrderNo());
        transferHouseRequestDto.setContact(bookTrainOrderRequest.getLinker());
        transferHouseRequestDto.setPhoneNo(bookTrainOrderRequest.getLinkerPhone());
        transferHouseRequestDto.setTktNo(bookTrainOrderRequest.getFlightInfo().getTicketNo());
        transferHouseRequestDto.setPassengerName(bookTrainOrderRequest.getFlightInfo().getPassengerInfo().getPassengerName());
        List<FlightInformation> flightInformations = new ArrayList<>();
        for (TransferFlightInfo transferFlightInfo : bookTrainOrderRequest.getFlightInfo().getTransferFlightInfos()) {
            FlightInformation flightInformation = new FlightInformation();
            BeanUtils.copyNotNullProperties(transferFlightInfo, flightInformation);
            flightInformation.setSegmentType(transferFlightInfo.getIndex());
            Map<String, String> couponOrderParam = new HashMap<>();
            couponOrderParam.put(TRANSFER_FLIGHT_INFO, JsonUtil.objectToJson(transferFlightInfo));
            couponOrderParam.put("passengerInfo", JsonUtil.objectToJson(passengerInfo));
            flightInformation.setCouponOrderParam(couponOrderParam);
            flightInformations.add(flightInformation);
        }
        transferHouseRequestDto.setFlightInformations(flightInformations);
        requestDto.setRequest(transferHouseRequestDto);
        HttpResult httpResult = this.doPostClient(requestDto, HandlerConstants.URL_FARE_API + HandlerConstants.TRANSFER_ACCOMMODATION_APPLY_URL);
        if (httpResult.isResult() && StringUtils.isNotBlank(httpResult.getResponse())) {
            BaseCouponOrderResponseDto<BaseCouponOrderIdentity> baseCouponOrderResponseDto = JsonUtil.fromJson(httpResult.getResponse(),
                    new TypeToken<BaseCouponOrderResponseDto<BaseCouponOrderIdentity>>() {
                    }.getType());
            if (UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(baseCouponOrderResponseDto.getResultCode())
                    && baseCouponOrderResponseDto.getResult() != null) {
                resp.setObjData(baseCouponOrderResponseDto.getResult().getOrderNo());
                resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                return resp;
            } else {
                throw new OperationFailedException(baseCouponOrderResponseDto.getErrorInfo());
            }
        } else {
            log.error("网络异常，申请中转住宿失败");
            resp.setResultInfo(WSEnum.ERROR.getResultInfo());
            resp.setResultCode("网络异常");
        }
        return resp;
    }

    @ApiOperation(value = "中转住宿详情", notes = "中转住宿详情")
    @RequestMapping(value = "/tranOrderDetail", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp tranOrderDetail(@RequestBody BaseReq<QueryTransferOrderDetailRequest> req, HttpServletRequest request) {
        BaseResp<QueryTransferFlightsResponse> resp = new BaseResp<>();
        String ip = this.getClientIP(request);
        String reqId = StringUtil.newGUID() + request.getRequestURI();
        try {
            //校验参数
            this.checkRequest(req);
            BaseCouponOrderRequestDto requestDto = genBaseRequest(req, request);
            BaseCouponOrderIdentity identity = new BaseCouponOrderIdentity();
            identity.setOrderNo(req.getRequest().getOrderNo());
            requestDto.setRequest(identity);
            HttpResult httpResult = this.doPostClient(requestDto, HandlerConstants.URL_FARE_API +
                    HandlerConstants.TRANSFER_ACCOMMODATION_QUERY_ORDER_DETAIL_URL);
            if (!httpResult.isResult() || StringUtils.isBlank(httpResult.getResponse())) {
                throw new OperationFailedException("网络异常，查询中转住宿详情出错");
            }
            BaseCouponOrderResponseDto<GetOrderDetailResponseDto> dto = JsonUtil.fromJson(httpResult.getResponse(),
                    new TypeToken<BaseCouponOrderResponseDto<GetOrderDetailResponseDto>>() {
                    }.getType());
            if (!UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(dto.getResultCode())) {
                throw new OperationFailedException(dto.getErrorInfo());
            }
            QueryTransferFlightsResponse queryTransferFlightsResponse = new QueryTransferFlightsResponse();
            GetOrderDetailResponseDto detailDto = dto.getResult();
            queryTransferFlightsResponse.setOrderCreateTime(DateUtils.dateToString(new Date(detailDto.getCreateDateTime()),
                    DateUtils.YYYY_MM_DD_HH_MM_PATTERN));
            queryTransferFlightsResponse.setOrderNo(detailDto.getOrderNo());
            queryTransferFlightsResponse.setLinkerPhone(detailDto.getPhoneNo());
            queryTransferFlightsResponse.setLinker(detailDto.getContact());
            FlightInformation flightInfo1 = detailDto.getFlightInformations().get(0);
            FlightInformation flightInfo2 = detailDto.getFlightInformations().get(1);
            Map<String, AirPortInfoDto> airportMap = basicService.queryAllAirportMap(ChannelCodeEnum.MOBILE.getChannelCode(), ip);
            AirPortInfoDto depAirportInfo = airportMap.get(flightInfo1.getDepAirportCode());
            AirPortInfoDto middleAirportInfo = airportMap.get(flightInfo1.getArrAirportCode());
            AirPortInfoDto arrAirportInfo = airportMap.get(flightInfo2.getArrAirportCode());
            queryTransferFlightsResponse.setArrCityName(arrAirportInfo.getCityName());
            queryTransferFlightsResponse.setArrCityCode(arrAirportInfo.getCityCode());
            queryTransferFlightsResponse.setArrTime(flightInfo2.getCouponOrderParam().get("arrAirportTerminal"));
            queryTransferFlightsResponse.setDepCityCode(depAirportInfo.getCityCode());
            queryTransferFlightsResponse.setDepCityName(depAirportInfo.getCityName());
            queryTransferFlightsResponse.setDepTime(flightInfo1.getCouponOrderParam().get(DEP_TIME));
            queryTransferFlightsResponse.setFlightNo(flightInfo1.getFlightNo());
            queryTransferFlightsResponse.setDepAirportName(depAirportInfo.getAirPortName());
            queryTransferFlightsResponse.setDepTerminal(flightInfo1.getCouponOrderParam().get("depAirportTerminal"));
            queryTransferFlightsResponse.setRoute(depAirportInfo.getCityName() + "—"
                    + middleAirportInfo.getCityName() + "—" + arrAirportInfo.getCityName());
            queryTransferFlightsResponse.setRouteCode(depAirportInfo.getCityCode() + "—"
                    + middleAirportInfo.getCityCode() + "—" + arrAirportInfo.getCityCode());
            queryTransferFlightsResponse.setTicketNo(detailDto.getTktNo());
            List<TransferFlightInfo> transferFlightInfos = Lists.newArrayList();
            transferFlightInfos.add(JsonUtil.fromJson(flightInfo1.getCouponOrderParam().get(TRANSFER_FLIGHT_INFO), TransferFlightInfo.class));
            transferFlightInfos.add(JsonUtil.fromJson(flightInfo2.getCouponOrderParam().get(TRANSFER_FLIGHT_INFO), TransferFlightInfo.class));
            queryTransferFlightsResponse.setPassengerInfo(JsonUtil.fromJson(flightInfo1.getCouponOrderParam().get("passengerInfo"), TransferPassengerInfo.class));
            queryTransferFlightsResponse.setTransferFlightInfos(transferFlightInfos);
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
            resp.setObjData(queryTransferFlightsResponse);
        } catch (Exception e) {
            this.logError(resp, reqId, ip, req, e);
        }
        return resp;
    }

    private void fetchApplyStatus(List<QueryTransferFlightsResponse> queryTransferFlightsResponses, BaseReq<? extends UserInfoMust> req,
                                  HttpServletRequest request) {
        BaseCouponOrderRequestDto requestDto = genBaseRequest(req, request);
        TransferHouseCheckApplyDto transferHouseCheckApplyDto = new TransferHouseCheckApplyDto();
        List<CheckTransferHouse> checkTransferHouses = Lists.newArrayList();
        for (QueryTransferFlightsResponse queryTransferFlightsResponse : queryTransferFlightsResponses) {
            // 只需传第一段
            Optional<TransferFlightInfo> transferFlightInfo = queryTransferFlightsResponse.getTransferFlightInfos().stream()
                    .filter(info -> 1 == info.getIndex()).findFirst();
            if (transferFlightInfo.isPresent()) {
                CheckTransferHouse checkTransferHouse = new CheckTransferHouse();
                checkTransferHouse.setTktNo(queryTransferFlightsResponse.getTicketNo());
                checkTransferHouse.setFlightDate(transferFlightInfo.get().getFlightDate());
                checkTransferHouse.setFlightNo(transferFlightInfo.get().getFlightNo());
                checkTransferHouse.setCouponSource("TransferHouse");
                checkTransferHouses.add(checkTransferHouse);
                if (CollectionUtils.isNotEmpty(queryTransferFlightsResponse.getPassengerInfo().getIdentityInfos())) {
                    checkTransferHouse.setCertNo(IdentityInfoUtil.getIdentityInfo(queryTransferFlightsResponse.getPassengerInfo().getIdentityInfos()).getIdNo());
                }
            }
        }
        transferHouseCheckApplyDto.setCheckTransferHouseList(checkTransferHouses);
        requestDto.setRequest(transferHouseCheckApplyDto);
        HttpResult httpResult = this.doPostClient(requestDto, HandlerConstants.URL_FARE_API +
                HandlerConstants.TRANSFER_ACCOMMODATION_CHECK_APPLY_URL);
        if (!httpResult.isResult() || StringUtils.isBlank(httpResult.getResponse())) {
            throw new OperationFailedException("网络异常，查询申请状态出错");
        }
        BaseCouponOrderResponseDto<TransferHouseCheckApplyDto> dto = JsonUtil.fromJson(httpResult.getResponse(),
                new TypeToken<BaseCouponOrderResponseDto<TransferHouseCheckApplyDto>>() {
                }.getType());
        if (!UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(dto.getResultCode())) {
            throw new OperationFailedException(dto.getErrorInfo());
        }
        if (CollectionUtils.isNotEmpty(dto.getResult().getCheckTransferHouseList())) {
            for (CheckTransferHouse checkTransferHouse : dto.getResult().getCheckTransferHouseList()) {
                for (QueryTransferFlightsResponse queryTransferFlightsResponse : queryTransferFlightsResponses) {
                    if (queryTransferFlightsResponse.getTicketNo().equals(checkTransferHouse.getTktNo())
                            && queryTransferFlightsResponse.getFlightNo().equals(checkTransferHouse.getFlightNo())) {
                        queryTransferFlightsResponse.setStatus(checkTransferHouse.getApplyResult() ? 2 : 1);
                        queryTransferFlightsResponse.setLinker(checkTransferHouse.getContact());
                        queryTransferFlightsResponse.setLinkerPhone(checkTransferHouse.getPhoneNo());
                        queryTransferFlightsResponse.setOrderNo(checkTransferHouse.getOrderNo());
                        if (null != checkTransferHouse.getCreateDateTime() && checkTransferHouse.getCreateDateTime() > 0) {
                            queryTransferFlightsResponse.setOrderCreateTime(DateUtils.dateToString(
                                    new Date(checkTransferHouse.getCreateDateTime()), DateUtils.YYYY_MM_DD_HH_MM_PATTERN));
                        }
                        break;
                    }
                }
            }
        }
    }

    private BaseCouponOrderRequestDto genBaseRequest(BaseReq<? extends UserInfoMust> req, HttpServletRequest request) {
        BaseCouponOrderRequestDto<?> requestDto = new BaseCouponOrderRequestDto<>();
        requestDto.setChannelCode(req.getChannelCode());
        requestDto.setFfpCardNo(req.getRequest().getFfpCardNo());
        requestDto.setFfpId(req.getRequest().getFfpId());
        requestDto.setRequestIp(this.getClientIP(request));
        requestDto.setVersion("10");
        requestDto.setUserNo(this.getChannelInfo(req.getChannelCode(), "10"));
        return requestDto;
    }

    /**
     * 获取后续票号
     *
     * @param followTicketNo 018-1156006925/26
     * @return 0181156006926
     */
    private static String fetchFollowTicketNo(String followTicketNo) {
        if (!followTicketNo.contains("/")) {
            return followTicketNo;
        }
        String[] ticketNo = followTicketNo.split("/");
        return (ticketNo[0].substring(0, ticketNo[0].length() - ticketNo[1].length()) + ticketNo[1]).replace("-", "");
    }

    /**
     * 获取客票信息
     *
     * @param channelCode
     * @param ticketNo
     * @return
     */
    private TicketListInfoResponse queryTicket(String channelCode, String ticketNo, String passName, String clientIp) {
        String userNo = getChannelInfo(channelCode, "10");
        TicketInfoRequest ticketInfoRequest = new TicketInfoRequest(HandlerConstants.VERSION, channelCode, userNo);
        String certType = CertNoUtil.getCertTypeByCertNo(ticketNo);
        ticketInfoRequest.setCertType(certType);
        ticketInfoRequest.setPassengerName(passName);
        if ("TN".equals(certType)) {
            ticketInfoRequest.setTicketNo(ticketNo);
        } else {
            ticketInfoRequest.setCertNo(ticketNo);
            throw new CommonException(WSEnum.NOT_SUPPORT.getResultCode(), WSEnum.NOT_SUPPORT.getResultInfo());
        }
        ticketInfoRequest.setQueryType(TicketQueryTypeEnum.CHANGE.getCode());
        TicketListInfoResponse ticketListInfoResponse;
        String url = HandlerConstants.URL_FARE_API + HandlerConstants.QUERY_TICKET_INFO;
        Map<String, String> headMap = HttpUtil.getHeaderMap(clientIp, "");
        HttpResult httpResult = this.doPostClient(ticketInfoRequest, url, headMap, handConfig.getReadTimeout(), handConfig.getConnectTimeout());
        if (httpResult.isResult()) {
            if (StringUtils.isBlank(httpResult.getResponse())) {
                ticketListInfoResponse = new TicketListInfoResponse();
                ticketListInfoResponse.setErrorInfo("获取客票信息为空！");
                ticketListInfoResponse.setResultCode(UnifiedOrderResultEnum.FAIL.getResultCode());
            } else {
                try {
                    ticketListInfoResponse = (TicketListInfoResponse) JsonUtil.jsonToBean(httpResult.getResponse(), TicketListInfoResponse.class);
                } catch (Exception ex) {
                    log.error("请求参数：{},请求路径：{}，返回结果：{}，错误信息：", JsonUtil.objectToJson(ticketInfoRequest),
                            url, httpResult.getResponse(), ex);
                    ticketListInfoResponse = new TicketListInfoResponse();
                    ticketListInfoResponse.setErrorInfo("数据转换异常！");
                    ticketListInfoResponse.setResultCode(UnifiedOrderResultEnum.FAIL.getResultCode());
                }
            }
        } else {
            ticketListInfoResponse = new TicketListInfoResponse();
            ticketListInfoResponse.setErrorInfo(httpResult.getResponse());
            ticketListInfoResponse.setResultCode(UnifiedOrderResultEnum.FAIL.getResultCode());
        }
        return ticketListInfoResponse;
    }

    private FlightInfo queryFlightInfo(String flightDate, String flightNo, String depAirportCode, String arrAirportCode) {
        FlightInfo flightInfo = new FlightInfo();
        flightInfo.setFlightDate(flightDate);
        flightInfo.setDepAirport(depAirportCode);
        flightInfo.setArrAirport(arrAirportCode);
        flightInfo.setFlightNo(flightNo);
        List<FlightInfo> flightInfoList = basicService.queryFlightInfo(flightInfo);
        if (CollectionUtils.isNotEmpty(flightInfoList)) {
            return flightInfoList.get(0);
        }
        return null;
    }

}

package com.juneyaoair.mobile.handler.controller.v2.util;

import cn.hutool.core.util.ObjectUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.juneyaoair.appenum.WSEnum;
import com.juneyaoair.appenum.av.*;
import com.juneyaoair.appenum.common.AirCompanyEnum;
import com.juneyaoair.appenum.flight.FlightDirection;
import com.juneyaoair.appenum.order.PassengerTypeEnum;
import com.juneyaoair.appenum.order.UnifiedOrderResultEnum;
import com.juneyaoair.baseclass.activity.MaoTaiConfig;
import com.juneyaoair.baseclass.av.common.*;
import com.juneyaoair.baseclass.basicsys.response.AirPortInfoDto;
import com.juneyaoair.baseclass.basicsys.response.PictureDto;
import com.juneyaoair.baseclass.change.ChangeAndRefundRule;
import com.juneyaoair.baseclass.change.ChangeFlightInfo;
import com.juneyaoair.baseclass.common.base.ConditionFilter;
import com.juneyaoair.baseclass.request.av.AirCompany;
import com.juneyaoair.baseclass.request.av.AircraftModel;
import com.juneyaoair.baseclass.request.booking.*;
import com.juneyaoair.baseclass.response.av.CabinFare;
import com.juneyaoair.baseclass.response.av.FlightInfo;
import com.juneyaoair.baseclass.response.av.*;
import com.juneyaoair.baseclass.response.booking.TicketBookingResp;
import com.juneyaoair.bo.FlightQueryBO;
import com.juneyaoair.client.ClientInfo;
import com.juneyaoair.exception.OperationFailedException;
import com.juneyaoair.mobile.SystemConstants;
import com.juneyaoair.mobile.handler.HandlerConstants;
import com.juneyaoair.mobile.handler.config.bean.HandConfig;
import com.juneyaoair.mobile.handler.config.bean.TempleteConfig;
import com.juneyaoair.mobile.handler.controller.util.AVObjectConvert;
import com.juneyaoair.mobile.handler.controller.util.OrderDetailConvert;
import com.juneyaoair.mobile.handler.controller.util.OrderObjectConvert;
import com.juneyaoair.mobile.handler.service.LocalCacheService;
import com.juneyaoair.mobile.handler.util.FlightUtil;
import com.juneyaoair.pattern.PatternCommon;
import com.juneyaoair.thirdentity.av.comm.*;
import com.juneyaoair.thirdentity.av.response.PtQueryFlightFareResponse;
import com.juneyaoair.thirdentity.change.response.InterChangeFee;
import com.juneyaoair.thirdentity.request.booking.*;
import com.juneyaoair.thirdentity.request.score.ScoreUseRuleRequest;
import com.juneyaoair.thirdentity.response.tax.InternatTaxInfo;
import com.juneyaoair.utils.StringUtil;
import com.juneyaoair.utils.http.HttpResult;
import com.juneyaoair.utils.json.JsonMapper;
import com.juneyaoair.utils.json.JsonUtil;
import com.juneyaoair.utils.util.BeanUtils;
import com.juneyaoair.utils.util.DateUtils;
import com.juneyaoair.utils.util.NumberUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import static com.juneyaoair.mobile.handler.controller.v2.util.V2OrderObjectConvert.getCouponProductInfoList;

/**
 * <AUTHOR>
 * @description 新版运价的结果转换
 * @date 2018/12/5  20:27.
 */
public class AVObjectConvertV2 {

    public static final String INF_SPECIAL_REMARK = "婴儿可免费托运婴儿手推车一辆";


    private AVObjectConvertV2() {

    }

    private static Logger log = LoggerFactory.getLogger(AVObjectConvertV2.class);
    //Y表示允许售卖WIFI舱
    private static final String SALE_WIFI_FLAG = "Y";

    public static final String LIST_PSP = "listPSP";
    public static final String LIST_SEG = "listSeg";
    public static final String LIST_PRICE = "listPrice";


    /**
     * 航班信息处理
     *
     * @param res
     * @param handConfig
     * @param flightQueryBO 具体查询类型 参考FlightQueryTypeEnum
     * @param userAppVer
     * @return
     */
    public static QueryFlightFareResp toFlightFareResponse(PtQueryFlightFareResponse res, HandConfig handConfig, TempleteConfig templeteConfig, FlightQueryBO flightQueryBO, List<String> packageCabinTypes, List<FlightAdvertisementDto> advertisementDtos, LocalCacheService localCacheService, ClientInfo userAppVer) {
        String queryType = flightQueryBO.getQueryType();
        //处理航班各类信息
        String dateStr = DateUtils.getDateStringAll(new Date());
        QueryFlightFareResp resp = new QueryFlightFareResp(res.getRouteType(), res.getCurrencyCode(), dateStr, res.getInterFlag(), res.getResultCode(), res.getErrorInfo(), new ArrayList<>());
        if (!UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(res.getResultCode()) || CollectionUtils.isEmpty(res.getFlightInfoList())) {
            return resp;
        }
        List<FlightInfo> flightInfoList = toFlightInfoList(null, res, flightQueryBO, handConfig, templeteConfig, null, packageCabinTypes, localCacheService);
        if (FlightQueryTypeEnum.POLICE_REMNANTS.getType().equals(queryType)) {
            //只展示自有航班信息
            if (!StringUtil.isNullOrEmpty(flightInfoList)) {
                flightInfoList = flightInfoList.stream().filter(flightInfo -> AirCompanyEnum.HO.getAirCompanyCode().equals(flightInfo.getCarrierNo().substring(0, 2))).collect(Collectors.toList());
                resp.setFlightInfoList(flightInfoList);
            }
        } else if (FlightQueryTypeEnum.PACKAGE_CABIN.getType().equals(queryType) || FlightQueryTypeEnum.NORMAL.getType().equals(queryType)) {
            //航班分组处理
            flightInfoList = groupAndOrderFlightList(flightInfoList);
            resp.setFlightInfoList(flightInfoList);
        } else {
            resp.setFlightInfoList(flightInfoList);
        }
        //国内航班 民航公布运价处理
        if (HandlerConstants.TRIP_TYPE_D.equals(res.getInterFlag())) {
            resp.setYprice(dealYprice(flightInfoList));
        }
        if (CollectionUtils.isNotEmpty(advertisementDtos) && CollectionUtils.isNotEmpty(flightInfoList)) {
            setFlightAdvertisement(flightInfoList, advertisementDtos);
        }
        resp.setFareTaxInfoList(res.getTaxInfoList());//税费列表
        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
        return resp;
    }

    private static void setFlightAdvertisement(List<FlightInfo> flightInfoList, List<FlightAdvertisementDto> advertisementDtos) {
        advertisementDtos.forEach(advertisement -> {
            List<FlightInfo> filteredFlightInfo = flightInfoList.stream().filter(flightInfo -> {
                String airline = flightInfo.getDepAirport() + "-" + flightInfo.getArrAirport();
                return advertisement.getAirlineList().contains(airline);
            }).collect(Collectors.toList());
            filteredFlightInfo.forEach(flightInfo ->
                    flightInfo.getCabinFareList().stream().filter(cabinFare -> {
                        boolean result = false;
                        if (advertisement.getCabinList().contains("K3+") && NumberUtils.toDouble(cabinFare.getShowDisCount()) >= 3 && "K".equals(cabinFare.getCabinCode())) {
                            result = true;
                        }
                        if (advertisement.getCabinList().contains("K3-") && NumberUtils.toDouble(cabinFare.getShowDisCount()) <= 3 && "K".equals(cabinFare.getCabinCode())) {
                            result = true;
                        }
                        if (advertisement.getCabinList().contains(cabinFare.getCabinCode())) {
                            result = true;
                        }
                        return result;
                    }).forEach(cabinFare -> {
                        if (null == cabinFare.getAdvertisements()) {
                            cabinFare.setAdvertisements(Lists.newArrayList());
                        }
                        cabinFare.getAdvertisements().add(advertisement.getPictureDto());
                    }));
        });
        flightInfoList.forEach(flightInfo -> flightInfo.getCabinFareList().forEach(cabinFare -> {
            // 若舱位广告大于1，则随机展示一个广告
            if (CollectionUtils.isNotEmpty(cabinFare.getAdvertisements()) && cabinFare.getAdvertisements().size() > 1) {
                PictureDto pictureDto = cabinFare.getAdvertisements().get(new Random().nextInt(cabinFare.getAdvertisements().size()));
                cabinFare.setAdvertisements(Lists.newArrayList(pictureDto));
            }
        }));
    }

    /**
     * 根据航班计算民航公布运价
     *
     * @param flightInfoList
     * @return
     */
    private static Double dealYprice(List<FlightInfo> flightInfoList) {
        Double yprice = 0.0;
        if (StringUtil.isNullOrEmpty(flightInfoList)) {
            return yprice;
        }
        for (FlightInfo flightInfo : flightInfoList) {
            //当前航班无舱位时，遍历下一个航班
            if (StringUtil.isNullOrEmpty(flightInfo.getCabinFareList())) {
                continue;
            }
            yprice = flightInfo.getCabinFareList().get(0).getYPrice();
            if (yprice > 0) {
                break;
            }
        }
        return yprice;
    }

    /**
     * 改期航班查询信息处理
     *
     * @param res
     * @param handConfig
     * @param flightQueryBO 具体查询类型 参考FlightQueryTypeEnum
     * @return
     */
    public static ChangeFlightFareResp toFlightFareResponse(PtQueryFlightFareResponse res,
                                                            HandConfig handConfig, TempleteConfig templeteConfig, FlightQueryBO flightQueryBO, Map<String, Set<String>> trrCabinCodesMap, LocalCacheService localCacheService) {
        String queryType = flightQueryBO.getQueryType();
        //处理航班各类信息
        String dateStr = DateUtils.getDateStringAll(new Date());
        ChangeFlightFareResp resp = new ChangeFlightFareResp(res.getRouteType(), res.getCurrencyCode(), dateStr, res.getInterFlag(), res.getResultCode(), res.getErrorInfo(), new ArrayList<>());
        if (!UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(res.getResultCode()) || StringUtil.isNullOrEmpty(res.getFlightInfoList())) {
            return resp;
        }
        List<String> packageCabinTypes = Lists.newArrayList();
        packageCabinTypes.add(FareBasisEnum.DISNEY_FARE.getFareBasisCode());
        List<FlightInfo> flightInfoList = toFlightInfoList(null, res, flightQueryBO, handConfig, templeteConfig, trrCabinCodesMap, packageCabinTypes, localCacheService);
        if (FlightQueryTypeEnum.POLICE_REMNANTS.getType().equals(queryType)) {
            //只展示自有航班信息
            if (!StringUtil.isNullOrEmpty(flightInfoList)) {
                flightInfoList = flightInfoList.stream().filter(flightInfo -> AirCompanyEnum.HO.getAirCompanyCode().equals(flightInfo.getCarrierNo().substring(0, 2))).collect(Collectors.toList());
                resp.setFlightInfoList(flightInfoList);
            }
        } else if (FlightQueryTypeEnum.PACKAGE_CABIN.getType().equals(queryType) || FlightQueryTypeEnum.NORMAL.getType().equals(queryType)) {
            //航班分组处理
            flightInfoList = groupAndOrderFlightList(flightInfoList);
            resp.setFlightInfoList(flightInfoList);
        } else {
            resp.setFlightInfoList(flightInfoList);
        }
        //国内航班 民航公布运价处理
        if (HandlerConstants.TRIP_TYPE_D.equals(res.getInterFlag())) {
            resp.setYprice(dealYprice(flightInfoList));
        }
        resp.setFareTaxInfoList(res.getTaxInfoList());//税费列表
        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
        return resp;
    }

    /**
     * 自有航班与非自有航班分组
     *
     * @param flightList
     * @return
     */
    private static List<FlightInfo> groupAndOrderFlightList(List<FlightInfo> flightList) {
        if (StringUtil.isNullOrEmpty(flightList)) {
            return flightList;
        }
        List<FlightInfo> ownFlightListG = new LinkedList<>();//自有航班 去程
        List<FlightInfo> ownFlightListB = new LinkedList<>();//自有航班 返程
        List<FlightInfo> notOwnFlightListG = new LinkedList<>();//非自有航班 去程
        List<FlightInfo> notOwnFlightListB = new LinkedList<>();//非自有航班 返程
        try {
            for (FlightInfo flightInfo : flightList) {
                String carrierNoTwoCode = flightInfo.getCarrierNo().substring(0, 2);//承运航班二字码
                if ("G".equals(flightInfo.getFlightDirection())) {
                    if (AirCompanyEnum.HO.getAirCompanyCode().equals(carrierNoTwoCode)) {
                        ownFlightListG.add(flightInfo);
                    } else {
                        notOwnFlightListG.add(flightInfo);
                    }
                } else if ("B".equals(flightInfo.getFlightDirection())) {
                    if (AirCompanyEnum.HO.getAirCompanyCode().equals(carrierNoTwoCode)) {
                        ownFlightListB.add(flightInfo);
                    } else {
                        notOwnFlightListB.add(flightInfo);
                    }
                }
            }
            //出发时间排序 保留 看返回数据是否已经排好序
            if (!StringUtil.isNullOrEmpty(notOwnFlightListG)) {//合并去程航班
                ownFlightListG.addAll(notOwnFlightListG);
            }
            if (!StringUtil.isNullOrEmpty(notOwnFlightListB)) {//拼接所有的返程航班信息
                ownFlightListB.addAll(notOwnFlightListB);
            }
            if (!StringUtil.isNullOrEmpty(ownFlightListB)) {  //返回重新拼接之后的航班列表
                ownFlightListG.addAll(ownFlightListB);
            }
            return ownFlightListG;
        } catch (Exception e) {
            log.error("flightList分组排序发生错误:{}", e.getMessage());
            return flightList;
        }
    }

    /**
     * 航班信息处理
     *
     * @param res
     * @param flightQueryBO CHANGE 改期  NORMAL代表正常的运价查询  NOTVOLUNTARYCHANGE  非自愿改期
     * @param handConfig
     * @return
     */
    private static List<FlightInfo> toFlightInfoList(ConditionFilter conditionFilter, PtQueryFlightFareResponse res, FlightQueryBO flightQueryBO, HandConfig handConfig,
                                                     TempleteConfig templeteConfig, Map<String, Set<String>> trrCabinCodesMap, List<String> packageCabinTypes, LocalCacheService localCacheService) {
        String curDate = DateUtils.getCurrentDateTimeStr();
        String operationType = flightQueryBO.getQueryType();
        Boolean isDisneyFlag = flightQueryBO.getIsDisneyFlag();
        //处理航司，机型等数据
        Map<String, AirCompany> airCompanyMap = FlightUtil.toAirCompanyMap(handConfig.getAirCompany());
        Map<String, AircraftModel> aircraftModelMap = FlightUtil.toAircraftModelMap(handConfig.getAircraftModel());
        List<V2FlightInfo> v2FlightInfoList = res.getFlightInfoList();
        String interFlag = res.getInterFlag();
        List<FlightInfo> flightList = new LinkedList<>();
        //过滤合适的航班
        String filterCabin = "";
        for (V2FlightInfo v2FlightInfo : v2FlightInfoList) {
            String cityPair = v2FlightInfo.getDepCity() + v2FlightInfo.getArrCity();
            String airportPair = v2FlightInfo.getDepAirport() + v2FlightInfo.getArrAirport();
            if (conditionFilter != null) {
                if ((StringUtils.isNotBlank(conditionFilter.getFlightNo())) && (!v2FlightInfo.getFlightNo().equals(conditionFilter.getFlightNo()))) {
                    continue;
                }
                //匹配对应的城市信息
                if (!cityPair.equals(conditionFilter.getCityPair())) {
                    continue;
                }
                filterCabin = conditionFilter.getCabin();
            }

            //起飞前1小时过滤
            if (DateUtils.filterDateTimeWithZone(curDate, "8", v2FlightInfo.getDepDateTime(), v2FlightInfo.getDepZone(), handConfig.getCloseSellTime())) {
                continue;
            }
            //改期过滤川航
            if (FlightQueryTypeEnum.CHANGE.getType().equals(operationType) && StringUtils.isNotBlank(v2FlightInfo.getCarrierNo())) {
                if ("3U".equals(v2FlightInfo.getCarrierNo().substring(0, 2))) {
                    continue;
                }
            }
            //航班信息处理
            FlightInfo flightInfo = toFlightInfo(v2FlightInfo, airCompanyMap, aircraftModelMap, localCacheService);
            String airCode = v2FlightInfo.getCarrierNo().substring(0, 2);
            //运价处理  成人 儿童 婴儿 军残警残
            List<CabinFare> adtCabinFareList = new ArrayList<>();
            List<CabinFare> chdInfCabinFareList = new ArrayList<>();
            List<CabinFare> gmjcCabinFareList = new ArrayList<>();
            List<CabinFare> cabinOLDFareList = new ArrayList<>();
            // 非自愿改期不过滤已售罄舱位
            boolean filterSoldCabin = true;
            if (null != trrCabinCodesMap && CollectionUtils.isNotEmpty(trrCabinCodesMap.get(airportPair))) {
                filterSoldCabin = false;
            }
            //军残警残
            if (FlightQueryTypeEnum.POLICE_REMNANTS.getType().equals(operationType)) {
                gmjcCabinFareList = toCabinFareList(v2FlightInfo.getCabinGMJCFareList(), airCode, HandlerConstants.PASSENGER_TYPE_GMJC,
                        res, filterCabin, handConfig, filterSoldCabin, flightInfo.getFlightNo(), v2FlightInfo.getFType(), v2FlightInfo);
                gmjcCabinFareList = setFlightMinPrice(gmjcCabinFareList, interFlag, flightInfo, handConfig, operationType);
            } else if (FlightQueryTypeEnum.NO_ACCOMPANY_CHILD.getType().equals(operationType)) {//无陪儿童
                chdInfCabinFareList = toCabinFareList(v2FlightInfo.getCabinCHDINFFareList(), airCode,
                        HandlerConstants.PASSENGER_TYPE_CHD + "," + HandlerConstants.PASSENGER_TYPE_INF, res, filterCabin, handConfig, filterSoldCabin, flightInfo.getFlightNo(), v2FlightInfo.getFType(), v2FlightInfo);
            }//主题卡舱位筛选
            else if (FlightQueryTypeEnum.THEME_CARD.getType().equals(operationType)) {
                //保留有座位的舱位
                v2FlightInfo.getCabinFareList().removeIf(v2CabinFare -> "0".equals(v2CabinFare.getCabinNumber()) || "S".equals(v2CabinFare.getCabinNumber()));
                adtCabinFareList = toCabinFareList(v2FlightInfo.getCabinFareList(), airCode, HandlerConstants.PASSENGER_TYPE_ADT,
                        res, filterCabin, handConfig, filterSoldCabin, flightInfo.getFlightNo(), v2FlightInfo.getFType(), v2FlightInfo);
                chdInfCabinFareList = toCabinFareList(v2FlightInfo.getCabinCHDINFFareList(), airCode,
                        HandlerConstants.PASSENGER_TYPE_CHD + "," + HandlerConstants.PASSENGER_TYPE_INF, res, filterCabin, handConfig, filterSoldCabin, flightInfo.getFlightNo(), v2FlightInfo.getFType(), v2FlightInfo);
                //主题卡目前只支持X舱
                adtCabinFareList = adtCabinFareList.stream().filter(cabinFare -> handConfig.getThemeCabin().equals(cabinFare.getCabinCode())).collect(Collectors.toList());
                adtCabinFareList.forEach(cabinFare -> {
                    cabinFare.setThemeCardType(flightQueryBO.getThemeCardType());
                    cabinFare.setThemeCabinInfo(flightQueryBO.getThemeCardType(), FlightUtil.toThemeModelMap(handConfig.getThemeCabinLabel()));
                    cabinFare.setCabinType(PackageTypeEnum.THEME_CARD.getPackType());
                    cabinFare.setCabinLabelList2(new ArrayList<>());
                    cabinFare.setSortPriority(10);
                });
                chdInfCabinFareList = chdInfCabinFareList.stream().filter(cabinFare -> handConfig.getThemeCabin().equals(cabinFare.getCabinCode())).collect(Collectors.toList());
                chdInfCabinFareList.forEach(cabinFare -> {
                    cabinFare.setCabinType(PackageTypeEnum.THEME_CARD.getPackType());
                    cabinFare.setCabinLabelList2(new ArrayList<>());
                    cabinFare.setSortPriority(10);
                });
                flightInfo.setMinPrice(0D);
                //处理儿童婴儿的退改说明
                setChildInfRule(adtCabinFareList, chdInfCabinFareList, v2FlightInfo.getCarrierNo());
            } else {//其他原有的获取舱位逻辑  正常改期  普通航班查询
                adtCabinFareList = toCabinFareList(v2FlightInfo.getCabinFareList(), airCode, HandlerConstants.PASSENGER_TYPE_ADT,
                        res, filterCabin, handConfig, filterSoldCabin, flightInfo.getFlightNo(), v2FlightInfo.getFType(), v2FlightInfo);
                chdInfCabinFareList = toCabinFareList(v2FlightInfo.getCabinCHDINFFareList(), airCode,
                        HandlerConstants.PASSENGER_TYPE_CHD + "," + HandlerConstants.PASSENGER_TYPE_INF, res, filterCabin, handConfig, filterSoldCabin, flightInfo.getFlightNo(),
                        v2FlightInfo.getFType(), v2FlightInfo);
                //处理茅台展示舱位
                List<CabinFare> maotaiCabinFareList = filterMaotaiCabin(handConfig, flightInfo.getFlightNo(), flightInfo.getFlightDate(), adtCabinFareList);
                //原始舱位展示设置
                adtCabinFareList = setFlightMinPrice(adtCabinFareList, interFlag, flightInfo, handConfig, operationType);

                if (FlightQueryTypeEnum.PACKAGE_CABIN.getType().equals(operationType)
                        || (FlightQueryTypeEnum.CHANGE.getType().equals(operationType) && (isDisneyFlag == null ? false : isDisneyFlag))) {
                    //一舱多价以及往返优惠处理，合并所有舱位运价
                    adtCabinFareList = otherCabinPrice(adtCabinFareList, v2FlightInfo, airCode, res, handConfig, templeteConfig, operationType, packageCabinTypes);
                    //迪士老人运价
                    cabinOLDFareList = adtCabinFareList.stream().filter(fare -> "OLD".equals(fare.getPassengerIdentity())).collect(Collectors.toList());

                    adtCabinFareList = adtCabinFareList.stream().filter(fare -> !("OLD".equals(fare.getPassengerIdentity()))).collect(Collectors.toList());
                    //处理儿童会员价以及秒杀价
                    chdInfCabinFareList = chdCabinPrice(chdInfCabinFareList, v2FlightInfo, airCode, res, handConfig, templeteConfig, operationType, packageCabinTypes, handConfig);
                    //舱位数据匹配，原有舱位返回的更新舱位标签信息，否则追加
                    if (CollectionUtils.isNotEmpty(adtCabinFareList) && CollectionUtils.isNotEmpty(maotaiCabinFareList)) {
                        for (CabinFare maotaiCabinFare : maotaiCabinFareList) {
                            boolean isExist = false;
                            for (CabinFare cabinFare : adtCabinFareList) {
                                if (cabinFare.getCabinCode().equals(maotaiCabinFare.getCabinCode())) {
                                    isExist = true;
                                    BeanUtils.copyNotNullProperties(maotaiCabinFare, cabinFare);
                                    break;
                                }
                            }
                            if (!isExist) {
                                adtCabinFareList.add(maotaiCabinFare);
                            }
                        }
                    }
                    boolean domesticFlag = HandlerConstants.TRIP_TYPE_D.equalsIgnoreCase(res.getInterFlag());
                    //设置航班最低价
                    if (!StringUtil.isNullOrEmpty(adtCabinFareList)) {
                        CabinFare minPriceCabinFare = getCabinFareDisplayPrice(adtCabinFareList);
                        if (minPriceCabinFare != null) {
                            flightInfo.setMinPrice(minPriceCabinFare.getPriceValue());
                            flightInfo.setMinCabinClassName(minPriceCabinFare.getCabinClass());
                            flightInfo.setOnewayMinPrice(Optional.ofNullable(minPriceCabinFare.flightPriceComb).map(i -> i.goPriceValue).map(i -> i.doubleValue()).orElse(null));
                        }
                    }
                    //只有吉祥的航班支持畅飞卡
                    if (AirCompanyEnum.HO.getAirCompanyCode().equals(StringUtils.isNotBlank(v2FlightInfo.getCarrierNo()) ? v2FlightInfo.getCarrierNo().substring(0, 2) : "")) {
                        // 添加儿童畅飞卡
                        if (packageCabinTypes.contains(FareBasisEnum.CHD_UNLIMITED_FARE.getFareBasisCode())) {
                            StringBuilder cabinNumber = new StringBuilder("0");
                            chdInfCabinFareList.stream().filter(cabinFare ->
                                    PackageTypeEnum.CHD_UNLIMITED_FLY.getPackType().equals(cabinFare.getCabinType())).findFirst().ifPresent(cabinFare -> {
                                cabinNumber.setLength(0);
                                cabinNumber.append(cabinFare.getCabinNumber());
                            });
                            CabinFare commonMinCabin = adtCabinFareList.stream()
                                    .filter(cabinFare -> "Y".equals(cabinFare.getCabinClass()) && PackageTypeEnum.CABIN_NORMAL.getPackType().equals(cabinFare.getCabinType()))
                                    .min(Comparator.comparingDouble(CabinFare::getPriceValue)).orElse(null);
                            if (null != commonMinCabin) {
                                // 儿童随心飞运价定义为最低价格的复制
                                CabinFare chdUnlimitedFly = new CabinFare();
                                BeanUtils.copyNotNullProperties(commonMinCabin, chdUnlimitedFly);
                                chdUnlimitedFly.setCabinType(PackageTypeEnum.CHD_UNLIMITED_FLY.getPackType());
                                chdUnlimitedFly.setCabinNumber(cabinNumber.toString());
                                chdUnlimitedFly.setSortPriority(20);
                                chdUnlimitedFly.setCabinLabelList2(handConfig.getLabelInfoConfig().getChdUnlimitedFlyLabelInfos());
                                // 包含成人畅飞卡排第二，不包含排第一
                                adtCabinFareList.add(packageCabinTypes.contains(FareBasisEnum.ADT_UNLIMITED_FARE.getFareBasisCode()) ? 1 : 0, chdUnlimitedFly);
                            }
                        }
                        if (!packageCabinTypes.contains(FareBasisEnum.ADT_UNLIMITED_FARE.getFareBasisCode())) {
                            // 新版畅飞卡更改畅飞类型
                            if (packageCabinTypes.contains(FareBasisEnum.UNLIMITED_FARE_2.getFareBasisCode())) {
                                adtCabinFareList.stream().filter(cabinFare ->
                                        PackageTypeEnum.ADT_UNLIMITED_FLY.getPackType().equals(cabinFare.getCabinType())).forEach(cabinFare ->
                                        cabinFare.setCabinType(PackageTypeEnum.UNLIMITED_FARE_V2.getPackType())
                                );
                            } else if (packageCabinTypes.contains(FareBasisEnum.UNLIMITED_FARE_2_ADT_PAY.getFareBasisCode())) {// 成人需付费的类型
                                StringBuilder cabinNumber = new StringBuilder("0");
                                Optional<CabinFare> xCabin = adtCabinFareList.stream().filter(cabinFare -> handConfig.getFreeTicketCabin().equals(cabinFare.getCabinCode())).findFirst();
                                if (xCabin.isPresent()) {
                                    cabinNumber.setLength(0);
                                    cabinNumber.append(xCabin.get().getCabinNumber());
                                    adtCabinFareList.remove(xCabin.get());
                                }
                                Optional<CabinFare> inCabin = adtCabinFareList.stream().filter(cabinFare -> handConfig.getAwardFlyFreeTicketCabin().contains(cabinFare.getCabinCode())).findFirst();
                                if (inCabin.isPresent()) {
                                    cabinNumber.setLength(0);
                                    cabinNumber.append(inCabin.get().getCabinNumber());
                                    adtCabinFareList.remove(inCabin.get());
                                }
                                CabinFare commonMinCabin = adtCabinFareList.stream()
                                        .filter(cabinFare -> "Y".equals(cabinFare.getCabinClass()) && PackageTypeEnum.CABIN_NORMAL.getPackType().equals(cabinFare.getCabinType()))
                                        .min(Comparator.comparingDouble(CabinFare::getPriceValue)).orElse(null);
                                if (null != commonMinCabin) {
                                    // 儿童随心飞运价定义为最低价格的复制
                                    CabinFare adtUnlimitedFly = new CabinFare();
                                    BeanUtils.copyNotNullProperties(commonMinCabin, adtUnlimitedFly);
                                    adtUnlimitedFly.setCabinType(PackageTypeEnum.UNLIMITED_FARE_V2.getPackType());
                                    adtUnlimitedFly.setCabinNumber(cabinNumber.toString());
                                    adtUnlimitedFly.setSortPriority(10);
                                    adtUnlimitedFly.setCabinLabelList2(handConfig.getLabelInfoConfig().getAdtUnlimitedFlyLabelInfos());
                                    ChildRule childRule = new ChildRule();
                                    if (null != adtUnlimitedFly.getChildRule()) {
                                        BeanUtils.copyNotNullProperties(adtUnlimitedFly.getChildRule(), childRule);
                                    }
                                    childRule.setRefundRuleList(Lists.newArrayList(new ChangeAndRefundRule("起飞前", -1D, "不得更改"),
                                            new ChangeAndRefundRule("起飞后", -1D, "仅退税费")));
                                    childRule.setChangeRuleList(Lists.newArrayList(new ChangeAndRefundRule("起飞前", -1D, "不得更改"),
                                            new ChangeAndRefundRule("起飞后", -1D, "仅退税费")));
                                    adtUnlimitedFly.setChildRule(childRule);
                                    // 包含成人畅飞卡排第二，不包含排第一
                                    adtCabinFareList.add(0, adtUnlimitedFly);
                                }
                            }
                            // 不包含成人畅飞卡时删除成人X舱
                            adtCabinFareList = adtCabinFareList.stream().filter(cabinFare ->
                                    !PackageTypeEnum.ADT_UNLIMITED_FLY.getPackType().equals(cabinFare.getCabinType())).collect(Collectors.toList());
                        }
                        adtCabinFareList = sortPrice(adtCabinFareList);
                    }
                }
                //处理婴儿免费行李额
                try {
                    dealFreeBag(adtCabinFareList, chdInfCabinFareList, interFlag);
                } catch (Exception e) {
                    log.error("处理婴儿免费行李额异常,错误信息:{},成人舱位:{},儿童婴儿舱位:{}", e, JsonMapper.buildNonNullMapper().toJson(adtCabinFareList), JsonMapper.buildNonNullMapper().toJson(chdInfCabinFareList));
                }
                //处理儿童婴儿的退改说明
                setChildInfRule(adtCabinFareList, chdInfCabinFareList, v2FlightInfo.getCarrierNo());
            }
            if ((FlightQueryTypeEnum.CHANGE.getType().equals(operationType) && (isDisneyFlag == null ? false : isDisneyFlag))) {
                adtCabinFareList = filterDisneyFare(adtCabinFareList);
                cabinOLDFareList = filterDisneyFare(cabinOLDFareList);
                chdInfCabinFareList = filterDisneyFare(chdInfCabinFareList);
            }
            flightInfo.setCabinFareList(adtCabinFareList == null ? new ArrayList<>() : adtCabinFareList);
            CabinFare[] chdinfFare = new CabinFare[chdInfCabinFareList.size()];
            flightInfo.setCabinCHDINFFareList(chdInfCabinFareList.toArray(chdinfFare));
            flightInfo.setCabinOLDFareList(cabinOLDFareList);
            flightInfo.setCabinGMJCFareList(gmjcCabinFareList == null ? new ArrayList<>() : gmjcCabinFareList);
            flightInfo.setFareType(FareTypeEnum.SIMPLE.getFare());//国内航班暂定为simple
            flightInfo.setFlightNoIconList(AVObjectConvertV3.setFlightNoIconList(flightInfo.getFlightNo(), handConfig.getAirCompany()));
            flightInfo.setCarrierFlightNoIconList(AvObjectConvertCommon.setFlightNoIconList(handConfig.getAirCompany(), flightInfo.getCarrierNo()));

            flightList.add(flightInfo);
        }
        flightList = sortDeparttime(flightList);
        return flightList;
    }

    private static List<CabinFare> filterDisneyFare(List<CabinFare> adtCabinFareList) {
        if (CollectionUtils.isNotEmpty(adtCabinFareList)) {
            return adtCabinFareList.stream().filter(filterDisneyFare -> PackageTypeEnum.DISNEY_FARE.getPackType().equals(filterDisneyFare.getCabinType())).collect(Collectors.toList());
        } else {
            return Lists.newArrayList();
        }

    }

    private static CabinFare getCabinFareDisplayPrice(List<CabinFare> adtCabinFareList) {
        return adtCabinFareList.stream().filter(i -> i != null && i.getPriceValue() != null).min(Comparator.comparingDouble(CabinFare::getPriceValue)).orElse(null);
    }

    /**
     * 过滤茅台特殊舱位
     *
     * @param handConfig
     * @param flightNo      航班号
     * @param cabinFareList 成人舱位列表
     * @return
     */
    private static List<CabinFare> filterMaotaiCabin(HandConfig handConfig, String flightNo, String flightDate, List<CabinFare> cabinFareList) {
        List<CabinFare> maotaiCabinFareList = null;
        MaoTaiConfig outMaoTaiConfig = handConfig.getOutMaoTaiConfig();
        MaoTaiConfig inMaoTaiConfig = handConfig.getInMaoTaiConfig();
        if (outMaoTaiConfig != null) {
            String startDateStr = outMaoTaiConfig.getStartDate();
            String endDateStr = outMaoTaiConfig.getEndDate();
            //活动时间比较
            Date date = new Date();
            Date startDate = DateUtils.toDate(startDateStr, DateUtils.YYYY_MM_DD_PATTERN);
            Date endDate = DateUtils.toDate(endDateStr, DateUtils.YYYY_MM_DD_PATTERN);
            if (date.getTime() >= startDate.getTime() && date.getTime() < endDate.getTime()) {
                maotaiCabinFareList = filterCabinList(outMaoTaiConfig, flightNo, flightDate, cabinFareList);
            }
        }
        if (inMaoTaiConfig != null) {
            String startDateStr = inMaoTaiConfig.getStartDate();
            String endDateStr = inMaoTaiConfig.getEndDate();
            //活动时间比较
            Date date = new Date();
            Date startDate = DateUtils.toDate(startDateStr, DateUtils.YYYY_MM_DD_PATTERN);
            Date endDate = DateUtils.toDate(endDateStr, DateUtils.YYYY_MM_DD_PATTERN);
            if (date.getTime() >= startDate.getTime() && date.getTime() < endDate.getTime()) {
                maotaiCabinFareList = filterCabinList(inMaoTaiConfig, flightNo, flightDate, cabinFareList);
            }
        }
        return maotaiCabinFareList;
    }

    private static List<CabinFare> filterCabinList(MaoTaiConfig maoTaiConfig, String flightNo, String flightDate, List<CabinFare> cabinFareList) {
        List<CabinFare> maoTaiCabinFareList = new ArrayList();
        //航班号匹配
        if (StringUtils.isNotBlank(maoTaiConfig.getFlightNo()) && maoTaiConfig.getFlightNo().contains(flightNo)) {
            //航班时间
            String flightStartDateStr = maoTaiConfig.getFlightStartDate();
            String flightEndDateStr = maoTaiConfig.getFlightEndDate();
            Date flightStartDate = DateUtils.toDate(flightStartDateStr, DateUtils.YYYY_MM_DD_PATTERN);
            Date flightEndDate = DateUtils.toDate(flightEndDateStr, DateUtils.YYYY_MM_DD_PATTERN);
            Date curFlightDate = DateUtils.toDate(flightDate, DateUtils.YYYY_MM_DD_PATTERN);
            if (curFlightDate.getTime() >= flightStartDate.getTime() && curFlightDate.getTime() < flightEndDate.getTime()) {
                for (CabinFare cabinFare : cabinFareList) {
                    //舱位匹配
                    if (StringUtils.isNotBlank(maoTaiConfig.getCabins()) && maoTaiConfig.getCabins().contains(cabinFare.getCabinCode())) {
                        cabinFare.setActivityLabel(maoTaiConfig.getLabelAndRuleInfo());
                        maoTaiCabinFareList.add(cabinFare);
                    }
                }
            }
        }
        return maoTaiCabinFareList;
    }

    /**
     * 一舱多价及往返打包处理
     *
     * @param adtCabinFareList 转换后的成人舱位列表
     * @param v2FlightInfo
     */
    private static List<CabinFare> otherCabinPrice(List<CabinFare> adtCabinFareList, V2FlightInfo v2FlightInfo, String airCode,
                                                   PtQueryFlightFareResponse res, HandConfig config, TempleteConfig templeteConfig, String operationType, List<String> packageCabinTypes) {
        if (StringUtil.isNullOrEmpty(adtCabinFareList)) {
            return new ArrayList<>();
        }
        List<CabinFare> packageCabinFareList = null;
        if (!StringUtil.isNullOrEmpty(v2FlightInfo.getCabinFarePackageList())) {
            //舱位列表转换
            packageCabinFareList = toCabinFareList(v2FlightInfo.getCabinFarePackageList(), airCode, "", res, "", config, true, v2FlightInfo.getFlightNo(), v2FlightInfo.getFType(),
                    v2FlightInfo);
        }
        List<CabinFare> cabinFareList = new ArrayList<>();
        List<CabinFare> finalPackageCabinFareList = packageCabinFareList;
        boolean hasWifi = false;
        boolean hasCoupon = false;
        int index = 0; // 排序序号
        List<CabinFare> highRebateCabinFareList = Lists.newArrayList();
        List<CabinFare> memberCabinFareList = Lists.newArrayList();
        List<CabinFare> youthCabinFareList = Lists.newArrayList();
        List<CabinFare> multiDiscountFareList = Lists.newArrayList();
        List<CabinFare> baggageFareList = Lists.newArrayList();
        List<CabinFare> memberSecKillFareList = Lists.newArrayList();
        List<CabinFare> disneyFareList = Lists.newArrayList();
        List<CabinFare> oldDisneyFareList = Lists.newArrayList();
        List<CabinFare> airportTransferList = Lists.newArrayList();
        List<CabinFare> firstRideMemberList = Lists.newArrayList();
        for (CabinFare cabinFare : adtCabinFareList) {
            cabinFareList.add(cabinFare);
            // 对CabinFare的会员专享的进行打标
            if (StringUtils.isNotBlank(cabinFare.getFareBasis()) &&
                    cabinFare.getFareBasis().endsWith(FareBasisEnum.MEMBER_FARE.getFareBasisCode())
                    && cabinFare.getFareBasis().endsWith(FareBasisEnum.MEMBER_FARE_YJ.getFareBasisCode())) {
                cabinFare.setCabinType(PackageTypeEnum.MEMBER_FARE.getPackType());
                cabinFare.setActivityLabel(config.getMemberFareLabel());
            }
            //查询当前舱位的其他产品
            Map<String, List<CabinFare>> packCabinFareMap = filterPackageCabin(v2FlightInfo, cabinFare, finalPackageCabinFareList, config, templeteConfig);
            if (!packCabinFareMap.isEmpty()) {
                List<CabinFare> wifiCabinFareList = packCabinFareMap.get(FareBasisEnum.WIFI_FAREBASIS.getFareBasisCode());
                List<CabinFare> couponCabinFareList = packCabinFareMap.get(FareBasisEnum.COUPON_FAREBASIS.getFareBasisCode());
                if (CollectionUtils.isNotEmpty(packCabinFareMap.get(FareBasisEnum.YOUTH_FARE.getFareBasisCode()))) {
                    youthCabinFareList.addAll(packCabinFareMap.get(FareBasisEnum.YOUTH_FARE.getFareBasisCode()));
                }
                if (CollectionUtils.isNotEmpty(packCabinFareMap.get(FareBasisEnum.MEMBER_FARE.getFareBasisCode()))) {
                    memberCabinFareList.addAll(packCabinFareMap.get(FareBasisEnum.MEMBER_FARE.getFareBasisCode()));
                }
                if (CollectionUtils.isNotEmpty(packCabinFareMap.get(FareBasisEnum.DISNEY_FARE.getFareBasisCode()))) {
                    disneyFareList.addAll(packCabinFareMap.get(FareBasisEnum.DISNEY_FARE.getFareBasisCode()));
                }
                if (CollectionUtils.isNotEmpty(packCabinFareMap.get(FareBasisEnum.DISNEY_FARE.getFareBasisCode() + "OLD"))) {
                    oldDisneyFareList.addAll(packCabinFareMap.get(FareBasisEnum.DISNEY_FARE.getFareBasisCode() + "OLD"));
                }
                if (CollectionUtils.isNotEmpty(packCabinFareMap.get(FareBasisEnum.HIGH_REBATE.getFareBasisCode()))) {
                    highRebateCabinFareList.addAll(packCabinFareMap.get(FareBasisEnum.HIGH_REBATE.getFareBasisCode()));
                }
                if (CollectionUtils.isNotEmpty(packCabinFareMap.get(FareBasisEnum.MULTI_DISCOUNT.getFareBasisCode()))) {
                    multiDiscountFareList.addAll(packCabinFareMap.get(FareBasisEnum.MULTI_DISCOUNT.getFareBasisCode()));
                }
                if (CollectionUtils.isNotEmpty(packCabinFareMap.get(FareBasisEnum.BAGGAGE_FARE.getFareBasisCode()))) {
                    baggageFareList.addAll(packCabinFareMap.get(FareBasisEnum.BAGGAGE_FARE.getFareBasisCode()));
                }
                if (CollectionUtils.isNotEmpty(packCabinFareMap.get(FareBasisEnum.MEMBER_SEC_KILL.getFareBasisCode()))) {
                    memberSecKillFareList.addAll(packCabinFareMap.get(FareBasisEnum.MEMBER_SEC_KILL.getFareBasisCode()));
                }

                if (CollectionUtils.isNotEmpty(packCabinFareMap.get(FareBasisEnum.AIRPORT_TRANSFER_FARE.getFareBasisCode()))) {
                    airportTransferList.addAll(packCabinFareMap.get(FareBasisEnum.AIRPORT_TRANSFER_FARE.getFareBasisCode()));
                }
                if (CollectionUtils.isNotEmpty(packCabinFareMap.get(FareBasisEnum.FIRST_RIDE_MEMBER_FARE.getFareBasisCode()))) {
                    firstRideMemberList.addAll(packCabinFareMap.get(FareBasisEnum.FIRST_RIDE_MEMBER_FARE.getFareBasisCode()));
                }

                //表示当前舱位是否已有打包产品
                boolean hasPack = false;
                //默认先看wifi产品
                // 打包产品价格比原舱位高，排在原舱位之后
                if (CollectionUtils.isNotEmpty(wifiCabinFareList) && SALE_WIFI_FLAG.equals(config.getSaleWifiCabin()) && packageCabinTypes.contains(FareBasisEnum.WIFI_FAREBASIS.getFareBasisCode())
                        && !hasWifi) {
                    Optional<CabinFare> wifiCabinFare = wifiCabinFareList.stream().min(Comparator.comparing(CabinFare::getPriceValue));
                    if (wifiCabinFare.isPresent()) {
                        cabinFareList.add(++index, wifiCabinFare.get());
                        hasPack = true;
                        hasWifi = true;
                    }
                }
                //当前航班无相同产品且当前舱位也不包含其他产品
                if (!StringUtil.isNullOrEmpty(couponCabinFareList) && !hasCoupon && !hasPack && packageCabinTypes.contains(FareBasisEnum.COUPON_FAREBASIS.getFareBasisCode())) {
                    cabinFareList.add(++index, couponCabinFareList.get(0));
                    hasCoupon = true;
                }
            }
            index++;
        }
        Optional<CabinFare> memberCabinFare = memberCabinFareList.stream().filter(cabinFare -> !"J".equalsIgnoreCase(cabinFare.getCabinClass())).min(Comparator.comparing(CabinFare::getPriceValue));
        if (packageCabinTypes.contains(FareBasisEnum.MEMBER_FARE.getFareBasisCode()) && memberCabinFare.isPresent()) {
            cabinFareList.add(memberCabinFare.get());
        }
        //2021-07-13 公务舱会员专享运价展示
        Optional<CabinFare> jMemberCabinFare = memberCabinFareList.stream().filter(cabinFare -> "J".equalsIgnoreCase(cabinFare.getCabinClass())).min(Comparator.comparing(CabinFare::getPriceValue));
        if (packageCabinTypes.contains(FareBasisEnum.MEMBER_FARE.getFareBasisCode()) && jMemberCabinFare.isPresent()) {
            cabinFareList.add(jMemberCabinFare.get());
        }
        Optional<CabinFare> atdDisneyFareJ = disneyFareList.stream().filter(cabinFare -> "J".equalsIgnoreCase(cabinFare.getCabinClass())).min(Comparator.comparing(CabinFare::getPriceValue));
        if (packageCabinTypes.contains(FareBasisEnum.DISNEY_FARE.getFareBasisCode()) && atdDisneyFareJ.isPresent()) {
            cabinFareList.add(atdDisneyFareJ.get());
        }
        Optional<CabinFare> atdDisneyFare = disneyFareList.stream().filter(cabinFare -> !"J".equalsIgnoreCase(cabinFare.getCabinClass())).min(Comparator.comparing(CabinFare::getPriceValue));
        if (packageCabinTypes.contains(FareBasisEnum.DISNEY_FARE.getFareBasisCode()) && atdDisneyFare.isPresent()) {
            cabinFareList.add(atdDisneyFare.get());
        }

        Optional<CabinFare> oldDisneyFareJ = oldDisneyFareList.stream().filter(cabinFare -> "J".equalsIgnoreCase(cabinFare.getCabinClass())).min(Comparator.comparing(CabinFare::getPriceValue));
        if (packageCabinTypes.contains(FareBasisEnum.DISNEY_FARE.getFareBasisCode()) && oldDisneyFareJ.isPresent()) {
            cabinFareList.add(oldDisneyFareJ.get());
        }
        Optional<CabinFare> oldDisneyFare = oldDisneyFareList.stream().filter(cabinFare -> !"J".equalsIgnoreCase(cabinFare.getCabinClass())).min(Comparator.comparing(CabinFare::getPriceValue));
        if (packageCabinTypes.contains(FareBasisEnum.DISNEY_FARE.getFareBasisCode()) && oldDisneyFare.isPresent()) {
            cabinFareList.add(oldDisneyFare.get());
        }

        Optional<CabinFare> youthCabinFare = youthCabinFareList.stream().min(Comparator.comparing(CabinFare::getPriceValue));
        if (packageCabinTypes.contains(FareBasisEnum.YOUTH_FARE.getFareBasisCode()) && youthCabinFare.isPresent()) {
            cabinFareList.add(youthCabinFare.get());
        }
        Optional<CabinFare> multiDiscountFare = multiDiscountFareList.stream().min(Comparator.comparing(CabinFare::getPriceValue));
        if (packageCabinTypes.contains(FareBasisEnum.MULTI_DISCOUNT.getFareBasisCode()) && multiDiscountFare.isPresent()) {
            cabinFareList.add(multiDiscountFare.get());
        }
        Optional<CabinFare> baggageFare = baggageFareList.stream().min(Comparator.comparing(CabinFare::getPriceValue));
        if (packageCabinTypes.contains(FareBasisEnum.BAGGAGE_FARE.getFareBasisCode()) && baggageFare.isPresent()) {
            cabinFareList.add(baggageFare.get());
        }

        Optional<CabinFare> airportTransferFare = airportTransferList.stream().min(Comparator.comparing(CabinFare::getPriceValue));
        if (packageCabinTypes.contains(FareBasisEnum.AIRPORT_TRANSFER_FARE.getFareBasisCode()) && airportTransferFare.isPresent()) {
            cabinFareList.add(airportTransferFare.get());
        }

        Optional<CabinFare> firstRideMemberFareJ = firstRideMemberList.stream().filter(cabinFare -> "J".equalsIgnoreCase(cabinFare.getCabinClass())).min(Comparator.comparing(CabinFare::getPriceValue));
        if (packageCabinTypes.contains(FareBasisEnum.FIRST_RIDE_MEMBER_FARE.getFareBasisCode()) && firstRideMemberFareJ.isPresent()) {
            cabinFareList.add(firstRideMemberFareJ.get());
        }
        Optional<CabinFare> firstRideMemberFare = firstRideMemberList.stream().filter(cabinFare -> !"J".equalsIgnoreCase(cabinFare.getCabinClass())).min(Comparator.comparing(CabinFare::getPriceValue));
        if (packageCabinTypes.contains(FareBasisEnum.FIRST_RIDE_MEMBER_FARE.getFareBasisCode()) && firstRideMemberFare.isPresent()) {
            cabinFareList.add(firstRideMemberFare.get());
        }


        Optional<CabinFare> memberSecKillFare = memberSecKillFareList.stream().min(Comparator.comparing(CabinFare::getPriceValue));
        memberSecKillFare.ifPresent(cabinFareList::add);
        // 高舱高返置顶展示
        Optional<CabinFare> highRebateFare = highRebateCabinFareList.stream().min(Comparator.comparing(CabinFare::getPriceValue));
        if (packageCabinTypes.contains(FareBasisEnum.HIGH_REBATE.getFareBasisCode()) && highRebateFare.isPresent()) {
            cabinFareList.add(0, highRebateFare.get());
        }
        return cabinFareList;
    }


    /**
     * 转换儿童会员价
     *
     * @param chdInfCabinFareList
     * @param v2FlightInfo
     * @param airCode
     * @param res
     * @param handConfig
     * @param templeteConfig
     * @param operationType
     * @param packageCabinTypes
     * @param config
     * @return
     */
    private static List<CabinFare> chdCabinPrice(List<CabinFare> chdInfCabinFareList,
                                                 V2FlightInfo v2FlightInfo,
                                                 String airCode,
                                                 PtQueryFlightFareResponse res,
                                                 HandConfig handConfig,
                                                 TempleteConfig templeteConfig,
                                                 String operationType,
                                                 List<String> packageCabinTypes,
                                                 HandConfig config) {
        String flightNo = v2FlightInfo.getFlightNo();
        List<V2CabinFare> cabinCHDINFFareList = v2FlightInfo.getCabinCHDINFPackageFareList();
        HashMap<String, Fare> fareDic = res.getFareDic();
        if (CollectionUtils.isNotEmpty(cabinCHDINFFareList)) {
            for (V2CabinFare v2CabinFare : cabinCHDINFFareList) {
                Fare fare = fareDic.get(v2CabinFare.getFareKey());
                // 2022-01-10 增加儿童会员秒杀
                if (!StringUtil.isNullOrEmpty(fare.getFareBasis())) {
                    CabinFare cabinFare = toCabinFare(v2CabinFare, fare, res.getInterFlag(), config, flightNo, airCode, v2FlightInfo);
                    // 是否儿童会员秒杀
                    if (StringUtils.isNotBlank(cabinFare.getFareBasis()) && null != v2CabinFare.getCabinCode() &&
                            v2CabinFare.getCabinCode().equals(cabinFare.getCabinCode()) &&
                            AVObjectConvert.memberSecKillCabinFare(cabinFare.getCabinCode(), cabinFare.getTourCode(), config)) {
                        cabinFare.setCabinType(PackageTypeEnum.MEMBER_SEC_KILL.getPackType());
                        chdInfCabinFareList.add(cabinFare);
                        continue;
                    }
                    if (fare.getFareBasis().endsWith(FareBasisEnum.MEMBER_FARE.getFareBasisCode())) {
                        cabinFare.setCabinType(PackageTypeEnum.MEMBER_FARE.getPackType());
                        chdInfCabinFareList.add(cabinFare);
                    }
                    if (!StringUtil.isNullOrEmpty(v2CabinFare.getSpecialFareCode())) {
                        if (FareBasisEnum.DISNEY_FARE.getFareBasisCode().equals(v2CabinFare.getSpecialFareCode())) {
                            cabinFare.setCabinType(PackageTypeEnum.DISNEY_FARE.getPackType());
                            chdInfCabinFareList.add(cabinFare);
                        }
                    }
                }

            }
        }
        return chdInfCabinFareList;
    }

    /**
     * 根据原始舱位过滤同等舱位的其他运价
     *
     * @param cabinFare
     * @param packageCabinFareList
     * @return
     */
    private static Map<String, List<CabinFare>> filterPackageCabin(V2FlightInfo v2FlightInfo, CabinFare cabinFare, List<CabinFare> packageCabinFareList,
                                                                   HandConfig config, TempleteConfig templeteConfig) {
        Map<String, List<CabinFare>> fareBasisMap = new HashMap<>();
        if (!StringUtil.isNullOrEmpty(packageCabinFareList) && config != null) {
            //-----wifi处理-------------
            List<CabinFare> wifiList = packageCabinFareList.stream()
                    .filter(c -> c.getFareBasis() != null && c.getFareBasis().endsWith(FareBasisEnum.WIFI_FAREBASIS.getFareBasisCode()))
                    .collect(Collectors.toList());
            if (!StringUtil.isNullOrEmpty(wifiList) && "789".equals(v2FlightInfo.getFType())) {
                wifiList.forEach(packCabin -> {
                    //获取机票赠送的券的信息
                    PackagePro packagePro = OrderDetailConvert.getPackagePro(config, packCabin.getFareBasis());
                    packCabin.setCabinType(PackageTypeEnum.WIFI_FARE.getPackType());

                    if (packagePro != null) {
                        formatPackCabin(packCabin, cabinFare, packagePro, templeteConfig);
                    }
                    //设置wifi运价图标和详情
                    LabelAndRuleInfo labelAndRuleInfo = config.getWifiLabelAndRuleInfo();
                    packCabin.setActivityLabel(labelAndRuleInfo);
                    //设置订单页详情页wifi信息展示
                    TravelPrivilege travelPrivilege = config.getWifiTravelPrivilege();
                    List<TravelPrivilege> wifiTravelPrivilege = new ArrayList<>();
                    wifiTravelPrivilege.add(travelPrivilege);
                    packCabin.setWifiTravelPrivilege(wifiTravelPrivilege);
                });
                fareBasisMap.put(FareBasisEnum.WIFI_FAREBASIS.getFareBasisCode(), wifiList);
            }
            //----wifi处理结束------

            Predicate<CabinFare> predicateCoupon = c -> c.getFareBasis() != null && c.getFareBasis().endsWith(FareBasisEnum.COUPON_FAREBASIS.getFareBasisCode())
                    && cabinFare.getCabinCode().equals(c.getCabinCode());
            List<CabinFare> couponCabinFarePackageList = packageCabinFareList.stream().filter(predicateCoupon).collect(Collectors.toList());
            if (!StringUtil.isNullOrEmpty(couponCabinFarePackageList)) {
                couponCabinFarePackageList.forEach(packCabin -> {
                    //获取机票赠送的券的信息
                    PackagePro packagePro = OrderDetailConvert.getPackagePro(config, packCabin.getFareBasis());
                    if (packagePro != null) {
                        formatPackCabin(packCabin, cabinFare, packagePro, templeteConfig);
                    }
                });
                fareBasisMap.put(FareBasisEnum.COUPON_FAREBASIS.getFareBasisCode(), couponCabinFarePackageList);
            }
            Predicate<CabinFare> predicateYouth = c -> c.getFareBasis() != null && c.getFareBasis().endsWith(FareBasisEnum.YOUTH_FARE.getFareBasisCode())
                    && cabinFare.getCabinCode().equals(c.getCabinCode());
            List<CabinFare> youthCabinFarePackageList = packageCabinFareList.stream().filter(predicateYouth).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(youthCabinFarePackageList)) {
                youthCabinFarePackageList.forEach(packCabin -> {
                    packCabin.setCabinType(PackageTypeEnum.YOUTH_FARE.getPackType());
                    packCabin.setActivityLabel(config.getYouthFareLabel());
                    PackagePro packagePro = OrderDetailConvert.getPackagePro(config, packCabin.getFareBasis());
                    if (packagePro != null) {
                        formatPackCabin(packCabin, cabinFare, packagePro, templeteConfig);
                    }
                });
                fareBasisMap.put(FareBasisEnum.YOUTH_FARE.getFareBasisCode(), youthCabinFarePackageList);
            }
            // 2021-10-19 是否会员秒杀舱位
            // 经济舱会员运价筛选条件增加非会员秒杀运价
            Predicate<CabinFare> predicateMember = c -> c.getFareBasis() != null && c.getFareBasis().endsWith(FareBasisEnum.MEMBER_FARE.getFareBasisCode())
                    && cabinFare.getCabinCode().equals(c.getCabinCode()) && c.getPriceValue() < c.getYPrice()
                    && !AVObjectConvert.memberSecKillCabinFare(c.getCabinCode(), c.getTourCode(), config);
            //2021-07-13 公务舱会员专享运价
            if ("J".equalsIgnoreCase(cabinFare.getCabinClass())) {
                predicateMember = c -> c.getFareBasis() != null && c.getFareBasis().endsWith(FareBasisEnum.MEMBER_FARE.getFareBasisCode())
                        && cabinFare.getCabinCode().equals(c.getCabinCode());
            }
            Optional<CabinFare> memberCabinFarePackage = packageCabinFareList.stream().filter(predicateMember).min(Comparator.comparing(CabinFare::getPriceValue));
            if (memberCabinFarePackage.isPresent()) {
                CabinFare packCabin = memberCabinFarePackage.get();
                packCabin.setCabinType(PackageTypeEnum.MEMBER_FARE.getPackType());
                packCabin.setActivityLabel(config.getMemberFareLabel());
                PackagePro packagePro = OrderDetailConvert.getPackagePro(config, packCabin.getFareBasis());
                if (packagePro != null) {
                    formatPackCabin(packCabin, cabinFare, packagePro, templeteConfig);
                }
                fareBasisMap.put(FareBasisEnum.MEMBER_FARE.getFareBasisCode(), Collections.singletonList(packCabin));
            }
            // 2021-10-19 会员秒杀
            Predicate<CabinFare> memberSecKillPredicate = c -> c.getFareBasis() != null && cabinFare.getCabinCode().equals(c.getCabinCode())
                    && AVObjectConvert.memberSecKillCabinFare(c.getCabinCode(), c.getTourCode(), config);
            Optional<CabinFare> memberSecKillFarePackage = packageCabinFareList.stream().filter(memberSecKillPredicate).min(Comparator.comparing(CabinFare::getPriceValue));
            if (memberSecKillFarePackage.isPresent()) {
                CabinFare packCabin = memberSecKillFarePackage.get();
                packCabin.setCabinType(PackageTypeEnum.MEMBER_SEC_KILL.getPackType());
                packCabin.setActivityLabel(config.getMemberSecKillFareLabel());
                fareBasisMap.put(FareBasisEnum.MEMBER_SEC_KILL.getFareBasisCode(), Collections.singletonList(packCabin));
            }
            // 高舱高返
            Predicate<CabinFare> predicateHighRebate = c -> c.getFareBasis() != null && c.getFareBasis().endsWith(FareBasisEnum.HIGH_REBATE.getFareBasisCode())
                    && cabinFare.getCabinCode().equals(c.getCabinCode());
            List<CabinFare> highRebateCabinFarePackageList = packageCabinFareList.stream().filter(predicateHighRebate).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(highRebateCabinFarePackageList)) {
                highRebateCabinFarePackageList.forEach(packCabin -> {
                    packCabin.setCabinType(PackageTypeEnum.HIGH_REBATE.getPackType());
                    PackagePro packagePro = OrderDetailConvert.getPackagePro(config, packCabin.getFareBasis());
                    if (packagePro != null) {
                        formatPackCabin(packCabin, cabinFare, packagePro, templeteConfig);
                    }
                    List<LabelInfo> cabinLabelList = Lists.newArrayList();
                    StringBuilder score = new StringBuilder();
                    if (CollectionUtils.isNotEmpty(packCabin.getTravelPrivilegeList())) {
                        packCabin.getTravelPrivilegeList().forEach(travelPrivilege -> {
                            if (StringUtils.isNotBlank(travelPrivilege.getType())) {
                                switch (travelPrivilege.getType()) {
                                    case "coupon":
                                        LabelInfo labelInfo1 = new LabelInfo("改期券", "", LabelTypeEnum.ACTIVITY.getType());
                                        labelInfo1.setLabelCode(travelPrivilege.getPrivilegeId());
                                        labelInfo1.setShowType("notShowInBookDetail");
                                        cabinLabelList.add(labelInfo1);
                                        break;
                                    case "compensation":
                                        LabelInfo labelInfo2 = new LabelInfo("延误赔", "", LabelTypeEnum.ACTIVITY.getType());
                                        labelInfo2.setLabelCode(travelPrivilege.getPrivilegeId());
                                        labelInfo2.setShowType("notShowInBookDetail");
                                        cabinLabelList.add(labelInfo2);
                                        break;
                                    case "returnscore":
                                        LabelInfo labelInfo3 = new LabelInfo("购票可获赠" + travelPrivilege.getNumber() + "积分", "", LabelTypeEnum.ACTIVITY.getType());
                                        labelInfo3.setLabelCode(travelPrivilege.getPrivilegeId());
                                        labelInfo3.setShowType("notShowInBookDetail");
                                        cabinLabelList.add(labelInfo3);
                                        score.setLength(0);
                                        score.append(travelPrivilege.getNumber());
                                        break;
                                }
                            }
                        });
                    }
                    LabelAndRuleInfo labelAndRuleInfo = JsonUtil.fromJson(config.getHighRebateFareLabel(), LabelAndRuleInfo.class);
                    if (null != labelAndRuleInfo) {
                        if (null != labelAndRuleInfo.getLabelDetail() && CollectionUtils.isNotEmpty(labelAndRuleInfo.getLabelDetail().getDetail())) {
                            labelAndRuleInfo.getLabelDetail().getDetail().stream().filter(labelDetail -> "积分累积".equals(labelDetail.getTitle()))
                                    .forEach(labelDetail -> labelDetail.setDescription(labelDetail.getDescription().replace("#score#", score)));
                        }
                        packCabin.setActivityLabel(labelAndRuleInfo);
                    }
                    packCabin.setCabinLabelList(cabinLabelList);
                    packCabin.setSortPriority(50);
                });
                fareBasisMap.put(FareBasisEnum.HIGH_REBATE.getFareBasisCode(), highRebateCabinFarePackageList);
            }
            // 多人特惠
            Predicate<CabinFare> predicateMulti = c -> c.getFareBasis() != null && c.getFareBasis().endsWith(FareBasisEnum.MULTI_DISCOUNT.getFareBasisCode())
                    && cabinFare.getCabinCode().equals(c.getCabinCode()) && c.getPriceValue() < c.getYPrice();
            Optional<CabinFare> multiCabinFarePackage = packageCabinFareList.stream().filter(predicateMulti).min(Comparator.comparing(CabinFare::getPriceValue));
            if (multiCabinFarePackage.isPresent()) {
                CabinFare packCabin = multiCabinFarePackage.get();
                packCabin.setCabinType(PackageTypeEnum.MULTI_DISCOUNT.getPackType());
                packCabin.setActivityLabel(config.getMultiDiscountFareLabel());
                fareBasisMap.put(FareBasisEnum.MULTI_DISCOUNT.getFareBasisCode(), Collections.singletonList(packCabin));
            }
            // 行李优享
            Predicate<CabinFare> predicateBaggage = c -> c.getFareBasis() != null && c.getFareBasis().endsWith(FareBasisEnum.BAGGAGE_FARE.getFareBasisCode())
                    && cabinFare.getCabinCode().equals(c.getCabinCode()) && c.getPriceValue() < c.getYPrice();
            Optional<CabinFare> baggageCabinFarePackage = packageCabinFareList.stream().filter(predicateBaggage).min(Comparator.comparing(CabinFare::getPriceValue));
            if (baggageCabinFarePackage.isPresent()) {
                CabinFare packCabin = baggageCabinFarePackage.get();
                packCabin.setCabinType(PackageTypeEnum.BAGGAGE_FARE.getPackType());
                packCabin.setActivityLabel(config.getBaggageFareLabel());
                fareBasisMap.put(FareBasisEnum.BAGGAGE_FARE.getFareBasisCode(), Collections.singletonList(packCabin));
            }

            //首乘会员价
            Predicate<CabinFare> firstRideMemberBaggage = c -> c.getFareBasis() != null && c.getFareBasis().endsWith(FareBasisEnum.FIRST_RIDE_MEMBER_FARE.getFareBasisCode());
            List<CabinFare> firstRideMemberPackage = packageCabinFareList.stream().filter(firstRideMemberBaggage).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(firstRideMemberPackage)) {
                firstRideMemberPackage.forEach(packCabin -> {
                    packCabin.setCabinType(PackageTypeEnum.FIRST_RIDE_MEMBER_FARE.getPackType());
                    packCabin.setActivityLabel(config.getFirstRideFareLabel());
                });
                fareBasisMap.put(FareBasisEnum.FIRST_RIDE_MEMBER_FARE.getFareBasisCode(), firstRideMemberPackage);
            }

            //迪士尼特惠
            Predicate<CabinFare> disney = c -> c.getSpecialFareCode() != null &&
                    FareBasisEnum.DISNEY_FARE.getFareBasisCode().equals(c.getSpecialFareCode());
            List<CabinFare> disneyPackages = packageCabinFareList.stream().filter(disney).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(disneyPackages)) {
                disneyPackages.stream().forEach(packCabin -> {
                    packCabin.setCabinType(PackageTypeEnum.DISNEY_FARE.getPackType());
                    packCabin.setActivityLabel(config.getDisneyFareLabel());
                    packCabin.setSortPriority(10);
                });
                List<CabinFare> disneyPackagesOLD = disneyPackages.stream().filter(packCabin -> "OLD".equals(packCabin.getPassengerIdentity())).collect(Collectors.toList());
                List<CabinFare> disneyPackagesAdt = disneyPackages.stream().filter(packCabin -> !"OLD".equals(packCabin.getPassengerIdentity())).collect(Collectors.toList());
                fareBasisMap.put(FareBasisEnum.DISNEY_FARE.getFareBasisCode() + "OLD", disneyPackagesOLD);
                fareBasisMap.put(FareBasisEnum.DISNEY_FARE.getFareBasisCode(), disneyPackagesAdt);
            }

            // 机加接送机
            Predicate<CabinFare> transferMulti = c -> c.getSpecialFareCode() != null &&
                    FareBasisEnum.AIRPORT_TRANSFER_FARE.getFareBasisCode().equals(c.getSpecialFareCode());
            Optional<CabinFare> transferPackage = packageCabinFareList.stream().filter(transferMulti).min(Comparator.comparing(CabinFare::getPriceValue));
            if (transferPackage.isPresent()) {
                List<LabelInfo> cabinLabelList = Lists.newArrayList();
                CabinFare packCabin = transferPackage.get();
                if (CollectionUtils.isNotEmpty(packCabin.getTravelPrivilegeList())) {
                    packCabin.getTravelPrivilegeList().forEach(travelPrivilege -> {
                        if (StringUtils.isNotBlank(travelPrivilege.getType()) && "machineCoupon".equalsIgnoreCase(travelPrivilege.getType())) {
                            LabelInfo labelInfo1 = new LabelInfo(travelPrivilege.getName(), "", LabelTypeEnum.ACTIVITY.getType());
                            labelInfo1.setLabelCode(travelPrivilege.getPrivilegeId());
                            labelInfo1.setShowType("notShowInBookDetail");
                            cabinLabelList.add(labelInfo1);
                        }
                    });
                }
                packCabin.setCabinType(PackageTypeEnum.AIRPORT_TRANSFER_FARE.getPackType());
                packCabin.setActivityLabel(config.getAirportTransferFareLabel());
                packCabin.setCabinLabelList(cabinLabelList);
                fareBasisMap.put(FareBasisEnum.AIRPORT_TRANSFER_FARE.getFareBasisCode(), Collections.singletonList(packCabin));
            }
        }


        return fareBasisMap;
    }

    //渲染同舱位公共信息及特殊信息
    private static void formatPackCabin(CabinFare packageCabinFare, CabinFare cabinFare, PackagePro packagePro, TempleteConfig templeteConfig) {
        packageCabinFare.setCabinClassName(cabinFare.getCabinClassName());
        packageCabinFare.setCabinLabel(cabinFare.getCabinLabel());
        packageCabinFare.setCabinType(PackageTypeEnum.CABIN_NORMAL.getPackType());
        packageCabinFare.setChangeDesc(templeteConfig != null ? templeteConfig.getSignatureConditions() : "");
        List<LabelInfo> cabinLabelList = new ArrayList();
        LabelInfo labelInfo = new LabelInfo(packagePro.getProCode(), packagePro.getProDesc(), packagePro.getProUrl(), LabelTypeEnum.ACTIVITY.getType(), packagePro.getProDetail());
        cabinLabelList.add(labelInfo);
        packageCabinFare.setCabinLabelList(cabinLabelList);
    }

    /**
     * 航班信息处理
     *
     * @param v2FlightInfo
     * @param airCompanyMap
     * @param aircraftModelMap
     * @return
     */
    private static FlightInfo toFlightInfo(V2FlightInfo v2FlightInfo, Map<String, AirCompany> airCompanyMap, Map<String, AircraftModel> aircraftModelMap, LocalCacheService localCacheService) {
        FlightInfo flightInfo = new FlightInfo();
        BeanUtils.copyProperties(v2FlightInfo, flightInfo);
        //机场城市处理
        AirPortInfoDto depAirport = localCacheService.getLocalAirport(v2FlightInfo.getDepAirport(), v2FlightInfo.getFlightDate());
        AirPortInfoDto arrAirport = localCacheService.getLocalAirport(v2FlightInfo.getArrAirport(), v2FlightInfo.getFlightDate());
        flightInfo.setDepAirportName(depAirport == null ? v2FlightInfo.getDepAirport() : depAirport.getAirPortName());
        flightInfo.setArrAirportName(arrAirport == null ? v2FlightInfo.getArrAirport() : arrAirport.getAirPortName());
        flightInfo.setDepCityName(depAirport == null ? v2FlightInfo.getDepAirport() : depAirport.getCityName());
        flightInfo.setArrCityName(arrAirport == null ? v2FlightInfo.getArrAirport() : arrAirport.getCityName());
        flightInfo.setDeptCountryNo(depAirport == null ? "" : depAirport.getCountryNo());
        flightInfo.setCountryNo(arrAirport == null ? "" : arrAirport.getCountryNo());
        //航司机型处理
        AircraftModel aircraftModel = FlightUtil.convertAircraftModel(aircraftModelMap, v2FlightInfo.getFType());
        flightInfo.setAircraftModel("机型" + v2FlightInfo.getFType());
        if (aircraftModel != null) {
            flightInfo.setAircraftModel(aircraftModel.getRemark());
        }
        flightInfo.setCarrierNoName("");
        AirCompany airCompany = FlightUtil.convertCompany(airCompanyMap, v2FlightInfo.getCarrierNo());
        if (airCompany != null) {
            flightInfo.setCarrierNoName(airCompany.getAirComName() + flightInfo.getCarrierNo());
        }
        //机上WIFI
        flightInfo.setWifiFlag(checkWifi(v2FlightInfo.getNwst()));
        if (flightInfo.isWifiFlag()) {
            flightInfo.setWifiName("机上WIFI");
        }
        //飞行时间计算
        //到达时区
        String arrZone = v2FlightInfo.getArrZone();
        //出发时区
        String depZone = v2FlightInfo.getDepZone();
        //出发时间(当地时间)
        String depTime = v2FlightInfo.getDepDateTime();
        //到达时间(当地时间)
        String arrTime = v2FlightInfo.getArrDateTime();
        if ((!StringUtil.isNullOrEmpty(arrZone)) &&
                (!StringUtil.isNullOrEmpty(depZone)) &&
                (!StringUtil.isNullOrEmpty(depTime)) &&
                (!StringUtil.isNullOrEmpty(arrTime))) {
            //添加夏、冬令时处理
            if (!depZone.equals(arrZone)) {
                depZone = FlightUtil.convertSummerOrWinterTime(depZone, v2FlightInfo.getDepDateTime(), depAirport);
                arrZone = FlightUtil.convertSummerOrWinterTime(arrZone, v2FlightInfo.getArrDateTime(), arrAirport);
            }
            long diff = DateUtils.calDuration(depTime, depZone, arrTime, arrZone);
            flightInfo.setDuration(diff < 0 ? 0 : diff);
            int days = DateUtils.diffDays(depTime.substring(0, 10), arrTime.substring(0, 10), DateUtils.YYYY_MM_DD_PATTERN);
            flightInfo.setDays(days < 0 ? 0 : days);
        }
        //经停城市处理
        if (!StringUtil.isNullOrEmpty(v2FlightInfo.getStopAirport())) {
            AirPortInfoDto stopAirPort = localCacheService.getLocalAirport(v2FlightInfo.getStopAirport());
            flightInfo.setStopAirportName(stopAirPort == null ? v2FlightInfo.getStopAirport() : stopAirPort.getCityName());
        }
        if (null == flightInfo.getTotalTax()) {
            flightInfo.setTotalTax(0d);
        }
        return flightInfo;
    }

    /**
     * 航班舱位低价处理，及舱位名称处理
     */
    private static List<CabinFare> setFlightMinPrice(List<CabinFare> cabinFareList, String interFlag, FlightInfo flightInfo, HandConfig handConfig, String operationType) {
        //生成航班的J、Y两舱最低价
        List<CabinFare> cabinFares = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(cabinFareList)) {
            //过滤筛选合适的舱位数量
            int ycabinLimit = handConfig.getYcabinLimit();
            int jcabinLimit = handConfig.getJcabinLimit();
            if (FlightQueryTypeEnum.CHANGE.getType().equals(operationType)) {
                ycabinLimit = handConfig.getChangeYCabinLimit();
            }
            cabinFares = filterCabin(cabinFareList, interFlag, ycabinLimit, jcabinLimit);
            //舱位价格已从低到高排序
            if (!StringUtil.isNullOrEmpty(cabinFares)) {
                flightInfo.setMinPrice(cabinFares.get(0).getPriceValue());
                flightInfo.setMinCabinClassName(cabinFares.get(0).getCabinClass());
            }
        }
        return cabinFares;
    }

    /**
     * 检验是否支持机上WIFI
     *
     * @param nwst
     * @return
     */
    private static boolean checkWifi(String nwst) {
        return "I".equals(nwst) || "W".equals(nwst) || "V".equals(nwst);
    }


    /**
     * 航班舱位运价信息处理  增加hangconfig
     *
     * @param ptV2CabinFareList
     * @param airCode           承运航司二字码
     * @param passType
     * @param cabin
     * @param config
     * @return
     */
    private static List<CabinFare> toCabinFareList(List<V2CabinFare> ptV2CabinFareList, String airCode, String passType,
                                                   PtQueryFlightFareResponse res, String cabin, HandConfig config, boolean filterSoldCabin, String flightNo, String flightType, V2FlightInfo flightInfo) {
        if (StringUtil.isNullOrEmpty(ptV2CabinFareList)) {
            return new ArrayList<>();
        }
        List<V2CabinFare> v2CabinFareList = ptV2CabinFareList;
        //航程类型匹配  国际的需要按照航程类型匹配，国内的可直接使用
        if (HandlerConstants.TRIP_TYPE_I.equals(res.getInterFlag())) {
            v2CabinFareList = ptV2CabinFareList.stream().filter(c -> c.getPriceRouteType().equals(res.getRouteType())).collect(Collectors.toList());
            if (StringUtil.isNullOrEmpty(v2CabinFareList)) {
                return new ArrayList<>();
            }
        }
        //儿童和婴儿分别过滤
        if (HandlerConstants.PASSENGER_TYPE_CHD.equals(passType) || HandlerConstants.PASSENGER_TYPE_INF.equals(passType)) {
            v2CabinFareList = v2CabinFareList.stream().filter(c -> c.getPassengerType().equals(passType)).collect(Collectors.toList());
        }


        List<CabinFare> cabinFareList = new LinkedList<>();
        for (V2CabinFare v2CabinFare : v2CabinFareList) {
            //获取指定的舱位运价
            if ((!StringUtil.isNullOrEmpty(cabin)) && (!v2CabinFare.getCabinCode().equals(cabin))) {
                continue;
            }
            //过滤无可售座位的舱位 非自愿改期、畅飞卡X舱时不过滤
            String cabinNumber = v2CabinFare.getCabinNumber();
            if (StringUtils.isNotBlank(cabinNumber) && filterSoldCabin && !config.getFreeTicketCabin().equals(v2CabinFare.getCabinCode()) && (cabinNumber.equals("0") || cabinNumber.equals("S"))) {
                continue;
            }
//            //机票候补测试代码，后面需要删除
//            if (res.getFlightInfoList().size()==1&&"HO1029".equals(res.getFlightInfoList().get(0).getFlightNo())&&!
//                    "S".equals(v2CabinFare.getCabinCode())){
//                continue;
//            }

            Map<String, Fare> fareMap = res.getFareDic();
            Fare fare = fareMap.get(v2CabinFare.getFareKey());
            if (fare == null) {
                continue;
            }
            //舱位信息处理
            CabinFare cabinFare = toCabinFare(v2CabinFare, fare, res.getInterFlag(), config, flightNo, airCode, flightInfo);
            cabinFare.setAlternateButton(false);
            int minute = DateUtils.dateminuteDiff(DateUtils.toAllDate(flightInfo.getDepDateTime()), new Date());

            // 候补 经济舱 且 运价类型 WAIT 且 距离起飞时间2小时以上
            boolean alternateButton = "Y".equals(cabinFare.getCabinClass()) && SystemConstants.WAIT.equals(flightInfo.getFlightFareType()) && minute > config.getWaitFareMinute();
            if (alternateButton) {
                cabinFare.setAlternateButton(true);
                cabinFare.setCabinNumber("A");
            }
            //国内 自有航班 品牌运价处理
            if (AirCompanyEnum.HO.getAirCompanyCode().equals(airCode) && HandlerConstants.TRIP_TYPE_D.equals(res.getInterFlag())) {
                brandFare(cabinFare, config, flightType);
                //成人畅飞标签
                if (config.getFreeTicketCabin().equals(cabinFare.getCabinCode()) && PassengerTypeEnum.ADT.getPassType().equals(cabinFare.getPassengerType())) {
                    cabinFare.setCabinLabelList2(config.getLabelInfoConfig().getAdtUnlimitedFlyLabelInfos());
                }
            }
            // 候补
            if (alternateButton) {
                cabinFare.setCabinLabelList2(config.getLabelInfoConfig().getWaitLabelInfos());
                formatLabelName(cabinFare);
            }
            cabinFareList.add(cabinFare);
        }
        return cabinFareList;
    }

    /**
     * 舱位航班处理
     *
     * @param v2CabinFare
     * @param fare
     * @param interFlag
     * @return
     */
    private static CabinFare toCabinFare(V2CabinFare v2CabinFare, Fare fare, String interFlag, HandConfig config, String flightNo, String airCode, V2FlightInfo v2FlightInfo) {
        CabinFare cabinFare = new CabinFare();
        BeanUtils.copyProperties(v2CabinFare, cabinFare);
        BeanUtils.copyProperties(fare, cabinFare);
        cabinFare.setFareID(fare.getFareID());
        cabinFare.setProductNo(fare.getProductNo());
        cabinFare.setDiscount(v2CabinFare.getDiscount());
        cabinFare.setSpecialFareCode(v2CabinFare.getSpecialFareCode());
        cabinFare.setInterFlag(interFlag);
        cabinFare.setFlightNo(flightNo);
        cabinFare.setTravelPrivilegeList(v2CabinFare.getPrivilegeList());
        //舱位退改规则处理  国内国际处理
        List<ChangeAndRefundRule> refundRuleList = AVObjectConvertV2.toRefundRules(fare.getRefundedRules(), interFlag);
        List<ChangeAndRefundRule> changeRuleList = AVObjectConvertV2.toChangeRules(fare.getChangeRules(), interFlag);
        FlightUtil.completeRule(refundRuleList, changeRuleList);
        // 免票X舱处理
        if (config.getFreeTicketCabin().equals(cabinFare.getCabinCode())) {
            refundRuleList.stream().filter(changeAndRefundRule -> -1d == changeAndRefundRule.getChangeFee()).forEach(changeAndRefundRule -> changeAndRefundRule.setRuleDesc("仅退税费"));
        }
        // 免票I,N处理
        String[] awardFlyFreeTicketCabin = config.getAwardFlyFreeTicketCabin().split(",");
        for (String s : awardFlyFreeTicketCabin) {
            if (s.equals(cabinFare.getCabinCode())) {
                refundRuleList.stream().filter(changeAndRefundRule -> -1d == changeAndRefundRule.getChangeFee()).forEach(changeAndRefundRule -> changeAndRefundRule.setRuleDesc("仅退税费"));
            }
        }
        cabinFare.setRefundRuleList(refundRuleList);
        cabinFare.setChangeRuleList(changeRuleList);
        //经济舱折扣显示处理
        if (!StringUtil.isNullOrEmpty(fare.getDiscount()) && !"0".equals(fare.getDiscount())) {
            try {
                DecimalFormat df = new DecimalFormat("0.0");
                double discount = Double.valueOf(fare.getDiscount()) / 10;
                String str = df.format(discount);
                //处理.0的数据
                if (str.endsWith(".0")) {
                    str = str.substring(0, str.indexOf(".0"));
                }
                if (!"0".equals(str)) {
                    cabinFare.setShowDisCount(str);
                }
            } catch (Exception e) {
                cabinFare.setShowDisCount("");
            }
        }
        //计算国际折扣率
        double value = fare.getPriceValue();
        double rsp = fare.getRSP();
        int discount = (int) (value * 100 / rsp);
        cabinFare.setIntDiscount(discount);
        cabinFare.setDynamicCabin(fare.getDynamicCabin() == null ? "" : fare.getDynamicCabin());
        cabinFare.setDynamicFareID(fare.getDynamicFareID() == null ? "" : fare.getDynamicFareID());
        //舱位等级名称
        cabinFare.setCabinClassName(CommonUtil.showCabinClassName(cabinFare.getCabinClass(), interFlag));
        if ((v2FlightInfo.getCodeShare() != null && !v2FlightInfo.getCodeShare())
                || (v2FlightInfo.getCarrierNo() != null && v2FlightInfo.getCarrierNo().startsWith(AirCompanyEnum.HO.getAirCompanyCode()))) {
            cabinFare.setScoreGive(v2CabinFare.getReturnScore() == null ? 0 : v2CabinFare.getReturnScore().intValue());
        } else {
            cabinFare.setScoreGive(0);
        }
        //积分返现

        //普通舱位的默认设置
        cabinFare.setCabinType(PackageTypeEnum.CABIN_NORMAL.getPackType());
        cabinFare.setSortPriority(100);
        //HO航班有免票舱位，东航不处理
        if (AirCompanyEnum.HO.getAirCompanyCode().equals(airCode)) {
            if (config.getFreeTicketCabin().equals(cabinFare.getCabinCode())) {
                if (PassengerTypeEnum.ADT.getPassType().equals(cabinFare.getPassengerType())) {
                    cabinFare.setCabinType(PackageTypeEnum.ADT_UNLIMITED_FLY.getPackType());
                    cabinFare.setSortPriority(10);
                } else if (PassengerTypeEnum.CHD.getPassType().equals(cabinFare.getPassengerType())) {
                    cabinFare.setCabinType(PackageTypeEnum.CHD_UNLIMITED_FLY.getPackType());
                    cabinFare.setSortPriority(20);
                }
            } else if (config.getAwardFlyFreeTicketCabin().contains(cabinFare.getCabinCode())) {
                if (PassengerTypeEnum.ADT.getPassType().equals(cabinFare.getPassengerType())) {
                    cabinFare.setCabinType(PackageTypeEnum.ADT_UNLIMITED_FLY.getPackType());
                    cabinFare.setSortPriority(10);
                } else if (PassengerTypeEnum.CHD.getPassType().equals(cabinFare.getPassengerType())) {
                    cabinFare.setCabinType(PackageTypeEnum.CHD_UNLIMITED_FLY.getPackType());
                    cabinFare.setSortPriority(20);
                } else {
                    cabinFare.setCabinType(PackageTypeEnum.CHD_UNLIMITED_FLY.getPackType());
                    cabinFare.setSortPriority(10);
                }
            }
        }
        //多程惠达 往返程优惠金额
        cabinFare.setDiscountPriceValue(fare.getDiscountDiff() == null ? 0 : fare.getDiscountDiff());
        cabinFare.setAdtBaggage(genBaggageRuleFromCabinFare(cabinFare, PassengerTypeEnum.ADT));
        // 以下字段空字符串可能引起APP崩溃  2019-11-20
        if (StringUtils.isBlank(cabinFare.getMinStay())) {
            cabinFare.setMinStay(null);
        }
        if (StringUtils.isBlank(cabinFare.getValidityPeriod())) {
            cabinFare.setValidityPeriod(null);
        }
        return cabinFare;
    }

    /**
     * 品牌运价舱位处理
     *
     * @param cabinFare
     */
    private static void brandFare(CabinFare cabinFare, HandConfig config, String flightType) {

        if ("J".equals(cabinFare.getCabinClass())) {
            if ("789".equals(flightType)) {
                cabinFare.setCabinLabelList2(config.getLabelInfoConfig().getFlightTypeLabelInfos());
            } else {
                cabinFare.setCabinLabelList2(config.getLabelInfoConfig().getJFlightTypeLabelInfos());
            }
        }
        // 低碳特惠
        else if (CollectionUtils.isNotEmpty(cabinFare.getShippingRulesLabel()) && cabinFare.getShippingRulesLabel().contains(ShippingRulesLabelEnum.LCPREFERENCE.name())) {
            cabinFare.setCabinLabelList2(config.getLabelInfoConfig().getNoFreeBaggageLabelInfos());
            cabinFare.putActivityLabelMap(ShippingRulesLabelEnum.LCPREFERENCE.name(), config.getNoFreeBaggageFareLabel());
        }
        // 品牌运价
        else if (cabinFare.getFareBasis() != null && cabinFare.getFareBasis().endsWith(FareBasisEnum.BRAND_FARE.getFareBasisCode())) {
            cabinFare.setCabinLabelBrand(config.getLabelInfoConfig().getBrandLabel());
            cabinFare.setCabinLabelList2(config.getLabelInfoConfig().getBrandLabelInfos());
            BrandPriceRule brandPriceRule = JsonUtil.fromJson(config.getBrandPriceRule(), BrandPriceRule.class);
            brandPriceRule.getApplicableCabin().setDescription(brandPriceRule.getApplicableCabin().getDescription().replace("{cabin}", cabinFare.getCabinCode()));
            cabinFare.setBrandPriceRule(brandPriceRule);
        }
        // 高舱高返
        else if (cabinFare.getFareBasis() != null && cabinFare.getFareBasis().endsWith(FareBasisEnum.HIGH_REBATE.getFareBasisCode())) {
            cabinFare.setCabinLabelList2(config.getLabelInfoConfig().getHighRebateLabelInfos());
        } else {
            if ("J".equals(cabinFare.getCabinClass())) {
                cabinFare.setCabinLabelList2(config.getLabelInfoConfig().getBusinessClassLabelInfoList());
            } else if ("Y".equals(cabinFare.getCabinClass())) {
                if ("I".equals(cabinFare.getInterFlag())) {
                    cabinFare.setCabinLabelList2(config.getLabelInfoConfig().getCommonInternationalLabelInfos());
                } else {
                    List<LabelInfo> commonDomesticLabel = new ArrayList<>();
                    for (LabelInfo labelInfo : config.getLabelInfoConfig().getCommonDomesticLabelInfos()) {
                        LabelInfo labelInfoNew = new LabelInfo();
                        BeanUtils.copyProperties(labelInfo, labelInfoNew);
                        commonDomesticLabel.add(labelInfoNew);
                    }
                    cabinFare.setCabinLabelList2(commonDomesticLabel);
                }
            }
        }
        baggeageCabinLabelByFlag(cabinFare, cabinFare.getCabinLabelList2());
    }


    /**
     * 标签  特殊标记   HAND 非托运行李   CHECK 托运
     * 根据标签添加每件重量的具体信息
     * @param cabinFare
     * @param commonDomesticLabel
     */
    static void baggeageCabinLabelByFlag(CabinFare cabinFare, List<LabelInfo> commonDomesticLabel) {
        // 取运价行李公斤数
        String checkKg = cabinFare.getCheckBaggeage();
        String handKg = cabinFare.getHandBaggeage();
        List<LabelInfo> commonDomesticList = new ArrayList<>();
        for (LabelInfo labelInfo : commonDomesticLabel) {
            LabelInfo labelInfoNew = new LabelInfo();
            BeanUtils.copyProperties(labelInfo, labelInfoNew);
            commonDomesticList.add(labelInfoNew);
        }
        // 处理名为"托运行李“的标签
        commonDomesticList.forEach(labelInfo -> {
            if ("CHECK".equals(labelInfo.getLabelFlag())) {
                labelInfo.setLabelName("托运行李" + checkKg);
            }else  if ("HAND".equals(labelInfo.getLabelFlag())) {
                labelInfo.setLabelName("非托运行李"+ handKg);
            }
        });
        cabinFare.setCabinLabelList2(commonDomesticList);
    }

    /**
     * 格式化标签名称
     *
     * @param cabinFare
     */
    public static void formatLabelName(CabinFare cabinFare) {
        if (null == cabinFare || CollectionUtils.isEmpty(cabinFare.getCabinLabelList2())) {
            return;
        }
        List<LabelInfo> newLabelInfoList = Lists.newArrayList();
        List<LabelInfo> labelInfoList = cabinFare.getCabinLabelList2();
        // 取运价行李公斤数
        String kg = cabinFare.getCheckBaggeage();
        // 处理名为"托运行李“的标签
        labelInfoList.forEach(labelInfo -> {
            LabelInfo newLabelInfo = new LabelInfo();
            BeanUtils.copyProperties(labelInfo, newLabelInfo);
            if (newLabelInfo.getLabelName().equals("托运行李")) {
                newLabelInfo.setLabelName("托运行李" + kg);
            }
            newLabelInfoList.add(newLabelInfo);
        });
        cabinFare.setCabinLabelList2(newLabelInfoList);
    }

    /**
     * 生成航班两舱最低价
     * 国内D（FC\Y）
     * 国际I（C\Y）
     * Y舱显示数量
     */
    public static List<CabinFare> filterCabin(List<CabinFare> adtList, String interFlag, int ycabinLimit, int jcabinLimit) {
        //处理最低价
        List<CabinFare> fadtFare = new ArrayList<>();
        List<CabinFare> yadtFare = new ArrayList<>();
        //将运价分为两舱 ， 低价舱位存在相同最低价时，取私有运价-2
        String fClass = interFlag.equals("I") ? "CJ" : "FCJ";
        for (int l = 0; l < adtList.size(); l++) {
            if (fClass.indexOf(adtList.get(l).getCabinClass()) > -1) {
                fadtFare.add(adtList.get(l));
            }
            if (adtList.get(l).getCabinClass().equals("Y")) {
                yadtFare.add(adtList.get(l));
            }
        }
        //去掉重复的
        List<CabinFare> newFADTFar = removeSameFare(fadtFare);
        List<CabinFare> newYADTFar = removeSameFare(yadtFare);
        return joinFare(newFADTFar, newYADTFar, ycabinLimit, jcabinLimit);
    }

    /**
     * 去掉重复舱位运价
     *
     * @param fareList
     * @return
     */
    private static List<CabinFare> removeSameFare(List<CabinFare> fareList) {
        if (StringUtil.isNullOrEmpty(fareList)) {
            return fareList;
        }
        List<CabinFare> newFareList = new ArrayList<>();
        for (int i = 0; i < fareList.size(); i++) {
            boolean chkFlag = true;
            CabinFare cabinFare = fareList.get(i);
            //当前舱位存在其他产品类型运价
            List<CabinFare> diffCabinFareProductList = fareList.stream().filter(fare -> fare.getCabinCode().equals(cabinFare.getCabinCode()) && !fare.getPriceProductType().equals(cabinFare.getPriceProductType())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(diffCabinFareProductList)) {
                chkFlag = false;
                //其他产品类型运价中是否存在私有运价
                CabinFare minCabinFare = diffCabinFareProductList.stream()
                        .filter(fare -> "2".equals(fare.getPriceProductType()))
                        .min(Comparator.comparingDouble(CabinFare::getPriceValue))
                        .orElse(null);
                if (minCabinFare != null) {
                    newFareList.add(minCabinFare);
                }
            }
            if (chkFlag) {
                newFareList.add(cabinFare);
            }
        }
        return sortPrice(newFareList);
    }

    private static List<CabinFare> removeSameFareBak(List<CabinFare> fareList) {
        if (StringUtil.isNullOrEmpty(fareList)) {
            return fareList;
        }
        List<CabinFare> newFareList = new ArrayList<>();
        boolean chkFlag = true;
        for (int i = 0; i < fareList.size(); i++) {
            chkFlag = true;
            for (int j = 0; j < fareList.size(); j++) {
                if (fareList.get(i).getCabinCode().equals(fareList.get(j).getCabinCode())
                        && (!fareList.get(i).getPriceProductType().equals(fareList.get(j).getPriceProductType()))) {
                    if (fareList.get(j).getPriceProductType().equals("2")) { //私有运价
                        newFareList.add(fareList.get(j));
                    }
                    chkFlag = false;
                    break;
                }
            }
            if (chkFlag) {
                newFareList.add(fareList.get(i));
            }
        }
        return sortPrice(newFareList);
    }

    /**
     * 价格排序
     */
    private static List<CabinFare> sortPrice(List<CabinFare> fareList) {
        return fareList.stream().sorted(Comparator.comparing(CabinFare::getSortPriority)
                .thenComparingDouble(CabinFare::getPriceValue).thenComparing(CabinFare::getCabinType)).collect(Collectors.toList());
    }

    /**
     * 合并运价
     *
     * @param fCanbinFare 价格已从低到高排序
     * @param yCanbinFare 价格已从低到高排序
     * @return
     */
    private static List<CabinFare> joinFare(List<CabinFare> fCanbinFare, List<CabinFare> yCanbinFare, int ycabinLimit, int jcabinLimit) {
        List<CabinFare> fineCabinFare = formatYcabinList(yCanbinFare, ycabinLimit);
        List<CabinFare> lCabinFareList = formatJcabinList(fCanbinFare, jcabinLimit);
        fineCabinFare.addAll(lCabinFareList);
        return sortPrice(fineCabinFare);//重新价格排序
    }

    /**
     * 过滤y舱的显示数量
     *
     * @param yCanbinFare
     * @param ycabinLimit
     * @return
     */
    private static List<CabinFare> formatYcabinList(List<CabinFare> yCanbinFare, int ycabinLimit) {
        List<CabinFare> fineCabinFare = new ArrayList<>();
        if (yCanbinFare.size() > ycabinLimit) {//舱位数量大于限制
            boolean hasY = false;
            // 是否存在托运行李
            boolean hasCheckBaggage = false;
            for (int i = 0; i < ycabinLimit - 1; i++) {
                CabinFare cabinFare = yCanbinFare.get(i);
                cabinFare.setPersonalizedDisplay(CabinRemarkEnum.DISCOUNT_ECONOMY_CLASS.getPersonalizedDisplayCode());
                cabinFare.setCabinLabel(CabinRemarkEnum.DISCOUNT_ECONOMY_CLASS.getPersonalizedDisplayRemark());
                if ("Y".equals(cabinFare.getCabinCode())) {
                    hasY = true;
                    cabinFare.setPersonalizedDisplay(CabinRemarkEnum.STANDARD_ECONOMY_CLASS.getPersonalizedDisplayCode());
                    cabinFare.setCabinLabel(CabinRemarkEnum.STANDARD_ECONOMY_CLASS.getPersonalizedDisplayRemark());
                }
                // 无标记 或 无低碳特惠标记认为存在托运行李
                if (CollectionUtils.isEmpty(cabinFare.getShippingRulesLabel()) || !cabinFare.getShippingRulesLabel().contains(ShippingRulesLabelEnum.LCPREFERENCE.name())) {
                    hasCheckBaggage = true;
                }
                fineCabinFare.add(cabinFare);
            }
            // 无托运行李获取第一个带托运行李的舱位
            if (!hasCheckBaggage) {
                for (CabinFare cabinFare : yCanbinFare) {
                    if (CollectionUtils.isEmpty(cabinFare.getShippingRulesLabel()) || !cabinFare.getShippingRulesLabel().contains(ShippingRulesLabelEnum.LCPREFERENCE.name())) {
                        cabinFare.setPersonalizedDisplay(CabinRemarkEnum.DISCOUNT_ECONOMY_CLASS.getPersonalizedDisplayCode());
                        cabinFare.setCabinLabel(CabinRemarkEnum.DISCOUNT_ECONOMY_CLASS.getPersonalizedDisplayRemark());
                        if ("Y".equals(cabinFare.getCabinCode())) {
                            hasY = true;
                            cabinFare.setPersonalizedDisplay(CabinRemarkEnum.STANDARD_ECONOMY_CLASS.getPersonalizedDisplayCode());
                            cabinFare.setCabinLabel(CabinRemarkEnum.STANDARD_ECONOMY_CLASS.getPersonalizedDisplayRemark());
                        }
                        fineCabinFare.add(cabinFare);
                        break;
                    }
                }
            }
            int yCNT = yCanbinFare.size() - 1;//价格最高的
            CabinFare yCabinFare = yCanbinFare.get(yCNT);
            yCabinFare.setPersonalizedDisplay(CabinRemarkEnum.DISCOUNT_ECONOMY_CLASS.getPersonalizedDisplayCode());
            yCabinFare.setCabinLabel(CabinRemarkEnum.DISCOUNT_ECONOMY_CLASS.getPersonalizedDisplayRemark());
            if ("Y".equals(yCabinFare.getCabinCode())) {
                yCabinFare.setPersonalizedDisplay(CabinRemarkEnum.STANDARD_ECONOMY_CLASS.getPersonalizedDisplayCode());
                yCabinFare.setCabinLabel(CabinRemarkEnum.STANDARD_ECONOMY_CLASS.getPersonalizedDisplayRemark());
            }
            //如果前两条经济舱仓位包含Y，最后一条不是Y，添加到列表中
            if (hasY) {
                if (!"Y".equals(yCabinFare.getCabinCode())) {
                    fineCabinFare.add(yCabinFare);
                }
            } else {
                //如果前两条不包含Y，最后一条不是Y，取出Y仓放进去
                if (!"Y".equals(yCabinFare.getCabinCode())) {
                    CabinFare cabinFare = yCanbinFare.stream().filter(yCabin -> "Y".equals(yCabin.getCabinCode())).findFirst().orElse(null);
                    if (cabinFare != null) {
                        cabinFare.setPersonalizedDisplay(CabinRemarkEnum.DISCOUNT_ECONOMY_CLASS.getPersonalizedDisplayCode());
                        cabinFare.setCabinLabel(CabinRemarkEnum.DISCOUNT_ECONOMY_CLASS.getPersonalizedDisplayRemark());
                        cabinFare.setPersonalizedDisplay(CabinRemarkEnum.STANDARD_ECONOMY_CLASS.getPersonalizedDisplayCode());
                        cabinFare.setCabinLabel(CabinRemarkEnum.STANDARD_ECONOMY_CLASS.getPersonalizedDisplayRemark());
                        fineCabinFare.add(cabinFare);
                    } else {
                        fineCabinFare.add(yCabinFare);
                    }
                } else {
                    //如果前两条不包含Y，最后一条是Y仓时，直接添加进去
                    fineCabinFare.add(yCabinFare);
                }
            }
//            if (!hasY || !"Y".equals(yCabinFare.getCabinCode())) {
//                fineCabinFare.add(yCabinFare);
//            }
        } else {
            for (int y = 0; y < yCanbinFare.size(); y++) {
                CabinFare cabinFare = yCanbinFare.get(y);
                cabinFare.setPersonalizedDisplay(CabinRemarkEnum.DISCOUNT_ECONOMY_CLASS.getPersonalizedDisplayCode());
                cabinFare.setCabinLabel(CabinRemarkEnum.DISCOUNT_ECONOMY_CLASS.getPersonalizedDisplayRemark());
                if ("Y".equals(cabinFare.getCabinCode())) {//标准经济
                    cabinFare.setPersonalizedDisplay(CabinRemarkEnum.STANDARD_ECONOMY_CLASS.getPersonalizedDisplayCode());
                    cabinFare.setCabinLabel(CabinRemarkEnum.STANDARD_ECONOMY_CLASS.getPersonalizedDisplayRemark());
                }
                fineCabinFare.add(cabinFare);
            }
        }
        return fineCabinFare;
    }

    /**
     * 过滤y舱的显示数量
     *
     * @param jCanbinFare
     * @param jcabinLimit
     * @return
     */
    private static List<CabinFare> formatJcabinList(List<CabinFare> jCanbinFare, int jcabinLimit) {
        List<CabinFare> fineCabinFare;
        if (jCanbinFare.size() > jcabinLimit) {//舱位数量大于限制,先获取前jcabinLimit-1
            fineCabinFare = showCabin(jCanbinFare, jcabinLimit - 1);
            int ycnt = jCanbinFare.size() - 1;//价格最高的
            CabinFare jCabinFare = jCanbinFare.get(ycnt);
            jCabinFare.setPersonalizedDisplay(CabinRemarkEnum.BUSINESS_CLASS.getPersonalizedDisplayCode());
            jCabinFare.setCabinLabel(CabinRemarkEnum.BUSINESS_CLASS.getPersonalizedDisplayRemark());
            if ("J".equals(jCabinFare.getCabinCode())) {
                jCabinFare.setPersonalizedDisplay(CabinRemarkEnum.STANDARD_BUSINESS_CLASS.getPersonalizedDisplayCode());
                jCabinFare.setCabinLabel(CabinRemarkEnum.STANDARD_BUSINESS_CLASS.getPersonalizedDisplayRemark());
            }
            //判断已选舱位是否包含J舱
            boolean hasJ = fineCabinFare.stream().anyMatch(cabinFare -> "J".equals(cabinFare.getCabinCode()));
            if (!hasJ || !"J".equals(jCabinFare.getCabinCode())) {
                fineCabinFare.add(jCabinFare);
            }
        } else {
            fineCabinFare = showCabin(jCanbinFare, jCanbinFare.size());
        }
        return fineCabinFare;
    }

    //舱位展示设置
    private static List<CabinFare> showCabin(List<CabinFare> jCanbinFare, int jcabinLimit) {
        List<CabinFare> fineCabinFare = new ArrayList<>();
        for (int i = 0; i < jcabinLimit; i++) {
            CabinFare cabinFare = jCanbinFare.get(i);
            cabinFare.setPersonalizedDisplay(CabinRemarkEnum.BUSINESS_CLASS.getPersonalizedDisplayCode());
            cabinFare.setCabinLabel(CabinRemarkEnum.BUSINESS_CLASS.getPersonalizedDisplayRemark());
            if ("J".equals(cabinFare.getCabinCode())) {
                cabinFare.setPersonalizedDisplay(CabinRemarkEnum.STANDARD_BUSINESS_CLASS.getPersonalizedDisplayCode());
                cabinFare.setCabinLabel(CabinRemarkEnum.STANDARD_BUSINESS_CLASS.getPersonalizedDisplayRemark());
            }
            fineCabinFare.add(cabinFare);
        }
        return fineCabinFare;
    }

    /**
     * 计算国内改期手续服务费
     *
     * @param fare        运价
     * @param curDateStr  当前时间
     * @param depDateStr  出发时间
     * @param depTimeZone 出发时区
     * @return
     */
    public static double calChangeServcieFee(Fare fare, String curDateStr, String depDateStr, String depTimeZone) {
        if (StringUtil.isNullOrEmpty(fare.getChangeRules())) {
            return -1;
        } else {
            Date depDate;
            Date curDate = DateUtils.toDate(curDateStr, "yyyy-MM-dd HH:mm");
            if ("8".equals(depTimeZone)) {
                depDate = DateUtils.toDate(depDateStr, "yyyy-MM-dd HH:mm");
            } else {
                depDate = DateUtils.toTargetDate(depDateStr, depTimeZone, "8");
            }
            //规则解析
            for (int i = 0; i < fare.getChangeRules().size(); i++) {
                PtChangeRuleInfo changeRuleInfo = fare.getChangeRules().get(i);
                if (StringUtil.isNullOrEmpty(changeRuleInfo.getFlightTimeCondition())) {
                    continue;
                }
                int flag = 1;
                //起飞前
                if ("0".equals(changeRuleInfo.getFlightTimeCondition())) {
                    flag = -1;
                } else if ("1".equals(changeRuleInfo.getFlightTimeCondition())) {
                    flag = 1;
                }
                //根据时间返回费用
                Double fee = getFee(flag, depDate, curDate, changeRuleInfo);
                if (fee != null) {
                    return fee;
                }
            }
        }
        return -1;
    }

    /**
     * @param flag    起飞前后标志
     * @param depDate 航班起飞日期
     * @param curDate 当前日期
     * @return
     */
    private static Double getFee(int flag, Date depDate, Date curDate, PtChangeRuleInfo changeRuleInfo) {
        Date fromDate = computingRule(changeRuleInfo.getTimeConditionStart(), depDate, flag);
        Date toDate = computingRule(changeRuleInfo.getTimeConditionEnd(), depDate, flag);
        //区间内只有一边的
        if (fromDate != null && toDate == null) {
            //表示起飞前
            if (flag == -1 && curDate.before(fromDate)) {
                return changeRuleInfo.getFee();
            }
            if (flag == 1 && curDate.after(fromDate)) {
                return changeRuleInfo.getFee();
            }
        }
        //说明在某个区间内
        if (fromDate != null && toDate != null) {
            log.info("当前的时间范围：{}-{}", fromDate, toDate);
            if (flag == -1 && (curDate.getTime() < fromDate.getTime() && curDate.getTime() > toDate.getTime())) {
                return changeRuleInfo.getFee();
            }
            if (flag == 1 && (curDate.getTime() >= fromDate.getTime() && curDate.getTime() <= toDate.getTime())) {
                return changeRuleInfo.getFee();
            }
        }
        return null;
    }

    //解析对应时间条件
    private static Date computingRule(String timeCondition, Date date, int flag) {
        Date newDate = null;
        if (!StringUtil.isNullOrEmpty(timeCondition)) {
            String[] timeConditionArray = splitTimeCondition(timeCondition);
            newDate = computingTime(timeConditionArray, date, flag);
            return newDate;
        }
        return newDate;
    }

    //时间解析
    private static Date computingTime(String[] timeConditionArray, Date date, int flag) {
        Date newDate = null;
        switch (timeConditionArray[1]) {
            case "Y":
                newDate = DateUtils.addOrLessMonth(date, 12 * Integer.parseInt(timeConditionArray[0]) * flag);
                break;
            case "M":
                newDate = DateUtils.addOrLessMonth(date, Integer.parseInt(timeConditionArray[0]) * flag);
                break;
            case "D":
                newDate = DateUtils.addOrLessDay(date, Integer.parseInt(timeConditionArray[0]) * flag);
                break;
            case "H":
                newDate = DateUtils.dateAddOrLessSecond(date, 3600 * Integer.parseInt(timeConditionArray[0]) * flag);
                break;
            case "MI":
                newDate = DateUtils.dateAddOrLessSecond(date, Integer.parseInt(timeConditionArray[0]) * 60 * flag);
                break;
            default:
                break;
        }
        return newDate;
    }


    /**
     * 计算国际改期手续费
     *
     * @return
     */
    public static double calChangeInterServcieFee(List<InterChangeFee> interChangeFeeList, ChangeFlightInfo changeFlightInfo, String passType) {
        //无国际改期手续费
        if (StringUtil.isNullOrEmpty(interChangeFeeList)) {
            return -1;
        }
        //获取同等的城市段
        List<InterChangeFee> flightInterChangeFeeList = interChangeFeeList.stream()
                .filter(interChangeFee -> interChangeFee.getDepCity().equals(changeFlightInfo.getDepCityCode()))
                .filter(interChangeFee -> interChangeFee.getArrCity().equals(changeFlightInfo.getArrCityCode()))
                .collect(Collectors.toList());
        if (StringUtil.isNullOrEmpty(flightInterChangeFeeList)) {
            return -1;
        }
        //匹配对应的乘客类型
        InterChangeFee interChangeFee = null;
        for (InterChangeFee temp : flightInterChangeFeeList) {
            List<String> strList = temp.getPassengerType().stream().filter(p -> p.equals(passType)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(strList)) {
                interChangeFee = temp;
                break;
            }
        }
        //缺少对应的手续费用
        if (interChangeFee == null) {
            log.error("{}查询不到对应的手续费用", passType);
            return -1;
        }
        //获取原有的舱位信息
        CabinFare cabinFare = null;
        if (HandlerConstants.PASSENGER_TYPE_ADT.equals(passType)) {
            cabinFare = changeFlightInfo.getAdtCabinFare();
        } else if (HandlerConstants.PASSENGER_TYPE_GMJC.equals(passType)) {
            cabinFare = changeFlightInfo.getGmjcCabinFare();
        } else if (HandlerConstants.PASSENGER_TYPE_CHD.equals(passType)) {
            cabinFare = changeFlightInfo.getChdCabinFare();
        } else if (HandlerConstants.PASSENGER_TYPE_INF.equals(passType)) {
            cabinFare = changeFlightInfo.getInfCabinFare();
        }
        if (cabinFare == null) {
            return -1;
        }
        Double fee = null;
        for (Map.Entry<String, Double> entrySet : interChangeFee.getChangeFee().entrySet()) {
            if (cabinFare.getCabinCode().equals(entrySet.getKey())) {
                fee = entrySet.getValue();
                break;
            }
        }
        //无法直接匹配舱位的，采用Y舱的 ？？
        if (fee == null) {
            fee = interChangeFee.getChangeFee().get("Y");
        }
        if (fee == null) {
            log.error("{}查询无匹配的舱位手续费用", passType);
            return -1;
        }
        return fee;
    }

    /**
     * 将统一订单的改期规则转换为前端需要展示的改期规则
     *
     * @param ptChangeRuleInfoList
     * @param interFlag            国内国际标识
     */
    public static List<ChangeAndRefundRule> toChangeRules(List<PtChangeRuleInfo> ptChangeRuleInfoList, String interFlag) {
        List<ChangeAndRefundRule> changeAndRefundRuleList = new ArrayList<>();
        if (StringUtil.isNullOrEmpty(ptChangeRuleInfoList)) {
            return changeAndRefundRuleList;
        }
        List<PtChangeRuleInfo> ptChangeRuleInfoListnew = ptChangeRuleInfoList.stream().filter(c -> !StringUtil.isNullOrEmpty(c.getFlightTimeCondition())).sorted(Comparator.comparing(PtChangeRuleInfo::getFlightTimeCondition).thenComparingDouble(PtChangeRuleInfo::getFee)).collect(Collectors.toList());
        //按照起飞前，起飞后分组
        Map<String, List<PtChangeRuleInfo>> ptChangeRuleInfoMap = ptChangeRuleInfoListnew.stream().collect((Collectors.groupingBy(ptChangeRuleInfo -> ptChangeRuleInfo.getFlightTimeCondition())));
        ptChangeRuleInfoListnew.clear();
        List<PtChangeRuleInfo> ptChangeRuleInfoList0 = new ArrayList<>();
        List<PtChangeRuleInfo> ptChangeRuleInfoList1 = new ArrayList<>();
        for (Map.Entry<String, List<PtChangeRuleInfo>> entry : ptChangeRuleInfoMap.entrySet()) {
            String key = entry.getKey();
            //按照时间条件分组
            Map<String, List<PtChangeRuleInfo>> ptChangeRuleInfoMapTemp = entry.getValue().stream().collect((Collectors.groupingBy(ptChangeRuleInfo -> ptChangeRuleInfo.getTimeConditionStart().split("/")[1])));
            for (Map.Entry<String, List<PtChangeRuleInfo>> tmp : ptChangeRuleInfoMapTemp.entrySet()) {
                if (key.equals("0")) {
                    tmp.getValue().sort(Comparator.comparing((PtChangeRuleInfo ptChangeRuleInfo) -> null != ptChangeRuleInfo.getTimeConditionStart() ? Integer.parseInt(ptChangeRuleInfo.getTimeConditionStart().split("/")[0]) : 1).reversed());
                } else {
                    tmp.getValue().sort(Comparator.comparing((PtChangeRuleInfo ptChangeRuleInfo) -> null != ptChangeRuleInfo.getTimeConditionStart() ? Integer.parseInt(ptChangeRuleInfo.getTimeConditionStart().split("/")[0]) : 1));
                }
            }
            if (key.equals("0")) {
                addChangeInfo(ptChangeRuleInfoList0, ptChangeRuleInfoMapTemp);
            } else {
                addChangeInfo(ptChangeRuleInfoList1, ptChangeRuleInfoMapTemp);
            }
        }
        //规则重新整合
        ptChangeRuleInfoListnew.addAll(ptChangeRuleInfoList0);
        ptChangeRuleInfoListnew.addAll(ptChangeRuleInfoList1);
        int i = 0;
        for (PtChangeRuleInfo ptChangeRuleInfo : ptChangeRuleInfoListnew) {
            ChangeAndRefundRule changeRule = null;
            //起飞前
            if ("0".equals(ptChangeRuleInfo.getFlightTimeCondition())) {
                String leftTime = "0";
                String rightTime = "0";
                String originalLeftTimeUnit = "";
                String originalRightTimeUnit = "";
                String leftTimeUnit = "";
                String rightTimeUnit = "";
                if (!StringUtil.isNullOrEmpty(ptChangeRuleInfo.getTimeConditionEnd())) {//左区间
                    String[] timeConditionArray = splitTimeCondition(ptChangeRuleInfo.getTimeConditionEnd());
                    originalLeftTimeUnit = timeConditionArray[1];
                    leftTimeUnit = convertTimeUnitName(timeConditionArray[1]);//时间单位
                    leftTime = timeConditionArray[0];
                }
                if (!StringUtil.isNullOrEmpty(ptChangeRuleInfo.getTimeConditionStart())) {//右区间
                    String[] timeConditionArray = splitTimeCondition(ptChangeRuleInfo.getTimeConditionStart());
                    originalRightTimeUnit = timeConditionArray[1];
                    rightTimeUnit = convertTimeUnitName(timeConditionArray[1]);//时间单位
                    rightTime = timeConditionArray[0];
                }
                changeRule = new ChangeAndRefundRule();
                changeRule.setFlightFlag(-1);
                changeRule.setTimeConditionStart(ptChangeRuleInfo.getTimeConditionStart());
                changeRule.setTimeConditionEnd(ptChangeRuleInfo.getTimeConditionEnd());
                changeRule.setLeftTime(leftTime);
                changeRule.setLeftTimeUnit(leftTimeUnit);
                changeRule.setOriginalLeftTimeUnit(originalLeftTimeUnit);
                changeRule.setRightTime(rightTime);
                changeRule.setRightTimeUnit(rightTimeUnit);
                changeRule.setOriginalRightTimeUnit(originalRightTimeUnit);
                if (StringUtil.isNullOrEmpty(leftTimeUnit) && !StringUtil.isNullOrEmpty(rightTimeUnit)) {
                    changeRule.setDesc("起飞前" + rightTime + rightTimeUnit + toHoursDesc(rightTime, originalRightTimeUnit) + "之前");
                    if (HandlerConstants.TRIP_TYPE_I.equals(interFlag) && "0".equals(rightTime)) {
                        changeRule.setDesc("起飞前");
                    }
                }
                if (!StringUtil.isNullOrEmpty(leftTimeUnit) && !StringUtil.isNullOrEmpty(rightTimeUnit)) {
                    if (leftTimeUnit.equals(rightTimeUnit)) {
                        changeRule.setDesc("起飞前" + rightTime + toHoursDesc(rightTime, originalRightTimeUnit) + "-" + leftTime + rightTimeUnit + toHoursDesc(leftTime, originalRightTimeUnit) + "以内");
                    } else {
                        changeRule.setDesc("起飞前" + rightTime + rightTimeUnit + toHoursDesc(rightTime, originalRightTimeUnit) + "-" + leftTime + leftTimeUnit + toHoursDesc(leftTime, originalLeftTimeUnit) + "以内");
                    }
                    if ("0".equals(rightTime)) {
                        changeRule.setDesc("起飞前" + leftTime + leftTimeUnit + toHoursDesc(leftTime, originalLeftTimeUnit) + "以内");
                    }
                    //第一条规则默认只展示右区间数值
                    if (i == 0) {
                        changeRule.setDesc("起飞前" + rightTime + rightTimeUnit + toHoursDesc(rightTime, originalRightTimeUnit) + "之前");
                    }
                }
                changeRule.setChangeFee(ptChangeRuleInfo.getFee());
            }
            //起飞后
            if ("1".equals(ptChangeRuleInfo.getFlightTimeCondition())) {
                String leftTime = "0";
                String rightTime = "0";
                String originalLeftTimeUnit = "";
                String originalRightTimeUnit = "";
                String leftTimeUnit = "";
                String rightTimeUnit = "";
                if (!StringUtil.isNullOrEmpty(ptChangeRuleInfo.getTimeConditionStart())) {
                    String[] timeConditionArray = splitTimeCondition(ptChangeRuleInfo.getTimeConditionStart());
                    originalLeftTimeUnit = timeConditionArray[1];
                    leftTimeUnit = convertTimeUnitName(timeConditionArray[1]);
                    leftTime = timeConditionArray[0];
                }
                if (!StringUtil.isNullOrEmpty(ptChangeRuleInfo.getTimeConditionEnd())) {
                    String[] timeConditionArray = splitTimeCondition(ptChangeRuleInfo.getTimeConditionEnd());
                    originalRightTimeUnit = timeConditionArray[1];
                    rightTimeUnit = convertTimeUnitName(timeConditionArray[1]);
                    rightTime = timeConditionArray[0];
                }
                changeRule = new ChangeAndRefundRule();
                changeRule.setFlightFlag(1);
                changeRule.setTimeConditionStart(ptChangeRuleInfo.getTimeConditionStart());
                changeRule.setTimeConditionEnd(ptChangeRuleInfo.getTimeConditionEnd());
                changeRule.setLeftTime(leftTime);
                changeRule.setLeftTimeUnit(leftTimeUnit);
                changeRule.setOriginalLeftTimeUnit(originalLeftTimeUnit);
                changeRule.setRightTime(rightTime);
                changeRule.setRightTimeUnit(rightTimeUnit);
                changeRule.setOriginalRightTimeUnit(originalRightTimeUnit);
                //两者都有值时leftTime表示的是右区间
                if (!StringUtil.isNullOrEmpty(leftTimeUnit) && !StringUtil.isNullOrEmpty(rightTimeUnit)) {
                    if (leftTimeUnit.equals(rightTimeUnit)) {//时间单位合并
                        changeRule.setDesc("起飞后" + rightTime + toHoursDesc(rightTime, originalRightTimeUnit) + "-" + leftTime + rightTimeUnit + toHoursDesc(leftTime, originalRightTimeUnit) + "以内");
                    } else {
                        changeRule.setDesc("起飞后" + rightTime + rightTimeUnit + toHoursDesc(rightTime, originalRightTimeUnit) + "-" + leftTime + leftTimeUnit + toHoursDesc(leftTime, originalLeftTimeUnit) + "以内");
                    }
                }
                if (StringUtil.isNullOrEmpty(rightTimeUnit) && !StringUtil.isNullOrEmpty(leftTimeUnit)) {
                    changeRule.setDesc("起飞后" + leftTime + leftTimeUnit + toHoursDesc(leftTime, originalLeftTimeUnit) + "之后");
                    if (HandlerConstants.TRIP_TYPE_I.equals(interFlag) && "0".equals(leftTime)) {
                        changeRule.setDesc("起飞后");
                    }
                }
                changeRule.setChangeFee(ptChangeRuleInfo.getFee());
            }
            if ("0".equals(ptChangeRuleInfo.getTicketUsage())) {
                changeRule = new ChangeAndRefundRule();
                if ("0".equals(ptChangeRuleInfo.getFlightTimeCondition())) {
                    changeRule.setDesc("全部未使用(起飞前)");
                } else if ("1".equals(ptChangeRuleInfo.getFlightTimeCondition())) {
                    changeRule.setDesc("全部未使用(起飞后)");
                } else {
                    changeRule.setDesc("全部未使用");
                }
                changeRule.setChangeFee(ptChangeRuleInfo.getFee());
            } else if ("1".equals(ptChangeRuleInfo.getTicketUsage())) {
                changeRule = new ChangeAndRefundRule();
                if ("0".equals(ptChangeRuleInfo.getFlightTimeCondition())) {
                    changeRule.setDesc("部分已使用(起飞前)");
                } else if ("1".equals(ptChangeRuleInfo.getFlightTimeCondition())) {
                    changeRule.setDesc("部分已使用(起飞后)");
                } else {
                    changeRule.setDesc("部分已使用");
                }
                changeRule.setChangeFee(ptChangeRuleInfo.getFee());
            }
            //遍历至最后一个规则时更改描述
            if (changeRule != null) {
                changeAndRefundRuleList.add(changeRule);
                if ((i == ptChangeRuleInfoListnew.size() - 1) && !StringUtil.isNullOrEmpty(changeAndRefundRuleList)) {
                    int size = changeAndRefundRuleList.size();
                    if (size > 2) {
                        ChangeAndRefundRule preRule = changeAndRefundRuleList.get(size - 2);
                        //处于状态变换分界点时preRule = {ChangeAndRefundRule@16951} "ChangeAndRefundRule(desc=起飞前4小时以内, changeFee=292.0, ruleDesc=null, flightFlag=-1, timeConditionStart=0/MI, timeConditionEnd=4/H, leftTime=4, leftTimeUnit=小时, originalLeftTimeUnit=H, rightTime=0, rightTimeUnit=分钟, originalRightTimeUnit=MI)"
                        if (preRule.getFlightFlag() != changeRule.getFlightFlag()) {
                            if (preRule.getTimeConditionStart().equals(changeRule.getTimeConditionStart())) {
                                changeRule.setDesc("起飞前" + preRule.getLeftTime() + preRule.getLeftTimeUnit() + toHoursDesc(preRule.getLeftTime(), preRule.getOriginalLeftTimeUnit()) + "之后");//更改描述
                            }
                            changeAndRefundRuleList.remove(size - 2);
                        }
                    }
                }
            }
            i++;
        }
        return changeAndRefundRuleList;
    }

    private static void addChangeInfo(List<PtChangeRuleInfo> ptChangeRuleInfoList, Map<String, List<PtChangeRuleInfo>> ptChangeRuleInfoMapTemp) {
        if (CollectionUtils.isNotEmpty(ptChangeRuleInfoMapTemp.get("Y"))) {
            ptChangeRuleInfoList.addAll(ptChangeRuleInfoMapTemp.get("Y"));
        }
        if (CollectionUtils.isNotEmpty(ptChangeRuleInfoMapTemp.get("M"))) {
            ptChangeRuleInfoList.addAll(ptChangeRuleInfoMapTemp.get("M"));
        }
        if (CollectionUtils.isNotEmpty(ptChangeRuleInfoMapTemp.get("D"))) {
            ptChangeRuleInfoList.addAll(ptChangeRuleInfoMapTemp.get("D"));
        }
        if (CollectionUtils.isNotEmpty(ptChangeRuleInfoMapTemp.get("H"))) {
            ptChangeRuleInfoList.addAll(ptChangeRuleInfoMapTemp.get("H"));
        }
        if (CollectionUtils.isNotEmpty(ptChangeRuleInfoMapTemp.get("MI"))) {
            ptChangeRuleInfoList.addAll(ptChangeRuleInfoMapTemp.get("MI"));
        }
    }

    /**
     * 将统一订单的退票规则转换为前端需要展示的改期规则
     *
     * @param ptRefundRuleInfoList
     * @param interFlag            国内国际标识
     * @return
     */
    public static List<ChangeAndRefundRule> toRefundRules(List<PtRefundRuleInfo> ptRefundRuleInfoList, String interFlag) {
        List<ChangeAndRefundRule> changeAndRefundRuleList = new ArrayList<>();
        if (StringUtil.isNullOrEmpty(ptRefundRuleInfoList)) {
            return changeAndRefundRuleList;
        }
        //过滤为空的条件
        List<PtRefundRuleInfo> ptRefundRuleInfos = ptRefundRuleInfoList.stream().filter(r -> !StringUtil.isNullOrEmpty(r.getFlightTimeCondition())
                || StringUtils.isNotBlank(r.getTicketUsage())).collect(Collectors.toList());
        //按照起飞前，起飞后分组
        Map<String, List<PtRefundRuleInfo>> ptRefundRuleInfoMap = ptRefundRuleInfos.stream().collect((Collectors.groupingBy(ptRefundRuleInfo -> ptRefundRuleInfo.getFlightTimeCondition())));
        ptRefundRuleInfos.clear();
        List<PtRefundRuleInfo> ptRefundRuleInfoList0 = new ArrayList<>();
        List<PtRefundRuleInfo> ptRefundRuleInfoList1 = new ArrayList<>();
        for (Map.Entry<String, List<PtRefundRuleInfo>> entry : ptRefundRuleInfoMap.entrySet()) {
            String key = entry.getKey();
            //按照时间条件分组
            Map<String, List<PtRefundRuleInfo>> ptRefundRuleInfoMapTemp = entry.getValue().stream().collect((Collectors.groupingBy(ptRefundRuleInfo -> ptRefundRuleInfo.getTimeConditionStart().split("/")[1])));
            for (Map.Entry<String, List<PtRefundRuleInfo>> tmp : ptRefundRuleInfoMapTemp.entrySet()) {
                if (key.equals("0")) {
                    tmp.getValue().sort(Comparator.comparing((PtRefundRuleInfo ptRefundRuleInfo) -> null != ptRefundRuleInfo.getTimeConditionStart() ? Integer.parseInt(ptRefundRuleInfo.getTimeConditionStart().split("/")[0]) : 1).reversed());
                } else {
                    tmp.getValue().sort(Comparator.comparing((PtRefundRuleInfo ptRefundRuleInfo) -> null != ptRefundRuleInfo.getTimeConditionStart() ? Integer.parseInt(ptRefundRuleInfo.getTimeConditionStart().split("/")[0]) : 1));
                }
            }
            if (key.equals("0")) {
                addRefundInfo(ptRefundRuleInfoList0, ptRefundRuleInfoMapTemp);
            } else {
                addRefundInfo(ptRefundRuleInfoList1, ptRefundRuleInfoMapTemp);
            }
        }
        //规则重新整合
        ptRefundRuleInfos.addAll(ptRefundRuleInfoList0);
        ptRefundRuleInfos.addAll(ptRefundRuleInfoList1);
        int i = 0;
        for (PtRefundRuleInfo ptRefundRuleInfo : ptRefundRuleInfos) {
            ChangeAndRefundRule changeRule = null;
            //起飞前
            if ("0".equals(ptRefundRuleInfo.getFlightTimeCondition())) {
                String leftTime = "0";
                String rightTime = "0";
                String originalLeftTimeUnit = "";
                String originalRightTimeUnit = "";
                String leftTimeUnit = "";
                String rightTimeUnit = "";
                if (!StringUtil.isNullOrEmpty(ptRefundRuleInfo.getTimeConditionEnd())) {
                    String[] timeConditionArray = splitTimeCondition(ptRefundRuleInfo.getTimeConditionEnd());
                    originalLeftTimeUnit = timeConditionArray[1];
                    leftTimeUnit = convertTimeUnitName(timeConditionArray[1]);//时间单位
                    leftTime = timeConditionArray[0];
                }
                if (!StringUtil.isNullOrEmpty(ptRefundRuleInfo.getTimeConditionStart())) {
                    String[] timeConditionArray = splitTimeCondition(ptRefundRuleInfo.getTimeConditionStart());
                    originalRightTimeUnit = timeConditionArray[1];
                    rightTimeUnit = convertTimeUnitName(timeConditionArray[1]);//时间单位
                    rightTime = timeConditionArray[0];
                }
                changeRule = new ChangeAndRefundRule();
                changeRule.setFlightFlag(-1);
                changeRule.setTimeConditionStart(ptRefundRuleInfo.getTimeConditionStart());
                changeRule.setTimeConditionEnd(ptRefundRuleInfo.getTimeConditionEnd());
                changeRule.setLeftTime(leftTime);
                changeRule.setLeftTimeUnit(leftTimeUnit);
                changeRule.setOriginalLeftTimeUnit(originalLeftTimeUnit);
                changeRule.setRightTime(rightTime);
                changeRule.setRightTimeUnit(rightTimeUnit);
                changeRule.setOriginalRightTimeUnit(originalRightTimeUnit);
                if (StringUtil.isNullOrEmpty(leftTimeUnit) && StringUtils.isNotBlank(rightTimeUnit)) {
                    changeRule.setDesc("起飞前" + rightTime + rightTimeUnit + toHoursDesc(rightTime, originalRightTimeUnit) + "之前");
                    if (HandlerConstants.TRIP_TYPE_I.equals(interFlag) && "0".equals(rightTime)) {
                        changeRule.setDesc("起飞前");
                    }
                }
                if (StringUtils.isNotBlank(leftTimeUnit) && StringUtils.isNotBlank(rightTimeUnit)) {
                    if (leftTimeUnit.equals(rightTimeUnit)) {
                        changeRule.setDesc("起飞前" + rightTime + toHoursDesc(rightTime, originalRightTimeUnit) + "-" + leftTime + rightTimeUnit + toHoursDesc(leftTime, originalRightTimeUnit) + "以内");
                    } else {
                        changeRule.setDesc("起飞前" + rightTime + rightTimeUnit + toHoursDesc(rightTime, originalRightTimeUnit) + "-" + leftTime + leftTimeUnit + toHoursDesc(leftTime, originalLeftTimeUnit) + "以内");
                    }
                    if ("0".equals(rightTime)) {
                        changeRule.setDesc("起飞前" + leftTime + leftTimeUnit + toHoursDesc(leftTime, originalLeftTimeUnit) + "以内");
                    }
                    if (i == 0) {
                        changeRule.setDesc("起飞前" + rightTime + rightTimeUnit + toHoursDesc(rightTime, originalRightTimeUnit) + "之前");
                    }
                }
                changeRule.setChangeFee(ptRefundRuleInfo.getFee());
                convertFreeCabinRule(ptRefundRuleInfo, changeRule);
            }
            //起飞后
            if ("1".equals(ptRefundRuleInfo.getFlightTimeCondition())) {
                String leftTime = "0";
                String rightTime = "0";
                String originalLeftTimeUnit = "";
                String originalRightTimeUnit = "";
                String leftTimeUnit = "";
                String rightTimeUnit = "";
                if (!StringUtil.isNullOrEmpty(ptRefundRuleInfo.getTimeConditionStart())) {
                    String[] timeConditionArray = splitTimeCondition(ptRefundRuleInfo.getTimeConditionStart());
                    originalLeftTimeUnit = timeConditionArray[1];
                    leftTimeUnit = convertTimeUnitName(timeConditionArray[1]);
                    leftTime = timeConditionArray[0];
                }
                if (!StringUtil.isNullOrEmpty(ptRefundRuleInfo.getTimeConditionEnd())) {
                    String[] timeConditionArray = splitTimeCondition(ptRefundRuleInfo.getTimeConditionEnd());
                    originalRightTimeUnit = timeConditionArray[1];
                    rightTimeUnit = convertTimeUnitName(timeConditionArray[1]);
                    rightTime = timeConditionArray[0];
                }
                changeRule = new ChangeAndRefundRule();
                changeRule.setFlightFlag(1);
                changeRule.setTimeConditionStart(ptRefundRuleInfo.getTimeConditionStart());
                changeRule.setTimeConditionEnd(ptRefundRuleInfo.getTimeConditionEnd());
                changeRule.setLeftTime(leftTime);
                changeRule.setLeftTimeUnit(leftTimeUnit);
                changeRule.setOriginalLeftTimeUnit(originalLeftTimeUnit);
                changeRule.setRightTime(rightTime);
                changeRule.setRightTimeUnit(rightTimeUnit);
                changeRule.setOriginalRightTimeUnit(originalRightTimeUnit);
                //注意TimeConditionStart的区间方向  TimeConditionEnd有值时TimeConditionStart代表右区间，TimeConditionEnd无值时TimeConditionStart代表左区间
                if (StringUtil.isNullOrEmpty(rightTimeUnit) && !StringUtil.isNullOrEmpty(leftTimeUnit)) {
                    changeRule.setDesc("起飞后" + leftTime + leftTimeUnit + toHoursDesc(leftTime, originalLeftTimeUnit) + "之后");
                    if (HandlerConstants.TRIP_TYPE_I.equals(interFlag) && "0".equals(leftTime)) {
                        changeRule.setDesc("起飞后");
                    }
                }
                //(TimeConditionEnd[rightTime]-TimeConditionStart[leftTime])
                if (!StringUtil.isNullOrEmpty(leftTimeUnit) && !StringUtil.isNullOrEmpty(rightTimeUnit)) {
                    if (leftTimeUnit.equals(rightTimeUnit)) {//时间单位合并
                        changeRule.setDesc("起飞后" + rightTime + toHoursDesc(rightTime, originalRightTimeUnit) + "-" + leftTime + rightTimeUnit + toHoursDesc(leftTime, originalRightTimeUnit) + "以内");
                    } else {
                        changeRule.setDesc("起飞后" + rightTime + rightTimeUnit + toHoursDesc(rightTime, originalRightTimeUnit) + "-" + leftTime + leftTimeUnit + toHoursDesc(leftTime, originalLeftTimeUnit) + "以内");
                    }
                }
                changeRule.setChangeFee(ptRefundRuleInfo.getFee());
                convertFreeCabinRule(ptRefundRuleInfo, changeRule);
            }
            if ("0".equals(ptRefundRuleInfo.getTicketUsage())) {
                changeRule = new ChangeAndRefundRule();
                if ("0".equals(ptRefundRuleInfo.getFlightTimeCondition())) {
                    changeRule.setDesc("全部未使用(起飞前)");
                } else if ("1".equals(ptRefundRuleInfo.getFlightTimeCondition())) {
                    changeRule.setDesc("全部未使用(起飞后)");
                } else {
                    changeRule.setDesc("全部未使用");
                }
                changeRule.setChangeFee(ptRefundRuleInfo.getFee());
            } else if ("1".equals(ptRefundRuleInfo.getTicketUsage())) {
                changeRule = new ChangeAndRefundRule();
                if ("0".equals(ptRefundRuleInfo.getFlightTimeCondition())) {
                    changeRule.setDesc("部分已使用(起飞前)");
                } else if ("1".equals(ptRefundRuleInfo.getFlightTimeCondition())) {
                    changeRule.setDesc("部分已使用(起飞后)");
                } else {
                    changeRule.setDesc("部分已使用");
                }
                changeRule.setChangeFee(ptRefundRuleInfo.getFee());
            }
            //遍历至最后一个规则时更改描述
            if (changeRule != null) {
                changeAndRefundRuleList.add(changeRule);
                //遍历至最后的规则时
                if ((i == ptRefundRuleInfos.size() - 1) && !StringUtil.isNullOrEmpty(changeAndRefundRuleList)) {
                    int size = changeAndRefundRuleList.size();
                    if (size > 2) {
                        ChangeAndRefundRule preRule = changeAndRefundRuleList.get(size - 2);
                        //处于状态变换分界点时
                        if (preRule.getFlightFlag() != changeRule.getFlightFlag() && preRule.getChangeFee().doubleValue() == changeRule.getChangeFee().doubleValue()) {
                            if (preRule.getTimeConditionStart().equals(changeRule.getTimeConditionStart())) {
                                changeRule.setDesc("起飞前" + preRule.getLeftTime() + preRule.getLeftTimeUnit() + toHoursDesc(preRule.getLeftTime(), preRule.getOriginalLeftTimeUnit()) + "之后");//更改描述
                            }
                            changeAndRefundRuleList.remove(size - 2);
                        }
                    }
                }
            }

            i++;
        }
        return changeAndRefundRuleList;
    }

    private static void convertFreeCabinRule(PtRefundRuleInfo ptRefundRuleInfo, ChangeAndRefundRule changeAndRefundRule) {
        //如果未使用退票费金额等于0且费率不为0 表示票价是为0的，更改描述文案
        if (ptRefundRuleInfo.getFee() == 0 && StringUtils.isNotBlank(ptRefundRuleInfo.getShowFeeRate()) && !"0%".equals(ptRefundRuleInfo.getShowFeeRate())
                && !"0.0%".equals(ptRefundRuleInfo.getShowFeeRate())) {
            changeAndRefundRule.setChangeFee(-1D);
            changeAndRefundRule.setRuleDesc("按规定执行");
        }
    }

    private static void addRefundInfo(List<PtRefundRuleInfo> ptRefundRuleInfoList, Map<String, List<PtRefundRuleInfo>> ptRefundRuleInfoMapTemp) {
        if (CollectionUtils.isNotEmpty(ptRefundRuleInfoMapTemp.get("Y"))) {
            ptRefundRuleInfoList.addAll(ptRefundRuleInfoMapTemp.get("Y"));
        }
        if (CollectionUtils.isNotEmpty(ptRefundRuleInfoMapTemp.get("M"))) {
            ptRefundRuleInfoList.addAll(ptRefundRuleInfoMapTemp.get("M"));
        }
        if (CollectionUtils.isNotEmpty(ptRefundRuleInfoMapTemp.get("D"))) {
            ptRefundRuleInfoList.addAll(ptRefundRuleInfoMapTemp.get("D"));
        }
        if (CollectionUtils.isNotEmpty(ptRefundRuleInfoMapTemp.get("H"))) {
            ptRefundRuleInfoList.addAll(ptRefundRuleInfoMapTemp.get("H"));
        }
        if (CollectionUtils.isNotEmpty(ptRefundRuleInfoMapTemp.get("MI"))) {
            ptRefundRuleInfoList.addAll(ptRefundRuleInfoMapTemp.get("MI"));
        }
    }

    /**
     * 时间单位转为对应的时间描述
     *
     * @param timeUnit
     * @return
     */
    private static String convertTimeUnitName(String timeUnit) {
        String desc = "";
        switch (timeUnit) {
            case "Y":
                desc = "年";
                break;
            case "M":
                desc = "月";
                break;
            case "D":
                desc = "天";
                break;
            case "H":
                desc = "小时";
                break;
            case "MI":
                desc = "分钟";
                break;
            default:
                break;
        }
        return desc;
    }

    /**
     * 几天的描述转换为 小时
     *
     * @param time
     * @param timeUnit
     * @return
     */
    private static String toHoursDesc(String time, String timeUnit) {
        Integer intTime = NumberUtil.stringToInt(time);
        if (intTime != null && intTime > 0 && "D".equals(timeUnit)) {
            return "(" + intTime * 24 + "小时)";
        }
        return "";
    }

    /**
     * 按时间排序
     */
    public static List<FlightInfo> sortDeparttime(List<FlightInfo> flightList) {
        return flightList.stream().sorted(Comparator.comparing(FlightInfo::getDepDateTime)).collect(Collectors.toList());
    }

    /**
     * 将2/h格式拆分为数组
     *
     * @param timeCondition
     * @return 第一位表示数值  第二位表示单位
     */
    private static String[] splitTimeCondition(String timeCondition) {
        return timeCondition.split("/");
    }

    /**
     * 处理婴儿行李额展示
     *
     * @param adtList
     * @param chdInfList
     */
    private static void dealFreeBag(List<CabinFare> adtList, List<CabinFare> chdInfList, String interFlag) {
        if (StringUtil.isNullOrEmpty(adtList)) {
            return;
        }
        if (CollectionUtils.isEmpty(chdInfList)) {
            adtList.forEach(cabinFareAdt -> {
                // 未获取到儿童行李信息使用成人行李额信息
                cabinFareAdt.setChdBaggage(AVObjectConvertV2.getChdBaggageFromAtd(cabinFareAdt.getAdtBaggage()));
                cabinFareAdt.setInfFreeBaggage(getInfBaggage());
            });
            return;
        }
        adtList.forEach(cabinFareAdt -> {
            // 设置成人行李额规则
            cabinFareAdt.setAdtBaggage(genBaggageRuleFromCabinFare(cabinFareAdt, PassengerTypeEnum.ADT));

            String cabinCode = cabinFareAdt.getCabinCode();
            String cabinClass = cabinFareAdt.getCabinClass();
            //婴儿运价
            List<CabinFare> infList = chdInfList.stream().filter(c -> HandlerConstants.PASSENGER_TYPE_INF.equals(c.getPassengerType())).collect(Collectors.toList());
            List<CabinFare> chdList = chdInfList.stream().filter(c -> HandlerConstants.PASSENGER_TYPE_CHD.equals(c.getPassengerType())).collect(Collectors.toList());
            //处理儿童退改规则
            if (!StringUtil.isNullOrEmpty(chdList)) {
                //儿童畅飞卡儿童取Y舱
                if (PackageTypeEnum.CHD_UNLIMITED_FLY.getPackType().equals(cabinFareAdt.getCabinType())) {
                    CabinFare childFare = chdList.stream().filter(chd -> "Y".equals(chd.getCabinCode())).findFirst().orElse(null);
                    if (childFare != null) {
                        cabinFareAdt.setChdBaggage(genBaggageRuleFromCabinFare(childFare, PassengerTypeEnum.CHD));
                    }
                } else {
                    //先取同等舱位的，同等舱位没有的情况取父舱规则
                    CabinFare childFare = chdList.stream().filter(chd -> cabinCode.equals(chd.getCabinCode())).findFirst().orElse(null);
                    if (childFare == null) {
                        childFare = chdList.stream().filter(chd -> cabinClass.equals(chd.getCabinCode())).findFirst().orElse(null);
                    }
                    if (childFare != null) {
                        cabinFareAdt.setChdBaggage(genBaggageRuleFromCabinFare(childFare, PassengerTypeEnum.CHD));
                    } else {
                        // 未获取到儿童行李信息使用成人行李额信息
                        cabinFareAdt.setChdBaggage(AVObjectConvertV2.getChdBaggageFromAtd(cabinFareAdt.getAdtBaggage()));
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(infList)) {
                // 畅飞卡不展示婴儿行李额
                if (!PackageTypeEnum.CHD_UNLIMITED_FLY.getPackType().equals(cabinFareAdt.getCabinType())
                        && !PackageTypeEnum.ADT_UNLIMITED_FLY.getPackType().equals(cabinFareAdt.getCabinType())) {
                    //先取同等舱位的，同等舱位没有的情况取标准F，Y舱
                    CabinFare infFare = infList.stream().filter(inf -> cabinCode.equals(inf.getCabinCode())).findFirst().orElse(null);
                    if (infFare == null) {
                        infFare = infList.stream().filter(inf -> cabinClass.equals(inf.getCabinCode())).findFirst().orElse(null);
                    }
                    if (infFare != null) {
                        cabinFareAdt.setInfFreeBaggage(genBaggageRuleFromCabinFare(infFare, PassengerTypeEnum.INF));
                    } else {
                        cabinFareAdt.setInfFreeBaggage(getInfBaggage());
                    }
                }
            }
        });
    }

    /**
     * 生成默认婴儿行李信息
     *
     * @return
     */
    public static BaggerRule getInfBaggage() {
        BaggerRule infBaggerRule = new BaggerRule();
        infBaggerRule.setCheckBaggeage("0KG");
        infBaggerRule.setHandBaggeage("无免费行李额");
        infBaggerRule.setSpecialRemark(INF_SPECIAL_REMARK);
        return infBaggerRule;
    }

    /**
     * 基于成人行李额信息生成儿童行李信息
     *
     * @param baggerRule
     * @return
     */
    public static BaggerRule getChdBaggageFromAtd(BaggerRule baggerRule) {
        if (null == baggerRule) {
            return null;
        }
        // 未获取到儿童行李信息使用成人行李额信息
        BaggerRule chdBaggage = new BaggerRule();
        BeanUtils.copyProperties(baggerRule, chdBaggage);
        return chdBaggage;
    }

    /**
     * 从运价中提取生成行李额规则
     *
     * @param cabinFare
     * @return
     */
    public static BaggerRule genBaggageRuleFromCabinFare(CabinFare cabinFare, PassengerTypeEnum passengerTypeEnum) {
        BaggerRule baggerRule = new BaggerRule();
        baggerRule.setBaggage(cabinFare.getBaggage());
        if (PassengerTypeEnum.INF.equals(passengerTypeEnum)) {
            baggerRule.setBaggageDesc("婴儿免费行李额");
            if ("0KG".equalsIgnoreCase(cabinFare.getBaggage()) || "0".equals(cabinFare.getBaggage())) {
                baggerRule.setBaggage("购买国内航班婴儿票的旅客不享受免费行李额，但可免费托运婴儿手推车一辆。");
            }
        }
        baggerRule.setHandBaggeage(genBaggeageStr(cabinFare.getHandBaggeage()));
        baggerRule.setHandBaggeageRemark(cabinFare.getHandBaggeageRemark());
        baggerRule.setCheckBaggeage(genBaggeageStr(cabinFare.getCheckBaggeage()));
        baggerRule.setCheckBaggeageRemark(cabinFare.getCheckBaggeageRemark());
        if (StringUtils.isNotBlank(cabinFare.getSpecialRemark())) {
            String specialRemarkPrefix = "";
            if (!cabinFare.getSpecialRemark().startsWith("婴儿")) {
                switch (passengerTypeEnum) {
                    case ADT:
                        specialRemarkPrefix = "成人";
                        break;
                    case CHD:
                        specialRemarkPrefix = "儿童";
                        break;
                    case INF:
                        specialRemarkPrefix = "婴儿";
                        break;
                    default:
                        specialRemarkPrefix = "";
                }
            }
            baggerRule.setSpecialRemark(specialRemarkPrefix + cabinFare.getSpecialRemark());
        }
        return baggerRule;
    }

    private static String genBaggeageStr(String baggage) {
        return StringUtils.isNotEmpty(baggage) ? baggage : "无行李额";
    }

    /**
     * 儿童婴儿规则展示处理
     *
     * @param cabinFareListADT
     * @param cabinFareListCHDINF
     */
    private static void setChildInfRule(List<CabinFare> cabinFareListADT, List<CabinFare> cabinFareListCHDINF, String carrierNo) {
        if (StringUtil.isNullOrEmpty(cabinFareListADT)) {
            return;
        }
        if (StringUtil.isNullOrEmpty(cabinFareListCHDINF)) {
            return;
        }
        List<CabinFare> chdList = cabinFareListCHDINF.stream().filter(chd -> HandlerConstants.PASSENGER_TYPE_CHD.equals(chd.getPassengerType())).collect(Collectors.toList());
        List<CabinFare> infList = cabinFareListCHDINF.stream().filter(chd -> HandlerConstants.PASSENGER_TYPE_INF.equals(chd.getPassengerType())).collect(Collectors.toList());
        for (CabinFare cabinFare : cabinFareListADT) {
            String cabinCode = cabinFare.getCabinCode();
            String cabinClass = cabinFare.getCabinClass();
            String cabinType = cabinFare.getCabinType();
            CabinFare childFare = null;
            //处理儿童退改规则
            if (!StringUtil.isNullOrEmpty(chdList)) {
                if (PackageTypeEnum.DISNEY_FARE.getPackType().equals(cabinFare.getCabinType())) {
                    childFare = chdList.stream().filter(chd -> PackageTypeEnum.DISNEY_FARE.getPackType().equals(chd.getCabinType())).findFirst().orElse(null);
                } else {
                    //先取同等舱位的，同等舱位没有的情况取父舱规则
                    childFare = chdList.stream().filter(chd -> cabinCode.equals(chd.getCabinCode()) && cabinType.equals(chd.getCabinType())).findFirst().orElse(null);
                }

                if (childFare == null) {
                    childFare = chdList.stream().filter(chd -> cabinCode.equals(chd.getCabinCode())).findFirst().orElse(null);
                }
                if (childFare == null) {
                    childFare = chdList.stream().filter(chd -> cabinClass.equals(chd.getCabinCode())).findFirst().orElse(null);
                }
                if (childFare != null) {
                    ChildRule childRule = new ChildRule();
                    childRule.setRefundRuleList(childFare.getRefundRuleList());
                    childRule.setChangeRuleList(childFare.getChangeRuleList());
                    cabinFare.setChildRule(childRule);
                }
            }
            //处理婴儿退改规则
            if (!StringUtil.isNullOrEmpty(infList)) {
                //先取同等舱位的，同等舱位没有的情况取父舱规则
                CabinFare infFare = infList.stream().filter(chd -> cabinCode.equals(chd.getCabinCode()) && cabinType.equals(chd.getCabinType())).findFirst().orElse(null);
                if (infFare == null) {
                    infFare = infList.stream().filter(chd -> cabinCode.equals(chd.getCabinCode())).findFirst().orElse(null);
                }
                if (infFare == null) {
                    infFare = infList.stream().filter(chd -> cabinClass.equals(chd.getCabinCode())).findFirst().orElse(null);
                }
                if (infFare != null) {
                    InfRule infRule = new InfRule();
                    infRule.setRefundRuleList(infFare.getRefundRuleList());
                    infRule.setChangeRuleList(infFare.getChangeRuleList());
                    cabinFare.setInfRule(infRule);
                }
            } else {
                if (carrierNo.startsWith("MU")) {
                    if (childFare != null) {
                        List<ChangeAndRefundRule> changeRuleList = new ArrayList<>();
                        List<ChangeAndRefundRule> refundRuleList = new ArrayList<>();
                        childFare.getRefundRuleList().forEach(refundRule -> {
                            ChangeAndRefundRule changeAndRefundRule = new ChangeAndRefundRule();
                            BeanUtils.copyProperties(refundRule, changeAndRefundRule);
                            changeAndRefundRule.setChangeFee(0.0);
                            refundRuleList.add(changeAndRefundRule);
                        });
                        childFare.getChangeRuleList().forEach(changeRule -> {
                            ChangeAndRefundRule changeAndRefundRule = new ChangeAndRefundRule();
                            BeanUtils.copyProperties(changeRule, changeAndRefundRule);
                            changeAndRefundRule.setChangeFee(0.0);
                            changeRuleList.add(changeAndRefundRule);
                        });
                        InfRule infRule = new InfRule();
                        infRule.setRefundRuleList(refundRuleList);
                        infRule.setChangeRuleList(changeRuleList);
                        cabinFare.setInfRule(infRule);
                    }

                }
            }
            // 成人、儿童畅飞卡不展示婴儿退改规则
            if (PackageTypeEnum.CHD_UNLIMITED_FLY.getPackType().equals(cabinFare.getCabinType())
                    || PackageTypeEnum.ADT_UNLIMITED_FLY.getPackType().equals(cabinFare.getCabinType())
                    || PackageTypeEnum.UNLIMITED_FARE_V2.getPackType().equals(cabinFare.getCabinType())
                    || PackageTypeEnum.THEME_CARD.getPackType().equals(cabinFare.getCabinType())) {
                List<ChangeAndRefundRule> refundRuleList = Lists.newArrayList();
                List<ChangeAndRefundRule> changeRuleList = Lists.newArrayList();
                ChangeAndRefundRule before = new ChangeAndRefundRule();
                before.setDesc("起飞前");
                before.setChangeFee(-1d);
                before.setRuleDesc("仅退税费");
                ChangeAndRefundRule after = new ChangeAndRefundRule();
                after.setDesc("起飞后");
                after.setChangeFee(-1d);
                after.setRuleDesc("仅退税费");
                refundRuleList.add(before);
                refundRuleList.add(after);
                ChangeAndRefundRule changeBefore = new ChangeAndRefundRule();
                changeBefore.setDesc("起飞前");
                changeBefore.setChangeFee(-1d);
                changeBefore.setRuleDesc("不得更改");
                ChangeAndRefundRule changeAfter = new ChangeAndRefundRule();
                changeAfter.setDesc("起飞后");
                changeAfter.setChangeFee(-1d);
                changeAfter.setRuleDesc("不得更改");
                changeRuleList.add(changeBefore);
                changeRuleList.add(changeAfter);
                if (null != cabinFare.getChildRule()) {
                    cabinFare.getChildRule().setRefundRuleList(refundRuleList);
                    cabinFare.getChildRule().setChangeRuleList(changeRuleList);
                }
                if (PackageTypeEnum.ADT_UNLIMITED_FLY.getPackType().equals(cabinFare.getCabinType())) {
                    cabinFare.setChangeRuleList(changeRuleList);
                    cabinFare.setRefundRuleList(refundRuleList);
                }
                cabinFare.setInfRule(null);
            }
        }
    }

    /*
     * 手机请求类转换成统一订单平台请求类
     */
    public static PtTicketBookingReq toPlatformReq(TicketBookingV3Request request, String userNo, String realChannelCode) {
        PtTicketBookingReq target = new PtTicketBookingReq(HandlerConstants.VERSION,
                request.getChannelCode(), userNo, HandlerConstants.CURRENCY_CODE, HandlerConstants.LANG_CODE);
        BeanUtils.copyProperties(request, target);
        //2023-02-23 国内无需判断组合运价乘客类型
        //2021-05-31 旅客类型 首页聚合查询旅客类型，如果乘客查询ADT，下单乘机人包含ADT，如果乘客查询ADT，CHD，下单乘机人必须包含ADT，CHD
        if (request.getFlightInfoComb() != null
                && CollectionUtils.isNotEmpty(request.getFlightInfoComb().getPassengerType())) {
            target.setPassengerType(request.getFlightInfoComb().getPassengerType());
        }
        target.setTicketOrderSort("Normal");
        target.setTicketOrderType("Person");
        target.setUuId(request.getUuId());
        target.setFlightFareType(request.getFlightFareType());
        // SPA无法使用优惠券和积分及购买保险
        if (FareTypeEnum.SPA.getFare().equals(request.getFlightInfoComb().getFareType())) {
            if (StringUtils.isNotBlank(request.getCouponCode())) {
                throw new OperationFailedException("此航班无法使用优惠券");
            }
            if (request.getUseScoreTotal() > 0) {
                throw new OperationFailedException("此航班无法使用积分");
            }
            request.getPassengerInfoList().forEach(passengerInfo -> {
                if (CollectionUtils.isNotEmpty(passengerInfo.getInsuranceList())) {
                    throw new OperationFailedException("此航班无法购买保险");
                }
            });
        }

        List<PtTicketOrderInfo> tOrderList = toTicketOrderInfoList(request);
        target.setTicketOrderInfoList(tOrderList);
        double payAmount = OrderObjectConvert.sumPrice(tOrderList);
        if (request.getPostTripCert()) {//行程单
            target.setIsPostTripCert(request.getPostTripCert());
            PtTripCertSendInfo tripCertSendOTO = new PtTripCertSendInfo();
            BeanUtils.copyProperties(request.getTripCertSendInfo(), tripCertSendOTO);
            target.setTripCertSendOTO(tripCertSendOTO);
            payAmount += tripCertSendOTO.getDeliveryFee();
        }
        //
        if (StringUtil.isNullOrEmpty(request.getPremiumProducts())) {
            target.setIsBuyCoupon(false);
        } else {
            List<CouponProductInfo> couponProductInfoList = getCouponProductInfoList(request.getPremiumProducts(), request.getRouteType(), request.getPassengerInfoList());
            target.setIsBuyCoupon(true);
            target.setCouponProductInfoList(couponProductInfoList);
            for (CouponProductInfo couponProductInfo : couponProductInfoList) {
                payAmount += Double.valueOf(couponProductInfo.getBookingCount())
                        * Double.valueOf(couponProductInfo.getSalePrice());
            }
        }
        //头等舱休息室
        if (request.getBuyLounge()) {
            target.setBuyLounge(request.getBuyLounge());
            List<PtLoungeQuery> loungeQueries = new ArrayList<>();
            for (LoungeInfo loungeInfo : request.getLoungeQueryList()) {
                PtLoungeQuery ptLoungeQuery = new PtLoungeQuery();
                BeanUtils.copyProperties(loungeInfo, ptLoungeQuery);
                ptLoungeQuery.setLoungeName(loungeInfo.getName());
                loungeQueries.add(ptLoungeQuery);
                payAmount += loungeInfo.getLoungeAmount() * loungeInfo.getLoungeCount();
            }
            target.setLoungeQueryList(loungeQueries);
        }

        //wifi信息
        if (request.getBuyWifi()) {
            target.setBuyWifi(request.getBuyWifi());
            for (WifiQuery wifiQuery : request.getWifiQueryList()) {
                payAmount += wifiQuery.getWifiAmount() * wifiQuery.getPurchaseQuantity();
            }
        }

        // 行李计费
        for (PtTicketOrderInfo ptTicketOrderInfo : tOrderList) {
            List<PtPassengerInfo> ptPassengerInfoList = ptTicketOrderInfo.getPassengerInfoList();
            for (PtPassengerInfo ptPassengerInfo : ptPassengerInfoList) {
                List<PtWeightProduct> ptWeightProductList = ptPassengerInfo.getWeightProductList();
                if (CollectionUtils.isNotEmpty(ptWeightProductList)) {
                    for (int i = 0; i < ptWeightProductList.size(); i++) {
                        PtWeightProduct ptWeightProduct = new PtWeightProduct();
                        BeanUtils.copyProperties(ptWeightProductList.get(i), ptWeightProduct);
                        if (StringUtil.isNullOrEmpty(ptWeightProduct.getCouponCode())) {
                            payAmount += ptWeightProduct.getWeightAmount();
                        }
                    }
                }
            }
        }

        //2021-05-12 是否使用国际品牌运价下单 Y表示使用
        target.setBrandBuy("N");
        //if (!ChannelCodeEnum.WXAPP.getChannelCode().equals(realChannelCode)) {
        for (PtTicketOrderInfo ptTicketOrderInfo : tOrderList) {
            List<PtPrice> priceInfoList = ptTicketOrderInfo.getPriceInfoList();
            PtPrice price = priceInfoList.stream().filter(ptPrice -> StringUtils.isNotBlank(ptPrice.getBrandCode())).findFirst().orElse(null);
            if (price != null) {
                target.setBrandBuy("Y");
                break;
            }
        }
        //}

        target.setPayAmount(payAmount);
        target.setRandCode("check");//测试环境是否跳过运价校验，nocheck为跳过，其他情况不跳过
        target.setFareSource(buildFareSource(request.getInterFlag(), request.getFareSource()));
        return target;
    }

    private static String buildFareSource(String interFlag, String fareSource) {
        if (StringUtils.isEmpty(fareSource)) {
            if ("I".equals(interFlag)) {
                return "Searchone";
            } else {
                return "HO";
            }
        }
        return fareSource;
    }

    /**
     * 生成机票订单
     * 不同舱位生成多条PtTicketOrderInfo
     * 每个PtTicketOrderInfo对应一个PNR
     *
     * @param source
     * @return
     */
    private static List<PtTicketOrderInfo> toTicketOrderInfoList(TicketBookingBaseRequest source) {
        int i = 0;//子订单序号
        List<PtTicketOrderInfo> orderInfoList = new ArrayList<>();
        // 是否为拥军优属订单
        Boolean yjysFare = false;
        String fareBasis = null;
        if (ObjectUtil.isNotEmpty(source.getFlightInfoComb())) {
            if (CollectionUtils.isNotEmpty(source.getFlightInfoComb().getAdtCabinFareList())) {
                fareBasis = source.getFlightInfoComb().getAdtCabinFareList().get(0).getFareBasis();
            }
        }
        if (StringUtils.isNotBlank(fareBasis)) {
            if (fareBasis.endsWith(FareBasisEnum.YJYS_FARE.getFareBasisCode())) {
                yjysFare = true;
            }
        }
        Map<String, List<PassengerInfo>> groupPassenger = toGroupPassenger(source);
        for (Map.Entry<String, List<PassengerInfo>> entry : groupPassenger.entrySet()) {
            String passengerType = entry.getKey();
            List<PassengerInfo> passengerInfoList = entry.getValue();
            Map<String, List<?>> segPriceMap = toSegmentInfoList(source.getFlightInfoComb(), passengerType, passengerInfoList, source.getInterFlag());
            for (PassengerInfo passengerInfo : passengerInfoList) {
                if (passengerInfo.getCertType().equalsIgnoreCase("PP") && !passengerInfo.getCertNo().matches(PatternCommon.PASSPORT_NO)) {
                    throw new OperationFailedException("护照号不正确");
                }
                List<PtPSPriceRel> priceList = new ArrayList<>();//该乘客的 运价 航段对应关系
                List<PtPSPriceRel> curPriceRelList = (List<PtPSPriceRel>) segPriceMap.get(LIST_PSP);//该类乘客的 乘客 运价 航段对应关系
                for (PtPSPriceRel ptPriceRel : curPriceRelList) {// 乘客对应所有价格
                    if (ptPriceRel.getPassengerNO() == passengerInfo.getPassengerNO()) {
                        priceList.add(ptPriceRel);
                    }
                }
                List<PtPrice> curAllPriceList = (List<PtPrice>) segPriceMap.get(LIST_PRICE);//该类乘客的运价
                // 计算价格
                double ticketPrice = 0.0;
                double pricePaid = 0.0;
                double yqTax = 0.0;
                double cnTax = 0.0;
                BigDecimal otherTax = BigDecimal.ZERO;
                List<InternatTaxInfo> otherTaxInfos = new ArrayList<>();
                // 价格序号
                int[] priceNos = priceList.stream().mapToInt(PtPSPriceRel::getPriceNO).distinct().toArray();
                for (int priceNo : priceNos) {
                    for (PtPrice ptPrice : curAllPriceList) {
                        if (priceNo == ptPrice.getPriceNO()) {
                            ticketPrice += ptPrice.getRSP();
                            if (yjysFare) {
                                pricePaid += ptPrice.getDiscountedPriceValue().doubleValue();
                            } else {
                                pricePaid += ptPrice.getPriceValue();
                            }
                            yqTax += ptPrice.getYQTax();
                            cnTax += ptPrice.getCNTax();
                            otherTax = otherTax.add(BigDecimal.valueOf(ptPrice.getOtherTax()));
                            if (CollectionUtils.isNotEmpty(ptPrice.getOtherTaxList())) {
                                otherTaxInfos.addAll(ptPrice.getOtherTaxList());
                            }
                        }
                    }
                }
                passengerInfo.setTicketPrice(ticketPrice);
                passengerInfo.setPricePaid(pricePaid);
                passengerInfo.setyQTax(yqTax);
                passengerInfo.setcNTax(cnTax);
                passengerInfo.setOtherTax(otherTax.doubleValue());
                passengerInfo.setOtherTaxList(otherTaxInfos);
            }
            PtTicketOrderInfo tickOrder = toTicketOrderInfo(source, i++, passengerInfoList, segPriceMap, entry.getKey());
            if (groupPassenger.containsKey(HandlerConstants.PASSENGER_TYPE_ADT)
                    && groupPassenger.containsKey(HandlerConstants.PASSENGER_TYPE_GMJC)
                    && HandlerConstants.PASSENGER_TYPE_GMJC.equals(entry.getKey())) {//同时拥有成人和军残，优惠券只保持一份
                tickOrder.setUseScoreTotal(0);
                tickOrder.setCouponCode("");
                tickOrder.setPromoCode("");
            }
            orderInfoList.add(tickOrder);
        }
        return orderInfoList;
    }


    private static Map<String, List<PassengerInfo>> toGroupPassenger(TicketBookingBaseRequest source) {
        List<PassengerInfo> passengerInfoList = source.getPassengerInfoList();
        Map<String, List<PassengerInfo>> mapTicketGroup = new HashMap<>();
        if (HandlerConstants.FLIGHT_INTER_I.equals(source.getInterFlag())) {
            mapTicketGroup.put(HandlerConstants.PASSENGER_TYPE_ADT, passengerInfoList);
            return mapTicketGroup;
        }
        List<PassengerInfo> chdList = new ArrayList<>();
        List<PassengerInfo> adtList = new ArrayList<>();
        List<PassengerInfo> infList = new ArrayList<>();
        List<PassengerInfo> gmjcList = new ArrayList<>();
        for (PassengerInfo pass : passengerInfoList) {
            if (HandlerConstants.PASSENGER_TYPE_CHD.equals(pass.getPassengerType())) {
                chdList.add(pass);
            } else if (HandlerConstants.PASSENGER_TYPE_ADT.equals(pass.getPassengerType())) {
                if ("GM".equals(pass.getCertType()) || "JC".equals(pass.getCertType())) {
                    gmjcList.add(pass);
                } else {
                    adtList.add(pass);
                }
            } else if (HandlerConstants.PASSENGER_TYPE_GMJC.equals(pass.getPassengerType())) {
                gmjcList.add(pass);
            } else if (HandlerConstants.PASSENGER_TYPE_INF.equals(pass.getPassengerType())) {
                infList.add(pass);
            }
        }
        //成人与婴儿分组合并
        if (CollectionUtils.isNotEmpty(adtList)) {
            adtList.addAll(infList);
        } else {
            gmjcList.addAll(infList);
        }
        if (HandlerConstants.FLIGHT_INTER_D.equals(source.getInterFlag()) && CollectionUtils.isEmpty(chdList)) {
            //无儿童直接返回
            if (CollectionUtils.isNotEmpty(adtList)) {
                mapTicketGroup.put(HandlerConstants.PASSENGER_TYPE_ADT, adtList);
            }
            if (CollectionUtils.isNotEmpty(gmjcList)) {
                mapTicketGroup.put(HandlerConstants.PASSENGER_TYPE_GMJC, gmjcList);
            }
            return mapTicketGroup;
        }
        Optional<CabinFare> chdCabinFare =
                source.getFlightInfoComb().getChdCabinFareList().stream()
                        .filter(cabinFare -> HandlerConstants.PASSENGER_TYPE_CHD.equals(cabinFare.getPassengerType())).findFirst();
        if (!chdCabinFare.isPresent()) {
            throw new OperationFailedException("未查询到儿童票价");
        }
        //成人与儿童同舱
        CabinFare adtCabinFare = source.getFlightInfoComb().getAdtCabinFareList().get(0);
        if (StringUtils.isNotBlank(chdCabinFare.get().getCabinCode()) && chdCabinFare.get().getCabinCode().equals(adtCabinFare.getCabinCode())) {
            mapTicketGroup.put(HandlerConstants.PASSENGER_TYPE_ADT, passengerInfoList);// //因为成人、儿童与婴儿航段相同，使用成人类型取相同航段
            return mapTicketGroup;
        }
        if (StringUtils.isNotBlank(chdCabinFare.get().getCabinComb()) && chdCabinFare.get().getCabinComb().equals(adtCabinFare.getCabinComb())) {
            mapTicketGroup.put(HandlerConstants.PASSENGER_TYPE_ADT, passengerInfoList);// //因为成人、儿童与婴儿航段相同，使用成人类型取相同航段
            return mapTicketGroup;
        }
        // 不同情况分别取
        if (CollectionUtils.isNotEmpty(adtList)) {
            mapTicketGroup.put(HandlerConstants.PASSENGER_TYPE_ADT, adtList);// 因为成人与婴儿航段相同，使用成人类型取相同航段
        }
        if (CollectionUtils.isNotEmpty(chdList)) {
            mapTicketGroup.put(HandlerConstants.PASSENGER_TYPE_CHD, chdList);
        }
        if (CollectionUtils.isNotEmpty(gmjcList)) {
            mapTicketGroup.put(HandlerConstants.PASSENGER_TYPE_GMJC, gmjcList);
        }
        return mapTicketGroup;
    }

    // 生成子订单
    private static PtTicketOrderInfo toTicketOrderInfo(TicketBookingBaseRequest source, int orderNo,
                                                       List<PassengerInfo> passList, Map<String, List<?>> segPriceMap, String passengerType) {
        PtTicketOrderInfo ptTicketOrderInfo = new PtTicketOrderInfo();
        BeanUtils.copyProperties(source, ptTicketOrderInfo);
        ptTicketOrderInfo.setFFPId(source.getFfpId());
        ptTicketOrderInfo.setFFPCardType("Member");
        ptTicketOrderInfo.setFFPLevel(source.getFfpLevel());
        ptTicketOrderInfo.setFareType(source.getFlightInfoComb().getFareType());
        ptTicketOrderInfo.setFareSource(HandlerConstants.FLIGHT_INTER_I.equals(source.getInterFlag()) ? "S1" : "HO");
        ptTicketOrderInfo.setLinkerEmail(ptTicketOrderInfo.getLinkerEMail());//兼容java端请求
        if (HandlerConstants.PASSENGER_TYPE_CHD.equals(passengerType)) {
            ptTicketOrderInfo.setFFPLevel("");
            ptTicketOrderInfo.setUseScoreTotal(0);
            ptTicketOrderInfo.setCouponCode("");
            ptTicketOrderInfo.setPromoCode("");
        }
        List<PtPassengerInfo> ptPassList = new ArrayList<>();
        for (PassengerInfo pass : passList) {
            PtPassengerInfo ptPassengerInfo = new PtPassengerInfo();
            BeanUtils.copyProperties(pass, ptPassengerInfo);
            ptPassengerInfo.setIsSaveCommon("Y");//默认保存到常用联系人
            if (("Y").equals(pass.getIsBuyInsurance()) && CollectionUtils.isNotEmpty(pass.getInsuranceList())) {
                List<PtInsuranceInfo> insureList = new ArrayList<>();
                for (InsuranceInfo insure : pass.getInsuranceList()) {
                    PtInsuranceInfo ptInsure = new PtInsuranceInfo();
                    BeanUtils.copyProperties(insure, ptInsure);
                    insureList.add(ptInsure);
                }
                ptPassengerInfo.setInsuranceList(insureList);
            } else {
                ptPassengerInfo.setIsBuyInsurance("N");
                ptPassengerInfo.setInsuranceList(null);
            }
            //统一订单为String类型，需将list转换
            if (CollectionUtils.isNotEmpty(pass.getPassengerIdentityUrl())) {
                ptPassengerInfo.setPassengerIdentityUrl(pass.getPassengerIdentityUrl().stream().collect(Collectors.joining(";")));
            }
            ptPassList.add(ptPassengerInfo);
        }

        // 一单多券下单, 抽取PassengerInfo中的couponCode 按顺序使用逗号合并
        if (!Strings.isNullOrEmpty(source.getCouponScene()) && "1".equals(source.getCouponScene())) {
            String couponCode = "";
            for (PassengerInfo pass : passList) {
                if (Strings.isNullOrEmpty(couponCode)) {
                    couponCode = pass.getCouponCode();
                } else {
                    couponCode = couponCode + "," + pass.getCouponCode();
                }
            }
            ptTicketOrderInfo.setCouponCode(couponCode);
        }

        ptTicketOrderInfo.setTicketOrderId(orderNo);
        ptTicketOrderInfo.setPassengerInfoList(ptPassList);
        ptTicketOrderInfo.setSegmentInfoList((List<PtSegmentInfo>) segPriceMap.get(LIST_SEG));
        ptTicketOrderInfo.setPriceInfoList((List<PtPrice>) segPriceMap.get(LIST_PRICE));
        ptTicketOrderInfo.setPSPriceRelList((List<PtPSPriceRel>) segPriceMap.get(LIST_PSP));
        ptTicketOrderInfo.setFareCallSource(source.getFareCallSource());
        // 拼接中转城市
        if (FareTypeEnum.ONEWAY.getFare().equals(ptTicketOrderInfo.getFareType())) {
            ptTicketOrderInfo.setTransferCitys(genTransferCitys(ptTicketOrderInfo.getSegmentInfoList()));
        }
        return ptTicketOrderInfo;
    }

    public static List<String> genTransferCitys(List<PtSegmentInfo> segmentInfos) {
        List<String> transferCitys = Lists.newArrayList();
        // 往返或者单程
        String routeType = segmentInfos.get(0).getDepCity().equals(segmentInfos.get(segmentInfos.size() - 1).getArrCity())
                ? HandlerConstants.ROUTE_TYPE_RT : HandlerConstants.ROUTE_TYPE_OW;
        if (HandlerConstants.ROUTE_TYPE_RT.equals(routeType) && 4 == segmentInfos.size()) {
            transferCitys.add(segmentInfos.get(0).getArrCity());
            transferCitys.add(segmentInfos.get(2).getArrCity());
        } else if (HandlerConstants.ROUTE_TYPE_OW.equals(routeType) && 2 == segmentInfos.size()) {
            transferCitys.add(segmentInfos.get(0).getArrCity());
        }
        return transferCitys.stream().distinct().collect(Collectors.toList());
    }

    public static Map<String, List<?>> toSegmentInfoList(FlightInfoComb flightInfoComb, String passengerType, List<PassengerInfo> passengerList,
                                                         String interFlag) {
        int i = passengerType.equals(HandlerConstants.PASSENGER_TYPE_CHD) ? 10 : 0;// 航段序号赋值
        int j = passengerType.equals(HandlerConstants.PASSENGER_TYPE_CHD) ? 10 : 0;// 运价序号赋值
        int h = passengerType.equals(HandlerConstants.PASSENGER_TYPE_CHD) ? 10 : 0;// 乘客序号赋值
        for (PassengerInfo pass : passengerList) {// 乘客集合
            pass.setPassengerNO(h++);
        }
        List<PtSegmentInfo> listSeg = new ArrayList<>();// 航段集合
        List<PtPrice> listPrice = new ArrayList<>();// 运价集合
        List<PtPSPriceRel> listPSP = new ArrayList<>();// 乘客、航段与运价关系集合
        for (int f = 0; f < flightInfoComb.getCombFlightInfoList().size(); f++) {
            FlightInfo flight = flightInfoComb.getCombFlightInfoList().get(f);
            PtSegmentInfo seg = toSegmentInfo(flightInfoComb, flight, i++, passengerType, f, interFlag);
            listSeg.add(seg);
        }
        Map<String, List<PassengerInfo>> passMap = OrderObjectConvert.toPassageGroup(passengerList);// 乘客分组  ADT CHD INF GMJC
        for (Map.Entry<String, List<PassengerInfo>> entry : passMap.entrySet()) {
            CabinFare cabinFare; //运价
            switch (entry.getKey()) {
                case HandlerConstants.PASSENGER_TYPE_GMJC:
                case HandlerConstants.PASSENGER_TYPE_ADT:
                    if (CollectionUtils.isEmpty(flightInfoComb.getAdtCabinFareList())) {
                        throw new OperationFailedException("未查询到成人运价");
                    }
                    cabinFare = getCabinFare(flightInfoComb.getAdtCabinFareList(), HandlerConstants.PASSENGER_TYPE_ADT);
                    break;
                case HandlerConstants.PASSENGER_TYPE_INF:
                    if (CollectionUtils.isEmpty(flightInfoComb.getInfCabinFareList())) {
                        throw new OperationFailedException("未查询到婴儿运价");
                    }
                    cabinFare = getCabinFare(flightInfoComb.getInfCabinFareList(), entry.getKey());
                    break;
                case HandlerConstants.PASSENGER_TYPE_CHD:
                    if (CollectionUtils.isEmpty(flightInfoComb.getChdCabinFareList())) {
                        throw new OperationFailedException("未查询到儿童运价");
                    }
                    cabinFare = getCabinFare(flightInfoComb.getChdCabinFareList(), entry.getKey());
                    break;
                default:
                    throw new OperationFailedException("不支持的乘客类型:" + entry.getKey());
            }
            if (cabinFare == null) {
                throw new OperationFailedException("未找到合适的运价:" + entry.getKey());
            }
            PtPrice price = getPriceByPassengerType(cabinFare, j++, interFlag);
            listPrice.add(price);
            for (PtSegmentInfo seg : listSeg) {
                // 人与价格关系
                for (PassengerInfo pass : entry.getValue()) {
                    listPSP.add(new PtPSPriceRel(pass.getPassengerNO(), seg.getSegNO(), price
                            .getPriceNO(), true));
                }
            }
        }
        Map<String, List<?>> map = new HashMap<>();
        map.put(LIST_SEG, listSeg);
        map.put(LIST_PRICE, listPrice);
        map.put(LIST_PSP, listPSP);
        return map;
    }


    public static Map<String, List<?>> toOrderChangeSegmentInfoList(FlightInfoComb flightInfoComb, String passengerType, List<PassengerInfo> passengerList,
                                                                    String interFlag) {
        int i = passengerType.equals(HandlerConstants.PASSENGER_TYPE_CHD) ? 10 : 0;// 航段序号赋值
        int j = passengerType.equals(HandlerConstants.PASSENGER_TYPE_CHD) ? 10 : 0;// 运价序号赋值
        int h = passengerType.equals(HandlerConstants.PASSENGER_TYPE_CHD) ? 10 : 0;// 乘客序号赋值
        for (PassengerInfo pass : passengerList) {// 乘客集合
            pass.setPassengerNO(h++);
        }
        List<PtSegmentInfo> listSeg = new ArrayList<>();// 航段集合
        List<PtPrice> listPrice = new ArrayList<>();// 运价集合
        List<PtPrice> listPriceNew = new ArrayList<>();
        List<PtPSPriceRel> listPSP = new ArrayList<>();// 乘客、航段与运价关系集合
        for (int f = 0; f < flightInfoComb.getCombFlightInfoList().size(); f++) {
            FlightInfo flight = flightInfoComb.getCombFlightInfoList().get(f);
            PtSegmentInfo seg = toSegmentInfo(flightInfoComb, flight, i++, passengerType, f, interFlag);
            listSeg.add(seg);
        }


        Map<String, List<PassengerInfo>> passMap = OrderObjectConvert.toPassageGroup(passengerList);// 乘客分组  ADT CHD INF GMJC
        for (Map.Entry<String, List<PassengerInfo>> entry : passMap.entrySet()) {
            CabinFare cabinFare; //运价
            switch (entry.getKey()) {
                case HandlerConstants.PASSENGER_TYPE_GMJC:
                case HandlerConstants.PASSENGER_TYPE_ADT:
                    if (CollectionUtils.isEmpty(flightInfoComb.getAdtCabinFareList())) {
                        throw new OperationFailedException("未查询到成人运价");
                    }
                    cabinFare = getCabinFare(flightInfoComb.getAdtCabinFareList(), HandlerConstants.PASSENGER_TYPE_ADT);
                    break;
                case HandlerConstants.PASSENGER_TYPE_INF:
                    if (CollectionUtils.isEmpty(flightInfoComb.getInfCabinFareList())) {
                        throw new OperationFailedException("未查询到婴儿运价");
                    }
                    cabinFare = getCabinFare(flightInfoComb.getInfCabinFareList(), entry.getKey());
                    break;
                case HandlerConstants.PASSENGER_TYPE_CHD:
                    if (CollectionUtils.isEmpty(flightInfoComb.getChdCabinFareList())) {
                        throw new OperationFailedException("未查询到儿童运价");
                    }
                    cabinFare = getCabinFare(flightInfoComb.getChdCabinFareList(), entry.getKey());
                    break;
                default:
                    throw new OperationFailedException("不支持的乘客类型:" + entry.getKey());
            }
            if (cabinFare == null) {
                throw new OperationFailedException("未找到合适的运价:" + entry.getKey());
            }
            String[] fareBasis = cabinFare.getFareBasis().split("\\+");
            String[] priceValue = cabinFare.getPriceValueComb().split("/");
            for (int p = 1; p <= listSeg.size(); p++) {
                PtPrice ptPrice = new PtPrice();
                BeanUtils.copyProperties(cabinFare, ptPrice);
                ptPrice.setPriceNO(j++);
                ptPrice.setOtherTax(0d);
                if (p % 2 == 0) {
                    ptPrice.setPriceValueComb("0");
                    ptPrice.setPriceValue(0.0);
                    ptPrice.setRSP(0.0);
                    ptPrice.setOtherTaxList(Lists.newArrayList());
                    if (HandlerConstants.FLIGHT_INTER_I.equals(interFlag) && null != cabinFare.getTaxInfo()) {
                        ptPrice.setOtherTax(0.0);
                        ptPrice.setYQTax(0.0);
                        ptPrice.setCNTax(0.0);
                    }
                    if (listSeg.size() == 2) {
                        ptPrice.setFareBasis(fareBasis[0]);
                        ptPrice.setPriceValueComb(priceValue[p - 1]);
                        ptPrice.setPriceValue(Double.parseDouble(priceValue[p - 1]));
                        ptPrice.setRSP(Double.parseDouble(priceValue[p - 1]));
                    }
                    if (listSeg.size() == 4) {
                        ptPrice.setFareBasis(fareBasis[1]);
                    }
                } else {
                    if (p > 2) {
                        ptPrice.setFareBasis(fareBasis[1]);
                        ptPrice.setPriceValueComb(priceValue[p - 2]);
                        ptPrice.setPriceValue(Double.parseDouble(priceValue[p - 2]));
                        ptPrice.setRSP(Double.parseDouble(priceValue[p - 2]));
                    } else {
                        ptPrice.setPriceValueComb(priceValue[p - 1]);
                        ptPrice.setPriceValue(Double.parseDouble(priceValue[p - 1]));
                        ptPrice.setRSP(Double.parseDouble(priceValue[p - 1]));
                        ptPrice.setFareBasis(fareBasis[0]);
                    }
                    if (HandlerConstants.FLIGHT_INTER_I.equals(interFlag) && null != cabinFare.getTaxInfo()) {
                        ptPrice.setOtherTaxList(cabinFare.getOtherTaxList());
                        ptPrice.setOtherTax(cabinFare.getTaxInfo().getOtherTax());
                        ptPrice.setYQTax(cabinFare.getTaxInfo().getYQTax());
                        ptPrice.setCNTax(cabinFare.getTaxInfo().getCNTax());
                    }
                }
                listPriceNew.add(ptPrice);
                listPrice.add(ptPrice);
            }
            Iterator<PtPrice> it = listPriceNew.iterator();
            int index = 0;
            while (it.hasNext()) {
                PtPrice ptPrice = it.next();
                // 人与价格关系
                for (PassengerInfo pass : entry.getValue()) {
                    listPSP.add(new PtPSPriceRel(pass.getPassengerNO(), listSeg.get(index).getSegNO(), ptPrice
                            .getPriceNO(), true));
                }
                it.remove();
                index++;
            }
        }

        Map<String, List<?>> map = new HashMap<>();
        map.put(LIST_SEG, listSeg);
        map.put(LIST_PRICE, listPrice);
        map.put(LIST_PSP, listPSP);
        return map;
    }

    // 取乘客运价
    private static CabinFare getCabinFare(List<CabinFare> cabinList, String passengerType) {
        for (CabinFare cabinFare : cabinList) {
            if (passengerType.equals(cabinFare.getPassengerType())) {
                return cabinFare;
            }
        }
        return null;
    }

    // 生成运价
    private static PtPrice getPriceByPassengerType(CabinFare cabinFare, int num, String interFlag) {
        PtPrice ptPrice = new PtPrice();
        BeanUtils.copyProperties(cabinFare, ptPrice);
        ptPrice.setPriceNO(num);
        ptPrice.setIsGiftScore(null != cabinFare.getScoreGiftInfo());

        //多程惠达优惠金额
        ptPrice.setDiscountdiff(cabinFare.getDiscountPriceValue());
        if (null != cabinFare.getScoreGiftInfo()) {
            PtScoreGift ptScoreGift = new PtScoreGift();
            BeanUtils.copyProperties(cabinFare.getScoreGiftInfo(), ptScoreGift);
            ptPrice.setScoreGiftInfo(ptScoreGift);
        } else {
            ptPrice.setScoreGiftInfo(null);
        }
        ptPrice.setOtherTax(0d);
        if (HandlerConstants.FLIGHT_INTER_I.equals(interFlag) && null != cabinFare.getTaxInfo()) {
            ptPrice.setOtherTaxList(cabinFare.getOtherTaxList());
            ptPrice.setOtherTax(cabinFare.getTaxInfo().getOtherTax());
            ptPrice.setYQTax(cabinFare.getTaxInfo().getYQTax());
            ptPrice.setCNTax(cabinFare.getTaxInfo().getCNTax());
        }
        return ptPrice;
    }

    /**
     * @param source
     * @param flight        当前航班信息
     * @param num           航段序号
     * @param passengerType 乘客类型
     * @param fNum          当前航班索引
     * @param interFlag
     * @return
     */
    private static PtSegmentInfo toSegmentInfo(FlightInfoComb source, FlightInfo flight,
                                               int num, String passengerType, int fNum, String interFlag) {
        PtSegmentInfo ptSegmentInfo = new PtSegmentInfo();
        BeanUtils.copyProperties(flight, ptSegmentInfo);
        ptSegmentInfo.setSegNO(num);
        ptSegmentInfo.setIsCodeShare(flight.getCodeShare() == null ? false : flight.getCodeShare());
        ptSegmentInfo.setCarrierFlightNo(flight.getCarrierNo());
        ptSegmentInfo.setIsSeatedOnPlane(flight.getASR() == null ? false : flight.getASR());
        ptSegmentInfo.setPlaneStyle(flight.getFType());
        ptSegmentInfo.setStopNumber(NumberUtils.toInt(flight.getStopNumber()));
        ptSegmentInfo.setVoluntarilyChangeFlag(!flight.isNotVoluntaryChange());
        ptSegmentInfo.setUpgradeFlag(true);
        ptSegmentInfo.setPassengerType(passengerType);
        List<CabinFare> cabinList;
        if (HandlerConstants.PASSENGER_TYPE_ADT.equals(passengerType) || HandlerConstants.PASSENGER_TYPE_GMJC.equals(passengerType)) {
            cabinList = source.getAdtCabinFareList();
        } else if (HandlerConstants.PASSENGER_TYPE_CHD.equals(passengerType)) {
            cabinList = source.getChdCabinFareList();
        } else {
            cabinList = source.getInfCabinFareList();
        }
        for (CabinFare cabinFare : cabinList) {
            String cabinCode = StringUtils.isNotBlank(cabinFare.getCabinCode()) ? cabinFare.getCabinCode() : cabinFare.getCabinComb();
            if (passengerType.equals(cabinFare.getPassengerType())) {
                //舱位代码处理
                if (cabinCode.length() == 1) {
                    ptSegmentInfo.setCabin(cabinCode);
                } else if (cabinCode.contains("/") || cabinCode.contains("-")) {
                    String[] cabinCodes = cabinCode.replace("/", "-").split("-");
                    ptSegmentInfo.setCabin(cabinCodes[fNum]);
                }
                //舱位等级处理
                String cabinClass = cabinFare.getCabinClass();
                if (cabinClass.length() == 1) {
                    ptSegmentInfo.setCabinClass(cabinClass);
                } else {
                    //判断是否往返航班舱位 Y-Y/Y-Y 或者Y/Y
                    if (cabinClass.contains("/")) {
                        if (cabinClass.contains("-")) {
                            String[] cabinClasses = cabinClass.replace("/", "-").split("-");
                            // 单航段为cabinCode，联程的为cabinComb
                            ptSegmentInfo.setCabinClass(cabinClasses[fNum]);
                        } else {
                            String[] cabinClasses = cabinClass.split("/");
                            if (FlightDirection.GO.getCode().equals(flight.getFlightDirection())) {
                                ptSegmentInfo.setCabinClass(cabinClasses[0]);
                            } else {
                                ptSegmentInfo.setCabinClass(cabinClasses[1]);
                            }
                        }
                    } else {
                        // Y-Y 或者 Y
                        if (cabinClass.contains("-")) {
                            String[] cabinClasses = cabinClass.split("-");
                            // 单航段为cabinCode，联程的为cabinComb
                            ptSegmentInfo.setCabinClass(cabinClasses[fNum]);
                        } else {
                            ptSegmentInfo.setCabinClass(cabinClass);
                        }
                    }
                }
                break;
            }
        }
        return ptSegmentInfo;
    }

    /**
     * 处理下订单接口返回结果
     *
     * @param serviceResult
     * @param resp
     * @param reqId
     * @return
     */
    public static TicketBookingResp dealBookingResult(HttpResult serviceResult, TicketBookingResp resp, String reqId) {
        if (serviceResult == null) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            log.info("下单接口错误");
            resp.setErrorInfo("网络繁忙");
            return resp;
        }
        if (null != serviceResult && serviceResult.isResult()) {
            try {
                resp = (TicketBookingResp) JsonUtil.jsonToBean(serviceResult.getResponse(), TicketBookingResp.class);
                if (UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(resp.getResultCode())) {
                    resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                    resp.setErrorInfo(WSEnum.SUCCESS.getResultCode());
                } else {
                    if ("9998".equals(resp.getResultCode()) || "9999".equals(resp.getResultCode())) {
                        if (!resp.getErrorInfo().contains("避免影响生产环境的机票销售")) {
                            resp.setErrorInfo("网络异常，请稍后再试");
                        }
                    }else if ("5117".equals(resp.getResultCode())) {
                        resp.setErrorInfo("乘客姓名错误，请您核对乘机人信息");
                    }  else if ("1009".equals(resp.getResultCode())) {
                        resp.setResultCode(WSEnum.ERROR_QUERY_CABIN_NOT_ERROR.getResultCode());
                        resp.setErrorInfo(WSEnum.ERROR_QUERY_CABIN_NOT_ERROR.getResultInfo());
                        return resp;
                    } else if ("2210".equals(resp.getResultCode()) || "85000".equals(resp.getResultCode())) {
                        // 畅飞卡未使用数达到上限 或其他业务错误
                        resp.setErrorInfo(resp.getErrorInfo());
                        resp.setResultCode(WSEnum.ALERT_ERROR_MESSAGE.getResultCode());
                    } else if (UnifiedOrderResultEnum.F50003.getResultCode().equals(resp.getResultCode())) {
                        resp.setErrorInfo(resp.getErrorInfo());
                    }
                    String errorInfo = resp.getErrorInfo();
                    if (!StringUtil.isNullOrEmpty(errorInfo)) {
                        if (errorInfo.contains("常旅客卡号无效")) {
                            resp.setErrorInfo("常旅客卡号无效，请修改乘机人中常旅客卡号后再次提交订单");
                        } else if (errorInfo.contains("常旅客卡号信息有误")) {
                            resp.setErrorInfo("常旅客卡号信息有误，请修改乘机人中常旅客卡号后再次提交订单");
                        } else if (errorInfo.contains("舱位余坐不足")) {
                            resp.setErrorInfo("该航班舱位的剩余座位数不足");
                        } else if (errorInfo.contains("积分使用有误")) {
                            resp.setErrorInfo("积分+优惠券金额不能大于机票价格");
                        }
                    }
                    resp.setResultCode(WSEnum.ALERT_ERROR_MESSAGE.getResultCode());
                    resp.setErrorInfo("生成订单结果:" + resp.getErrorInfo());
                }
            } catch (Exception e) {
                log.error("请求号:{},生成订单规则出错{}!", reqId, e.getMessage());
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo("生成订单出错返回结果空");
            }
        } else if ("服务器超时".equals(serviceResult.getResponse())) {
            resp.setResultCode(WSEnum.OPERATION_TIMEOUT.getResultCode());
            log.info("下单接口超时,请求号:{}", reqId);
            resp.setErrorInfo("请至我的订单列表查看");
        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            log.info("下单接口异常:{},请求号:{}", serviceResult.getResponse(), reqId);
            resp.setErrorInfo("网络繁忙");
        }
        return resp;
    }

    //生成积分查询
    public static ScoreUseRuleRequest toScoreUseRuleRequest(QueryScoreRuleV3Request bookInfo, String userNo) {
        ScoreUseRuleRequest req = new ScoreUseRuleRequest(HandlerConstants.VERSION, bookInfo.getChannelCode(), userNo, null);
        List<PtTicketOrderInfo> tickOrderList = toTicketOrderInfoList(bookInfo);
        PtTicketOrderInfo adtOrder = tickOrderList.get(0);
        if (tickOrderList.size() > 1) {//儿童成人分开的情况
            PtTicketOrderInfo chdOrder = tickOrderList.get(1);
            adtOrder.getPassengerInfoList().addAll(chdOrder.getPassengerInfoList());
            adtOrder.getSegmentInfoList().addAll(chdOrder.getSegmentInfoList());
            adtOrder.getPSPriceRelList().addAll(chdOrder.getPSPriceRelList());
            adtOrder.getPriceInfoList().addAll(chdOrder.getPriceInfoList());
        }
        List<PtPSPriceRel> distinctPassengerPriceNOs = new ArrayList<>();
        for (PtPSPriceRel ptPSPriceRel : adtOrder.getPSPriceRelList()) {
            boolean contain = false;
            for (PtPSPriceRel distinct : distinctPassengerPriceNOs) {
                if (ptPSPriceRel.getPriceNO() == distinct.getPriceNO() && ptPSPriceRel.getPassengerNO() == distinct.getPassengerNO()) {
                    contain = true;
                    break;
                }
            }
            if (!contain) {
                distinctPassengerPriceNOs.add(ptPSPriceRel);
            }
        }
        Set<PtSegmentInfo> segmentInfoList = new HashSet<>();
        Set<Integer> segmentNos = new HashSet<>();
        for (PtPSPriceRel ptPSPriceRel : distinctPassengerPriceNOs) {
            segmentNos.add(ptPSPriceRel.getSegNO());
        }
        for (PtSegmentInfo ptSegmentInfo : adtOrder.getSegmentInfoList()) {
            if (segmentNos.contains(ptSegmentInfo.getSegNO())) {
                segmentInfoList.add(ptSegmentInfo);
            }
        }
        adtOrder.setSegmentInfoList(new ArrayList<>(segmentInfoList));
        adtOrder.setPSPriceRelList(distinctPassengerPriceNOs);
        req.setBookingInfo(adtOrder);
        return req;
    }

}

package com.juneyaoair.mobile.handler.controller;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;
import com.google.gson.reflect.TypeToken;
import com.juneyaoair.appenum.ChannelCodeEnum;
import com.juneyaoair.appenum.PayMethodEnum;
import com.juneyaoair.appenum.SensitiveOperationEnum;
import com.juneyaoair.appenum.WSEnum;
import com.juneyaoair.appenum.member.CertificateTypeEnum;
import com.juneyaoair.appenum.member.MemberDetailRequestItemsEnum;
import com.juneyaoair.appenum.member.MemberLevelEnum;
import com.juneyaoair.appenum.order.MerchantPaymentEnum;
import com.juneyaoair.appenum.order.OrderPayEnumType;
import com.juneyaoair.appenum.order.UnifiedOrderResultEnum;
import com.juneyaoair.baseclass.DCEPayment.common.DCEPayConfig;
import com.juneyaoair.baseclass.basicsys.response.CityInfoDto;
import com.juneyaoair.baseclass.common.request.BaseReq;
import com.juneyaoair.baseclass.common.request.BasicBaseReq;
import com.juneyaoair.baseclass.common.request.WalletDetailReq;
import com.juneyaoair.baseclass.common.response.*;
import com.juneyaoair.baseclass.member.request.AccountCheckParam;
import com.juneyaoair.baseclass.request.crm.MemberWhiteListRequest;
import com.juneyaoair.baseclass.request.payment.*;
import com.juneyaoair.baseclass.response.crm.MemberWhiteListResponse;
import com.juneyaoair.baseclass.response.order.comm.OrderPassengerInfo;
import com.juneyaoair.baseclass.response.payment.*;
import com.juneyaoair.baseclass.response.payment.hrBank.PaymentHRResultResp;
import com.juneyaoair.baseclass.response.payment.paysetting.Bank;
import com.juneyaoair.baseclass.response.payment.paysetting.BankDesc;
import com.juneyaoair.baseclass.response.payment.paysetting.SubBankDesc;
import com.juneyaoair.client.ClientInfo;
import com.juneyaoair.crm.IMemberService;
import com.juneyaoair.exception.CommonException;
import com.juneyaoair.exception.RequestParamErrorException;
import com.juneyaoair.exception.ServiceException;
import com.juneyaoair.message.bean.message.result.WechatAccessResult;
import com.juneyaoair.message.client.MessageClient;
import com.juneyaoair.mobile.aop.annotation.InterfaceLog;
import com.juneyaoair.mobile.comm.service.IRedisService;
import com.juneyaoair.mobile.constants.MobileCoreBeanNames;
import com.juneyaoair.mobile.handler.HandlerConstants;
import com.juneyaoair.mobile.handler.comm.RedisKeyConfig;
import com.juneyaoair.mobile.handler.comm.SourceType;
import com.juneyaoair.mobile.handler.config.bean.HandConfig;
import com.juneyaoair.mobile.handler.controller.service.IPaymentService;
import com.juneyaoair.mobile.handler.controller.service.ScoreControlService;
import com.juneyaoair.mobile.handler.controller.util.CRMReqUtil;
import com.juneyaoair.mobile.handler.controller.util.VirtualPaymentConvert;
import com.juneyaoair.mobile.handler.service.*;
import com.juneyaoair.thirdentity.basic.ParamPayMethodDTO;
import com.juneyaoair.thirdentity.basic.PayMethodActivityDTO;
import com.juneyaoair.thirdentity.basic.PayMethodDTO;
import com.juneyaoair.thirdentity.comm.request.PtApiCRMRequest;
import com.juneyaoair.thirdentity.comm.request.PtCrmMileageRequest;
import com.juneyaoair.thirdentity.comm.response.PtCRMResponse;
import com.juneyaoair.thirdentity.comm.response.PtCrmMileageResponse;
import com.juneyaoair.thirdentity.member.request.PtMemberDetailRequest;
import com.juneyaoair.thirdentity.member.response.MemberCertificateSoaModelV2;
import com.juneyaoair.thirdentity.member.response.PtMemberDetail;
import com.juneyaoair.thirdentity.request.payment.PtPaymentReq;
import com.juneyaoair.thirdentity.request.payment.PtPaymentWXReq;
import com.juneyaoair.thirdentity.response.payment.hrBank.PtAutoFillData;
import com.juneyaoair.thirdentity.response.payment.hrBank.PtHRPaymentResp;
import com.juneyaoair.thirdentity.response.payment.hrBank.PtTranDetail;
import com.juneyaoair.thirdentity.tongdun.PayMethodTypeEnum;
import com.juneyaoair.utils.EncoderHandler;
import com.juneyaoair.utils.StringUtil;
import com.juneyaoair.utils.http.HttpResult;
import com.juneyaoair.utils.http.HttpUtil;
import com.juneyaoair.utils.json.JsonMapper;
import com.juneyaoair.utils.json.JsonUtil;
import com.juneyaoair.utils.util.BeanUtils;
import com.juneyaoair.utils.util.MdcUtils;
import com.juneyaoair.utils.util.VersionNoUtil;
import com.juneyaoair.weixin.pay.JsSDKConfig;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by yaocf on 2017/4/21.
 */
@RequestMapping("/nemPaymentService")
@RestController
@Api(value = "支付服务")
public class NewPaymentController extends BassController {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource(name = MobileCoreBeanNames.API_REDIS_SERVICE)
    private IRedisService apiRedisService;

    @Autowired
    private IBasicService basicService;

    @Autowired
    private HandConfig handConfig;

    @Autowired
    private IPaymentService paymentService;

    @Autowired
    private IMemberCacheService memberCacheService;

    @Autowired
    private LocalCacheService localCacheService;

    @Autowired
    private IMemberService memberService;
    @Autowired
    private IMemberPasswordService memberPasswordService;
    @Autowired
    private MessageClient messageClient;

    @Autowired
    private IOrderService orderService;

    @Autowired
    private ScoreControlService scoreControlService;

    private static final String LOG_RESP_ONE = "不合理的访问请求";
    private static final String LOG_RESP_ONE_925 = "网络异常，请检查网络环境";
    private static final String LOG_RESP_TWO = "支付请求出错";
    private static final String LOG_RESP_TWO_925 = "支付出错，请重新支付。";
    private static final String LOG_RESP_THREE = "支付失败:{}";
    private static final String LOG_RESP_FOUR = "配置信息加载异常";
    private static final String LOG_RESP_FIVE = "支付失败:";
    private static final String LOG_RESP_SIX = "未找到对应的配置信息:{}";
    private static final String LOG_RESP_SEVEN = "支付返回结果:{}";
    private static final String LOG_RESP_EIGHT = "请求号:{},支付返回结果:{}";
    private static final String LOG_RESP_NIGHT = "支付发生异常！";
    private static final String LOG_RESP_PNR_ERROR = "小吉正在为您处理中，等候片刻即可支付";
    private static final String LOG_RESP_TEN = "请求号:{},响应结果:{}";
    private static final String LOG_RESP_ELE = "请求号:{},IP地址:{},客户端提交参数:{}";
    private static final String LOG_INFO_ONE = "渠道:{},IP:{},手机号:{}";
    private static final String VAULE_ONE = "PaymentChannelNo";
    private static final String VALUE_TWO = "ChkValue";
    private static final String VALUE_FOUR = "RespCode";
    private static final String VALUE_FIVE = "HUARUIPAY";
    private static final String VALUE_SIX = "BillCreateIP";
    private static final String VALUE_SEVEN = "buyer_id";
    private static final String VALUE_PROMO_PARAMS = "promo_params_str";
    private static final String PAY_DESC = "支付参数";
    private static final String SERVICE_NAME = "支付服务";
    private static final String DCEPAY_CODE = "RespCode";
    private static final String RESULT_CODE = "1001";

    /**
     * 畅飞卡上线期间支付方式固定写死
     */
    private Map<String, List<BankDesc>> paySettingMap = new HashMap<>();
    private static final String PAYMENT_CONFIG_MOBILE = "[\n" +
            "        {\n" +
            "            \"bank\": \"ANTCREDITPAY\",\n" +
            "            \"bankName\": \"花呗分期\",\n" +
            "            \"bankLogo\": \"data:image/jpeg;base64,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\",\n" +
            "            \"subBankLogo\": \"\",\n" +
            "            \"bankActivity\": null,\n" +
            "            \"bankActivityUrl\": null,\n" +
            "            \"bankActivityRemark\": null,\n" +
            "            \"orderNo\": 4,\n" +
            "            \"isDefault\": false,\n" +
            "            \"subBankDescList\": [\n" +
            "                {\n" +
            "                    \"subBankName\": \"分3期\",\n" +
            "                    \"subBankDesc\": \"手续费需另付\",\n" +
            "                    \"subBank\": \"ANTCREDITPAY3\"\n" +
            "                },\n" +
            "                {\n" +
            "                    \"subBankName\": \"分6期\",\n" +
            "                    \"subBankDesc\": \"手续费需另付\",\n" +
            "                    \"subBank\": \"ANTCREDITPAY6\"\n" +
            "                },\n" +
            "                {\n" +
            "                    \"subBankName\": \"分12期\",\n" +
            "                    \"subBankDesc\": \"手续费需另付\",\n" +
            "                    \"subBank\": \"ANTCREDITPAY12\"\n" +
            "                }\n" +
            "            ]\n" +
            "        },\n" +
            "        {\n" +
            "            \"bank\": \"ALIPAY\",\n" +
            "            \"bankName\": \"支付宝\",\n" +
            "            \"bankLogo\": \"data:image/jpeg;base64,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\",\n" +
            "            \"subBankLogo\": \"\",\n" +
            "            \"bankActivity\": null,\n" +
            "            \"bankActivityUrl\": null,\n" +
            "            \"bankActivityRemark\": null,\n" +
            "            \"orderNo\": 5,\n" +
            "            \"isDefault\": false,\n" +
            "            \"subBankDescList\": null\n" +
            "        },\n" +
            "        {\n" +
            "            \"bank\": \"WEIXINPAY\",\n" +
            "            \"bankName\": \"微信支付\",\n" +
            "            \"bankLogo\": \"data:image/jpeg;base64,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\",\n" +
            "            \"subBankLogo\": \"\",\n" +
            "            \"bankActivity\": null,\n" +
            "            \"bankActivityUrl\": null,\n" +
            "            \"bankActivityRemark\": null,\n" +
            "            \"orderNo\": 10,\n" +
            "            \"isDefault\": true,\n" +
            "            \"subBankDescList\": null\n" +
            "        },\n" +
            "        {\n" +
            "            \"bank\": \"CARDPAY\",\n" +
            "            \"bankName\": \"银联卡支付\",\n" +
            "            \"bankLogo\": \"data:image/jpeg;base64,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\",\n" +
            "            \"subBankLogo\": \"\",\n" +
            "            \"bankActivity\": null,\n" +
            "            \"bankActivityUrl\": null,\n" +
            "            \"bankActivityRemark\": null,\n" +
            "            \"orderNo\": 15,\n" +
            "            \"isDefault\": false,\n" +
            "            \"subBankDescList\": null\n" +
            "        }\n" +
            "    ]";
    private static final String PAYMENT_CONFIG_MWEB = "[\n" +
            "        {\n" +
            "            \"bank\": \"ALIPAY\",\n" +
            "            \"bankName\": \"支付宝支付\",\n" +
            "            \"bankLogo\": \"data:image/jpeg;base64,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\",\n" +
            "            \"subBankLogo\": \"\",\n" +
            "            \"bankActivity\": null,\n" +
            "            \"bankActivityUrl\": null,\n" +
            "            \"bankActivityRemark\": null,\n" +
            "            \"orderNo\": 1,\n" +
            "            \"isDefault\": true,\n" +
            "            \"subBankDescList\": null\n" +
            "        },\n" +
            "        {\n" +
            "            \"bank\": \"WEIXINPAY\",\n" +
            "            \"bankName\": \"微信支付\",\n" +
            "            \"bankLogo\": \"data:image/jpeg;base64,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\",\n" +
            "            \"subBankLogo\": \"data:image/jpeg;base64,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\",\n" +
            "            \"bankActivity\": null,\n" +
            "            \"bankActivityUrl\": null,\n" +
            "            \"bankActivityRemark\": null,\n" +
            "            \"orderNo\": 1,\n" +
            "            \"isDefault\": false,\n" +
            "            \"subBankDescList\": null\n" +
            "        },\n" +
            "        {\n" +
            "            \"bank\": \"ANTCREDITPAY\",\n" +
            "            \"bankName\": \"花呗分期\",\n" +
            "            \"bankLogo\": \"data:image/jpeg;base64,iVBORw0KGgoAAAANSUhEUgAAAlgAAAJYCAYAAAC+ZpjcAAAACXBIWXMAAAsSAAALEgHS3X78AAAgAElEQVR4nO3dP2xk53X38UeCOsfaTTWGVZCGA8TawqQL6QWiYqmIscvlymXiXaoKYunFUjFijBLAyw2QeOBAEReW/cKVyA1SWjtb2qAtsrALqRDpgpaACCILG5nKy/yp+eLcOXd1d/hn/tzz3Pv8+X4AQkIiD4f3Djm/Oc95zvPEycmJAwAAgJ0nuZYAAAC2CFgAAADGCFgAAADGCFgAAADGCFgAAADGCFgAAADGCFgAAADGCFgAAADGCFgAAADGCFgAAADGCFgAAADGCFgAAADGCFgAAADGCFgAAADGCFgAAADGCFgAAADGCFgAAADGCFgAAADGCFgAAADGCFgAAADGCFgAAADGCFgAAADGCFgAAADGCFgAAADGCFgAAADGCFgAAADGCFgAAADGCFgAAADGCFgAAADGCFgAAADGCFgAAADGCFgAAADGCFgAAADGCFgAAADGCFgAAADGCFgAAADGCFgAAADGCFgAAADGCFgAAADGnuKCAgCqOl132Tm36Jxbcu7Rv19kzzn3UP95OOgV/wSy9sTJyUnu1wAAstfpunnn3IpzbtU5t2BwPXY1cO3I16BXBDAgGwQsAMhYp1tUqdadc1c9XwUJXH35GvTcIa85pI6ABQAZ6nSLZb+NBoLVWfadc5uELaSMgAUAmel0i4rV7UB+asIWkkTAAoBMaPP6jlGPlQ9l2NqkZwuxI2ABQAZ0SVB6oOYi+WkfVHq2CFuIDgELABKnlStZfrsU4U96rEFLqlo7ATwfYCIELABIWATLgtM40sZ8+rUQPAIWACSs0y16mm4m+BM+0KpWP4DnApxCwAKARHW6xeDQ+4nf3yNtjN+gVwshIWABQIJ0aXAvoqZ2C1v0aiEUHPYMAGlayyxcOV0Kfa/TdXudbnHkD9AaKlgAkJjIdw1aOtameJYP0TgqWACQnhXCVeGSTqz/gzT764HWQCMIWACQHpbHTpPlw087XdfXA64Br1giBICEaJXmU+7pWLvOuXUa4uELFSwASAvVmclc1Yb4HRri4QMBCwDSQsCajgStdzpdd0jQgiUCFgCkZZH7OZM5ghYs0YMFAAnpdB1/1G3IhPhVerQwKwIWACRC51/9gftpimZ4zIQlQgBIB8uD9qrN8PS3YWIELAAAxiuDFgNLMRECFgAAkysHlm7okixwJgIWAKSD8/aac0vOe+x0i0O1gVNocgeAhLCLsBXsOMQpVLAAIC3H3M/GzVUa4enPQoGABQBp2eN+tuaq9met058FAhYApKXP/WzdbQm6na5byfw6ZI2ABQBpoQ8oDLJseJ9lw3wRsAAgIYNesUR4xD0NxqNlw9wvRG4IWACQng3uaXBu60HSTIPPBGMaACAx2mB96Jy7xL0N0l0935C5ZQmjggUAidE3bqpY4bpFE3z6qGABQIK0irWnzdYI1wMdUko1KzFUsAAgQfqGvcq9Dd41PXKHalZiqGABQMLkUGJdkkL4tpxza1Sz0kDAAoDEdbrFUuEC9zkKnGuYCJYIASB9Mhpgn/schfJcQzYpRI4KFgBkQJved6hkRWVfq1mcLxkhKlgAkAHt66GSFRcJw3LUzlruFyJGVLAAICNaydrU3WuIB+McIkPAAoAM6dl4t7n3UZEG+BWWDOPAEiEAZGjQKwLWixwMHRVpgP+QJcM4UMECgIzpkuEa1azosGQYOAIWAECC1rz2Zl3lakSDJcOAEbAAAI90usVOw3WCVlReGfSKcIyAELAAAKd0um5Rlw5vcnWisDXocfZkSAhYAIBz6dLhmh4cfYkrFTSZcbZEX1YYCFgAgLEqzfBrBK2gHWvIoi+rZQQsAMDECFrRoC+rZQQsAMDUCFpRuDvoMTOrLQQsAMDMCFrBY15WSwhYAIDaCFpB29d5WYe5X4gmEbAAAGY0aMkcrVtc1aDQ/N4wAhYAwJyOd1hnjlZQJGSt0fzeDAIWAMAbJsMHiR2GDSBgAQC863SLQaUStOa42kFg8rtnBCwAQCMqjfC3ueJBIGR5RMACADRK+7M2WTYMwq7uMGSMgzECFgCgFZ2uW9GgxViHdnGGoQdPJvcTAQCiMOi5vnNFNesud6xVC865HV3ChREqWACA1uluw02a4Fu1r1PfmZVlgIAFAAhGp1vsNKQJvj0MJDVCwAKAFumyzKI+g3n9Oou84RU9MoOe20n5nnW6xfXY1KUrNI+QZYCABQAN0WUwCQ9LGqTqBgjZAXaoXzspBS+O3GkdIasmAhYAeKLjCFb0q6mRBNJHI0Grn0Lg0lDaZ6dhKwhZNRCwAMCQVl5W9avtJa5jDSd93bEXJb2msmR4LdafIWKErBkRsADAQASHGx9rSNkY9Iolxeh0usUU+LdifO6RI2TNgIAFADVEEKzO8kCDVnRLiNoA32ecQ+MIWVMiYAHADHTZaiOyYDVKmuTXYwtaLBm2hpA1BQIWAEwpwSNeYg1aLBk2j5A1IQIWAEwog8rJlnNuLaYz6dhl2ApC1gQIWAAwgYx6f441ZG0G8Fwmon1wfQaTNkpeJ/McEH0+AhYAjNHpFiMXNjKrkjzQc+mieAOlL6sV+1rJImSdgYAFABfQcPVOptdIqhQrMfVmdbpFEGb6e3MIWed4MshnBQAByDxcOa3YvacHMEdh0Csa31+J5fkmYEFPDsAIKlgAcAbC1SlRNcAnuNMzdFuDXvE7A0XAAoARujPtPa7LKVEtB+nGhB1CVmMIWRUsEQJARWVHGk4rloO0oTx4OkZAwvIR97IRN3U2WfYcAQsATmFZ6WIxhqxFrb7Bv7d0eT17BCwAUNrMfZXrMVZsIeuhVrIIWc14R5dns0YPFgB8tjS4R/VqKrH1ZF3WniwGkvqX/bR3KlgAMLROuJragg5gjQKVrEbJ79JmLFVOHwhYALKnuwZv5n4dZhRVYzMhq1FZz8giYAGAoym3prc0pEahErKOY73gEVnodOM519ISPVgAsqa9V5/mfh0MyCiExZiOTGFOVqNeH/TiWU62QAULQO6oXtmY0xEX0dAG7JXIr3ssoqpyWiBgAcgdAcvOtdjeRPUga84ubEY/p/ENBCwA2dI/9nO8AkxF128z6BXPeSuAp5K6rHYWErAA5CyrJYuGzMV4XIqeocfOQv8WYgzhsyBgAcgZ/Td+rEdapVhhZ2EjruVwZiEBC0DOsj/OwxNZCoqxinUY4/OOVPJN7wQsAFnS8Qxsz/cnys0D2o/1IICnkoN+yv1YBCwAuZrnznslvVix7tBcZamwEZdSnvROwAKQKwKWf7FWsR6yVNgYmfSe5ABSAhaAXBGw/LuqS7HR0aXC3YTvTUhudbrpbTghYAEAfIr5jZMqVnM2Yw3j5yFgAQB8inanmB6lwwDSZlxKbT4WAQsA4NO1yK/uOg3vjZEl5fVUfhgCFgDAq5jnHelsrCSbsAN1O5X5WAQsAIBvsQ903aCK1agkziskYAHIVbLzdwIUdfOyjm3oB/BUcjGXQtWQgAUgVw+5841J4UiiZHqDInEz9tENBCwAWdIdYsBEtBeLI3SaFfVSIQELQM72ufuNuJrIz5HUGIEIRD26gYAFIGf0YWFig17Rh0Wze7OudbpxDnwlYAHIGQEL06LZvXnrMU55J2AByBkBC9NimbB5US4VErAAZEu339O4jIkNekUoZ5mweVdjWyokYAHIHUs+/qW2mYDXTDvWY9pVSMACkLVBr1h6oCLhV2ozx1habkdUS4UELACgr8a3w8R+HgJWe67FMoCUgAUAHObrW1JDXXXoKFXP9mzEsFRIwAKQPX3D3Mr9OniU4tR8TgJoz1wMRxcRsABgiLPmPNGdd6lhmbBdtzrdsM+4fCqA5wAUdJDcvB4MW5Z/y//bw5FPjPLH7SHnycGKVLE6XXfHOXebi2oq1TEYqfWVxWgz5IPEnzg5OQngaSBHna5bcu7R16LuEJnFrgauPoELdWhfx54uQcDGK7pTMyn69+s9XiOte33QC7OHkoCFRnW6btW5YgfIUo1AdZEjnVGzSdjCLHjjNPfHOtA1KRrG/5DazxUh2WwwH+JrjIAF73Tpb12DlY9QdZ5dDVpswcdUOt3iE/EtrlptW4Ne8aEqSZ2u4w00DEG+zghY8EYrARKsrrZ8laWqtaYn4QNjaXVClp0XuFq1fC3lSjIBKygvhraZgl2EMCc7Ozrd4oX+XgDhymk/zX15TjGeyI7m6XLDKrOOatnNYJn+KIDngKHgdgETsGBGPvXr0sqHgQSrUfKcPu102Y6P8TQcRHW4bGBy+D1jJ2E4rmqPbzAIWDChRxccRtK3crvTdXtUszCO9u+9zoWa2lais68QtqAOgyZgoRatWklv0/2GG9jrkt6aPe0TA86lW8CZ8j65Yyp/aMlcSK89AhZmplN0ZRnlWqRXUQLhe6GVlREe3aFEyJrMaopjGc5BFTw8a6GsThCwMJNOt/iU8GEiAxnfIWRhHELWRO5mtluXgbThuRRK/x8BC1PrdIu+lLcSu3KELIylIesOV+pM+4MeS4MIws0QzikkYGFi2m8lS4I3E71qhCyMNegVn45f4Uo9Zl9PZ8hGSM3UOFPrx+cQsDCRjAYvvqM7IoFz6e7CrzEnq1CEq4z6rkrBHjKMwtW2NzERsDBWhlOtN0MoLyNsOidrXo9kylWu4crR4B6FVnuxCFi4UKZHhkiTJOcXYiwJFoNe8Sk5x76snMOVI2BF4WqbKxIELJwr8/PYFnQqPTCW9mV9TUNHDmSQ6GLG4crl1nMWsdb+jhOwcCYOuy3cYhApJiVLhhI6dPJ7qr1Z8nO9orspc0cbQRzm2tq8RMDCefqZh6sSVSxMRSe/y/LR3cSunPSaLWqDf9Z0kGVMJ1fkrpVeLAIWTtE5VyEe1tyGBUY3YFramyUzob6UwHDSI+fcdek1G/Q43FhR2Y5LK1WsJ05OTiK/brCkE9pTGyJalyyLzGfeb4IatOIhv1urEVU+5HW/of1lqNDzV2M9IixXR4NesxsTqGDhEe03IlyddknfGIGZSOVHK1rzOqQ05Gb4I+2zuky4OhcVrPg0XsWigoWCNrUf0ldwrsY//SBtWtVa0fDedr/jsfZdbg56xeYWnEO3/d/n+kSp0b/jBCwU9Agcmtovdj2zg2zRkErYWtKvJj7oHOlO4T6v68lpj2qqx4Xl4JWmNmoQsCB/MGQZ4DZXYqwHgx7H6JQ0FJz1afChTjnH7Nd2UccAVL/qhC6pUO1VvnZoWJ8elf4k7Os4Fe8IWJnTvqv3cr8OU/jj3Jrd9TWypG/0l6fcYSq9Rg+rb+6Er9lp8CoPGb6oD2hPr7tjyc+O9vC8k8rPk7EXm/i9IGBlTD+NyR/iudyvxRQaKy+3pbJcteJxXMduZXmKwIUo0EqRjF094sorAlbG9CiYW7lfhyklu0yolaq1Fraflw3WfV26YhwGgkO1Pzneq1hP5XldoX8sCFfTS257tr4W1lscLntJm4Zv6vN5UAYuwhYCwsiKtKxqFd0bKlgZYmmwti+l0CCsS4GhT+0nbKF1VK+S5fVvOYNG87ROuKol+kNetVl3L4Ijka5pU/GhbI/XGURA06hepcnrfaWClRndhfRh7tehpjsxT7hOYI7PsVbeNmmQh28MFk2a12PQ6MHKz0buF8BAlBPddWl4M4Ez1C5p/+CtTrcYA1GGLZYQYUp/Z/ibma5LurHHywdmlggzostCoS8JxSDWI3N2EjygdkHPz/yDHMDLEiKMrdFOkTxv5xOyRJgJGttNNTJDxVJmx3scaWP8BtPKMSvaKbLiZb4hFax88EksU3oUUk5np83pEuKnna7bafoEfcSvspyOPHj5G0EFKwOcn2UumgoWn8IfOa5UtWiMx4U40DlL5iMbqGDlYZ1wlS0+hQ+Vw0w/lONOpKqlHzyAx2jFk3CVH/NGdypYidNhkp/mfh2MRVHB0qXB2wE8lZBt6Q5EDkRGWfHd4QNplsxHNlDBSh8D8jKk1Zm13K/DBKRS8V6nWwwyXdcPJMiQ/s4QrvJ1SQ+4N0PASpi+WVDqthdDtWONN4qpzGm171Md90BjfEYIV1CmH0oJWGmjeuVHDAMtCQizK47n6XTdQz2eJ/qjkXC+Srha4DJlb8Hy952AlSiqV14FvQtNh20ykqO+amO8LCGusYSYFsIVzmBWxSJgpYvqlScRNEQzzdzenE6MZwkxEYQrnMPs7ycBK0H6h4M3WT92I3iOUU2Zj9DoEiK/a5EhXOECl6w+QBGw0kSDsz9BV690CYvlwWaUS4j3dQlxg36t8OnvCOEKFzH50ETAShPb8/3pB/78eINvR3k8z4eMfAhXp1tUd/cIVxjjmsXvLwErMVrapHrlx3EEx6wQsNpXHfmwR3N8GOQ+yMwz/j5iQrWrWE9xpZNDc7s/oVevBG/kYVnQ5vi3Ol23r0cX9a3PPMP5Kgc3X+MyYQoSyDfqXDAqWAnR8jf9N/7EcK4fAStcC5WdiHu6jEjF0SPdgHBIuMIM5ur+flLBSgtbx/054rw6GFrQr9udrjvS6uhmBEvQUaBqBSOrdXqaCViJYLCodzFUrxCnskH+VqdbHDjb168dy4Nnc6G9Vuv0WsHACgELjuqVdwQsNKEc/VB8WOp0i7lrZdiiunUBbZHYYIcgDBXLhLP+7hGw0kHA8mcroqZkeZ5XA3gesHG1vJ+6lLhDdetxGqzWed3Dk7VZ318JWAmgud27mKpX7E5L19xIdWu/Eray6w/UBvY1ghU8m3lcAwErDVSv/NmN7M2LgJWPaqO802OcdlIOXNq8XjYe86ESTZCjc1YGvenH9BCwIqd/cGhu9ye23iv6dPJVLicmF7i0WrXC3zq0ZGmWOYhPnJyccMMippPb38n9OngioxmimyslhxCzgwpn2NfAtaehK9hqp35wXNJQtcLrGS2b6b2AClb8OHfQn1in4vf5pI8zLFR32OlIiL1K6NprM3RpL2n5RV8VQjLTbkIqWBHT2Vef5n4dPImyeuU+W065H8BTQXyqoetQv/Ysdyzq3615DVLzen4moxUQuruD3nQFDSpYcat9GCXOFe2ZjtKMqdUJllUwrUvV0RAl7emSJcYyaO1V/v0ilysHkM/TmI6ITT10lApWxOQ8Mz75eRFt9arU6RYDF2+F8WwAIAlfmmYZncOeI6VldsKVH9FWrypqnQIPADhlqlUjAla8WB70Q6pX0R+Lo5+ytgJ4KgCQCgJWJhgu6kcK1avSujYtAwDqu6ojRCZCwIoQy4PeJFG9KmkVi6VCALAzcRWLgBUnlgf9SKl6VRj0ip9pP4CnAgApWJr0ZyBgxYnlQXu7KVWvRqyyVAgAJqhgpYrlQW+Sq16VdPowE/8BoD45/HlxkkchYMVn4vIkJrYb+2G442h17m7YzxIAojDR+zABKz70X9lLtnpVpcc8MLoBAOqZ6H2YgBWfa7lfAGMPUq9eVQ16RT8WIQsAZjfRYeQErIjoIb6wlV1vEiELAOrpdMcvExKw4kL/la2tac6VSgkhCwBqIWAlhoBlK4veq/NoyHolzGcHAEEjYKWC8Qzm7uZavarS3YVfkyn24TwrAAje2D4sAlY8qF7ZOc69elWlc7IWGeMAAJMb14dFwIoHAcvOxqDnHqbyw1iQ66FjHKSatRv/TwQA3hGwEsEOQhvHHIB8PqlmDXrFH43rLBsCwIUIWLHTsfyXcr8ORqheTWDQc/1Br+j7u05FCwDOdGEfFgErDiwP2qB6NSUNWvL6e5GgBQCPu+hcQgJWHAhYNtapXs1Gpt1XgtaDGH8GAPDg3PdnAlYcCFj1HQ16VK/q0qAl/YBfYlApAFDBipbOv6L/qj7GMhiSGWI6qJSgBSBnVLAiRvWqviMdqAljBC0AmZvrdN3lsy4BASt8BKz6qF55RtACkLEzlwkJWOEjYNVD9apBBC0AGTrzfZqAFTAtO87lfh1qIly1gKAFICNUsCJ07u4ETIS5Vy0jaAHIAAErQiwP1sPU9kCMBC3maAFIyZmN7gSssBGwZkf1KkAatFaYDA8gMaeqWASssLFEOLtNqlfhGpkMT9ACELtTBRECVqAYMFob1asIVIKWHCp9lPv1ABCt+dEnTsAKF9Wr2W3JUlSsTz5Heqi0/IF6haAFIEIsEUaEgDU7qleR0pll8tq/o310ABCDhdHnSMAKFw3us9kd9NxejE8cQ9I7N+gV0/elonWXywIgBp3u44URAla4qGDNhupVIjRorTFDC0AkHuvDImAFSOdp0OA+PTkWpx/bk8bFKjO02HEIIGRUsCJA9Wo2VK8SVtlxSCM8gBARsCJA/9VsOHcwA9IIrzsOaYQHEBKWCCNwap4GxtpisGheKo3w9GcBCMFjOwkJWGEiYE2P6lWGtBGe/iwAQdAh4QUCVpiu5n4BpiTN7TtRPWOYGunPYtkQQFsIWKGqpl9MjOZ2FHRQKfOzALTlUaM7ASs8BKzpMZoBj1TmZ32NZUMADbtcfjsCVnjYQTidB5w7iLPIRH+WDQE07NF7OAErPFSwpkP1Chdi2RBAGwhY4SFgTe5Y3zyBC40sG+5ztQB48miT2lNc4eCsVddwcSGuE6aiB4EvdrrF79k6R1IB8OWJk5MTLi6A7OiOXdmBeo27D8DQizI6hiVCAFnSQ6RXnHPXaYIHYI2ABSBrg16xUYIjdwBYKWZhEbAAZG/kyJ2j3K8HgFqK/mACFgAoPXJpkZEOAGoopgEQsACgojLSgWoWgFkQsADgPFSzANTBmAYAGKPTLY6/6DM3C8AkBj33BBUsABhDq1lS9n/AtQIwCQIWAExAe7OYmwVgIiwRAsCUdAr8ZvXcMQCoeJGABQAz6nSL8wxvc/0AjPAUsK4sz2u/whJXHIDaqVyIQ3ewfZjChel0i52G0gA/F8DTARAGo4B1ZVmmlq7o1xI7bQBMYVf/UwlgD51ze7EFsE63mNy8ycHRANSdegHryrKEqTX+qADwZFcD1/DrYHsv5Avd6RZ/D9f5kAlkb8aANQxW6zR4AmjBrla7dtzB9k5oN0CXDKWatRDA0wHQjikD1nApkDI4gJCUgasfSoWLJUMge1MErCvLK/oHg9I3gFAda8O5hK1+289RlwzfCvZqAfBld7KAdWV5wzl3i9sAICJBhC1dMtzhwymQlQkC1pVlqVrd5HUBIGJl2NpoYxlRlwx36MsCsjEmYBGuAKRnX9sdNt3B9sMmf7pO1/E3FcjDBQGLcAUgbWVVa73JmVudrlt1zr3DawtI2v7ZAevKMo2ZAHKyq0GrkbEP9GUB6TsdsK4syy/+h9x7ABna1aXDTd8/uvZl9ZknCKTp8YA1nHO1x5laADJ3pBWtJoIWB0YDCXpy5EdaI1wBQPF38B13ZXlPT67wZtArAtaL2hMGIBGfVbCuLM9r9YqeAAB43G7xAdTjiAdGOQBpqQYsdg0CwMW2NGh5G+/AKAcgDcOANey9OqR6BQBjHWt/1oavS8UoByB+ZQ/WKuEKACZyqRhj47E/a9ArqljX6csC4lVWsPZY9weAmdzVipb5siHzsoB4PXHy7EvS3P4p9xAAZnZUrAR4GFRK8zsQJ1ki9LoFGQAyIGMd3nNXlje0p9XMoOce6t/pfV5IQDwkYC1yvwDAxK2i2jQ8EcNMJWTtcpuAOBCwAMDWQnHc2PBMVzMSsga9ImRtcb+A8EkP1hmnPQMADDzQ3izTBnhmZQHhGz0qBwBg55qnJcNVKllA2AhYAODXgoasFcvvQsgCwkbAAgD/ZI7VfXdled3yOxGygHARsACgObf13FczGrIY4QAEhoAFAM26qcfsWM7LYk4WEBgCFgA0r+zLMglZDCMFwkPAAoB2SMg6tNphqCFrlQOigTAQsACgPZcsxzgMem5PK1mELKBlBCwAaJePkGU6RR7A9AhYANA+65AlOxXvcF+B1uwTsAAgDNYha12P6gHQvIcELAAIh2nI0qZ3dhYCLSBgAUBYzEIWOwuB9hCwACA8ErI2LeZk0fQOtOOJk2dfOuHaI2vPfMG5la/bXYEP9p173+OqjOXz/f3Aufs/s3ks+LBfjF042H5Y97E73aLx/SZ3CWjEnae4zsjeMx3nXr1hdxV+dM9zwDJ8vh/8hoAVNhlG2tfZVnVJFWtRHxOAZwSsqs//kXPPftnPY//2E+f++39sHut5j38ffQYDALO4WhwQfbC9WufqST9Wp1v0Y33IXQD8I2BVSXD5oafRMf9nxe6x3vi2c3/qKQiufoeQBYRHDojecQfbm3WemfRjdbrudefcW9xjwKuHBKwqX5Uh6XOxql45rTL5Clhf+RMCFiYnvzPPZbTi1G7P2jvuyvKhO9jeqfMgg57b6HTdSlEZA+DLHgGrSsKFD9aBRR7vWy/7ea7yhnnvp34eG+mRcGXZvxa69nvW+sX4hoPtw5qPI0uFe7pbEYAHjGkoSf/Vc1/189gfeAhYvuRUjQDic0mb3msZ9JwEtHXuP+ANk9wfialxXJYb5ZO0D5//nHNf8bT8CMDCgruyvFH3cWSpkCnvgB/S70jAKvkKWB9/4tzv/tP+cd/fs3/M0vNWp3QA8OSWu7JssXOm1s5EAOcjYJVeesHP4/pazvvFr/w8rvNczQNgRSa9z9d5LJ3y7mnrNJCtI0fAUjIZ+4sdP4/tKwh9JHO1/tfPY//5n/l5XACWhsfp1LdRviEAMFFsQmEXofNYvZIA5LMhXcKb5REvVXJNfFbJmiLh+Zkx4dl696h8T59VQMvn+/TnJnuuloNyYUmGkK65g+2Ze7J0AOl6MQYCgBkClrjuKaRY7x4cRcAaT65P02ME5Hv6ui/WZJ7a5pvjH5QBtCF7S4eQztyYOei5TZ3yzmwsoL5iVh1LhFJt8DW003dAkcf3tUzoq6oHwAeLpcI17gxgh4DlM0hsN1AB8lUlk3ENhCwgFgvFUmEN2vB+lzsO1FZUkwlYvpYHf/nrZnpWfE6VJmABMVmvu6tQh4fOnkwAACAASURBVI8ec9eBWh667AOWDNSMdXmw+n18LhPKhHsAMbikOwJnJg3vLBUCtVHBcte/4e+xm1geLPkKc7JMuEwVC4jItboDSKXhnbENwOz0g0rmuwhXPAWs/s+b3dLe/5m/XWtyjdo93Ba5+NG94Ze1rX/1d85omDYMzitkbAMwm0eN0flWsKR6JRUaH5oebyDb538/8PPY8sYkOy0BxGLOXVmudZCzVrF2uePA1B6W/4N8A9aNl/08rgSdNuZH+awyNT1HCkBda+7K8uWaj1ErpAGZejSPLs+AJZOrfTW3t7WcJsuSvsjyI83uQEwsGt53qssdACaSeQXr1Zv+Httn0LnI7/5zOBrCF18VPwC+3DQY21ArpAEZ2il/5PwClvQT+Wp4lXAlQact937q7xvf+CZVLCA+Fr1Y7CgEJndY/pf5BSyf/UT9lnfb+Wx2lw0BVLGA2FhUsejFAiY06OUasGSwqK9xBh9/EsZhuD62uZeoYgExqhuQ+kx3BybyWAjIaw7WG6/6e+x77/p77GlIk71U6b7YsX/ssorlM8RZm2S2kmx62HzT7hv7mudUsny+H/zGuZt/a/NYCNXNYmzDwfbhLM9PhiZ2ukXI8ti8CiThsd+xfCpY8qbkq/dKluVCGsbpM+xRxQJiVLeKRbM7MN5e9b/IJ2C98W1/jx1aRUfCnq/zCaWK5fNaAvChVi/WoFe8cTCyAbhYhgFLprb7mnsVWvVKyDE9PncUSh+bVAQBxGS15nPd5G4DF8psiVCWs7oZVa9K8rx87Sh0niuCAHyoO92dgAVcQCu9j6QfsCQI+DpzMMTqVZXP8CcVQenHAhALme6+MutzlWZ359wD7jZwplNnd6YdsGQZy9dYBvH3P/D32BYk/PmsYsluRQ6CBmKyVvO59rnbwJlO7dJNN2DJ0uA/f9ff48v29hDmXo3jMwRKZdDnNQZgbcFdWV6s8Zg7E/w3QI72Rn/odAPWa55mQZVCr16VJAT6PKNQRl+wVAjEZOYqlk6pZjchcFomAeulF5z7lsdjXaS3qc0zB6f1/R/7G9sgun8znJIPIAYrNZvdWSYERgx6p6u76QUs6QnyuWwlPU2hTG2flIRBn2MbxPe/ywBSIA61mt0JWMApZ1Z10wtYb9/xt2vQ6dKgzJmKjVTd5LxEX2RXIaMbgFjU2U0oSyFH3GngkVPLgy65gCWVK18DRcW/vRtHY/t53vDcNyY7NunHAmJwjWVCwEziAUumtfscySBLg29HdMjxWT76xP9gVOnHYso7EIM6y4QMHQU+k3DAkgbrf/o7v98j1qXBUb6XCsUP/5GmdyB8dZcJj7nHwNkN7uKp6K+NvJFv/avf7yGhJOalwVGv3Xbu3Z/461WTx5V7svyXaYTSackAVvkCwjZcJjzYfjjjs+wXh0gDeTs1wb0UdwVLdq3JG7nPpnYZKBrqeYOzkl2Fvud4yT259yY7C4Gw1VkmZOgocM7yoIs6YMkbd/EG7jFcyeyo177n7/Hb9ItfDZv2fZINB4QsIGRLNZ4bAQu44PcgzoBVhiufOwYlXN3827SXuGQAqVTofCJkASGr04fFVHfggoAVXw9WE+FK9H483HWXOqnQ+b6eZci68Z08e7IwGdkJ/HydY/LOwYaLi1wqziY82D53mWOMneJ8QyBPR4OeO7eHMa6A1VS4kp6r+z/z+z1CIYHHd9O7I2RhAnJ2qM/zQ3GelYv6SMaY9X8HpODCZfJ4lgibClf9n6fX1D6ONL0Xy6Eezyt0LBcCgaIPC5hNAgFLSvzb/95MuPK9uy5UshzaVMi6/xOWbYBwXJ31mWgfFsfmIFeRByyZCu57FIPLPFyVJGRJ75lvsgwk95SJ70AYrizXqWKxTIgcHekHjHOFHbCk6XXT8ygGR7h6jPSe/cO/+P8+ck/l3so9BtC2OrsLCFjI0djl8XADlhzc7Pv4G0e4OpOErJf/2v9yoZB7LPcaQJvowwKmE2HAeuYLwx4dnwc3lwhX52uqJ0vIvZZ7LvceQBuoYAHT6Y/7r8MKWC+9MBwX4LuZXcgyGOHqYmXI+v3A//eSey73Xl4DAJo2V5xLOAOdA8TBz8jJ/kXzr0phzMGSbfuv3XDuWy/7/15SkZFgJUfFYDwJWdf/upkRGdKX9cM7wyN83r4X77wsCaS/8xhKn/5cMx9CkJvFGst9e3V2IwKRmej3pP2AJTvJpAeniQGDH3/i3Bs/yGNCuyUJOhKy5D41sXQrQVsqWRKE34/wJA7pYfM5S01+Z2SDAGBriYAFTGTs8qBrdYlQqlZvfHv4RtFEuJJ+K5kiTrianQQeWVptoi9LXhPy2pDXCINJgSbM1/geF25XBxJyPOhN9kGknYAl1Qlpam5qSbDst+KIlvqkOiN9WR83FFTlNSKvFWZmAb7VCVg0uiMXE1d5m10ilF1iUpH48z9r5vt98JthsJKjYGCn7Mt69cbwy7eymvXLXzv3/R9zP1Plq3dNTg3wPUsvDXWW+KhgIRcTLQ+6xgKWLPHceLmZN2OnVSvpgbn302a+X67kGstmge9/t5mmawnmzy0M7+u9d6lIpsZX75qcGvDcVzO/uBOSnYQH22N3R42SidadbgDPH/Bv4gqW/yXCG98cniPYVLiSKocMySRcNaOsZskbYxO9WVKJkNeSvKaYAg9YqzMPizMJkbr9ccfjVPkNWG//o3Pdv2mmPC89Qavfce6177GE1AYJWBJsZTNBE+Q1JVPg5TUGwMpMs7AUy4RI3eY0P5/fJcKmdn9J78b2r4bLR8/RDN0qCbfS+9bUkgw7DAFLi9P0mIyYemkRiMxUY0zCGDRalzRBN7UEienIsiENxkAOZCfhNe40EnU06E23Wzbcw54vIsuB0muF8C3/ZXP9WQDqqtODBaRs6spuXAFLlgJlppU0Vf/2PwJ4QhhLdvo13Z8FYFb0YAFnm6r/ykUTsMqxC1INka3ciI/0ZslMsr/4K4IWkCYCFlI19fKgCz5gVYOVz7Pd0ByCFhAylgiB02ba+BFmwBoNVgyUTA9BCwjRJe4KcMrUy4MuuIBFsMoPQQsAEK6ZlgddMGMapHldAhX9Vfkqg5a8DuRYpZVvxDveQSbMP+9xpeVpxl4gPIOe2+G4HCRo1rlwLQcsGbcgZ8oRrFCSoCUHOr+tQUuOWootaMlcNvkCAMRupuVB11rAkknfP9py7v39Vr49IlCOd5AAvvzCcJAsoQXwb8YDn4EEzbw86BoPWNJjI2+anBWISUnQkgqnfL30wrCi1dQxPECeFqc9EgRI1MzVK9dIwJLG9Xs/HYYrghXq+MWvhl9f+fIwaK18ncsJAPAl4IAlwUqWAdkNCEsfffJZQ7yELQAAbO0OevWG5/oNWFJtAHyRiihVUQCAvVrVKxfMmIZZfLDvb7q7bLP30VAtuyZlh1yINt8M83kBaBpH3iB3x3XGM5TiDViy9OhrF6LMMPIRsP7rf9k5CSBsB9sELOSuP+i52jtp4zjsORXvz7zbEwAANKP28qAjYAEALHS6bokLiQTI7CuTMSUELAAAgKENq+tAwDqLr63/cuYiAAAIlcnyoCNgncPX2XeMFAAQNnbhIGdbFs3tpXh3EQKhkvEhvkaIiOcXGKsBX+q8uSxyVxA5s+qVI2CdQd68fGFEA3C+Z77g5/fvaU8VaYy6zBVBxMya20sELABhkLMlOV+ybcySQa7WrX9uerBGPeepgvXBb/w8LgDYYYkQOTKZ3D6KgDXq6T/y87gceA0gfHWmuLNEiFhtWja3lwhYo77yJ34e97f/4edxAcBOnYA1z31ApMxmX1URsEb5moH10Sd+HhcA7NQJWHPcB0TowaDn54BzAlaV7GJiBhaAXM140HOnS/UK0fJSvXIErBG+qleOChaA4NWZI0PAQoz2rUczVBGwqnwFLHYQAghfnWUSdhAiRt6qV46ANeJ5T38j3me0DIDg1flDxQ5CxEYGi5pObh9FwKp67qt+HpflQQDhqxOwlri/iIz5YNFRBKzSSy/4e2yOyAEQvjoBix4sxMTLYNFRBKySrzMIP/6EIaMAQndcYwfhZUY0IDIbPgaLjiJglXxVsLZ/5edxAcBOneoVDe6IybHv5vYShz073T34xY6fx/4FAasVUpGc9FxJmX9myddmCR/P95mOc6/eGP/f/X7g3P2f2X3fs/T+n3MfeTjxYPNN+8dMT52t6vRfISaNVK8cAUtd/4afx/3v/6XBvS0SriYJDj7IZglfGyasfXHCgCWjRnwHLAlX9Cu2hQoWctBY9cqxRKh8LQ9SvQIQhzoVLAIWYtFY9coRsHQpieVBAPnadwfbM73p0OCOiDRavXIELOfciqflQelZIWABCB/9V8hBo9Url33A+vwfObfydT+PTbgCEAcCFlLXePXKZR+wbrzs77HvvevvsQHADgELqWu8euUIWN/087iy4+p3/+nnsYGQPPsn3I647dbsv/I0oRkw00r1ymUdsGQ0w+c/5+ex+563swOhkGV2xKzOcSFUrxCDVqpXLuuA5WtGUhMDGYEU/W7AbW1enYC1EusPjWwctVW9ctkGLAlXvkYzEK6A2bCs3rT9Wc8fVFSwELr1tqpXLsuAJUsavnqvZHI7ze3IydOeltnRhJmb2zvdYrgo868QsqNBz222+fzyC1iv3fDXe3Xvp8799//4eWwgRH/6ZZsn9XuWB1tQ582H6hVCt9b288srYMmhzt/yNJqB6hUwO/qvmibLg3XOH1yN88dGJnYHvVr9hSbyOuz5jVf9PfaP7lG9ColURGRcBmzIQcyjnvmC3cXld6dpM1evOl03z3gGBG49hKeXT8CSvqvnvurnseXNXJYHEQ7ZbMCGA7+eMdwo8tszAhx8YvcgUrU16NUanmsmjyVC+aTtayyD+P6P/T02kIOPPuE2N+dBzd2DLA8iVMehVK9cNgHr7Tv+Gtt/+WvOHUSenjNcJWJEQ5Nmrl6xPIjAyVDROh8eTKUfsN74tt1Op1HS2E71Crl62nCKOxWsphy7g+06uwepXiFUMpYhmOqVSz5gvfSCv12DThvb+eSNXH3F6BzCjwlXDao71ZqAhVC1PpZhVLpN7jKS4Z+/6+/xZYcaje35eN5oVeS3n4zfMScbMqyWtOVDgC9WTe7v7/t7jhhVZ/fgEsNFEaggxjKMSjNgybT273/XX9+VLA3+/Q/8PDbCI+Fq802bp/UXfzU+YMn3+/M/s/l+H+z7CzBWx02xPNiULZrbkaggX5vpLRFKuLr3pr++KyHhiqXBfFg1c8s4j0leN5aByHJWVZVVRc9RwWpQnerVZcYzIFB3Qmpsr0ovYPlsahf/9i67BnOz/ILNDzzp68by9WW506/Ksv+KDytN2HUH23VmA0m4uhTPj4tMHBn0FXqTVsCSnquVr/t7fHkzYNdgXqQCZBXYJ63USOCwOpvvWU8fNr7S8DVBXXV3VwW1OwtQq4OeexjqxUgnYPkOV/KGd+M7/h6/bdJXhtOsXlNyfaepTFlNoZdw6GOZ0GqJkGn7TahVvaK5HYF6EMrE9vOkEbB8hyt5c3zte2mfl0aj8dmuf8PmcaZd9uv/3Ob7Oh1XYkkCm0WDu3xo4XXXBKpXSM1xDJsu4g5Y0tDuO1yJ//u99N8ILM+VS4WEK6udctOO9JBlQqvDqq8b/35YVa/oZWxC3eqVTG6/GtVPjBysh7w0WIo3YJW7BX2Hq3/4lzz6RKyCREqszq+U3r1ZAno/0GVCq8b5e+/aPA4uQvUKqZGZV8E2tlfFGbCkwdb3KAan4SqHHhFfW/ljZlq9mjFIyGvPqtnd8rBziyVHqc6xe9A3i+rVzWh+WuQgiqXBUnwBS5Yntv6VcGXJakdYKqQ62v22zQ8jAanO68jqNSiVXosgLeHKYoCvVXUOF6F6hdSshzrz6ixxBSz5FC4TtX1NaC/lFK6chyboUqw7Ey1PAah7VI1Uv6yu4xsGodGi6b9u6MQktmpWrxgsitBEszRYiiNgSUXh7X+0XeY4T27hSqoavvrYYtwYIAHC6pgaiyAhO1etzhOUn6tOg7q8Viyujc/zEeF0GaVu9WmNwaIISFRLg6XwA5a8IWz/u92b3nmkSrD6nfw+Wb99x99jxzbWQpZKrZYGhdVQWtmBaNWLJbtu5QPLLCyCeNPVK+v+wjhe0xt1zhzU3qvbtk8JqCWqpcFSuAFL3gRkSaOJJUEJVzf/Nq+p0mVV0Gcv22//w99jW5NwJb19Vq81aeK2HENgdbi4NO7LEui05PVy45v1v3/T1Svr6mz4r+kjd7BN7xVSEt3SYCnMgCVVq/s/ce5bL/v/XrKFfvkv8xl4KG+Usgwm19d3VfCDSAKrdbiSwG4ViEoS/n/5a5vHkvs+bT/WjZfrXx/5XWuyeiX3tYm2grDUWkZh5yACE+XSYOmpMJ6GKqtWvmdblWRatvUboS91e6Xkfy9fz321mecrISOGiqB1uBK9H/sZQfDGD4bL5RbPVT68fDRh4JHXjUVQaeocT7mn8iHCxwe0sDduPKh5oLML+eBcZGktxqXBUjgBS5Yf5I+47+VAp38k5U0wpn4rmbQe06fxGKZ0y5uw9FxZvuYktPt6XUn/j5wqIMvmFv7p73Rg75gp8/88w5LiqH97t37gPtg+///3sVagfY9v+SjYJcLan/T1zMFrdk8JqEXOGtyM+RK2H7BkOVCqVr7/MJbkD7FUAjgDza+Q5xz5qpTKa8t3lUZCivQxWYXt7t8MKz7nVXLlg0/dqqc0tr/tufeqqb8f4VZlV93Bdt2jQ6heIRRHMS8NltoLWPJH/Y1Xm1uycvopWv7Qp3xocwjkDTXUNyKZ+SXhyvpoIKmK3vhOM68tCViW4zXkcZ79snOv3T69tGlR3ZPwlsLv3MfBfiiTpcF+nQfodIs3M6MzkIDaVmM4a3Cc5gNW031WTt/w5Y98TrsE29RUr800pFL66k0/gb7chdpkiJBrLKHIqnIjj/PuT4bhbdqDqS8ij5fK7939nwfwJE6xWBq8TPUKAbkz6Lm6vYRBaD5gyZtCk+GKqlWzZKdbSP1XUrGyWOY6Txmuml5yltezVMwsz+SUapUsGco/LcYpyGshpaGiYfYVrhgtDTJUFCGQkQzJjAkJaxehJXqtmidh442AdmVK1eqHPgepthSuHn1/DyHLSvn7lwrZvBDe4dR36+4a1MZ2xjIgBFGPZDhLfIc9jyNvenLczfW/Jlw1qY1lsjaFMj+tDFkh9Qc12Y/WBPl5wqvE7buD7TWDx4l6lxaSshrzSIazpBWw5I+gvOlxkGyz2q7kNE2qGSEFCHke8oGiH0CPUIpB29dcs9kdWxzE3OkWSzFzLf4cQOnuoOdqbdQIURpLhPLGIuEqvBJ++qRyctbusxSVlQzLJnBLf69L4tJH1YYUg7b0cIb3gW21zlmDbhiuFjlvEIGQviuLamxw4g5YBKv2yJupBI2UmpgvImcLSoAJ/bUm9+T9veE5k9ajKC6SargKb0fsnbojGRRLgwiBSTU2VHEGLIJVe8pgde/dPPqtQq9anUVCjiwZvnajufM8U6pilmdJhrdrcMvgIGepXm0w8wqBWElh3tV54glY8kdPpoPLGzvBqlnl4FB5w4nhCBwrRbCKNEjKc5bqi9wvOerGVzVLKnuvfS+NsB32hwcZJlZ7GUV3Dd6yeUpALa+nMu/qPOEHLHlzv6d9EDnPsvqv/xm+mTVBAqx8SSVEvnIKtPImK6EklQqpBGPZ+OHjrM8wl9CmI/f7A/3wsP2rUP/GyDLKUt15VzpQNLlGYkRpa9BLf7htuAFLhhRKqMqpYnIRCTrS4wI/JMjf/1m6S59SmZGf78bLw7BVJ2i1tYRm8QFDDmuWDyvlB4fwe8ZMwpXaZKAoAmBSjY1BWAGrrFbJH26WAdGEnIK8BMdy2VOC1vVvTL902OaSYJ4fMGRS+17dB+l0ize0azZPCZjZcep9V1XtB6xySUY+YTMYFL7FsSTkVxm05EtC1so3xh8lJNdN5kExY65Jr9Sd1O4+G8nwVlQ/OVK1ktow0Yu0E7DKUJVb0zTaIVUXGV0gwYoDvx8ngUm+nvnCsKolZzeOkl270mvFeZ5NknBVe5SC9l0l3UiMaLySelP7qCdOnn3pJKynBABZMwlXbhiw9hjJgABIU3tS5wxOIr2zCAEgXpbhapNwhQDs5hiuHAELAIJhGa6kqf0mtxYt2095Uvs4BCwAaJ9luFqhqR0ByGrH4FkIWADQLstwtcg5gwhAMb8tpx2DZyFgAUA7jj2Eqx2GiSIAq4Oeqz2/LXZxHvYMAHErJ7SbvAnpOAYmtSMEMo4h+yOZHBUsAGjcvodwtcOOQQTgzqDHEnWJChYANGff8GxBwhVCIrOu1rkjnyFgAUAzttzBttk8IMIVApLlINFxWCIEAP9eIVwhUfuEq7NRwQIAf46KQYtG/VaOcIWwDJe8caYn9QIBAGw9cM4tEq6QqH2ddZXtINFxpIJ1yC8rAJh63R1sb1g+IOEKASFcTeBJ/YUFANQnbzxfI1whYYSrCT1FwAIAE3fdwfaa9aVkQjsCkv35gtN44uTkxLkry7JMOBfP0waAYEgj+6o72Db/sEq4QkDK8wWzPwJnUuWYBsbaA8D07riD7XlP4Uq2vn9IuEIACFczKAOWab8AACRu1zn3JXew7WVydadb/E1+hxcRAkC4mtFwiVBcWZZPYFfj+xEAoDGyHLjmDra9VP0rhzZf45YiAEfac0W4mkF1kjtnCAHA2Y6L5cDhXCtf4arstyJcIQSyW3CRcDW7zypYrqhi9fnlBoDHbGnVytvOKe232qDfCoFgFIOB0aNy1nTsPb/kAHK3VVT2D7YPfV4H7be6lfvFRjAIV0Yer2C5ooolIeuteH8kAKilqWC1qP1WDA9FKAhXhk4HLMdSIYDsHOu4Gu/Byg3D1Zr2vbJagFDI2ZmrhCs7o0uEpVWOZQCQgSOtIm347LEqsUsQgdoa9Ir3fRg6u4LliiqW/CHYY8I7gATtaqhqbMhyp+tWNFxRtUJICFeenB+wXBGy6BEAkIqyWrXZxDJgqdN187pDkKoVQvP6oMegcV8uDljuUSWL5UIAMSp7qzZ9HGczDr1WCNgrg17xgQOejA9Y7lHIkpR7kxsBIHBlqOo3uQRY1ekW4242+GCKAHH0TUMmC1ilK8v0EAAI0X4lVLX2xqFN7HwYRag4+qZB0wUs96iatc5gPAAt2tfWheFXAzsAx+l0i7+La3wARaCYcdWw6QNW6cryvAatFf6gAPDoSHc072mg2gshUJX0mJt1dlwjYMVxT4SrZs0esErDitaKfrFLBsCsJEgdapA6fBSqAgpTVQQrROLOoFe8TtGw+gFr1JXlpeLE+eHXPDcUQMXOGf9+2OTYhLp0npUsBV7lxiJgx1q1YqdgS+wDFgAkiIoVIkIzewDOOyoHALKnuwJXtWJFsEIMdjVc0W/VMgIWAIzQ6etrGq7YxINY3B30itctAkDAAgCl/VWrbNhBZOi3ChABC0DWqFYhcjLfapV+q/AQsABkR0NVWa3iOBvEivlWASNgAcgCoQqJeX3QK45lQqAIWACSpYcuS6haIlQhESwJRoKABSAZnW4x4Hip8kVPFVLCkmBECFgAoqUVqvL0CAIVUsUuwQgRsAAET/un5jVEzWugYskPOdjVJcFojpPCEAELQBB0ee+yBqgyRF3mzD9kjIOaI0bAAozoWXUccH6xyxqcSvMcQQOcQiN7AghYgJ2+c0WPBFPAAczqrhwqTiN7/J44OTnJ/RoApvS4lU0argFM4UirVjtctDQ8mfsFAKwNekUlS5a+HnBxAUxAqlaLhKu0UMECPNJq1gZ9RgDOsK/jFwhWCSJgAZ51ukVjt+wEusW1BqDYIZg4AhbQEB2KucH8JiBrzLXKBAELaFinW3xqXaMJHsgK09gzQ8ACWqCTyTcY6QBkgTMEM0TAAlqky4abNMEDSaKJPWMELCAALBsCSWE5EAQsIBS6bChB6yY3BYjWHVn+ZzkQBCwgMLpsuM4hx0BUHmjVit2BKBCwgEAxpBSIwq6eHUifFR5DwAIC1+kWvVnr9GcBQTnSYEWfFc5EwAIioNPg12iEB1pHAzsmQsACIkLQAlpzrEv2NLBjIgQsIELsOAQaQ7DCTAhYQMQIWoA3BCvUQsACEqBBS5YNV1k6BGohWMEEAQtICD1awMyONFhtEqxggYAFJKgStFaZowVciHEL8IKABSSu0y1C1jpBC3jMri4D9rks8IGABWRCj+CRqtY17jkytqXBao8XAXwiYAGZqew8XKFPC5mQZcBN7a/irEA0goAFZEr7tFZYPkTCdjVU0V+FxhGwAJTLh6vM00ICjrVatUG1Cm0iYAF4RKtaq/q1wJVBRKhWISgELABn6nTdYiVs0auFENFbhWARsACM1ekWvVqr7EBEAGQJsK+haocbglARsABMrNIYv0LYQsMeaKhibhWiQMACMBPCFhrwQKtVfY6vQWwIWABqI2zBSLn8t0OoQuwIWABMadhaqgQuGuRxkaMyVLH8h5QQsAB4VdmNuMToB6jdSqjiyBokiYAFoDGVpcQl/WKCfB6OKkt/Oyz9IQcELACt0erWUuWL5cQ0HJVhSgMVM6qQHQIWgGBUAtciFa6o7DtXLPURqABFwAIQrErDfBm4rnK3WndcDVPy7yz5AacRsABERatc8jVfCV8sLfohS32HZZDSMEV1CpgAAQtA9DrdImyVgav8d6pdkyurUnsaqPaoTAH1ELAAJEuXGMuKV7nc6DINX2WIelgJUocEKcAPAhaAbHW6jwJX+c+y+uUiC2FleHKVEFUGqYfMmgKaR8ACgDEqQcyNhLBSWSGzVFaYqsrw5AhOQNgIWAAAAMae5IICAADYImABAAAYI2ABAAAYI2ABAAAYI2ABAAAYI2ABAAAYI2ABAAAYIYELjgAAAMhJREFUI2ABAAAYI2ABAAAYI2ABAAAYI2ABAAAYI2ABAAAYI2ABAAAYI2ABAAAYI2ABAAAYI2ABAAAYI2ABAAAYI2ABAAAYI2ABAAAYI2ABAAAYI2ABAAAYI2ABAAAYI2ABAAAYI2ABAAAYI2ABAAAYI2ABAAAYI2ABAAAYI2ABAAAYI2ABAAAYI2ABAAAYI2ABAAAYI2ABAAAYI2ABAAAYI2ABAAAYI2ABAAAYI2ABAAAYI2ABAAAYI2ABAAAYI2ABAABYcs79f+89int8f0yHAAAAAElFTkSuQmCC\",\n" +
            "            \"subBankLogo\": \"\",\n" +
            "            \"bankActivity\": null,\n" +
            "            \"bankActivityUrl\": null,\n" +
            "            \"bankActivityRemark\": null,\n" +
            "            \"orderNo\": 4,\n" +
            "            \"isDefault\": false,\n" +
            "            \"subBankDescList\": [\n" +
            "                {\n" +
            "                    \"subBankName\": \"分3期\",\n" +
            "                    \"subBankDesc\": \"手续费需另付\",\n" +
            "                    \"subBank\": \"ANTCREDITPAY3\"\n" +
            "                },\n" +
            "                {\n" +
            "                    \"subBankName\": \"分6期\",\n" +
            "                    \"subBankDesc\": \"手续费需另付\",\n" +
            "                    \"subBank\": \"ANTCREDITPAY6\"\n" +
            "                },\n" +
            "                {\n" +
            "                    \"subBankName\": \"分12期\",\n" +
            "                    \"subBankDesc\": \"手续费需另付\",\n" +
            "                    \"subBank\": \"ANTCREDITPAY12\"\n" +
            "                }\n" +
            "            ]\n" +
            "        }\n" +
            "    ]";
    private static final String PAYMENT_CONFIG_WEIXIN = "[\n" +
            "            {\n" +
            "                \"bank\": \"WEIXINPAY\",\n" +
            "                \"bankName\": \"微信支付\",\n" +
            "                \"bankLogo\": \"data:image/jpeg;base64,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\",\n" +
            "                \"subBankLogo\": \"\",\n" +
            "                \"bankActivity\": null,\n" +
            "                \"bankActivityUrl\": null,\n" +
            "                \"bankActivityRemark\": null,\n" +
            "                \"orderNo\": 5,\n" +
            "                \"isDefault\": true,\n" +
            "                \"subBankDescList\": null\n" +
            "            }\n" +
            "        ]";


    //SHA1Encode加密
    private static String sha1Encode(String str) {
        String chkValue = null;
        chkValue = EncoderHandler.encodeBySHA1(str);
        return chkValue;
    }

    //支付配置渲染
    private Bank initPay(String channelNo, String orderType, String payMethod, String merchantPayment) {
        String dir;
        orderType = changeOrderType("", orderType);
        if (StringUtils.isBlank(merchantPayment)) {
            dir = RedisKeyConfig.PAY_METHOD_CONFIG_DIR + channelNo + ":" + orderType + ":JX:";
        } else {
            dir = RedisKeyConfig.PAY_METHOD_CONFIG_DIR + channelNo + ":" + orderType + ":" + merchantPayment + ":";
        }
        Bank bank;
        String paykey = dir + channelNo + "_" + orderType + "_" + payMethod;
        try {
            String pay = apiRedisService.getData(paykey);
            if (StringUtils.isBlank(pay)) {//缓存信息为空重新缓存配置信息，从文件中读取
                List<Bank> bankList = paymentService.queryBankList(merchantPayment, channelNo, orderType);
                List<Bank> filterBankList = bankList.stream().filter(bank1 -> bank1.getBank().equals(payMethod)).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(filterBankList)) {
                    log.info("请求号:{},{}暂不支持此支付方式", MdcUtils.getRequestId(), payMethod);
                    throw new ServiceException("暂不支持此支付方式");
                }
                if (filterBankList.size() > 1) {
                    log.info("请求号:{},{}支付方式配置有误", MdcUtils.getRequestId(), payMethod);
                    throw new ServiceException("支付方式配置有误");
                }
                bank = filterBankList.get(0);
                apiRedisService.replaceData(paykey, JsonUtil.objectToJson(bank), handConfig.getPayCacheTime());//暂时存放一天
               /* String json = MerchantPaymentEnum.JN.equals(merchantPayment) ? handConfig.getPayJnConfigs() : handConfig.getPayConfigs();
                List<PaySetting> paySettingList = (List<PaySetting>) JsonUtil.jsonToList(json, new TypeToken<List<PaySetting>>() {
                }.getType());
                if (paySettingList != null) {
                    for (PaySetting paySetting : paySettingList) {
                        if (paySetting.getChannelNo().equals(channelNo)) {//匹配渠道
                            for (BankInfo bankInfo : paySetting.getBankInfo()) {
                                if (bankInfo.getOrderType().equals(orderType)) {//匹配类型
                                    for (Bank info : bankInfo.getBankList()) {
                                        String key = paySetting.getChannelNo() + "_" + bankInfo.getOrderType() + "_" + info.getBank();
                                        if (paykey.equals(dir + key)) {
                                            bank = info;
                                            apiRedisService.replaceData(dir + key, JsonUtil.objectToJson(info), handConfig.getPayCacheTime());//暂时存放一天
                                        }
                                    }
                                }
                            }
                        }
                    }
                }*/
            } else {
                bank = (Bank) JsonUtil.jsonToBean(pay, Bank.class);
            }
        } catch (Exception e) {
            logger.error("支付配置:{},加载异常:{}", paykey, e.getMessage());
            bank = null;
        }
        return bank;
    }

    //支付方式渲染
    @ApiOperation(value = "支付方式渲染", notes = "支付方式渲染")
    @RequestMapping(value = "/initPayMethod", method = RequestMethod.POST)
    //@InterfaceLog
    public BaseResp<List<BankDesc>> initPayMethod(@RequestBody WxPaymentReq req, HttpServletRequest request) {
        String ip = getClientIP(request);
        String headChannelCode = request.getHeader(HandlerConstants.TOKEN_CHANNELCODE);
        String versionCode = request.getHeader(HandlerConstants.CLIENT_VERSION_CODE);
        String clientVersion = request.getHeader(HandlerConstants.CLIENT_VERSION);
        ChannelCodeEnum channelCodeEnum = ChannelCodeEnum.checkEnum(headChannelCode);
        //修复channelCodeEnum为ALIPAY的情况
        if (("O".equals(req.getOrderType()) || "Q".equals(req.getOrderType())) && handConfig.getUnlimitedCard2Config().isPayConfigUseCache()) {
            BaseResp resp = new BaseResp();
            Map resultMap = new HashMap();
            List<BankDesc> showList = getPayBankByCache(headChannelCode);
            log.info(showList.hashCode() + "");
            if (CollectionUtils.isEmpty(showList)) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("不支持的渠道支付方式");
                return resp;
            }
            resultMap.put("show", showList);
            // 6.2.1版本之前不展示分期
            if (ChannelCodeEnum.MOBILE.getChannelCode().equals(headChannelCode) && VersionNoUtil.toMVerInt(versionCode) < 62100) {
                resultMap.put("show", showList.stream().filter(bankDesc -> !"ANTCREDITPAY".equals(bankDesc.getBank())).collect(Collectors.toList()));
            }
            resultMap.put("hide", new ArrayList<>());
            resp.setObjData(resultMap);
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
            return resp;
        }
        //新的业务结构
        BaseResp resp = initPayV2(req, channelCodeEnum, versionCode, clientVersion, ip, headChannelCode, request);
        // 短信支付临时调整 ********
        if ("Y".equals(req.getSmsOrder())) {
            Map<String, List<BankDesc>> map = (Map<String, List<BankDesc>>) resp.getObjData();
            List<BankDesc> bankDescList = Lists.newArrayList();
            map.values().forEach(value -> bankDescList.addAll(value));
            resp.setObjData(bankDescList);
        }
        return resp;
    }

    /**
     * 通过本地缓存获取支付配置
     * 仅在畅飞卡等大流量场景使用
     *
     * @param headChannelCode
     * @return
     */
    private List<BankDesc> getPayBankByCache(String headChannelCode) {
        return paySettingMap.get(headChannelCode);
    }

    @PostConstruct
    private void initPaymentCache() {
        // 支付缓存
        List<BankDesc> mobileBank = JsonUtil.fromJson(PAYMENT_CONFIG_MOBILE, new TypeToken<List<BankDesc>>() {
        }.getType());
        List<BankDesc> mwbBank = JsonUtil.fromJson(PAYMENT_CONFIG_MWEB, new TypeToken<List<BankDesc>>() {
        }.getType());
        List<BankDesc> weixinBank = JsonUtil.fromJson(PAYMENT_CONFIG_WEIXIN, new TypeToken<List<BankDesc>>() {
        }.getType());
        this.paySettingMap.put(ChannelCodeEnum.MOBILE.getChannelCode(), mobileBank);
        this.paySettingMap.put(ChannelCodeEnum.MWEB.getChannelCode(), mwbBank);
        this.paySettingMap.put(ChannelCodeEnum.WEIXIN.getChannelCode(), weixinBank);
    }

    /**
     * 新的支付方式返回
     *
     * @param req
     * @param channelCodeEnum 请求头渠道信息
     * @param versionCode
     * @param ip
     * @param request
     * @return
     */
    private BaseResp<List<BankDesc>> initPayV2(WxPaymentReq req, ChannelCodeEnum channelCodeEnum, String versionCode, String clientVersion, String ip, String headChannelCode, HttpServletRequest request) {
        BaseResp resp = new BaseResp();
        //检验渠道
        if (channelCodeEnum == null) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS_925.getResultCode());
            resp.setResultInfo(WSEnum.ERROR_REQUEST_PARAMS_925.getResultInfo());
            return resp;
        }
        //遇到OrderType为Q的统一转为O,Q的逐步废弃
        String orderType = changeOrderType(req.getPayType(), req.getOrderType());//新的业务结构
        OrderPayEnumType orderPayEnumType = OrderPayEnumType.checkEnum(orderType);
        //配置文件支持的支付配置
        List<BankDesc> bankDescList = queryPayMethodV2(channelCodeEnum.getChannelCode(), orderType, req.getMerchantPayment());
        // 6.2.1版本之前不展示分期
        if (ChannelCodeEnum.MOBILE.getChannelCode().equals(headChannelCode)) {
            if (VersionNoUtil.toVerInt(clientVersion) < 6020001) {
                bankDescList = bankDescList.stream().filter(bankDesc -> !"ANTCREDITPAY".equals(bankDesc.getBank())).collect(Collectors.toList());
            }
            if (VersionNoUtil.toVerInt(clientVersion) <= 6030000) {
                bankDescList = bankDescList.stream().filter(bankDesc -> !PayMethodEnum.HOWALLET.value.equals(bankDesc.getBank())).collect(Collectors.toList());
            }
            // 6.9.1版本之前不展示先飞后付
            if (VersionNoUtil.toVerInt(clientVersion) < 6090001) {
                bankDescList = bankDescList.stream().filter(bankDesc -> !PayMethodEnum.PAYAFTERFLY.value.equals(bankDesc.getBank())).collect(Collectors.toList());
            }
            // 7.6.3版本之前不展示建行支付
            if (VersionNoUtil.toVerInt(clientVersion) < 7060003) {
                bankDescList = bankDescList.stream().filter(bankDesc -> !PayMethodEnum.CCBPAY.value.equals(bankDesc.getBank())).collect(Collectors.toList());
            }
        }
        //按照后台结果重新排序，判断活动相关时间
        String key = null;
        if (StringUtils.isNotBlank(req.getMerchantPayment())) {
            key = RedisKeyConfig.ALL_PAY_METHOD + channelCodeEnum.getChannelCode() + ":" + req.getMerchantPayment() + ":" + orderPayEnumType.getPayType();
        } else {
            key = RedisKeyConfig.ALL_PAY_METHOD + channelCodeEnum.getChannelCode() + ":" + MerchantPaymentEnum.JX.name() + ":" + orderPayEnumType.getPayType();
        }
        BasicBaseReq<ParamPayMethodDTO> basicBaseReq = createParamPayMethodDTO(req.getChannelCode(), channelCodeEnum, orderPayEnumType.getPayType(), req.getMerchantPayment(), ip);
        List<PayMethodDTO> payMethodDTOList = basicService.queryPayMethods(basicBaseReq, key);
        if (CollectionUtils.isEmpty(payMethodDTOList)) {
            throw new ServiceException("暂无对应的配置，请先生效配置");
        }
        Map<String, List<BankDesc>> map = new HashMap<>();
        List<BankDesc> finalShowBankDescList = new ArrayList<>();
        List<BankDesc> finalNoShowBankDescList = new ArrayList<>();
        for (BankDesc bankDesc : bankDescList) {
            PayMethodDTO payMethodDTO = payMethodDTOList.stream().filter(dto -> bankDesc.getBank().equals(dto.getPayMethodCode())).findFirst().orElse(null);
            if (payMethodDTO != null) {
                bankDesc.setBankName(payMethodDTO.getPayMethodName());
                bankDesc.setBankLogo(payMethodDTO.getPayLogoStream());
                bankDesc.setSubBankLogo(payMethodDTO.getUnionpayLogoStream());
                bankDesc.setOrderNo(StringUtils.isBlank(payMethodDTO.getOrderNo()) ? 0 : Integer.parseInt(payMethodDTO.getOrderNo()));
                bankDesc.setIsDefault("Y".equals(payMethodDTO.getIsDefault()));
                bankDesc.setPosition(payMethodDTO.getPosition());
                //先飞后付备注信息
                if (PayMethodEnum.PAYAFTERFLY.value.equals(bankDesc.getBank())) {
                    bankDesc.setRemarks(handConfig.getPayAfterFlyRemarks());
                }
                //活动处理
                if (CollectionUtils.isNotEmpty(payMethodDTO.getActivityList())) {
                    long curTime = System.currentTimeMillis();
                    List<PayMethodActivityDTO> payMethodActivityDTOList = new ArrayList<>();
                    for (PayMethodActivityDTO payMethodActivityDTO : payMethodDTO.getActivityList()) {
                        //存在开始结束时间时，需要进行时间检验，否则不做相关相关控制
                        if ((!StringUtil.isNullOrEmpty(payMethodActivityDTO.getStartTime())) && (!StringUtil.isNullOrEmpty(payMethodActivityDTO.getEndTime()))) {
                            long start = Long.valueOf(payMethodActivityDTO.getStartTime());
                            long end = Long.valueOf(payMethodActivityDTO.getEndTime());
                            Boolean isShowPayMethodActivity = true;
                            //立减活动目前支持支付宝
                            if ("ALIPAY".equals(payMethodDTO.getPayMethodCode())) {
                                //   支付宝立减活动 是否区分航线  Y  N
                                Boolean isDistinguishRoute = payMethodActivityDTO.getDistinguishRoute() != null &&
                                        "Y".equals(payMethodActivityDTO.getDistinguishRoute()) ? true : false;

                                if (isDistinguishRoute) {
                                    isShowPayMethodActivity = false;
                                    String routeData = req.getDepAirport() + "-" + req.getArrAirport() + "-" + req.getInterFlag();

                                    if (StringUtils.isNotBlank(payMethodActivityDTO.getRouteData())) {
                                        isShowPayMethodActivity = payMethodActivityDTO.getRouteData().contains(routeData);
                                    }
                                    //往返航班取去程的的活动参数
                                    if ("RT".equals(req.getRouteType()) && !isShowPayMethodActivity && payMethodActivityDTOList.size() == 0) {
                                        String rtouteData = req.getArrAirport() + "-" + req.getDepAirport() + "-" + req.getInterFlag();
                                        isShowPayMethodActivity = payMethodActivityDTO.getRouteData().contains(rtouteData);
                                    }
                                }
                            }
                            if (curTime >= start && curTime <= end && isShowPayMethodActivity) {
                                payMethodActivityDTOList.add(payMethodActivityDTO);
                            }
                        }
                    }
                    if (CollectionUtils.isNotEmpty(payMethodActivityDTOList)) {
                        //活动时间排序 开始时间升序 结束时间升序
                        payMethodActivityDTOList.sort(Comparator.comparing(PayMethodActivityDTO::getStartTime).thenComparing(PayMethodActivityDTO::getEndTime));
                        bankDesc.setBankActivity(payMethodActivityDTOList.get(0).getPromotionDescription());
                        bankDesc.setBankActivityRemark(payMethodActivityDTOList.get(0).getActivityDescription());
                        bankDesc.setBankActivityUrl(payMethodActivityDTOList.get(0).getUrl());
                        bankDesc.setPromoParam(payMethodActivityDTOList.get(0).getPromoParam());

                    }
                }
                if ("Y".equals(payMethodDTO.getIsHide())) {
                    finalNoShowBankDescList.add(bankDesc);
                } else {
                    finalShowBankDescList.add(bankDesc);
                }
            }
        }
        //支付方式排序
        finalNoShowBankDescList.sort(Comparator.comparing(BankDesc::getOrderNo));
        finalShowBankDescList.sort(Comparator.comparing(BankDesc::getOrderNo));
        //默认状态调整，只保持一个有效的默认状态
        boolean existDefault = false;
        for (BankDesc bankDesc : finalShowBankDescList) {
            if (existDefault) {
                bankDesc.setIsDefault(false);
            } else {
                if (bankDesc.getIsDefault() != null && bankDesc.getIsDefault()) {
                    existDefault = true;
                }
            }
        }
        for (BankDesc bankDesc : finalNoShowBankDescList) {
            if (existDefault) {
                bankDesc.setIsDefault(false);
            } else {
                if (bankDesc.getIsDefault() != null && bankDesc.getIsDefault()) {
                    existDefault = true;
                }
            }
        }
        //无默认的时候默认列表第一个选中
        if (!existDefault) {
            if (CollectionUtils.isNotEmpty(finalShowBankDescList)) {
                finalShowBankDescList.get(0).setIsDefault(true);
            } else if (CollectionUtils.isNotEmpty(finalNoShowBankDescList)) {
                finalShowBankDescList.get(0).setIsDefault(true);
            }
        }
        String platforminfo = request.getHeader(HandlerConstants.CLIENT_PLATFORM);
        // 手机渠道 且 鸿蒙 只保留支付宝和云闪付
        if (ChannelCodeEnum.MOBILE.getChannelCode().equals(headChannelCode) && "harmony".equalsIgnoreCase(platforminfo)) {
            finalShowBankDescList = finalShowBankDescList.stream().filter(bankDesc -> PayMethodEnum.ALIPAY.value.equals(bankDesc.getBank()) || PayMethodEnum.CHINAPAY.value.equals(bankDesc.getBank())).collect(Collectors.toList());
            finalNoShowBankDescList = finalNoShowBankDescList.stream().filter(bankDesc -> PayMethodEnum.ALIPAY.value.equals(bankDesc.getBank()) || PayMethodEnum.CHINAPAY.value.equals(bankDesc.getBank())).collect(Collectors.toList());
        }
        map.put("show", finalShowBankDescList);
        map.put("hide", finalNoShowBankDescList);
        resp.setObjData(map);
        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
        resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
        return resp;
    }

    /**
     * 支付类型 D:国内  I:国际  Q:其他(废弃)  O:其他
     *
     * @param payType   支付类型 逐步使用此字段判断支付方式
     * @param orderType 订单类型
     * @return
     */
    private String changeOrderType(String payType, String orderType) {
        String newOrderType = payType;
        if (StringUtils.isBlank(payType)) {
            newOrderType = orderType;
        }
        //Q类型逐步放弃，变更为O
        if (OrderPayEnumType.Q.getPayType().equals(newOrderType)) {
            newOrderType = OrderPayEnumType.O.getPayType();
        }
        return newOrderType;
    }

    private BasicBaseReq createParamPayMethodDTO(String channelCode, ChannelCodeEnum channelCodeEnum, String productType, String merchantPayment, String ip) {
        BasicBaseReq basicBaseReq = new BasicBaseReq(HandlerConstants.BASIC_INFO_VERSION, channelCode, ip);
        ParamPayMethodDTO paramPayMethodDTO = new ParamPayMethodDTO();
        paramPayMethodDTO.setChannel(channelCodeEnum.getChannelCode());
        paramPayMethodDTO.setPayProductType(productType);
        paramPayMethodDTO.setMerchantPayment(merchantPayment);
        basicBaseReq.setRequest(paramPayMethodDTO);
        return basicBaseReq;
    }

    //获取所有支持的支付方式
    /*private List<BankDesc> queryPayMethod(String headChannelCode, String orderType, String merchantPayment) {
        List<BankDesc> bankDescList = new ArrayList<>();
        String key = null;
        orderType = changeOrderType("", orderType);
        //为了区分测试环境和预发布环境的缓存信息
        //默认
        if (StringUtils.isBlank(merchantPayment)) {
            key = RedisKeyConfig.PAY_METHOD_DIR + "JX:" + headChannelCode + ":" + orderType;
        } else {
            key = RedisKeyConfig.PAY_METHOD_DIR + merchantPayment + ":" + headChannelCode + ":" + orderType;
        }
        String pay = apiRedisService.getData(key);
        if (StringUtil.isNullOrEmpty(pay)) {
            //根据商户；类型获取不同的支付网关配置
            String json = "JN".equals(merchantPayment) ? handConfig.getPayJnConfigs() : handConfig.getPayConfigs();
            List<PaySetting> paySettingList = (List<PaySetting>) JsonUtil.jsonToList(json, new TypeToken<List<PaySetting>>() {
            }.getType());
            if (paySettingList != null) {
                for (PaySetting paySetting : paySettingList) {
                    if (paySetting.getChannelNo().equals(headChannelCode)) {//判断支付渠道
                        for (BankInfo orderInfo : paySetting.getBankInfo()) {
                            if (orderInfo.getOrderType().equals(orderType)) {//判读订单支付类型  D:国内  I:国际  O:其他(众筹) Q:其他
                                //获取存在的支付配置
                                for (Bank bank : orderInfo.getBankList()) {
                                    BankDesc bankDesc = new BankDesc();
                                    bankDesc.setBank(bank.getBank());
                                    bankDesc.setBankName(bank.getBankName());
                                    bankDesc.setBankActivity(bank.getBankActivity());
                                    bankDesc.setBankActivityUrl(bank.getBankActivityUrl());
                                    bankDesc.setBankActivityDateStart(bank.getBankActivityDateStart());
                                    bankDesc.setBankActivityDateEnd(bank.getBankActivityDateEnd());
                                    if (CollectionUtils.isNotEmpty(bank.getSubBankSettings())) {
                                        List<SubBankDesc> subBankDescs = Lists.newArrayList();
                                        bank.getSubBankSettings().forEach(subBankSetting -> {
                                            SubBankDesc subBankDesc = new SubBankDesc();
                                            BeanUtils.copyNotNullProperties(subBankSetting, subBankDesc);
                                            subBankDescs.add(subBankDesc);
                                        });
                                        bankDesc.setSubBankDescList(subBankDescs);
                                    }
                                    bankDescList.add(bankDesc);
                                }
                            }
                        }
                        apiRedisService.replaceData(key, JsonUtil.objectToJson(bankDescList), handConfig.getPayCacheTime());
                    }
                }
            }
        } else {
            bankDescList = (List<BankDesc>) JsonUtil.jsonToList(pay, new TypeToken<List<BankDesc>>() {
            }.getType());
        }
        return bankDescList;
    }*/

    /**
     * 获取所有支持的支付方式V2
     *
     * @param headChannelCode
     * @param orderType
     * @param merchantPayment
     * @return
     */
    private List<BankDesc> queryPayMethodV2(String headChannelCode, String orderType, String merchantPayment) {
        List<BankDesc> bankDescList = new ArrayList<>();
        orderType = changeOrderType("", orderType);
        List<Bank> bankList = paymentService.queryBankList(merchantPayment, headChannelCode, orderType);
        //获取存在的支付配置
        for (Bank bank : bankList) {
            BankDesc bankDesc = new BankDesc();
            bankDesc.setBank(bank.getBank());
            bankDesc.setBankName(bank.getBankName());
            bankDesc.setBankActivity(bank.getBankActivity());
            bankDesc.setBankActivityUrl(bank.getBankActivityUrl());
            bankDesc.setBankActivityDateStart(bank.getBankActivityDateStart());
            bankDesc.setBankActivityDateEnd(bank.getBankActivityDateEnd());
            if (CollectionUtils.isNotEmpty(bank.getSubBankSettings())) {
                List<SubBankDesc> subBankDescs = Lists.newArrayList();
                bank.getSubBankSettings().forEach(subBankSetting -> {
                    SubBankDesc subBankDesc = new SubBankDesc();
                    BeanUtils.copyNotNullProperties(subBankSetting, subBankDesc);
                    subBankDescs.add(subBankDesc);
                });
                bankDesc.setSubBankDescList(subBankDescs);
            }
            bankDescList.add(bankDesc);
        }
        return bankDescList;
    }

    //支付宝支付
    @ApiOperation(value = "支付宝支付", notes = "支付宝支付")
    @RequestMapping(value = "/orderPayByAlipay", method = RequestMethod.POST)
    @InterfaceLog
    public PaymentAlipayResp orderPayByAlipay(@RequestBody @Validated WxPaymentReq req, BindingResult bindingResult, HttpServletRequest request) {
        PaymentAlipayResp resp = new PaymentAlipayResp();
        if (bindingResult.hasErrors()) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS_925_4.getResultCode());
            resp.setErrorInfo(WSEnum.ERROR_REQUEST_PARAMS_925_4.getResultInfo());
            return resp;
        }
        //支付宝小程序的渠道号转换为MOBILE进行处理
        if (ChannelCodeEnum.MP_ALIPAY.getChannelCode().equals(req.getChannelCode())) {
            req.setChannelCode(ChannelCodeEnum.WEIXIN.getChannelCode());
        }
        //<editor-fold desc="验证">
        boolean flag = this.checkKeyInfo(req.getFfpId(), req.getLoginKeyInfo(), req.getChannelCode());
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR_925.getResultCode());
            resp.setErrorInfo(WSEnum.ERROR_CHK_ERROR_925.getResultInfo());
            return resp;
        }
        String payChannelCode = req.getChannelCode();//默认的支付渠道即为访问渠道
        String returnUrl = HandlerConstants.MWEB_PAY_RETURN_URL;
        if (!StringUtil.isNullOrEmpty(req.getPayChannel())) {//主要处理M网站的支付问题
            payChannelCode = req.getPayChannel();
            if ("MWEB".equals(req.getPayChannel())) {//MWEB的回调
                returnUrl = HandlerConstants.MWEB_SHOUFUYOU_RETURN_URL_PAY;
            }
            if ("MWEB_ALIPAY".equals(req.getPayChannel())) {
                returnUrl = HandlerConstants.MWEB_PAY_RETURN_URL;
                payChannelCode = ChannelCodeEnum.MWEB.getChannelCode();
            }
        }
        String realChannelCode = request.getHeader(HandlerConstants.TOKEN_CHANNELCODE);
        if (ChannelCodeEnum.MWEB.getChannelCode().equalsIgnoreCase(realChannelCode)) {
            returnUrl = HandlerConstants.MWEB_PAY_RETURN_URL;
            payChannelCode = ChannelCodeEnum.MWEB.getChannelCode();
        }
        //加载支付配置
        String payType = req.getPayType();
        if (StringUtil.isNullOrEmpty(payType)) {
            payType = req.getOrderType();
        }
        Bank bank;
        String payMethod = req.getPayMethod();
        // 花呗分期
        if (StringUtils.isNotBlank(payMethod) && payMethod.startsWith("ANTCREDITPAY")) {
            bank = initPay(payChannelCode, payType, "ANTCREDITPAY", req.getMerchantPayment());
        } else if (ChannelCodeEnum.MP_ALIPAY.getChannelCode().equals(realChannelCode)) {
            bank = initPay(realChannelCode, payType, "ALIPAY", req.getMerchantPayment());
        } else {
            bank = initPay(payChannelCode, payType, "ALIPAY", req.getMerchantPayment());
        }
        if (bank == null) {
            logger.info(LOG_RESP_SIX, payChannelCode + "_" + payType + "_" + payMethod);
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo(LOG_RESP_FOUR);
            return resp;
        }
        //</editor-fold>
        //<editor-fold desc="加密,生成加密字符串">
        OrderPayEnumType orderPayEnumType = OrderPayEnumType.checkEnum(payType);
        //品牌餐食重新设置订单描述信息
        if (StringUtils.isNotBlank(req.getChannelPriInfo()) && req.getChannelPriInfo().indexOf("BrandMeals") > -1) {
            orderPayEnumType = OrderPayEnumType.checkEnum("BrandMeals");
        }
        String subject = orderPayEnumType.getSubject();
        String body = orderPayEnumType.getBody();
        if (StringUtils.isNotBlank(req.getProductDesc())) {
            subject = req.getProductDesc();
            body = req.getProductDesc();
        }
        PtPaymentWXReq paymentReq = new PtPaymentWXReq(HandlerConstants.VERSION, HandlerConstants.CURRENCY_CODE, returnUrl,
                HandlerConstants.BACK_RETURN_URL_PAY, "", subject, body, "", "Web");
        String postUrl = HandlerConstants.URL_PAY;
        BeanUtils.copyProperties(req, paymentReq);
        paymentReq.setChannelNo(req.getChannelCode());
        if (ChannelCodeEnum.MP_ALIPAY.getChannelCode().equals(realChannelCode)) {
            paymentReq.setChannelNo(realChannelCode);
        }
        paymentReq.setOrderType(orderPayEnumType.getOrderType());
        String billCreateIP = "";
        if (StringUtil.isNullOrEmpty(req.getClientIp())) {
            billCreateIP = this.getClientIP(request);
        } else {
            billCreateIP = req.getClientIp();
            if (!this.checkToken(req.getToken(), billCreateIP, HandlerConstants.ACCESSSECRET)) {
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setErrorInfo(LOG_RESP_ONE_925);
                return resp;
            }
        }
        StringBuilder gatewayNo = new StringBuilder();
        StringBuilder gatewayType = new StringBuilder();
        StringBuilder key = new StringBuilder();
        // 花呗支付处理
        if (payMethod.startsWith("ANTCREDITPAY") && CollectionUtils.isNotEmpty(bank.getSubBankSettings())) {
            bank.getSubBankSettings().stream().filter(subBankSetting -> payMethod.equals(subBankSetting.getSubBank())).findFirst().ifPresent(subBankSetting -> {
                gatewayNo.append(subBankSetting.getGateway());
                gatewayType.append(subBankSetting.getGatewayType());
                key.append(subBankSetting.getKey());
            });
        } else {
            gatewayNo.append(bank.getGateway());//网关号
            gatewayType.append(bank.getGatewayType());
            key.append(bank.getKey());
        }
        paymentReq.setGatewayNo(gatewayNo.toString());
        paymentReq.setGatewayType(gatewayType.toString());
        //动态参数设置
        Map<String, String> dynamicParameterMap = new HashMap<>();
        dynamicParameterMap.put(VALUE_SIX, billCreateIP);
        //支付宝小程序需要传入userId
        if (ChannelCodeEnum.MP_ALIPAY.getChannelCode().equals(realChannelCode)) {
            String userId = req.getUserId();
            if (StringUtil.isNullOrEmpty(userId)) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS_925_3.getResultCode());
                resp.setErrorInfo(WSEnum.ERROR_REQUEST_PARAMS_925_3.getResultInfo());
                return resp;
            }
            dynamicParameterMap.put(VALUE_SEVEN, req.getUserId());
        }
        //支付宝立减活动参数
        if (StringUtils.isNotBlank(req.getPromoParam())) {
            dynamicParameterMap.put(VALUE_PROMO_PARAMS, req.getPromoParam());
        }

        String dynamicParameters = JsonUtil.objectToJson(dynamicParameterMap);
        //net 需要的格式  {'BillCreateIP':'*************','buyer_id':'2088802389899020','promo_params_str':'{\"consumption_voucher\":\"shanghai_sanfang\",\"uscc\":\"913100007867226104\"}'}
        dynamicParameters = dynamicParameters.replaceAll("\"", "'").replaceAll("\\\\'", "\\\\\"");
        log.info("dynamicParameters 替换过后的格式 {}", dynamicParameters);
        paymentReq.setDynamicParameters(dynamicParameters);
        Map<String, String> parametersMap = paymentReq.getPayPara();
        String sha1Content = paymentReq.getPayParaSHA1Str();
        //19支付平台渠道用户号
        String payChannel = bank.getPayChannel();
        sha1Content = sha1Content + payChannel;
        parametersMap.put(VAULE_ONE, payChannel);
        //密钥
        String chkValue = sha1Encode(sha1Content + key.toString());
        parametersMap.put(VALUE_TWO, chkValue);
        paymentReq.setChkValue(chkValue);
        HttpResult result = doPayPost(postUrl, parametersMap);
        //2021-02-07 金卡及以上会员不调用同盾风控
        PtCRMResponse<PtMemberDetail> ptCRMResponse = memberCacheService.queryMemberDetailInfo(request, req.getFfpId(), req.getFfpCardNo(), req.getChannelCode());
        int levelCode = 0;
        if (ptCRMResponse != null && ptCRMResponse.getCode() == 0) {
            levelCode = NumberUtils.toInt(ptCRMResponse.getData().getStateInfo().getMemberLevelCode());
        }
        if (result.isResult()) {
            logger.info(LOG_RESP_SEVEN, result.getResponse());
            String paymentInfo = result.getResponse().trim();
            if ((!StringUtil.isNullOrEmpty(paymentInfo)) && paymentInfo.indexOf(VALUE_FOUR) > -1) {
                resp = (PaymentAlipayResp) JsonUtil.jsonToBean(paymentInfo, PaymentAlipayResp.class);
            }
            if (StringUtil.isNullOrEmpty(resp.getRespCode())) {
                resp.setPaymentInfo(paymentInfo);
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
                this.putSendCouponRedisIfNecessary(orderPayEnumType.getOrderType(), req.getOrderNo(), req.getFfpCardNo());
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo(LOG_RESP_FIVE + resp.getErrorMsg());
                if (UnifiedOrderResultEnum.ERROR_PNR.getResultCode().equals(resp.getRespCode())) {
                    resp.setResultCode(WSEnum.TOAST_ERROR.getResultCode());
                    resp.setErrorInfo(LOG_RESP_PNR_ERROR);
                }
            }
            return resp;
        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo(LOG_RESP_TWO_925);
            return resp;
        }
    }

    @InterfaceLog
    @ApiOperation(value = "先飞后付", notes = "先飞后付（华瑞）")
    @RequestMapping(value = "/payAfterFly ", method = RequestMethod.POST)
    @SuppressWarnings("rawtypes")
    public BaseResp payAfterFly(@RequestBody @Validated PayAfterFlyRequest req, BindingResult bindingResult, HttpServletRequest request) {
        BaseResp resp = new BaseResp<>();
        String billCreateIP = this.getClientIP(request);
        //参数检验
        if (bindingResult.hasErrors()) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
            return resp;
        }
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(req.getFfpId(), req.getLoginKeyInfo(), req.getChannelCode());
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        //重新设置订单乘机人信息
        String cacheData = apiRedisService.getData(RedisKeyConfig.createOrderPass(req.getChannelOrderNo()));
        if(StringUtils.isNotBlank(cacheData)){
            List<OrderPassengerInfo> orderPassengerInfoList = JsonUtil.fromJson(cacheData,new TypeToken<List<OrderPassengerInfo>>() {});
            if(CollectionUtils.isNotEmpty(orderPassengerInfoList)){
                List<PassengerInfo> passengerInfoList = new ArrayList<>();
                for(OrderPassengerInfo passengerInfo:orderPassengerInfoList){
                    PassengerInfo pass = new PassengerInfo();
                    pass.setPassengerCertno(passengerInfo.getCertNo());
                    pass.setPassengerName(passengerInfo.getPassengerName());
                    pass.setPassengerPhone(passengerInfo.getHandphoneNo());
                    passengerInfoList.add(pass);
                }
                req.setPassengerInfo(passengerInfoList);
            }
        }else{
            throw new CommonException(WSEnum.OPERATION_TIMEOUT.getResultCode(), "操作超时，请重新查询后操作");
        }
        //加载支付配置
        String payChannelCode = req.getChannelCode();//默认的支付渠道即为访问渠道
        //头部信息为真实的访问渠道
        String realChannelCode = request.getHeader(HandlerConstants.TOKEN_CHANNELCODE);
        PayMethodEnum payMethodEnum = PayMethodEnum.checkType(req.getPayMethod());
        if (payMethodEnum == null) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setResultInfo("请确认支付方式参数");
            return resp;
        }
        //加载支付配置
        String payType = req.getPayType();
        OrderPayEnumType orderPayEnumType = OrderPayEnumType.checkEnum(payType);
        //品牌餐食重新设置订单描述信息
        if (StringUtils.isNotBlank(req.getChannelPriInfo()) && req.getChannelPriInfo().contains("BrandMeals")) {
            orderPayEnumType = OrderPayEnumType.checkEnum("BrandMeals");
        }

        String subject = orderPayEnumType.getSubject();
        String body = orderPayEnumType.getBody();
        if (StringUtils.isNotBlank(req.getOrderDescribe())) {
            subject = req.getOrderDescribe();
            body = req.getOrderDescribe();
            if (req.getOrderDescribe().length() > 100) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo("商品描述限100个字符");
                return resp;
            }
        }

        String returnUrl = HandlerConstants.MWEB_PAY_RETURN_URL;
        PtPaymentWXReq paymentReq = new PtPaymentWXReq(HandlerConstants.VERSION, HandlerConstants.CURRENCY_CODE, returnUrl,
                HandlerConstants.BACK_RETURN_URL_PAY, "", subject, body, "", "Web");
        BeanUtils.copyProperties(req, paymentReq);
        paymentReq.setAmount(req.getOrderPaidaMount());
        paymentReq.setChannelNo(req.getChannelCode());
        paymentReq.setOrderType(orderPayEnumType.getOrderType());
        paymentReq.setChannelPriInfo(req.getChannelPriInfo() == null ? "" : req.getChannelPriInfo());
        if (ChannelCodeEnum.MWEB.getChannelCode().equals(req.getChannelCode())) {
            paymentReq.setChannelNo(ChannelCodeEnum.MOBILE.getChannelCode());
        }
        if (StringUtils.isNotBlank(realChannelCode)) {
            payChannelCode = realChannelCode;
        }
        //加载支付配置
        Bank bank = initPay(payChannelCode, payType, payMethodEnum.value, null);
        if (bank == null) {
            logger.info(LOG_RESP_SIX, payChannelCode + "_" + payType + "_" + payMethodEnum.value);
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo(LOG_RESP_FOUR);
            return resp;
        }
        paymentReq.setGatewayNo(bank.getGateway());
        paymentReq.setGatewayType(bank.getGatewayType());
        Map<String, String> dynamicParameterMap = new HashMap<>();
        //动态参数设置
        dynamicParameterMap.put(VALUE_SIX, billCreateIP);
        dynamicParameterMap.put("FfpCardNo", req.getFfpCardNo());
        Extension extension = new Extension();
        /*extension.setOrderInfo(req.getOrderInfo());*/
        extension.setOrderInfo("订单信息");
        dynamicParameterMap.put("Extension", JSON.toJSONString(extension));
        ChannelRiskLoanInfo channelRiskLoanInfo = toGenerateHrParams(req, orderPayEnumType, request, billCreateIP);
        dynamicParameterMap.put("ChannelRiskLoanInfo", JSON.toJSONString(channelRiskLoanInfo));
        String dynamicParameters = JsonUtil.objectToJson(dynamicParameterMap);
        paymentReq.setDynamicParameters(dynamicParameters);
        Map<String, String> parametersMap = paymentReq.getPayPara();
        String sha1Content = paymentReq.getPayParaSHA1Str();
        //19支付平台渠道用户号
        String payChannel = bank.getPayChannel();
        sha1Content = sha1Content + payChannel;
        parametersMap.put(VAULE_ONE, payChannel);
        //密钥
        String key = bank.getKey();
        String chkValue = sha1Encode(sha1Content + key);
        parametersMap.put(VALUE_TWO, chkValue);
        paymentReq.setChkValue(chkValue);
        resp = paymentService.doPay(parametersMap);
        if (!WSEnum.SUCCESS.getResultCode().equals(resp.getResultCode()) || null == resp.getObjData()) {
            return resp;
        }

        Map<String, String> payMap = (Map<String, String>) resp.getObjData();
        if ((payMap.containsKey(VALUE_FOUR) && !UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(payMap.get(VALUE_FOUR)))
                || (payMap.containsKey("Status") && !UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(payMap.get("Status")))) {
            resp.setObjData(null);
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(payMap.get("ErrorMsg") == null ? "支付异常！" : payMap.get("ErrorMsg"));
        }

        PayAfterFlyResponse payAfterFlyResponse = new PayAfterFlyResponse();
        payAfterFlyResponse.setDataType("flyFirst");
        DataEntity dataEntity = new DataEntity();
        dataEntity.setIdentity(payMap.get("IdNo"));
        dataEntity.setH5Token(payMap.get("H5Token"));
        dataEntity.setAppID(payMap.get("AppID"));
        dataEntity.setSign(payMap.get("Sign"));
        payAfterFlyResponse.setData(dataEntity);

       /* Set<Map.Entry<String, String>> entries = payMap.entrySet();
        for (Map.Entry<String,String> entry:entries
             ) {
            String entryKey = entry.getKey();
            String entryValue = entry.getValue();
            char[] chars = entryKey.toCharArray();
            chars[0] += 32;
            payMap.put(String.valueOf(chars),entryValue);
        }*/

        resp.setObjData(payAfterFlyResponse);
        return resp;
    }

    private ChannelRiskLoanInfo toGenerateHrParams(PayAfterFlyRequest req, OrderPayEnumType orderPayEnumType, HttpServletRequest request, String billCreateIP) {
        ChannelRiskLoanInfo ptPAFRequest = new ChannelRiskLoanInfo();
        BeanUtils.copyNotNullProperties(req, ptPAFRequest);
        if (StringUtils.isNotEmpty(req.getInternational_or_domestic()) && "D".equals(req.getInternational_or_domestic())) {
            ptPAFRequest.setInternational_or_domestic("国内");
        } else if (StringUtils.isNotEmpty(req.getInternational_or_domestic()) && "I".equals(req.getInternational_or_domestic())) {
            ptPAFRequest.setInternational_or_domestic("国际");
        }
        if (StringUtils.isNotEmpty(req.getOneway_or_round()) && "RT".equals(req.getOneway_or_round())) {
            ptPAFRequest.setOneway_or_round("往返");
        } else if (StringUtils.isNotEmpty(req.getOneway_or_round()) && ("OW".equals(req.getOneway_or_round()))) {
            ptPAFRequest.setOneway_or_round("单程");
        }
        ChannelRiskLoanInfo channelRiskLoanInfo = toGenChannelRiskLoanParams(req.getFfpCardNo(), request, "MOBILE", billCreateIP);
        ptPAFRequest.setWhiteFlag(channelRiskLoanInfo.getWhiteFlag());
        ptPAFRequest.setOrderDescribe(StringUtils.isNotEmpty(req.getOrderDescribe()) ? req.getOrderDescribe() : orderPayEnumType.getBody());
        ptPAFRequest.setMemberid(req.getFfpCardNo());
        ptPAFRequest.setContactName(req.getUserName());
        ptPAFRequest.setContactPhone(req.getUserMobile());
        PtCRMResponse<PtMemberDetail> ptCRMResponse = null;
        String idNo = ptPAFRequest.getIdNo();
        try {
            CityInfoDto arrCityInfo = localCacheService.getLocalCity(req.getArrCode());
            if (null != arrCityInfo) {
                ptPAFRequest.setIs_destination_domestic(HandlerConstants.TRIP_TYPE_D.equals(arrCityInfo.getIsInternational()) ? "Y" : "N");
            }

            if (StringUtils.isEmpty(idNo)) {
                //从CRM获取身份证号
                //查询用户保存的证件信息
                String[] items = {MemberDetailRequestItemsEnum.CERTIFICATEINFO.eName};
                PtApiCRMRequest<PtMemberDetailRequest> ptApiRequest = CRMReqUtil.buildMemberDetailReq(req.getFfpCardNo(), req.getFfpId(), request, req.getChannelCode(), items);
                ptCRMResponse = memberService.memberDetail(ptApiRequest);
                if (ptCRMResponse.getCode() == 0) {
                    MemberCertificateSoaModelV2 certificateInfo = CRMReqUtil.getCertificateInfo(ptCRMResponse.getData().getCertificateInfo(), CertificateTypeEnum.ID_CARD.getCode());
                    if (certificateInfo != null) {
                        idNo = certificateInfo.getCertificateNumber();
                    }
                }
            }
            if (StringUtils.isEmpty(idNo)) {
                throw new CommonException(WSEnum.NETWORK_ERROR.getResultCode(), "未查询到身份证号");
            }
        } catch (CommonException ce) {
            log.error("先飞后付获取用户身份证号结果为空,查询方法：memberDetail(),查询结果:{}", JSON.toJSONString(ptCRMResponse));
            throw ce;
        } catch (Exception e) {
            log.error("先飞后付获取用户身份证号网络出错,查询方法：memberDetail(),查询结果:{}", JSON.toJSONString(ptCRMResponse));
            throw e;
        }

        ptPAFRequest.setIdNo(idNo);
        return ptPAFRequest;
    }

    @InterfaceLog
    @ApiOperation(value = "微信支付", notes = "微信支付")
    @RequestMapping(value = "/orderPayByWX", method = RequestMethod.POST)
    public PaymentWXResp orderPayByWX(@RequestBody @Validated WxPaymentReq req, BindingResult bindingResult, HttpServletRequest request) {
        PaymentWXResp resp = new PaymentWXResp();
        if (bindingResult.hasErrors()) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
            return resp;
        }
        //<editor-fold desc="验证">
        boolean flag = this.checkKeyInfo(req.getFfpId(), req.getLoginKeyInfo(), req.getChannelCode());
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR_925.getResultCode());
            resp.setErrorInfo(WSEnum.ERROR_CHK_ERROR_925.getResultInfo());
            return resp;
        }
        String billCreateIP = this.getClientIP(request);
        //根据渠道判断
        //加载支付配置
        String payChannelCode = req.getChannelCode();//默认的支付渠道即为访问渠道
        if (!StringUtil.isNullOrEmpty(req.getPayChannel())) {//主要处理M网站的支付问题
            payChannelCode = req.getPayChannel();
            if ("MWEB_WEIXIN".equals(req.getPayChannel())) {
                payChannelCode = ChannelCodeEnum.MWEB.getChannelCode();
            }
        }
        String realChannelCode = request.getHeader(HandlerConstants.TOKEN_CHANNELCODE);
        if (ChannelCodeEnum.MWEB.getChannelCode().equalsIgnoreCase(realChannelCode)) {
            payChannelCode = ChannelCodeEnum.MWEB.getChannelCode();
        }
        //加载支付配置
        String payType = req.getPayType();
        if (StringUtil.isNullOrEmpty(payType)) {
            payType = req.getOrderType();
        }
        Bank bank = initPay(payChannelCode, payType, "WEIXINPAY", req.getMerchantPayment());
        if (bank == null) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo(LOG_RESP_FOUR);
            return resp;
        }
        //</editor-fold>
        //<editor-fold desc="加密,生成加密字符串">
        OrderPayEnumType orderPayEnumType = OrderPayEnumType.checkEnum(payType);
        //品牌餐食重新设置订单描述信息
        if (StringUtils.isNotBlank(req.getChannelPriInfo()) && req.getChannelPriInfo().indexOf("BrandMeals") > -1) {
            orderPayEnumType = OrderPayEnumType.checkEnum("BrandMeals");
        }
        String subject = orderPayEnumType.getSubject();
        String body = orderPayEnumType.getBody();
        if (StringUtils.isNotBlank(req.getProductDesc())) {
            subject = req.getProductDesc();
            body = req.getProductDesc();
        }
        PtPaymentWXReq paymentReq = new PtPaymentWXReq(HandlerConstants.VERSION, HandlerConstants.CURRENCY_CODE, "",
                HandlerConstants.BACK_RETURN_URL_PAY, "", subject, body, "", "Web");
        String postUrl = HandlerConstants.URL_PAY;
        BeanUtils.copyProperties(req, paymentReq);
        paymentReq.setChannelNo(req.getChannelCode());
        paymentReq.setOrderType(orderPayEnumType.getOrderType());
        if (ChannelCodeEnum.MWEB.getChannelCode().equals(req.getChannelCode())) {
            paymentReq.setChannelNo(ChannelCodeEnum.MOBILE.getChannelCode());
        }
        //微信支付网关暂时就一个，固定
        String gatewayNo = bank.getGateway();
        paymentReq.setGatewayNo(gatewayNo);
        paymentReq.setGatewayType(bank.getGatewayType());
        //动态参数设置
        Map<String, String> dynamicParameterMap = new HashMap<>();
        dynamicParameterMap.put(VALUE_SIX, billCreateIP);
        dynamicParameterMap.put("openid", req.getOpenid());
        String dynamicParameters = JsonUtil.objectToJson(dynamicParameterMap);
        paymentReq.setDynamicParameters(dynamicParameters);

        Map<String, String> parametersMap = paymentReq.getPayPara();
        String sha1Content = paymentReq.getPayParaSHA1Str();

        //19支付平台渠道用户号
        String payChannel = bank.getPayChannel();
        sha1Content = sha1Content + payChannel;
        parametersMap.put(VAULE_ONE, payChannel);
        //密钥
        String key = bank.getKey();
        String chkValue = sha1Encode(sha1Content + key);
        parametersMap.put(VALUE_TWO, chkValue);
        paymentReq.setChkValue(chkValue);
        HttpResult result = doPayPost(postUrl, parametersMap);
        //2021-02-07 金卡及以上会员不调用同盾风控
        PtCRMResponse<PtMemberDetail> ptCRMResponse = memberCacheService.queryMemberDetailInfo(request, req.getFfpId(), req.getFfpCardNo(), req.getChannelCode());
        int levelCode = 0;
        if (ptCRMResponse != null && ptCRMResponse.getCode() == 0) {
            levelCode = NumberUtils.toInt(ptCRMResponse.getData().getStateInfo().getMemberLevelCode());
        }
        if (result.isResult()) {
            resp = (PaymentWXResp) JsonUtil.jsonToBean(result.getResponse(), PaymentWXResp.class);
            String packageU = (String) JsonUtil.getJsonValue(result.getResponse(), "package");
            resp.setPackageU(packageU);
            if (StringUtil.isNullOrEmpty(resp.getAppId()) || StringUtil.isNullOrEmpty(resp.getNonceStr())) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo("支付异常:" + resp.getErrorMsg());
                if (UnifiedOrderResultEnum.ERROR_PNR.getResultCode().equals(resp.getRespCode())) {
                    resp.setResultCode(WSEnum.TOAST_ERROR.getResultCode());
                    resp.setErrorInfo(LOG_RESP_PNR_ERROR);
                }
            } else {
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
                this.putSendCouponRedisIfNecessary(orderPayEnumType.getOrderType(), req.getOrderNo(), req.getFfpCardNo());
            }
            //同盾校验
            //5.3版本使用同盾
            //支付目前只调用同盾记录，不做判断
            if ("Y".equals(handConfig.getUseTongDun()) && levelCode < NumberUtils.toInt(MemberLevelEnum.Golden.getLevelCode())) {
                transferTD(req, request, resp.getResultCode(), PayMethodTypeEnum.WEIXIN.getName());
            } else {
                log.debug("请求未进行同盾验证，渠道订单号：{}，会员号：{}", req.getChannelOrderNo(), req.getFfpCardNo());
            }
            return resp;
        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo(LOG_RESP_PNR_ERROR);
            //同盾校验
            //5.3版本使用同盾
            //支付目前只调用同盾记录，不做判断
            if ("Y".equals(handConfig.getUseTongDun()) && levelCode < NumberUtils.toInt(MemberLevelEnum.Golden.getLevelCode())) {
                transferTD(req, request, resp.getResultCode(), PayMethodTypeEnum.WEIXIN.getName());
            } else {
                log.debug("请求未进行同盾验证，渠道订单号：{}，会员号：{}", req.getChannelOrderNo(), req.getFfpCardNo());
            }
            return resp;
        }
    }

    @InterfaceLog
    @ApiOperation(value = "统一支付接口", notes = "统一支付（后期支付尽量一个接口，不再重复封装）")
    @RequestMapping(value = "/uniformPay ", method = RequestMethod.POST)
    public BaseResp uniformPay(@RequestBody @Validated WxPaymentReq req, BindingResult bindingResult, HttpServletRequest request) {
        BaseResp resp = new BaseResp();
        String billCreateIP = this.getClientIP(request);
        //参数检验
        if (bindingResult.hasErrors()) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
            return resp;
        }
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(req.getFfpId(), req.getLoginKeyInfo(), req.getChannelCode());
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        //加载支付配置
        String payChannelCode = req.getChannelCode();//默认的支付渠道即为访问渠道
        //头部信息为真实的访问渠道
        String realChannelCode = request.getHeader(HandlerConstants.TOKEN_CHANNELCODE);
        //尝试从缓存信息中获取openid
        if (ChannelCodeEnum.WXAPP.getChannelCode().equals(realChannelCode)) {
            String userToken = request.getHeader("userToken");
            if (StringUtil.isNullOrEmpty(req.getOpenid())) {
                //redis已无缓存信息的需要重新登录
                Map map = apiRedisService.getMapData(RedisKeyConfig.MINI_REDIS_TABLE + ":" + realChannelCode + ":" + userToken);
                if (map == null || map.isEmpty()) {
                    resp.setResultCode(WSEnum.INVALID_TOKEN.getResultCode());
                    resp.setResultInfo(WSEnum.INVALID_TOKEN.getResultInfo());
                    return resp;
                }
                req.setOpenid(map.get(HandlerConstants.WX_OPENID) == null ? "" : (String) map.get(HandlerConstants.WX_OPENID));
            }
        }
        if (ChannelCodeEnum.CHECKIN.getChannelCode().equals(realChannelCode)) {
            req.setOpenid(request.getHeader("openId"));
        }
        //微信渠道和小程序渠道，操作支付时需要openid
        if (ChannelCodeEnum.WXAPP.getChannelCode().equals(realChannelCode)
                || ChannelCodeEnum.WEIXIN.getChannelCode().equals(realChannelCode)
                || ChannelCodeEnum.CHECKIN.getChannelCode().equals(realChannelCode)) {
            if (StringUtil.isNullOrEmpty(req.getOpenid())) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo("请确认用户OPENID");
                return resp;
            }
        }
        PayMethodEnum payMethodEnum = PayMethodEnum.checkType(req.getPayMethod());
        if (payMethodEnum == null) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setResultInfo("请确认支付方式参数");
            return resp;
        }
        //加载支付配置
        String payType = req.getPayType();
        if (StringUtil.isNullOrEmpty(payType)) {
            payType = req.getOrderType();
        }
        OrderPayEnumType orderPayEnumType = OrderPayEnumType.checkEnum(payType);
        //品牌餐食重新设置订单描述信息
        if (StringUtils.isNotBlank(req.getChannelPriInfo()) && req.getChannelPriInfo().indexOf("BrandMeals") > -1) {
            orderPayEnumType = OrderPayEnumType.checkEnum("BrandMeals");
        }
        String subject = orderPayEnumType.getSubject();
        String body = orderPayEnumType.getBody();
        if (StringUtils.isNotBlank(req.getProductDesc())) {
            subject = req.getProductDesc();
            body = req.getProductDesc();
            if (req.getProductDesc().length() > 100) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo("商品描述限100个字符");
                return resp;
            }
        }
        String returnUrl = HandlerConstants.MWEB_PAY_RETURN_URL;
        PtPaymentWXReq paymentReq = new PtPaymentWXReq(HandlerConstants.VERSION, HandlerConstants.CURRENCY_CODE, returnUrl,
                HandlerConstants.BACK_RETURN_URL_PAY, "", subject, body, "", "Web");
        BeanUtils.copyProperties(req, paymentReq);
        paymentReq.setChannelNo(req.getChannelCode());
        paymentReq.setOrderType(orderPayEnumType.getOrderType());
        paymentReq.setChannelPriInfo(req.getChannelPriInfo() == null ? "" : req.getChannelPriInfo());
        if (ChannelCodeEnum.MWEB.getChannelCode().equals(req.getChannelCode())) {
            paymentReq.setChannelNo(ChannelCodeEnum.MOBILE.getChannelCode());
        }
        if (!StringUtil.isNullOrEmpty(realChannelCode)) {
            payChannelCode = realChannelCode;
        }
        //加载支付配置
        Bank bank = initPay(payChannelCode, payType, payMethodEnum.value, req.getMerchantPayment());
        if (bank == null) {
            logger.info(LOG_RESP_SIX, payChannelCode + "_" + payType + "_" + payMethodEnum.value);
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo(LOG_RESP_FOUR);
            return resp;
        }
        paymentReq.setGatewayNo(bank.getGateway());
        paymentReq.setGatewayType(bank.getGatewayType());
        Map<String, String> dynamicParameterMap = new HashMap<>();
        //动态参数设置
        dynamicParameterMap.put(VALUE_SIX, billCreateIP);
        dynamicParameterMap.put("FfpCardNo", req.getFfpCardNo());
        //微信 小程序支付需要openid参数
        if (!StringUtil.isNullOrEmpty(req.getOpenid())) {
            dynamicParameterMap.put("openid", req.getOpenid());
        }

        if (PayMethodEnum.HOWALLET.value.equals(req.getPayMethod())) {
            String versionCode = request.getHeader(HandlerConstants.CLIENT_VERSION_CODE);
            if (ChannelCodeEnum.MOBILE.getChannelCode().equals(realChannelCode) && VersionNoUtil.toMVerInt(versionCode) < 63100) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("您的版本不支持，请升级app！");
                return resp;
            } else {
                dynamicParameterMap.put("Mobile", req.getMobile());
            }

            ChannelRiskLoanInfo channelRiskLoanInfo = toGenChannelRiskLoanParams(req.getFfpCardNo(), request, realChannelCode, billCreateIP);
            dynamicParameterMap.put("ChannelRiskLoanInfo", JSON.toJSONString(channelRiskLoanInfo));

            String idNo = toGetCertNo(req, request);
            dynamicParameterMap.put("identityNo", idNo);
            dynamicParameterMap.put("UserId", req.getFfpId());
        }
        if (PayMethodEnum.DCEPAY.value.equals(req.getPayMethod())) {
            if (StringUtils.isAnyBlank(req.getVerifyCode(), req.getSmsType(), req.getMobile(), req.getBankCode())) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo(WSEnum.ERROR_REQUEST_PARAMS.getResultInfo());
                return resp;
            }
            boolean verifyCodePassed = checkVerifyCode(req.getVerifyCode(), req.getClientIp(), req.getChannelCode(), req.getMobile(), req.getSmsType(), req.getToken(), request);
            if (!verifyCodePassed) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("验证码验证未通过");
                return resp;
            }

            dynamicParameterMap.put("Mobile", req.getMobile());
            dynamicParameterMap.put("BankCode", req.getBankCode());
            //数字人民币的网关号需要特殊处理 不能是APP端配置的网关号 而是获取支付列表方式时获取不同银行的网关号
            /*paymentReq.setGatewayNo(req.getGatewayNo());*/ //因为版本上线问题 网关号暂时由从支付系统读取更改为从本地apollo读取
            List<DCEPayConfig> bankConfigList = handConfig.getBankConfigList();
            DCEPayConfig dcePayConfig = bankConfigList.stream().filter(bankConfig -> req.getBankCode().equals(bankConfig.getBankCode())).findFirst().orElse(null);
            if (null != dcePayConfig) {
                paymentReq.setGatewayNo(dcePayConfig.getGateWayNo());
            }

        }
        String dynamicParameters = JsonUtil.objectToJson(dynamicParameterMap);
        paymentReq.setDynamicParameters(dynamicParameters);
        Map<String, String> parametersMap = paymentReq.getPayPara();
        String sha1Content = paymentReq.getPayParaSHA1Str();
        //19支付平台渠道用户号
        String payChannel = bank.getPayChannel();
        sha1Content = sha1Content + payChannel;
        parametersMap.put(VAULE_ONE, payChannel);
        //密钥
        String key = bank.getKey();
        String chkValue = sha1Encode(sha1Content + key);
        parametersMap.put(VALUE_TWO, chkValue);
        paymentReq.setChkValue(chkValue);
        if ("Y".equals(bank.getAsync())) {
            resp = paymentService.doAsyncPay(parametersMap);
        } else {
            resp = paymentService.doPay(parametersMap);
        }
        if (!WSEnum.SUCCESS.getResultCode().equals(resp.getResultCode())) {
            return resp;
        }
        if (resp.getObjData() != null) {
            Map<String, String> payMap = (Map<String, String>) resp.getObjData();
            if ((payMap.containsKey(VALUE_FOUR) && !UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(payMap.get(VALUE_FOUR)))
                    || (payMap.containsKey("Status") && !UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(payMap.get("Status")))) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo(payMap.get("ErrorMsg") == null ? "支付异常！" : payMap.get("ErrorMsg"));
                //对于数字人民币支付，存在账户限额、余额不足等情况需要弹窗处理
                handleDCEPayResponse(resp, payMap, payMethodEnum.value);
            } else {
                if (PayMethodEnum.HOWALLET.value.equals(req.getPayMethod())) {
                    Map<String, Object> respMap = new HashMap<>();
                    respMap.put("dataType", "orderPay");
                    respMap.put("data", payMap);
                    resp.setObjData(respMap);
                } else if (PayMethodEnum.CCBPAY.value.equals(req.getPayMethod())) {
                    resp.setObjData(toGenerateOrderStr(req.getPlatformInfo(),payMap));
                }
            }
            this.putSendCouponRedisIfNecessary(req.getOrderType(), req.getOrderNo(), req.getFfpCardNo());
        }
        return resp;
    }


    /**
     * @param platform
     * @param payMap
     * @return java.lang.String
     * <AUTHOR>
     * @Description 生成订单支付串
     * @Date 10:13 2024/8/21
     **/
    private String toGenerateOrderStr(String platform, Map<String, String> payMap) {
        if (StringUtils.isEmpty(platform) || null == payMap || payMap.isEmpty()) {
            return "";
        }
        return "MERCHANTID=" + toConvertElement(payMap.get("MERCHANTID")) + "&POSID=" + toConvertElement(payMap.get("POSID"))
                + "&BRANCHID=" + toConvertElement(payMap.get("BRANCHID")) + "&ORDERID=" + toConvertElement(payMap.get("ORDERID")) + "&PAYMENT=" + toConvertElement(payMap.get("PAYMENT"))
                + "&CURCODE=" + toConvertElement(payMap.get("CURCODE")) + "&TXCODE=" + toConvertElement(payMap.get("TXCODE")) + "&REMARK1=" + toConvertElement((payMap.get("REMARK1"))) + "&REMARK2=" + toConvertElement(payMap.get("REMARK2"))
                + "&TYPE=" + toConvertElement(payMap.get("TYPE")) + "&GATEWAY=" + toConvertElement(payMap.get("GATEWAY")) + "&CLIENTIP=" + toConvertElement(payMap.get("CLIENTIP")) + "&REGINFO=" + toConvertElement(payMap.get("REGINFO"))
                + "&PROINFO=" + toConvertElement((payMap.get("PROINFO"))) + "&REFERER=" + toConvertElement(payMap.get("REFERER")) + "&THIRDAPPINFO=" + toConvertElement(payMap.get("THIRDAPPINFO")) + "&TIMEOUT=" + toConvertElement(payMap.get("TIMEOUT"))
                + "&MAC=" + toConvertElement(payMap.get("MAC"));
    }

    private String toConvertElement(String oriElement) {
        return StringUtils.isEmpty(oriElement) ? "" : oriElement;
    }

    private String toGetCertNo(WxPaymentReq req, HttpServletRequest request) {
        PtCRMResponse<PtMemberDetail> ptMemberDetailPtCRMResponse = null;
        String idNo = null;
        try {
            //从CRM获取身份证号
            String[] items = {MemberDetailRequestItemsEnum.CERTIFICATEINFO.eName};
            PtApiCRMRequest<PtMemberDetailRequest> ptApiRequest = CRMReqUtil.buildMemberDetailReq(req.getFfpCardNo(), req.getFfpId(), request, req.getChannelCode(), items);
            ptMemberDetailPtCRMResponse = memberService.memberDetail(ptApiRequest);
            if (ptMemberDetailPtCRMResponse.getCode() == 0) {
                MemberCertificateSoaModelV2 certificateInfo = CRMReqUtil.getCertificateInfo(ptMemberDetailPtCRMResponse.getData().getCertificateInfo(), CertificateTypeEnum.ID_CARD.getCode());
                if (certificateInfo != null) {
                    idNo = certificateInfo.getCertificateNumber();
                }
            }
            if (StringUtils.isEmpty(idNo)) {
                throw new CommonException(WSEnum.NETWORK_ERROR.getResultCode(), "未查询到身份证号");
            }
        } catch (CommonException ce) {
            log.error("吉祥钱包获取用户身份证号结果为空,查询方法：memberDetail(),查询结果:{}", JSON.toJSONString(ptMemberDetailPtCRMResponse));
            throw ce;
        } catch (Exception e) {
            log.error("吉祥钱包获取用户身份证号网络出错,查询方法：memberDetail(),查询结果:{}", JSON.toJSONString(ptMemberDetailPtCRMResponse));
            throw e;
        }
        return idNo;
    }


    @ApiOperation(value = "小程序获取支付唯一标识符", notes = "小程序获取支付唯一标识符")
    @RequestMapping(value = "/toGainUnionNo ", method = RequestMethod.POST)
    @InterfaceLog
    @SuppressWarnings("rawtypes")
    public BaseResp toGainUnionNo(@RequestBody @Validated GainUnionRequest req, BindingResult bindingResult, HttpServletRequest request) {
        BaseResp resp = new BaseResp();
        String reqId = StringUtil.newGUID() + request.getRequestURI();
        String billCreateIP = this.getClientIP(request);

        //参数检验
        if (bindingResult.hasErrors()) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
            return resp;
        }
        String reqJson = JsonMapper.buildNormalMapper().toJson(req);
        long start = System.currentTimeMillis();

        String payChannelCode = ChannelCodeEnum.MOBILE.getChannelCode();//此支付方式渠道统一规定为MOBILE

        //头部信息为真实的访问渠道
        String realChannelCode = request.getHeader(HandlerConstants.TOKEN_CHANNELCODE);
        //尝试从缓存信息中获取openid
        if (ChannelCodeEnum.WXAPP.getChannelCode().equals(realChannelCode)) {
            String userToken = request.getHeader("userToken");
            if (StringUtil.isNullOrEmpty(req.getUnionId())) {
                //redis已无缓存信息的需要重新登录
                Map map = apiRedisService.getMapData(RedisKeyConfig.MINI_REDIS_TABLE + ":" + realChannelCode + ":" + userToken);
                if (map == null || map.isEmpty()) {
                    resp.setResultCode(WSEnum.INVALID_TOKEN.getResultCode());
                    resp.setResultInfo(WSEnum.INVALID_TOKEN.getResultInfo());
                    return resp;
                }
                req.setUnionId(map.get(HandlerConstants.WX_OPENID) == null ? "" : (String) map.get(HandlerConstants.WX_OPENID));
            }
        }

        //微信渠道和小程序渠道，操作支付时需要openid
        if (ChannelCodeEnum.WXAPP.getChannelCode().equals(realChannelCode) && PayMethodEnum.YBWEIXINPAY.value.equals(req.getPayMethod())) {
            if (StringUtil.isNullOrEmpty(req.getUnionId())) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo("请确认用户OPENID");
                return resp;
            }
        }

        PayMethodEnum payMethodEnum = PayMethodEnum.checkType(req.getPayMethod());
        if (payMethodEnum == null) {
            throw new CommonException(WSEnum.ALERT_ERROR_MESSAGE.getResultCode(), "请确认支付方式参数");
        }

        String payType = req.getPayType();
        if (StringUtils.isEmpty(payType)) {
            payType = req.getOrderType();
        }
        OrderPayEnumType orderPayEnumType = OrderPayEnumType.checkEnum(payType);
        if (StringUtils.isEmpty(payType)) {
            throw new CommonException(WSEnum.ALERT_ERROR_MESSAGE.getResultCode(), "请确认支付类型");
        }

        //品牌餐食重新设置订单描述信息
        if (StringUtils.isNotBlank(req.getChannelPriInfo()) && req.getChannelPriInfo().contains("BrandMeals")) {
            orderPayEnumType = OrderPayEnumType.checkEnum("BrandMeals");
        }

        String subject = orderPayEnumType.getSubject();
        String body = orderPayEnumType.getBody();
        String returnUrl = HandlerConstants.MWEB_PAY_RETURN_URL;
        PtPaymentWXReq paymentReq = new PtPaymentWXReq(HandlerConstants.VERSION, HandlerConstants.CURRENCY_CODE, returnUrl,
                HandlerConstants.BACK_RETURN_URL_PAY, "", subject, body, "", "Web");

        BeanUtils.copyProperties(req, paymentReq);
        paymentReq.setChannelNo(payChannelCode);
        paymentReq.setOrderType(orderPayEnumType.getOrderType());
        paymentReq.setChannelPriInfo("");

        //加载支付配置
        Bank bank = initPay(payChannelCode, payType, payMethodEnum.value, null);
        if (bank == null) {
            logger.info(LOG_RESP_SIX, payChannelCode + "_" + payType + "_" + payMethodEnum.value);
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo(LOG_RESP_FOUR);
            return resp;
        }
        paymentReq.setGatewayNo(bank.getGateway());
        paymentReq.setGatewayType(bank.getGatewayType());
        Map<String, String> dynamicParameterMap = new HashMap<>();
        //动态参数设置
        dynamicParameterMap.put(VALUE_SIX, billCreateIP);

        //微信小程序支付需要openid参数
        if (PayMethodEnum.YBWEIXINPAY.value.equals(req.getPayMethod())) {
            dynamicParameterMap.put("OPENID", req.getUnionId());
        }
        //支付宝支付宝小程序需要传userId
        if (PayMethodEnum.YBALIPAY.value.equals(req.getPayMethod())) {
            dynamicParameterMap.put("USERID", req.getUnionId());
        }

        String dynamicParameters = JsonUtil.objectToJson(dynamicParameterMap);
        paymentReq.setDynamicParameters(dynamicParameters);

        Map<String, String> parametersMap = paymentReq.getPayPara();
        //19支付平台渠道用户号
        String sha1Content = paymentReq.getPayParaSHA1Str();
        String payChannel = bank.getPayChannel();
        sha1Content = sha1Content + payChannel;
        parametersMap.put(VAULE_ONE, payChannel);

        //密钥
        String key = bank.getKey();
        String chkValue = sha1Encode(sha1Content + key);
        parametersMap.put(VALUE_TWO, chkValue);
        paymentReq.setChkValue(chkValue);

        log.info("{}提交第三方支付参数:{}", reqId, JsonUtil.objectToJson(parametersMap));
        resp = paymentService.doPay4Mini(parametersMap);
        log.info("{}提交第三方支付响应:{}", reqId, JsonUtil.objectToJson(resp));

        if (!WSEnum.SUCCESS.getResultCode().equals(resp.getResultCode())) {
            return resp;
        }
        if (resp.getObjData() != null) {
            Map<String, String> payMap = (Map<String, String>) resp.getObjData();
            if ((payMap.containsKey(VALUE_FOUR) && !UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(payMap.get(VALUE_FOUR)))
                    || (payMap.containsKey("Status") && !UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(payMap.get("Status")))) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo(payMap.get("ErrorMsg") == null ? "获取订单唯一标识异常！" : payMap.get("ErrorMsg"));
            }
        }
        String result = JsonUtil.objectToJson(resp);
        saveRespInfo(PAY_DESC, reqId, billCreateIP, System.currentTimeMillis() - start, reqJson, result);
        return resp;
    }


    @ApiOperation(value = "获取先飞后付钱包额度", notes = "获取先飞后付钱包额度")
    @RequestMapping(value = "/getQuota ", method = RequestMethod.POST)
    @InterfaceLog
    @SuppressWarnings("rawtypes")
    public BaseResp getQuota(@RequestBody @Validated WalletDetailReq walletDetailReq, BindingResult bindingResult, HttpServletRequest request) {
        String headChannelCode = request.getHeader(HandlerConstants.TOKEN_CHANNELCODE);
        String platFormInfo = request.getHeader(HandlerConstants.CLIENT_PLATFORM);
        List<WalletQuotaResp> walletQuotaResps = new ArrayList<>();
        BaseResp resp = new BaseResp();
        //参数检验
        if (bindingResult.hasErrors()) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
            return resp;
        }
        walletDetailReq.setPlatformInfo(platFormInfo);
        //目前默认只有APP支持电子钱包以及先飞后付
        WalletDetailResp walletDetailResp = null;
        if (ChannelCodeEnum.MOBILE.getChannelCode().equals(headChannelCode)) {
            walletDetailReq.setPlatformInfo(headChannelCode);
            HttpResult httpResult = this.doPostClient(walletDetailReq, HandlerConstants.BASIC_INFO_URL + HandlerConstants.BASIC_WALLET_QUERYWALLETDETAIL, null, 15000, 3000);
            if (httpResult != null && httpResult.isResult()) {
                walletDetailResp = (WalletDetailResp) JsonUtil.jsonToBean(httpResult.getResponse(), new TypeToken<WalletDetailResp>() {
                }.getType());
            } else {
                //请求失败取默认值
                walletDetailResp = new WalletDetailResp();
                PAFRelativeResp pafRelativeResp = new PAFRelativeResp(false, "3000");
                walletDetailResp.setPafRelativeResp(pafRelativeResp);
            }

            //钱包获取额度
            if (ObjectUtil.isNotEmpty(walletDetailResp.getWalletRelativeResp())) {
                WalletQuotaResp walletQuotaResp = new WalletQuotaResp();
                walletQuotaResp.setOpenAlready(walletDetailResp.getWalletRelativeResp().isOpenAlready());
                walletQuotaResp.setAvailableQuota(walletDetailResp.getWalletRelativeResp().getBalanceRemaining());
                walletQuotaResp.setPayType(PayMethodEnum.HOWALLET.value);
                walletQuotaResps.add(walletQuotaResp);
            }

            //先飞后付获取额度
            if (ObjectUtil.isNotEmpty(walletDetailResp.getPafRelativeResp())) {
                WalletQuotaResp walletQuotaResp = new WalletQuotaResp();
                walletQuotaResp.setOpenAlready(walletDetailResp.getPafRelativeResp().isOpenAlready());
                walletQuotaResp.setAvailableQuota(walletDetailResp.getPafRelativeResp().getBalance());
                walletQuotaResp.setPayType(PayMethodEnum.PAYAFTERFLY.value);
                walletQuotaResps.add(walletQuotaResp);
            }

        }
        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
        resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
        resp.setObjData(walletQuotaResps);
        return resp;
    }


    private ChannelRiskLoanInfo toGenChannelRiskLoanParams(String ffpCardNo, HttpServletRequest request, String headChannelCode, String ip) {
        ChannelRiskLoanInfo channelRiskLoanInfo = new ChannelRiskLoanInfo();
        channelRiskLoanInfo.setWhiteFlag("N");
        PtCrmMileageRequest<MemberWhiteListRequest> ptWhiteListRequest = CRMReqUtil.buildCommCrmReq(request, headChannelCode, CRMReqUtil.getChannelPwd(headChannelCode));
        try {
            Map<String, String> map = new HashMap<>();
            MemberWhiteListRequest memberWhiteListRequest = new MemberWhiteListRequest();
            memberWhiteListRequest.setMemberCardNo(ffpCardNo);
            ptWhiteListRequest.setData(memberWhiteListRequest);
            Map<String, String> headMap = HttpUtil.getHeaderMap(ip, map, "");
            HttpResult httpResult = HttpUtil.doPostClient(ptWhiteListRequest, HandlerConstants.URL_FARE_OPEN_API + HandlerConstants.WHITELIST_QUERY, headMap);
            PtCrmMileageResponse<MemberWhiteListResponse> ptCrmMileageResponse;
            if (httpResult.isResult() && StringUtils.isNotEmpty(httpResult.getResponse())) {
                ptCrmMileageResponse = (PtCrmMileageResponse<MemberWhiteListResponse>) JsonUtil.jsonToBean(httpResult.getResponse(), new TypeToken<PtCrmMileageResponse<MemberWhiteListResponse>>() {
                }.getType());
                if (ptCrmMileageResponse.getCode() == 0
                        && null != ptCrmMileageResponse.getData()
                        && StringUtils.isNotEmpty(ptCrmMileageResponse.getData().getWhiteSign())) {
                    if ("Y".equals(ptCrmMileageResponse.getData().getWhiteSign())) {
                        channelRiskLoanInfo.setWhiteFlag("Y");
                    }
                }
            }
        } catch (Exception e) {
            log.error("[先飞后付白名单查询发生错误]：请求参数:{},错误信息:", JsonUtil.objectToJson(ptWhiteListRequest), e.getMessage());
            return channelRiskLoanInfo;
        }
        return channelRiskLoanInfo;
    }

    @SuppressWarnings("rawtypes")
    private void handleDCEPayResponse(BaseResp resp, Map<String, String> payMap, String value) {
        if (payMap.isEmpty() || !PayMethodEnum.DCEPAY.value.equals(value)) {
            return;
        }
        if (!"1001".equals(payMap.get(DCEPAY_CODE))) {
            resp.setResultCode(WSEnum.ALERT_ERROR_MESSAGE.getResultCode());
            resp.setResultInfo("使用数字人民币支付失败，请选择其他支付方式");
        }
        /*DCEPayEnum dcePayEnum = DCEPayEnum.getDCEPayEnum(payMap.get(DCEPAY_CODE));
        if (null != dcePayEnum) {
            if (!"1001".equals(dcePayEnum.code())) {
                resp.setResultCode(WSEnum.ALERT_ERROR_MESSAGE.getResultCode());
                resp.setResultInfo("使用数字人民币支付失败，请选择其他支付方式");
            }*/
            /*resp.setResultCode(WSEnum.ALERT_ERROR_MESSAGE.getResultCode());
            resp.setResultInfo(dcePayEnum.msg());*/
        /* }*/
    }

    private boolean checkVerifyCode(String verifyCode, String ip, String channelCode, String mobile, String smsType, String token, HttpServletRequest request) {
        boolean passed = Boolean.FALSE;
        String ipAddr;
        if (StringUtil.isNullOrEmpty(ip)) {
            ipAddr = this.getClientIP(request);
        } else {
            if (checkToken(token, ip, HandlerConstants.ACCESSSECRET)) {
                ipAddr = ip;
            } else {
                return passed;
            }
        }
        log.info(LOG_INFO_ONE, channelCode, ipAddr, mobile);
        if (!SourceType.DIGIT_CURRENCY_PAYMENT.getValue().equals(smsType)) {
            return passed;

        }
        //验证IP
        if (!this.chkDayOptErr(ipAddr, smsType, "")) {
            return passed;
        }
        //验证手机
        if (!this.chkDayOptErr(mobile, smsType, "")) {
            return passed;
        }
        String regCacheKey = "SMS:" + mobile + smsType;
        String veryCodeCacheStr = apiRedisService.getData(regCacheKey);
        if (StringUtils.isBlank(verifyCode) || StringUtils.isBlank(veryCodeCacheStr) || !verifyCode.equals(veryCodeCacheStr)) {
            return passed;
        }
        this.clearDayVisit("", mobile, smsType, "");//验证成功，清除账号限制
        return Boolean.TRUE;
    }

    //信用飞
    @InterfaceLog
    @ApiOperation(value = "信用飞支付", notes = "信用飞支付")
    @RequestMapping(value = "/orderByXinYongFei", method = RequestMethod.POST)
    public PaymentShouFuResp shoufuPay(@RequestBody WxPaymentReq req, HttpServletRequest request) {
        PaymentShouFuResp resp = new PaymentShouFuResp();
        String headChannelCode = request.getHeader(HandlerConstants.TOKEN_CHANNELCODE);//默认的支付渠道即为访问渠道
        String versionCode = request.getHeader(HandlerConstants.CLIENT_VERSION_CODE);
        String payChannelCode = headChannelCode;
        String returnUrl = HandlerConstants.MWEB_PAY_RETURN_URL;
        if (!StringUtil.isNullOrEmpty(req.getPayChannel())) {//主要处理M网站的支付问题
            payChannelCode = req.getPayChannel();
            if ("MWEB_XINYONGFEI".equals(req.getPayChannel())) {
                payChannelCode = ChannelCodeEnum.MWEB.getChannelCode();
            }
        }
        //<editor-fold desc="验证">
        boolean flag = this.checkKeyInfo(req.getFfpId(), req.getLoginKeyInfo(), req.getChannelCode());
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<WxPaymentReq>> violations = validator.validate(req);
        if (CollectionUtils.isNotEmpty(violations)) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(violations.iterator().next().getMessage());
            return resp;
        }
        String payType = req.getPayType();
        if (StringUtil.isNullOrEmpty(payType)) {
            payType = req.getOrderType();
        }
        //加载支付配置
        Bank bank = initPay(payChannelCode, payType, "SHOUFUPAY", req.getMerchantPayment());
        if (bank == null) {
            logger.info(LOG_RESP_SIX, payChannelCode + "_" + payType + "_" + "SHOUFUPAY");
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo(LOG_RESP_FOUR);
            return resp;
        }
        OrderPayEnumType orderPayEnumType = OrderPayEnumType.checkEnum(payType);
        //品牌餐食重新设置订单描述信息
        if (StringUtils.isNotBlank(req.getChannelPriInfo()) && req.getChannelPriInfo().indexOf("BrandMeals") > -1) {
            orderPayEnumType = OrderPayEnumType.checkEnum("BrandMeals");
        }
        String subject = orderPayEnumType.getSubject();
        String body = orderPayEnumType.getBody();
        if (StringUtils.isNotBlank(req.getProductDesc())) {
            subject = req.getProductDesc();
            body = req.getProductDesc();
        }
        PtPaymentWXReq paymentReq = new PtPaymentWXReq(HandlerConstants.VERSION, HandlerConstants.CURRENCY_CODE, returnUrl,
                HandlerConstants.BACK_RETURN_URL_PAY, "", subject, body, "", "Web");
        String postUrl = HandlerConstants.URL_ASYNC_PAY;
        BeanUtils.copyProperties(req, paymentReq);
        paymentReq.setChannelNo(req.getChannelCode());
        //根据渠道判断
        String billCreateIP = "";
        if (StringUtil.isNullOrEmpty(req.getClientIp())) {
            billCreateIP = this.getClientIP(request);
        } else {
            billCreateIP = req.getClientIp();
            if (!this.checkToken(req.getToken(), billCreateIP, HandlerConstants.ACCESSSECRET)) {
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setErrorInfo(LOG_RESP_ONE);
                return resp;
            }
        }
        //动态参数设置
        Map<String, String> dynamicParameterMap = new HashMap<>();
        dynamicParameterMap.put(VALUE_SIX, billCreateIP);
        dynamicParameterMap.put("openid", req.getOpenid());
        String dynamicParameters = JsonUtil.objectToJson(dynamicParameterMap);
        paymentReq.setDynamicParameters(dynamicParameters);
        paymentReq.setGatewayNo(bank.getGateway());
        paymentReq.setGatewayType(bank.getGatewayType());
        Map<String, String> parametersMap = paymentReq.getPayPara();
        String sha1Content = paymentReq.getPayParaSHA1Str();
        //19支付平台渠道用户号
        String payChannel = bank.getPayChannel();
        sha1Content = sha1Content + payChannel;
        parametersMap.put(VAULE_ONE, payChannel);
        //密钥
        String key = bank.getKey();
        String chkValue = sha1Encode(sha1Content + key);
        parametersMap.put(VALUE_TWO, chkValue);
        paymentReq.setChkValue(chkValue);
        //</editor-fold>
        HttpResult result = doPayPost(postUrl, parametersMap);
        if (result.isResult()) {
            logger.info(LOG_RESP_SEVEN, result.getResponse());
            String paymentInfo = result.getResponse().trim();
            if ((!StringUtil.isNullOrEmpty(paymentInfo)) && paymentInfo.indexOf(VALUE_FOUR) > -1) {
                resp = (PaymentShouFuResp) JsonUtil.jsonToBean(paymentInfo, PaymentShouFuResp.class);
            }
            if (StringUtil.isNullOrEmpty(resp.getRespCode())) {
                if (ChannelCodeEnum.MOBILE.getChannelCode().equals(headChannelCode)) {
                    if (VersionNoUtil.toMVerInt(versionCode) >= 61200) {
                        resp.setPaymentInfo(paymentInfo);
                    } else {
                        String payInfo = UUID.randomUUID().toString();
                        apiRedisService.putData("SHOUFUYOU_RESP:" + payInfo, paymentInfo, 1800);//将返回的html存入redis
                        resp.setPaymentInfo(payInfo);
                    }
                } else {
                    resp.setPaymentInfo(paymentInfo);
                }
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
                this.putSendCouponRedisIfNecessary(req.getOrderType(), req.getOrderNo(), req.getFfpCardNo());
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo(LOG_RESP_FIVE + resp.getErrorMsg());
                logger.error(LOG_RESP_THREE, resp.getErrorMsg());
            }
            return resp;
        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo(LOG_RESP_TWO);
            return resp;
        }

    }


    @ApiOperation(value = "获取支付宝新客立减参数", notes = "获取支付宝新客立减参数")
    @RequestMapping(value = "/initAliPayArgument", method = RequestMethod.POST)
    @InterfaceLog
    public BaseResp initAliPayArgument(@RequestBody BaseReq<AliPayArgumentReq> req) {
        BaseResp resp = new BaseResp();
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<BaseReq>> violations = validator.validate(req);
        if (CollectionUtils.isNotEmpty(violations)) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(violations.iterator().next().getMessage());
            return resp;
        }
        AliPayRtaRequest aliPayRtaRequest = new AliPayRtaRequest();
        String uuid = StringUtil.newGUID();
        aliPayRtaRequest.setRequest_id(uuid);
        List<DeviceReq> devices = new ArrayList<>();
        DeviceReq deviceReq = new DeviceReq();
        deviceReq.setDevice_id(req.getRequest().getDeviceId());
        deviceReq.setDevice_type(req.getRequest().getDeviceType());
        devices.add(deviceReq);
        aliPayRtaRequest.setDevices(devices);
        try {
            String url = "https://ugapi.alipay.com/rta/json/JuneyaoAirlines";
            HttpResult result = HttpUtil.doPostClient(aliPayRtaRequest, url);
            if (result == null || StringUtil.isNullOrEmpty(result.getResponse())) {
                resp.setResultCode(WSEnum.ERROR_QUERY_ERROR.getResultCode());
                resp.setResultInfo("请求支付宝错误");
                return resp;
            }
            AliPayArgumentResp aliPayArgumentResp = (AliPayArgumentResp) JsonUtil.jsonToBean(result.getResponse(), new TypeToken<AliPayArgumentResp>() {
            }.getType());
            log.info("内容 {}", JSONArray.toJSONString(aliPayArgumentResp));
            if (!aliPayArgumentResp.getSuccess()) {
                resp.setResultCode(WSEnum.ERROR_QUERY_ERROR.getResultCode());
                resp.setResultInfo("请求支付宝返回数据为空");
            } else {
                AliPayArgumentResponse aliPayArgumentResponse = new AliPayArgumentResponse();
                if (CollectionUtils.isNotEmpty(aliPayArgumentResp.getResponse().getRtaInfoList())) {
                    Map<String, String> map = new HashMap<>();
                    map.put(handConfig.getAliPayArgument(), aliPayArgumentResp.getResponse().getRtaInfoList().get(0));
                    aliPayArgumentResponse.setMap(map);
                    aliPayArgumentResponse.setBankInfo(aliPayArgumentResp.getResponse().getBankInfo());
                }
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                resp.setObjData(aliPayArgumentResponse);
            }

        } catch (Exception e) {
            StackTraceElement stackTraceElement = e.getStackTrace()[0];
            log.info("出错行数 {}, 错误内容 {}", stackTraceElement.getLineNumber(), e.getMessage());
            resp.setResultCode(WSEnum.ERROR_QUERY_ERROR.getResultCode());
            resp.setResultInfo("请求支付宝接口出错");
        }

        return resp;
    }


    @ApiOperation(value = "信用飞html初始化", notes = "信用飞html初始化")
    @RequestMapping(value = "/initShoufuPay", method = RequestMethod.POST)
    @InterfaceLog
    public BaseResp initShoufuPay(@RequestBody BaseReq<InitShoufuPayParam> req, HttpServletRequest request) {
        BaseResp resp = new BaseResp();
        InitShoufuPayParam initShoufuPayParam = req.getRequest();
        String channelCode = req.getChannelCode();
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<BaseReq>> violations = validator.validate(req);
        if (CollectionUtils.isNotEmpty(violations)) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(violations.iterator().next().getMessage());
            return resp;
        }
        String payMethod = initShoufuPayParam.getPayMethod();
        PayMethodEnum payMethodEnum = PayMethodEnum.checkType(payMethod);
        if (payMethodEnum == null) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setResultInfo("不支持的支付方式");
            return resp;
        }
        boolean flag = this.checkKeyInfo(initShoufuPayParam.getFfpId(), initShoufuPayParam.getLoginKeyInfo(), channelCode);
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        String returnHTML = apiRedisService.getData("SHOUFUYOU_RESP:" + initShoufuPayParam.getPayInfo());
        if (StringUtils.isBlank(returnHTML)) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setResultInfo("支付操作超时，请重新发起支付请求");
            return resp;
        } else {
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
            resp.setObjData(returnHTML);
            return resp;
        }
    }

    //华瑞银行
    @ApiOperation(value = "华瑞银行初始化", notes = "华瑞银行初始化")
    @RequestMapping(value = "/initByHr", method = RequestMethod.POST)
    @InterfaceLog
    public PaymentHRResp inithrbankPay(@RequestBody WxPaymentReq req, HttpServletRequest request) {
        PaymentHRResp resp = new PaymentHRResp();
        String payChannelCode = request.getHeader(HandlerConstants.TOKEN_CHANNELCODE);//默认的支付渠道即为访问渠道
        if (!StringUtil.isNullOrEmpty(req.getPayChannel())) {//主要处理M网站的支付问题
            payChannelCode = req.getPayChannel();
        }
        //用户参数验证
        boolean flag = this.checkKeyInfo(req.getFfpId(), req.getLoginKeyInfo(), req.getChannelCode());
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<WxPaymentReq>> violations = validator.validate(req);
        if (CollectionUtils.isNotEmpty(violations)) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(violations.iterator().next().getMessage());
            return resp;
        }
        PtPaymentReq paymentReq = new PtPaymentReq();
        String postUrl = HandlerConstants.URL_INIT_BY_HUARUI;
        BeanUtils.copyProperties(req, paymentReq);
        paymentReq.setChannelNo(req.getChannelCode());
        //加载支付配置
        String payType = req.getPayType();
        if (StringUtil.isNullOrEmpty(payType)) {
            payType = req.getOrderType();
        }
        Bank bank = initPay(payChannelCode, payType, VALUE_FIVE, req.getMerchantPayment());
        if (bank == null) {
            logger.info(LOG_RESP_SIX, payChannelCode + "_" + payType + "_" + VALUE_FIVE);
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo(LOG_RESP_FOUR);
            return resp;
        }
        paymentReq.setGatewayNo(bank.getGateway());//支付网关号
        paymentReq.setGatewayType(bank.getGatewayType());
        Map<String, String> parametersMap = paymentReq.getPayPara();
        HttpResult result = doPayPost(postUrl, parametersMap);
        if (result.isResult()) {
            logger.info(LOG_RESP_SEVEN, result.getResponse());
            String paymentInfo = result.getResponse().trim();
            if ((!StringUtil.isNullOrEmpty(paymentInfo)) && paymentInfo.indexOf("MD5sign") > -1 && paymentInfo.indexOf("random") > -1) {
                resp = (PaymentHRResp) JsonUtil.jsonToBean(paymentInfo, PaymentHRResp.class);
                if (StringUtil.isNullOrEmpty(resp.getMD5sign())) {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setErrorInfo("应用签名不正确");
                    if (UnifiedOrderResultEnum.ERROR_PNR.getResultCode().equals(resp.getRespCode())) {
                        resp.setResultCode(WSEnum.TOAST_ERROR.getResultCode());
                        resp.setErrorInfo(LOG_RESP_PNR_ERROR);
                    }
                } else {
                    if (StringUtil.isNullOrEmpty(resp.getRandomNumber())) {
                        resp.setRandomNumber(resp.getRandom());
                    }
                    resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                    resp.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
                }
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo("请求配置信息出错");
                if (UnifiedOrderResultEnum.ERROR_PNR.getResultCode().equals(resp.getRespCode())) {
                    resp.setResultCode(WSEnum.TOAST_ERROR.getResultCode());
                    resp.setErrorInfo(LOG_RESP_PNR_ERROR);
                }
                return resp;
            }
        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo(LOG_RESP_TWO);
            return resp;
        }
        return resp;
    }

    //华瑞银行支付
    @ApiOperation(value = "华瑞银行支付", notes = "华瑞银行支付")
    @RequestMapping(value = "/hrbankpay", method = RequestMethod.POST)
    public PaymentHRResultResp hrbankPay(@RequestBody WxPaymentReq req, HttpServletRequest request) {
        String reqJson = JsonUtil.objectToJson(req);
        logger.info("hrbankPay请求参数:{}", reqJson);
        PaymentHRResultResp resp = new PaymentHRResultResp();
        String payChannelCode = req.getChannelCode();//默认的支付渠道即为访问渠道
        if (!StringUtil.isNullOrEmpty(req.getPayChannel())) {//主要处理M网站的支付问题
            payChannelCode = req.getPayChannel();
        }
        //<editor-fold desc="验证">
        boolean flag = this.checkKeyInfo(req.getFfpId(), req.getLoginKeyInfo(), req.getChannelCode());
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<WxPaymentReq>> violations = validator.validate(req);
        if (CollectionUtils.isNotEmpty(violations)) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(violations.iterator().next().getMessage());
            return resp;
        }
        //加载支付配置
        String payType = req.getPayType();
        if (StringUtil.isNullOrEmpty(payType)) {
            payType = req.getOrderType();
        }
        OrderPayEnumType orderPayEnumType = OrderPayEnumType.checkEnum(payType);
        //品牌餐食重新设置订单描述信息
        if (StringUtils.isNotBlank(req.getChannelPriInfo()) && req.getChannelPriInfo().indexOf("BrandMeals") > -1) {
            orderPayEnumType = OrderPayEnumType.checkEnum("BrandMeals");
        }
        String subject = orderPayEnumType.getSubject();
        String body = orderPayEnumType.getBody();
        if (StringUtils.isNotBlank(req.getProductDesc())) {
            subject = req.getProductDesc();
            body = req.getProductDesc();
        }
        //</editor-fold>
        //<editor-fold desc="加密,生成加密字符串">
        PtPaymentWXReq paymentReq = new PtPaymentWXReq(HandlerConstants.VERSION, HandlerConstants.CURRENCY_CODE, "",
                HandlerConstants.BACK_RETURN_URL_PAY, "", subject, body, "", "Web");
        String postUrl = HandlerConstants.URL_PAY;
        BeanUtils.copyProperties(req, paymentReq);
        Bank bank = initPay(payChannelCode, payType, VALUE_FIVE, req.getMerchantPayment());
        if (bank == null) {
            logger.info(LOG_RESP_SIX, payChannelCode + "_" + payType + "_" + VALUE_FIVE);
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo(LOG_RESP_FOUR);
            return resp;
        }
        paymentReq.setChannelNo(req.getChannelCode());
        String billCreateIP = "";
        if (StringUtil.isNullOrEmpty(req.getClientIp())) {
            billCreateIP = this.getClientIP(request);
        } else {
            billCreateIP = req.getClientIp();
            if (!this.checkToken(req.getToken(), billCreateIP, HandlerConstants.ACCESSSECRET)) {
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setErrorInfo(LOG_RESP_ONE);
                return resp;
            }
        }
        paymentReq.setGatewayNo(bank.getGateway());
        paymentReq.setGatewayType(bank.getGatewayType());
        //动态参数设置
        Map<String, String> dynamicParameterMap = new HashMap<>();
        dynamicParameterMap.put(VALUE_SIX, billCreateIP);
        String dynamicParameters = JsonUtil.objectToJson(dynamicParameterMap);
        paymentReq.setDynamicParameters(dynamicParameters);

        Map<String, String> parametersMap = paymentReq.getPayPara();
        String sha1Content = paymentReq.getPayParaSHA1Str();
        //19支付平台渠道用户号
        String payChannel = bank.getPayChannel();
        sha1Content = sha1Content + payChannel;
        parametersMap.put(VAULE_ONE, payChannel);
        //密钥
        String key = bank.getKey();
        String chkValue = sha1Encode(sha1Content + key);
        parametersMap.put(VALUE_TWO, chkValue);
        paymentReq.setChkValue(chkValue);
        //</editor-fold>
        HttpResult result = doPayPost(postUrl, parametersMap);
        if (result.isResult()) {
            String paymentInfo = result.getResponse();
            logger.info("doPayPost返回结果:{}", paymentInfo);
            if (!StringUtil.isNullOrEmpty(paymentInfo) && paymentInfo.indexOf("sign") > -1) {
                try {
                    PtHRPaymentResp response = (PtHRPaymentResp) JsonUtil.jsonToBean(paymentInfo, PtHRPaymentResp.class);
                    if (StringUtil.isNullOrEmpty(response.getSign()) || StringUtil.isNullOrEmpty(response.getRandom())) {
                        resp.setResultCode(WSEnum.ERROR.getResultCode());
                        resp.setErrorInfo("支付异常:订单签名不能为空");
                        if (UnifiedOrderResultEnum.ERROR_PNR.getResultCode().equals(response.getRespCode())) {
                            resp.setResultCode(WSEnum.TOAST_ERROR.getResultCode());
                            resp.setErrorInfo(LOG_RESP_PNR_ERROR);
                        }
                    } else {
                        BeanUtils.copyProperties(response, resp);
                        PtTranDetail tranDetail = response.getTranDetail();
                        PtAutoFillData autoFillData = response.getAutoFillData();
                        resp.setAttach(StringUtil.isNullOrEmpty(tranDetail.getAttach()) ? "" : tranDetail.getAttach());
                        resp.setTimeValid(tranDetail.getTimeValid());
                        resp.setOutTradeNo(tranDetail.getOutTradeNo());
                        resp.setBackUrl(tranDetail.getBackUrl());//通知地址
                        resp.setBody(tranDetail.getBody());
                        resp.setTotalFee(tranDetail.getTotalFee());
                        resp.setDetail(tranDetail.getDetail());
                        resp.setGoodsTag(StringUtil.isNullOrEmpty(tranDetail.getGoodsTag()) ? "" : tranDetail.getGoodsTag());
                        resp.setMchName(tranDetail.getMchName());
                        resp.setMchID(tranDetail.getMchID());
                        resp.setSpbillCreateIp(billCreateIP);
                        resp.setFeeType(tranDetail.getFeeType());
                        resp.setLimitPay(StringUtil.isNullOrEmpty(tranDetail.getLimitPay()) ? "" : tranDetail.getLimitPay());
                        resp.setConfirmOrder(tranDetail.getConfirmOrder());
                        resp.setUnpaidAmount(tranDetail.getUnpaidAmount());
                        resp.setPaidAmount(tranDetail.getPaidAmount());

                        resp.setRealName(StringUtil.isNullOrEmpty(autoFillData.getRealName()) ? "" : autoFillData.getRealName());
                        resp.setCardNo(StringUtil.isNullOrEmpty(autoFillData.getCardNo()) ? "" : autoFillData.getCardNo());
                        resp.setIdentity(StringUtil.isNullOrEmpty(autoFillData.getIdentity()) ? "" : autoFillData.getIdentity());
                        resp.setMobile(StringUtil.isNullOrEmpty(autoFillData.getMobile()) ? "" : autoFillData.getMobile());
                        resp.setRevmobile(StringUtil.isNullOrEmpty(autoFillData.getRevmobile()) ? "" : autoFillData.getRevmobile());

                        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                        resp.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
                        this.putSendCouponRedisIfNecessary(req.getOrderType(), req.getOrderNo(), req.getFfpCardNo());
                    }
                } catch (Exception e) {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setErrorInfo("订单参数转换异常");
                }
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo("订单签名不正确");
            }

        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo(LOG_RESP_TWO);
        }
        String respJson = JsonUtil.objectToJson(resp);
        logger.info("请求IP:{},hrbankPayWX返回的结果:{}", billCreateIP, respJson);
        //2021-02-07 金卡及以上会员不调用同盾风控
        PtCRMResponse<PtMemberDetail> ptCRMResponse = memberCacheService.queryMemberDetailInfo(request, req.getFfpId(), req.getFfpCardNo(), req.getChannelCode());
        int levelCode = 0;
        if (ptCRMResponse != null && ptCRMResponse.getCode() == 0) {
            levelCode = NumberUtils.toInt(ptCRMResponse.getData().getStateInfo().getMemberLevelCode());
        }
        //同盾校验
        //5.3版本使用同盾
        //支付目前只调用同盾记录，不做判断
        if ("Y".equals(handConfig.getUseTongDun()) && levelCode < NumberUtils.toInt(MemberLevelEnum.Golden.getLevelCode())) {
            transferTD(req, request, resp.getResultCode(), PayMethodTypeEnum.HUARUIYINHANG.getName());

        } else {
            log.debug("请求未进行同盾验证，渠道订单号：{}，会员号：{}", req.getChannelOrderNo(), req.getFfpCardNo());
        }
        return resp;
    }

    //招商银行支付
    @ApiOperation(value = "招商一网通支付", notes = "招商一网通支付")
    @RequestMapping(value = "/cmbChinaPay", method = RequestMethod.POST)
    @InterfaceLog
    public PaymentResp cmbChinaPay(@RequestBody WxPaymentReq req, HttpServletRequest request) {
        PaymentResp resp = new PaymentResp();
        String reqId = StringUtil.newGUID() + request.getRequestURI();
        String ip = this.getClientIP(request);
        String reqJson = JsonUtil.objectToJson(req);
        String headChannelCode = request.getHeader(HandlerConstants.TOKEN_CHANNELCODE);
        String verCode = request.getHeader(HandlerConstants.CLIENT_VERSION_CODE);
        //参数有效性检验
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<WxPaymentReq>> violations = validator.validate(req);
        if (CollectionUtils.isNotEmpty(violations)) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(violations.iterator().next().getMessage());
            return resp;
        }
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(req.getFfpId(), req.getLoginKeyInfo(), req.getChannelCode());
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        try {
            String payChannelCode = req.getChannelCode();//默认的支付渠道即为访问渠道
            if (!StringUtil.isNullOrEmpty(req.getPayChannel())) {//主要处理M网站的支付问题
                payChannelCode = req.getPayChannel();
                if ("MWEB_CMBCHINAPAY".equals(payChannelCode)) {
                    payChannelCode = ChannelCodeEnum.MWEB.getChannelCode();
                }
            }
            String payType = req.getPayType();
            if (StringUtil.isNullOrEmpty(payType)) {
                payType = req.getOrderType();
            }
            OrderPayEnumType orderPayEnumType = OrderPayEnumType.checkEnum(payType);
            //品牌餐食重新设置订单描述信息
            if (StringUtils.isNotBlank(req.getChannelPriInfo()) && req.getChannelPriInfo().indexOf("BrandMeals") > -1) {
                orderPayEnumType = OrderPayEnumType.checkEnum("BrandMeals");
            }
            String subject = orderPayEnumType.getSubject();
            String body = orderPayEnumType.getBody();
            if (StringUtils.isNotBlank(req.getProductDesc())) {
                subject = req.getProductDesc();
                body = req.getProductDesc();
            }
            String returnUrl = "";
            //<editor-fold desc="加密,生成加密字符串">
            PtPaymentWXReq paymentReq = new PtPaymentWXReq(HandlerConstants.VERSION, HandlerConstants.CURRENCY_CODE, returnUrl,
                    HandlerConstants.BACK_RETURN_URL_PAY, "", subject, body, "", "Web");
            String postUrl = HandlerConstants.URL_PAY;
            BeanUtils.copyProperties(req, paymentReq);
            paymentReq.setChannelNo(req.getChannelCode());
            String billCreateIP = ip;
            String gatewayNo = "";
            //加载支付配置
            Bank bank =null;
            if (ChannelCodeEnum.MOBILE.getChannelCode().equals(headChannelCode) && VersionNoUtil.compareVersions(req.getClientVersion(),"7.10.3")>=0) {
                 bank = initPay(payChannelCode, payType, PayMethodEnum.CMBCHINAPAYV2.value, req.getMerchantPayment());
            }else {
                bank = initPay(payChannelCode, payType, PayMethodEnum.CMBCHINAPAY.value, req.getMerchantPayment());
            }
            if (bank == null) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo(LOG_RESP_FOUR);
                logger.info("请求号:{},未找到对应的配置信息:{}", reqId, payChannelCode + "_" + payType + "_" + PayMethodEnum.CMBCHINAPAY.value);
                return resp;
            }
            gatewayNo = bank.getGateway();//网关号
            paymentReq.setGatewayNo(gatewayNo);
            paymentReq.setGatewayType(bank.getGatewayType());
            Map dynamicParametersMap = new HashMap<>();
            dynamicParametersMap.put(VALUE_SIX, billCreateIP);
            dynamicParametersMap.put("FfpId", req.getFfpId());
            paymentReq.setDynamicParameters(JsonUtil.objectToJson(dynamicParametersMap));
            Map<String, String> parametersMap = paymentReq.getPayPara();
            String sha1Content = paymentReq.getPayParaSHA1Str();
            //19支付平台渠道用户号
            String payChannel = bank.getPayChannel();
            sha1Content = sha1Content + payChannel;
            parametersMap.put(VAULE_ONE, payChannel);
            //密钥
            String key = bank.getKey();
            String chkValue = sha1Encode(sha1Content + key);
            parametersMap.put(VALUE_TWO, chkValue);
            paymentReq.setChkValue(chkValue);
            HttpResult result = doPayPost(postUrl, parametersMap);
            //2021-02-07 金卡及以上会员不调用同盾风控
            PtCRMResponse<PtMemberDetail> ptCRMResponse = memberCacheService.queryMemberDetailInfo(request, req.getFfpId(), req.getFfpCardNo(), req.getChannelCode());
            int levelCode = 0;
            if (ptCRMResponse != null && ptCRMResponse.getCode() == 0) {
                levelCode = NumberUtils.toInt(ptCRMResponse.getData().getStateInfo().getMemberLevelCode());
            }
            if (result.isResult()) {
                logger.info(LOG_RESP_EIGHT, reqId, result.getResponse());
                String paymentInfo = result.getResponse().trim();
                if ((!StringUtil.isNullOrEmpty(paymentInfo)) && paymentInfo.indexOf(VALUE_FOUR) > -1) {
                    resp = (PaymentResp) JsonUtil.jsonToBean(paymentInfo, PaymentResp.class);
                }
                if (StringUtil.isNullOrEmpty(resp.getRespCode()) || "1001".equals(resp.getRespCode())) {
                    resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                    this.putSendCouponRedisIfNecessary(req.getOrderType(), req.getOrderNo(), req.getFfpCardNo());
                    //新版M站的网页支付
                    if (ChannelCodeEnum.MWEB.getChannelCode().equals(headChannelCode) || ChannelCodeEnum.WEIXIN.getChannelCode().equals(headChannelCode)) {
                        resp.setErrorInfo(paymentInfo);
                    } else if (ChannelCodeEnum.MOBILE.getChannelCode().equals(headChannelCode) &&VersionNoUtil.compareVersions(req.getClientVersion(),"7.10.3")<0) {
                        resp.setErrorInfo(paymentInfo);
                    } else if (ChannelCodeEnum.MOBILE.getChannelCode().equals(headChannelCode) &&VersionNoUtil.compareVersions(req.getClientVersion(),"7.10.3")>=0) {
                        String paymentJson = paymentInfo.replace("'", "\"");
                        String   paymentEncode = StringUtil.urlEncode(paymentJson);
                        resp.setErrorInfo(paymentEncode);
                    } else {
                        resp.setErrorInfo("暂不支持的支付方式");
                        resp.setResultCode(WSEnum.ERROR.getResultCode());
                        return resp;
                    }
                } else {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setErrorInfo(LOG_RESP_FIVE + resp.getErrorMsg());
                    if (UnifiedOrderResultEnum.ERROR_PNR.getResultCode().equals(resp.getRespCode())) {
                        resp.setResultCode(WSEnum.TOAST_ERROR.getResultCode());
                        resp.setErrorInfo(LOG_RESP_PNR_ERROR);
                    }
                }

                //同盾校验
                //5.3版本使用同盾
                //支付目前只调用同盾记录，不做判断
                if ("Y".equals(handConfig.getUseTongDun()) && levelCode < NumberUtils.toInt(MemberLevelEnum.Golden.getLevelCode())) {
                    transferTD(req, request, resp.getResultCode(), PayMethodTypeEnum.YIWANGTONG.getName());
                } else {
                    log.debug("请求未进行同盾验证，渠道订单号：{}，会员号：{}", req.getChannelOrderNo(), req.getFfpCardNo());
                }
                return resp;
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo(LOG_RESP_TWO);
                //同盾校验
                //5.3版本使用同盾
                //支付目前只调用同盾记录，不做判断
                if ("Y".equals(handConfig.getUseTongDun()) && levelCode < NumberUtils.toInt(MemberLevelEnum.Golden.getLevelCode())) {
                    transferTD(req, request, resp.getResultCode(), PayMethodTypeEnum.YIWANGTONG.getName());
                } else {
                    log.debug("请求未进行同盾验证，渠道订单号：{}，会员号：{}", req.getChannelOrderNo(), req.getFfpCardNo());
                }
                return resp;
            }

        } catch (Exception e) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo(LOG_RESP_NIGHT);
            saveError("招商一网通支付", reqId, ip, reqJson, e);
            return resp;
        }
    }

    //0元支付
    @RequestMapping(value = "/payment0", method = RequestMethod.POST)
    public PaymentResp payment0(@RequestBody WxPaymentReq req, HttpServletRequest request) {
        PaymentResp resp = new PaymentResp();
        String reqId = StringUtil.newGUID() + "_payment0";
        String ip = this.getClientIP(request);
        if (!StringUtil.isNullOrEmpty(req.getClientIp())) {
            if (!this.checkToken(req.getToken(), req.getClientIp(), HandlerConstants.ACCESSSECRET)) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo("您的支付请求抛锚了！");
                return resp;
            }
            ip = req.getClientIp();
        }
        String reqJson = JsonUtil.objectToJson(req);
        logger.info(LOG_RESP_ELE, reqId, ip, reqJson);
        //参数有效性检验
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<WxPaymentReq>> violations = validator.validate(req);
        if (CollectionUtils.isNotEmpty(violations)) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setErrorInfo(violations.iterator().next().getMessage());
            return resp;
        }
        //验证用户查询是否正常
        boolean flag = this.checkKeyInfo(req.getFfpId(), req.getLoginKeyInfo(), req.getChannelCode());
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        try {
            String channelCode = req.getChannelCode();
            String key = getChannelInfo(channelCode, "20");
            Map<String, String> parametersMap = VirtualPaymentConvert.payment0(req.getChannelCode(), req.getOrderNo(), req.getChannelOrderNo(), key, "", req.getChannelBuyDatetime(), req.getOrderType());
            parametersMap.put("UseScore", req.getUseScore());
            String postUrl = HandlerConstants.URL_PAY;
            HttpResult result = doPayPost(postUrl, parametersMap);
            //2021-02-07 金卡及以上会员不调用同盾风控
            PtCRMResponse<PtMemberDetail> ptCRMResponse = memberCacheService.queryMemberDetailInfo(request, req.getFfpId(), req.getFfpCardNo(), req.getChannelCode());
            int levelCode = 0;
            if (ptCRMResponse != null && ptCRMResponse.getCode() == 0) {
                levelCode = NumberUtils.toInt(ptCRMResponse.getData().getStateInfo().getMemberLevelCode());
            }
            if (result.isResult()) {
                String payment = result.getResponse();
                resp = (PaymentResp) JsonUtil.jsonToBean(payment, PaymentResp.class);
                if ("1001".equals(resp.getRespCode())) {
                    resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                    this.putSendCouponRedisIfNecessary(req.getOrderType(), req.getOrderNo(), req.getFfpCardNo());
                } else {
                    resp.setErrorInfo(resp.getErrorMsg());
                    if (UnifiedOrderResultEnum.ERROR_PNR.getResultCode().equals(resp.getRespCode())) {
                        resp.setResultCode(WSEnum.TOAST_ERROR.getResultCode());
                        resp.setErrorInfo(LOG_RESP_PNR_ERROR);
                    }
                }
                logger.info(LOG_RESP_EIGHT, reqId, result.getResponse());
                //同盾校验
                //5.3版本使用同盾
                //支付目前只调用同盾记录，不做判断
                if ("Y".equals(handConfig.getUseTongDun()) && levelCode < NumberUtils.toInt(MemberLevelEnum.Golden.getLevelCode())) {
                    transferTD(req, request, resp.getResultCode(), PayMethodTypeEnum.QITA.getName());
                } else {
                    log.debug("请求未进行同盾验证，渠道订单号：{}，会员号：{}", req.getChannelOrderNo(), req.getFfpCardNo());
                }
                return resp;
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo(LOG_RESP_TWO);
                String respJson = JsonUtil.objectToJson(resp);
                logger.info(LOG_RESP_TEN, reqId, respJson);
                //同盾校验
                //5.3版本使用同盾
                //支付目前只调用同盾记录，不做判断
                if ("Y".equals(handConfig.getUseTongDun()) && levelCode < NumberUtils.toInt(MemberLevelEnum.Golden.getLevelCode())) {
                    transferTD(req, request, resp.getResultCode(), PayMethodTypeEnum.QITA.getName());
                } else {
                    log.debug("请求未进行同盾验证，渠道订单号：{}，会员号：{}", req.getChannelOrderNo(), req.getFfpCardNo());
                }
                return resp;
            }

        } catch (Exception e) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo(LOG_RESP_NIGHT);
            logger.info(LOG_RESP_TEN, reqId, e.getMessage());
            return resp;
        }
    }

    /**
     * 仅限内部使用(此方法不对外暴露)
     *
     * @param req
     * @param request
     * @return
     */
    @RequestMapping(value = "/virtualPayment", method = RequestMethod.POST)
    @InterfaceLog
    public BaseResp<OrderPayInfo> virtualPayment(@RequestBody WxPaymentReq req, HttpServletRequest request) {
        BaseResp resp = new BaseResp();
        if ("Y".equals(handConfig.getSupportVirtualPayment())) {
            String reqId = StringUtil.newGUID() + "_virtualPayment";
            String ip = this.getClientIP(request);
            String reqJson = JsonUtil.objectToJson(req);
            logger.info(LOG_RESP_ELE, reqId, ip, reqJson);
            //参数有效性检验
            ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
            Validator validator = factory.getValidator();
            Set<ConstraintViolation<WxPaymentReq>> violations = validator.validate(req);
            if (CollectionUtils.isNotEmpty(violations)) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setErrorInfo(violations.iterator().next().getMessage());
                return resp;
            }
            if (!("B2C".equals(req.getPlatformInfo()) && ("2018juneyaoair2019".equals(req.getPayMethod()) || "2018juneyaoair2019".equals(req.getToken())))) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setErrorInfo("无访问权限！");
                return resp;
            }
            //验证用户查询是否正常
            boolean flag = this.checkKeyInfo(req.getFfpId(), req.getLoginKeyInfo(), req.getChannelCode());
            if (!flag) {
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                return resp;
            }
            try {
                String channelCode = req.getChannelCode();
                String key = getChannelInfo(channelCode, "20");
                Map<String, String> parametersMap = VirtualPaymentConvert.virtualPayment(channelCode, req.getOrderNo(), req.getChannelOrderNo(), key, "", req.getChannelBuyDatetime(), req.getOrderType(), req.getAmount());
                parametersMap.put("UseScore", req.getUseScore());
                String postUrl = HandlerConstants.URL_PAY;
                HttpResult result = doPayPost(postUrl, parametersMap);
                if (result.isResult()) {
                    String payment = result.getResponse();
                    PaymentResp paymentResp = (PaymentResp) JsonUtil.jsonToBean(payment, PaymentResp.class);
                    if ("1001".equals(paymentResp.getRespCode())) {
                        OrderPayInfo orderPayInfo = new OrderPayInfo();
                        orderPayInfo.setOrderNo(paymentResp.getOrderNo());
                        orderPayInfo.setChannelOrderNo(paymentResp.getChannelOrderNo());
                        orderPayInfo.setAmount(paymentResp.getAmount());
                        orderPayInfo.setCurrency(paymentResp.getCurrency());
                        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                        resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                        this.putSendCouponRedisIfNecessary(req.getOrderType(), req.getOrderNo(), req.getFfpCardNo());
                    } else {
                        resp.setResultCode(WSEnum.ERROR.getResultCode());
                        resp.setResultInfo(paymentResp.getErrorMsg());
                        if (UnifiedOrderResultEnum.ERROR_PNR.getResultCode().equals(paymentResp.getRespCode())) {
                            resp.setResultCode(WSEnum.TOAST_ERROR.getResultCode());
                            resp.setErrorInfo(LOG_RESP_PNR_ERROR);
                        }
                    }
                    logger.info(LOG_RESP_EIGHT, reqId, result.getResponse());
                    return resp;
                } else {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo(LOG_RESP_TWO);
                    String respJson = JsonUtil.objectToJson(resp);
                    logger.info(LOG_RESP_TEN, reqId, respJson);
                    return resp;
                }

            } catch (Exception e) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo(LOG_RESP_NIGHT);
                logger.info(LOG_RESP_TEN, reqId, e.getMessage());
                return resp;
            }
        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setErrorInfo("不支持的支付方式！");
            return resp;
        }
    }


    /**
     * 冻结积分
     *
     * @return
     */
    @InterfaceLog
    @RequestMapping(value = "/OrderScroeShare", method = RequestMethod.POST)
    @ApiOperation(value = "积分后置冻结积分", notes = "")
    public BaseResp OrderScroeShare(@RequestBody @Validated OrderScroeShareReq orderScroeShareReq, BindingResult bindingResult, HttpServletRequest request) {
        BaseResp baseResp = new BaseResp();
        String ip = this.getClientIP(request);
        if (bindingResult.hasErrors()) {
            throw new RequestParamErrorException(bindingResult.getAllErrors().get(0).getDefaultMessage());
        }
        String channelCode = StringUtils.isNotEmpty(orderScroeShareReq.getChannelCode()) ? orderScroeShareReq.getChannelCode() : ChannelCodeEnum.MOBILE.getChannelCode();
        ClientInfo clientInfo = initClientInfo(request, orderScroeShareReq.getChannelCode(), orderScroeShareReq.getFfpId(), orderScroeShareReq.getFfpCardNo());
        AccountCheckParam accountCheckParam = new AccountCheckParam();
        accountCheckParam.setBlackBox(orderScroeShareReq.getBlackBox());
        accountCheckParam.setOperation(SensitiveOperationEnum.USE_SALE.getOperation());
        accountCheckParam.setFfpId(orderScroeShareReq.getFfpId());
        accountCheckParam.setFfpCardNo(orderScroeShareReq.getFfpCardNo());
        //同盾风控检查
        memberPasswordService.checkAccount(clientInfo, accountCheckParam, null, null);
        //积分使用规则检验
        if (orderService.checkScoreUseRule(clientInfo, channelCode)) {
            throw new CommonException(WSEnum.NO_REAL_NAME.getResultCode(), "非常抱歉，当前账户存在安全风险，通过实名认证后才可使用积分，请前往实名认证");
        }
        baseResp = orderService.checkFreeScoreLimit(orderScroeShareReq.getFfpCardNo(), orderScroeShareReq.getFfpId(), orderScroeShareReq.getChannelCode(),
                getChannelInfo(orderScroeShareReq.getChannelCode(), "40"), Integer.parseInt(orderScroeShareReq.getUseScoreTotal()),
                orderScroeShareReq.getScoreVerifyPassword(), request);
        if (!WSEnum.SUCCESS.getResultCode().equals(baseResp.getResultCode())) {
            return baseResp;
        }
        String url = HandlerConstants.URL_FARE_OPEN_API + HandlerConstants.PAY_ORDER_SCROE_SHARE;
        Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
        OrderScroeShareRequest orderScroeShareRequest = new OrderScroeShareRequest();
        BeanUtils.copyProperties(orderScroeShareReq, orderScroeShareRequest);
        orderScroeShareRequest.setChannelCode(channelCode);
        orderScroeShareRequest.setVersion("10");
        orderScroeShareRequest.setSfCardNo(orderScroeShareReq.getFfpCardNo());
        orderScroeShareRequest.setFFPId(orderScroeShareReq.getFfpId());
        HttpResult result = this.doPostClient(orderScroeShareRequest, url, headMap, handConfig.getReadTimeout(), handConfig.getConnectTimeout());
        if (result.isResult()) {
            if (!StringUtil.isNullOrEmpty(result.getResponse())) {
                ScroeShareResp response = (ScroeShareResp) JsonUtil.jsonToBean(result.getResponse(), ScroeShareResp.class);
                if (UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(response.getResultCode())) {
                    baseResp.setObjData(response);
                    baseResp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                    baseResp.setResultCode(WSEnum.SUCCESS.getResultCode());
                    return baseResp;
                } else {
                    baseResp.setResultInfo(response.getErrorInfo());
                    baseResp.setResultCode(WSEnum.ERROR.getResultCode());
                    return baseResp;
                }
            }
        }
        baseResp.setResultInfo(WSEnum.ERROR.getResultInfo());
        baseResp.setResultCode(WSEnum.ERROR.getResultCode());
        return baseResp;
    }


    /**
     * 积分后置控制开关接口
     *
     * @return
     */
    @InterfaceLog
    @RequestMapping(value = "/ScoreControlSwitch", method = RequestMethod.POST)
    public BaseResp ScoreControlSwitch(@RequestBody OrderScroeShareReq orderScroeShareReq, HttpServletRequest request) {
        String realChannelCode = request.getHeader(HandlerConstants.TOKEN_CHANNELCODE);
        BaseResp baseResp = new BaseResp();
        OrderScroeShareRequest orderScroeShareRequest = new OrderScroeShareRequest();
        BeanUtils.copyProperties(orderScroeShareReq, orderScroeShareRequest);
        if (StringUtil.isNullOrEmpty(orderScroeShareRequest.getChannelCode())) {
            orderScroeShareRequest.setChannelCode(realChannelCode == null ? "B2C" : realChannelCode);
        }
        orderScroeShareRequest.setVersion("10");
        ScoreControlSwitchResp scoreControlSwitchResp = scoreControlService.scoreControlSwitch(orderScroeShareRequest);
        if (scoreControlSwitchResp != null) {
            baseResp.setObjData(scoreControlSwitchResp);
            baseResp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
            baseResp.setResultCode(WSEnum.SUCCESS.getResultCode());
        } else {
            baseResp.setResultInfo(WSEnum.ERROR.getResultInfo());
            baseResp.setResultCode(WSEnum.ERROR.getResultCode());
        }
        return baseResp;
    }

    /**
     * 银联支付接入 云闪付 小米支付 华为支付 APPLE PAY
     *
     * @return
     */
    @InterfaceLog
    @ApiOperation(value = "银联支付", notes = "银联支付（云闪付、小米支付、华为支付、APPLE PAY）")
    @RequestMapping(value = "/chinaPay", method = RequestMethod.POST)
    public BaseResp chinaPay(@RequestBody @Validated WxPaymentReq req, BindingResult bindingResult, HttpServletRequest request) {
        BaseResp resp = new BaseResp();
        String reqId = MdcUtils.getRequestId();
        String ip = this.getClientIP(request);
        if (bindingResult.hasErrors()) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
            return resp;
        }
        String payChannelCode = req.getChannelCode();//默认的支付渠道即为访问渠道
        String returnUrl = "";
        if (!StringUtil.isNullOrEmpty(req.getPayChannel())) {//主要处理M网站的支付问题
            payChannelCode = req.getPayChannel();
        }
        PayMethodEnum payMethod = PayMethodEnum.checkType(req.getPayMethod());
        if (payMethod == null) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setResultInfo("不支持的支付方式");
            return resp;
        }
        boolean flag = this.checkKeyInfo(req.getFfpId(), req.getLoginKeyInfo(), req.getChannelCode());
        if (!flag) {
            resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return resp;
        }
        //加载支付配置
        String payType = req.getPayType();
        if (StringUtil.isNullOrEmpty(payType)) {
            payType = req.getOrderType();
        }
        OrderPayEnumType orderPayEnumType = OrderPayEnumType.checkEnum(payType);
        //品牌餐食重新设置订单描述信息
        if (StringUtils.isNotBlank(req.getChannelPriInfo()) && req.getChannelPriInfo().indexOf("BrandMeals") > -1) {
            orderPayEnumType = OrderPayEnumType.checkEnum("BrandMeals");
        }
        String subject = orderPayEnumType.getSubject();
        String body = orderPayEnumType.getBody();
        if (StringUtils.isNotBlank(req.getProductDesc())) {
            subject = req.getProductDesc();
            body = req.getProductDesc();
        }
        PtPaymentWXReq paymentReq = new PtPaymentWXReq(HandlerConstants.VERSION, HandlerConstants.CURRENCY_CODE, returnUrl,
                HandlerConstants.BACK_RETURN_URL_PAY, "", subject, body, "", "Web");
        String postUrl = HandlerConstants.URL_PAY;
        BeanUtils.copyProperties(req, paymentReq);
        paymentReq.setChannelNo(req.getChannelCode());
        String billCreateIP = this.getClientIP(request);
        Bank bank = initPay(payChannelCode, payType, payMethod.value, req.getMerchantPayment());
        if (bank == null) {
            logger.info(LOG_RESP_SIX, payChannelCode + "_" + payType + "_" + payMethod.value);
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(LOG_RESP_FOUR);
            return resp;
        }
        String gatewayNo = bank.getGateway();//网关号
        paymentReq.setGatewayNo(gatewayNo);
        paymentReq.setGatewayType(bank.getGatewayType());
        //动态参数设置
        Map<String, String> dynamicParameterMap = new HashMap<>();
        dynamicParameterMap.put(VALUE_SIX, billCreateIP);
        String dynamicParameters = JsonUtil.objectToJson(dynamicParameterMap);
        paymentReq.setDynamicParameters(dynamicParameters);
        Map<String, String> parametersMap = paymentReq.getPayPara();
        String sha1Content = paymentReq.getPayParaSHA1Str();
        //19支付平台渠道用户号
        String payChannel = bank.getPayChannel();
        sha1Content = sha1Content + payChannel;
        parametersMap.put(VAULE_ONE, payChannel);
        //密钥
        String key = bank.getKey();
        String chkValue = sha1Encode(sha1Content + key);
        parametersMap.put(VALUE_TWO, chkValue);
        paymentReq.setChkValue(chkValue);
        HttpResult result = doPayPost(postUrl, parametersMap);
        //2021-02-07 金卡及以上会员不调用同盾风控
        PtCRMResponse<PtMemberDetail> ptCRMResponse = memberCacheService.queryMemberDetailInfo(request, req.getFfpId(), req.getFfpCardNo(), req.getChannelCode());
        int levelCode = 0;
        if (ptCRMResponse != null && ptCRMResponse.getCode() == 0) {
            levelCode = NumberUtils.toInt(ptCRMResponse.getData().getStateInfo().getMemberLevelCode());
        }
        if (result.isResult()) {
            String paymentInfo = result.getResponse();
            logger.info("请求号:{}，IP地址:{}，支付平台结果：{}", reqId, ip, paymentInfo);
            if (!StringUtil.isNullOrEmpty(paymentInfo) && paymentInfo.indexOf("Signature") > -1 && paymentInfo.indexOf("MerOrderNo") > -1) {
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                this.putSendCouponRedisIfNecessary(req.getOrderType(), req.getOrderNo(), req.getFfpCardNo());
                resp.setObjData(paymentInfo);
                //同盾校验
                //5.3版本使用同盾
                //支付目前只调用同盾记录，不做判断
                if ("Y".equals(handConfig.getUseTongDun()) && levelCode < NumberUtils.toInt(MemberLevelEnum.Golden.getLevelCode())) {
                    if (req.getPayMethod().equals(PayMethodEnum.CHINAPAY.value)) {
                        transferTD(req, request, resp.getResultCode(), PayMethodTypeEnum.YUNSHANFU.getName());
                    } else {
                        transferTD(req, request, resp.getResultCode(), PayMethodTypeEnum.QITA.getName());
                    }
                } else {
                    log.debug("请求未进行同盾验证，渠道订单号：{}，会员号：{}", req.getChannelOrderNo(), req.getFfpCardNo());
                }
                return resp;
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("支付发生错误！");
                try {
                    PaymentResp paymentResp = (PaymentResp) JsonUtil.jsonToBean(paymentInfo, PaymentResp.class);
                    if (!UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(paymentResp.getResultCode())) {
                        resp.setResultInfo(paymentResp.getErrorMsg());
                        resp.setErrorInfo(paymentResp.getErrorMsg());
                    }
                    if (UnifiedOrderResultEnum.ERROR_PNR.getResultCode().equals(paymentResp.getRespCode())) {
                        resp.setResultCode(WSEnum.TOAST_ERROR.getResultCode());
                        resp.setErrorInfo(LOG_RESP_PNR_ERROR);
                    }
                } catch (Exception e) {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo("支付发生错误！");
                }
                //同盾校验
                //5.3版本使用同盾
                //支付目前只调用同盾记录，不做判断
                if ("Y".equals(handConfig.getUseTongDun()) && levelCode < NumberUtils.toInt(MemberLevelEnum.Golden.getLevelCode())) {
                    if (req.getPayMethod().equals(PayMethodEnum.CHINAPAY.value)) {
                        transferTD(req, request, resp.getResultCode(), PayMethodTypeEnum.YUNSHANFU.getName());
                    } else {
                        transferTD(req, request, resp.getResultCode(), PayMethodTypeEnum.QITA.getName());
                    }
                } else {
                    log.debug("请求未进行同盾验证，渠道订单号：{}，会员号：{}", req.getChannelOrderNo(), req.getFfpCardNo());
                }
                return resp;
            }
        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("网络请求异常！");
            return resp;
        }
    }

    @ApiOperation(value = "微信公账号签名", notes = "微信公账号签名")
    @RequestMapping(value = "jsSdkSignature.do", method = RequestMethod.GET)
    public JsSDKConfig getConfig(HttpServletRequest request) {
        WechatAccessResult wechatAccessResult = messageClient.getWechatAccessResult(null, null);
        String ticket = null == wechatAccessResult ? null : wechatAccessResult.getJsapiTicket();
        if (StringUtils.isBlank(ticket)) {
            throw new CommonException(WSEnum.ERROR_CHK_ERROR.getResultCode(), "未获取到有效许可信息，请稍后再试");
        }
        String requestUrl = request.getParameter("reqUrl");
        String timestamp = String.valueOf(System.currentTimeMillis()).substring(0, 10);
        String noncestr = "howechat";
        String signature = getSignature(ticket, timestamp, noncestr, requestUrl);
        log.info("[jsSdkSignaturejsSdkSignature] ticket={}, timestamp={}, noncestr={}, requestUrl={}", ticket, timestamp, noncestr, requestUrl);
        JsSDKConfig jsSDKConfig = new JsSDKConfig();
        jsSDKConfig.setAppId(handConfig.getWxPublicAppId());
        jsSDKConfig.setNonceStr(noncestr);
        jsSDKConfig.setTimestamp(timestamp);
        jsSDKConfig.setSignature(signature);
        return jsSDKConfig;
    }

    //信息签名
    private static String getSignature(String ticket, String timestamp, String nonce, String url) {
        String str = "jsapi_ticket=" + ticket +
                "&noncestr=" + nonce +
                "&timestamp=" + timestamp +
                "&url=" + url;
        return EncoderHandler.encode("SHA1", str);
    }

    //创建支付请求参数
    private PtPaymentWXReq createPayReq(WxPaymentReq req, Bank bank, String returnUrl) {
        PtPaymentWXReq paymentReq = new PtPaymentWXReq(HandlerConstants.VERSION, HandlerConstants.CURRENCY_CODE, returnUrl,
                HandlerConstants.BACK_RETURN_URL_PAY, "", "订单", "机票订单", "", "Web");
        BeanUtils.copyProperties(req, paymentReq);
        paymentReq.setChannelNo(req.getChannelCode());
        paymentReq.setGatewayNo(bank.getGateway());//网关号
        paymentReq.setGatewayType(bank.getGatewayType());//网关类型
        return paymentReq;
    }

    private void transferTD(WxPaymentReq req, HttpServletRequest request, String resultCode, String payMethod) {
    }

    /**
     * 保存支付缓存
     * 用于
     *
     * @param orderType
     * @param orderNo
     * @param ffpCardNo
     */
    private void putSendCouponRedisIfNecessary(String orderType, String orderNo, String ffpCardNo) {
        if (OrderPayEnumType.I.getOrderType().equals(orderType) || OrderPayEnumType.D.getOrderType().equals(orderType)
                && StringUtils.isNotBlank(apiRedisService.getData(RedisKeyConfig.CREATE_TICKET_ORDER_CACHE + orderNo))) {
            apiRedisService.putData(RedisKeyConfig.genCheckSendCouponKey(orderNo, ffpCardNo),
                    orderNo + ffpCardNo, 2 * 60 * 60L);
        }
    }

}

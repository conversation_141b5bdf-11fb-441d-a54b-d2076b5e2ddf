package com.juneyaoair.mobile.handler.controller;

import com.alibaba.fastjson.JSONObject;
import com.geetest.geeguard.sdk.GeetestLib;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.google.gson.reflect.TypeToken;
import com.juneyaoair.appenum.ChannelCodeEnum;
import com.juneyaoair.appenum.SensitiveOperationEnum;
import com.juneyaoair.appenum.WSEnum;
import com.juneyaoair.appenum.av.FareTypeEnum;
import com.juneyaoair.appenum.av.FlightQueryTypeEnum;
import com.juneyaoair.appenum.flight.FlightDirection;
import com.juneyaoair.appenum.geetest.GeetestTypeEnum;
import com.juneyaoair.appenum.member.MemberDetailRequestItemsEnum;
import com.juneyaoair.appenum.order.UnifiedOrderResultEnum;
import com.juneyaoair.appenum.premium.PremiumEnum;
import com.juneyaoair.appenum.rightcoupon.VoucherTypesEnum;
import com.juneyaoair.baseclass.airwifi.FlightProduct;
import com.juneyaoair.baseclass.airwifi.request.AirWifiProductReq;
import com.juneyaoair.baseclass.airwifi.response.AirWifiProductResp;
import com.juneyaoair.baseclass.basicsys.response.AirPortInfoDto;
import com.juneyaoair.baseclass.basicsys.response.CityInfoDto;
import com.juneyaoair.baseclass.common.base.CheckDayLicense;
import com.juneyaoair.baseclass.common.base.DayLicenseEnum;
import com.juneyaoair.baseclass.common.base.ThemeCoupon;
import com.juneyaoair.baseclass.common.base.UserInfoMust;
import com.juneyaoair.baseclass.common.request.BaseReq;
import com.juneyaoair.baseclass.common.response.BaseResp;
import com.juneyaoair.baseclass.common.response.BasicBaseResp;
import com.juneyaoair.baseclass.coupon.request.v3.BuyCouponProductRequest;
import com.juneyaoair.baseclass.coupon.request.v3.ProductFlightInfo;
import com.juneyaoair.baseclass.coupon.request.v3.QueryCouponProductRequest;
import com.juneyaoair.baseclass.coupon.response.v3.BuyCouponProductResponse;
import com.juneyaoair.baseclass.coupon.response.v3.CommonCouponProductInfo;
import com.juneyaoair.baseclass.coupon.response.v3.NewChangeCouponProductResponse;
import com.juneyaoair.baseclass.member.request.AccountCheckParam;
import com.juneyaoair.baseclass.member.request.TicketInfoQueryRequest;
import com.juneyaoair.baseclass.member.response.TicketInfoQueryResponse;
import com.juneyaoair.baseclass.newcoupon.bean.SingleBookCondition;
import com.juneyaoair.baseclass.prepayment.common.*;
import com.juneyaoair.baseclass.prepayment.request.*;
import com.juneyaoair.baseclass.prepayment.response.*;
import com.juneyaoair.baseclass.request.av.AircraftModel;
import com.juneyaoair.baseclass.request.changfly.ChangflyDTo;
import com.juneyaoair.baseclass.request.changfly.ChangflyReq;
import com.juneyaoair.baseclass.request.changfly.ChangflyResp;
import com.juneyaoair.baseclass.request.flightDistance.Segment;
import com.juneyaoair.baseclass.request.geetest.Geetest;
import com.juneyaoair.baseclass.request.premium.PremiumFlightInfo;
import com.juneyaoair.baseclass.request.premium.PremiumInfoRequest;
import com.juneyaoair.baseclass.request.premium.basic.ActivityPremiumResp;
import com.juneyaoair.baseclass.request.prepaymentBaggage.QueryPrepaymentBaggageSegmentReq;
import com.juneyaoair.baseclass.response.changfly.ChangFeiCardBindingDTO;
import com.juneyaoair.baseclass.response.changfly.ChangflyrequestDTO;
import com.juneyaoair.baseclass.response.flightDistance.FlightDistanceDTO;
import com.juneyaoair.baseclass.response.payment.PaymentResp;
import com.juneyaoair.baseclass.response.premium.*;
import com.juneyaoair.baseclass.response.prepaymentBaggage.*;
import com.juneyaoair.baseclass.response.prepaymentBaggage.CouponInfo;
import com.juneyaoair.client.ClientInfo;
import com.juneyaoair.crm.IMemberService;
import com.juneyaoair.exception.CommonException;
import com.juneyaoair.exception.NetworkException;
import com.juneyaoair.exception.ServiceException;
import com.juneyaoair.mobile.SystemConstants;
import com.juneyaoair.mobile.aop.annotation.InterfaceLog;
import com.juneyaoair.mobile.comm.service.IRedisService;
import com.juneyaoair.mobile.constants.MobileCoreBeanNames;
import com.juneyaoair.mobile.handler.HandlerConstants;
import com.juneyaoair.mobile.handler.bean.invoice.CouponSourceEnum;
import com.juneyaoair.mobile.handler.comm.RedisKeyConfig;
import com.juneyaoair.mobile.handler.config.bean.HandConfig;
import com.juneyaoair.mobile.handler.controller.util.CRMReqUtil;
import com.juneyaoair.mobile.handler.controller.util.VirtualPaymentConvert;
import com.juneyaoair.mobile.handler.controller.v2.util.CommonUtil;
import com.juneyaoair.mobile.handler.controller.v2.util.RightCouponConvert;
import com.juneyaoair.mobile.handler.manage.OrderManage;
import com.juneyaoair.mobile.handler.manage.PdiManage;
import com.juneyaoair.mobile.handler.sdk.TravellerHttpApi;
import com.juneyaoair.mobile.handler.service.*;
import com.juneyaoair.mobile.handler.util.CertNoUtil;
import com.juneyaoair.mobile.handler.util.FlightUtil;
import com.juneyaoair.mobile.handler.util.GeetestUtil;
import com.juneyaoair.mobile.mongo.entity.ApiErrorLogs;
import com.juneyaoair.mobile.mongo.entity.FlightInfo;
import com.juneyaoair.mobile.mongo.service.apiErrorLogs.IApiErrorLogsService;
import com.juneyaoair.pattern.PatternCommon;
import com.juneyaoair.thirdentity.comm.request.PtApiCRMRequest;
import com.juneyaoair.thirdentity.comm.request.PtRequest;
import com.juneyaoair.thirdentity.comm.response.PtCRMResponse;
import com.juneyaoair.thirdentity.comm.response.PtResponse;
import com.juneyaoair.thirdentity.member.request.PtMemberDetailRequest;
import com.juneyaoair.thirdentity.member.response.PtMemberDetail;
import com.juneyaoair.thirdentity.pdi.OrderCusInfoVO;
import com.juneyaoair.thirdentity.pdi.PdiCreateOrder;
import com.juneyaoair.thirdentity.pdi.PdiProduct;
import com.juneyaoair.thirdentity.pdi.PdiResult;
import com.juneyaoair.thirdentity.request.speedRefund.TicketInfoRequest;
import com.juneyaoair.thirdentity.response.detr.IdentityInfo;
import com.juneyaoair.thirdentity.response.detr.PtIBETicketInfo;
import com.juneyaoair.thirdentity.response.detr.PtSegmentInfo;
import com.juneyaoair.thirdentity.response.speedRefund.TicketListInfoResponse;
import com.juneyaoair.thirdentity.salecoupon.request.*;
import com.juneyaoair.thirdentity.salecoupon.response.PtProductPrePayResponseDTO;
import com.juneyaoair.thirdentity.salecoupon.response.PtQueryResourceResponse;
import com.juneyaoair.thirdentity.salecoupon.response.ptChangFlyResponseDTO;
import com.juneyaoair.thirdentity.salecoupon.v2.common.*;
import com.juneyaoair.thirdentity.salecoupon.v2.request.ProductQueryRequestDto;
import com.juneyaoair.thirdentity.salecoupon.v2.request.PtBookProductInfo;
import com.juneyaoair.thirdentity.salecoupon.v2.request.PtCreateOrderRequest;
import com.juneyaoair.thirdentity.salecoupon.v2.response.*;
import com.juneyaoair.util.ValidatorUtils;
import com.juneyaoair.utils.EncoderHandler;
import com.juneyaoair.utils.StringUtil;
import com.juneyaoair.utils.http.HttpResult;
import com.juneyaoair.utils.http.HttpUtil;
import com.juneyaoair.utils.json.JsonUtil;
import com.juneyaoair.utils.util.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 新产品平台权益券查询产品下单接口
 * @Date 2020-12-17 10:18
 **/
@RequestMapping("/new/coupon")
@RestController
@Api(value = "NewCouponController", tags = {"新权益券服务"})
public class NewCouponController extends BassController {

    @Autowired
    private OrderManage orderManage;
    @Autowired
    private IBasicService basicService;
    @Autowired
    private IOrderService orderService;
    @Autowired
    private HandConfig handConfig;
    @Autowired
    private LocalCacheService localCacheService;
    @Autowired
    private PdiManage pdiManage;
    @Autowired
    private IMemberService memberService;
    @Autowired
    private IMemberPasswordService memberPasswordService;
    @Resource(name = "apiErrorLogsService")
    private IApiErrorLogsService apiErrorLogsServiceImpl;
    @Autowired
    private IExtraBaggageService iExtraBaggageService;
    @Autowired
    private GeetestService geetestService;
    @Autowired
    private TravellerHttpApi travellerHttpApi;
    @Resource(name = MobileCoreBeanNames.API_REDIS_SERVICE)
    private IRedisService apiRedisService;
    public static final String ERR_CONTENT_PREPAYMENT_BAGGAGE_CONNECTING_FLIGHT = "该行程为中转联程航班，暂不支持购买额外行李";
    public static final String ERR_CONTENT_PREPAYMENT_BAGGAGE_CHECKED_IN = "该行程已办理值机，若需购买额外行李请取消值机";
    public static final String ERR_CONTENT_PREPAYMENT_BAGGAGE_CODESHARES = "此行程为代码共享航班，请到实际承运方航司购买行李";
    public static final String ERR_CONTENT_PREPAYMENT_BAGGAGE_IS_INF = "婴儿旅客暂不支持购买额外行李";
    public static final String ERR_CONTENT_PREPAYMENT_BAGGAGE_NOT_018 = "代码共享航班请到柜台购买额外行李";

    @Autowired
    private CheckLicenseService checkLicenseService;

    @InterfaceLog
    @ApiOperation(value = "查询改期券产品", notes = "查询改期券产品")
    @RequestMapping(value = "queryChangeCouponProduct", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp queryChangeCouponProduct(@RequestBody BaseReq<QueryCouponProductRequest> req, HttpServletRequest request) {
        BaseResp resp = new BaseResp();
        String reqId = StringUtil.newGUID() + request.getRequestURI();
        String ip = this.getClientIP(request);
        try {
            this.checkRequest(req);
            QueryCouponProductRequest couponProductRequest = req.getRequest();
            String channelCode = req.getChannelCode();
            //微信渠道暂时无产品查询时，查询时变换为MOBILE
            if (ChannelCodeEnum.WEIXIN.getChannelCode().equals(req.getChannelCode())) {
                channelCode = ChannelCodeEnum.MOBILE.getChannelCode();
            }
            String userNo = this.getChannelInfo(channelCode, "10");
            if (!StringUtil.isNullOrEmpty(couponProductRequest.getFfpId())) {
                //验证用户查询是否正常
                boolean flag = this.checkKeyInfo(couponProductRequest.getFfpId(), couponProductRequest.getLoginKeyInfo(), req.getChannelCode());
                if (!flag) {
                    resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                    resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                    return resp;
                }
            }
            PtRequest ptRequest = createProductQuery(channelCode, userNo, couponProductRequest, VoucherTypesEnum.RESCHEDULE.getCode());
            //处理改期券前端响应对象
            handleRescheduleCouponResponse(ptRequest, resp, channelCode, ip);

        } catch (Exception e) {
            this.logError(resp, reqId, ip, req, e, "查询改期券产品出现异常");
        }
        return resp;
    }

    @InterfaceLog
    @ApiOperation(value = "查询升舱券产品", notes = "查询升舱券产品")
    @RequestMapping(value = "queryUpgradeCouponProduct", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp queryUpgradeCouponProduct(@RequestBody BaseReq<QueryCouponProductRequest> req, HttpServletRequest request) {
        BaseResp resp = new BaseResp();
        String reqId = StringUtil.newGUID() + request.getRequestURI();
        String ip = this.getClientIP(request);
        try {
            this.checkRequest(req);
            QueryCouponProductRequest couponProductRequest = req.getRequest();
            String channelCode = req.getChannelCode();
            //微信渠道暂时无产品查询时，查询时变换为MOBILE
            if (ChannelCodeEnum.WEIXIN.getChannelCode().equals(req.getChannelCode())) {
                channelCode = ChannelCodeEnum.MOBILE.getChannelCode();
            }
            String userNo = this.getChannelInfo(channelCode, "10");
            if (!StringUtil.isNullOrEmpty(couponProductRequest.getFfpId())) {
                //验证用户查询是否正常
                boolean flag = this.checkKeyInfo(couponProductRequest.getFfpId(), couponProductRequest.getLoginKeyInfo(), req.getChannelCode());
                if (!flag) {
                    resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                    resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                    return resp;
                }
            }
            PtRequest ptRequest = createProductQuery(channelCode, userNo, couponProductRequest, VoucherTypesEnum.UPGRADECOUPON.getCode());
            //处理升舱券前端响应对象
            handleUpgradeCouponResponse(ptRequest, resp, channelCode, ip);

        } catch (Exception e) {
            this.logError(resp, reqId, ip, req, e, "查询查询升舱券产品出现异常");
        }
        return resp;
    }

    @InterfaceLog
    @ApiOperation(value = "查询行李券产品", notes = "查询行李券产品")
    @RequestMapping(value = "queryBaggageCouponProduct", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp queryBaggageCouponProduct(@RequestBody BaseReq<QueryCouponProductRequest> req, HttpServletRequest request) {
        BaseResp resp = new BaseResp();
        String reqId = StringUtil.newGUID() + request.getRequestURI();
        String ip = this.getClientIP(request);
        try {
            this.checkRequest(req);
            QueryCouponProductRequest couponProductRequest = req.getRequest();
            String channelCode = req.getChannelCode();
            //微信渠道暂时无产品查询时，查询时变换为MOBILE
            if (ChannelCodeEnum.WEIXIN.getChannelCode().equals(req.getChannelCode())) {
                channelCode = ChannelCodeEnum.MOBILE.getChannelCode();
            }
            String userNo = this.getChannelInfo(channelCode, "10");
            if (!StringUtil.isNullOrEmpty(couponProductRequest.getFfpId())) {
                //验证用户查询是否正常
                boolean flag = this.checkKeyInfo(couponProductRequest.getFfpId(), couponProductRequest.getLoginKeyInfo(), req.getChannelCode());
                if (!flag) {
                    resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                    resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                    return resp;
                }
            }
            PtRequest ptRequest = createProductQuery(channelCode, userNo, couponProductRequest, VoucherTypesEnum.BAGGAGECOUPON.getCode());
            //处理行李券前端响应对象
            handleBaggageCouponResponse(ptRequest, resp, channelCode, ip);

        } catch (Exception e) {
            this.logError(resp, reqId, ip, req, e, "查询行李券产品出现异常");
        }
        return resp;
    }

    @InterfaceLog
    @ApiOperation(value = "查询休息室产品", notes = "查询休息室产品")
    @RequestMapping(value = "queryLoungeCouponProduct", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp queryLoungeCouponProduct(@RequestBody BaseReq<QueryCouponProductRequest> req, HttpServletRequest request) {
        BaseResp resp = new BaseResp();
        String reqId = StringUtil.newGUID() + request.getRequestURI();
        String ip = this.getClientIP(request);
        try {
            this.checkRequest(req);
            QueryCouponProductRequest couponProductRequest = req.getRequest();
            String channelCode = req.getChannelCode();
            //微信渠道暂时无产品查询时，查询时变换为MOBILE
            if (ChannelCodeEnum.WEIXIN.getChannelCode().equals(req.getChannelCode())) {
                channelCode = ChannelCodeEnum.MOBILE.getChannelCode();
            }
            String userNo = this.getChannelInfo(channelCode, "10");
            if (!StringUtil.isNullOrEmpty(couponProductRequest.getFfpId())) {
                //验证用户查询是否正常
                boolean flag = this.checkKeyInfo(couponProductRequest.getFfpId(), couponProductRequest.getLoginKeyInfo(), req.getChannelCode());
                if (!flag) {
                    resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                    resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                    return resp;
                }
            }
            PtRequest ptRequest = createProductQuery(channelCode, userNo, couponProductRequest, VoucherTypesEnum.LOUNGECOUPON.getCode());
            //处理休息室前端响应对象
            handleLoungeCouponResponse(ptRequest, resp, channelCode, ip);

        } catch (Exception e) {
            this.logError(resp, reqId, ip, req, e, "查询休息室产品出现异常");
        }
        return resp;
    }

    @InterfaceLog
    @ApiOperation(value = "查询行程WIFI", notes = "查询行程WIFI")
    @RequestMapping(value = "/queryWifiProduct", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp queryWifiProduct(@RequestBody @Validated BaseReq<AirWifiProductReq> baseReq, BindingResult bindingResult, HttpServletRequest request) {
        String reqId = StringUtil.newGUID() + request.getRequestURI();
        String ip = this.getClientIP(request);
        String headChannelCode = request.getHeader(HEAD_CHANNEL_CODE);
        BaseResp resp = new BaseResp();
        String channelCode = baseReq.getChannelCode();
        this.checkRequest(baseReq, bindingResult);
        try {
            AirWifiProductReq airWifiProductReq = baseReq.getRequest();
            if (StringUtils.isAnyBlank(airWifiProductReq.getFlightNo())) {
                throw new IllegalArgumentException("服务升级中");
            }
            GeetestLib gtSdk = GeetestUtil.initGeetest(headChannelCode, GeetestTypeEnum.TICKET.getGeetestType());
            HashMap<String, String> param = new HashMap<>();
            param.put("user_id", ip); //网站用户id  设备号
            param.put("client_type", airWifiProductReq.getClient_type()); //web:电脑上的浏览器；h5:手机上的浏览器，包括移动应用内完全内置的web_view；native：通过原生SDK植入APP应用的方式
            param.put("ip_address", ip); //传输用户请求验证时所携带的IP
            Geetest geetest = new Geetest(airWifiProductReq.getGeetest_challenge(), airWifiProductReq.getGeetest_validate(), airWifiProductReq.getGeetest_seccode());
            geetestService.validateMd5(gtSdk, geetest, param);
            JSONObject tags = new JSONObject();
            tags.put("IP",ip);
            tags.put("FfpCardNo",airWifiProductReq.getFfpCardNo());
            tags.put("ChannelCode",headChannelCode);
            MetricLogUtil.saveMetricLog("行程WIFI-客票提取",tags,new BigDecimal(1));
            //查询行程信息
            TicketInfoQueryRequest bigDataReq = new TicketInfoQueryRequest();
            //判断输入的是票号或者证件号
            String certNo = "";
            if (airWifiProductReq.getTktNo().matches(PatternCommon.TICKET_NO)) {
                bigDataReq.setTicketNumber(airWifiProductReq.getTktNo().replace("-", ""));
                certNo = "";
            } else {
                bigDataReq.setTravellerNumber(airWifiProductReq.getTktNo());
                certNo = airWifiProductReq.getTktNo();
            }
            String name;
            if (StringUtil.isContainStr(airWifiProductReq.getLastName(), PatternCommon.CHINESE_STRING) || StringUtil.isContainStr(airWifiProductReq.getFirstName(), PatternCommon.CHINESE_STRING)) {
                name = airWifiProductReq.getLastName() + airWifiProductReq.getFirstName();
            } else {
                name = airWifiProductReq.getLastName() + "/" + airWifiProductReq.getFirstName();
            }
            bigDataReq.setTravellerName(name);
            bigDataReq.setFlightStartDate(DateUtils.getCurrentDateStr());
            //接口加密
            bigDataReq.setSignature(EncoderHandler.encodeByMD5(HandlerConstants.BIGDATA_CLIENTCODE + certNo + HandlerConstants.BIGDATA_CLIENT_PASSWORD));
            Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
            List<TicketInfoQueryResponse> TicketInfoQueryResponseList = travellerHttpApi.searchTravellerItineraryInfo(bigDataReq, headMap);
            if (CollectionUtils.isNotEmpty(TicketInfoQueryResponseList)) {
                //获取机场基础信信息
                List<TicketInfoQueryResponse> ticketInfoList = TicketInfoQueryResponseList.parallelStream()
                        //匹配姓名过滤
                        .filter(s -> (name.toUpperCase().equals(s.getTraveller_name()) || s.getTraveller_name().contains(name)))
                        //目前只支持789的航班
                        .filter(s -> {
                            if (StringUtils.isBlank(s.getAircraft_type())) {
                                FlightInfo flightInfo = new FlightInfo();
                                flightInfo.setArrAirport(s.getSegment_arrival_airportcode());
                                flightInfo.setDepAirport(s.getSegment_depa_airportcode());
                                flightInfo.setFlightDate(s.getSegment_depa_date());
                                flightInfo.setFlightNo(s.getSegment_car_airlinecode() + s.getSegment_car_flightnumber());
                                List<FlightInfo> flightInfoList = basicService.queryFlightInfo(flightInfo);
                                if (CollectionUtils.isEmpty(flightInfoList)) {
                                    return false;
                                } else {
                                    String aircraftType = flightInfoList.get(0).getPlanType();
                                    return "789".equals(aircraftType);
                                }
                            } else {
                                return s.getAircraft_type().contains("789");
                            }
                        })
                        //过滤改期queryWifiProduct
                        .filter(s -> "0".equalsIgnoreCase(s.getExchange_flag()))
                        //过滤废票
                        .filter(s -> !"VT".equalsIgnoreCase(s.getHdr_event()))
                        //过滤退票
                        .filter(s -> !"TRFD".equalsIgnoreCase(s.getHdr_event()))
                        //过滤航班日期为null
                        .filter(s -> !StringUtil.isNullOrEmpty(s.getSegment_depa_date()))
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(ticketInfoList)) {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setErrorInfo("无满足条件的行程");
                    resp.setResultInfo("无满足条件的行程");
                    return resp;
                }
                //判断行程验证是否包含
                boolean check = ticketInfoList.stream().anyMatch(ptSegmentInfo -> {
                    if (airWifiProductReq.getFlightNo().equals(formatFlightNo(ptSegmentInfo))) {
                        return true;
                    } else {
                        return false;
                    }
                });
                if (!check) {
                    throw new ServiceException("无满足条件的行程");
                }
                //查询相关会员信息
                String[] items = {MemberDetailRequestItemsEnum.STATEINFO.eName};
                PtApiCRMRequest<PtMemberDetailRequest> ptApiCRMRequest = CRMReqUtil.buildMemberDetailReq(airWifiProductReq.getFfpCardNo(), airWifiProductReq.getFfpId(), request, channelCode, items);
                PtCRMResponse<PtMemberDetail> ptCRMResponse = memberService.memberDetail(ptApiCRMRequest);
                if (ptCRMResponse.getCode() != 0) {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setErrorInfo(ptCRMResponse.getMsg());
                    resp.setResultInfo(ptCRMResponse.getMsg());
                    return resp;
                }
                String levelCode = ptCRMResponse.getData().getStateInfo().getMemberLevelCode();
                //空地互联相关产品
                PdiResult<String> tokenResult = pdiManage.getToken();
                if (tokenResult.isSuccess()) {
                    PdiResult<List<PdiProduct>> productPdiResult = pdiManage.getProducts(tokenResult.getData(), "GROUND");
                    if (!productPdiResult.isSuccess()) {
                        resp.setResultCode(WSEnum.ERROR.getResultCode());
                        resp.setErrorInfo(tokenResult.getErrMessage());
                        resp.setResultInfo(tokenResult.getErrMessage());
                        return resp;
                    }
                    //相关产品封装  根据根据航线类别，会员等级，可用舱位筛选对应产品
                    List<FlightProduct> finalFlightProductCollectList = formatFlightProduct(levelCode, ticketInfoList, productPdiResult.getData());
                    //封装最后的返回结果
                    AirWifiProductResp airWifiProductResp = new AirWifiProductResp();
                    if (CollectionUtils.isNotEmpty(finalFlightProductCollectList)) {
                        //航班排序
                        finalFlightProductCollectList.sort(Comparator.comparing(FlightProduct::getFlightDate).thenComparing(FlightProduct::getDepTime));
                        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                        resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                        airWifiProductResp.setFlightProductList(finalFlightProductCollectList);
                    } else {
                        resp.setResultCode(WSEnum.NO_DATA.getResultCode());
                        resp.setResultInfo(WSEnum.NO_DATA.getResultInfo());
                        airWifiProductResp.setFlightProductList(new ArrayList<>());
                    }
                    resp.setObjData(airWifiProductResp);
                    return resp;
                } else {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setErrorInfo(tokenResult.getErrMessage());
                    resp.setResultInfo(tokenResult.getErrMessage());
                    return resp;
                }
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("未查询到行程信息");
                return resp;
            }
        } catch (IllegalArgumentException illegalArgumentException) {
            throw illegalArgumentException;
        } catch (Exception e) {
            this.logError(resp, reqId, ip, baseReq, e, "查询WIFI产品出现异常");
        }
        return resp;
    }

    //航班号处理
    private String formatFlightNo(TicketInfoQueryResponse ptSegmentInfo) {
        if (StringUtils.isNoneBlank(ptSegmentInfo.getSegment_opt_airlinecode(), ptSegmentInfo.getSegment_opt_flightnumber())) {
            return ptSegmentInfo.getSegment_opt_airlinecode() + ptSegmentInfo.getSegment_opt_flightnumber();
        } else {
            return ptSegmentInfo.getSegment_car_airlinecode() + ptSegmentInfo.getSegment_car_flightnumber();
        }
    }

    /**
     * 根据条件筛选wifi产品
     *
     * @param levelCode
     * @param ticketInfoList
     * @param data
     * @return
     */
    private List<FlightProduct> formatFlightProduct(String levelCode, List<TicketInfoQueryResponse> ticketInfoList, List<PdiProduct> data) {
        if (CollectionUtils.isEmpty(data)) {
            return null;
        }
        List<FlightProduct> flightProductList = ticketInfoList.parallelStream().map(ticketInfoQueryResponse -> {
            //查询WiFi产品信息
            FlightProduct flightProduct = new FlightProduct();
            flightProduct.setFlightNo(ticketInfoQueryResponse.getSegment_opt_airlinecode() + ticketInfoQueryResponse.getSegment_opt_flightnumber());
            flightProduct.setFlightDate(ticketInfoQueryResponse.getSegment_depa_date());
            flightProduct.setTktNo(ticketInfoQueryResponse.getTicket_ticketnumber());
            flightProduct.setCertNo(ticketInfoQueryResponse.getTraveller_number());
            flightProduct.setCabin(ticketInfoQueryResponse.getSegment_clazz());
            flightProduct.setDepTime(ticketInfoQueryResponse.getSegment_depa_time());
            flightProduct.setArrTime(ticketInfoQueryResponse.getSegment_arrival_time());
            flightProduct.setDepAirportCode(ticketInfoQueryResponse.getSegment_depa_airportcode());
            flightProduct.setDepTerminal(ticketInfoQueryResponse.getSegment_depa_terminal());
            flightProduct.setArrAirportCode(ticketInfoQueryResponse.getSegment_arrival_airportcode());
            flightProduct.setArrTerminal(ticketInfoQueryResponse.getSegment_arr_terminal());
            flightProduct.setTravellerName(ticketInfoQueryResponse.getTraveller_name());
            AirPortInfoDto depAirport = localCacheService.getLocalAirport(ticketInfoQueryResponse.getSegment_depa_airportcode());
            AirPortInfoDto arrAirport = localCacheService.getLocalAirport(ticketInfoQueryResponse.getSegment_arrival_airportcode());
            //判断是否是公务舱
            if ("J".equals(CommonUtil.getCabinClassByCabinCode(ticketInfoQueryResponse.getSegment_clazz(), handConfig.getCabinClass()))) {
                flightProduct.setJCabinFlag(true);
                flightProduct.setTips("尊敬的旅客，吉祥航空已为您开通全航程机上Wi-Fi服务，无需兑换即可使用");
            }
            //行程数据ticket_tickettype会存在数据为空的情况
            if (StringUtils.isBlank(ticketInfoQueryResponse.getTicket_tickettype())) {
                if (HandlerConstants.TRIP_TYPE_I.equals(depAirport.getIsInternational()) || HandlerConstants.TRIP_TYPE_I.equals(arrAirport.getIsInternational())) {
                    ticketInfoQueryResponse.setTicket_tickettype(HandlerConstants.TRIP_TYPE_I);
                } else {
                    ticketInfoQueryResponse.setTicket_tickettype(HandlerConstants.TRIP_TYPE_D);
                }
            }

            //注意机场信息为空的情况
            flightProduct.setDepAirportName(depAirport != null ? depAirport.getAirPortName() : ticketInfoQueryResponse.getSegment_depa_airportcode());
            flightProduct.setDepCityCode(depAirport != null ? depAirport.getCityCode() : "");
            flightProduct.setDepCityName(depAirport != null ? depAirport.getCityName() : ticketInfoQueryResponse.getSegment_depa_airportcode());
            flightProduct.setArrAirportName(arrAirport != null ? arrAirport.getAirPortName() : ticketInfoQueryResponse.getSegment_arrival_airportcode());
            flightProduct.setArrCityCode(arrAirport != null ? arrAirport.getCityCode() : "");
            flightProduct.setArrCityName(arrAirport != null ? arrAirport.getCityName() : ticketInfoQueryResponse.getSegment_arrival_airportcode());
            String flightDepDateTime = ticketInfoQueryResponse.getSegment_depa_date() + " " + ticketInfoQueryResponse.getSegment_depa_time();
            String flightArrDateTime = ticketInfoQueryResponse.getSegment_arrival_date() + " " + ticketInfoQueryResponse.getSegment_arrival_time();
            //处理夏令时
            if (depAirport != null && arrAirport != null) {
                String depCityZone = FlightUtil.convertSummerOrWinterTime(depAirport.getCityTimeZone(), flightDepDateTime, depAirport);
                String arrCityZone = FlightUtil.convertSummerOrWinterTime(arrAirport.getCityTimeZone(), flightArrDateTime, arrAirport);
                //计算飞行时长
                flightProduct.setDuration(DateUtils.calDuration(flightDepDateTime, depCityZone, flightArrDateTime, arrCityZone));
            }

            //  国内、日韩及港澳台 按国内算，50积分；
            //  除以上按国际算，150积分
            com.juneyaoair.baseclass.response.booking.Segment segmentInfo = localCacheService.getSegment(
                    depAirport != null ? depAirport.getCityCode() : "",
                    arrAirport != null ? arrAirport.getCityCode() : "");
            // 国际
            boolean isInternational = HandlerConstants.TRIP_TYPE_I.equals(ticketInfoQueryResponse.getTicket_tickettype());
            // 港澳台
            boolean isHKMCTW = HandlerConstants.TRIP_TYPE_R.equals(segmentInfo.getSegmentType());
            // 日韩
            boolean isJPKR = isJPKRSegment(
                    depAirport != null ? depAirport.getCityCode() : "",
                    arrAirport != null ? arrAirport.getCityCode() : "");

            //wifi产品筛选
            List<PdiProduct> pdiProductList = data.stream()
                    //过滤舱位
                    .filter(pdiProduct -> {
                        if (pdiProduct.getCompartmentS() == null) {
                            return true;
                        }
                        return Arrays.asList(pdiProduct.getCompartmentS()).contains(mapCabinClass(ticketInfoQueryResponse.getSegment_clazz()));
                    })
                    //过滤会员等级
                    .filter(pdiProduct -> {
                        if (pdiProduct.getCrmLevelCodeS() == null) {
                            return true;
                        }
                        return Arrays.asList(pdiProduct.getCrmLevelCodeS()).contains("L" + levelCode);
                    })
                    //过滤积分售价
                    .filter(pdiProduct -> StringUtils.isNotBlank(pdiProduct.getIntegral()) && Double.parseDouble(pdiProduct.getIntegral()) > 0)
                    //过滤航线性质
                    .filter(pdiProduct -> {
                        if (pdiProduct.getInterFlagS() == null) {
                            return true;
                        }
                        // 产品包含国内
                        if (Arrays.asList(pdiProduct.getInterFlagS()).contains("D")) {
                            return !isInternational || isJPKR || isHKMCTW;
                        }
                        // 产品包含国际
                        if (Arrays.asList(pdiProduct.getInterFlagS()).contains("I")) {
                            return isInternational && !isJPKR && !isHKMCTW;
                        }
                        return false;
                    })
                    .collect(Collectors.toList());
            List<CommonCouponProductInfo> allCommonCouponProductInfoList = pdiProductList.stream().map(pdiProduct -> {
                CommonCouponProductInfo commonCouponProductInfo = new CommonCouponProductInfo();
                commonCouponProductInfo.setProductNum(pdiProduct.getProductId());
                commonCouponProductInfo.setProductId(pdiProduct.getProductId());
                commonCouponProductInfo.setBookingId(pdiProduct.getProductId());
                commonCouponProductInfo.setRuleId(pdiProduct.getProductId());
                commonCouponProductInfo.setProductType(VoucherTypesEnum.ONBOARDWIFI.getCode());
                commonCouponProductInfo.setProductName(pdiProduct.getProductName());
                commonCouponProductInfo.setSalePrice(Double.valueOf(pdiProduct.getIntegral()));
                commonCouponProductInfo.setStandardPrice(Double.valueOf(pdiProduct.getProductPrice()));
                commonCouponProductInfo.setProductSku(pdiProduct.getProductId());
                commonCouponProductInfo.setSaleTotalCount(0);
                commonCouponProductInfo.setResourceId(pdiProduct.getProductId());
                commonCouponProductInfo.setDescription(pdiProduct.getMediaTypeNameS());
                commonCouponProductInfo.setSign(createProSign(commonCouponProductInfo));
                return commonCouponProductInfo;
            }).collect(Collectors.toList());
            flightProduct.setCommonCouponProductInfoList(allCommonCouponProductInfoList);
            //创建签名
            flightProduct.setFlightSign(createFlightSign(flightProduct));
            return flightProduct;
        }).collect(Collectors.toList());
        //只保留有产品的航班
        flightProductList = flightProductList.stream().filter(f -> CollectionUtils.isNotEmpty(f.getCommonCouponProductInfoList())).collect(Collectors.toList());
        return flightProductList;
    }


    /**
     * 机上WIFI 是否为日韩航线
     *
     * @param cityArray
     * @return
     */
    public boolean isJPKRSegment(String... cityArray) {
        com.juneyaoair.baseclass.response.booking.Segment segment = new com.juneyaoair.baseclass.response.booking.Segment();
        if (ArrayUtils.isEmpty(cityArray)) {
            return false;
        }
        Set<String> cityCodeSet = Sets.newHashSet();
        for (String cityCode : cityArray) {
            if (StringUtils.isNotBlank(cityCode)) {
                cityCodeSet.add(cityCode);
            }
        }
        segment.setCityCodeSet(cityCodeSet);
        for (String cityCode : cityCodeSet) {
            CityInfoDto cityInfo = localCacheService.getLocalCity(cityCode);
            if (null == cityInfo) {
                log.error("getSegment未获取到：{}城市信息", cityCode);
                throw new CommonException(WSEnum.ERROR_QUERY_ERROR.getResultCode(), "未获取到城市信息");
            }
            String countryCode = cityInfo.getCountryCode();
            if ("JP".equals(countryCode) || "KR".equals(countryCode)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 舱位等级转换
     *
     * @param cabinCode
     * @return
     */
    private String mapCabinClass(String cabinCode) {
        String cabinClass = CommonUtil.getCabinClassByCabinCode(cabinCode, handConfig.getCabinClass());
        if ("J".equals(cabinClass)) {
            return "GW";
        } else {
            return "JJ";
        }
    }

    @ApiOperation(value = "优享服务查询", notes = "优享服务查询")
    @RequestMapping(value = "/queryPremiumProduct", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @InterfaceLog
    public BaseResp queryPremiumProduct(@RequestBody BaseReq<PremiumInfoRequest> req, HttpServletRequest request) {
        BaseResp resp = new BaseResp();
        String reqId = StringUtil.newGUID() + request.getRequestURI();
        String ip = this.getClientIP(request);
        String H_CHANNELCODE = request.getHeader("channelCode");
        try {
            //1.参数校验
            this.checkRequest(req);
            PremiumInfoRequest premiumInfoRequest = req.getRequest();
            String channelCode = req.getChannelCode();
            //微信渠道暂时无产品查询时，查询时变换为MOBILE
            if (ChannelCodeEnum.WEIXIN.getChannelCode().equals(req.getChannelCode())) {
                channelCode = ChannelCodeEnum.MOBILE.getChannelCode();
            }
            String userNo = this.getChannelInfo(channelCode, "10");
            if (!StringUtil.isNullOrEmpty(premiumInfoRequest.getFfpId())) {
                //验证用户查询是否正常
                boolean flag = this.checkKeyInfo(premiumInfoRequest.getFfpId(), premiumInfoRequest.getLoginKeyInfo(), req.getChannelCode());
                if (!flag) {
                    resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                    resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                    return resp;
                }
            }

            List<PremiumFlightInfo> flightInfoList = premiumInfoRequest.getFlightInfoList();
            boolean    iSoSA= flightInfoList.stream().anyMatch(
                    (flight->("OSA".equals(flight.getArrCityCode())&&!"UKB".equals(flight.getArrAirport()))
                            ||("OSA".equals(flight.getDepCityCode())
                            &&!"UKB".equals(flight.getDepAirport()))));
            if (!premiumInfoRequest.getFareType().equals(FareTypeEnum.SIMPLE.getFare())&&!iSoSA) {
                //依旧返回成功,前端处理
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setResultInfo("不支持联程");
                resp.setObjData(null);
                return resp;
            }

            // 不支持,机票候补
            if (SystemConstants.WAIT.equals(premiumInfoRequest.getFlightFareType())
                    || FlightQueryTypeEnum.FREE_TICKET.getType().equals(premiumInfoRequest.getFlightFareType())
                    || FlightQueryTypeEnum.BUSINESS_FLY_FREE_TICKET.getType().equals(premiumInfoRequest.getFlightFareType())) {
                //依旧返回成功,前端处理
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setResultInfo("不支持机票候补");
                resp.setObjData(null);
                return resp;
            }

            // 不支持东航
            if (CollectionUtils.isNotEmpty(premiumInfoRequest.getFlightInfoList())) {
                Boolean isNotHo = premiumInfoRequest.getFlightInfoList().stream()
                        .anyMatch(premiumFlightInfo -> !premiumFlightInfo.getCarrierNo().startsWith("HO"));
                if (isNotHo) {
                    //依旧返回成功,前端处理
                    resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                    resp.setResultInfo("不支持非吉祥承运");
                    resp.setObjData(null);
                    return resp;
                }

            }
            //2.参数封装
            PremiumRespV2 premiumResp = new PremiumRespV2();
            PremiumResp premiumResp1 = new PremiumResp();

            List<PremiumProductResponse> PremiumProductList = new ArrayList<>();
            PremiumFlightInfo premiumFlightInfoGo = null;
            PremiumFlightInfo premiumFlightInfoBack = null;
            //版本大于等于6.8
            Boolean isNewVersion = VersionNoUtil.toVerInt(req.getClientVersion()) >= 6080000;
            //往返
            if ("RT".equals(premiumInfoRequest.getRouteType())) {
                //--获取去程
                premiumFlightInfoGo = flightInfoList.stream()
                        .filter(o -> o.getFlightDirection().equals(FlightDirection.GO.getCode()))
                        .findFirst()
                        .orElse(null);
                if (premiumFlightInfoGo == null) {
                    resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                    resp.setResultInfo("去程信息不能为空，请补充");
                    return resp;
                }
                //--获取返程
                premiumFlightInfoBack = flightInfoList.stream()
                        .filter(o -> o.getFlightDirection().equals(FlightDirection.BACK.getCode()))
                        .findFirst()
                        .orElse(null);
                if (premiumFlightInfoBack == null) {
                    resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                    resp.setResultInfo("返程信息不能为空，请补充");
                    return resp;
                }
            } else {
                //单程
                if (StringUtil.isNullOrEmpty(flightInfoList)) {
                    resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                    resp.setResultInfo("航班航线列表不能为空，请补充");
                    return resp;
                }
            }

            //**查询远程请求,获取优享服务信息**
            BasicBaseResp<List<ActivityPremiumResp>> basicBaseResp = basicService.queryPremiumByChannelCode(channelCode, ip, premiumInfoRequest.getFfpId(), premiumInfoRequest.getFfpCardNo());
            if (basicBaseResp == null) {
                resp.setResultCode(WSEnum.NETWORK_ERROR.getResultCode());
                resp.setResultInfo("优享服务,远程接口查询错误");
                return resp;
            }
            List<ActivityPremiumResp> activityPremiumRespList = basicBaseResp.getResult();
            if (StringUtil.isNullOrEmpty(activityPremiumRespList)) {
                resp.setResultCode(WSEnum.NO_DATA.getResultCode());
                resp.setResultInfo("优享服务,无数据");
                return resp;
            }
            //日本接送机预约服务中转航线特殊处理
            if (CollectionUtils.isNotEmpty(activityPremiumRespList)) {
                if (premiumInfoRequest.getFareType().equals(FareTypeEnum.ADDON.getFare())&&iSoSA){
                    activityPremiumRespList= activityPremiumRespList.stream().filter(activityPremium->VoucherTypesEnum.INNN.getCode().equals(activityPremium.getTicketNumber())).collect(Collectors.toList());
                }
            }

            Map<String, ActivityPremiumResp> activityPremiumRespMap = activityPremiumRespList.stream()
                    .collect(Collectors.toMap(ActivityPremiumResp::getTicketNumber, o -> o));

            Map<String, CityInfoDto> cityInfoMap = basicService.queryAllCityMap(channelCode, ip);

            //1.处理改期券
            ActivityPremiumResp rescheduleActivityPremiumResp = activityPremiumRespMap.get(VoucherTypesEnum.RESCHEDULE.getCode());
            if (rescheduleActivityPremiumResp != null) {
                //查询所有产品
                List<Product> productList = queryProduct(premiumInfoRequest, rescheduleActivityPremiumResp, request, channelCode, userNo);
                if (!StringUtil.isNullOrEmpty(productList)) {
                    //***过滤数据***
                    productList = verifyProductList(productList, premiumInfoRequest, cityInfoMap);
                    if (CollectionUtils.isNotEmpty(productList)) {
                        productList = productList.stream().filter(product -> {
                            //航班起飞几小时前可用 非空 默认为0
                            if (StringUtils.isBlank(product.getAdvanceHour())) {
                                return false;
                            }
                            //6.8 以前过滤掉早鸟改期券
                            if (Integer.valueOf(product.getAdvanceHour()) > 0 && VersionNoUtil.toVerInt(req.getClientVersion()) < 6080000 && !ChannelCodeEnum.MWEB.getChannelCode().equals(H_CHANNELCODE)) {
                                return false;
                            }
                            if (isNewVersion || ChannelCodeEnum.MWEB.getChannelCode().equals(H_CHANNELCODE)) {
                                return !flightInfoList.stream().allMatch(flightInfo -> {
                                    return DateUtils.differHours(DateUtils.getStringDate(), flightInfo.getDepDateTime()) < Integer.parseInt(product.getAdvanceHour());
                                });
                            }
                            //当该航班距起飞不足24小时，【早鸟改期券】不可购买
                            return true;
                        }).collect(Collectors.toList());


                        PremiumProductResponse premiumProductResponse = getPremiumProductResponse(rescheduleActivityPremiumResp, productList);
                        premiumProductResponse.setProductType(VoucherTypesEnum.RESCHEDULE.getCode());
                        //设置改期券
                        PremiumProductList.add(premiumProductResponse);
                        //6.8 以前数据结构
                        premiumResp1.setRescheduleCoupon(premiumProductResponse);
                    }
                }
            }

            //2.处理升舱券
            ActivityPremiumResp upgradeActivityPremiumResp = activityPremiumRespMap.get(VoucherTypesEnum.UPGRADECOUPON.getCode());
            if (upgradeActivityPremiumResp != null) {
                //查询所有产品
                List<Product> productList = queryProduct(premiumInfoRequest, upgradeActivityPremiumResp, request, channelCode, userNo);
                if (CollectionUtils.isNotEmpty(productList)) {
                    List<Product> productListUse = verifyProductList(productList, premiumInfoRequest, cityInfoMap);
                    if (CollectionUtils.isNotEmpty(productListUse)) {
                        PremiumProductResponse premiumProductResponse = getPremiumProductResponse(upgradeActivityPremiumResp, productListUse);
                        //设置升舱
                        premiumProductResponse.setProductType(VoucherTypesEnum.UPGRADECOUPON.getCode());
                        PremiumProductList.add(premiumProductResponse);
                        //6.8 以前数据结构
                        premiumResp1.setUpgradeCoupon(premiumProductResponse);
                    }

                }
            }

            //3.处理行李券
            ActivityPremiumResp baggageActivityPremiumResp = activityPremiumRespMap.get(VoucherTypesEnum.BAGGAGECOUPON.getCode());
            if (baggageActivityPremiumResp != null) {
                //查询所有产品
                List<Product> productList = queryProduct(premiumInfoRequest, baggageActivityPremiumResp, request, channelCode, userNo);
                if (CollectionUtils.isNotEmpty(productList)) {
                    //***过滤数据***
                    productList = verifyProductList(productList, premiumInfoRequest, cityInfoMap);
                    if (CollectionUtils.isNotEmpty(productList)) {
                        PremiumProductResponse premiumProductResponse = getPremiumProductResponse(baggageActivityPremiumResp, productList);
                        premiumProductResponse.setProductType(VoucherTypesEnum.BAGGAGECOUPON.getCode());
                        //设置行李券
                        PremiumProductList.add(premiumProductResponse);
                        //6.8 以前数据结构
                        premiumResp1.setBaggageCoupon(premiumProductResponse);
                    }
                }
            }

            //4.处理休息室
            ActivityPremiumResp loungeActivityPremiumResp = activityPremiumRespMap.get(VoucherTypesEnum.LOUNGECOUPON.getCode());
            if (loungeActivityPremiumResp != null) {
                //查询所有产品
                List<Product> productList = queryProduct(premiumInfoRequest, loungeActivityPremiumResp, request, channelCode, userNo);
                if (CollectionUtils.isNotEmpty(productList)) {
                    //***过滤数据***
                    productList = verifyProductList(productList, premiumInfoRequest, cityInfoMap);
                    if (CollectionUtils.isNotEmpty(productList)) {
                        PremiumProductResponse premiumProductResponse = getPremiumProductResponse(loungeActivityPremiumResp, productList);
                        premiumProductResponse.setProductType(VoucherTypesEnum.LOUNGECOUPON.getCode());
                        //设置休息室
                        PremiumProductList.add(premiumProductResponse);
                        //6.8 以前数据结构
                        premiumResp1.setLoungeCoupon(premiumProductResponse);
                    }
                }
            }

            //5.迪士尼券
            ActivityPremiumResp disneyTicketPremiumResp = activityPremiumRespMap.get(VoucherTypesEnum.DISNEY.getCode());
            boolean    disneyVersion =VersionNoUtil.toVerInt(req.getClientVersion()) >= 76000;
            boolean     isDisney    = premiumInfoRequest.getIsDisney() != null && premiumInfoRequest.getIsDisney();
            if (disneyTicketPremiumResp != null&&!isDisney&disneyVersion) {
                //查询所有产品
                List<Product> productList = productQueryDisneyTicket(premiumInfoRequest, disneyTicketPremiumResp, request, channelCode);
                    if (CollectionUtils.isNotEmpty(productList)) {
                        PremiumProductResponse premiumProductResponse = getPremiumProductResponse(disneyTicketPremiumResp, productList);
                        premiumProductResponse.setProductType(VoucherTypesEnum.DISNEY.getCode());
                        //设置休息室
                        PremiumProductList.add(premiumProductResponse);
                        //6.8 以前数据结构
                        premiumResp1.setLoungeCoupon(premiumProductResponse);
                    }
            }

            //6.大阪拼车接送服务抵扣券
            ActivityPremiumResp INNNPremiumResp = activityPremiumRespMap.get(VoucherTypesEnum.INNN.getCode());
            Boolean    INNNVersion =VersionNoUtil.toVerInt(req.getClientVersion()) >= 79000;
            if (INNNPremiumResp != null&&INNNVersion&&iSoSA) {
                //查询所有产品
                List<Product> productList = productQueryDisneyTicket(premiumInfoRequest, INNNPremiumResp, request, channelCode);
                if (CollectionUtils.isNotEmpty(productList)) {
                    PremiumProductResponse premiumProductResponse = getPremiumProductResponse(INNNPremiumResp, productList);
                    premiumProductResponse.setProductType(VoucherTypesEnum.INNN.getCode());
                    PremiumProductList.add(premiumProductResponse);
                    //6.8 以前数据结构
                    premiumResp1.setLoungeCoupon(premiumProductResponse);
                }
            }

            if (isNewVersion || ChannelCodeEnum.MWEB.getChannelCode().equals(H_CHANNELCODE)) {
                //排序
                List<PremiumProductResponse> premiumProductResponses = PremiumProductList.stream()
                        .sorted(Comparator.comparing(PremiumProductResponse::getSerialNumber).reversed()).collect(Collectors.toList());
                premiumResp.setPremiumProductList(premiumProductResponses);
            }
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
            resp.setObjData(isNewVersion || ChannelCodeEnum.MWEB.getChannelCode().equals(H_CHANNELCODE) ? premiumResp : premiumResp1);
        } catch (Exception e) {
            this.logError(resp, reqId, ip, req, e, "查询休息室产品出现异常");
        }
        return resp;
    }

    @InterfaceLog
    @ApiOperation(value = "预付费行李-查询航段信息", notes = "根据票号,或证件号查询航段")
    @RequestMapping(value = "/queryPrepaymentBaggageSegment", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp<List<QueryPrepaymentBaggageSegmentResp>> queryPrepaymentBaggageSegment(@RequestBody @Validated BaseReq<QueryPrepaymentBaggageSegmentReq> req, BindingResult bindingResult, HttpServletRequest request) {
        BaseResp<List<QueryPrepaymentBaggageSegmentResp>> resp = new BaseResp();
        String ip = this.getClientIP(request);
        QueryPrepaymentBaggageSegmentReq segmentReq = req.getRequest();
        String reqId = MdcUtils.getRequestId();
        String headChannelCode = request.getHeader(HEAD_CHANNEL_CODE);
        String ticketNo = null;
        List<String> certNOs = null;
        if (bindingResult.hasErrors()) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
            return resp;
        }
        try {
            if (Strings.isNullOrEmpty(segmentReq.getFlightNo())
                    || Strings.isNullOrEmpty(segmentReq.getName())) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo(WSEnum.ERROR_REQUEST_PARAMS.getResultInfo());
                return resp;
            }
            if (StringUtils.isNotBlank(segmentReq.getFfpId())) {
                //验证用户查询是否正常
                boolean flag = this.checkKeyInfo(segmentReq.getFfpId(), segmentReq.getLoginKeyInfo(), req.getChannelCode());
                if (!flag) {
                    resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                    resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                    return resp;
                }
            }
            //
            if (this.isIdCard(segmentReq.getTicketNo())) {
                certNOs = Collections.singletonList(segmentReq.getTicketNo());
            } else {
                ticketNo = segmentReq.getTicketNo();
            }
            // 若为票号,检验是否018开头，不支持非018开头的票号
            if (StringUtils.isNotBlank(ticketNo) && !ticketNo.startsWith("018")) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo(NewCouponController.ERR_CONTENT_PREPAYMENT_BAGGAGE_NOT_018);
                return resp;
            }
            //极验验证操作
            GeetestLib gtSdk = GeetestUtil.initGeetest(headChannelCode, GeetestTypeEnum.TICKET.getGeetestType());
            HashMap<String, String> param = new HashMap<>();
            param.put("user_id", ip); //网站用户id  设备号
            param.put("client_type", segmentReq.getClient_type()); //web:电脑上的浏览器；h5:手机上的浏览器，包括移动应用内完全内置的web_view；native：通过原生SDK植入APP应用的方式
            param.put("ip_address", ip); //传输用户请求验证时所携带的IP
            Geetest geetest = new Geetest(segmentReq.getGeetest_challenge(), segmentReq.getGeetest_validate(), segmentReq.getGeetest_seccode());
            geetestService.validateMd5(gtSdk, geetest, param);
            JSONObject tags = new JSONObject();
            tags.put("IP",ip);
            tags.put("FfpCardNo",segmentReq.getFfpCardNo());
            tags.put("ChannelCode",headChannelCode);
            MetricLogUtil.saveMetricLog("预付费行李-客票提取",tags,new BigDecimal(1));
            // 添加IP和FFP的访问频率限制
            CheckDayLicense ipCheckDayLicense = new CheckDayLicense(ip, DayLicenseEnum.QUERY_PREPAYMENT_BAGGAGE_IP, "预付费行李查询达到上限");
            CheckDayLicense ffpCheckDayLicense = new CheckDayLicense(segmentReq.getFfpCardNo(), DayLicenseEnum.QUERY_PREPAYMENT_BAGGAGE_FFP, "预付费行李查询达到上限");
            checkLicenseService.checkDayLicense(true, ipCheckDayLicense, ffpCheckDayLicense);
            // 1、查询大数据接口
            List<TicketInfoQueryResponse> tickInfo = iExtraBaggageService.queryFlightSegment(request, req.getRequest().getFfpCardNo(), req.getRequest().getFfpId(), certNOs, ticketNo);

            // 根据请求信息过滤客票信息 且排除中转联程航班
            tickInfo = filterTicketInfoByReq(segmentReq, tickInfo);
            //
            boolean isPetCbbg = tickInfo.stream().anyMatch(tick ->
                    StringUtils.isNotBlank(tick.getFare_basis_code()) &&
                            (tick.getFare_basis_code().endsWith("PET") || tick.getFare_basis_code().endsWith("CBBG")));

            tickInfo = tickInfo.stream().filter(tick ->
                            StringUtils.isNotBlank(tick.getFare_basis_code()) &&
                                    !(tick.getFare_basis_code().endsWith("PET") || tick.getFare_basis_code().endsWith("CBBG")))
                    .collect(Collectors.toList());

            // 2、无数据查询查询 IBE接口
            if (tickInfo.isEmpty() && !isPetCbbg) {
                // 2.1 通过处理IBE数据返回
                return this.returnByQueryIBE(req, segmentReq, ip, resp);
            } else {
                // 3 、正常处理大数据行程接口返回
                this.handleRespByItineraryData(req, resp, segmentReq, tickInfo);
            }
            if (tickInfo.isEmpty() && isPetCbbg) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("不支持此类型（宠物票/占座行李票）客票购买行李");
                return resp;
            }

            //  额外行李购买证件信息脱敏
            this.prepaymentBaggageCertNoDesensitization(resp);

        } catch (CommonException e) {
            // 直接返回CommonException中的错误信息
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(e.getMessage());
            return resp;
        } catch (Exception e) {
            this.logError(resp, reqId, ip, req, e, "查询预付费行李航段信息异常");
        }
        // 4、正常处理大数据行程接口返回
        return resp;
    }

    private void prepaymentBaggageCertNoDesensitization(BaseResp<List<QueryPrepaymentBaggageSegmentResp>> resp) {
        resp.getObjData().forEach(queryPrepaymentBaggageSegmentResp -> {
            // 页面证件信息
            String certNo = queryPrepaymentBaggageSegmentResp.getBaggagePassengerInfo().getIdNo();
            String desensitizedCertNo = SensitiveInfoHider.hideMiddleSensitiveInfo(certNo);
            queryPrepaymentBaggageSegmentResp.getBaggagePassengerInfo().setIdNo(desensitizedCertNo);
            String tktNo = queryPrepaymentBaggageSegmentResp.getTicketNumber();
            apiRedisService.putData(RedisKeyConfig.createUpIdInfo(tktNo), certNo, 60 * 10L);

            // BaggageSegmentInfoList仅在由数据接口返回时，包含证件信息，也隐藏
            queryPrepaymentBaggageSegmentResp.getBaggageSegmentInfoList().forEach(v -> {
                if (certNo.equals(v.getTravellerNumber())) {
                    v.setTravellerNumber(desensitizedCertNo);
                }
            });
        });
    }

    /**
     * 处理，查询大数据行程接口,与用户输入信息校验匹配后 过滤的行程数据
     *
     * @param req
     * @param resp
     * @param segmentReq
     * @param tickInfo
     */
    private void handleRespByItineraryData(BaseReq<QueryPrepaymentBaggageSegmentReq> req, BaseResp<List<QueryPrepaymentBaggageSegmentResp>> resp, QueryPrepaymentBaggageSegmentReq segmentReq, List<TicketInfoQueryResponse> tickInfo) {
        List<QueryPrepaymentBaggageSegmentResp> resData;
        resData = iExtraBaggageService.convertToQueryPrepaymentBaggageSegmentResp(tickInfo, req, false);
        //
        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
        resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
        resp.setObjData(resData);
    }


    /**
     * 原IBE提票接口
     *
     * @param req
     * @param segmentReq
     * @param ip
     * @param resp
     * @return
     */
    public BaseResp<List<QueryPrepaymentBaggageSegmentResp>> returnByQueryIBE(BaseReq<QueryPrepaymentBaggageSegmentReq> req, QueryPrepaymentBaggageSegmentReq segmentReq, String ip, BaseResp<List<QueryPrepaymentBaggageSegmentResp>> resp) {
        //查询远程接口
        TicketListInfoResponse ticketListInfoResponse = queryTicket(req.getChannelCode(), segmentReq.getTicketNo(),segmentReq.getName(), ip);
        if (!UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(ticketListInfoResponse.getResultCode())) {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(ticketListInfoResponse.getErrorInfo());
            return resp;
        }
        if (CollectionUtils.isEmpty(ticketListInfoResponse.getIBETicketInfoList())) {
            resp.setResultCode(WSEnum.CANNOT_QUERY_USE_TICKET.getResultCode());
            resp.setResultInfo(WSEnum.CANNOT_QUERY_USE_TICKET.getResultInfo());
            return resp;
        }
        List<PtIBETicketInfo> ptIBETicketInfoList = ticketListInfoResponse.getIBETicketInfoList();
        List<PtIBETicketInfo> filterList = new ArrayList<>();
        // 航班号、航班日期、出发城市、到达城市、姓名 过滤
        for (PtIBETicketInfo ptIBETicketInfo : ptIBETicketInfoList) {
            String passName = segmentReq.getName().toUpperCase();
            String patternStr = passName + "(\\s*(CHD)?\\s*(\\(CHILD\\))?\\s*|\\s*(MR)?\\s*|\\s*(MS)?\\s*|\\s*(\\(UM[0-9]+)\\)?\\s*)";//正则表达式
            Pattern pattern = Pattern.compile(patternStr);
            Matcher matcher = pattern.matcher(ptIBETicketInfo.getPassengerName());
            boolean isMatch = ptIBETicketInfo.getSegmentInfoList().stream().anyMatch(ptSegmentInfo -> {
                boolean bNum = segmentReq.getFlightNo().equals(ptSegmentInfo.getFlightNo());
                boolean bName = matcher.matches();
                return bNum && bName;
            });
            if (isMatch) {
                filterList.add(ptIBETicketInfo);
            }
        }

        //客票状态过滤
        if (filterList.size() == 1 && filterList.get(0).getSegmentInfoList().size() == 1) {
            PtSegmentInfo ptSegmentInfo = filterList.get(0).getSegmentInfoList().get(0);
            // OperationAirline为空，则承运值为MarketingAirline
            boolean isHoOperationAirLine = ("HO".equalsIgnoreCase(ptSegmentInfo.getOperationAirline())
                    || (StringUtils.isEmpty(ptSegmentInfo.getOperationAirline())) && "HO".equalsIgnoreCase(ptSegmentInfo.getMarketingAirline()));
            if (!isHoOperationAirLine) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo(ERR_CONTENT_PREPAYMENT_BAGGAGE_CODESHARES);
                return resp;
            }
            if (HandlerConstants.CHECKED_IN.equals(ptSegmentInfo.getTicketStatus())) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("该行程已办理值机，若需购买额外行李请取消值机");
                return resp;
            }
            if (HandlerConstants.REFUNDED.equals(ptSegmentInfo.getTicketStatus())) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("您的客票已退票，无法订购");
                return resp;
            }
            if (!HandlerConstants.OPEN_FOR_USE.equals(ptSegmentInfo.getTicketStatus())) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("客票状态有误，无法订购");
                return resp;
            }
        }
        //过滤非OPEN FOR　USE 和 CHECKED IN(需显示不可购买)
        filterList.forEach(ptIBETicketInfo -> ptIBETicketInfo.getSegmentInfoList().removeIf(ptSegmentInfo ->
                !(HandlerConstants.OPEN_FOR_USE.equals(ptSegmentInfo.getTicketStatus()) || HandlerConstants.CHECKED_IN.equals(ptSegmentInfo.getTicketStatus()))));
        //过滤掉航段为空的
        filterList = filterList.stream()
                .filter(ticketInfo -> !StringUtil.isNullOrEmpty(ticketInfo.getSegmentInfoList()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(filterList)) {
            //如果没有能够购买的,则提示错误信息
            resp.setResultCode(WSEnum.CANNOT_QUERY_USE_TICKET.getResultCode());
            resp.setResultInfo(WSEnum.CANNOT_QUERY_USE_TICKET.getResultInfo());
            return resp;
        }
        //构建行李需要的航段信息
        List<QueryPrepaymentBaggageSegmentResp> respList = this.buildPrepaymentBaggageSegment(filterList);
        String userNo = getChannelInfo(req.getChannelCode(), "10");
        //处理没有产品的（处理了起飞时间不可购买）
        this.checkCanPayBaggage(ip, req.getChannelCode(), userNo, respList, segmentReq);
        //
        respList = this.handleSegErrDescAndEnableEnter(respList);
        //
        if (StringUtil.isNullOrEmpty(respList)) {
            //如果没有能够购买的,则提示错误信息
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("查询的客票暂不支持购买额外行李，请重新查询！");
            return resp;
        }

        // 设置使用新的预付费产品接口
        respList.forEach(segmentResp -> segmentResp.setUseNewBaggageProduct(true));

        // 航线日期排序
        respList = respList.stream().sorted(Comparator.comparing(
                v -> DateUtils.toDate(v.getBaggageSegmentInfoList().get(0).getFlightDate()).getTime())
        ).collect(Collectors.toList());

        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
        resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
        resp.setObjData(respList);

        //  额外行李购买证件信息脱敏
        this.prepaymentBaggageCertNoDesensitization(resp);

        return resp;
    }

    /**
     * 证件号/票号 查询结果匹配:航班号、航班日期、出发城市、到达城市、姓名、
     * <p>
     * 根据请求信息过滤客票信息
     *
     * @param segmentReq
     * @param tickInfo
     * @return
     */
    private List<TicketInfoQueryResponse> filterTicketInfoByReq(QueryPrepaymentBaggageSegmentReq segmentReq, List<TicketInfoQueryResponse> tickInfo) {
        String passName = segmentReq.getName().toUpperCase();
        // tips 无陪儿童 e.g.: "戴一一 (UM6)" "周渝楷 (UM9)"
        String patternStr = passName + "(\\s*(CHD)?\\s*(\\(CHILD\\))?\\s*|\\s*(MR)?\\s*|\\s*(MS)?\\s*|\\s*(\\(UM[0-9]+)\\)?\\s*)";//正则表达式
        Pattern pattern = Pattern.compile(patternStr);
        
        // 先按照查询条件过滤出匹配的航段
        List<TicketInfoQueryResponse> matchedTickets = tickInfo.stream().filter(ticketInfoQueryResponse -> {
            //
            boolean bNum = segmentReq.getFlightNo().equals(ticketInfoQueryResponse.getSegment_opt_airlinecode() + ticketInfoQueryResponse.getSegment_opt_flightnumber());

            //
            Matcher matcher = pattern.matcher(ticketInfoQueryResponse.getTraveller_name());
            boolean bName = matcher.matches();

            return bNum && bName;
        }).collect(Collectors.toList());

        // 如果找到匹配的航段,检查是否存在中转联程
        if (!matchedTickets.isEmpty()) {
            TicketInfoQueryResponse matchedSegment = matchedTickets.get(0);
            String ticketNumber = matchedSegment.getTicket_ticketnumber();
            
            // 获取同票号的所有航段
            List<TicketInfoQueryResponse> sameTicketSegments = tickInfo.stream()
                .filter(t -> t.getTicket_ticketnumber().equals(ticketNumber))
                .collect(Collectors.toList());

            // 如果存在多个航段,判断是否为中转联程航班
            if (sameTicketSegments.size() > 1 && checkConnectingFlight(sameTicketSegments)) {
                throw new CommonException(WSEnum.ERROR.getResultCode(), ERR_CONTENT_PREPAYMENT_BAGGAGE_CONNECTING_FLIGHT);
            }
            
            return matchedTickets;
        }
        
        return new ArrayList<>();
    }

    /**
     * 抽取JudgeExtraProReqBo
     *
     * @param ffpId
     * @param ffpCardNo
     * @param channelCode
     * @return
     */
    private BaseReq<UserInfoMust> extractJudgeExtraProReq(String ffpId, String ffpCardNo, String channelCode) {
        BaseReq<UserInfoMust> reqBo = new BaseReq<>();
        UserInfoMust infoMust = new UserInfoMust();
        infoMust.setFfpId(ffpId);
        infoMust.setFfpCardNo(ffpCardNo);
        reqBo.setChannelCode(channelCode);
        reqBo.setRequest(infoMust);
        return reqBo;
    }

    // 判断是否是证件号
    private boolean isIdCard(String str) {
        // 身份证
        if (str.length() == 18) {
            return true;
        }
        // 护照
        if (str.length() == 9) {
            return true;
        }
        return false;
    }


    /**
     * 行李航段查询 - 处理可否进入购买页面及原因、
     * 为处理起飞时间xx不可购买
     * 提取子层不能购买原因
     * 有可购买的航段即可选择进入购买页面
     *
     * @param respList
     * @return
     * @see QueryPrepaymentBaggageSegmentResp#canPurchase
     * @see QueryPrepaymentBaggageSegmentResp#segErrorDesc
     * @see BaggageSegmentInfo#canPurchase
     * @see BaggageSegmentInfo#errorDesc
     */
    private List<QueryPrepaymentBaggageSegmentResp> handleSegErrDescAndEnableEnter(List<QueryPrepaymentBaggageSegmentResp> respList) {
        // 提取子层不能购买原因
        for (QueryPrepaymentBaggageSegmentResp resp : respList) {
            List<BaggageSegmentInfo> baggageSegmentInfoList = resp.getBaggageSegmentInfoList();
            Boolean canPurchase = baggageSegmentInfoList.stream().anyMatch(BaggageSegmentInfo::getCanPurchase);
            String segErrorDesc = baggageSegmentInfoList.stream().filter(v -> !Strings.isNullOrEmpty(v.getErrorDesc())).findFirst().map(BaggageSegmentInfo::getErrorDesc).orElse(null);
            resp.setCanPurchase(canPurchase);
            resp.setSegErrorDesc(segErrorDesc);
        }
        // 有可购买的航段即可选择进入购买页面,
        for (QueryPrepaymentBaggageSegmentResp segmentResp : respList) {
            segmentResp.setCanPurchase(segmentResp.getBaggageSegmentInfoList().stream().anyMatch(BaggageSegmentInfo::getCanPurchase));
        }
        return respList;
    }


    @ApiOperation(value = "预付费行李-购买说明须知文案", notes = "查询文案内容")
    @RequestMapping(value = "/queryPrepaymentBaggageDocument", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @InterfaceLog
    public BaseResp<PrepaymentBaggageDocumentResp> queryPrepaymentBaggageDocument(@RequestBody BaseReq req, HttpServletRequest request) {
        BaseResp<PrepaymentBaggageDocumentResp> resp = new BaseResp<>();
        String reqId = StringUtil.newGUID() + request.getRequestURI();
        String ip = this.getClientIP(request);
        try {
            String userNo = getChannelInfo(req.getChannelCode(), "10");
            PtQueryResourceReq ptQueryResourceReq = new PtQueryResourceReq();
            ptQueryResourceReq.setChannelCode(req.getChannelCode());
            ptQueryResourceReq.setUserNo(userNo);
            ptQueryResourceReq.setRequest(CouponSourceEnum.EXTRABAGGAGE.getCode());
            HttpResult result = HttpUtil.doPostClient(ptQueryResourceReq, HandlerConstants.URL_COUPON_API + HandlerConstants.PRODUCT_QUERY_RESOURCE);
            if (result == null || StringUtil.isNullOrEmpty(result.getResponse())) {
                resp.setResultCode(WSEnum.ERROR_QUERY_ERROR.getResultCode());
                resp.setResultInfo("预付费行李文案查询错误");
                return resp;
            }
            PtQueryResourceResponse ptSearchResponse = (PtQueryResourceResponse) JsonUtil.jsonToBean(result.getResponse(), PtQueryResourceResponse.class);
            PrepaymentBaggageDocumentResp prepaymentBaggageDocumentResp = new PrepaymentBaggageDocumentResp();
            if ("10001".equals(ptSearchResponse.getResultCode())) {
                ResourceBase resourceBase = ptSearchResponse.getResult();
                prepaymentBaggageDocumentResp.setPurchaseNotes(resourceBase.getPurchaseNotes());
                prepaymentBaggageDocumentResp.setUseMode(resourceBase.getUseMode());
                prepaymentBaggageDocumentResp.setStandard(resourceBase.getResourceDesc());
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                resp.setObjData(prepaymentBaggageDocumentResp);
            } else {
                log.info("预付费行李文案查询失败,响应内容:", result.getResponse());
                resp.setResultCode(WSEnum.ERROR_QUERY_ERROR.getResultCode());
                resp.setResultInfo("预付费行李文案查询失败");
                return resp;
            }
        } catch (Exception e) {
            this.logError(resp, reqId, ip, req, e, "查询预付费行李文案内容异常");
        }
        return resp;
    }

    @ApiOperation(value = "预付费行李-退款", notes = "预付费行李-退款")
    @RequestMapping(value = "/refundPrepaymentBaggage", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @InterfaceLog
    public BaseResp refundPrepaymentBaggage(@RequestBody BaseReq<RefundPrepaymentBaggageDTO> req, HttpServletRequest request) {
        BaseResp<BaggageOrderInfoResp> resp = new BaseResp<>();
        String ip = this.getClientIP(request);
        RefundPrepaymentBaggageDTO refundDTO = req.getRequest();
        String reqId = StringUtil.newGUID() + request.getRequestURI();
        try {
            this.checkRequest(req);
            if (!StringUtil.isNullOrEmpty(refundDTO.getFfpId())) {
                //验证用户查询是否正常
                boolean flag = this.checkKeyInfo(refundDTO.getFfpId(), refundDTO.getLoginKeyInfo(), req.getChannelCode());
                if (!flag) {
                    resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                    resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                    return resp;
                }
            }

            //1.先查询预付费行李
            String userNo = getChannelInfo(req.getChannelCode(), "10");

            BaseRequestDTO<String> baseRequestDTO = new BaseRequestDTO<>();
            baseRequestDTO.setChannelCode(req.getChannelCode());
            baseRequestDTO.setFfpId(refundDTO.getFfpId());
            baseRequestDTO.setFfpCardNo(refundDTO.getFfpCardNo());
            baseRequestDTO.setUserNo(userNo);
            baseRequestDTO.setRequest(refundDTO.getOrderNo());

            Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
            String queryUrl = HandlerConstants.URL_COUPON_API + HandlerConstants.PRODUCT_PREPAYBAGGAGE_ORDERINFO;
            HttpResult httpResult = this.doPostClient(baseRequestDTO, queryUrl, headMap, handConfig.getReadTimeout(), handConfig.getConnectTimeout());

            QueryOrderInfoResponse orderInfoResponse;
            if (null != httpResult && httpResult.isResult()) {
                com.juneyaoair.baseclass.prepayment.common.BaseResultDTO<QueryOrderInfoResponse> baseResultDTO = (com.juneyaoair.baseclass.prepayment.common.BaseResultDTO<QueryOrderInfoResponse>) JsonUtil.jsonToBean(httpResult.getResponse(), new TypeToken<com.juneyaoair.baseclass.prepayment.common.BaseResultDTO<QueryOrderInfoResponse>>() {
                }.getType());
                if (!baseResultDTO.getResultCode().equals("10001")) {
                    throw new BaggageException(WSEnum.ERROR.getResultCode(), "查询订单错误");
                }
                orderInfoResponse = baseResultDTO.getResult();
            } else {
                throw new BaggageException(WSEnum.NO_DATA.getResultCode(), "远程服务异常");
            }
            //判断状态是否状态
            Map<String, Boolean> map = checkCanRefund(orderInfoResponse);

            RefundBaggageRequest refundBaggageRequest = new RefundBaggageRequest();
            refundBaggageRequest.setVersion("10");
            refundBaggageRequest.setChannelCode(req.getChannelCode());
            refundBaggageRequest.setUserNo(getChannelInfo(req.getChannelCode(), "10"));
            refundBaggageRequest.setRequestIp(ip);
            refundBaggageRequest.setOrderNo(refundDTO.getOrderNo());
            refundBaggageRequest.setChannelOrderNo(refundDTO.getChannelOrderNo());
            List<CouponOrderInfoDto> refundCouponInfoDtoList = refundDTO.getRefundCouponInfoDtoList();
            List<RefundCouponInfoDto> refundCouponInfoDtos = new ArrayList<>();
            for (CouponOrderInfoDto couponOrderInfoDto : refundCouponInfoDtoList) {
                //判断是否可以退款
                Boolean canRefund = map.get(couponOrderInfoDto.getEmdTktNo());
                if (canRefund == null || !canRefund) {
                    resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                    resp.setResultInfo("状态错误,不可以退款");
                    return resp;
                }

                RefundCouponInfoDto refundCouponInfoDto = new RefundCouponInfoDto();
                refundCouponInfoDto.setReason("APP退款");
                refundCouponInfoDto.setIsVoluntaryRefund("Y");
                BigDecimal couponSaleAmount = new BigDecimal(couponOrderInfoDto.getCouponSaleAmount());
                BigDecimal subtract = couponSaleAmount.subtract(new BigDecimal(couponOrderInfoDto.getFfpUseScore()));
                refundCouponInfoDto.setAmount(subtract);
                refundCouponInfoDto.setScore(Integer.valueOf(couponOrderInfoDto.getFfpUseScore()));
                refundCouponInfoDto.setCouponCode(couponOrderInfoDto.getCouponCode());
                refundCouponInfoDtos.add(refundCouponInfoDto);
            }
            refundBaggageRequest.setBasicRefundCouponInfoDto(refundCouponInfoDtos);
            refundBaggageRequest.setReason("APP退款");
            List<RefundCouponInfoDto> basicRefundCouponInfoDto = refundBaggageRequest.getBasicRefundCouponInfoDto();
            if (!StringUtil.isNullOrEmpty(basicRefundCouponInfoDto)) {
                for (RefundCouponInfoDto refundCouponInfoDto : basicRefundCouponInfoDto) {
                    refundCouponInfoDto.setReason("");
                    refundCouponInfoDto.setIsVoluntaryRefund("Y");
                }
            }

            String updateUrl = HandlerConstants.URL_FARE_OPEN_API + HandlerConstants.ORDER_REFUND_COUPONORDER;
            HttpResult updateHttpResult = this.doPostClient(refundBaggageRequest, updateUrl, headMap, handConfig.getReadTimeout(), handConfig.getConnectTimeout());
            if (null != updateHttpResult && updateHttpResult.isResult()) {
                CancelBaggageResp cancelBaggageResp = (CancelBaggageResp) JsonUtil.jsonToBean(updateHttpResult.getResponse(), new TypeToken<CancelBaggageResp>() {
                }.getType());
                if (!cancelBaggageResp.getResultCode().equals("1001")) {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo("退款错误:" + cancelBaggageResp.getErrorInfo());
                    return resp;
                }
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("错误:服务异常");
                return resp;
            }
        } catch (Exception e) {
            this.logError(resp, reqId, ip, req, e, "取消预付费行李异常");
        }
        return resp;
    }

    private Map<String, Boolean> checkCanRefund(QueryOrderInfoResponse orderInfoResponse) {
        Map<String, Boolean> map = new HashMap<>();
        for (CouponOrderInfoDto couponOrderInfoDto : orderInfoResponse.getCouponOrderInfoDtoList()) {
            //是否退款
            couponOrderInfoDto.setCanRefund(false);
            BaggageEmdTicketStateEnum baggageEmdTicketStateEnum = BaggageEmdTicketStateEnum.checkCode(couponOrderInfoDto.getEmdTicketState());
            if (couponOrderInfoDto.getCouponState().equals(PrepaymentBaggageStatusEnum.Not.getStatusCode())
                    && (baggageEmdTicketStateEnum.equals(BaggageEmdTicketStateEnum.EMPTY) || baggageEmdTicketStateEnum.equals(BaggageEmdTicketStateEnum.ISSUED))) {
                //设置可以退款
                couponOrderInfoDto.setCanRefund(true);
            }


            // 私域行李券
            // 是否使用了券
            // 非 Not\Active 表示使用了私域行李券。使其不可退
            boolean isUseCoupon = false;
            if (couponOrderInfoDto.getOrderCoupon() != null) {
                isUseCoupon = !("Not".equals(couponOrderInfoDto.getOrderCoupon().getCouponState())
                        || ("Active").equals(couponOrderInfoDto.getOrderCoupon().getCouponState()));
            }
            if (couponOrderInfoDto.getCanRefund() && isUseCoupon) {
                couponOrderInfoDto.setCanRefund(false);
            }

            map.put(couponOrderInfoDto.getEmdTktNo(), couponOrderInfoDto.getCanRefund());
        }
        return map;
    }


    /**
     * 获取客票信息
     *
     * @param channelCode
     * @param ticketNo
     * @return
     */
    private TicketListInfoResponse queryTicket(String channelCode, String ticketNo, String passName,String clientIp) {
        String userNo = getChannelInfo(channelCode, "10");
        TicketInfoRequest ticketInfoRequest = new TicketInfoRequest(HandlerConstants.VERSION, channelCode, userNo);
        String certType = CertNoUtil.getCertTypeByCertNo(ticketNo);
        String queryCertType = certType;
        if ("PP".equals(certType)) { //国内航班使用护照时通过NI类型查询
            queryCertType = "NI";
        }
        ticketInfoRequest.setCertType(queryCertType);
        if ("TN".equals(certType)) {
            ticketInfoRequest.setTicketNo(ticketNo);
            ticketInfoRequest.setPassengerName(passName);
        } else {
            ticketInfoRequest.setCertNo(ticketNo);
            throw new CommonException(WSEnum.NOT_SUPPORT.getResultCode(), WSEnum.NOT_SUPPORT.getResultInfo());
        }
        TicketListInfoResponse ticketListInfoResponse = new TicketListInfoResponse();
        String url = HandlerConstants.URL_FARE_API + HandlerConstants.QUERY_TICKET_INFO;
        Map<String, String> headMap = HttpUtil.getHeaderMap(clientIp, "");
        HttpResult httpResult = this.doPostClient(ticketInfoRequest, url, headMap, handConfig.getReadTimeout(), handConfig.getConnectTimeout());
        if (httpResult.isResult()) {
            if (StringUtil.isNullOrEmpty(httpResult.getResponse())) {
                ticketListInfoResponse.setErrorInfo("获取客票信息为空！");
                ticketListInfoResponse.setResultCode(UnifiedOrderResultEnum.FAIL.getResultCode());
            } else {
                try {
                    ticketListInfoResponse = (TicketListInfoResponse) JsonUtil.jsonToBean(httpResult.getResponse(), TicketListInfoResponse.class);
                    if (!UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(ticketListInfoResponse.getResultCode())
                            && "PP".equals(certType)) {
                        //护照失败的情况下再根据PP类型查询信息
                        queryCertType = "PP";
                        ticketInfoRequest.setCertType(queryCertType);
                        httpResult = this.doPostClient(ticketInfoRequest, url, headMap, handConfig.getReadTimeout(), handConfig.getConnectTimeout());
                        if (httpResult.isResult()) {
                            if (StringUtil.isNullOrEmpty(httpResult.getResponse())) {
                                ticketListInfoResponse = new TicketListInfoResponse();
                                ticketListInfoResponse.setErrorInfo("获取客票信息为空！");
                                ticketListInfoResponse.setResultCode(UnifiedOrderResultEnum.FAIL.getResultCode());
                            } else {
                                try {
                                    ticketListInfoResponse = (TicketListInfoResponse) JsonUtil.jsonToBean(httpResult.getResponse(), TicketListInfoResponse.class);
                                } catch (Exception e) {
                                    log.error("请求参数：{},请求路径：{}，返回结果：{}，错误信息：", JsonUtil.objectToJson(ticketInfoRequest), url, httpResult.getResponse(), e);
                                    ticketListInfoResponse.setErrorInfo("数据转换异常！");
                                    ticketListInfoResponse.setResultCode(UnifiedOrderResultEnum.FAIL.getResultCode());
                                }
                            }
                        } else {
                            ticketListInfoResponse.setResultCode(UnifiedOrderResultEnum.FAIL.getResultCode());
                            ticketListInfoResponse.setErrorInfo(httpResult.getResponse());
                        }
                    }
                } catch (Exception ex) {
                    log.error("请求参数：{},请求路径：{}，返回结果：{}，错误信息：", JsonUtil.objectToJson(ticketInfoRequest), url, httpResult.getResponse(), ex);
                    ticketListInfoResponse.setErrorInfo("数据转换异常！");
                    ticketListInfoResponse.setResultCode(UnifiedOrderResultEnum.FAIL.getResultCode());
                }
            }
        } else {
            ticketListInfoResponse.setResultCode(UnifiedOrderResultEnum.FAIL.getResultCode());
            ticketListInfoResponse.setErrorInfo(httpResult.getResponse());
        }
        return ticketListInfoResponse;
    }

    /**
     * 构建QueryPrepaymentBaggageSegmentResp内容
     *
     * @param segmentList
     * @return
     */
    private List<QueryPrepaymentBaggageSegmentResp> buildPrepaymentBaggageSegment(List<PtIBETicketInfo> segmentList) {
        List<QueryPrepaymentBaggageSegmentResp> respList = new ArrayList<>();
        for (PtIBETicketInfo ptIBETicketInfo : segmentList) {
            QueryPrepaymentBaggageSegmentResp segmentResp = new QueryPrepaymentBaggageSegmentResp();
            List<PtSegmentInfo> segmentInfoList = ptIBETicketInfo.getSegmentInfoList();
            //往返标识
            String routeType = HandlerConstants.ROUTE_TYPE_OW;
            if (ptIBETicketInfo.getOrgCity().equals(ptIBETicketInfo.getDstCity())) {
                routeType = HandlerConstants.ROUTE_TYPE_RT;
            }
            segmentResp.setBaggagePassengerInfo(copyBaggagePassengerInfo(ptIBETicketInfo));
            segmentResp.setRouteType(routeType);
            segmentResp.setInterFlag(ptIBETicketInfo.getInterFlag());
            segmentResp.setTicketNumber(ptIBETicketInfo.getTicketNo());
            //
            List<BaggageSegmentInfo> baggageSegmentInfoList = this.copyBaggageSegmentColumn(segmentInfoList, ptIBETicketInfo, segmentResp);
            //
            Boolean canPurchase = baggageSegmentInfoList.stream().anyMatch(BaggageSegmentInfo::getCanPurchase);
            String segErrorDesc = baggageSegmentInfoList.stream().filter(v -> !Strings.isNullOrEmpty(v.getErrorDesc())).findFirst().map(BaggageSegmentInfo::getErrorDesc).orElse(null);
            segmentResp.setCanPurchase(canPurchase);
            segmentResp.setSegErrorDesc(segErrorDesc);
            segmentResp.setBaggageSegmentInfoList(baggageSegmentInfoList);
            respList.add(segmentResp);
        }
        return respList;
    }

    /**
     * 处理乘机人信息
     *
     * @param ptIBETicketInfo
     * @return
     */
    private BaggagePassengerInfo copyBaggagePassengerInfo(PtIBETicketInfo ptIBETicketInfo) {
        BaggagePassengerInfo baggagePassengerInfo = new BaggagePassengerInfo();
        IdentityInfo identityInfo = null;
        for (IdentityInfo temp : ptIBETicketInfo.getIdentityInfoList()) {
            if ("NI".equals(temp.getIdType())) {
                identityInfo = temp;
                break;
            }
            if ("PP".equals(temp.getIdType())) {
                identityInfo = temp;
                break;
            }
            if ("ID".equals(temp.getIdType())) {
                identityInfo = temp;
                break;
            }
        }
        if (identityInfo == null) {
            identityInfo = ptIBETicketInfo.getIdentityInfoList().get(0);
        }
        String certType = CertNoUtil.getCertTypeByCertNo(identityInfo.getIdNo());
        identityInfo.setIdType(certType);

        baggagePassengerInfo.setIdNo(identityInfo.getIdNo());
        baggagePassengerInfo.setIdType(identityInfo.getIdType());
        baggagePassengerInfo.setPassengerName(ptIBETicketInfo.getPassengerName());
        return baggagePassengerInfo;
    }

    /**
     * 处理航段信息
     *
     * @param segmentInfoList
     * @param ptIBETicketInfo
     * @param segmentResp
     * @return
     */
    private List<BaggageSegmentInfo> copyBaggageSegmentColumn(List<PtSegmentInfo> segmentInfoList, PtIBETicketInfo ptIBETicketInfo, QueryPrepaymentBaggageSegmentResp segmentResp) {
        List<BaggageSegmentInfo> segmentRespList = new ArrayList<>();
        for (PtSegmentInfo segmentInfo : segmentInfoList) {
            BaggageSegmentInfo baggageSegmentInfo = new BaggageSegmentInfo();

            baggageSegmentInfo.setTicketNumber(ptIBETicketInfo.getTicketNo());

            Date depDate = DateUtils.toDate(segmentInfo.getDepTime(), DateUtils.YYYY_MM_DD_HH_MM_PATTERN);
            Date arrDate = DateUtils.toDate(segmentInfo.getArrTime(), DateUtils.YYYY_MM_DD_HH_MM_PATTERN);
            AirPortInfoDto depLocalAirport = localCacheService.getLocalAirport(segmentInfo.getDepAirportCode(), depDate);
            AirPortInfoDto arrLocalAirport = localCacheService.getLocalAirport(segmentInfo.getArrAirportCode(), depDate);

            baggageSegmentInfo.setDepAirportTerminal(segmentInfo.getDepAirportTerminal());
            baggageSegmentInfo.setArrAirportTerminal(segmentInfo.getArrAirportTerminal());

            baggageSegmentInfo.setDepCity(depLocalAirport.getCityCode());
            baggageSegmentInfo.setDepCityName(depLocalAirport.getCityName());
            baggageSegmentInfo.setDepAirport(depLocalAirport.getAirPortCode());
            baggageSegmentInfo.setDepAirportName(depLocalAirport.getAirPortName());

            String[] depDateStr = segmentInfo.getDepTime().split(" ");
            baggageSegmentInfo.setDepDate(depDateStr[0]);
            baggageSegmentInfo.setDepTime(depDateStr[1]);
            String depWeek = DateUtils.getWeek(depDate);
            baggageSegmentInfo.setDepWeekNum(depWeek);

            baggageSegmentInfo.setArrCity(arrLocalAirport.getCityCode());
            baggageSegmentInfo.setArrCityName(arrLocalAirport.getCityName());
            baggageSegmentInfo.setArrAirport(arrLocalAirport.getAirPortCode());
            baggageSegmentInfo.setArrAirportName(arrLocalAirport.getAirPortName());

            String[] arrDepStr = segmentInfo.getArrTime().split(" ");
            baggageSegmentInfo.setArrDate(arrDepStr[0]);
            baggageSegmentInfo.setArrTime(arrDepStr[1]);
            String arrWeek = DateUtils.getWeek(arrDate);
            baggageSegmentInfo.setArrWeekNum(arrWeek);
            //时长转换

            AirPortInfoDto depAirPort = localCacheService.getLocalAirport(segmentInfo.getDepAirportCode(), DateUtils.toDate(segmentInfo.getDepTime(), DateUtils.YYYY_MM_DD_HH_MM_PATTERN));
            AirPortInfoDto arrAirPort = localCacheService.getLocalAirport(segmentInfo.getArrAirportCode(), DateUtils.toDate(segmentInfo.getArrTime(), DateUtils.YYYY_MM_DD_HH_MM_PATTERN));
            if (depAirPort != null && arrAirPort != null) {
                String depCityTimeZone = depAirPort.getCityTimeZone();
                String arrCityTimeZone = arrAirPort.getCityTimeZone();
                //飞行时长
                if ((!StringUtil.isNullOrEmpty(arrCityTimeZone)) &&
                        (!StringUtil.isNullOrEmpty(depCityTimeZone)) &&
                        (!StringUtil.isNullOrEmpty(segmentInfo.getDepTime())) &&
                        (!StringUtil.isNullOrEmpty(segmentInfo.getArrTime()))) {
                    //添加夏、冬令时处理
                    if (!depCityTimeZone.equals(arrCityTimeZone)) {
                        depCityTimeZone = FlightUtil.convertSummerOrWinterTime(depCityTimeZone, segmentInfo.getDepTime(), depAirPort);
                        arrCityTimeZone = FlightUtil.convertSummerOrWinterTime(arrCityTimeZone, segmentInfo.getArrTime(), arrAirPort);
                    }
                    //飞行时长
                    long flightTime = DateUtils.calDuration(segmentInfo.getDepTime(), depCityTimeZone, segmentInfo.getArrTime(), arrCityTimeZone);
                    baggageSegmentInfo.setFlightTime(formatTime(flightTime));
                    int day = DateUtils.diffDays(segmentInfo.getDepTime().substring(0, 10), segmentInfo.getArrTime().substring(0, 10), DateUtils.YYYY_MM_DD_PATTERN);
                    baggageSegmentInfo.setDurDay(day < 0 ? 0 : day);
                }
            }
            baggageSegmentInfo.setAirLineCode(segmentInfo.getFlightNo());
            //处理机型转换
            FlightInfo flightInfo = new FlightInfo();
            flightInfo.setFlightNo(segmentInfo.getFlightNo());
            flightInfo.setDepAirport(segmentInfo.getDepAirportCode());
            flightInfo.setArrAirport(segmentInfo.getArrAirportCode());
            flightInfo.setFlightDate(segmentInfo.getDepTime().substring(0, 10));
            List<FlightInfo> flightInfoList = basicService.queryFlightInfo(flightInfo);
            final List<FlightInfo> info = new ArrayList();
            flightInfoList.forEach((FlightInfo flight) -> {
                if (flight.getFlightNo().equals(segmentInfo.getFlightNo())) {
                    info.add(flight);
                }
            });

            if (!StringUtil.isNullOrEmpty(info)) {
                Map<String, AircraftModel> aircraftModelMap = FlightUtil.toAircraftModelMap(handConfig.getAircraftModel());
                FlightInfo flightInfoResult = info.get(0);
                AircraftModel aircraftModel = FlightUtil.convertAircraftModel(aircraftModelMap, flightInfoResult.getPlanType());
                baggageSegmentInfo.setAirLineName(aircraftModel == null ? "" : aircraftModel.getRemark());
                baggageSegmentInfo.setPlaneType(flightInfoResult.getPlanType());
            }

            //没有默认取cabinClass
            String cabin = segmentInfo.getCabin();
            if (StringUtil.isNullOrEmpty(cabin)) {
                cabin = segmentInfo.getCabinClass();
            }
            baggageSegmentInfo.setCabin(cabin);
            baggageSegmentInfo.setCabinClass(segmentInfo.getCabinClass());
            baggageSegmentInfo.setCabinClassName(CommonUtil.showCabinClassName(segmentInfo.getCabinClass()));
            baggageSegmentInfo.setPnrNo(segmentInfo.getPnrNo());
            baggageSegmentInfo.setTravellerName(ptIBETicketInfo.getPassengerName());
            baggageSegmentInfo.setTravellerNumber(ptIBETicketInfo.getPassengerName());
            baggageSegmentInfo.setPnrNo(segmentInfo.getPnrNo());
            baggageSegmentInfo.setTicketStatus(segmentInfo.getTicketStatus());

            String routeType = HandlerConstants.ROUTE_TYPE_OW;
            if (ptIBETicketInfo.getOrgCity().equals(ptIBETicketInfo.getDstCity())) {
                routeType = HandlerConstants.ROUTE_TYPE_RT;
            }
            int segCount = ptIBETicketInfo.getSegmentInfoList().size();

            //航班方向
            baggageSegmentInfo.setFlightDirection(FlightDirection.GO.getCode());
            if (HandlerConstants.ROUTE_TYPE_RT.equals(routeType) && segmentInfo.getSegmentIndex() > (segCount / 2)) {
                baggageSegmentInfo.setFlightDirection(FlightDirection.BACK.getCode());
            }

            baggageSegmentInfo.setFlightDate(baggageSegmentInfo.getDepDate());
            baggageSegmentInfo.setTicketType(segmentInfo.getType());
            baggageSegmentInfo.setInterFlag(ptIBETicketInfo.getInterFlag());
            baggageSegmentInfo.setFlightType(segmentResp.getRouteType());

            //只有客票状态为 open的才能购买
            baggageSegmentInfo.setCanPurchase(false);
            if (segmentInfo.getTicketStatus().equals(HandlerConstants.OPEN_FOR_USE)) {
                baggageSegmentInfo.setCanPurchase(true);
            }

            // 是否联程校验
            if (this.whetherConnectingFlight(ptIBETicketInfo)) {
                baggageSegmentInfo.setCanPurchase(false);
                baggageSegmentInfo.setErrorDesc(this.ERR_CONTENT_PREPAYMENT_BAGGAGE_CONNECTING_FLIGHT);
            }

            if (segmentInfo.getTicketStatus().equals(HandlerConstants.CHECKED_IN)) {
                baggageSegmentInfo.setCanPurchase(false);
                baggageSegmentInfo.setErrorDesc(this.ERR_CONTENT_PREPAYMENT_BAGGAGE_CHECKED_IN);
            }
            segmentRespList.add(baggageSegmentInfo);
        }
        return segmentRespList;
    }


    /**
     * 是否为联程航班
     * 1. 有后续票号
     * 或
     * 2. 航段数大于1且dstCity/orgCity不同
     *
     * @param ptIBETicketInfo
     * @return
     */
    private boolean whetherConnectingFlight(PtIBETicketInfo ptIBETicketInfo) {
        boolean isConnectingFlight = false;
        if (!StringUtil.isNullOrEmpty(ptIBETicketInfo.getFollowTicketNo())) {
            isConnectingFlight = true;
        }
        if (ptIBETicketInfo.getSegmentInfoList().size() > 1 && !ptIBETicketInfo.getDstCity().equals(ptIBETicketInfo.getOrgCity())) {
            isConnectingFlight = true;
        }
        return isConnectingFlight;
    }

    /**
     * 判断当前航段是否支持购买预付费行李
     *
     * @param ip
     * @param channelCode
     * @param userNo
     * @param respList
     * @param segmentReq
     * @return
     */
    private void checkCanPayBaggage(String ip, String channelCode, String userNo, List<QueryPrepaymentBaggageSegmentResp> respList, QueryPrepaymentBaggageSegmentReq segmentReq) {
        BaseRequestDTO<CheckCanPayBaggageReq> requestDTO = new BaseRequestDTO<>();
        requestDTO.setVersion("10");
        requestDTO.setChannelCode(channelCode);
        requestDTO.setUserNo(userNo);
        requestDTO.setFfpId(segmentReq.getFfpId());
        requestDTO.setFfpCardNo(segmentReq.getFfpCardNo());
        CheckCanPayBaggageReq checkCanPayBaggageReq = new CheckCanPayBaggageReq();
        checkCanPayBaggageReq.setSearchTypes(VoucherTypesEnum.EXTRABAGGAGE.getCode());
        if (StringUtil.isNullOrEmpty(respList)) {
            return;
        }
        for (QueryPrepaymentBaggageSegmentResp segmentResp : respList) {
            List<BaggageSegmentInfo> baggageSegmentInfoList = segmentResp.getBaggageSegmentInfoList();
            if (StringUtil.isNullOrEmpty(baggageSegmentInfoList)) {
                continue;
            }
            for (BaggageSegmentInfo baggageSegmentInfo : baggageSegmentInfoList) {
                SingleBookCondition singleBookCondition = new SingleBookCondition();
                singleBookCondition.setDepAirportCode(baggageSegmentInfo.getDepAirport());
                singleBookCondition.setArrAirportCode(baggageSegmentInfo.getArrAirport());
                singleBookCondition.setDepCity(baggageSegmentInfo.getDepCity());
                singleBookCondition.setArrCity(baggageSegmentInfo.getArrCity());
                singleBookCondition.setFlightDate(baggageSegmentInfo.getDepDate() + " " + baggageSegmentInfo.getDepTime() + ":00");
                checkCanPayBaggageReq.setSingleBookCondition(singleBookCondition);
                requestDTO.setRequest(checkCanPayBaggageReq);
                String requestUrl = HandlerConstants.URL_COUPON_API + HandlerConstants.PRODUCT_CHECK_CANPAY_BAGGAGE;
                HttpResult result = HttpUtil.doPostClient(requestDTO, requestUrl);
                if (!result.isResult()) {
                    throw new NetworkException("查询是否购买预付费行李异常");
                }
                BaseResultDTO<CanPayBaggageResp> resultDTO = (BaseResultDTO<CanPayBaggageResp>)
                        JsonUtil.jsonToBean(result.getResponse(), new TypeToken<BaseResultDTO<CanPayBaggageResp>>() {
                        }.getType());
                if ("10001".equals(resultDTO.getResultCode())) {
                    CanPayBaggageResp canPayBaggageResp = resultDTO.getResult();
                    if (!canPayBaggageResp.getCanBuy()) {
                        baggageSegmentInfo.setCanPurchase(false);
                    }
                    if (canPayBaggageResp.getTimeLimit() != null) {
                        //时间验证  已取消,时间不确定,具体超时时间由产品端控制
                        Date arrDateTime = DateUtils.toDate(baggageSegmentInfo.getDepDate() + " " + baggageSegmentInfo.getDepTime(), DateUtils.YYYY_MM_DD_HH_MM_PATTERN);
                        Date dateOutTime = DateUtils.dateAddOrLessSecond(arrDateTime, -60 * canPayBaggageResp.getTimeLimit());

                        if (dateOutTime.before(new Date())) {
                            baggageSegmentInfo.setErrorDesc("航班起飞时间小于" + canPayBaggageResp.getTimeLimit() + "分钟后不支持购买");
                            baggageSegmentInfo.setCanPurchase(false);
                        }
                    }
                } else {
                    throw new NetworkException("查询是否购买预付费行李错误");
                }
            }
        }
    }

    /**
     * 毫秒转化时分,英文
     */
    private String formatTime(Long ms) {
        Integer ss = 1000;
        Integer mi = ss * 60;
        Integer hh = mi * 60;
        Integer dd = hh * 24;

        Long day = ms / dd;
        Long hour = (ms - day * dd) / hh;
        Long minute = (ms - day * dd - hour * hh) / mi;
        StringBuilder sb = new StringBuilder();
        if (day > 0) {
            sb.append(day + "d");
        }
        if (hour > 0) {
            sb.append(hour + "h");
        }
        if (minute > 0) {
            sb.append(minute + "m");
        }

        return sb.toString();
    }

    //获取航班信息
    private FlightInfo queryFlightInfo(String flightDate, String flightNo, String depAirportCode, String arrAirportCode) {
        com.juneyaoair.mobile.mongo.entity.FlightInfo flightInfo = new com.juneyaoair.mobile.mongo.entity.FlightInfo();
        flightInfo.setFlightDate(flightDate);
        flightInfo.setDepAirport(depAirportCode);
        flightInfo.setArrAirport(arrAirportCode);
        flightInfo.setFlightNo(flightNo);
        List<com.juneyaoair.mobile.mongo.entity.FlightInfo> flightInfoList = basicService.queryFlightInfo(flightInfo);
        if (CollectionUtils.isNotEmpty(flightInfoList)) {
            return flightInfoList.get(0);
        }
        return null;
    }

    private PremiumProductResponse getPremiumProductResponse(ActivityPremiumResp activityPremiumResp, List<Product> productList) {
        PremiumProductResponse premiumProductResponse = new PremiumProductResponse();
        BeanUtils.copyNotNullProperties(activityPremiumResp, premiumProductResponse);
        DistrictProduct districtProduct = new DistrictProduct();
        //映射数据
        List<PremiumProductInfo> premiumProductInfoList = mapperPremiumProductInfo(productList);
        districtProduct.setDomestic(premiumProductInfoList);
        premiumProductResponse.setPremiumName(getPremiumName(activityPremiumResp.getTicketNumber()));
        premiumProductResponse.setDistrictProduct(districtProduct);
        PremiumProductInfo premiumProductInfo = premiumProductInfoList.stream().min(Comparator.comparing(x -> x.getSalePrice())).orElse(null);
        if (premiumProductInfo!=null){
            premiumProductResponse.setSalePrice(premiumProductInfo.getSalePrice());
            premiumProductResponse.setStandardPrice(premiumProductInfo.getStandardPrice());
        }
        return premiumProductResponse;
    }

    private String getPremiumName(String type) {
        PremiumEnum[] values = PremiumEnum.values();
        for (PremiumEnum value : values) {
            if (value.getCode().equals(type)) {
                return value.getName();
            }
        }
        return "";
    }


    private List<PremiumProductInfo> mapperPremiumProductInfo(List<Product> productList) {
        List<PremiumProductInfo> premiumProductInfoList = new ArrayList<>();
        for (Product product : productList) {
            PremiumProductInfo premiumProductInfo = new PremiumProductInfo();
            BeanUtils.copyNotNullProperties(product, premiumProductInfo);
            premiumProductInfoList.add(premiumProductInfo);
        }
        return premiumProductInfoList;
    }


    /**
     * 根据远程请求优选服务详细信息和权益券信息,生成具体优选服务订单信息
     *
     * @param productResponse
     * @param activityPremiumResp
     * @return
     */
    private List<Product> queryProduct(PremiumInfoRequest productResponse,
                                       ActivityPremiumResp activityPremiumResp,
                                       HttpServletRequest request,
                                       String channelCode,
                                       String userNo) {
        List<String> productIds = activityPremiumResp.getProductIds();
        StringBuilder proNums = new StringBuilder();
        if (StringUtil.isNullOrEmpty(productIds)) {
            return null;
        }
        for (String productId : productIds) {
            proNums.append(productId);
            proNums.append(",");
        }
        proNums.setLength(proNums.length() - 1);

        //根据id查询
        String ip = this.getClientIP(request);
        Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
        PtRequest<ProductQueryRequestDto> ptRequest = new PtRequest(HandlerConstants.VERSION, channelCode, userNo);
        ptRequest.setFfpId(productResponse.getFfpId());
        ptRequest.setFfpCardNo(productResponse.getFfpCardNo());
        ProductQueryRequestDto productQueryRequestDto = new ProductQueryRequestDto();
        productQueryRequestDto.setProNum(proNums.toString());
        productQueryRequestDto.setSearchTypes("ALL");
        ptRequest.setRequest(productQueryRequestDto);
        PtResponse<List<ProductInfo>> ptResponse = orderManage.queryProducts(ptRequest, headMap);

        //如果不成功或者为null或者空则直接返回null
        if (!UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(ptResponse.getResultCode()) || StringUtil.isNullOrEmpty(ptResponse.getResult())) {
            return null;
        }
        //获取所有数据
        List<ProductInfo> productInfos = ptResponse.getResult();
        List<Product> productList = new ArrayList<>();
        for (ProductInfo productInfo : productInfos) {
            List<Product> products = productInfo.getProducts();
            if (CollectionUtils.isNotEmpty(products)) {
                products.forEach(product -> {
                    if (CollectionUtils.isNotEmpty(product.getProFields())) {
                        product.getProFields().forEach(proField -> {
                            if ("StandardPrice".equals(proField.getFieldCode())) {
                                product.setStandardPrice(Double.parseDouble(proField.getFieldValue()));
                            }
                        });
                    }

                });
            }

            productList.addAll(products);
        }
        productList.sort((a, b) -> (int) (a.getSalePrice() - b.getSalePrice()));
        return productList;
    }





    /**
     * 根据远程请求优选服务详细信息和权益券信息,生成具体优选服务订单信息
     *
     * @param productResponse
     * @param activityPremiumResp
     * @return
     */
    private List<Product> productQueryDisneyTicket(PremiumInfoRequest productResponse,
                                       ActivityPremiumResp activityPremiumResp,
                                       HttpServletRequest request,
                                       String channelCode) {
        List<String> productIds = activityPremiumResp.getProductIds();
        List<Product> productList = new ArrayList<>();

        try {
            //根据id查询
            String ip = this.getClientIP(request);
            ProductAvailableRespDto ptResponse = orderManage.productQueryAvailable(channelCode,productResponse.getFfpId(),
                    productResponse.getFfpCardNo(),ip,productIds);
            List<ProductActivityDto>   productActivityDtoList  =ptResponse.getProductList();
            if(CollectionUtils.isNotEmpty(productActivityDtoList)){
                productActivityDtoList.forEach(productActivityDto -> {
                    Product  product =new Product();
                    product.setProductId(productActivityDto.getProductId());
                    product.setProductNum(productActivityDto.getActivityNo());
                    product.setProductName(productActivityDto.getActivityName());
                    product.setSalePrice(productActivityDto.getOriginalPrice().doubleValue());
                    product.setProductType(productActivityDto.getProductType());
                    product.setActivityName(productActivityDto.getActivityName());
                    product.setEndDatetime(productActivityDto.getEndDatetime());
                    product.setBeginDatetime(productActivityDto.getBeginDatetime());
                    if (CollectionUtils.isNotEmpty(productActivityDto.getProductFieldList())) {
                        productActivityDto.getProductFieldList().forEach(proField -> {
                            if ("DisneyRule".equals(proField.getFieldCode())) {
                                product.setProductRule(proField.getFieldValue());
                            }
                            if ("standardPrice".equals(proField.getFieldCode())) {
                                product.setStandardPrice(Double.valueOf(proField.getFieldValue()));
                            }
                        });
                    }
                    productList.add(product);
                });
            }
        }catch (Exception e){
            log.info("优选服务详细信息和权益券信息报错{}",e);
        }


        return productList;
    }



    /**
     * 根据请求参数过滤内容
     *
     * @param productList
     * @param productResponse
     * @param cityInfoMap
     * @return
     */
    private List<Product> verifyProductList(List<Product> productList, PremiumInfoRequest productResponse, Map<String, CityInfoDto> cityInfoMap) {
        List<PremiumFlightInfo> flightInfoList = productResponse.getFlightInfoList();
        //过滤往返
        for (PremiumFlightInfo premiumFlightInfo : flightInfoList) {
            //如果是休息室,就只根据机场判断
            if (productList.get(0).getProductType().equals(VoucherTypesEnum.LOUNGECOUPON.getCode())) {
                List<Product> collect = productList.stream()
                        .filter(product -> filterLounge(product, productResponse))
                        .collect(Collectors.toList());
                if (!StringUtil.isNullOrEmpty(collect)) {
                    return collect;
                } else {
                    return null;
                }
            }
            List<Product> products = productList.stream()
                    .filter(product -> (listLimitFilter(product.getProductRuleLimit().getCabin(), premiumFlightInfo.getCabinCode())
                            && listLimitFilter(product.getProductRuleLimit().getAirLine(), suitFlightLine(premiumFlightInfo.getDepCityCode(), premiumFlightInfo.getArrCityCode(), cityInfoMap))))
                    .collect(Collectors.toList());
            if (!StringUtil.isNullOrEmpty(products)) {
                return products;
            }
        }
        return null;
    }

    /**
     * 处理休息室
     *
     * @param product
     * @param premiumInfoRequest
     * @return
     */
    private static boolean filterLounge(Product product, PremiumInfoRequest premiumInfoRequest) {
        if (!product.getProductType().equals(VoucherTypesEnum.LOUNGECOUPON.getCode())) {
            return true;
        }
        String airportLocation = product.getAirportLocation();
        if (StringUtil.isNullOrEmpty(airportLocation)) {
            return true;
        }
        for (PremiumFlightInfo premiumFlightInfo : premiumInfoRequest.getFlightInfoList()) {
            if (premiumFlightInfo.getDepAirport().equals(airportLocation)
                    && StringUtils.isNotBlank(product.getFlightType())
                    && (HandlerConstants.TRIP_ALL.equals(product.getFlightType())
                    || (HandlerConstants.TRIP_TYPE_D.equals(premiumInfoRequest.getInterFlag()) ? HandlerConstants.TRIP_DOMESTIC : HandlerConstants.TRIP_INTL).equals(product.getFlightType()))) {
                return true;
            }
        }
        return false;
    }

    /**
     * 过滤白名单集合里面是否包含当前信息
     *
     * @param limitLists
     * @param value
     * @return
     */
    private static boolean listLimitFilter(List<String> limitLists, String value) {
        if (CollectionUtils.isNotEmpty(limitLists)) {
            return limitLists.contains(value);
        } else {
            return true;
        }
    }

    /**
     * 过滤集合里面是否包含当前信息
     *
     * @param limitLists
     * @param value
     * @return
     */
    private static boolean listLimitFilter(List<AirlineLimit> limitLists, List<String> value) {
        if (CollectionUtils.isEmpty(limitLists)) {
            return true;
        }
        List<String> limitAirLines = Lists.newArrayList();
        boolean success = false;
        for (AirlineLimit limit : limitLists) {
            if (CollectionUtils.isNotEmpty(limit.getValue())) {
                limitAirLines.addAll(limit.getValue());
            }
        }
        if (CollectionUtils.isNotEmpty(limitAirLines)) {
            for (String str : value) {
                success = limitAirLines.stream().anyMatch(s -> s.equals(str));
                if (success) {
                    return true;
                }
            }
        } else {
            return true;
        }
        return success;
    }


    private static List<String> suitFlightLine(String depCityCode, String arrCityCode, Map<String, CityInfoDto> cityInfoMap) {
        List<String> suitAirline = new ArrayList<>();
        suitAirline.add("*-*");
        suitAirline.add(depCityCode + "-*");
        suitAirline.add("*-" + arrCityCode);
        suitAirline.add(depCityCode + "-" + arrCityCode);
        CityInfoDto depCity = cityInfoMap.get(depCityCode);
        CityInfoDto arrCity = cityInfoMap.get(arrCityCode);
        if (depCity != null && arrCity != null) {
            if (HandlerConstants.TRIP_TYPE_D.equals(depCity.getIsInternational()) && HandlerConstants.TRIP_TYPE_D.equals(arrCity.getIsInternational())) {
                suitAirline.add("*-DOMESTIC");
                suitAirline.add("DOMESTIC-*");
                suitAirline.add("DOMESTIC-DOMESTIC");
                suitAirline.add(depCityCode + "-DOMESTIC");
                suitAirline.add("DOMESTIC-" + arrCityCode);
            } else if (HandlerConstants.TRIP_TYPE_D.equals(depCity.getIsInternational()) && HandlerConstants.TRIP_TYPE_I.equals(arrCity.getIsInternational())) {
                suitAirline.add("*-INTL");
                suitAirline.add("DOMESTIC-*");
                suitAirline.add("DOMESTIC-INTL");
                suitAirline.add(depCityCode + "-INTL");
                suitAirline.add("DOMESTIC-" + arrCityCode);
            } else if (HandlerConstants.TRIP_TYPE_I.equals(depCity.getIsInternational()) && HandlerConstants.TRIP_TYPE_D.equals(arrCity.getIsInternational())) {
                suitAirline.add("*-DOMESTIC");
                suitAirline.add("INTL-*");
                suitAirline.add("INTL-DOMESTIC");
                suitAirline.add(depCityCode + "-DOMESTIC");
                suitAirline.add("INTL-" + arrCityCode);
            } else {
                suitAirline.add("*-INTL");
                suitAirline.add("INTL-*");
                suitAirline.add("INTL-INTL");
                suitAirline.add(depCityCode + "-INTL");
                suitAirline.add("INTL-" + arrCityCode);
            }
        }
        return suitAirline;
    }

    /**
     * 构造请求参数
     *
     * @param premiumInfoRequest
     * @param premiumFlightInfoGo
     * @param userNo
     * @param channelCode
     * @param voucherType
     * @return
     */
    private PtRequest getPtRequest(PremiumInfoRequest premiumInfoRequest, PremiumFlightInfo premiumFlightInfoGo, String userNo, String channelCode, String voucherType) {
        PtRequest ptGoRequest;
        QueryCouponProductRequest couponProductGoRequest = new QueryCouponProductRequest();
        couponProductGoRequest.setFfpId(premiumInfoRequest.getFfpId());
        couponProductGoRequest.setFfpCardNo(premiumInfoRequest.getFfpCardNo());
        couponProductGoRequest.setArrCityCode(premiumFlightInfoGo.getArrCityCode());
        couponProductGoRequest.setDepCityCode(premiumFlightInfoGo.getDepCityCode());
        //封装request
        ptGoRequest = createProductQuery(channelCode, userNo, couponProductGoRequest, voucherType);
        return ptGoRequest;
    }

    @ApiOperation(value = "创建权益券产品下单", notes = "创建权益券产品下单")
    @RequestMapping(value = "/createCommonCouponOrder", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @InterfaceLog
    public BaseResp<BuyCouponProductResponse> createFlightProductOrder(@RequestBody @Validated BaseReq<BuyCouponProductRequest> req, BindingResult bindingResult, HttpServletRequest request) {
        BaseResp<BuyCouponProductResponse> resp = new BaseResp();
        String headChannelCode = request.getHeader(HandlerConstants.TOKEN_CHANNELCODE);
        String versionCode = request.getHeader(HandlerConstants.CLIENT_VERSION_CODE);
        String reqId = headChannelCode + versionCode + StringUtil.newGUID() + request.getRequestURI();
        String ip = this.getClientIP(request);
        //校验参数
        this.checkRequest(req, bindingResult);
        BuyCouponProductRequest buyCouponProductRequest = req.getRequest();
        try {
            //判断 invitationCode  长度不能大于50，并且校验输入格式数字和字母
            String invitationCode = req.getRequest().getInvitationCode();
            this.checkInvitationCode(resp, invitationCode);
            if ("10003".equals(resp.getResultCode())) {
                return resp;
            }
            //不同的活动类型需要检验不同的参数
            checkProParam(buyCouponProductRequest);
            //检验活动开放日期
            CommonCouponProductInfo couponProductInfo = buyCouponProductRequest.getProductInfo();
            //验证产品信息
            String sign = reCreateProSign(couponProductInfo);
            if (!couponProductInfo.getSign().equals(sign)) {
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setResultInfo("请重新查询后下单！");
                return resp;
            }
            if (buyCouponProductRequest.getSaleCount() > 5) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo("最多可购买5份");
                return resp;
            }
            String channelCode = req.getChannelCode();
            String userNo = getChannelInfo(channelCode, "10");
            //转换请求参数
            PtCreateOrderRequest ptCreateOrderRequest = createOrderParam(buyCouponProductRequest);
            PtRequest ptRequest = new PtRequest(HandlerConstants.VERSION, channelCode, userNo);
            ptRequest.setRequestIp(ip);
            ptRequest.setRequest(ptCreateOrderRequest);
            ptRequest.setSearchType(couponProductInfo.getProductType());
            Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
            ClientInfo clientInfo = initClientInfo(request, channelCode, buyCouponProductRequest.getFfpId(), buyCouponProductRequest.getFfpCardNo());
            //使用积分
            if (buyCouponProductRequest.getScore() > 0) {
                AccountCheckParam accountCheckParam = new AccountCheckParam();
                accountCheckParam.setFfpCardNo(buyCouponProductRequest.getFfpCardNo());
                accountCheckParam.setFfpId(String.valueOf(buyCouponProductRequest.getFfpId()));
                accountCheckParam.setOperation(SensitiveOperationEnum.USE_SALE.getOperation());
                accountCheckParam.setBlackBox(buyCouponProductRequest.getBlackBox());
                memberPasswordService.checkAccount(clientInfo, accountCheckParam, null, null);
                //积分使用规则检验
                if (orderService.checkScoreUseRule(clientInfo, channelCode)) {
                    throw new CommonException(WSEnum.NO_REAL_NAME.getResultCode(), "非常抱歉，当前账户存在安全风险，通过实名认证后才可使用积分，请前往实名认证");
                }
                CommonCouponProductInfo commonCouponProductInfo = buyCouponProductRequest.getProductInfo();
                //产品总金额
                BigDecimal total = BigDecimal.valueOf(commonCouponProductInfo.getSalePrice()).multiply(BigDecimal.valueOf(buyCouponProductRequest.getSaleCount()));
                if (new BigDecimal(buyCouponProductRequest.getScore()).compareTo(total) > 0) {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo("积分金额大于订单总金额");
                    return resp;
                }
                //验证消费密码
                resp = orderService.checkFreeScoreLimit(buyCouponProductRequest.getFfpCardNo(), buyCouponProductRequest.getFfpId(), channelCode, getChannelInfo(channelCode, "40"), buyCouponProductRequest.getScore(), buyCouponProductRequest.getSalePwd(), request);
                if (!WSEnum.SUCCESS.getResultCode().equals(resp.getResultCode())) {
                    return resp;
                }
            }
            //根据类型动态调整下单链接地址
            String url = HandlerConstants.URL_FARE_API + HandlerConstants.PRODUCT_CREATE_V10;
            PtResponse<PtBaseCouponOrderIdentity> ptResponse = orderManage.createProductOrder(ptRequest, headMap, url);
            if (ptResponse != null && UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(ptResponse.getResultCode())) {
                BuyCouponProductResponse buyCouponProductResponse = new BuyCouponProductResponse();
                PtBaseCouponOrderIdentity ptBaseCouponOrderIdentity = ptResponse.getResult();
                buyCouponProductResponse.setOrderNo(ptBaseCouponOrderIdentity.getOrderNo());
                buyCouponProductResponse.setChannelOrderNo(ptBaseCouponOrderIdentity.getOrderChannelOrderNo());
                buyCouponProductResponse.setPayState(false);
                buyCouponProductResponse.setCouponSource(couponProductInfo.getProductType());
                //机上WIFI 下单默认就是支付状态，无需0元支付
                if (VoucherTypesEnum.ONBOARDWIFI.getCode().equals(couponProductInfo.getProductType())) {
                    buyCouponProductResponse.setPayState(true);
                    //同步订单至PDI
                    sendOrderToPdi(buyCouponProductRequest, ptBaseCouponOrderIdentity.getOrderNo(), ptBaseCouponOrderIdentity.getOrderChannelOrderNo());
                } else {
                    //0元同步支付
                    if (buyCouponProductRequest.getScore() > 0 && ptCreateOrderRequest.getTotalAmount().doubleValue() == 0) {
                        String key = getChannelInfo(req.getChannelCode(), "20");

                        String postUrl = HandlerConstants.URL_PAY;
                        Map<String, String> parametersMap = VirtualPaymentConvert.payment0(req.getChannelCode(),
                                ptBaseCouponOrderIdentity.getOrderNo(), ptBaseCouponOrderIdentity.getOrderChannelOrderNo(), key,
                                "CouponType", "", "O");
                        parametersMap.put("UseScore", String.valueOf(buyCouponProductRequest.getScore()));
                        log.info("请求号:{}，IP地址:{}，0元支付请求：{}", reqId, ip, JsonUtil.objectToJson(parametersMap));
                        long timestamp = System.currentTimeMillis();
                        HttpResult payResult = doPayPost(postUrl, parametersMap);
                        log.info("请求号:{}，IP地址:{}，耗时：{}毫秒, 0元支付结果：{}", reqId, ip, System.currentTimeMillis() - timestamp, payResult.getResponse());
                        PaymentResp paymentResp;
                        if (payResult.isResult()) {
                            String paymentInfo = payResult.getResponse().trim();
                            paymentResp = (PaymentResp) JsonUtil.jsonToBean(paymentInfo, PaymentResp.class);
                            //虚拟支付成功
                            if (paymentResp != null && "1001".equals(paymentResp.getRespCode())) {
                                buyCouponProductResponse.setPayState(true);
                                resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                            } else {
                                resp.setResultCode(WSEnum.ERROR.getResultCode());
                                resp.setResultInfo("权益购买支付失败！");
                            }
                        } else {
                            resp.setResultCode(WSEnum.ERROR.getResultCode());
                            resp.setResultInfo("支付请求出错");
                        }
                    }
                }
                resp.setObjData(buyCouponProductResponse);
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
            } else if (ptResponse != null && UnifiedOrderResultEnum.R1005.getResultCode().equals(ptResponse.getResultCode())) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo(ptResponse.getErrorInfo());
            } else if (ptResponse != null && UnifiedOrderResultEnum.CHECK_9999.getResultCode().equals(ptResponse.getResultCode())) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo(ptResponse.getErrorInfo());
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("小吉正在努力搬砖中，请您稍后再试");
            }
            return resp;
        } catch (CommonException commonException) {
            throw commonException;
        } catch (IllegalArgumentException e) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo(e.getMessage());
            return resp;
        } catch (ServiceException serviceException) {
            throw serviceException;
        } catch (Exception e) {
            this.logError(resp, reqId, ip, req, e, "糟糕，出现意外了");
            return resp;
        }
    }

    private void sendOrderToPdi(BuyCouponProductRequest buyCouponProductRequest, String orderNo, String channelOrderNo) {
        PdiResult<String> tokenResult = pdiManage.getToken();
        if (tokenResult.isSuccess()) {
            PdiCreateOrder pdiCreateOrder = new PdiCreateOrder();
            pdiCreateOrder.setAirlineOrderNo(orderNo);
            pdiCreateOrder.setChannelOrderNo(channelOrderNo);
            //下单网络环境：0-机上 1-机下
            pdiCreateOrder.setEnvironment("1");
            //终端类型 ：app、wap、pc
            pdiCreateOrder.setTerminal("app");
            //订单类型：1-现金 2-积分
            pdiCreateOrder.setOrderType("2");
            CommonCouponProductInfo commonCouponProductInfo = buyCouponProductRequest.getProductInfo();
            pdiCreateOrder.setProductId(commonCouponProductInfo.getProductId());
            pdiCreateOrder.setStandardPrice(String.valueOf(commonCouponProductInfo.getStandardPrice()));
            pdiCreateOrder.setTotalAmount(String.valueOf(commonCouponProductInfo.getSalePrice()));
            //实付金额（若为积分订单就传积分值，俩位小数）
            pdiCreateOrder.setPayAmount(String.valueOf(commonCouponProductInfo.getSalePrice()));
            OrderCusInfoVO orderCusInfoVO = getOrderCusInfoVO(buyCouponProductRequest);
            pdiCreateOrder.setOrderCusInfoVO(orderCusInfoVO);
            PdiResult<String> orderPdiResult = pdiManage.createOrder(tokenResult.getData(), pdiCreateOrder);
            if (!orderPdiResult.isSuccess()) {
                ApiErrorLogs apiErrorLogs = new ApiErrorLogs();
                apiErrorLogs.setFfpId(buyCouponProductRequest.getFfpId());
                apiErrorLogs.setFfpCard(buyCouponProductRequest.getFfpCardNo());
                apiErrorLogs.setPath(HandlerConstants.PDI_URL + HandlerConstants.PDI_CREATE_ORDER);
                apiErrorLogs.setRequest(JsonUtil.objectToJson(pdiCreateOrder));
                apiErrorLogs.setResponse(JsonUtil.objectToJson(orderPdiResult));
                apiErrorLogsServiceImpl.saveApiErrorLogs(apiErrorLogs);
            }
        }
    }

    private OrderCusInfoVO getOrderCusInfoVO(BuyCouponProductRequest buyCouponProductRequest) {
        OrderCusInfoVO orderCusInfoVO = new OrderCusInfoVO();
        ProductFlightInfo productFlightInfo = buyCouponProductRequest.getProductFlightInfo();
        orderCusInfoVO.setCustomerName(productFlightInfo.getTravellerName());
        orderCusInfoVO.setFlightDate(productFlightInfo.getFlightDate());
        orderCusInfoVO.setFlightNo(productFlightInfo.getFlightNo());
        orderCusInfoVO.setShortCardNo(productFlightInfo.getCertNo().substring(productFlightInfo.getCertNo().length() - 4));
        orderCusInfoVO.setMobile(buyCouponProductRequest.getMobilePhone());
        orderCusInfoVO.setTicketNo(StringUtils.isNotBlank(productFlightInfo.getTktNo()) ? productFlightInfo.getTktNo().replace("-", "") : "");
        orderCusInfoVO.setOriginIATA(productFlightInfo.getDepAirportCode());
        orderCusInfoVO.setDestinationIATA(productFlightInfo.getArrAirportCode());
        return orderCusInfoVO;
    }

    /**
     * 检验需要的参数
     *
     * @param buyCouponProductRequest
     */
    private void checkProParam(BuyCouponProductRequest buyCouponProductRequest) {
        ObjCheckUtil.notNull(buyCouponProductRequest, "业务参数不完整");
        ObjCheckUtil.notNull(buyCouponProductRequest.getProductInfo(), "产品信息不可为空");
        if (VoucherTypesEnum.ONBOARDWIFI.getCode().equals(buyCouponProductRequest.getProductInfo().getProductType())) {
            ObjCheckUtil.notNull(buyCouponProductRequest.getProductFlightInfo(), "航班信息不可为空");
            ProductFlightInfo productFlightInfo = buyCouponProductRequest.getProductFlightInfo();
            ObjCheckUtil.notBlank(productFlightInfo.getFlightNo(), "航班号不可为空");
            ObjCheckUtil.notBlank(productFlightInfo.getFlightDate(), "航班日期不可为空");
            ObjCheckUtil.notBlank(productFlightInfo.getDepAirportCode(), "出发机场不可为空");
            ObjCheckUtil.notBlank(productFlightInfo.getArrAirportCode(), "到达机场不可为空");
            ObjCheckUtil.notBlank(productFlightInfo.getDepTime(), "航班出发时间不可为空");
            if (!productFlightInfo.getDepTime().matches(PatternCommon.TIME_REGEX)) {
                throw new IllegalArgumentException("航班出发时间格式不正确");
            }
            ObjCheckUtil.notBlank(productFlightInfo.getArrTime(), "航班到达时间不可为空");
            if (!productFlightInfo.getArrTime().matches(PatternCommon.TIME_REGEX)) {
                throw new IllegalArgumentException("航班到达时间格式不正确");
            }
            ObjCheckUtil.notBlank(productFlightInfo.getTktNo(), "票号不可为空");
            ObjCheckUtil.notBlank(productFlightInfo.getCabin(), "舱位不可为空");
            ObjCheckUtil.notBlank(productFlightInfo.getFlightSign(), "验证信息不可为空");
            String sign = createFlightSign(productFlightInfo);
            if (!productFlightInfo.getFlightSign().equals(sign)) {
                throw new IllegalArgumentException("请重新查询下单");
            }
            if (buyCouponProductRequest.getScore() <= 0) {
                throw new IllegalArgumentException("积分数必须大于0");
            }
        }
    }

    private PtRequest createAirWifiRequestDto(String channelCode, String userNo, QueryCouponProductRequest productQuery, String searchType, String interFlag) {
        PtRequest ptRequest = new PtRequest(HandlerConstants.VERSION, channelCode, userNo);
        ptRequest.setFfpId(productQuery.getFfpId());
        ptRequest.setFfpCardNo(productQuery.getFfpCardNo());
        ProductQueryRequestDto productQueryRequestDto = new ProductQueryRequestDto();
        productQueryRequestDto.setSearchTypes(searchType);
        productQueryRequestDto.setFlightType(interFlag);
        ptRequest.setRequest(productQueryRequestDto);
        return ptRequest;
    }

    private PtRequest createProductQuery(String channelCode, String userNo, QueryCouponProductRequest productQuery, String searchType) {
        PtRequest ptRequest = new PtRequest(HandlerConstants.VERSION, channelCode, userNo);
        ptRequest.setFfpId(productQuery.getFfpId());
        ptRequest.setFfpCardNo(productQuery.getFfpCardNo());
        ProductQueryRequestDto productQueryRequestDto = new ProductQueryRequestDto();
        productQueryRequestDto.setSearchTypes(searchType);
        if (VoucherTypesEnum.RESCHEDULE.getCode().equals(searchType)
                || VoucherTypesEnum.UPGRADECOUPON.getCode().equals(searchType)
                || VoucherTypesEnum.LOUNGECOUPON.getCode().equals(searchType)
                || VoucherTypesEnum.BAGGAGECOUPON.getCode().equals(searchType)) {
            productQueryRequestDto.setSingleBookCondition(productQueryRequestDto.createSingleBookCondition(productQuery.getDepCityCode(), productQuery.getArrCityCode()));
        }
        ptRequest.setRequest(productQueryRequestDto);
        return ptRequest;
    }

    /**
     * 处理改期券 权益券前端响应
     *
     * @return
     */
    public BaseResp handleRescheduleCouponResponse(PtRequest ptRequest, BaseResp resp, String channelCode, String ip) {
        NewChangeCouponProductResponse productResponse = new NewChangeCouponProductResponse();
        productResponse.setProductDesc("改期券");
        productResponse.setProductType(VoucherTypesEnum.RESCHEDULE.getCode());
        Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
        //查询产品
        List<ProductInfo> productInfos = getProductInfos(ptRequest, headMap, resp);
        if (CollectionUtils.isNotEmpty(productInfos)) {
            List<CommonCouponProductInfo> couponProListD = new ArrayList<>();
            List<CommonCouponProductInfo> couponProListI = new ArrayList<>();
            Map<String, String> cityMap = basicService.queryAllCityMap();
            for (ProductInfo productInfo : productInfos) {
                List<Product> products = productInfo.getProducts();
                if (CollectionUtils.isNotEmpty(products)) {
                    for (Product product : products) {
                        CommonCouponProductInfo commonCouponProductInfo = convertCommonCouponProduct(product, productInfo.getResourceInfo(), cityMap);
                        if ("DOMESTIC".equals(product.getFlightType())) {
                            couponProListD.add(commonCouponProductInfo);
                        } else if ("INTL".equals(product.getFlightType())) {
                            couponProListI.add(commonCouponProductInfo);
                        }
                    }
                }
            }
            productResponse.setDomestic(couponProListD);
            productResponse.setRegion(couponProListI);
            if (CollectionUtils.isEmpty(couponProListD) && CollectionUtils.isEmpty(couponProListI)) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("您选择的航线改期券已售罄，您可补差价改期~");
            } else {
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
            }
        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("您选择的航线改期券已售罄，您可补差价改期~");
        }
        resp.setObjData(productResponse);
        return resp;
    }

    /**
     * 处理升舱券 权益券前端响应
     *
     * @return
     */
    public BaseResp handleUpgradeCouponResponse(PtRequest ptRequest, BaseResp resp, String channelCode, String ip) {
        NewChangeCouponProductResponse productResponse = new NewChangeCouponProductResponse();
        productResponse.setProductDesc("升舱券");
        productResponse.setProductType(VoucherTypesEnum.UPGRADECOUPON.getCode());
        Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
        //查询产品
        List<ProductInfo> productInfos = getProductInfos(ptRequest, headMap, resp);
        if (CollectionUtils.isNotEmpty(productInfos)) {
            List<CommonCouponProductInfo> couponProListD = new ArrayList<>();
            List<CommonCouponProductInfo> couponProListI = new ArrayList<>();
            Map<String, String> cityMap = basicService.queryAllCityMap();
            for (ProductInfo productInfo : productInfos) {
                List<Product> products = productInfo.getProducts();
                if (CollectionUtils.isNotEmpty(products)) {
                    for (Product product : products) {
                        CommonCouponProductInfo commonCouponProductInfo = convertCommonCouponProduct(product, productInfo.getResourceInfo(), cityMap);
                        if ("DOMESTIC".equals(product.getFlightType())) {
                            couponProListD.add(commonCouponProductInfo);
                        } else if ("INTL".equals(product.getFlightType())) {
                            couponProListI.add(commonCouponProductInfo);
                        }
                    }
                }
            }
            productResponse.setDomestic(couponProListD);
            productResponse.setRegion(couponProListI);
            if (CollectionUtils.isEmpty(couponProListD) && CollectionUtils.isEmpty(couponProListI)) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("您选择的航线升舱券已售罄，您可补差价升舱~");
            } else {
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
            }
        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("您选择的航线升舱券已售罄，您可补差价升舱~");
        }
        resp.setObjData(productResponse);
        return resp;
    }

    /**
     * 处理行李券 权益券前端响应
     *
     * @return
     */
    public BaseResp handleBaggageCouponResponse(PtRequest ptRequest, BaseResp resp, String channelCode, String ip) {
        NewChangeCouponProductResponse productResponse = new NewChangeCouponProductResponse();
        productResponse.setProductDesc("行李券");
        productResponse.setProductType(VoucherTypesEnum.BAGGAGECOUPON.getCode());
        Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
        //查询产品
        List<ProductInfo> productInfos = getProductInfos(ptRequest, headMap, resp);
        if (CollectionUtils.isNotEmpty(productInfos)) {
            List<CommonCouponProductInfo> couponProListD = new ArrayList<>();
            List<CommonCouponProductInfo> couponProListI = new ArrayList<>();
            Map<String, String> cityMap = basicService.queryAllCityMap();
            for (ProductInfo productInfo : productInfos) {
                List<Product> products = productInfo.getProducts();
                if (CollectionUtils.isNotEmpty(products)) {
                    for (Product product : products) {
                        CommonCouponProductInfo commonCouponProductInfo = convertCommonCouponProduct(product, productInfo.getResourceInfo(), cityMap);
                        if ("DOMESTIC".equals(product.getFlightType())) {
                            couponProListD.add(commonCouponProductInfo);
                        } else if ("INTL".equals(product.getFlightType())) {
                            couponProListI.add(commonCouponProductInfo);
                        }
                    }
                }
            }
            productResponse.setDomestic(couponProListD);
            productResponse.setRegion(couponProListI);
            if (CollectionUtils.isEmpty(couponProListD) && CollectionUtils.isEmpty(couponProListI)) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("您选择的逾重行李券已售罄");
            } else {
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
            }
        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("您选择的逾重行李券已售罄");
        }
        resp.setObjData(productResponse);
        return resp;
    }

    /**
     * 处理休息室券 权益券前端响应
     *
     * @return
     */
    public BaseResp handleLoungeCouponResponse(PtRequest ptRequest, BaseResp resp, String channelCode, String ip) {
        NewChangeCouponProductResponse productResponse = new NewChangeCouponProductResponse();
        productResponse.setProductDesc("机场贵宾休息室券");
        productResponse.setProductType(VoucherTypesEnum.LOUNGECOUPON.getCode());
        Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
        //查询产品
        List<ProductInfo> productInfos = getProductInfos(ptRequest, headMap, resp);
        if (CollectionUtils.isNotEmpty(productInfos)) {
            List<CommonCouponProductInfo> couponProListD = new ArrayList<>();
            Map<String, String> cityMap = basicService.queryAllCityMap();
            for (ProductInfo productInfo : productInfos) {
                List<Product> products = productInfo.getProducts();
                if (CollectionUtils.isNotEmpty(products)) {
                    for (Product product : products) {
                        CommonCouponProductInfo commonCouponProductInfo = convertCommonCouponProduct(product, productInfo.getResourceInfo(), cityMap);
                        LoungeCouponExt loungeExt = product.getLoungeCouponExt();
                        if (loungeExt != null) {
                            //营业时间设置
                            String serviceStartTime = transferTime(loungeExt.getStartDate());
                            String serviceEndTime = transferTime(loungeExt.getEndDate());
                            commonCouponProductInfo.setServiceStartTime(serviceStartTime);
                            commonCouponProductInfo.setServiceEndTime(StringUtil.isNullOrEmpty(serviceEndTime) ? "当日出港航班结束" : serviceEndTime);
                            commonCouponProductInfo.setAddress(loungeExt.getLoungeAddress());
                            commonCouponProductInfo.setAirportTerminal(loungeExt.getAirportTerminal());
                        }

                        //处理休息室特有属性
                        couponProListD.add(commonCouponProductInfo);
                    }
                }
            }
            productResponse.setDomestic(couponProListD);
            if (CollectionUtils.isEmpty(couponProListD)) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("您选择的机场贵宾休息室券已售罄");
            } else {
                productResponse.setDomestic(couponProListD.stream().sorted(Comparator.comparing(CommonCouponProductInfo::getSalePrice)).collect(Collectors.toList()));
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
            }
        } else {
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("您选择的机场贵宾休息室券已售罄");
        }
        resp.setObjData(productResponse);
        return resp;
    }

    /**
     * 查询产品
     *
     * @param ptRequest
     * @param headMap
     * @param resp
     * @return
     */
    private List<ProductInfo> getProductInfos(PtRequest ptRequest, Map<String, String> headMap, BaseResp resp) {
        List<ProductInfo> productInfoList = null;
        PtResponse<List<ProductInfo>> ptResponse = orderManage.queryProducts(ptRequest, headMap);
        if (UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(ptResponse.getResultCode())) {
            if (CollectionUtils.isNotEmpty(ptResponse.getResult())) {
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                productInfoList = ptResponse.getResult().stream().map(productInfo -> createProductInfoSign(productInfo)).collect(Collectors.toList());
            } else {
                resp.setResultCode(WSEnum.NO_DATA.getResultCode());
                resp.setResultInfo("暂无可售产品");
            }
        } else {
            if (UnifiedOrderResultEnum.R1005.getResultCode().equals(ptResponse.getResultCode())) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo(ptResponse.getErrorInfo());
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("小吉正在努力搬砖中，请您稍后再试");
            }
        }
        return productInfoList;
    }

    /**
     * 转换产品对象为前端需要的
     *
     * @param product
     * @param resourceInfo
     * @return
     */
    public CommonCouponProductInfo convertCommonCouponProduct(Product product, ResourceBase resourceInfo, Map<String, String> cityMap) {
        CommonCouponProductInfo commonCouponProductInfo = new CommonCouponProductInfo();
        //航线限制处理
        List<List<String>> routeList = RightCouponConvert.applyOtherAirLineNew(product.getProductRuleLimit(), cityMap);
        commonCouponProductInfo.setOtherApplyRoutes(routeList);
        BeanUtils.copyNotNullProperties(product, commonCouponProductInfo);
        commonCouponProductInfo.setTags(StringUtil.isNullOrEmpty(resourceInfo.getTags()) ? new ArrayList<>() : resourceInfo.getTags());
        commonCouponProductInfo.setImgs(StringUtil.isNullOrEmpty(resourceInfo.getImgs()) ? new ArrayList<>() : resourceInfo.getImgs());
        commonCouponProductInfo.setValidityDays(product.getVaildityDays());
        commonCouponProductInfo.setDescription(resourceInfo.getResourceDesc());
        commonCouponProductInfo.setUseMode(resourceInfo.getUseMode());
        commonCouponProductInfo.setPurchaseNotes(resourceInfo.getPurchaseNotes());
        commonCouponProductInfo.setRefundRules(resourceInfo.getRefundRules());
        commonCouponProductInfo.setResourceId(resourceInfo.getResourceId());
        commonCouponProductInfo.setAdvanceHour(product.getAdvanceHour());
        commonCouponProductInfo.setBuyCouponEnjoyList(CollectionUtils.isNotEmpty(resourceInfo.getBuyCouponEnjoyList()) ? resourceInfo.getBuyCouponEnjoyList() : new ArrayList());
        Date curDate = new Date();
        commonCouponProductInfo.setStartDate(DateUtils.convertDateToString(curDate, "yyyy-MM-dd"));
        if (product.getVaildityDays() > 0) {
            commonCouponProductInfo.setEndDate(DateUtils.convertDateToString(DateUtils.addOrLessDay(curDate, product.getVaildityDays()), "yyyy-MM-dd"));
        } else {
            commonCouponProductInfo.setEndDate(StringUtils.isNotBlank(product.getLatestDate()) ? product.getLatestDate() : DateUtils.convertDateToString(curDate, "yyyy-MM-dd"));
        }
        return commonCouponProductInfo;
    }

    private ProductInfo createProductInfoSign(ProductInfo productInfo) {
        ResourceBase resourceBase = productInfo.getResourceInfo();
        List<Product> productList = productInfo.getProducts();
        if (CollectionUtils.isNotEmpty(productList)) {
            productList.forEach(product ->
                    product.setSign(createProSign(resourceBase, product))
            );
        }
        return productInfo;
    }

    /**
     * 查询产品列表时生成产品标记
     *
     * @param resourceBase
     * @param product
     * @return
     */
    private String createProSign(ResourceBase resourceBase, Product product) {
        String originalKey = product.createSignField(resourceBase.getResourceId());
        return EncoderHandler.encodeByMD5(originalKey);
    }

    /**
     * 生成签名串
     *
     * @param commonCouponProductInfo
     * @return
     */
    private String createProSign(CommonCouponProductInfo commonCouponProductInfo) {
        String originalKey = commonCouponProductInfo.createSignField();
        return EncoderHandler.encodeByMD5(originalKey);
    }

    /**
     * 航班信息签名
     *
     * @return
     */
    private String createFlightSign(ProductFlightInfo productFlightInfo) {
        String originalKey = productFlightInfo.createString();
        return EncoderHandler.encodeByMD5(originalKey + productFlightInfo.getFlightNo());
    }

    /**
     * 创建产品订单时重新生成产品标记，校验产品
     *
     * @param couponProductInfo
     * @return
     */
    private String reCreateProSign(CommonCouponProductInfo couponProductInfo) {
        String originalKey = couponProductInfo.getResourceId() + couponProductInfo.getProductNum() + couponProductInfo.getProductId()
                + couponProductInfo.getBookingId() + couponProductInfo.getRuleId() + couponProductInfo.getProductSku()
                + couponProductInfo.getProductType() + couponProductInfo.getSalePrice();
        return EncoderHandler.encodeByMD5(originalKey);
    }

    /**
     * 创建订单请求对象
     *
     * @param buyCouponProductRequest
     * @return
     */
    private PtCreateOrderRequest createOrderParam(BuyCouponProductRequest buyCouponProductRequest) {
        PtCreateOrderRequest ptCreateOrderRequest = new PtCreateOrderRequest();
        ptCreateOrderRequest.setInvitationCode(buyCouponProductRequest.getInvitationCode());
        ptCreateOrderRequest.setFfpId(buyCouponProductRequest.getFfpId());
        ptCreateOrderRequest.setFfpCardNo(buyCouponProductRequest.getFfpCardNo());
        ptCreateOrderRequest.setChannelOrderNo(buyCouponProductRequest.getChannelOrderNo());
        CommonCouponProductInfo commonCouponProductInfo = buyCouponProductRequest.getProductInfo();
        //减去积分之后的实际支付金额
        BigDecimal total = BigDecimal.valueOf(commonCouponProductInfo.getSalePrice()).multiply(BigDecimal.valueOf(buyCouponProductRequest.getSaleCount()));
        BigDecimal actualPaid = total.subtract(BigDecimal.valueOf(buyCouponProductRequest.getScore()));
        ptCreateOrderRequest.setTotalAmount(actualPaid.multiply(BigDecimal.valueOf(100)));
        ptCreateOrderRequest.setCurrency(HandlerConstants.CURRENCY_CODE);
        ptCreateOrderRequest.setPhoneNo(buyCouponProductRequest.getMobilePhone());
        ptCreateOrderRequest.setUseScore(buyCouponProductRequest.getScore());
        ptCreateOrderRequest.setRefundRule(commonCouponProductInfo.getRefundRules());
        ptCreateOrderRequest.setProductName(commonCouponProductInfo.getProductName());
        List<PtBookProductInfo> ptBookProductInfoList = new ArrayList<>();
        PtBookProductInfo ptBookProductInfo = new PtBookProductInfo();
        ptBookProductInfo.setProductId(commonCouponProductInfo.getProductId());
        ptBookProductInfo.setProductSku(commonCouponProductInfo.getProductSku());
        ptBookProductInfo.setBookingId(commonCouponProductInfo.getBookingId());
        ptBookProductInfo.setProductNum(commonCouponProductInfo.getProductNum());
        ptBookProductInfo.setRuleId(commonCouponProductInfo.getRuleId());
        ptBookProductInfo.setResourceType(commonCouponProductInfo.getProductType());
        ptBookProductInfo.setStandardPrice(commonCouponProductInfo.getStandardPrice());
        ptBookProductInfo.setSalePrice(commonCouponProductInfo.getSalePrice());
        ptBookProductInfo.setBookingCount(buyCouponProductRequest.getSaleCount());
        ptBookProductInfoList.add(ptBookProductInfo);
        ptCreateOrderRequest.setBookProductInfo(ptBookProductInfoList);
        if (VoucherTypesEnum.LOUNGECOUPON.getCode().equals(commonCouponProductInfo.getProductType())) {
            ptCreateOrderRequest.setDepAirportTerminal(commonCouponProductInfo.getAirportTerminal());
            ptCreateOrderRequest.setDepAirportCode(commonCouponProductInfo.getAirportLocation());
        }
        if (VoucherTypesEnum.ONBOARDWIFI.getCode().equals(commonCouponProductInfo.getProductType())) {
            ProductFlightInfo productFlightInfo = buyCouponProductRequest.getProductFlightInfo();
            ptCreateOrderRequest.setTktNo(productFlightInfo.getTktNo());
            ptCreateOrderRequest.setFlightNo(productFlightInfo.getFlightNo());
            ptCreateOrderRequest.setFlightDate(productFlightInfo.getFlightDate());
            ptCreateOrderRequest.setDepAirportCode(productFlightInfo.getDepAirportCode());
            ptCreateOrderRequest.setDepAirportTerminal(productFlightInfo.getDepTerminal());
            ptCreateOrderRequest.setArrAirportCode(productFlightInfo.getArrAirportCode());
            ptCreateOrderRequest.setArrAirportTerminal(productFlightInfo.getArrTerminal());
            ptCreateOrderRequest.setDepTime(productFlightInfo.getDepTime());
            ptCreateOrderRequest.setArrTime(productFlightInfo.getArrTime());
            ptCreateOrderRequest.setCabin(productFlightInfo.getCabin());
            ptCreateOrderRequest.setCertNo(productFlightInfo.getCertNo());
            ptCreateOrderRequest.setPassengerName(productFlightInfo.getTravellerName());
            //实际支付不等于0
            if (actualPaid.compareTo(BigDecimal.valueOf(0)) > 0) {
                throw new IllegalArgumentException("积分不足");
            }
        }
        return ptCreateOrderRequest;
    }

    /**
     * 24小时制转换成12小时
     *
     * @param time
     * @return
     */
    private String transferTime(String time) {
        if (Integer.parseInt(time.substring(0, 2)) <= 12) {
            return time + "am";
        } else if (Integer.parseInt(time.substring(0, 2)) < 22) {
            return ("0" + (Integer.parseInt(time.substring(0, 2)) - 12) + time.substring(2) + "pm");
        } else {
            return ((Integer.parseInt(time.substring(0, 2)) - 12) + time.substring(2) + "pm");
        }
    }

    /**
     * 判断是否启用新产品:预计22年6月10日上线
     */
    @ApiOperation(value = "预付费行李-是否启用新产品", notes = "现供额外行李首页购买须知使用")
    @RequestMapping(value = "/checkUseNewBaggageProduct", method = RequestMethod.GET)
    public BaseResp<CheckUseNewBaggageProductRespDto> checkUseNewBaggageProduct() {
        BaseResp<CheckUseNewBaggageProductRespDto> resp = new BaseResp<>();
        boolean useNewBaggageProduct = iExtraBaggageService.whetherUesNewBaggageProduct();
        resp.setObjData(CheckUseNewBaggageProductRespDto.builder()
                .useNewBaggageProduct(useNewBaggageProduct)
                .build());
        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
        resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
        return resp;
    }


    /**
     * 此接口有复用。1查询行李价格入口 2.购买页。建议拆分
     * <p>
     * 调用/Pdm/v2/smngAvailableP/findAvailableP 预付费行李
     *
     * @param reqParam
     * @param request
     * @return
     */
    @InterfaceLog
    @ApiOperation(value = "预付费行李-查询行李价格", notes = "根据出发地点、到达地、起飞时间查询")
    @RequestMapping(value = "queryPrePaymentBaggagePrice", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp<QueryPrePaymentBaggagePriceResp> queryPrePaymentBaggagePrice(@RequestBody BaseReq<QueryPrepaymentBaggagePriceReq> reqParam, HttpServletRequest request) {
        BaseResp<QueryPrePaymentBaggagePriceResp> resp = new BaseResp<>();
        QueryPrepaymentBaggagePriceReq req = reqParam.getRequest();
        String reqId = StringUtil.newGUID() + request.getRequestURI();
        String ip = this.getClientIP(request);
        try {

            ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
            Validator validator = factory.getValidator();
            Set<ConstraintViolation<BaseReq<QueryPrepaymentBaggagePriceReq>>> violations = validator.validate(reqParam);
            if (CollectionUtils.isNotEmpty(violations)) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo(violations.iterator().next().getMessage());
                return resp;
            }
            String userNo = getChannelInfo(reqParam.getChannelCode(), "10");

            if (ChannelCodeEnum.MWEB.getChannelCode().equals(reqParam.getChannelCode())) {
                reqParam.setChannelCode(ChannelCodeEnum.MOBILE.getChannelCode());
            }

            PtProductPrePayResponseDTO ptSearchResponse = buildAndQueryPrePaymentBaggagePrice(req, reqParam.getChannelCode(), userNo);
            if ("10001".equals(ptSearchResponse.getResultCode())) {
                List<PrePayProductInfo> productList = ptSearchResponse.getProductList();
                //配合预发布测试，生产需要屏蔽新的行李产品
                LocalDateTime localDateTime = LocalDateTime.now();
                if (CollectionUtils.isNotEmpty(productList)) {
                    if (CollectionUtils.isNotEmpty(handConfig.getOldProductList())) {
                        //产品切换之前只保留旧的,时间点后删除旧的产品
                        if (localDateTime.isBefore(DateUtils.toLocalDateTime(handConfig.getNewBaggageProductEnableTime(), DateUtils.YYYY_MM_DD_HH_MM_SS_PATTERN))) {
                            productList = productList.stream().filter(prePayProductInfo -> handConfig.getOldProductList().contains(prePayProductInfo.getResourceInfo().getResourceId())).collect(Collectors.toList());
                        } else {
                            productList.removeIf(prePayProductInfo -> handConfig.getOldProductList().contains(prePayProductInfo.getResourceInfo().getResourceId()));
                        }
                    }
                }
                if (StringUtil.isNullOrEmpty(productList)) {
                    resp.setResultCode(WSEnum.NO_DATA.getResultCode());
                    resp.setResultInfo("查询的航线暂不支持购买额外行李，请重新查询！");
                    return resp;
                }
                PrePayProductInfo prod = productList.get(0);
                if (CollectionUtils.isEmpty(prod.getProducts())) {
                    resp.setResultCode(WSEnum.NO_DATA.getResultCode());
                    resp.setResultInfo("查询的航线暂不支持购买额外行李，请重新查询！");
                    return resp;
                }
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());

                QueryPrePaymentBaggagePriceResp resData = new QueryPrePaymentBaggagePriceResp();
                // 不再校验产品端是否启用新产品
                    AtomicReference<PreProduct> unitProduct = new AtomicReference<>(new PreProduct());
                    // 根据"flightType": "INTL",区分国内国际行李
                    boolean isIntl = prod.getProducts().stream().anyMatch(product -> product.getFlightType().equals("INTL"));

                    if (isIntl) {
                        prod.getProducts().stream().filter(v -> v.getFlightType().equals("INTL")).findFirst().ifPresent(product -> unitProduct.set(product));
                        // 国际行李单价、单个行程限额1个
                        unitProduct.get().setPerOrderWeightLimit(1);
                        List<PreProduct> newProducts = this.intlUnitProductHandler(unitProduct.get());
                        resData.setProducts(newProducts);
                    } else {
                        prod.getProducts().stream().filter(v -> v.getRemark().contains("New")).findFirst().ifPresent(product -> unitProduct.set(product));
                        // 额外行李单价、单个订单限额(现均为统一30kg)
                        unitProduct.get().setPerOrderWeightLimit(30);
                        // 应前端需求此处由单价产品拆分为5KG/10KG/20KG/1KG
                        List<PreProduct> newProducts = this.splitTo3PresetProductsAndUnitProduct(unitProduct.get());
                        resData.setProducts(newProducts);
                    }

                    // 因行李价格入口复用，返回定价表
                    Optional.of(unitProduct).ifPresent(p -> {
                        String proFieldsSpecifications = p.get().getProFields().stream()
                                .filter(field -> "Specifications".equals(field.getFieldCode()))
                                .findFirst()
                                .map(ProField::getFieldValue)
                                .orElse(null);
                        String unit = "PC".equals(proFieldsSpecifications) ? "件" : "KG";
                        //
                        List<String> headRow = Arrays.asList("机场收费标准", "网站收费标准");
                        List<String> pricingRow = Arrays.asList(
                                "¥" + ("PC".equals(proFieldsSpecifications) ? "1000" : Math.round(p.get().getSalePrice() / 0.9)) + "/" + unit,
                                "¥" + Math.round(p.get().getSalePrice()) + "/" + unit
                        );
                        resData.setPreBaggagePricingTable(Arrays.asList(headRow, pricingRow));
                    });
                    // 设置是否使用新的预付费产品接口（区分产品端使用新产品）
                    if (iExtraBaggageService.whetherUesNewBaggageProduct()) {
                        resData.setUseNewBaggageProduct(true);
                    }

                resp.setObjData(resData);
                return resp;
            } else {
                resp.setResultCode(WSEnum.ERROR_QUERY_ERROR.getResultCode());
                resp.setResultInfo("预付费行李价格查询失败");
                return resp;
            }
        } catch (Exception e) {
            this.logError(resp, reqId, ip, reqParam, e, "查询预付费行李价格异常");
        }
        return resp;
    }



    /**
     * 校验Remark字段判断产品端是否存在新的预付费产品
     * <p>
     * 因uat 可能新老产品共存，存在Remark:New产品即校验通过
     * <p>
     * e.g.
     * remark:"New"
     * remark:"Old"
     *
     * @param product
     * @return
     */
    private boolean checkWhetherNewPreProduct(PrePayProductInfo product) {
        return product.getProducts().stream().anyMatch(v -> v.getRemark() != null ? v.getRemark().contains("New") : false);
    }

    /**
     * 由单价1KG行李预付费产品拆分为 5KG/10KG/20KG/1KG产品供前端适配
     * <p>
     * 仅处理价格
     *
     * @param origin
     * @return
     */
    private List<PreProduct> splitTo3PresetProductsAndUnitProduct(PreProduct origin) {
        List<PreProduct> res = new ArrayList<>();
        List<Integer> presetKG = Arrays.asList(5, 10, 20);
        for (int i = 1; i < 4; i++) {
            PreProduct presetProduct = new PreProduct();
            BeanUtils.copyProperties(origin, presetProduct);
            // 现前端根据ProductName区分 5KG/10KG/20KG
            presetProduct.setProductName(presetKG.get(i - 1) + "KG");
            res.add(presetProduct);
        }
        // 现前端根据UnitPriceProduct区分 1KG
        origin.setUnitPriceProduct(true);
        res.add(origin);
        return res;
    }


    private List<PreProduct> intlUnitProductHandler(PreProduct origin) {
        PreProduct presetProduct = new PreProduct();
        BeanUtils.copyProperties(origin, presetProduct);
        presetProduct.setProductName("1PC");
        presetProduct.setUnitPriceProduct(false);
        return Collections.singletonList(presetProduct);
    }

    /**
     * 判断匹配额外行李单价
     * 优先flightBasic航段距离接口,无数据则使用查询的产品匹配
     *
     * @param ip
     * @param channelCode
     * @param depAirportCode
     * @param arrAirportCode
     * @param preProduct
     * @return
     */
    private Pricing handlerExtraBaggagePrice(String ip, String channelCode, String depAirportCode, String arrAirportCode, PreProduct preProduct) {
        Segment segment = new Segment();
        segment.setArrAirportCode(arrAirportCode);
        segment.setDepAirportCode(depAirportCode);
        List<Segment> segmentList = Arrays.asList(segment);
        // 航段距离
        FlightDistanceDTO flightDistanceDTO = basicService.fetchFlightDistance(channelCode, ip, segmentList);
        return flightDistanceDTO == null ?
                iExtraBaggageService.matchExtraBaggagePricingByOnLinePrice(preProduct.getSalePrice()) :
                iExtraBaggageService.matchExtraBaggagePricingByMileage(flightDistanceDTO.getMileage().intValue());
    }

    @InterfaceLog
    @ApiOperation(value = "预付费行李-下单")
    @RequestMapping(value = "prePayBaggageCreateOrder", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp<PrePayBaggageCreateOrderResp> prePayBaggageCreateOrder(@RequestBody @Validated BaseReq<PrePayBaggageCreateOrderReq> reqParam, BindingResult bindingResult, HttpServletRequest request) {
        BaseResp<PrePayBaggageCreateOrderResp> resp = new BaseResp<>();
        PrePayBaggageCreateOrderReq req = reqParam.getRequest();
        String reqId = StringUtil.newGUID() + request.getRequestURI();
        String ip = this.getClientIP(request);
        if (bindingResult.hasErrors()) {
            resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setResultInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
            return resp;
        }

        // 脱敏信息
        String certRequest = reqParam.getRequest().getSubCouponOrderList().get(0).getBaggagePassengerInfo().getIdNo();
        if (certRequest.contains("*")) {
            String info = apiRedisService.getData(RedisKeyConfig.createUpIdInfo(reqParam.getRequest().getSubCouponOrderList().get(0).getTicketNumber()));
            if (StringUtils.isBlank(info)) {
                throw new ServiceException("操作超时，请返回重新查询下单");
            }
            reqParam.getRequest().getSubCouponOrderList().get(0).getBaggagePassengerInfo().setIdNo(info);
        }

        //
        try {
            //验证用户查询是否正常
            if (ChannelCodeEnum.MWEB.getChannelCode().equalsIgnoreCase(reqParam.getChannelCode())) {
                reqParam.setChannelCode(ChannelCodeEnum.MOBILE.getChannelCode());
            }
            String userNo = getChannelInfo(reqParam.getChannelCode(), "10");
            boolean flag = this.checkKeyInfo(req.getFfpId(), req.getLoginKeyInfo(), reqParam.getChannelCode());
            if (!flag) {
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                return resp;
            }
            boolean isIntl = req.getSubCouponOrderList().get(0).getBaggageProduct().getFlightType().equals("INTL");
            if (!isIntl) {
                //越时间点未下单新产品
                if (iExtraBaggageService.whetherUesNewBaggageProduct() && !req.getSubCouponOrderList().get(0).getBaggageProduct().getRemark().contains("New")) {
                    resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                    resp.setResultInfo("运价错误，建议回到额外行李首页重新购买");
                    return resp;
                }
            }
            ClientInfo clientInfo = initClientInfo(request, reqParam.getChannelCode(), req.getFfpId(), req.getFfpCardNo());
            //积分校验
            if (req.getUseTotalScore() > 0) {
                AccountCheckParam accountCheckParam = new AccountCheckParam();
                accountCheckParam.setFfpId(String.valueOf(req.getFfpId()));
                accountCheckParam.setFfpCardNo(req.getFfpCardNo());
                accountCheckParam.setOperation(SensitiveOperationEnum.USE_SALE.getOperation());
                accountCheckParam.setBlackBox(req.getBlackBox());
                memberPasswordService.checkAccount(clientInfo, accountCheckParam, null, null);
                //积分使用规则检验
                if (orderService.checkScoreUseRule(clientInfo, reqParam.getChannelCode())) {
                    throw new CommonException(WSEnum.NO_REAL_NAME.getResultCode(), "非常抱歉，当前账户存在安全风险，通过实名认证后才可使用积分，请前往实名认证");
                }
                //计算总金额
                List<BaggageProductSegmentInfo> subCouponOrderList = req.getSubCouponOrderList();
                double allPrice = 0;
                // 新旧产品积分处理/

                for (BaggageProductSegmentInfo baggageProductSegmentInfo : subCouponOrderList) {
                    PreProduct baggageProduct = baggageProductSegmentInfo.getBaggageProduct();
                    BigDecimal salePrice = BigDecimal.valueOf(baggageProduct.getSalePrice());
                    BigDecimal count = BigDecimal.valueOf(baggageProduct.getCount());
                    allPrice += salePrice.multiply(count).doubleValue();
                }

                //产品总金额
                BigDecimal totalAmount = BigDecimal.valueOf(allPrice);
                if (new BigDecimal(req.getUseTotalScore()).compareTo(totalAmount) > 0) {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo("积分金额大于订单总金额");
                    return resp;
                }

                //验证消费密码
                resp = orderService.checkFreeScoreLimit(req.getFfpCardNo(),
                        req.getFfpId(),
                        reqParam.getChannelCode(),
                        getChannelInfo(reqParam.getChannelCode(), "40"),
                        req.getUseTotalScore(),
                        req.getSalePwd(), request);
                if (!WSEnum.SUCCESS.getResultCode().equals(resp.getResultCode())) {
                    return resp;
                }
            }

            PtPrePayCreateOrderRequestDTO ptPrePayCreateOrderRequestDTO = new PtPrePayCreateOrderRequestDTO();
            PtBasicCreateOrderRequest ptBasicCreateOrderRequest = new PtBasicCreateOrderRequest();
            ptBasicCreateOrderRequest.setRequestIp(ip);
            ptBasicCreateOrderRequest.setChannelOrderNo(req.getChannelOrderNo());
            ptBasicCreateOrderRequest.setTotalAmount(req.getTotalAmount());
            ptBasicCreateOrderRequest.setCurrency(req.getCurrency());
            ptBasicCreateOrderRequest.setUseTotalScore(req.getUseTotalScore());
            ptBasicCreateOrderRequest.setPhoneCountryCode(req.getPhoneCountryCode());
            ptBasicCreateOrderRequest.setLinker(req.getLinker());
            ptBasicCreateOrderRequest.setFlightType(req.getRouteType());
            ptBasicCreateOrderRequest.setInterFlag(req.getInterFlag());
            ptBasicCreateOrderRequest.setPhoneNo(req.getPhoneNo());

            //处理产品信息:
            List<BaggageProductSegmentInfo> subCouponOrderList = req.getSubCouponOrderList();
            List<PtSubCouponOrder> subCouponOrders = copySubCouponOrders(subCouponOrderList);
            ptBasicCreateOrderRequest.setSubCouponOrderList(subCouponOrders);

            ptBasicCreateOrderRequest.setRequestIp(ip);
            ptPrePayCreateOrderRequestDTO.setVersion(HandlerConstants.VERSION)
                    .setChannelCode(reqParam.getChannelCode())
                    .setUserNo(userNo)
                    .setFfpId(req.getFfpId())
                    .setFfpCardNo(req.getFfpCardNo())
                    .setRequest(ptBasicCreateOrderRequest);

            HttpResult result = HttpUtil.doPostClient(ptPrePayCreateOrderRequestDTO, HandlerConstants.URL_COUPON_API + HandlerConstants.QUERY_PREPAYBAGGAGE_CREATEORDER);
            if (!result.isResult()) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("远程服务错误");
                return resp;
            }
            PtPrePayCreateOrderResponsetDTO ptPrePayCreateOrderResponsetDTO = (PtPrePayCreateOrderResponsetDTO) JsonUtil.jsonToBean(result.getResponse(), PtPrePayCreateOrderResponsetDTO.class);
            if ("10001".equals(ptPrePayCreateOrderResponsetDTO.getResultCode())) {
                PrePayBaggageCreateOrderResp prePayBaggageCreateOrderResp = new PrePayBaggageCreateOrderResp();
                PtBasicCreateOrderResponse response = ptPrePayCreateOrderResponsetDTO.getResult();
                prePayBaggageCreateOrderResp.setOrderId(response.getOrderId());
                prePayBaggageCreateOrderResp.setOrderNo(response.getOrderNo());
                prePayBaggageCreateOrderResp.setChannelOrderNo(response.getOrderChannelOrderNo());
                prePayBaggageCreateOrderResp.setCreateAt(response.getCreateAt());
                prePayBaggageCreateOrderResp.setStatus(response.getStatus());
                //判断是否为零元支付
                if (req.getTotalAmount().compareTo(BigDecimal.ZERO) == 0) {
                    prePayBaggageCreateOrderResp.setIsZeroPay(true);
                }

                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                resp.setObjData(prePayBaggageCreateOrderResp);
                return resp;
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("您的机票状态异常，暂无法购买额外行李");
                return resp;
            }
        } catch (CommonException commonException) {
            throw commonException;
        } catch (ServiceException serviceException) {
            throw serviceException;
        } catch (Exception e) {
            this.logError(resp, reqId, ip, reqParam, e, "预付费行李下单异常");
        }
        return resp;
    }


    /**
     * 端上请求参数封装为/Pdm/basicOrder/create请求参数
     *
     * @param subCouponOrderList
     * @return
     */
    private List<PtSubCouponOrder> copySubCouponOrders(List<BaggageProductSegmentInfo> subCouponOrderList) {
        log.debug("copySubCouponOrders subCouponOrderList:{}", subCouponOrderList);
        List<PtSubCouponOrder> subCouponOrders = new ArrayList<>();
        for (BaggageProductSegmentInfo productSegmentInfo : subCouponOrderList) {
            PreProduct baggageProduct = productSegmentInfo.getBaggageProduct();
            BaggagePassengerInfo baggagePassengerInfo = productSegmentInfo.getBaggagePassengerInfo();
            PtSubCouponOrder ptSubCouponOrder = new PtSubCouponOrder();
            ptSubCouponOrder.setProductNum(baggageProduct.getProductNum());
            ptSubCouponOrder.setResourceType(baggageProduct.getProductType());
            ptSubCouponOrder.setProductName(baggageProduct.getProductName());
            ptSubCouponOrder.setStandardPrice(baggageProduct.getStandardPrice());
            ptSubCouponOrder.setSalePrice(baggageProduct.getSalePrice());

            // 使用BookingCount自定义购买重量
            ptSubCouponOrder.setBookingCount(baggageProduct.getCount());

            ptSubCouponOrder.setUseScore(0);
            ptSubCouponOrder.setCertNo(baggagePassengerInfo.getIdNo());
            ptSubCouponOrder.setCertType(baggagePassengerInfo.getIdType());

            log.debug("prePayBaggageCreateOrder rawName:{}", baggagePassengerInfo.getPassengerName());
            ptSubCouponOrder.setPassengerName(this.removePassengerNameBracket(baggagePassengerInfo.getPassengerName()));

            ptSubCouponOrder.setTktNo(productSegmentInfo.getTicketNumber().replace("-", ""));
            ptSubCouponOrder.setPnrNo(productSegmentInfo.getPnrNo());
            ptSubCouponOrder.setDepAirportCode(productSegmentInfo.getDepAirport());
            ptSubCouponOrder.setArrAirportCode(productSegmentInfo.getArrAirport());
            ptSubCouponOrder.setDepCity(productSegmentInfo.getDepCity());
            ptSubCouponOrder.setArrCity(productSegmentInfo.getArrCity());
            ptSubCouponOrder.setDepAirportTerminal(productSegmentInfo.getDepAirportTerminal());
            ptSubCouponOrder.setArrAirportTerminal(productSegmentInfo.getArrAirportTerminal());

            ptSubCouponOrder.setFlightNo(productSegmentInfo.getAirLineCode());
            ptSubCouponOrder.setFlightDate(productSegmentInfo.getFlightDate());
            ptSubCouponOrder.setDepTime(productSegmentInfo.getDepTime());
            ptSubCouponOrder.setArrTime(productSegmentInfo.getArrTime());
            ptSubCouponOrder.setPlaneType(productSegmentInfo.getPlaneType());

            ptSubCouponOrder.setCabin(productSegmentInfo.getCabin());

            ptSubCouponOrder.setTicketType(productSegmentInfo.getInterFlag());
            ptSubCouponOrder.setAirlineType(productSegmentInfo.getInterFlag());
//                ptSubCouponOrder.setSeatNos(); 不需要传
            ptSubCouponOrder.setTrip(productSegmentInfo.getFlightDirection());

            //
            if (productSegmentInfo.getCouponInfo() != null) {
                CouponInfo couponInfo = productSegmentInfo.getCouponInfo();
                com.juneyaoair.thirdentity.salecoupon.request.CouponInfo couponInfoForCreateOrder = new com.juneyaoair.thirdentity.salecoupon.request.CouponInfo();
                couponInfoForCreateOrder.setCouponNo(couponInfo.getCouponNo());
                couponInfoForCreateOrder.setCouponType(couponInfo.getCouponType());
                //
                ptSubCouponOrder.setCouponInfo(couponInfoForCreateOrder);
            }

            //日期
            ptSubCouponOrder.setDepFlightDate(productSegmentInfo.getDepDate());
            ptSubCouponOrder.setArrFlightDate(productSegmentInfo.getArrDate());


            subCouponOrders.add(ptSubCouponOrder);
        }
        log.debug("copySubCouponOrders subCouponOrders:{}", subCouponOrders);
        return subCouponOrders;
    }


    // 正则删除乘客姓名后括号内容

    /**
     * @param passengerName e.g. : "周渝楷 (UM9)" "CUI/CAN" "张三"
     * @return
     */
    public String removePassengerNameBracket(String passengerName) {
        String reg = "\\s*\\(+[A-Za-z0-9]+\\)+";
        Pattern pat = Pattern.compile(reg);
        Matcher mat = pat.matcher(passengerName);
        String replaceName = mat.replaceAll("");
        return replaceName;
    }

    @ApiOperation(value = "查询畅飞卡", notes = "查询畅飞卡")
    @RequestMapping(value = "/queryChangFlyHotPotCard", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @InterfaceLog
    public BaseResp<ChangflyResp> queryChangFlyHotPotCard(@RequestBody BaseReq<ChangflyReq> changflyBaseReq, HttpServletRequest request) {
        BaseResp<ChangflyResp> resp = new BaseResp<>();
        ChangflyReq req = changflyBaseReq.getRequest();
        String reqId = StringUtil.newGUID() + request.getRequestURI();
        String ip = this.getClientIP(request);
        try {
            Set<ConstraintViolation<BaseReq<ChangflyReq>>> validate = ValidatorUtils.validate(changflyBaseReq);
            if (CollectionUtils.isNotEmpty(validate)) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo(validate.iterator().next().getMessage());
                return resp;
            }
            //验证用户查询是否正常
            if (ChannelCodeEnum.MWEB.getChannelCode().equalsIgnoreCase(changflyBaseReq.getChannelCode())) {
                changflyBaseReq.setChannelCode(ChannelCodeEnum.MOBILE.getChannelCode());
            }
            boolean flag = this.checkKeyInfo(req.getFfpId(), req.getLoginKeyInfo(), changflyBaseReq.getChannelCode());
            if (!flag) {
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR_925.getResultCode());
                resp.setResultInfo(WSEnum.ERROR_CHK_ERROR_925.getResultInfo());
                return resp;
            }

            ChangflyrequestDTO changflyDTO = new ChangflyrequestDTO();
            changflyDTO.setFfpId(req.getFfpId());
            changflyDTO.setFfpCardNo(req.getFfpCardNo());
            changflyDTO.setThemeCouponTypeList(Collections.singletonList(req.getCardType()));
            changflyDTO.setVersion(changflyBaseReq.getVersionCode());
            changflyDTO.setChannelCode(changflyBaseReq.getChannelCode());
            HttpResult result = HttpUtil.doPostClient(changflyDTO, HandlerConstants.URL_COUPON_API + HandlerConstants.PRODUCT_THEME_COUPON_ACCOUNTR_EDEEM_INFO);
            ptChangFlyResponseDTO ChangflyResponse = (ptChangFlyResponseDTO) JsonUtil.jsonToBean(result.getResponse(), ptChangFlyResponseDTO.class);
            if (!result.isResult()) {
                resp.setResultCode(WSEnum.ERROR_925.getResultCode());
                resp.setResultInfo(WSEnum.ERROR_925.getResultInfo());
                return resp;
            }
            if ("10001".equals(ChangflyResponse.getResultCode())) {
                ChangflyResp changflyResp = ChangflyResponse.getRedeemDetails().get(0);
                if (!changflyResp.getThemeCouponType().equals(req.getCardType())) {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo("您还没有此类型畅飞卡");
                    return resp;
                }
                if (changflyResp.getAccountBindResult() != true) {
                    resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                    resp.setResultInfo("您还没有绑定");
                    resp.setObjData(changflyResp);
                    return resp;
                }
                /*if (changflyResp.getBoughtCount()==0){
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo("您还没有购买");
                    resp.setObjData(changflyResp);
                    return resp;
                }*/
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                resp.setObjData(changflyResp);
                return resp;
            } else {
                resp.setResultCode(WSEnum.ERROR_925.getResultCode());
                resp.setResultInfo(WSEnum.ERROR_925.getResultInfo());
                return resp;
            }
        } catch (Exception e) {
            this.logError(resp, reqId, ip, changflyBaseReq, e, "小吉开小差啦，请稍后再试");
        }
        return resp;
    }

    @ApiOperation(value = "查询绑定主题卡", notes = "查询绑定主题卡")
    @RequestMapping(value = "/queryBoundSubjectCards", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @InterfaceLog
    public BaseResp<ChangflyDTo> queryBoundSubjectCards(@RequestBody BaseReq<UserInfoMust> mustBaseReq, HttpServletRequest request) {
        BaseResp<ChangflyDTo> resp = new BaseResp<>();
        ChangflyDTo changflyDTo = new ChangflyDTo();
        UserInfoMust req = mustBaseReq.getRequest();
        String reqId = StringUtil.newGUID() + request.getRequestURI();
        String ip = this.getClientIP(request);
        try {
            Set<ConstraintViolation<UserInfoMust>> validate = ValidatorUtils.validate(req);
            if (CollectionUtils.isNotEmpty(validate)) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo(validate.iterator().next().getMessage());
                return resp;
            }
            //验证用户查询是否正常
            if (ChannelCodeEnum.MWEB.getChannelCode().equalsIgnoreCase(mustBaseReq.getChannelCode())) {
                mustBaseReq.setChannelCode(ChannelCodeEnum.MOBILE.getChannelCode());
            }
            boolean flag = this.checkKeyInfo(req.getFfpId(), req.getLoginKeyInfo(), mustBaseReq.getChannelCode());
            if (!flag) {
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                return resp;
            }

            ChangFeiCardBindingDTO feiCardBindingDTO = new ChangFeiCardBindingDTO();
            feiCardBindingDTO.setFfpId(req.getFfpId());
            feiCardBindingDTO.setFfpCardNo(req.getFfpCardNo());
            feiCardBindingDTO.setVersion(mustBaseReq.getVersionCode());
            feiCardBindingDTO.setChannelCode(mustBaseReq.getChannelCode());
            feiCardBindingDTO.setThemeCouponTypeList(handConfig.getThemeCouponList());
            HttpResult result = HttpUtil.doPostClient(feiCardBindingDTO, HandlerConstants.URL_COUPON_API + HandlerConstants.PRODUCT_THEME_COUPON_ACCOUNTR_EDEEM_INFO);
            ptChangFlyResponseDTO ChangflyResponse = (ptChangFlyResponseDTO) JsonUtil.jsonToBean(result.getResponse(), ptChangFlyResponseDTO.class);
            if (!result.isResult()) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("远程服务错误");
                return resp;
            }
            if ("10001".equals(ChangflyResponse.getResultCode())) {
                List<ChangflyResp> redeemDetails = ChangflyResponse.getRedeemDetails();
                List<ChangflyResp> collect = redeemDetails.stream()
                        .filter(s -> s.getBoughtCount() != 0)
                        .filter(s -> s.getBoughtTime() != null)
                        .collect(Collectors.toList());
                Collections.sort(collect, new Comparator<ChangflyResp>() {
                    @Override
                    public int compare(ChangflyResp o1, ChangflyResp o2) {
                        SimpleDateFormat format = new SimpleDateFormat("");
                        try {
                            Date parse1 = format.parse(o1.getBoughtTime());
                            Date parse2 = format.parse(o2.getBoughtTime());
                            if (parse1.getTime() < parse2.getTime()) {
                                return -1;
                            } else if (parse1.getTime() > parse2.getTime()) {
                                return 1;
                            } else {
                                return 0;
                            }
                        } catch (Exception e) {
                            logError(resp, reqId, ip, mustBaseReq, e, "查询主题卡异常");
                        }
                        return 0;
                    }
                });
                //多次卡,app不进行弹框
//                for (ChangflyResp redeemDetail : collect) {
//                    //if (redeemDetail.getBoughtCount()!= 0){
//                    if (redeemDetail.getAccountBindResult() != true) {
//                        String themeCouponType = redeemDetail.getThemeCouponType();
//                        Map<String, ThemeCoupon> themeCouponMap = toThemeModelMap(handConfig.getThemeCabinLabel());
//                        redeemDetail.setThemeCouponType(themeCouponMap == null ? THEME_CARD_NAME : themeCouponMap.get(themeCouponType).getThemeName());
//                        redeemDetail.setSkipLinks("/hybrid/interestTicket/ticketList/index.html?titlestyle=4&title=权益券&navRightItem=2&navRightTitle=兑换");
//                        changflyDTo.setThemeCouponType(redeemDetail.getThemeCouponType());
//                        changflyDTo.setSkipLinks(redeemDetail.getSkipLinks());
//                        resp.setObjData(changflyDTo);
//                        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
//                        resp.setResultInfo("您的主题卡未绑定，请先绑定");
//                        return resp;
//                    }
//                    resp.setResultCode(WSEnum.SUCCESS.getResultCode());
//                    resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
//                    //}
//                }
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setResultInfo("您还没有主题卡");
                return resp;
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo(ChangflyResponse.getErrorInfo());
                return resp;
            }
        } catch (Exception e) {
            this.logError(resp, reqId, ip, mustBaseReq, e, "查询主题卡异常");
        }
        return resp;
    }

    /**
     * 主题卡集合
     *
     * @param themeStr
     * @returnBEGIN_OBJECT
     */
    public static Map<String, ThemeCoupon> toThemeModelMap(String themeStr) {
        try {
            Type type = new TypeToken<Map<String, ThemeCoupon>>() {
            }.getType();
            return (Map<String, ThemeCoupon>) JsonUtil.jsonToMap(themeStr, type);
        } catch (Exception e) {
            return null;
        }
    }


    /**
     * 构建并执行预付费行李价格查询
     * 
     * @param req 预付费行李价格查询请求参数
     * @param channelCode 渠道编码
     * @param userNo 用户编号
     * @return 预付费行李价格查询响应
     */
    private PtProductPrePayResponseDTO buildAndQueryPrePaymentBaggagePrice(QueryPrepaymentBaggagePriceReq req, String channelCode, String userNo) {
        PtProductPrePayRequestDTO ptProductPrePayRequestDTO = new PtProductPrePayRequestDTO();
        SingleBookCondition singleBookCondition = new SingleBookCondition();
        singleBookCondition.setDepAirportCode(req.getDepAirportCode());
        singleBookCondition.setArrAirportCode(req.getArrAirportCode());
        singleBookCondition.setDepCity(req.getDepCity());
        singleBookCondition.setArrCity(req.getArrCity());
        // 添加航班起飞时间
        singleBookCondition.setFlightDate(req.getDepDate() + " 00:00:00");
        
        ptProductPrePayRequestDTO.setVersion(HandlerConstants.VERSION)
                .setChannelCode(channelCode)
                .setUserNo(userNo)
                .setSearchTypes(VoucherTypesEnum.EXTRABAGGAGE.getCode())
                .setSingleBookCondition(singleBookCondition);

        return queryPrePaymentBaggagePriceFromRemote(ptProductPrePayRequestDTO);
    }

    /**
     * 执行预付费行李价格远程查询
     * 
     * @param ptProductPrePayRequestDTO 预付费行李价格查询请求DTO
     * @return 预付费行李价格查询响应
     * @throws RuntimeException 当远程调用失败或响应解析失败时抛出
     */
    private PtProductPrePayResponseDTO queryPrePaymentBaggagePriceFromRemote(PtProductPrePayRequestDTO ptProductPrePayRequestDTO) {
        HttpResult result = HttpUtil.doPostClient(ptProductPrePayRequestDTO, HandlerConstants.URL_COUPON_API + HandlerConstants.QUERY_PREPAYBAGGAGE_PRICE);
        PtProductPrePayResponseDTO ptSearchResponse = (PtProductPrePayResponseDTO) JsonUtil.jsonToBean(result.getResponse(), PtProductPrePayResponseDTO.class);
        return ptSearchResponse;
    }

    /**
     * 判断是否为中转联程航班
     * 1. 按照起飞时间对航段排序
     * 2. 比较首末航段的出发/到达城市是否相同
     * 
     * @param ticketInfo 航段信息列表
     * @return true:中转联程航班 false:非中转联程航班
     */
    private boolean checkConnectingFlight(List<TicketInfoQueryResponse> ticketInfo) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-ddHH:mm");
        // 同票号按日期时间排序
        List<TicketInfoQueryResponse> sortedTicketInfo = ticketInfo.stream()
            .sorted((v1, v2) -> {
                try {
                    return sdf.parse(v1.getSegment_depa_date() + v1.getSegment_depa_time())
                            .compareTo(sdf.parse(v2.getSegment_depa_date() + v2.getSegment_depa_time()));
                } catch (ParseException e) {
                    throw new RuntimeException(e);
                }
            }).collect(Collectors.toList());

        TicketInfoQueryResponse firstSegment = sortedTicketInfo.get(0);
        TicketInfoQueryResponse lastSegment = sortedTicketInfo.get(sortedTicketInfo.size() - 1);
        AirPortInfoDto firstDepAirport = localCacheService.getLocalAirport(firstSegment.getSegment_depa_airportcode());
        AirPortInfoDto lastArrAirport = localCacheService.getLocalAirport(lastSegment.getSegment_arrival_airportcode());
        
        // 航段数大于1且首末航段的出发/到达城市不同,则为中转联程航班
        return !firstDepAirport.getCityCode().equals(lastArrAirport.getCityCode());
    }
}

package com.juneyaoair.mobile.handler.controller;

import com.juneyaoair.appenum.*;
import com.juneyaoair.appenum.member.CertificateTypeEnum;
import com.juneyaoair.baseclass.basicsys.response.AirPortInfoDto;
import com.juneyaoair.baseclass.common.request.BaseReq;
import com.juneyaoair.baseclass.common.response.BaseResp;
import com.juneyaoair.baseclass.member.response.MemberContactInfo;
import com.juneyaoair.baseclass.specialservice.request.ChildApplyQuotaCheck;
import com.juneyaoair.baseclass.specialservice.request.*;
import com.juneyaoair.baseclass.specialservice.response.*;
import com.juneyaoair.mobile.aop.annotation.InterfaceLog;
import com.juneyaoair.mobile.handler.HandlerConstants;
import com.juneyaoair.mobile.handler.service.IBasicService;
import com.juneyaoair.thirdentity.specialservice.request.TfSpecialServiceOnePicRequest;
import com.juneyaoair.thirdentity.specialservice.request.TfSpecialServiceQueryRequest;
import com.juneyaoair.thirdentity.specialservice.request.TfSpecialServiceQueryRequestDTO;
import com.juneyaoair.thirdentity.specialservice.response.TfSpecialServiceApplyResponse;
import com.juneyaoair.thirdentity.specialservice.response.TfSpecialServiceOnePicResponse;
import com.juneyaoair.thirdentity.specialservice.response.TfSpecialServiceQueryResponse;
import com.juneyaoair.thirdentity.specialservice.response.TfSpecialServiceQueryResponseDTO;
import com.juneyaoair.utils.EncoderHandler;
import com.juneyaoair.utils.StringUtil;
import com.juneyaoair.utils.http.HttpResult;
import com.juneyaoair.utils.json.JsonUtil;
import com.juneyaoair.utils.util.BeanUtils;
import com.juneyaoair.utils.util.DateUtils;
import com.juneyaoair.utils.util.MdcUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * @Auther: lizongjie
 * @Date: 2018/11/19 13:21
 * @Description:
 */
@RequestMapping("/specialServiceList")
@RestController
@Api(value = "SpecialServiceApplyQueryController", description = "特殊旅客服务申请列表")
public class SpecialServiceApplyQueryController extends BassController {
    @Autowired
    private IBasicService basicService;

    private final String from = "B2C";
    private final String systemCode = "B2C001";
    private final String MD5PassWord = StringUtil.encodePwd("e23b4f33c231302aa437733de354b14d");
    private final String queryAllApply = "queryAllApply"; // 我的申请列表查询
    private final String cancelApply = "cancelApply"; // 取消申请
    private final String queryApplyByIds = "queryApplyByIds"; // 详情
    private final String updateApplyById = "updateApplyById"; // 详情页新增图片
    private final String url = HandlerConstants.URL_PASSAGER_API + HandlerConstants.SPE_SERVICE_APPLY;
    public static final List<String> recordStatus = Arrays.asList("1","2","3");
    //我的申请列表
    @Deprecated
    @ApiOperation(value = "queryAllApply", notes = "我的申请")
    @RequestMapping(value = "/queryAllApply", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp queryAllApply(@RequestBody BaseReq<TfSearch> req, HttpServletRequest request) {
        String reqId = StringUtil.newGUID() + "_queryAllApply";
        BaseResp resp = new BaseResp();
        String ip = this.getClientIP(request);

        try {
            log.info(COMMON_LOG_WITH_REQ_INFO,reqId, ip , JsonUtil.objectToJson(req));
            ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
            Validator validator = factory.getValidator();
            Set<ConstraintViolation<BaseReq>> violations = validator.validate(req);
            if (null != violations && violations.size() > 0) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo(violations.iterator().next().getMessage());
                log.info(COMMON_LOG_WITH_RESP_INFO,reqId, ip , JsonUtil.objectToJson(resp));
                return resp;
            }
            //验证用户查询是否正常
            TfSearch tfSearch = req.getRequest();
            boolean flag = this.checkKeyInfo(tfSearch.getFfpId(), tfSearch.getLoginKeyInfo(), req.getChannelCode());
            if (!flag) {
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                log.info(COMMON_LOG_WITH_RESP_INFO,reqId, ip , JsonUtil.objectToJson(resp));
                return resp;
            }
            //封装请求头
            NewReqHeader header = builderHeader(reqId);
            //封装请求体
            TfSpecialServiceQueryRequest tfSpecialServiceQueryRequest = new TfSpecialServiceQueryRequest();
            tfSpecialServiceQueryRequest.setInftCode(queryAllApply);
            tfSpecialServiceQueryRequest.setMemberId(tfSearch.getFfpId());
            TfSpecialServiceQueryRequestDTO tfSpecialServiceQueryRequestDTO = new TfSpecialServiceQueryRequestDTO();
            tfSpecialServiceQueryRequestDTO.setStartTime(tfSearch.getStartTime());
            tfSpecialServiceQueryRequestDTO.setEndTime(tfSearch.getEndTime());
            tfSpecialServiceQueryRequestDTO.setApplyStatus(tfSearch.getApplyStatus());
            tfSpecialServiceQueryRequest.setData(tfSpecialServiceQueryRequestDTO);
            log.info("给旅客服务网发送的请求体：{},请求地址：{},给旅客服务网发送请求时间：{}", JsonUtil.objectToJson(tfSpecialServiceQueryRequest),url,System.currentTimeMillis());
            //向旅客服务网发送请求
            HttpResult serviceResult = doPost2PassengerService(tfSpecialServiceQueryRequest, url, header);
            if (null != serviceResult && serviceResult.isResult()) {
                log.info("旅客服务网响应结果：{},旅客服务网响应时间：{}", serviceResult.getResponse(),System.currentTimeMillis());
                TfSpecialServiceQueryResponse tfSpecialServiceQueryResponse = (TfSpecialServiceQueryResponse) JsonUtil.jsonToBean(serviceResult.getResponse(), TfSpecialServiceQueryResponse.class);
                if (tfSpecialServiceQueryResponse.getCode() == 0) {
                    if (!StringUtil.isNullOrEmpty(tfSpecialServiceQueryResponse.getData().getDataList())) {
                        Map<String, AirPortInfoDto> airPortInfoMap = basicService.queryAllAirportMap(ChannelCodeEnum.MOBILE.getChannelCode(),ip);
                        List<TfBriefServiceInfo> tfBriefServiceInfoList = new ArrayList<>();
                        List<TfSpecialServiceQueryResponseDTO> dataList = tfSpecialServiceQueryResponse.getData().getDataList();
                        for (TfSpecialServiceQueryResponseDTO tfSpecialServiceQueryResponseDTO : dataList) {
                            String weekDay = "";
                            Date flightDate = DateUtils.toDate(tfSpecialServiceQueryResponseDTO.getFlightDate(), "yyyy-MM-dd");
                            if (flightDate != null) {
                                weekDay = DateUtils.getWeekStr(flightDate);
                            }
                            AirPortInfoDto depAirPortInfo = airPortInfoMap.get(tfSpecialServiceQueryResponseDTO.getDepatureStation()); // 始发站
                            AirPortInfoDto desAirPortInfo = airPortInfoMap.get(tfSpecialServiceQueryResponseDTO.getDestinationStation()); // 到达站
                            //申请列表
                            TfBriefServiceInfo tfBriefServiceInfo = new TfBriefServiceInfo();
                            BeanUtils.copyNotNullProperties(tfSpecialServiceQueryResponseDTO, tfBriefServiceInfo);
                            tfSpecialServiceQueryResponseDTO.getSpeServiceType();
                            NewSpecialServiceTypeEnum newSpecialServiceTypeEnum = NewSpecialServiceTypeEnum.getDesc(tfSpecialServiceQueryResponseDTO.getSpeServiceType());
                            tfBriefServiceInfo.setServiceName(newSpecialServiceTypeEnum == null ? "" : (newSpecialServiceTypeEnum.desc == null ? "" : newSpecialServiceTypeEnum.desc));
                            tfBriefServiceInfo.setWeekDay(weekDay);
                            if (1 == NumberUtils.toInt(tfSpecialServiceQueryResponseDTO.getIsCancel())) {
                                tfBriefServiceInfo.setApplyStatusName("已取消");
                                tfBriefServiceInfo.setRecordStatus("5");
                                tfSpecialServiceQueryResponseDTO.setRecordStatus("5");
                            }
                            tfBriefServiceInfo.setApplyStatusName(tranState(tfSpecialServiceQueryResponseDTO.getRecordStatus(), tfSpecialServiceQueryResponseDTO.getSpeServiceType()));
                            tfBriefServiceInfo.setDepartureStationName(depAirPortInfo == null ? "" : (depAirPortInfo.getCityName() == null ? "" : depAirPortInfo.getCityName()));
                            tfBriefServiceInfo.setDestinationStationName(desAirPortInfo == null ? "" : (desAirPortInfo.getCityName() == null ? "" : desAirPortInfo.getCityName()));
                          /*  //详情
                            TfDetailsQueryInfo details = new TfDetailsQueryInfo();
                            BeanUtils.copyProperties(tfSpecialServiceQueryResponseDTO,details);
                            details.setDestinationStationName(desAirPortInfo == null ? "" : (desAirPortInfo.getCityName() == null ? "" : desAirPortInfo.getCityName()));
                            details.setDepartureStationName(depAirPortInfo == null ? "" : (depAirPortInfo.getCityName() == null ? "" : depAirPortInfo.getCityName()));*/
                            //服务进度
                            List<TfScheduleQueryInfo> scheduleList = new ArrayList<>();
                            TfScheduleQueryInfo schedule = new TfScheduleQueryInfo();
                            if("1".equals(tfSpecialServiceQueryResponseDTO.getServiceStatus())){
                                //已申请
                                schedule.setApplyTime(tfSpecialServiceQueryResponseDTO.getApplyTime());
                                schedule.setApplyService("特服保障");
                                schedule.setApplyServiceSuccess("已申请");
                                scheduleList.add(schedule);
                            }else if("2".equals(tfSpecialServiceQueryResponseDTO.getServiceStatus())){
                                //已值机
                                schedule.setApplyTime(tfSpecialServiceQueryResponseDTO.getApplyTime());
                                schedule.setApplyService("特服保障");
                                schedule.setApplyServiceSuccess("已申请");
                                schedule.setCheckInTime(tfSpecialServiceQueryResponseDTO.getCheckInTime());
                                schedule.setSpeServiceGuaranteeOne("特服保障");
                                schedule.setCheckedIn("已值机");
                                scheduleList.add(schedule);
                            }else if( "3".equals(tfSpecialServiceQueryResponseDTO.getServiceStatus())){
                                //保障开始
                                schedule.setApplyTime(tfSpecialServiceQueryResponseDTO.getApplyTime());
                                schedule.setApplyService("特服保障");
                                schedule.setApplyServiceSuccess("已申请");
                                schedule.setCheckInTime(tfSpecialServiceQueryResponseDTO.getCheckInTime());
                                schedule.setSpeServiceGuaranteeOne("特服保障");
                                schedule.setCheckedIn("已值机");
                                schedule.setGroundSupportStartTime(tfSpecialServiceQueryResponseDTO.getGroundSupportStartTime());
                                schedule.setSpeServiceGuaranteeTwo("特服保障");
                                schedule.setSpeServiceStart("开始");
                                scheduleList.add(schedule);
                            }else if("4".equals(tfSpecialServiceQueryResponseDTO.getServiceStatus())){
                                //保障结束
                                schedule.setApplyTime(tfSpecialServiceQueryResponseDTO.getApplyTime());
                                schedule.setApplyService("特服保障");
                                schedule.setApplyServiceSuccess("已申请");
                                schedule.setCheckInTime(tfSpecialServiceQueryResponseDTO.getCheckInTime());
                                schedule.setSpeServiceGuaranteeOne("特服保障");
                                schedule.setCheckedIn("已值机");
                                schedule.setGroundSupportStartTime(tfSpecialServiceQueryResponseDTO.getGroundSupportStartTime());
                                schedule.setSpeServiceGuaranteeTwo("特服保障");
                                schedule.setSpeServiceStart("开始");
                                schedule.setGroundSupportEndTime(tfSpecialServiceQueryResponseDTO.getGroundSupportEndTime());
                                schedule.setSpeServiceGuaranteeThree("特服保障");
                                schedule.setSpeServiceEnd("结束");
                                scheduleList.add(schedule);
                            }else if("5".equals(tfSpecialServiceQueryResponseDTO.getServiceStatus())){
                                //已登机
                                schedule.setApplyTime(tfSpecialServiceQueryResponseDTO.getApplyTime());
                                schedule.setApplyService("特服保障");
                                schedule.setApplyServiceSuccess("已申请");
                                schedule.setCheckInTime(tfSpecialServiceQueryResponseDTO.getCheckInTime());
                                schedule.setSpeServiceGuaranteeOne("特服保障");
                                schedule.setCheckedIn("已值机");
                                schedule.setGroundSupportStartTime(tfSpecialServiceQueryResponseDTO.getGroundSupportStartTime());
                                schedule.setSpeServiceGuaranteeTwo("特服保障");
                                schedule.setSpeServiceStart("开始");
                                schedule.setGroundSupportEndTime(tfSpecialServiceQueryResponseDTO.getGroundSupportEndTime());
                                schedule.setSpeServiceGuaranteeThree("特服保障");
                                schedule.setSpeServiceEnd("结束");
                                schedule.setBoardingTime(tfSpecialServiceQueryResponseDTO.getBoardingTime());
                                schedule.setSpeServiceGuaranteeFour("特服保障");
                                schedule.setBoarded("已登机");
                                scheduleList.add(schedule);
                            }else if("6".equals(tfSpecialServiceQueryResponseDTO.getServiceStatus())){
                                //已抵达
                                schedule.setApplyTime(tfSpecialServiceQueryResponseDTO.getApplyTime());
                                schedule.setApplyService("特服保障");
                                schedule.setApplyServiceSuccess("已申请");
                                schedule.setCheckInTime(tfSpecialServiceQueryResponseDTO.getCheckInTime());
                                schedule.setSpeServiceGuaranteeOne("特服保障");
                                schedule.setCheckedIn("已值机");
                                schedule.setGroundSupportStartTime(tfSpecialServiceQueryResponseDTO.getGroundSupportStartTime());
                                schedule.setSpeServiceGuaranteeTwo("特服保障");
                                schedule.setSpeServiceStart("开始");
                                schedule.setGroundSupportEndTime(tfSpecialServiceQueryResponseDTO.getGroundSupportEndTime());
                                schedule.setSpeServiceGuaranteeThree("特服保障");
                                schedule.setSpeServiceEnd("结束");
                                schedule.setBoardingTime(tfSpecialServiceQueryResponseDTO.getBoardingTime());
                                schedule.setSpeServiceGuaranteeFour("特服保障");
                                schedule.setBoarded("已登机");
                                if(StringUtils.isNotBlank(tfSpecialServiceQueryResponseDTO.getArrivalTime())){
                                    schedule.setArrivalTime(tfSpecialServiceQueryResponseDTO.getArrivalTime());

                                }else{
                                    if(StringUtils.isNotBlank(tfSpecialServiceQueryResponseDTO.getArrGuaStartTime())){
                                        schedule.setArrivalTime(tfSpecialServiceQueryResponseDTO.getArrGuaStartTime());
                                    }
                                }
                                schedule.setSpeServiceGuaranteeFive("特服保障");
                                schedule.setArrived("已抵达");
                                scheduleList.add(schedule);
                            }
                            //tfBriefServiceInfo.setDetails(details);
                            tfBriefServiceInfo.setSchedule(scheduleList);
                            tfBriefServiceInfoList.add(tfBriefServiceInfo);
                        }
                        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                        resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                        resp.setObjData(tfBriefServiceInfoList);
                    } else {
                        resp.setResultCode(WSEnum.NO_DATA.getResultCode());
                        resp.setResultInfo(WSEnum.NO_DATA.getResultInfo());
                    }
                } else {
                    log.info("旅客服务网接口调用失败:{}",tfSpecialServiceQueryResponse.getMsg());
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo(tfSpecialServiceQueryResponse.getMsg());
                }
            } else {
                log.info("旅客服务网接口调用返回数据为空");
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("网络请求异常！");
            }
            log.info(COMMON_LOG_WITH_RESP_INFO,reqId, ip , JsonUtil.objectToJson(resp));
            return resp;
        } catch (Exception e) {
            log.error("请求号:{}，IP地址:{}，错误信息：{}", reqId, ip, e.getMessage());
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("请求异常！");
            log.info(COMMON_LOG_WITH_RESP_INFO,reqId, ip , JsonUtil.objectToJson(resp));
            return resp;
        }
    }

    @InterfaceLog
    @ApiOperation(value = "queryAllApplyNew", notes = "我的申请")
    @RequestMapping(value = "/queryAllApplyNew", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp queryAllApplyNew(@RequestBody @Validated BaseReq<TfSearch> req, BindingResult bindingResult,HttpServletRequest request) {
        BaseResp resp = new BaseResp();
        String ip = this.getClientIP(request);
        String reqId = MdcUtils.getRequestId();
        try {
            if (bindingResult.hasErrors()) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
                return resp;
            }
            //验证用户查询是否正常
            TfSearch tfSearch = req.getRequest();
            boolean flag = this.checkKeyInfo(tfSearch.getFfpId(), tfSearch.getLoginKeyInfo(), req.getChannelCode());
            if (!flag) {
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                return resp;
            }
            //封装请求头
            NewReqHeader header = builderHeader(reqId);
            //封装请求体
            TfSpecialServiceQueryRequest tfSpecialServiceQueryRequest = new TfSpecialServiceQueryRequest();
            tfSpecialServiceQueryRequest.setInftCode(queryAllApply);
            tfSpecialServiceQueryRequest.setMemberId(tfSearch.getFfpId());
            TfSpecialServiceQueryRequestDTO tfSpecialServiceQueryRequestDTO = new TfSpecialServiceQueryRequestDTO();
            tfSpecialServiceQueryRequestDTO.setStartTime(tfSearch.getStartTime());
            tfSpecialServiceQueryRequestDTO.setEndTime(tfSearch.getEndTime());
            tfSpecialServiceQueryRequestDTO.setApplyStatus(tfSearch.getApplyStatus());
            tfSpecialServiceQueryRequestDTO.setTicket_no(tfSearch.getTicketNo());
            tfSpecialServiceQueryRequestDTO.setFlight_no(tfSearch.getFlightNo());
            tfSpecialServiceQueryRequestDTO.setFlight_date(tfSearch.getFlightDate());
            tfSpecialServiceQueryRequestDTO.setDepature_station(tfSearch.getDepatureStation());
            tfSpecialServiceQueryRequestDTO.setDestination_station(tfSearch.getDestinationStation());
            tfSpecialServiceQueryRequest.setData(tfSpecialServiceQueryRequestDTO);
            log.info("给旅客服务网发送的请求体：{},请求地址：{},给旅客服务网发送请求时间：{}", JsonUtil.objectToJson(tfSpecialServiceQueryRequest),url,System.currentTimeMillis());
            //向旅客服务网发送请求
            HttpResult serviceResult = doPost2PassengerService(tfSpecialServiceQueryRequest, url, header);
            if (null != serviceResult && serviceResult.isResult()) {
                log.info("旅客服务网响应结果：{},旅客服务网响应时间：{}", serviceResult.getResponse(),System.currentTimeMillis());
                TfSpecialServiceQueryResponse tfSpecialServiceQueryResponse = (TfSpecialServiceQueryResponse) JsonUtil.jsonToBean(serviceResult.getResponse(), TfSpecialServiceQueryResponse.class);
                if (tfSpecialServiceQueryResponse.getCode() == 0) {
                    List<TfBriefServiceInfo> tfBriefServiceInfoList = new ArrayList<>();
                    if (!StringUtil.isNullOrEmpty(tfSpecialServiceQueryResponse.getData().getDataList())) {
                        Map<String, AirPortInfoDto> airPortInfoMap = basicService.queryAllAirportMap(ChannelCodeEnum.MOBILE.getChannelCode(),ip);
                        List<TfSpecialServiceQueryResponseDTO> dataList = tfSpecialServiceQueryResponse.getData().getDataList();

                        for (TfSpecialServiceQueryResponseDTO tfSpecialServiceQueryResponseDTO : dataList) {
                            String weekDay = "";
                            Date flightDate = DateUtils.toDate(tfSpecialServiceQueryResponseDTO.getFlightDate(), "yyyy-MM-dd");
                            if (flightDate != null) {
                                weekDay = DateUtils.getWeekStr(flightDate);
                            }
                            AirPortInfoDto depAirPortInfo = airPortInfoMap.get(tfSpecialServiceQueryResponseDTO.getDepatureStation()); // 始发站
                            AirPortInfoDto desAirPortInfo = airPortInfoMap.get(tfSpecialServiceQueryResponseDTO.getDestinationStation()); // 到达站
                            //申请列表
                            TfBriefServiceInfo tfBriefServiceInfo = new TfBriefServiceInfo();
                            BeanUtils.copyNotNullProperties(tfSpecialServiceQueryResponseDTO, tfBriefServiceInfo);
                            tfSpecialServiceQueryResponseDTO.getSpeServiceType();
                            NewSpecialServiceTypeEnum newSpecialServiceTypeEnum = NewSpecialServiceTypeEnum.getDesc(tfSpecialServiceQueryResponseDTO.getSpeServiceType());
                            tfBriefServiceInfo.setServiceName(newSpecialServiceTypeEnum == null ? "" : (newSpecialServiceTypeEnum.desc == null ? "" : newSpecialServiceTypeEnum.desc));
                            tfBriefServiceInfo.setWeekDay(weekDay);
                            if (1 == NumberUtils.toInt(tfSpecialServiceQueryResponseDTO.getIsCancel())) {
                                tfBriefServiceInfo.setApplyStatusName("已取消");
                                tfBriefServiceInfo.setRecordStatus("5");
                                tfSpecialServiceQueryResponseDTO.setRecordStatus("5");
                            }
                            tfBriefServiceInfo.setApplyStatusName(tranState(tfSpecialServiceQueryResponseDTO.getRecordStatus(), tfSpecialServiceQueryResponseDTO.getSpeServiceType()));
                            tfBriefServiceInfo.setDepartureStationName(depAirPortInfo == null ? "" : (depAirPortInfo.getCityName() == null ? "" : depAirPortInfo.getCityName()));
                            tfBriefServiceInfo.setDestinationStationName(desAirPortInfo == null ? "" : (desAirPortInfo.getCityName() == null ? "" : desAirPortInfo.getCityName()));
                            //服务进度
                            List<TfScheduleInfo> scheduleList = new ArrayList<>();
                            //处理服务进度信息
                            setScheduleInfo(tfSpecialServiceQueryResponseDTO, scheduleList);
                            tfBriefServiceInfo.setScheduleNew(scheduleList);
                            tfBriefServiceInfoList.add(tfBriefServiceInfo);
                        }
                        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                        resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                        resp.setObjData(tfBriefServiceInfoList);
                    } else {
                        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                        resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                        resp.setObjData(tfBriefServiceInfoList);
                    }
                } else {
                    log.info("旅客服务网接口调用失败:{}",tfSpecialServiceQueryResponse.getMsg());
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo(tfSpecialServiceQueryResponse.getMsg());
                }
            } else {
                log.info("旅客服务网接口调用返回数据为空");
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("网络请求异常！");
            }
            log.info(COMMON_LOG_WITH_RESP_INFO,reqId, ip , JsonUtil.objectToJson(resp));
            return resp;
        } catch (Exception e) {
            log.error("请求号:{}，IP地址:{}，错误信息：{}", reqId, ip, e.getMessage());
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("请求异常！");
            log.info(COMMON_LOG_WITH_RESP_INFO,reqId, ip , JsonUtil.objectToJson(resp));
            return resp;
        }
    }



    @InterfaceLog
    @ApiOperation(value = "unaccompaniedChildApplyList", notes = "无陪儿童服务列表")
    @RequestMapping(value = "/unaccompaniedChildApplyList", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp unaccompaniedChildApplyList(@RequestBody  @Validated BaseReq<QueryUnaccompaniedChild> req, BindingResult bindingResult, HttpServletRequest request) {
        String reqId = StringUtil.newGUID() + "unaccompaniedChildApplyList";
        BaseResp resp = new BaseResp();
        String ip = this.getClientIP(request);
        try {
            if (CollectionUtils.isEmpty(req.getRequest().getPassengerList())){
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setErrorInfo("乘客信息不能为空");
                return resp;
            }
            if (CollectionUtils.isEmpty(req.getRequest().getSegments())){
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setErrorInfo("航段信息不能为空");
                return resp;
            }
            if (bindingResult.hasErrors()) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setErrorInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
                return resp;
            }
            //验证用户查询是否正常
            QueryUnaccompaniedChild tfRequest = req.getRequest();
            boolean flag = this.checkKeyInfo(tfRequest.getFfpId(), tfRequest.getLoginKeyInfo(), req.getChannelCode());
            if (!flag) {
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());

                return resp;
            }
            //封装请求头
            NewReqHeader header = builderHeader(reqId);
            TfSpecialServiceQueryRequest tfSpecialServiceQueryRequest = new TfSpecialServiceQueryRequest();
            tfSpecialServiceQueryRequest.setInftCode(queryAllApply);
            tfSpecialServiceQueryRequest.setMemberId(tfRequest.getFfpId());
            TfSpecialServiceQueryRequestDTO tfSpecialServiceQueryRequestDTO = new TfSpecialServiceQueryRequestDTO();
            tfSpecialServiceQueryRequest.setData(tfSpecialServiceQueryRequestDTO);
            log.info("给旅客服务网发送的请求体：{},请求地址：{},给旅客服务网发送请求时间：{}", JsonUtil.objectToJson(tfSpecialServiceQueryRequest),url,System.currentTimeMillis());
            //向旅客服务网发送请求
            HttpResult serviceResult = doPost2PassengerService(tfSpecialServiceQueryRequest, url, header);
            log.info("旅客服务网响应结果：{},旅客服务网响应时间：{}", serviceResult.getResponse(),System.currentTimeMillis());
            if (null != serviceResult && serviceResult.isResult()) {
                TfSpecialServiceQueryResponse tfSpecialServiceQueryResponse = (TfSpecialServiceQueryResponse) JsonUtil.jsonToBean(serviceResult.getResponse(), TfSpecialServiceQueryResponse.class);
                if (tfSpecialServiceQueryResponse.getCode() == 0) {
                    List<UnaccompaniedChildApplyResp>   list =new ArrayList<>();
                    if (!StringUtil.isNullOrEmpty(tfSpecialServiceQueryResponse.getData().getDataList())) {
                        List<TfSpecialServiceQueryResponseDTO> dataList = tfSpecialServiceQueryResponse.getData().getDataList();
                        if (CollectionUtils.isNotEmpty(dataList)){
                            Map<String, AirPortInfoDto> airPortInfoMap = basicService.queryAllAirportMap(ChannelCodeEnum.MOBILE.getChannelCode(),ip);
                            //过滤无陪儿童服务旅服申请单和非1.预约成功、2.申请成功、3.受理中
                            List<TfSpecialServiceQueryResponseDTO>   tfSpecialist=  dataList.stream().filter(tfSpecialServiceQueryResponseDTO -> "2".equals(tfSpecialServiceQueryResponseDTO.getSpeServiceType())
                            &&recordStatus.contains(tfSpecialServiceQueryResponseDTO.getRecordStatus())&&StringUtils.isBlank(tfSpecialServiceQueryResponseDTO.getTicketNo())).collect(Collectors.toList());
                            //做证件信息筛选
                            extracted(tfSpecialist, tfRequest, list, airPortInfoMap);
                        }
                    }
                    resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                    resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                    resp.setObjData(list);
                } else {
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo(tfSpecialServiceQueryResponse.getMsg());
                }
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("网络请求异常！");
            }
            return resp;
        } catch (Exception e) {
            this.logError(resp, reqId, ip, req, e, "获取无陪儿童服务列表失败");
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("请求异常！");
            return resp;
        }
    }

    @InterfaceLog
    @ApiOperation(value = "childApplyQuotaCheck", notes = "无陪儿童配额校验")
    @RequestMapping(value = "/childApplyQuotaCheck", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp childApplyQuotaCheck(@RequestBody  @Validated  BaseReq<ChildApplyQuotaCheckReq> req, BindingResult bindingResult, HttpServletRequest request) {
        String reqId = StringUtil.newGUID() + "childApplyQuotaCheck";
        BaseResp<ChildApplyQuotaCheckResp>  baseResp = new BaseResp();
        ChildApplyQuotaCheckResp childApplyQuotaCheckResp = new ChildApplyQuotaCheckResp();
        childApplyQuotaCheckResp.setQuotaCheck(false);
        if (bindingResult.hasErrors()) {
            baseResp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            baseResp.setResultInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
            return baseResp;
        }

        List<ChildApplyQuotaCheck> childApplyQuotaCheckList  =req.getRequest().getChildApplyQuotaCheckList();
         AtomicReference<Boolean> quotaCheck = new AtomicReference<>(true);
        if (CollectionUtils.isNotEmpty(childApplyQuotaCheckList)) {
            childApplyQuotaCheckList.forEach(childApplyQuotaCheck -> {
                NewReqHeader header = builderHeader(reqId);
                ChildApplyQuotaCheckRequest childApplyQuotaCheckRequest =new ChildApplyQuotaCheckRequest();
                childApplyQuotaCheckRequest.setSpeServiceType("2");
                BeanUtils.copyProperties(childApplyQuotaCheck,childApplyQuotaCheckRequest);
                String   url=  HandlerConstants.URL_PASSAGER_API + HandlerConstants.SELECT_SPERVAPP_LOCK;
                //向旅客服务网发送请求
                log.info("给旅客服务网发送的请求体：{},请求地址：{},给旅客服务网发送请求时间：{}", JsonUtil.objectToJson(childApplyQuotaCheckRequest),url,System.currentTimeMillis());
                HttpResult serviceResult = doPost2PassengerService(childApplyQuotaCheckRequest, url, header);
                if (null != serviceResult && serviceResult.isResult()) {
                    log.info("旅客服务网响应结果：{},旅客服务网响应时间：{}", serviceResult.getResponse(), System.currentTimeMillis());
                    ChildApplyQuotaCheckRespone childApplyQuotaCheckRespone = (ChildApplyQuotaCheckRespone) JsonUtil.jsonToBean(serviceResult.getResponse(), ChildApplyQuotaCheckRespone.class);
                    if (childApplyQuotaCheckRespone.getCode() == 0) {
                        if (Double.valueOf(childApplyQuotaCheckRespone.getData().getSurplusNum())>0&& quotaCheck.get()){
                            quotaCheck.set(true);
                        }else {
                            quotaCheck.set(false);
                        }
                    }
                }else {
                    quotaCheck.set(false);
                }
            });
        }
        childApplyQuotaCheckResp.setQuotaCheck(quotaCheck.get());
        baseResp.setObjData(childApplyQuotaCheckResp);
        baseResp.setResultCode(WSEnum.SUCCESS.getResultCode());
        baseResp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
        return baseResp;
    }




    private void extracted(List<TfSpecialServiceQueryResponseDTO> tfSpecialist, QueryUnaccompaniedChild tfSearch, List<UnaccompaniedChildApplyResp> list, Map<String, AirPortInfoDto> airPortInfoMap) {
        for (TfSpecialServiceQueryResponseDTO tfSpecialServiceQueryResponseDTO : tfSpecialist) {
            tfSearch.getPassengerList().forEach(passenger -> {
                if(tfSpecialServiceQueryResponseDTO.getPassName().equals(passenger.getPassengerName())
                  &&tfSpecialServiceQueryResponseDTO.getPassCertification().equals(passenger.getCertNo())){
                    //航段信息匹配
                          tfSearch.getSegments().forEach(segmentInfoReq ->
                                  {
                                      if (tfSpecialServiceQueryResponseDTO.getDestinationStation() .equals(segmentInfoReq.getArrAirport())
                                              &&tfSpecialServiceQueryResponseDTO.getDepatureStation().equals(segmentInfoReq.getDepAirport())
                                              &&tfSpecialServiceQueryResponseDTO.getFlightDate().equals(segmentInfoReq.getFlightDate())
                                              &&((StringUtils.isNotBlank(tfSpecialServiceQueryResponseDTO.getFlightNo())
                                              &&segmentInfoReq.getFlightNo().equals(tfSpecialServiceQueryResponseDTO.getFlightNo()))
                                              ||StringUtils.isBlank(tfSpecialServiceQueryResponseDTO.getFlightNo()))) {
                                              tfSpecialServiceQueryResponseDTO.setFlightDirection(segmentInfoReq.getFlightDirection());
                                              childApplyList(list,tfSpecialServiceQueryResponseDTO, airPortInfoMap);
                                      }
                                  });
                  }
            });
        }

    }

    private void childApplyList(List<UnaccompaniedChildApplyResp>   list,TfSpecialServiceQueryResponseDTO tfSpecialServiceQueryResponseDTO,Map<String, AirPortInfoDto> airPortInfoMap){
            UnaccompaniedChildApplyResp unaccompaniedChildApplyResp = new UnaccompaniedChildApplyResp();
            BeanUtils.copyProperties(tfSpecialServiceQueryResponseDTO, unaccompaniedChildApplyResp);
            unaccompaniedChildApplyResp.setNumber(tfSpecialServiceQueryResponseDTO.getApplicationSource() + "-" +
                    tfSpecialServiceQueryResponseDTO.getCreateDate().substring(0, 10) + "-" + tfSpecialServiceQueryResponseDTO.getRecordNo());
            AirPortInfoDto depAirPortInfo = airPortInfoMap.get(tfSpecialServiceQueryResponseDTO.getDepatureStation()); // 始发站
            AirPortInfoDto desAirPortInfo = airPortInfoMap.get(tfSpecialServiceQueryResponseDTO.getDestinationStation()); // 到达站
            unaccompaniedChildApplyResp.setDepartureStationName(depAirPortInfo == null ? "" : (depAirPortInfo.getCityName() == null ? "" : depAirPortInfo.getCityName()));
            unaccompaniedChildApplyResp.setDestinationStationName(desAirPortInfo == null ? "" : (desAirPortInfo.getCityName() == null ? "" : desAirPortInfo.getCityName()));
            if ("PSPT".equals(unaccompaniedChildApplyResp.getPassCertType())) {
                unaccompaniedChildApplyResp.setPassCertType( CertificateTypeEnum.PASSPORT.getShowCode());
            }
           if ("PSPT".equals(unaccompaniedChildApplyResp.getJjPersonCertType())) {
               unaccompaniedChildApplyResp.setJjPersonCertType( CertificateTypeEnum.PASSPORT.getShowCode());
           }
          if ("PSPT".equals(unaccompaniedChildApplyResp.getSjPersionCertType())) {
             unaccompaniedChildApplyResp.setSjPersionCertType( CertificateTypeEnum.PASSPORT.getShowCode());
          }
          if ("PSPT".equals(unaccompaniedChildApplyResp.getPtPersonCertType())) {
             unaccompaniedChildApplyResp.setPtPersonCertType(CertificateTypeEnum.PASSPORT.getShowCode());
          }
        if (StringUtils.isNotBlank(unaccompaniedChildApplyResp.getPassContact())){
            unaccompaniedChildApplyResp.setPassCountryTelCode(getCountryTelCodeAndPhone(unaccompaniedChildApplyResp.getPassContact()).getCountryTelCode());
            unaccompaniedChildApplyResp.setPassContact(getCountryTelCodeAndPhone(unaccompaniedChildApplyResp.getPassContact()).getHandPhoneNo());
        }
        if (StringUtils.isNotBlank(unaccompaniedChildApplyResp.getJjPersonContact())){
            unaccompaniedChildApplyResp.setJjPersonCountryTelCode(getCountryTelCodeAndPhone(unaccompaniedChildApplyResp.getJjPersonContact()).getCountryTelCode());
            unaccompaniedChildApplyResp.setJjPersonContact(getCountryTelCodeAndPhone(unaccompaniedChildApplyResp.getJjPersonContact()).getHandPhoneNo());
        }
        if (StringUtils.isNotBlank(unaccompaniedChildApplyResp.getPtPersonContact())){
            unaccompaniedChildApplyResp.setPtPersonCountryTelCode(getCountryTelCodeAndPhone(unaccompaniedChildApplyResp.getPtPersonContact()).getCountryTelCode());
            unaccompaniedChildApplyResp.setPtPersonContact(getCountryTelCodeAndPhone(unaccompaniedChildApplyResp.getPtPersonContact()).getHandPhoneNo());
        }
        if (StringUtils.isNotBlank(unaccompaniedChildApplyResp.getSjPersonContact())){
            unaccompaniedChildApplyResp.setSjPersonCountryTelCode(getCountryTelCodeAndPhone(unaccompaniedChildApplyResp.getSjPersonContact()).getCountryTelCode());
            unaccompaniedChildApplyResp.setSjPersonContact(getCountryTelCodeAndPhone(unaccompaniedChildApplyResp.getSjPersonContact()).getHandPhoneNo());
        }
        if (StringUtils.isNotBlank(unaccompaniedChildApplyResp.getYdPersonPhone())){
            unaccompaniedChildApplyResp.setYdPersonCountryTelCode(getCountryTelCodeAndPhone(unaccompaniedChildApplyResp.getYdPersonPhone()).getCountryTelCode());
            unaccompaniedChildApplyResp.setYdPersonPhone(getCountryTelCodeAndPhone(unaccompaniedChildApplyResp.getYdPersonPhone()).getHandPhoneNo());
        }
            list.add(unaccompaniedChildApplyResp);
    }

    //取消申请
    @ApiOperation(value = "cancelApply", notes = "取消申请")
    @RequestMapping(value = "/cancelApply", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp cancelApply(@RequestBody BaseReq<TfSearch> req, HttpServletRequest request) {
        String reqId = StringUtil.newGUID() + "_cancelApply";
        BaseResp resp = new BaseResp();
        String ip = this.getClientIP(request);

        try {
            log.info(COMMON_LOG_WITH_REQ_INFO, reqId, ip, JsonUtil.objectToJson(req));
            ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
            Validator validator = factory.getValidator();
            Set<ConstraintViolation<BaseReq>> violations = validator.validate(req);
            if (null != violations && violations.size() > 0) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo(violations.iterator().next().getMessage());
                log.info(COMMON_LOG_WITH_RESP_INFO, reqId, ip, JsonUtil.objectToJson(resp));
                return resp;
            }
            TfSearch tfSearch = req.getRequest();
            if (StringUtil.isNullOrEmpty(tfSearch.getId())) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo("无此服务编号");
                log.info(COMMON_LOG_WITH_RESP_INFO, reqId, ip, JsonUtil.objectToJson(resp));
                return resp;
            }
            boolean flag = this.checkKeyInfo(tfSearch.getFfpId(), tfSearch.getLoginKeyInfo(), req.getChannelCode());
            if (!flag) {
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                log.info(COMMON_LOG_WITH_RESP_INFO, reqId, ip, JsonUtil.objectToJson(resp));
                return resp;
            }
            //封装请求头
            NewReqHeader header = builderHeader(reqId);
            //请求体
            TfSpecialServiceQueryRequest tfSpecialServiceQueryRequest = new TfSpecialServiceQueryRequest();
            tfSpecialServiceQueryRequest.setInftCode(cancelApply);
            tfSpecialServiceQueryRequest.setMemberId(tfSearch.getFfpId());
            TfSpecialServiceQueryRequestDTO tfSpecialServiceQueryRequestDTO = new TfSpecialServiceQueryRequestDTO();
            tfSpecialServiceQueryRequestDTO.setId(tfSearch.getId());
            tfSpecialServiceQueryRequest.setData(tfSpecialServiceQueryRequestDTO);
            log.info("给旅客服务网发送的请求体：{},请求地址：{},给旅客服务网发送请求时间：{}", JsonUtil.objectToJson(tfSpecialServiceQueryRequest),url,System.currentTimeMillis());
            //调用旅客服务网
            HttpResult serviceResult = doPost2PassengerService(tfSpecialServiceQueryRequest, url, header);

            if (null != serviceResult && serviceResult.isResult()) {
                TfSpecialServiceApplyResponse tfSpecialServiceApplyResponse = (TfSpecialServiceApplyResponse) JsonUtil.jsonToBean(serviceResult.getResponse(), TfSpecialServiceApplyResponse.class);
                log.info("给旅客服务网发送的请求体：{},旅客服务网响应时间：{}", JsonUtil.objectToJson(tfSpecialServiceApplyResponse),System.currentTimeMillis());
                if (tfSpecialServiceApplyResponse.getCode() == 0) {
                    if ("true".equals(tfSpecialServiceApplyResponse.getData().getSuccess())) {
                        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                        resp.setResultInfo(tfSpecialServiceApplyResponse.getMsg());
                    } else {
                        resp.setResultCode(WSEnum.ERROR.getResultCode());
                        resp.setResultInfo(tfSpecialServiceApplyResponse.getMsg());
                    }
                } else {
                    log.info("旅客服务网接口调用失败:{}",tfSpecialServiceApplyResponse.getMsg());
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo(tfSpecialServiceApplyResponse.getMsg());
                }
            } else {
                log.info("旅客服务网接口调用返回数据为空");
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("网络请求异常！");
            }
            log.info(COMMON_LOG_WITH_RESP_INFO, reqId, ip, JsonUtil.objectToJson(resp));
            return resp;
        } catch (Exception e) {
            log.error("请求号:{}，IP地址:{}，错误信息：{}", reqId, ip, e.getMessage(), e);
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("请求异常！");
            log.info(COMMON_LOG_WITH_RESP_INFO, reqId, ip, JsonUtil.objectToJson(resp));
            return resp;
        }
    }

    //详情
    @ApiOperation(value = "查看服务单详情", notes = "查看服务详情")
    @RequestMapping(value = "/queryOneApply", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp queryOneApply(@RequestBody BaseReq<TfSearch> req, HttpServletRequest request){


        String reqId = StringUtil.newGUID() + "queryOneApply";
        BaseResp resp = new BaseResp();
        String ip = this.getClientIP(request);
        try {
            log.info(COMMON_LOG_WITH_REQ_INFO,reqId, ip , JsonUtil.objectToJson(req));
            ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
            Validator validator = factory.getValidator();
            Set<ConstraintViolation<BaseReq>> violations = validator.validate(req);
            if (null != violations && violations.size() > 0) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo(violations.iterator().next().getMessage());
                log.info(COMMON_LOG_WITH_RESP_INFO,reqId, ip , JsonUtil.objectToJson(resp));
                return resp;
            }
            TfSearch tfSearch = req.getRequest();
            if(StringUtil.isNullOrEmpty(tfSearch.getId())){
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setResultInfo("无此服务编号");
                log.info(COMMON_LOG_WITH_RESP_INFO,reqId, ip , JsonUtil.objectToJson(resp));
                return resp;
            }
            //验证用户查询是否正常
            boolean flag = this.checkKeyInfo(tfSearch.getFfpId(), tfSearch.getLoginKeyInfo(), req.getChannelCode());
            if (!flag) {
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                log.info(COMMON_LOG_WITH_RESP_INFO,reqId, ip , JsonUtil.objectToJson(resp));
                return resp;
            }
            //封装请求头
            NewReqHeader header = builderHeader(reqId);
            //封装请求体
            TfSpecialServiceQueryRequest tfSpecialServiceQueryRequest = new TfSpecialServiceQueryRequest();
            tfSpecialServiceQueryRequest.setInftCode(queryApplyByIds);
            tfSpecialServiceQueryRequest.setMemberId(tfSearch.getFfpId());
            TfSpecialServiceQueryRequestDTO tfSpecialServiceQueryRequestDTO = new TfSpecialServiceQueryRequestDTO();
            List<String> ids = new ArrayList<>();
            ids.add(tfSearch.getId());
            tfSpecialServiceQueryRequestDTO.setIds(ids);
            tfSpecialServiceQueryRequest.setData(tfSpecialServiceQueryRequestDTO);
            log.info("给旅客服务网发送的请求体：{},请求地址：{},给旅客服务网发送请求时间：{}", JsonUtil.objectToJson(tfSpecialServiceQueryRequest),url,System.currentTimeMillis());
            //向旅客服务网发送请求
            HttpResult result = doPost2PassengerService(tfSpecialServiceQueryRequest, url, header);
            if(result != null){
                TfSpecialServiceQueryResponse tfSpecialServiceQueryResponse = (TfSpecialServiceQueryResponse) JsonUtil.jsonToBean(result.getResponse(), TfSpecialServiceQueryResponse.class);
                log.info("给旅客服务网发送的请求体：{},旅客服务网响应时间：{}", JsonUtil.objectToJson(tfSpecialServiceQueryResponse),System.currentTimeMillis());
                if (0 == tfSpecialServiceQueryResponse.getCode()){
                    resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                    resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                    TfSpecialServiceQueryResponseDTO tfSpecialServiceQueryResponseDTO = tfSpecialServiceQueryResponse.getData().getDataList().get(0);
                    //返回对象
                    TfDetailsQueryInfo tfDetailsQueryInfo = new TfDetailsQueryInfo();
                    BeanUtils.copyProperties(tfSpecialServiceQueryResponseDTO,tfDetailsQueryInfo);
                    tfDetailsQueryInfo.setNumber(tfSpecialServiceQueryResponseDTO.getApplicationSource() + "-" +
                            tfSpecialServiceQueryResponseDTO.getCreateDate().substring(0, 10) + "-" + tfSpecialServiceQueryResponseDTO.getRecordNo());
                    if (1 == NumberUtils.toInt(tfSpecialServiceQueryResponseDTO.getIsCancel())) {
                        tfDetailsQueryInfo.setRecordStatus("5");
                        tfSpecialServiceQueryResponseDTO.setRecordStatus("5");
                    }
                    tfDetailsQueryInfo.setMedicalCertificate(tfSpecialServiceQueryResponseDTO.getIsMedicalCertificate());
                    //航线信息
                    Map<String, AirPortInfoDto> airPortInfoMap = basicService.queryAllAirportMap(ChannelCodeEnum.MOBILE.getChannelCode(),ip);
                    AirPortInfoDto arrAirPortInfo = airPortInfoMap.get(tfSpecialServiceQueryResponseDTO.getDestinationStation()); // 到达站.
                    AirPortInfoDto depAirPortInfo = airPortInfoMap.get(tfSpecialServiceQueryResponseDTO.getDepatureStation()); // 始发站
                    tfDetailsQueryInfo.setDestinationStationName(arrAirPortInfo == null ? "" : (arrAirPortInfo.getAirPortName() == null ? "" : arrAirPortInfo.getAirPortName()));
                    tfDetailsQueryInfo.setDepartureStationName(depAirPortInfo == null ? "" : (depAirPortInfo.getAirPortName() == null ? "" : depAirPortInfo.getAirPortName()));
                    tfDetailsQueryInfo.setArrAirCode(arrAirPortInfo == null ? "" : (arrAirPortInfo.getAirPortCode() == null ? "" : arrAirPortInfo.getAirPortCode()));
                    tfDetailsQueryInfo.setDepAirCode(depAirPortInfo == null ? "" : (depAirPortInfo.getAirPortCode() == null ? "" : depAirPortInfo.getAirPortCode()));
                    if ("PSPT".equals(tfDetailsQueryInfo.getPassCertType())) {
                        tfDetailsQueryInfo.setPassCertType( CertificateTypeEnum.PASSPORT.getShowCode());
                    }
                    if ("PSPT".equals(tfDetailsQueryInfo.getJjPersonCertType())) {
                        tfDetailsQueryInfo.setJjPersonCertType(CertificateTypeEnum.PASSPORT.getShowCode());
                    }
                    if ("PSPT".equals(tfDetailsQueryInfo.getSjPersionCertType())) {
                        tfDetailsQueryInfo.setSjPersionCertType(CertificateTypeEnum.PASSPORT.getShowCode());
                    }
                    if ("PSPT".equals(tfDetailsQueryInfo.getPtPersonCertType())) {
                        tfDetailsQueryInfo.setPtPersonCertType(CertificateTypeEnum.PASSPORT.getShowCode());
                    }
                    //服务名称
                    NewSpecialServiceTypeEnum newSpecialServiceTypeEnum = NewSpecialServiceTypeEnum.getDesc(tfSpecialServiceQueryResponseDTO.getSpeServiceType());
                    tfDetailsQueryInfo.setSpeServiceName(newSpecialServiceTypeEnum == null ? "" : (newSpecialServiceTypeEnum.desc == null ? "" : newSpecialServiceTypeEnum.desc));
                    //残疾类型"1,2,3" 智力残疾：1 肢体残疾：2 精神残疾：3
                    String disabilityType = tfDetailsQueryInfo.getDisabilityType();
                    if(disabilityType != null){
                        if(disabilityType.contains("1")){
                            tfDetailsQueryInfo.setZlDisability("1");
                        }
                        if(disabilityType.contains("2")){
                            tfDetailsQueryInfo.setZtDisability("2");
                        }
                        if(disabilityType.contains("3")){
                            tfDetailsQueryInfo.setJsDisability("3");
                        }
                    }
                    // 审核原因
                    if("4".equals(tfSpecialServiceQueryResponseDTO.getRecordStatus())){
                        if(StringUtils.isNotBlank(tfSpecialServiceQueryResponseDTO.getApprovalReason())){
                            NewSpecialServiceApprovalReasonEnum approvalReasonEnum = NewSpecialServiceApprovalReasonEnum.getDesc(tfSpecialServiceQueryResponseDTO.getApprovalReason());
                            if(approvalReasonEnum != null){
                                tfDetailsQueryInfo.setAuditReason(approvalReasonEnum.desc);
                            }
                        }
                    }
                    //取消原因
                    if("5".equals(tfSpecialServiceQueryResponseDTO.getRecordStatus())){
                        if(StringUtils.isNotBlank(tfSpecialServiceQueryResponseDTO.getCancelReason())){
                            NewSpecialServiceCancelReasonEnum reasonEnum = NewSpecialServiceCancelReasonEnum.getDesc(tfSpecialServiceQueryResponseDTO.getCancelReason());
                            tfDetailsQueryInfo.setCancelReason(reasonEnum == null ? "旅客前端操作取消" : reasonEnum.desc);
                        }else{
                            tfDetailsQueryInfo.setCancelReason("旅客前端操作取消");
                        }
                    }
                    if (StringUtils.isNotBlank(tfDetailsQueryInfo.getPassContact())){
                        tfDetailsQueryInfo.setPassCountryTelCode(getCountryTelCodeAndPhone(tfDetailsQueryInfo.getPassContact()).getCountryTelCode());
                        tfDetailsQueryInfo.setPassContact(getCountryTelCodeAndPhone(tfDetailsQueryInfo.getPassContact()).getHandPhoneNo());
                    }
                    if (StringUtils.isNotBlank(tfDetailsQueryInfo.getJjPersonContact())){
                        tfDetailsQueryInfo.setJjPersonCountryTelCode(getCountryTelCodeAndPhone(tfDetailsQueryInfo.getJjPersonContact()).getCountryTelCode());
                        tfDetailsQueryInfo.setJjPersonContact(getCountryTelCodeAndPhone(tfDetailsQueryInfo.getJjPersonContact()).getHandPhoneNo());
                    }
                    if (StringUtils.isNotBlank(tfDetailsQueryInfo.getPtPersonContact())){
                        tfDetailsQueryInfo.setPtPersonCountryTelCode(getCountryTelCodeAndPhone(tfDetailsQueryInfo.getPtPersonContact()).getCountryTelCode());
                        tfDetailsQueryInfo.setPtPersonContact(getCountryTelCodeAndPhone(tfDetailsQueryInfo.getPtPersonContact()).getHandPhoneNo());
                    }
                    if (StringUtils.isNotBlank(tfDetailsQueryInfo.getSjPersonContact())){
                        tfDetailsQueryInfo.setSjPersonCountryTelCode(getCountryTelCodeAndPhone(tfDetailsQueryInfo.getSjPersonContact()).getCountryTelCode());
                        tfDetailsQueryInfo.setSjPersonContact(getCountryTelCodeAndPhone(tfDetailsQueryInfo.getSjPersonContact()).getHandPhoneNo());
                    }
                    if (StringUtils.isNotBlank(tfDetailsQueryInfo.getYdPersonPhone())){
                        tfDetailsQueryInfo.setYdPersonCountryTelCode(getCountryTelCodeAndPhone(tfDetailsQueryInfo.getYdPersonPhone()).getCountryTelCode());
                        tfDetailsQueryInfo.setYdPersonPhone(getCountryTelCodeAndPhone(tfDetailsQueryInfo.getYdPersonPhone()).getHandPhoneNo());
                    }
                    resp.setObjData(tfDetailsQueryInfo);
                }else{
                    log.info("旅客服务网接口调用失败:{}",tfSpecialServiceQueryResponse.getMsg());
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo(tfSpecialServiceQueryResponse.getMsg());
                }
            }else{
                log.info("旅客服务网接口调用返回数据为空");
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("网络请求异常！");
            }
            return resp;
        }catch (Exception e){
            log.error("请求号:{}，IP地址:{}，错误信息：{}",reqId, ip , e.getMessage());
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("请求异常！");
            log.info(COMMON_LOG_WITH_RESP_INFO,reqId, ip , JsonUtil.objectToJson(resp));
            return resp;
        }
    }

    //详情页单张图片添加
    @ApiOperation(value = "addOnePic", notes = "详情页单张图片添加")
    @RequestMapping(value = "/addOnePic", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp addOnePic(@RequestBody BaseReq<OnePic> req, HttpServletRequest request){
        String reqId = StringUtil.newGUID() + "_addOnePic";
        BaseResp resp = new BaseResp();
        try {
            log.info("请求号:{}，客户端提交参数：{}",reqId,  JsonUtil.objectToJson(req));
            ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
            Validator validator = factory.getValidator();
            Set<ConstraintViolation<BaseReq>> violations = validator.validate(req);
            if (null != violations && violations.size() > 0) {
                resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                resp.setResultInfo(violations.iterator().next().getMessage());
                log.info("请求号:{}，服务端响应结果：{}",reqId, JsonUtil.objectToJson(resp));
                return resp;
            }
            //验证用户查询是否正常
            OnePic pic = req.getRequest();
            boolean flag = this.checkKeyInfo(pic.getFfpId(), pic.getLoginKeyInfo(), req.getChannelCode());
            if (!flag) {
                resp.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                resp.setResultInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
                log.info("请求号:{}，服务端响应结果：{}",reqId, JsonUtil.objectToJson(resp));
                return resp;
            }
            //封装请求头
            NewReqHeader header = builderHeader(reqId);
            //封装请求体
            TfSpecialServiceOnePicRequest tfSpecialServiceOnePicRequestReq = new TfSpecialServiceOnePicRequest();
            tfSpecialServiceOnePicRequestReq.setInftCode(updateApplyById);
            tfSpecialServiceOnePicRequestReq.setMemberId(req.getRequest().getFfpId());
            tfSpecialServiceOnePicRequestReq.setData(req.getRequest());
            log.info("给旅客服务网发送的请求体：{},请求地址：{},给旅客服务网发送请求时间：{}", JsonUtil.objectToJson(tfSpecialServiceOnePicRequestReq),url,System.currentTimeMillis());
            //向旅客服务网发送请求
            HttpResult result = doPost2PassengerService(tfSpecialServiceOnePicRequestReq, url, header);
            if(result != null){
                //响应
                TfSpecialServiceOnePicResponse response = (TfSpecialServiceOnePicResponse)JsonUtil.jsonToBean(result.getResponse(), TfSpecialServiceOnePicResponse.class);
                log.info("给旅客服务网发送的请求体：{},旅客服务网响应时间：{}", JsonUtil.objectToJson(response),System.currentTimeMillis());
                if(response.getCode() == 0){
                    //操作成功
                    resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                    resp.setResultInfo(response.getMsg());
                    resp.setObjData(response.getData());
                }else{
                    //操作失败
                    log.info("旅客服务网接口调用失败:{}",response.getMsg());
                    resp.setResultCode(WSEnum.ERROR.getResultCode());
                    resp.setResultInfo(response.getMsg());
                }
            }else{
                log.info("旅客服务网接口调用返回数据为空");
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("网络请求异常！");
            }
            return resp;
        }catch (Exception e){
            log.error("请求号:{}，错误信息：{}",reqId, e.getMessage());
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo("网络请求异常！");
            log.info("请求号:{}，服务端响应结果：{}",reqId, JsonUtil.objectToJson(resp));
            return resp;
        }
    }


    public MemberContactInfo getCountryTelCodeAndPhone(String phoneNumber){
        MemberContactInfo  memberContactInfo =new MemberContactInfo();
        if (phoneNumber != null) {
              if(phoneNumber.contains("-")){
                    String  countryTelCode =  StringUtils.substringBefore(phoneNumber, "-");
                    String  handPhoneNo =  StringUtils.substringAfter(phoneNumber, "-");
                    memberContactInfo.setCountryTelCode(countryTelCode);
                    memberContactInfo.setHandPhoneNo(handPhoneNo);
                }else {
                    memberContactInfo.setCountryTelCode("86");
                    memberContactInfo.setHandPhoneNo(phoneNumber);
                }
        }
        return  memberContactInfo;
    }

    //部分状态特殊处理
    private String tranState(String applyStatus, String speServiceType) {
        String applyStatusName = "";
        switch (applyStatus) {
            case "1": {
                applyStatusName = "预约成功";
                break;
            }
           /* case "2": {
                applyStatusName = "申请成功";
                break;
            }*/
            case "2": {
                if (String.valueOf(NewSpecialServiceTypeEnum.BABYCRADLE).equals(speServiceType) ||
                        String.valueOf(NewSpecialServiceTypeEnum.NOACCOMPANYCHILDREN.serviceType).equals(speServiceType) ||
                        String.valueOf(NewSpecialServiceTypeEnum.NOACCOMPANYTEENAGERS.serviceType).equals(speServiceType) ||
                        String.valueOf(NewSpecialServiceTypeEnum.NOACCOMPANYOLD.serviceType).equals(speServiceType) ||
                        String.valueOf(NewSpecialServiceTypeEnum.NOACCOMPANYBLINDPASSENGERS.serviceType).equals(speServiceType) ||
                        String.valueOf(NewSpecialServiceTypeEnum.NOACCOMPANYDEAFPASSENGERS.serviceType).equals(speServiceType) ||
                        String.valueOf(NewSpecialServiceTypeEnum.LACTATIONPASSENGERS.serviceType).equals(speServiceType) ||
                        String.valueOf(NewSpecialServiceTypeEnum.PREGNANTPASSENGERS.serviceType).equals(speServiceType)) {
                    applyStatusName = "预约成功";
                } else {
                    applyStatusName = "申请成功";
                }
                break;
            }
            case "3": {
                applyStatusName = "受理中";
                break;
            }
            case "4": {
                applyStatusName = "受理失败";
                break;
            }
            case "5": {
                applyStatusName = "已取消";
                break;
            }
            default:
                applyStatusName = applyStatus;
                break;
        }
        return applyStatusName;
    }

    //封装请求头
    private NewReqHeader builderHeader(String reqId){
        NewReqHeader header = new NewReqHeader();
        String timestamp = String.valueOf(System.currentTimeMillis());
        header.setFrom(from);
        header.setSystemCode(systemCode);
        header.setMsgId(reqId);
        header.setTimestamp(timestamp);
        header.setVerifyCode(EncoderHandler.encodeByMD5(systemCode+MD5PassWord+timestamp+reqId));
        return header;
    }

    /**
     * 处理服务进度信息
     * @param tfSpecialServiceQueryResponseDTO
     * @param scheduleList
     */
    private void setScheduleInfo(TfSpecialServiceQueryResponseDTO tfSpecialServiceQueryResponseDTO,
                                 List<TfScheduleInfo> scheduleList){
        if("1".equals(tfSpecialServiceQueryResponseDTO.getServiceStatus())){
            //已申请
            scheduleList.add(new TfScheduleInfo(tfSpecialServiceQueryResponseDTO.getApplyTime(),"特服申请","已申请"));
        }else if("2".equals(tfSpecialServiceQueryResponseDTO.getServiceStatus())){
            //已值机
            scheduleList.add(new TfScheduleInfo(tfSpecialServiceQueryResponseDTO.getApplyTime(),"特服申请","已申请"));
            scheduleList.add(new TfScheduleInfo(tfSpecialServiceQueryResponseDTO.getCheckInTime(),"值机状态","已值机"));
        }else if( "3".equals(tfSpecialServiceQueryResponseDTO.getServiceStatus())){
            //保障开始
            scheduleList.add(new TfScheduleInfo(tfSpecialServiceQueryResponseDTO.getApplyTime(),"特服申请","已申请"));
            scheduleList.add(new TfScheduleInfo(tfSpecialServiceQueryResponseDTO.getCheckInTime(),"值机状态","已值机"));
            scheduleList.add(new TfScheduleInfo(tfSpecialServiceQueryResponseDTO.getGroundSupportStartTime(),"登机保障","登机保障开始"));
        }else if("4".equals(tfSpecialServiceQueryResponseDTO.getServiceStatus())){
            //保障结束
            scheduleList.add(new TfScheduleInfo(tfSpecialServiceQueryResponseDTO.getApplyTime(),"特服申请","已申请"));
            scheduleList.add(new TfScheduleInfo(tfSpecialServiceQueryResponseDTO.getCheckInTime(),"值机状态","已值机"));
            scheduleList.add(new TfScheduleInfo(tfSpecialServiceQueryResponseDTO.getGroundSupportStartTime(),"登机保障","登机保障开始"));
            scheduleList.add(new TfScheduleInfo(tfSpecialServiceQueryResponseDTO.getGroundSupportEndTime(),"登机保障","登机保障结束"));
        }else if("5".equals(tfSpecialServiceQueryResponseDTO.getServiceStatus())){
            //已登机
            scheduleList.add(new TfScheduleInfo(tfSpecialServiceQueryResponseDTO.getApplyTime(),"特服申请","已申请"));
            scheduleList.add(new TfScheduleInfo(tfSpecialServiceQueryResponseDTO.getCheckInTime(),"值机状态","已值机"));
            scheduleList.add(new TfScheduleInfo(tfSpecialServiceQueryResponseDTO.getGroundSupportStartTime(),"登机保障","登机保障开始"));
            scheduleList.add(new TfScheduleInfo(tfSpecialServiceQueryResponseDTO.getGroundSupportEndTime(),"登机保障","登机保障结束"));
            scheduleList.add(new TfScheduleInfo(tfSpecialServiceQueryResponseDTO.getBoardingTime(),"登机状态","已登机"));
        }else if("6".equals(tfSpecialServiceQueryResponseDTO.getServiceStatus())){
            //已抵达
            scheduleList.add(new TfScheduleInfo(tfSpecialServiceQueryResponseDTO.getApplyTime(),"特服申请","已申请"));
            scheduleList.add(new TfScheduleInfo(tfSpecialServiceQueryResponseDTO.getCheckInTime(),"值机状态","已值机"));
            scheduleList.add(new TfScheduleInfo(tfSpecialServiceQueryResponseDTO.getGroundSupportStartTime(),"登机保障","登机保障开始"));
            scheduleList.add(new TfScheduleInfo(tfSpecialServiceQueryResponseDTO.getGroundSupportEndTime(),"登机保障","登机保障结束"));
            scheduleList.add(new TfScheduleInfo(tfSpecialServiceQueryResponseDTO.getBoardingTime(),"登机状态","已登机"));
            scheduleList.add(new TfScheduleInfo(tfSpecialServiceQueryResponseDTO.getArrGuaStartTime(),"接机保障","接机保障开始"));
        }
        else if("7".equals(tfSpecialServiceQueryResponseDTO.getServiceStatus())){
            //已抵达
            scheduleList.add(new TfScheduleInfo(tfSpecialServiceQueryResponseDTO.getApplyTime(),"特服申请","已申请"));
            scheduleList.add(new TfScheduleInfo(tfSpecialServiceQueryResponseDTO.getCheckInTime(),"值机状态","已值机"));
            scheduleList.add(new TfScheduleInfo(tfSpecialServiceQueryResponseDTO.getGroundSupportStartTime(),"登机保障","登机保障开始"));
            scheduleList.add(new TfScheduleInfo(tfSpecialServiceQueryResponseDTO.getGroundSupportEndTime(),"登机保障","登机保障结束"));
            scheduleList.add(new TfScheduleInfo(tfSpecialServiceQueryResponseDTO.getBoardingTime(),"登机状态","已登机"));
            scheduleList.add(new TfScheduleInfo(tfSpecialServiceQueryResponseDTO.getArrGuaStartTime(),"接机保障","接机保障开始"));
            scheduleList.add(new TfScheduleInfo(tfSpecialServiceQueryResponseDTO.getArrGuaEndTime(),"特服保障","保障结束"));
        }

    }
}

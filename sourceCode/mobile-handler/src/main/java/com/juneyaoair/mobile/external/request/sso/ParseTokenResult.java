package com.juneyaoair.mobile.external.request.sso;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description; 解析token返回结果
 * @Version: V1.0
 * created by
 */
@Data
public class ParseTokenResult {

    @ApiModelProperty(value = "渠道")
    private String channelCode;

    @ApiModelProperty(value = "会员ID")
    private String ffpId;

    @ApiModelProperty(value = "会员卡号")
    private String ffpNo;

    @ApiModelProperty(value = "用户openId(如：微信openId)")
    private String openId;

    @ApiModelProperty(value = "用户userId(如：支付宝userId)")
    private String userId;

    @ApiModelProperty(value = "是否登录状态 true:登录 false:未登录")
    private boolean login;

    @ApiModelProperty(value = "账号是否启用状态 true:启用 false:关闭")
    private boolean accountStatus;

}

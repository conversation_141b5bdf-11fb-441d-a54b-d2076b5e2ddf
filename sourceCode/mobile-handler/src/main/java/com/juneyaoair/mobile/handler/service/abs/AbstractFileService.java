package com.juneyaoair.mobile.handler.service.abs;

import com.juneyaoair.mobile.handler.service.IFileService;

import java.util.Calendar;

/**
 * <AUTHOR>
 * @description
 * @date 2024/3/26 16:09
 */
public abstract class AbstractFileService implements IFileService {
    /**
     * 文件路径
     *
     * @param prefix 前缀
     * @param path   路径
     * @return 返回上传路径
     */
    protected String getPathByPath(String prefix, String path,String newFileName) {
        int lastIndexOf = path.lastIndexOf(".");
        if (lastIndexOf == -1) {
            return getPath(prefix, "",newFileName);
        }
        String suffix = path.substring(lastIndexOf);
        return getPath(prefix, suffix,newFileName);
    }

    /**
     * 文件路径
     *
     * @param prefix 前缀
     * @param suffix 后缀
     * @return 返回上传路径
     */
    private String getPath(String prefix, String suffix,String newFileName) {
        // 获取当前时间的Calendar实例
        Calendar cal = Calendar.getInstance();
        // 获取年份
        int year = cal.get(Calendar.YEAR);
        int month = cal.get(Calendar.MONTH) + 1;
        int dayOfMonth = cal.get(Calendar.DAY_OF_MONTH);
        //文件路径
        return prefix + "/" + year + "/" + month + "/" + dayOfMonth + "/" + newFileName + suffix;
    }
}

package com.juneyaoair.mobile.handler.controller.v2;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.juneyaoair.appenum.ChannelCodeEnum;
import com.juneyaoair.appenum.SensitiveOperationEnum;
import com.juneyaoair.appenum.WSEnum;
import com.juneyaoair.appenum.common.CheckInSeatStatus;
import com.juneyaoair.baseclass.common.request.BaseReq;
import com.juneyaoair.baseclass.common.request.BaseRequestDTO;
import com.juneyaoair.baseclass.common.response.BaseResultDTO;
import com.juneyaoair.baseclass.common.response.ReturnJson;
import com.juneyaoair.baseclass.cuss.request.SeatOrderParam;
import com.juneyaoair.baseclass.cuss.response.SeatOrderInfo;
import com.juneyaoair.baseclass.cuss.response.SeatOrderResult;
import com.juneyaoair.baseclass.cuss.response.SeatOrderSingle;
import com.juneyaoair.baseclass.member.request.AccountCheckParam;
import com.juneyaoair.baseclass.member.request.TicketInfoQueryRequest;
import com.juneyaoair.baseclass.member.response.TicketInfoQueryResponse;
import com.juneyaoair.baseclass.request.newCheckinSeat.CheckInSeatChartRequest;
import com.juneyaoair.baseclass.request.newCheckinSeat.GetSeatMapPsgInfoRequest;
import com.juneyaoair.baseclass.request.seat.ApiReserveSeatRequest;
import com.juneyaoair.baseclass.request.seat.EmdCancelSeat;
import com.juneyaoair.baseclass.request.seat.EmdRefundSeat;
import com.juneyaoair.baseclass.request.seat.SeatInfoQueryParam;
import com.juneyaoair.baseclass.response.newCheckinSeat.GetSelectSeatResponse;
import com.juneyaoair.baseclass.response.newCheckinSeat.v2.CheckInSeatChartResponseV2;
import com.juneyaoair.baseclass.response.newCheckinSeat.v2.CheckInSeatReserveResponseV2;
import com.juneyaoair.baseclass.response.newCheckinSeat.v2.SelectSeatReserveResponseV2;
import com.juneyaoair.baseclass.response.seat.SeatInfoResult;
import com.juneyaoair.baseclass.response.seat.TravellerTripTipResult;
import com.juneyaoair.client.ClientInfo;
import com.juneyaoair.crm.IMemberService;
import com.juneyaoair.cuss.dto.booking.request.member.MemberRequest;
import com.juneyaoair.cuss.dto.booking.request.seat.LocalSeatInfoParam;
import com.juneyaoair.cuss.dto.booking.request.seat.v2.SeatRequest;
import com.juneyaoair.cuss.dto.booking.response.member.MemberResponse;
import com.juneyaoair.cuss.dto.booking.response.order.CreateOrderResult;
import com.juneyaoair.cuss.dto.booking.response.seat.FlightSeatStatusResult;
import com.juneyaoair.cuss.dto.booking.response.seat.v2.CheckInFailed;
import com.juneyaoair.cuss.dto.booking.response.seat.v2.SeatChartResponseV2;
import com.juneyaoair.cuss.dto.booking.response.seat.v2.SelectSeatInfoResult;
import com.juneyaoair.cuss.dto.booking.response.seat.v2.SingleSelectSeatInfo;
import com.juneyaoair.cuss.enums.ENUM_FLIGHT_SEAT_STATUS;
import com.juneyaoair.exception.CommonException;
import com.juneyaoair.mobile.aop.annotation.InterfaceLog;
import com.juneyaoair.mobile.comm.service.IRedisService;
import com.juneyaoair.mobile.constants.MobileCoreBeanNames;
import com.juneyaoair.mobile.handler.HandlerConstants;
import com.juneyaoair.mobile.handler.bean.flight.FlightInfoResult;
import com.juneyaoair.mobile.handler.bean.travel.BoardInfoResultDTO;
import com.juneyaoair.mobile.handler.comm.RedisKeyConfig;
import com.juneyaoair.mobile.handler.comm.SourceType;
import com.juneyaoair.mobile.handler.config.bean.HandConfig;
import com.juneyaoair.mobile.handler.controller.BassController;
import com.juneyaoair.mobile.handler.controller.service.FlightService;
import com.juneyaoair.mobile.handler.controller.service.FlightTourService;
import com.juneyaoair.mobile.handler.controller.util.CussObjectConvert;
import com.juneyaoair.mobile.handler.sdk.TravellerHttpApi;
import com.juneyaoair.mobile.handler.service.CheckInSeatService;
import com.juneyaoair.mobile.handler.service.IMemberPasswordService;
import com.juneyaoair.mobile.handler.service.IOrderService;
import com.juneyaoair.mobile.handler.service.LocalCacheService;
import com.juneyaoair.mobile.interceptor.bean.VerifyTokenResp;
import com.juneyaoair.mobile.interceptor.bean.VerifyTokenResult;
import com.juneyaoair.mobile.interceptor.utils.TokenUtils;
import com.juneyaoair.mobile.mapstruct.FlightInfoResultMapping;
import com.juneyaoair.mobile.webservice.client.CrmWSClient;
import com.juneyaoair.mobile.webservice.client.crm.VerifyConsumePasswdResponseForClient;
import com.juneyaoair.pattern.PatternCommon;
import com.juneyaoair.util.ControllerUtil;
import com.juneyaoair.utils.EncoderHandler;
import com.juneyaoair.utils.StringUtil;
import com.juneyaoair.utils.http.HttpResult;
import com.juneyaoair.utils.http.HttpUtil;
import com.juneyaoair.utils.json.JsonMapper;
import com.juneyaoair.utils.json.JsonUtil;
import com.juneyaoair.utils.util.DateUtils;
import com.juneyaoair.utils.util.MdcUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import java.math.BigDecimal;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @Author: caolei
 * @Description: 值机选座服务 V2
 * @Date: 2021/6/29 9:44
 * @Modified by:
 */
@Slf4j
@RestController
@RequestMapping("/checkInSeatService")
@Api(value = "值机选座服务V2", tags = "值机选座服务V2")
public class V2CheckInSeatController extends BassController {

    @Resource(name = MobileCoreBeanNames.API_REDIS_SERVICE)
    private IRedisService apiRedisService;

    @Autowired
    private CrmWSClient crmClient;

    @Autowired
    private FlightService flightService;
    @Autowired
    private FlightTourService flightTourService;
    @Autowired
    private CheckInSeatService checkInSeatService;

    @Autowired
    private HandConfig handConfig;

    @Autowired
    private IOrderService orderService;

    @Autowired
    private LocalCacheService localCacheService;
    @Autowired
    private IMemberService iMemberService;
    @Autowired
    private IMemberPasswordService memberPasswordService;
    @Autowired
    private TravellerHttpApi travellerHttpApi;

    private static final String SUCCESS = "000000";

    /**
     * 获取值机选座座位图 V2
     *
     * @param seatChartMapRequest
     * @param request
     * @return
     */
    @InterfaceLog
    @ApiOperation(value = "获取航班座位图V2", notes = "获取航班座位图")
    @RequestMapping(value = "/getFlightSeatCharts/v2", method = RequestMethod.POST)
    public ReturnJson<CheckInSeatChartResponseV2> getFlightSeatCharts(@RequestBody CheckInSeatChartRequest seatChartMapRequest, HttpServletRequest request) {
        String uuid = MdcUtils.getRequestId();
        ReturnJson<CheckInSeatChartResponseV2> returnJson = new ReturnJson<>();
        String channelCodeHead = request.getHeader("channelCode");
        //输入参数校验
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<CheckInSeatChartRequest>> violations = validator.validate(seatChartMapRequest);
        if (null != violations && !violations.isEmpty()) {
            returnJson.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            returnJson.setErrorInfo(violations.iterator().next().getMessage());
            return returnJson;
        }
        // IP认证
        String clientIp = this.getClientIP(request);
        // 控制未登录和登陆后限制查询次数的差异
        Map<String, String> map = checkInVisitLimit(seatChartMapRequest, clientIp, seatChartMapRequest.getFfpId(), seatChartMapRequest.getFfpCardNo(), seatChartMapRequest.getLoginKeyInfo(), seatChartMapRequest.getChannelCode());
        if (!WSEnum.SUCCESS.getResultCode().equals(map.get("code"))) {
            returnJson.setResultCode(map.get("code"));
            returnJson.setErrorInfo(map.get("message"));
            return returnJson;
        }
        // 实际机型，用于航班apollo控制
        if (CheckInSeatStatus.CHECK_IN_SEAT_CLOSE.getCode().equals(seatChartMapRequest.getCheckInSeatStatus())) {
            returnJson.setResultCode(WSEnum.ERROR.getResultCode());
            returnJson.setErrorInfo("值机选座已关闭，暂不能获取座位图");
            return returnJson;
        }

        // 封装请求座位图参数
        BaseRequestDTO<SeatRequest> baseSeatRequest = new BaseRequestDTO<>();
        SeatRequest seatMapRequest = new SeatRequest();
        seatMapRequest.setArrAirportCode(seatChartMapRequest.getArrAirportCode());
        seatMapRequest.setDepAirportCode(seatChartMapRequest.getDepAirportCode());
        GetSeatMapPsgInfoRequest firstPsg = seatChartMapRequest.getGetSeatMapPsgInfos().get(0);
        seatMapRequest.setTicketNo(firstPsg.getTicketNo());
        seatMapRequest.setPassengerName(firstPsg.getPsrName());
        seatMapRequest.setPnrNo(firstPsg.getPnrNo());
        seatMapRequest.setCabin(firstPsg.getCabin());
        seatMapRequest.setFlightDate(seatChartMapRequest.getFlightDate());
        seatMapRequest.setFlightNo(seatChartMapRequest.getFlightNo());
        seatMapRequest.setMarketingFlightNo(seatChartMapRequest.getMarketingFlightNo());
        seatMapRequest.setCurrency(seatChartMapRequest.getCurrency());
        // 锁定安全出口所在排
        seatMapRequest.setLockTagSet(Sets.newHashSet("E"));
        baseSeatRequest.setIp(clientIp);
        baseSeatRequest.setVersion(HandlerConstants.NEW_CHECKIN_SEAT_VERSION);
        baseSeatRequest.setUserNo(String.valueOf(seatChartMapRequest.getFfpId()));
        baseSeatRequest.setRequest(seatMapRequest);

        // 结果对象
        CheckInSeatChartResponseV2 checkInSeatChartResponseV2 = new CheckInSeatChartResponseV2();
        // 渠道
        String channelCode = getChannelInfo(seatChartMapRequest.getChannelCode(), "50");
        // 设置渠道
        baseSeatRequest.setChannelCode(channelCode);
        //  记录接口返回结果
        SeatChartResponseV2 seatChartResponse;
        try {
            HttpResult httpResult = HttpUtil.doPostClient(baseSeatRequest, HandlerConstants.NEW_CHECKIN_SELECT_URL + HandlerConstants.NEW_EMD_SEAT_MAP);
            // 接口未获取到返回结果
            if (!httpResult.isResult()) {
                returnJson.setResultCode(WSEnum.ERROR.getResultCode());
                returnJson.setErrorInfo(httpResult.getResponse());
                return returnJson;
            }
            // 对返回结果进行转换
            BaseResultDTO<SeatChartResponseV2> resultDTO = JsonMapper.buildNonNullMapper().fromJson(httpResult.getResponse(), BaseResultDTO.class, SeatChartResponseV2.class);
            // 返回结果为空 或 返回code不为1001 返回异常
            if (null == resultDTO || !"1001".equals(resultDTO.getResultCode()) || null == resultDTO.getResult()) {
                log.error("{} 获取座位图超时或者出错", seatChartMapRequest.getCheckInSeatStatus());
                returnJson.setResultCode(WSEnum.ERROR.getResultCode());
                String message = null != resultDTO ? StringUtil.isNullOrEmpty(resultDTO.getErrorMsg()) ? "获取座位图出错" : resultDTO.getErrorMsg() : "获取座位图超时";
                returnJson.setErrorInfo(message);
                return returnJson;
            }
            seatChartResponse = resultDTO.getResult();
        } catch (Exception e) {
            log.error("{} 获取座位图出错,异常描述：{} 异常原因：", seatChartMapRequest.getCheckInSeatStatus(), e.getMessage(), e);
            returnJson.setResultCode(WSEnum.ERROR.getResultCode());
            returnJson.setErrorInfo("获取座位图出错");
            return returnJson;
        }

        checkInSeatChartResponseV2.setSeatCharts(seatChartResponse);
        //获取最高舱位
        checkInSeatChartResponseV2.setCabinClass(ControllerUtil.getHighestClassCabin(seatChartMapRequest.getGetSeatMapPsgInfos().get(0).getCabin()));
        // 从IBE获取预留座位信息
        List<GetSelectSeatResponse> ibeSelectSeatList = null;
        // 选座开放
        if (CheckInSeatStatus.SELECT_OPEN.getCode().equals(seatChartMapRequest.getCheckInSeatStatus())) {
            ibeSelectSeatList = checkInSeatService.queryIBESeatInfo(seatChartMapRequest, clientIp);
        } else if (CheckInSeatStatus.CHECK_IN_OPEN.getCode().equals(seatChartMapRequest.getCheckInSeatStatus())) {
            // 到值机时间去sypr拿离港预留座位信息
            ibeSelectSeatList = checkInSeatService.querySYPRSeatInfo(seatChartMapRequest, clientIp);
        }

        if (CollectionUtils.isEmpty(ibeSelectSeatList)) {
            throw new CommonException(WSEnum.ERROR.getResultCode(), "未查询到客票行程信息");
        }
        // 循环获取会员信息
        ibeSelectSeatList.parallelStream().forEach(ibeSelectSeatRequest -> {
            // 获取旅客会员信息
            MemberRequest memberRequest = new MemberRequest();
            memberRequest.setTicketNumber(ibeSelectSeatRequest.getTicketNo());
            MemberResponse memberResponse = checkInSeatService.getMemberInfo(memberRequest, seatChartMapRequest.getChannelCode(), clientIp);
            if (null != memberResponse) {
                Integer flightAge = DateUtils.getDateAge(DateUtils.toDate(seatChartMapRequest.getFlightDate()), DateUtils.toDate(memberResponse.getTravellerBirthdate()));
                ibeSelectSeatRequest.setPsrName(memberResponse.getTravellerName());
                if (StringUtils.isNotBlank(memberResponse.getTravellerSurname()) || StringUtils.isNotBlank(memberResponse.getTravellerGivenname())) {
                    ibeSelectSeatRequest.setPsrEnName(memberResponse.getTravellerSurname() + "/" + memberResponse.getTravellerGivenname());
                }
                ibeSelectSeatRequest.setFlightAge(flightAge);
                ibeSelectSeatRequest.setSeatMemberCode(memberResponse.getSeatMemberCode());
                ibeSelectSeatRequest.setMemberLevelCode(memberResponse.getMemberLevelCode());
            }
        });
        checkInSeatChartResponseV2.setGetIBESelectSeatResponseList(ibeSelectSeatList);
        if (!CollectionUtils.isEmpty(seatChartMapRequest.getGetSeatMapPsgInfos())) {
            boolean popInfo = freeBaggageAlert(seatChartMapRequest.getGetSeatMapPsgInfos(), seatChartMapRequest.getChannelCode(), channelCodeHead, clientIp);
            checkInSeatChartResponseV2.setFreeBaggageAllowanceAlert(popInfo);
        }
        checkInSeatChartResponseV2.setSeatDiscount(seatChartResponse.getSeatDiscount());
        // 设置返回
        returnJson.setResult(checkInSeatChartResponseV2);
        returnJson.setResultCode(WSEnum.SUCCESS.getResultCode());
        returnJson.setErrorInfo(WSEnum.SUCCESS.getResultInfo());
        log.info("{}_值机选座，返回获取航班座位图：{},时间：{}", uuid, JsonUtil.objectToJson(returnJson), DateUtils.getCurrentTimeStr());
        return returnJson;
    }

    private boolean freeBaggageAlert(List<GetSeatMapPsgInfoRequest> getSeatMapPsgInfoRequests, String channelCode, String headChannelCode, String clientIp) {
        /*String alertInfo = "";
        String correctInfo = "您购买的客票舱位不含免费托运行李额，可在办理值机前购买额外行李。";*/
        boolean alertInfo = Boolean.FALSE;
        String ticketNo;
        if (CollectionUtils.isEmpty(getSeatMapPsgInfoRequests)) {
            return alertInfo;
        }
        try {
            List<String> channels = new ArrayList<>();
            channels.add(ChannelCodeEnum.WXAPP.getChannelCode());
            channels.add(ChannelCodeEnum.MOBILE.getChannelCode());
            channels.add(ChannelCodeEnum.MWEB.getChannelCode());
            channels.add(ChannelCodeEnum.CHECKIN.getChannelCode());
            if (!channels.contains(headChannelCode)) {
                return alertInfo;
            }
            GetSeatMapPsgInfoRequest firstOne = getSeatMapPsgInfoRequests.stream().filter(elem -> StringUtils.isNotBlank(elem.getTicketNo())).findFirst().orElse(null);
            if (null == firstOne) {
                return alertInfo;
            }
            ticketNo = firstOne.getTicketNo();
            TicketInfoQueryRequest bigDataReq = new TicketInfoQueryRequest();
            bigDataReq.setTicketNumber(ticketNo.replace("-", ""));
            bigDataReq.setFlightStartDate(DateUtils.getCurrentDateStr());
            String key = "freeBaggageAlert:" + ticketNo;
            String redisServiceData = apiRedisService.getData(key);
            if (StringUtils.isNotBlank(redisServiceData)) {
                return alertInfo;
            }
            //接口加密
            bigDataReq.setSignature(EncoderHandler.encodeByMD5(HandlerConstants.BIGDATA_CLIENTCODE + HandlerConstants.BIGDATA_CLIENT_PASSWORD));
            Map<String, String> headMap = HttpUtil.getHeaderMap(clientIp, "");
            List<TicketInfoQueryResponse> bigDataTravellList = travellerHttpApi.searchTravellerItineraryInfo(bigDataReq, headMap);
            if (CollectionUtils.isEmpty(bigDataTravellList)) {
                return alertInfo;
            }
            String finalTicketNo = ticketNo;
            TicketInfoQueryResponse ticketInfoQueryResponse = bigDataTravellList.stream().filter(elem -> finalTicketNo.equals(elem.getTicket_ticketnumber())).findFirst().orElse(null);

            if (null == ticketInfoQueryResponse || StringUtils.isEmpty(ticketInfoQueryResponse.getSegment_baggageallowance())) {
                return alertInfo;
            }
            if ("00K".equals(ticketInfoQueryResponse.getSegment_baggageallowance())) {
                apiRedisService.putData(key, "Y", 24 * 60 * 60L);
                return Boolean.TRUE;
            }
        } catch (Exception e) {
            log.error("获取弹窗信息出错，出错信息为：{}", e.getMessage());
        }
        return alertInfo;
    }

    @InterfaceLog
    @ApiOperation(value = "操作值机选座V2", notes = "操作值机选座V2")
    @RequestMapping(value = "/doCheckInSelectSeat/v2", method = RequestMethod.POST)
    public CheckInSeatReserveResponseV2 doCheckInSelectSeat(@RequestBody @Validated ApiReserveSeatRequest apiReserveSeatRequest, BindingResult bindingResult, HttpServletRequest request) {
        CheckInSeatReserveResponseV2 result = new CheckInSeatReserveResponseV2();
        String uuid = UUID.randomUUID().toString();
        log.info("{}_值机选座，操作值机选座：{},时间：{}", uuid, JsonUtil.objectToJson(apiReserveSeatRequest), DateUtils.getCurrentTimeStr());
        String clientIp = "";
        if (StringUtil.isNullOrEmpty(apiReserveSeatRequest.getClientIp())) {
            clientIp = this.getClientIP(request);
        } else {
            clientIp = apiReserveSeatRequest.getClientIp();
            if (!this.checkToken(apiReserveSeatRequest.getToken(), clientIp, HandlerConstants.ACCESSSECRET)) {
                result.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                result.setErrorInfo("您的访问出意外啦！");
                return result;
            }
        }

        // 控制未登录和登陆后限制查询次数的差异
        Map<String, String> mapResult = checkInVisitLimit(apiReserveSeatRequest, clientIp, apiReserveSeatRequest.getFfpId(), apiReserveSeatRequest.getFfpCardNo(), apiReserveSeatRequest.getLoginKeyInfo(), apiReserveSeatRequest.getChannelCode());
        if (!WSEnum.SUCCESS.getResultCode().equals(mapResult.get("code"))) {
            result.setResultCode(mapResult.get("code"));
            result.setErrorInfo(mapResult.get("message"));
            return result;
        }
        // 登录认证
        String token = request.getHeader("token");
        String channel = request.getHeader("channelCode");
        if (StringUtil.isNullOrEmpty(apiReserveSeatRequest.getFfpCardNo())) {
            result.setResultCode(WSEnum.INVALID_TOKEN.getResultCode());
            result.setErrorInfo(WSEnum.INVALID_TOKEN.getResultInfo());
            log.info("{}_付费选座需要登录{}", JsonUtil.objectToJson(apiReserveSeatRequest), JsonUtil.objectToJson(result));
            return result;
        }
        // 校验请求参数
        if (bindingResult.hasErrors()) {
            result.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            result.setErrorInfo(bindingResult.getAllErrors().get(0).getDefaultMessage());
            return result;
        }
        ClientInfo clientInfo = initClientInfo(request,apiReserveSeatRequest.getChannelCode(),String.valueOf(apiReserveSeatRequest.getFfpId()),apiReserveSeatRequest.getFfpCardNo());
        // 使用积分需要验证用户的消费密码
        if (apiReserveSeatRequest.getUseTotalScore().compareTo(BigDecimal.ZERO) > 0) {
            // 消费密码
            String patternStr = PatternCommon.SALE_P_W_D;
            Pattern pattern = Pattern.compile(patternStr);
            Matcher matcher = pattern.matcher(apiReserveSeatRequest.getSalePwd());
            if (!matcher.matches()) {
                result.setResultCode(WSEnum.ERROR.getResultCode());
                result.setErrorInfo("消费密码为六位数字");
                return result;
            }
            AccountCheckParam accountCheckParam = new AccountCheckParam();
            accountCheckParam.setFfpCardNo(apiReserveSeatRequest.getFfpCardNo());
            accountCheckParam.setFfpId(String.valueOf(apiReserveSeatRequest.getFfpId()));
            accountCheckParam.setOperation(SensitiveOperationEnum.USE_SALE.getOperation());
            accountCheckParam.setBlackBox(apiReserveSeatRequest.getBlackBox());
            memberPasswordService.checkAccount(clientInfo,accountCheckParam,null,null);
            if (orderService.checkScoreUseRule(clientInfo,channel)) {
                result.setResultCode(WSEnum.NO_REAL_NAME.getResultCode());
                result.setErrorInfo("非常抱歉，当前账户存在安全风险，通过实名认证后才可使用积分，请前往实名认证");
                return result;
            }
            VerifyConsumePasswdResponseForClient clientResp = crmClient.verifyConsumePwd(apiReserveSeatRequest.getFfpId(), apiReserveSeatRequest.getSalePwd(),
                    apiReserveSeatRequest.getChannelCode(), ControllerUtil.getChannelInfo(apiReserveSeatRequest.getChannelCode(), "40"));
            if (!"S000".equals(clientResp.getMessageHeader().getErrorCode())) {
                result.setResultCode(WSEnum.ERROR.getResultCode());
                result.setErrorInfo(clientResp.getMessageHeader().getDescription());
                return result;
            }
        }

        // 不存在币种设置默认币种RMB
        if (StringUtils.isBlank(apiReserveSeatRequest.getCurrency())) {
            apiReserveSeatRequest.setCurrency("CNY");
        }
        // 查询航班选座状态
        FlightSeatStatusResult flightSeatStatusResult = checkInSeatService.getFlightSeatStatus(apiReserveSeatRequest.getFlightNo(), apiReserveSeatRequest.getFlightDate(), apiReserveSeatRequest.getDepAirportCode(), apiReserveSeatRequest.getArrAirportCode());
        if (ENUM_FLIGHT_SEAT_STATUS.NONE.equals(flightSeatStatusResult.getFlightSeatStatus())) {
            throw new CommonException(WSEnum.ERROR_CHK_ERROR.getResultCode(), "值机选座已关闭，不能进行选座");
        }
        // 执行选座操作
        BaseResultDTO<SelectSeatReserveResponseV2> resultDTO = checkInSeatService.doSelectSeat(apiReserveSeatRequest, clientIp, flightSeatStatusResult.getFlightSeatStatus());
        String headChannelCode = request.getHeader(HEAD_CHANNEL_CODE);
        //2021-05-20 值机小程序 不处理时间:，统一返回hh:mm，其他渠道返回hhmm
        if ((ChannelCodeEnum.CHECKIN.getChannelCode().equals(headChannelCode)) && null != resultDTO.getResult()) {
            SelectSeatReserveResponseV2 selectSeatResult = resultDTO.getResult();
            String depDateTime = selectSeatResult.getDepDateTime();
            selectSeatResult.setDepDateTime(StringUtils.isNotBlank(depDateTime) ? depDateTime.substring(0, 2) + ":" + depDateTime.substring(2) : "");
            String arrDateTime = selectSeatResult.getArrDateTime();
            selectSeatResult.setArrDateTime(StringUtils.isNotBlank(arrDateTime) ? arrDateTime.substring(0, 2) + ":" + arrDateTime.substring(2) : "");
        }

        result.setSelectSeatReserveResponse(resultDTO.getResult());
        if (WSEnum.SUCCESS.getResultCode().equals(resultDTO.getResultCode())) {
            // 添加赠送优惠券校验的缓存
            apiReserveSeatRequest.getReserveSeats().forEach(reserveSeatDetail -> apiRedisService.putData(RedisKeyConfig.genCheckSendCouponKey(reserveSeatDetail.getTktNo(),
                    apiReserveSeatRequest.getFfpCardNo()), reserveSeatDetail.getTktNo() + apiReserveSeatRequest.getFfpCardNo(), 2 * 60 * 60L));
            result.setResultCode(WSEnum.SUCCESS.getResultCode());
            log.info("{}_值机选座，返回操作选座成功信息：{},时间：{}", uuid, JsonUtil.objectToJson(result), DateUtils.getCurrentTimeStr());
        } else {
            result.setResultCode(WSEnum.ERROR.getResultCode());
            result.setErrorInfo(resultDTO.getErrorMsg());
            log.info("{}_值机选座，返回操作选座失败信息：{},时间：{}", uuid, JsonUtil.objectToJson(result), DateUtils.getCurrentTimeStr());
        }

        SelectSeatReserveResponseV2 failedList = resultDTO.getResult();
        if (null != failedList) {
            if (ENUM_FLIGHT_SEAT_STATUS.CHECK.equals(flightSeatStatusResult.getFlightSeatStatus()) && !CollectionUtils.isEmpty(failedList.getFailedSeatResultList())) {
                List<CheckInFailed> failedSeatResultList = failedList.getFailedSeatResultList();
                List<String> nameSt = failedSeatResultList.stream().map(CheckInFailed::getPsgrName).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(nameSt)) {
                    String nameString = StringUtils.join(nameSt, "、");
                    String nameResult = String.format(WSEnum.CHECK_IN_FAILED.getResultInfo(), nameString);
                    result.setResultCode(WSEnum.CHECK_IN_FAILED.getResultCode());
                    result.setErrorInfo(nameResult);
                }
            }
        }
        return result;
    }

    /**
     * 取消免费选座
     *
     * @param emdCancelSeat
     * @param request
     * @return
     */
    @InterfaceLog
    @ApiOperation(value = "取消免费选座", notes = "取消免费选座")
    @RequestMapping(value = "/cancelSeat/v2", method = RequestMethod.POST)
    public ReturnJson<String> cancelSeat(@RequestBody EmdCancelSeat emdCancelSeat, HttpServletRequest request) {
        ReturnJson<String> returnJson = new ReturnJson<>();
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<EmdCancelSeat>> violations = validator.validate(emdCancelSeat);
        if (null != violations && violations.size() > 0) {
            returnJson.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            returnJson.setErrorInfo(violations.iterator().next().getMessage());
            return returnJson;
        }
        //验证IP
        String clientIp = this.getClientIP(request);
        // 控制未登录和登陆后限制查询次数的差异
        Map<String, String> mapResult = checkInVisitLimit(emdCancelSeat, clientIp, emdCancelSeat.getFfpId(), emdCancelSeat.getFfpCardNo(), emdCancelSeat.getLoginKeyInfo(), emdCancelSeat.getChannelCode());
        if (!WSEnum.SUCCESS.getResultCode().equals(mapResult.get("code"))) {
            returnJson.setResultCode(mapResult.get("code"));
            returnJson.setErrorInfo(mapResult.get("message"));
            return returnJson;
        }

        // 取消时需短信验证
        if (StringUtils.isBlank(emdCancelSeat.getPhone())) {
            returnJson.setResultCode(WSEnum.ERROR.getResultCode());
            returnJson.setErrorInfo("验证手机不能为空");
            return returnJson;
        }
        if (StringUtils.isBlank(emdCancelSeat.getVerifyCode())) {
            returnJson.setResultCode(WSEnum.ERROR.getResultCode());
            returnJson.setErrorInfo("取消验证码不能为空");
            return returnJson;
        }
        String channelCode = ControllerUtil.getChannelInfo(emdCancelSeat.getChannelCode(), "50");
        // 查询cuss获取本地选座信息(获取已选座或已值机数据)
        LocalSeatInfoParam seatInfoRequest = new LocalSeatInfoParam();
        seatInfoRequest.setSeatStatus("T");
        seatInfoRequest.setSeatCheckInStatus("AC");
        seatInfoRequest.setFlightNo(emdCancelSeat.getFlightNo());
        seatInfoRequest.setFlightDate(emdCancelSeat.getFlightDate());
        seatInfoRequest.setDepAirportCode(emdCancelSeat.getDepAirportCode());
        seatInfoRequest.setArrAirportCode(emdCancelSeat.getArrAirportCode());
        seatInfoRequest.setTktNo(emdCancelSeat.getTktNo());
        SelectSeatInfoResult selectSeatInfoResult = checkInSeatService.getLocalSeatInfo(seatInfoRequest, channelCode, clientIp);
        if (null == selectSeatInfoResult || CollectionUtils.isEmpty(selectSeatInfoResult.getSelectSeatReservePsgInfos())) {
            returnJson.setResultCode(WSEnum.ERROR.getResultCode());
            returnJson.setErrorInfo("无值机选座信息");
            return returnJson;
        }
        SingleSelectSeatInfo singleSelectSeatInfo = selectSeatInfoResult.getSelectSeatReservePsgInfos().get(0);
        if (!emdCancelSeat.getPhone().equals(singleSelectSeatInfo.getMobile())) {
            returnJson.setResultCode(WSEnum.ERROR.getResultCode());
            returnJson.setErrorInfo("验证手机与选座手机不一致");
            return returnJson;
        }
        if (!checkVeriCode("SMS:" + emdCancelSeat.getPhone() + SourceType.EMD_CANCEL_SEAT.getValue(), emdCancelSeat.getVerifyCode())) {
            String errorInfo = "验证码错误或已经失效！";
            returnJson.setResultCode(WSEnum.ERROR.getResultCode());
            returnJson.setErrorInfo(errorInfo);
            return returnJson;
        } else {
            apiRedisService.removeData("SMS:" + emdCancelSeat.getPhone() + SourceType.EMD_CANCEL_SEAT.getValue());
            // 验证成功，清除账号限制
            this.clearDayVisit("", emdCancelSeat.getPhone(), SourceType.EMD_CANCEL_SEAT.getValue(), "");
        }

        // 调取消接口
        checkInSeatService.cancelSeat(emdCancelSeat, clientIp);
        returnJson.setResultCode(WSEnum.SUCCESS.getResultCode());
        returnJson.setErrorInfo("取消成功");
        return returnJson;
    }

    /**
     * 取消免费选座
     *
     * @param refundSeatOrder
     * @param request
     * @return
     */
    @InterfaceLog
    @ApiOperation(value = "付费选座退单", notes = "付费选座退单")
    @RequestMapping(value = "/refundSeatOrder/v2", method = RequestMethod.POST)
    public ReturnJson<String> refundSeatOrder(@RequestBody EmdRefundSeat refundSeatOrder, HttpServletRequest request) {
        ReturnJson<String> returnJson = new ReturnJson<>();
        String uuid = UUID.randomUUID().toString();
        log.info("{}_值机选座，付费选座退单Request:{},时间：{}", uuid, JsonUtil.objectToJson(refundSeatOrder), DateUtils.getCurrentTimeStr());
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<EmdRefundSeat>> violations = validator.validate(refundSeatOrder);
        if (null != violations && violations.size() > 0) {
            returnJson.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            returnJson.setErrorInfo(violations.iterator().next().getMessage());
            return returnJson;
        }
        // 是否自愿退票 只能为 Y/N
        if (!"Y".equals(refundSeatOrder.getIsVoluntaryRefund()) && !"N".equals(refundSeatOrder.getIsVoluntaryRefund())) {
            returnJson.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            returnJson.setErrorInfo("退单类型不正确！");
            return returnJson;
        }
        //验证IP
        String clientIp = "";
        if (StringUtil.isNullOrEmpty(refundSeatOrder.getClientIp())) {
            clientIp = this.getClientIP(request);
        } else {
            clientIp = refundSeatOrder.getClientIp();
            if (!this.checkToken(refundSeatOrder.getToken(), clientIp, HandlerConstants.ACCESSSECRET)) {
                returnJson.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
                returnJson.setErrorInfo("您的访问出意外啦！");
                return returnJson;
            }
        }

        // 控制未登录和登陆后限制查询次数的差异
        Map<String, String> mapResult = checkInVisitLimit(refundSeatOrder, clientIp, refundSeatOrder.getFfpId(), refundSeatOrder.getFfpCardNo(), refundSeatOrder.getLoginKeyInfo(), refundSeatOrder.getChannelCode());
        if (!WSEnum.SUCCESS.getResultCode().equals(mapResult.get("code"))) {
            returnJson.setResultCode(mapResult.get("code"));
            returnJson.setErrorInfo(mapResult.get("message"));
            return returnJson;
        }

        // 取消时需短信验证
        if (StringUtils.isBlank(refundSeatOrder.getPhone())) {
            returnJson.setResultCode(WSEnum.ERROR.getResultCode());
            returnJson.setErrorInfo("验证手机不能为空");
            return returnJson;
        }
        if (StringUtils.isBlank(refundSeatOrder.getVerifyCode())) {
            returnJson.setResultCode(WSEnum.ERROR.getResultCode());
            returnJson.setErrorInfo("取消验证码不能为空");
            return returnJson;
        }
        // 查询订单信息
        SeatOrderInfo seatOrderInfo = checkInSeatService.getSeatOrderInfo(refundSeatOrder.getChannelCode(), clientIp, refundSeatOrder.getOrderNo(), refundSeatOrder.getChannelOrderNo(), refundSeatOrder.getCouponCode());
        if (!"Y".equals(seatOrderInfo.getIsSelf())) {
            returnJson.setResultCode(WSEnum.ERROR.getResultCode());
            returnJson.setErrorInfo("外部渠道订单，请去原渠道取消！");
            return returnJson;
        }
        // 获取选座系统订单信息
        SeatOrderParam seatOrderParam = new SeatOrderParam();
        seatOrderParam.setOrderNo(refundSeatOrder.getOrderNo());
        seatOrderParam.setChannelOrderNo(refundSeatOrder.getChannelOrderNo());
        seatOrderParam.setCouponCode(refundSeatOrder.getCouponCode());
        SeatOrderResult seatOrderResult = checkInSeatService.getSeatOrder(seatOrderParam);
        if (null == seatOrderResult || CollectionUtils.isEmpty(seatOrderResult.getSeatOrderList())) {
            returnJson.setResultCode(WSEnum.ERROR.getResultCode());
            returnJson.setErrorInfo("无付费选座订单信息");
            return returnJson;
        }
        SeatOrderSingle seatOrderSingle = seatOrderResult.getSeatOrderList().get(0);
        if (!refundSeatOrder.getPhone().equals(seatOrderSingle.getMobile())) {
            returnJson.setResultCode(WSEnum.ERROR.getResultCode());
            returnJson.setErrorInfo("验证手机与选座手机不一致");
            return returnJson;
        }
        if (!checkVeriCode("SMS:" + refundSeatOrder.getPhone() + SourceType.EMD_CANCEL_SEAT.getValue(), refundSeatOrder.getVerifyCode())) {
            String errorInfo = "验证码错误或已经失效！";
            returnJson.setResultCode(WSEnum.ERROR.getResultCode());
            returnJson.setErrorInfo(errorInfo);
            return returnJson;
        } else {
            apiRedisService.removeData("SMS:" + refundSeatOrder.getPhone() + SourceType.EMD_CANCEL_SEAT.getValue());
            // 验证成功，清除账号限制
            this.clearDayVisit("", refundSeatOrder.getPhone(), SourceType.EMD_CANCEL_SEAT.getValue(), "");
        }

        // 调退单接口
        return checkInSeatService.refundSeatOrder(refundSeatOrder, clientIp);
    }

    /**
     * 验证验证码
     *
     * @param key
     * @param code
     * @return
     */
    public boolean checkVeriCode(String key, String code) {
        String veryCodeCacheStr = apiRedisService.getData(key);
        log.info("取消值机 key：{}", key);
        log.info("取消值机验证码：{}", veryCodeCacheStr);
        return !(StringUtils.isBlank(code) || StringUtils.isBlank(veryCodeCacheStr) || !code.equals(veryCodeCacheStr));
    }

    /**
     * 查看本地选座结果
     *
     * @param seatInfoRequestBase
     * @param request
     * @return
     */
    @InterfaceLog
    @ApiOperation(value = "查看本地选座结果（仅适用于需要现金支付，支付完成首次查看选座结果）", notes = "查看本地选座结果（仅适用于需要现金支付，支付完成首次查看选座结果）")
    @RequestMapping(value = "/getLocalSeatInfo/v2", method = RequestMethod.POST)
    public ReturnJson<SeatInfoResult> getLocalSeatInfo(@RequestBody BaseReq<SeatInfoQueryParam> seatInfoRequestBase, HttpServletRequest request) {
        SeatInfoQueryParam seatInfoRequest = seatInfoRequestBase.getRequest();
        if (StringUtils.isAnyBlank(seatInfoRequest.getFlightNo(), seatInfoRequest.getFlightDate(), seatInfoRequest.getDepAirportCode(),
                seatInfoRequest.getArrAirportCode())) {
            ReturnJson<SeatInfoResult> result = new ReturnJson<>();
            result.setResultCode(WSEnum.ERROR.getResultCode());
            result.setErrorInfo("参数校验不通过");
            return result;
        }
        if (StringUtils.isBlank(seatInfoRequest.getChannelOrderNo()) && StringUtils.isBlank(seatInfoRequest.getTktNo())) {
            ReturnJson<SeatInfoResult> result = new ReturnJson<>();
            result.setResultCode(WSEnum.ERROR.getResultCode());
            result.setErrorInfo("参数校验不通过");
            return result;
        }
        // 参数校验
        if (StringUtil.isNullOrEmpty(seatInfoRequest.getFfpId()) || StringUtil.isNullOrEmpty(seatInfoRequest.getFfpCardNo())) {
            ReturnJson<SeatInfoResult> result = new ReturnJson<>();
            result.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            result.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return result;
        }
        // 验证用户查询是否正常
        boolean flag = this.checkKeyInfo(seatInfoRequest.getFfpId(), seatInfoRequest.getLoginKeyInfo(), seatInfoRequestBase.getChannelCode());
        if (!flag) {
            ReturnJson<SeatInfoResult> result = new ReturnJson<>();
            result.setResultCode(WSEnum.ERROR_CHK_ERROR.getResultCode());
            result.setErrorInfo(WSEnum.ERROR_CHK_ERROR.getResultInfo());
            return result;
        }

        String clientIp = this.getClientIP(request);
        String channelCode = ControllerUtil.getChannelInfo(seatInfoRequestBase.getChannelCode(), "50");
        seatInfoRequest.setCreateCheckIn(true);
        SelectSeatInfoResult selectSeatInfoResult = checkInSeatService.getLocalSeatInfo(seatInfoRequest, channelCode, clientIp);
        if (null == selectSeatInfoResult) {
            ReturnJson<SeatInfoResult> result = new ReturnJson<>();
            result.setResultCode(WSEnum.ERROR.getResultCode());
            result.setErrorInfo("服务暂不可用，请稍后再试");
            return result;
        }
        if (CollectionUtils.isEmpty(selectSeatInfoResult.getSelectSeatReservePsgInfos())) {
            ReturnJson<SeatInfoResult> result = new ReturnJson<>();
            result.setResultCode(WSEnum.ERROR.getResultCode());
            result.setErrorInfo("未获取到值机选座数据");
            return result;
        }
        // 获取全部机场信息
        FlightInfoResult flightInfoResult = flightService.getFlightInfo(seatInfoRequest.getFlightDate(), seatInfoRequest.getFlightNo(), seatInfoRequest.getDepAirportCode(), seatInfoRequest.getArrAirportCode(), Lists.newArrayList("mileage"));
        SeatInfoResult seatInfoResult = FlightInfoResultMapping.toSeatInfoResult(flightInfoResult);
        SingleSelectSeatInfo singleSelectSeatInfo = selectSeatInfoResult.getSelectSeatReservePsgInfos().get(0);
        seatInfoResult.setCabin(null == singleSelectSeatInfo ? "" : singleSelectSeatInfo.getCabin());
        CreateOrderResult createOrderResult = selectSeatInfoResult.getOrderResult();
        if (null != createOrderResult) {
            SeatOrderInfo seatOrderInfo = checkInSeatService.getSeatOrderInfo(channelCode, clientIp, createOrderResult.getOrderNo(), createOrderResult.getOrderChannelOrderNo(), null);
            seatInfoResult.setOrderResult(seatOrderInfo);
        }
        // 查询航班选座状态
        FlightSeatStatusResult flightSeatStatusResult = checkInSeatService.getFlightSeatStatus(seatInfoRequest.getFlightNo(), seatInfoRequest.getFlightDate(), seatInfoRequest.getDepAirportCode(), seatInfoRequest.getArrAirportCode());
        // 开放值机
        if (ENUM_FLIGHT_SEAT_STATUS.CHECK.equals(flightSeatStatusResult.getFlightSeatStatus())) {
            seatInfoResult.setCheckInReservePsgInfos(selectSeatInfoResult.getSelectSeatReservePsgInfos());
            seatInfoResult.setBoardingGateNumber(selectSeatInfoResult.getBoardingGateNumber());
            seatInfoResult.setBordingTime(selectSeatInfoResult.getBordingTime());
            // 登机时间为空 调用接口获取登机时间、登记口信息
            if (StringUtils.isBlank(selectSeatInfoResult.getBordingTime())) {
                BoardInfoResultDTO boardInfoResultDTO = checkInSeatService.queryFlightBoardInAirport(seatInfoRequest.getFlightNo(), seatInfoRequest.getFlightDate(), seatInfoRequest.getDepAirportCode(), seatInfoRequest.getArrAirportCode());
                if (null != boardInfoResultDTO) {
                    seatInfoResult.setBoardingGateNumber(boardInfoResultDTO.getBoardingGateNumber());
                    seatInfoResult.setBordingTime(boardInfoResultDTO.getBoardingTime());
                }
            }
            // 对登机时间 登机口展示进行处理
            String boardingTime = seatInfoResult.getBordingTime();
            if (null != boardingTime && boardingTime.length() > 0 && !boardingTime.contains(":")) {
                boardingTime = boardingTime.substring(0, 2) + ":" + boardingTime.substring(2);
            }
            seatInfoResult.setBordingTime(boardingTime);
            String boardingGateNumber = null == seatInfoResult.getBoardingGateNumber() || seatInfoResult.getBoardingGateNumber().contains("?") ? "待定" : seatInfoResult.getBoardingGateNumber();
            seatInfoResult.setBoardingGateNumber(boardingGateNumber);
            if (!CollectionUtils.isEmpty(selectSeatInfoResult.getFailedSeatResultList())) {
                seatInfoResult.setFailedSeatResultList(selectSeatInfoResult.getFailedSeatResultList());
            }
        } else {
            seatInfoResult.setSelectSeatReservePsgInfos(selectSeatInfoResult.getSelectSeatReservePsgInfos());
        }
        seatInfoResult.setChannelOrderNo(seatInfoRequest.getChannelOrderNo());
        // 获取提示信息
        TravellerTripTipResult travellerTripTipResult = CussObjectConvert.getTravellerTripTip(seatInfoRequest.getDepAirportCode(), seatInfoRequest.getArrAirportCode(), Sets.newHashSet("SELECT"));
        if (null != travellerTripTipResult) {
            seatInfoResult.setTip(travellerTripTipResult.getTip());
            seatInfoResult.setTourTip(travellerTripTipResult.getTourTip());
        }
        if (ENUM_FLIGHT_SEAT_STATUS.CHECK.equals(flightSeatStatusResult.getFlightSeatStatus()) && !CollectionUtils.isEmpty(seatInfoResult.getFailedSeatResultList())) {
            List<CheckInFailed> failedSeatResultList = seatInfoResult.getFailedSeatResultList();
            ArrayList<String> nameSt = new ArrayList<>();
            for (CheckInFailed ci : failedSeatResultList
            ) {
                String psgrName = ci.getPsgrName();
                if (StringUtils.isNotEmpty(psgrName)) {
                    nameSt.add(psgrName);
                }
            }
            if (!CollectionUtils.isEmpty(nameSt)) {
                String nameString = StringUtils.join(nameSt, "、");
                String nameResult = String.format(WSEnum.CHECK_IN_FAILED.getResultInfo(), nameString);
                ReturnJson<SeatInfoResult> returnJson = new ReturnJson<>();
                returnJson.setResultCode(WSEnum.CHECK_IN_FAILED.getResultCode());
                returnJson.setErrorInfo(nameResult);
                returnJson.setResult(seatInfoResult);
                return returnJson;
            }

        }
        seatInfoResult.setSeatShareImgUrl(handConfig.getSeatShareImgUrl());
        return ReturnJson.success(seatInfoResult);
    }
}

package com.juneyaoair.mobile.handler.service.impl;

import com.juneyaoair.baseclass.captcha.CaptchaInterval;
import com.juneyaoair.baseclass.captcha.CaptchaSendIntervalConfig;
import com.juneyaoair.mobile.comm.service.IRedisService;
import com.juneyaoair.mobile.constants.MobileCoreBeanNames;
import com.juneyaoair.mobile.handler.comm.RedisKeyConfig;
import com.juneyaoair.mobile.handler.config.bean.HandConfig;
import com.juneyaoair.mobile.handler.service.ICaptchaConfigService;
import com.juneyaoair.utils.json.JsonUtil;
import com.juneyaoair.utils.util.DateUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Calendar;
import java.util.Comparator;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @create 2020-11-02 10:30
 */
@Service
public class CaptchaConfigServiceImpl implements ICaptchaConfigService {

    @Resource(name = MobileCoreBeanNames.API_REDIS_SERVICE)
    private IRedisService apiRedisService;

    @Autowired
    private HandConfig handConfig;

    /**
     * 获取下次可发送验证码时间
     * @param countryCode
     * @param mobile
     * @param type
     * @return 剩余等待时间单位秒 ， -1表示今日不可再发送
     */
    @Override
    public long mobileVisitInterval(String countryCode, String mobile, String type) {
        Date now = new Date();
        String date = DateUtils.dateToString(now, DateUtils.YYYYMMDD_PATTERN);
        String key = RedisKeyConfig.CAPTCHA_INTERVAL + type + "_" + mobile + "_" + date;
        String value = apiRedisService.getData(key);
        CaptchaInterval captchaInterval;
        if (StringUtils.isBlank(value)) {
            captchaInterval = new CaptchaInterval();
            captchaInterval.setCountryCode(countryCode);
            captchaInterval.setMobileNum(mobile);
            captchaInterval.setType(type);
            captchaInterval.setVisitTimes(0);
        } else {
            captchaInterval = JsonUtil.fromJson(value, CaptchaInterval.class);
        }
        captchaInterval.setVisitTimes(captchaInterval.getVisitTimes() + 1);
        captchaInterval.setLastVisitDate(now);
        int interval = findCurrentVisitInterval(captchaInterval.getSendCaptchaTimes());
        Calendar calendar = Calendar.getInstance();
        if (captchaInterval.getLastSendDate() != null) {
            calendar.setTime(captchaInterval.getLastSendDate());
        } else {
            // 从未发送过验证码的上次验证码发送时间初始化
            calendar.set(Calendar.YEAR, 2000);
        }
        // 大于0时增加间隔时间，小于0时今天不可发送
        if (interval >= 0) {
            calendar.add(Calendar.MINUTE, interval);
        } else {
            calendar.set(Calendar.HOUR, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            calendar.add(Calendar.DATE, 1);
        }
        captchaInterval.setCurrentInterval(interval);
        Calendar nextDay = Calendar.getInstance();
        nextDay.set(Calendar.HOUR, 0);
        nextDay.set(Calendar.MINUTE, 0);
        nextDay.set(Calendar.SECOND, 0);
        nextDay.add(Calendar.DATE, 1);
        // 下次可发送时间在当前时间之前，可以直接发送验证码
        if (calendar.getTime().before(now)) {
            captchaInterval.setSendCaptchaTimes(captchaInterval.getSendCaptchaTimes() + 1);
            captchaInterval.setLastSendDate(now);
            // 保存至次日
            apiRedisService.putData(key, JsonUtil.objectToJson(captchaInterval), (nextDay.getTimeInMillis() - now.getTime())/1000);
            return 0;
        } else {
            // 保存至次日
            apiRedisService.putData(key, JsonUtil.objectToJson(captchaInterval), (nextDay.getTimeInMillis() - now.getTime())/1000);
            return interval >= 0 ? (calendar.getTimeInMillis() - now.getTime()) / 1000 : -1;
        }
    }

    /**
     * 获取间隔时间配置
     * @param sendTimes
     * @return 间隔时间，单位分钟，-1表示今日不可继续发送
     */
    private int findCurrentVisitInterval (int sendTimes) {
        int interval = 1;
        List<CaptchaSendIntervalConfig> captchaSendIntervalConfigs = handConfig.getCaptchaSendIntervalConfigs();
        if (CollectionUtils.isNotEmpty(captchaSendIntervalConfigs)) {
            captchaSendIntervalConfigs.sort(Comparator.comparing(CaptchaSendIntervalConfig::getTimes));
            for (CaptchaSendIntervalConfig captchaSendIntervalConfig : captchaSendIntervalConfigs) {
                if (captchaSendIntervalConfig.getTimes() <= sendTimes) {
                    interval = captchaSendIntervalConfig.getInterval();
                } else {
                    break;
                }
            }
        }
        return interval;

    }

}

package com.juneyaoair.mobile.interceptor;

import com.juneyaoair.appenum.WSEnum;
import com.juneyaoair.baseclass.common.response.BaseResp;
import com.juneyaoair.mobile.external.request.sso.ParseTokenResult;
import com.juneyaoair.mobile.external.service.AuthService;
import com.juneyaoair.mobile.handler.HandlerConstants;
import com.juneyaoair.mobile.interceptor.bean.MobileVerifyTokenResp;
import com.juneyaoair.mobile.interceptor.utils.TokenUtils;
import com.juneyaoair.utils.IPUtil;
import com.juneyaoair.utils.json.JsonUtil;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.PrintWriter;

/**
 * @Author:zc
 * @Description:token有效性验证
 * @Date:Created in 10:31 2019/3/13
 * @Modified by:
 */
public class TokenInterceptor implements HandlerInterceptor {
    private Logger log = LoggerFactory.getLogger(this.getClass());

    private static final String CLIENT_VERSION = "clientVersion";
    private static final String CHANNEL_CODE = "channelCode";

    @Setter
    private AuthService authService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String channelCode = request.getHeader(CHANNEL_CODE);
        // UrlInterceptor已经排除了不存在渠道的接口 不存在渠道不需要校验token 登录校验交由代码中自行校验loginKeyInfo
        if (StringUtils.isBlank(channelCode)) {
            return true;
        }
        String token = request.getHeader("token");
        if (StringUtils.isBlank(token) || HandlerConstants.DEFAULT_TOKEN.equals(token)){
            BaseResp resp = setResult(WSEnum.INVALID_TOKEN.getResultCode(), WSEnum.INVALID_TOKEN.getResultInfo());
            sendResp(response, resp);
            log.info("【TokenInterceptor】移动端访问拦截，token为空。请求地址：{} 响应内容：{}",request.getRequestURI(), JsonUtil.objectToJson(resp));
            return false;
        }
        String clientVersion = request.getHeader(CLIENT_VERSION);
        String uri = request.getRequestURI();
        String ip = IPUtil.getIpAddr(request);
        /* 对token进行验证
         *  2025-06-30 TOKEN改造 非使用SSO-TOKEN保持原逻辑
         */
        if (!TokenUtils.getSsoTokenFlag(channelCode, clientVersion)){
            //请求request处理
            try {
                log.debug("【MobileTokenInterceptor拦截器，移动端登录】token内容:{}，uri：{}，ip{}",token,uri,ip);
                // 校验账号是否关闭
                if (!TokenUtils.verifyIsClosedByRequest(request)) {
                    BaseResp resp = setResult(WSEnum.INVALID_TOKEN.getResultCode(), "该账号已关闭");
                    sendResp(response, resp);
                    return false;
                }
                //根据token进行校验
                MobileVerifyTokenResp mobileVerifyTokenResp = TokenUtils.mobileVerifyTokenAndRenewal(token,request);
                if ("success".equals(mobileVerifyTokenResp.getCode())){
                    return true;
                }
                log.info("【MobileTokenInterceptor拦截器，移动端登录失败】，uri：{}，ip{}，错误状态码{}，错误信息{}，token内容:{}"
                        ,uri,ip,mobileVerifyTokenResp.getResCode(),mobileVerifyTokenResp.getMessage(),token);
                BaseResp resp = setResult(mobileVerifyTokenResp.getResCode(), mobileVerifyTokenResp.getMessage());
                sendResp(response, resp);
                return false;
            }catch (Exception e){
                log.error("【MobileTokenInterceptor拦截器，移动端登录错误】", e);
                BaseResp resp = setResult(WSEnum.ERROR.getResultCode(), "服务器异常！");
                sendResp(response, resp);
                return false;
            }
        } else {
            // 解析token 识别token使用是登录状态
            ParseTokenResult parseTokenResult = authService.parseToken(channelCode, token);
            if (!parseTokenResult.isLogin()) {
                BaseResp resp = setResult(WSEnum.INVALID_TOKEN.getResultCode(), "token失效，重新登录！");
                sendResp(response, resp);
                return false;
            }
            // 校验是否封禁
            if (!parseTokenResult.isAccountStatus()) {
                BaseResp resp = setResult(WSEnum.INVALID_TOKEN.getResultCode(), "该账号已封禁！");
                sendResp(response, resp);
                return false;
            }
            return true;
        }
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) {

    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {

    }

    /**
     * 输出结果
     *
     * @param msg
     * @return
     */
    private BaseResp setResult(String code, String msg) {
        BaseResp resp = new BaseResp();
        resp.setResultCode(code);
        resp.setResultInfo(msg);
        resp.setLoginFlag(true);
        return resp;
    }

    private void sendResp(HttpServletResponse response, BaseResp obj) throws Exception {
        response.setContentType("application/json; charset=utf-8");
        PrintWriter writer = response.getWriter();
        writer.print(JsonUtil.objectToJson(obj));
        writer.close();
        response.flushBuffer();
    }

}

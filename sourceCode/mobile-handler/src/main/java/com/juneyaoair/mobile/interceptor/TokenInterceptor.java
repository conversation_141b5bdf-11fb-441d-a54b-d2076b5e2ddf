package com.juneyaoair.mobile.interceptor;

import com.juneyaoair.appenum.ChannelCodeEnum;
import com.juneyaoair.appenum.WSEnum;
import com.juneyaoair.baseclass.common.response.BaseResp;
import com.juneyaoair.mobile.interceptor.bean.VerifyTokenResp;
import com.juneyaoair.mobile.interceptor.bean.VerifyTokenResult;
import com.juneyaoair.mobile.interceptor.utils.TokenUtils;
import com.juneyaoair.utils.json.JsonUtil;
import com.juneyaoair.utils.util.VersionNoUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.PrintWriter;

/**
 * @Author:zc
 * @Description:token有效性验证
 * @Date:Created in 10:31 2019/3/13
 * @Modified by:
 */
public class TokenInterceptor implements HandlerInterceptor {
    private Logger log = LoggerFactory.getLogger(this.getClass());


    private static final String CLIENT_VERSION = "clientVersion";

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String channelCode = request.getHeader("channelCode");
        String clientVersion = request.getHeader(CLIENT_VERSION);
        String requestURI = request.getRequestURI();
        if (ChannelCodeEnum.WEIXIN.getChannelCode().equalsIgnoreCase(channelCode)
                || ChannelCodeEnum.MWEB.getChannelCode().equalsIgnoreCase(channelCode)
                || ChannelCodeEnum.WXAPP.getChannelCode().equalsIgnoreCase(channelCode)
                || ChannelCodeEnum.MP_ALIPAY.getChannelCode().equalsIgnoreCase(channelCode)
                || ChannelCodeEnum.HW5G.getChannelCode().equalsIgnoreCase(channelCode)
                || ChannelCodeEnum.CHECKIN.getChannelCode().equalsIgnoreCase(channelCode)) {
            //M站，微信，小程序启用token登陆，非crm中token
            String accessToken = request.getHeader("token");
            VerifyTokenResp verifyTokenResp = TokenUtils.verifyTokenAndRenewal(channelCode,accessToken);
            if (verifyTokenResp.getCode().equals(VerifyTokenResult.FAIL_KEY)) {
                BaseResp resp = setResult(WSEnum.INVALID_TOKEN.getResultCode(), "token失效，重新登录！");
                sendResp(response, resp);
                return false;
            }
            // 校验token有效性
            try {
                TokenUtils.parserToken(accessToken);
            } catch (Exception e) {
                BaseResp resp = setResult(WSEnum.INVALID_TOKEN.getResultCode(), "token失效，重新登录！");
                sendResp(response, resp);
                return false;
            }
            response.setHeader("token", verifyTokenResp.getToken());
            response.setHeader("Access-Control-Expose-Headers","token");

        }
        //移动端token登录验证
        else if (ChannelCodeEnum.MOBILE.getChannelCode().equalsIgnoreCase(channelCode)){
            //判断版本号是否小于该版本号，入如果小于则直接跳过
            int verInt = VersionNoUtil.toVerInt(clientVersion);
            if (verInt< TokenUtils.MOBILE_TOKEN_VERSION){
//                log.info("【MobileTokenInterceptor】验证移动端token有效性，版本号：{} 小于指定版本：{}" , verInt, TokenUtils.MOBILE_TOKEN_VERSION);
                return true;
            }

            //没有token直接返回错误
            String token = request.getHeader("token");
            if (StringUtils.isBlank(token) || MobileTokenInterceptor.TOKEN_DEFAULT_CONTENT.equals(token)){
                BaseResp resp = setResult(WSEnum.MOBILE_NO_TOKEN.getResultCode(), WSEnum.MOBILE_NO_TOKEN.getResultInfo());
                sendResp(response, resp);
                log.info("【TokenInterceptor】移动端访问拦截，token为空。请求地址：{} 响应内容：{}",requestURI, JsonUtil.objectToJson(resp));
                return false;
            }
        }
        return true;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) throws Exception {

    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {

    }

    /**
     * 输出结果
     *
     * @param msg
     * @return
     */
    private BaseResp setResult(String code, String msg) {
        BaseResp resp = new BaseResp();
        resp.setResultCode(code);
        resp.setResultInfo(msg);
        resp.setLoginFlag(true);
        return resp;
    }

    private void sendResp(HttpServletResponse response, BaseResp obj) throws Exception {
        response.setContentType("application/json; charset=utf-8");
        PrintWriter writer = response.getWriter();
        writer.print(JsonUtil.objectToJson(obj));
        writer.close();
        response.flushBuffer();
    }
}

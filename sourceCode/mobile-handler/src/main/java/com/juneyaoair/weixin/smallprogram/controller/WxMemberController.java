package com.juneyaoair.weixin.smallprogram.controller;

import com.alibaba.fastjson.JSON;
import com.google.gson.reflect.TypeToken;
import com.juneyaoair.appenum.ChannelCodeEnum;
import com.juneyaoair.appenum.WSEnum;
import com.juneyaoair.appenum.member.*;
import com.juneyaoair.baseclass.common.base.UserInfoMust;
import com.juneyaoair.baseclass.common.request.BaseReq;
import com.juneyaoair.baseclass.common.response.BaseResp;
import com.juneyaoair.baseclass.common.response.BaseResultDTO;
import com.juneyaoair.baseclass.member.comm.ThirdAccountInfo;
import com.juneyaoair.baseclass.member.response.LoginResponse;
import com.juneyaoair.baseclass.response.crm.CustomerCertificateInfo;
import com.juneyaoair.baseclass.response.crm.MemberLoginResponse;
import com.juneyaoair.crm.IMemberService;
import com.juneyaoair.exception.CommonException;
import com.juneyaoair.exception.OperationFailedException;
import com.juneyaoair.exception.RequestParamErrorException;
import com.juneyaoair.mobile.aop.annotation.InterfaceLog;
import com.juneyaoair.mobile.aop.annotation.NotDuplicate;
import com.juneyaoair.mobile.comm.service.IRedisService;
import com.juneyaoair.mobile.constants.MobileCoreBeanNames;
import com.juneyaoair.mobile.handler.HandlerConstants;
import com.juneyaoair.mobile.handler.comm.RedisKeyConfig;
import com.juneyaoair.mobile.handler.config.bean.HandConfig;
import com.juneyaoair.mobile.handler.controller.BassController;
import com.juneyaoair.mobile.handler.manage.CommonManage;
import com.juneyaoair.thirdentity.comm.request.Header;
import com.juneyaoair.thirdentity.comm.request.PtApiCRMRequest;
import com.juneyaoair.thirdentity.comm.response.PtCRMResponse;
import com.juneyaoair.thirdentity.member.comm.CrmMemberBaseApiRequest;
import com.juneyaoair.thirdentity.member.request.BindWechatRelation;
import com.juneyaoair.thirdentity.member.request.PtMemberDetailRequest;
import com.juneyaoair.thirdentity.member.request.PtRegisterRequest;
import com.juneyaoair.thirdentity.member.response.*;
import com.juneyaoair.util.ControllerUtil;
import com.juneyaoair.utils.EncoderHandler;
import com.juneyaoair.utils.StringUtil;
import com.juneyaoair.utils.http.HttpResult;
import com.juneyaoair.utils.http.HttpUtil;
import com.juneyaoair.utils.json.JsonUtil;
import com.juneyaoair.utils.util.DateUtils;
import com.juneyaoair.utils.util.MdcUtils;
import com.juneyaoair.utils.util.PhoneUtil;
import com.juneyaoair.weixin.bean.user.QueryWxUserRecordRequest;
import com.juneyaoair.weixin.bean.user.WxBindUserInfo;
import com.juneyaoair.weixin.message.request.TemplateAuthorizeRequest;
import com.juneyaoair.weixin.smallprogram.common.*;
import com.juneyaoair.weixin.smallprogram.controller.util.LoginUtil;
import com.juneyaoair.weixin.smallprogram.controller.util.WXBizDataCrypt;
import com.juneyaoair.weixin.user.UserLoginToken;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpHost;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Type;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2019/2/13  10:20.
 */
@RequestMapping("/wechat/miniprogram/member")
@RestController
@Api(value = "WxMemberController", tags = "微信小程序会员服务")
public class WxMemberController extends BassController {
    @Autowired
    private IMemberService memberService;
    @Autowired
    private HandConfig handConfig;
    @Resource(name = MobileCoreBeanNames.API_REDIS_SERVICE)
    private IRedisService apiRedisService;
    @Autowired
    private CommonManage commonManage;

    private static final String OPENID = "openid";

    private static final String LOGOUT = "logout";
    private static final String MINI_REDIS_TABLE = RedisKeyConfig.MINI_REDIS_TABLE;
    private static final String SESSION_KEY = "sessionKey";
    private static final String TIMESTAMP = "timestamp";
    private static final int WX_READ_TIME_OUT = 5000;
    private static final int WX_CONNECT_TIME_OUT = 5000;


    /**
     * 通过小程序授权的方式获取手机号绑定吉祥会员
     * 若手机号未注册则注册吉祥会员并与小程序用户绑定
     * @param req
     * @param request
     * @return
     */
    @InterfaceLog
    @NotDuplicate
    @ApiOperation(value = "小程序手机号登录", notes = "小程序手机号登录")
    @RequestMapping(value = "/bindingByPhoneNo", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp<LoginResponse> bindingByPhoneNo(@RequestBody BaseReq<EncryptedUserInfoWithSid> req, HttpServletRequest request) {
        String reqId = MdcUtils.getRequestId();
        String clientIp = this.getClientIP(request);
        String headChannelCode = request.getHeader(HEAD_CHANNEL_CODE);
        BaseResp<LoginResponse> resp = new BaseResp<>();
        try {
            this.checkRequest(req);
            //微信数据解密获取手机号
            UserLoginToken userLoginToken = LoginUtil.getInfoFromHeader(request);
            if (userLoginToken == null) {
                throw new OperationFailedException(WSEnum.INVALID_TOKEN.getResultInfo());
            }
            log.info("请求号:{},渠道:{},头部信息:{}",reqId,headChannelCode,JsonUtil.objectToJson(userLoginToken));
            Map map = apiRedisService.getMapData(MINI_REDIS_TABLE+":"+headChannelCode+":"+userLoginToken.getUserToken());
            if (map == null || map.isEmpty()) {
                resp.setResultCode(WSEnum.INVALID_TOKEN.getResultCode());
                resp.setResultInfo(WSEnum.INVALID_TOKEN.getResultInfo()+"(1)");
                return resp;
            }
            String sessionKey = (String) map.get(SESSION_KEY);
            if(StringUtils.isBlank(sessionKey)){
                resp.setResultCode(WSEnum.INVALID_TOKEN.getResultCode());
                resp.setResultInfo(WSEnum.INVALID_TOKEN.getResultInfo()+"(2)");
                return resp;
            }
            String appId;
            if (ChannelCodeEnum.CHECKIN.getChannelCode().equals(headChannelCode)) {
                appId = handConfig.getCiSmallAppId();
            } else {
                appId = handConfig.getWxSmallAppId();
            }
            //解密对应的开放加密数据
            WXBizDataCrypt wxBizDataCrypt = new WXBizDataCrypt(appId,sessionKey);
            JSONObject decryptStr = wxBizDataCrypt.decrypt(req.getRequest().getEncryptedData(),req.getRequest().getIv());
            WxPhoneInfo wxPhoneInfo = (WxPhoneInfo)JsonUtil.jsonToBean(decryptStr.toString(),WxPhoneInfo.class);
            saveReqInfo("微信解密数据",reqId,clientIp,decryptStr.toString());
            String phoneNum = "";
            if(StringUtils.isBlank(wxPhoneInfo.getCountryCode()) || "86".equals(wxPhoneInfo.getCountryCode())){
                phoneNum = wxPhoneInfo.getPhoneNumber();
            }else{
                phoneNum = PhoneUtil.formatMobile(wxPhoneInfo.getCountryCode(), wxPhoneInfo.getPurePhoneNumber()).replace("+", "");
            }
            //根据手机号查询用户信息，查询不到用户信息的将注册手机号
            String[] items = {MemberDetailRequestItemsEnum.BASICINFO.eName, MemberDetailRequestItemsEnum.STATEINFO.eName,
                    MemberDetailRequestItemsEnum.CERTIFICATEINFO.eName, MemberDetailRequestItemsEnum.CONTACTINFO.eName,
                    MemberDetailRequestItemsEnum.ADDRESSINFOS.eName,MemberDetailRequestItemsEnum.REALVERIFYINFOS.eName};
            //渠道暂时固定为MOBILE渠道
            PtApiCRMRequest<PtMemberDetailRequest> ptApiRequest = buildMemberDetailReq(phoneNum, "-1", request, req.getChannelCode(), items);
            PtCRMResponse<PtMemberDetail> ptCRMResponse = memberService.memberDetail(ptApiRequest);
            LoginResponse loginResponse = null;
            if (ptCRMResponse.getCode() == 0) {
                //此处解密数据是手机信息数据
                loginResponse = bindingUserInfo(ptCRMResponse, req.getRequest(), userLoginToken, req.getChannelCode(),
                        map, wxBizDataCrypt, headChannelCode);
            } else if (ptCRMResponse.getCode() == 100002) {
                loginResponse = registerAndBinding(request, req.getChannelCode(), req.getRequest().getSid(), phoneNum,
                        ptApiRequest, req.getRequest(), userLoginToken, map, wxBizDataCrypt, headChannelCode);
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo(ptCRMResponse.getMsg());
            }
            resp.setObjData(loginResponse);
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
        } catch (Exception e) {
            log.info("请求号:{},错误信息:",reqId,e);
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR.getResultInfo());
        }
        return resp;
    }

    /**
     * 根据userToken获取小程序用户信息并与req中的吉祥会员绑定
     * 目前小程序账号密码绑定，验证码绑定操作走此接口 2019-10-29
     * @param request
     * @param req
     * @return
     */
    @InterfaceLog
    @RequestMapping(value = "bindLoginUser", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @ApiOperation(value = "直接绑定已登录会员", notes = "直接绑定已登录会员")
    public BaseResp bindLoginUser(HttpServletRequest request, @RequestBody BaseReq<EncryptedUserInfoWithSid> req) {
        BaseResp<LoginResponse> resp = new BaseResp<>();
        String ip = this.getClientIP(request);
        String reqId = MdcUtils.getRequestId();
        try {
            //校验参数
            this.checkRequest(req);
            //微信数据解密获取手机号
            UserLoginToken userLoginToken = LoginUtil.getInfoFromHeader(request);
            if (userLoginToken == null) {
                throw new RequestParamErrorException(WSEnum.INVALID_TOKEN.getResultInfo());
            }
            String headChannelCode = request.getHeader(HEAD_CHANNEL_CODE);
            String appId;
            if (ChannelCodeEnum.CHECKIN.getChannelCode().equals(headChannelCode)) {
                appId = handConfig.getCiSmallAppId();
            } else {
                appId = handConfig.getWxSmallAppId();
            }
            Map map = apiRedisService.getMapData(MINI_REDIS_TABLE + ":" + headChannelCode + ":" + userLoginToken.getUserToken());
            if (map == null || map.isEmpty()) {
                resp.setResultCode(WSEnum.INVALID_TOKEN.getResultCode());
                resp.setResultInfo(WSEnum.INVALID_TOKEN.getResultInfo());
                return resp;
            }
            String sessionKey = (String) map.get(SESSION_KEY);
            //解密对应的开放加密数据
            WXBizDataCrypt wxBizDataCrypt = new WXBizDataCrypt(appId, sessionKey);
            //根据手机号查询用户信息，查询不到用户信息的将注册手机号
            String[] items = {MemberDetailRequestItemsEnum.BASICINFO.eName, MemberDetailRequestItemsEnum.STATEINFO.eName,MemberDetailRequestItemsEnum.REALVERIFYINFOS.eName,
                    MemberDetailRequestItemsEnum.CERTIFICATEINFO.eName, MemberDetailRequestItemsEnum.CONTACTINFO.eName,MemberDetailRequestItemsEnum.ADDRESSINFOS.eName};
            //渠道暂时固定为MOBILE渠道
            PtApiCRMRequest<PtMemberDetailRequest> ptApiRequest = buildMemberDetailReq(null, req.getRequest().getFfpId(), request, req.getChannelCode(), items);
            PtCRMResponse<PtMemberDetail> ptCRMResponse = memberService.memberDetail(ptApiRequest);
            LoginResponse loginResponse = null;
            if (ptCRMResponse.getCode() == 0) {
                loginResponse = bindingUserInfo(ptCRMResponse, req.getRequest(), userLoginToken, headChannelCode, map, wxBizDataCrypt,
                       headChannelCode);
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo(ptCRMResponse.getMsg());
            }
            resp.setObjData(loginResponse);
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
        } catch (Exception e) {
            this.logError(resp, reqId, ip, req, e);
        }
        return resp;
    }

    /**
     * 解除微信用户与小程序的绑定关系
     * 2019-10-29
     * @param request
     * @param req
     * @return
     */
    @RequestMapping(value = "unbindLoginUser", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @ApiOperation(value = "解绑已登录会员", notes = "解绑已登录会员")
    @InterfaceLog
    public BaseResp unbindLoginUser(@RequestBody BaseReq<UserInfoMust> req, HttpServletRequest request) {
        BaseResp<String> resp = new BaseResp<>();
        String ip = this.getClientIP(request);
        String reqId = StringUtil.newGUID() + request.getRequestURI();
        try {
            //校验参数
            this.checkRequest(req);
            UserLoginToken userLoginToken = LoginUtil.getInfoFromHeader(request);
            if (userLoginToken == null) {
                throw new OperationFailedException(WSEnum.INVALID_TOKEN.getResultInfo());
            }
            String headChannelCode = request.getHeader(HEAD_CHANNEL_CODE);
            Map map = apiRedisService.getMapData(MINI_REDIS_TABLE + ":" + headChannelCode + ":" + userLoginToken.getUserToken());
            if (map == null || map.isEmpty()) {
                resp.setResultCode(WSEnum.INVALID_TOKEN.getResultCode());
                resp.setResultInfo(WSEnum.INVALID_TOKEN.getResultInfo());
                return resp;
            }
            WxUserInfo wxUserInfo = new WxUserInfo();
            wxUserInfo.setOpenId((String) map.get(OPENID));
            apiRedisService.removeData(MINI_REDIS_TABLE + ":" + req.getChannelCode() + ":" + userLoginToken.getUserToken());
            resp.setObjData("解绑成功");
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
        } catch (Exception e) {
            this.logError(resp, reqId, ip, req, e);
        }
        return resp;
    }

    @ApiOperation(value = "获取小程序用户信息", notes = "获取小程序用户信息")
    @RequestMapping(value = "/wxAuth", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @InterfaceLog
    public BaseResp wxminiAuth(@RequestBody BaseReq<EncryptedUserInfoWithSid> req, HttpServletRequest request) {
        BaseResp<WxUserInfo> resp = new BaseResp<>();
        String reqId = StringUtil.newGUID() + "_WeChat_MiniProgram_Member_WxAuth";
        String ip = this.getClientIP(request);
        try {
            this.checkRequest(req);
            //从请求头中获取对用的登录信息
            String headChannelCode = request.getHeader(HEAD_CHANNEL_CODE);
            String appId;
            if (ChannelCodeEnum.CHECKIN.getChannelCode().equals(headChannelCode)) {
                appId = handConfig.getCiSmallAppId();
            } else {
                appId = handConfig.getWxSmallAppId();
            }
            UserLoginToken userLoginToken = LoginUtil.getInfoFromHeader(request);
            if (userLoginToken == null) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo(WSEnum.INVALID_TOKEN.getResultInfo());
                return resp;
            }
            Map map = apiRedisService.getMapData(MINI_REDIS_TABLE + ":" + headChannelCode + ":" + userLoginToken.getUserToken());
            if (map == null || map.isEmpty()) {
                resp.setResultCode(WSEnum.INVALID_TOKEN.getResultCode());
                resp.setResultInfo(WSEnum.INVALID_TOKEN.getResultInfo());
                return resp;
            }
            //解密微信用户信息
            WXBizDataCrypt wxBizDataCrypt = new WXBizDataCrypt(appId, (String) map.get(SESSION_KEY));
            EncryptedUserInfoWithSid encryptedUserInfo = req.getRequest();
            JSONObject decryptStr = wxBizDataCrypt.decrypt(encryptedUserInfo.getEncryptedData(), req.getRequest().getIv());
            //完整微信用户信息
            WxUserInfo wxUserInfo = (WxUserInfo) JsonUtil.jsonToBean(decryptStr.toString(), WxUserInfo.class);
            resp.setObjData(wxUserInfo);
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            resp.setResultCode(WSEnum.SUCCESS.getResultInfo());
        } catch (Exception e) {
            this.logError(resp, reqId, ip, req, e);
        }
        return resp;
    }

    /**
     * 开启小程序会话，获取微信用户openid、unionid、sessionKey
     * @param req
     * @param request
     * @return
     */
    @InterfaceLog
    @ApiOperation(value = "小程序登录态", notes = "小程序登录")
    @RequestMapping(value = "/wxminiLogin", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp wxminiLogin(@RequestBody BaseReq<String> req, HttpServletRequest request, HttpServletResponse response){
        BaseResp<String> resp = new BaseResp<>();
        String reqId = StringUtil.newGUID() + "_WeChat_MiniProgram_Member_WxminiLogin";
        String ip = this.getClientIP(request);
        try {
            this.checkRequest(req);
            //微信code换取session_key
            String headChannelCode = request.getHeader(HEAD_CHANNEL_CODE);
            String appid;
            String appSecret;
            if (ChannelCodeEnum.CHECKIN.getChannelCode().equals(headChannelCode)) {
                appid = handConfig.getCiSmallAppId();
                appSecret = handConfig.getCiSmallSecret();
            } else {
                appid = handConfig.getWxSmallAppId();
                appSecret = handConfig.getWxSmallSecret();
            }
            String wxCode2SessionUrl = new StringBuilder(HandlerConstants.WEIXIN_API_URL+HandlerConstants.JSCODE2SESSION)
                    .append("?appid=").append(appid)
                    .append("&secret=").append(appSecret)
                    .append("&js_code=").append(req.getRequest())
                    .append("&grant_type=authorization_code")
                    .toString();
            long time1 = System.currentTimeMillis();
            HttpHost httpHost = commonManage.setProxy(HandlerConstants.WEIXIN_API_URL);
            HttpResult httpResult = HttpUtil.doGetClient(wxCode2SessionUrl,null,WX_READ_TIME_OUT,WX_CONNECT_TIME_OUT,httpHost);
            if(!httpResult.isResult()){
                throw new OperationFailedException("网络繁忙，请稍后再试");
            }
            String result = httpResult.getResponse();
            long time2 = System.currentTimeMillis();
            log.info("微信小程序请求，url={}，耗时{}ms，响应参数={}，", wxCode2SessionUrl, time2 - time1, result);
            if (StringUtil.isNullOrEmpty(result)) {
                throw new OperationFailedException("登录失败");
            }
            CodeToSession codeToSession = (CodeToSession) JsonUtil.jsonToBean(result, CodeToSession.class);
            if (codeToSession.getErrcode() != 0) {
                throw new OperationFailedException(codeToSession.getErrmsg());
            }
            //自定义登录状态
            String key = ChannelCodeEnum.WEIXIN.getChannelCode().equals(headChannelCode) ? HandlerConstants.USER_INFO_KEY : HandlerConstants.USER_INFO_KEY_M;
            long timeStamp = System.currentTimeMillis();
            String loginToken = EncoderHandler.encodeByMD5(codeToSession.getOpenid() + timeStamp + key).toLowerCase();
            WxUserInfo wxUserInfo = new WxUserInfo();
            wxUserInfo.setOpenId(codeToSession.getOpenid());
            wxUserInfo.setUnionId(codeToSession.getUnionid());
            wxUserInfo.setSession_key(codeToSession.getSession_key());
            apiRedisService.setMapData(MINI_REDIS_TABLE, headChannelCode, loginToken, userMap("", "", "" + timeStamp, wxUserInfo, null), DateUtils.addOrLessDay(new Date(), 30));
            resp.setObjData(loginToken);
            resp.setTimeStamp(timeStamp);
            resp.setSign(EncoderHandler.encodeByMD5(loginToken + timeStamp).toUpperCase());
            resp.setResultCode(WSEnum.SUCCESS.getResultCode());
            resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
            response.setHeader(HandlerConstants.CLIENT_HEADER_OPENID,codeToSession.getOpenid());
        } catch (Exception e) {
            this.logError(resp, reqId, ip, req, e);
        }
        return resp;
    }

    /**
     * 根据code开启小程序会话获取sessionKey
     * 若已绑定吉祥会员则自动登录并返回登陆信息
     * @param req
     * @param request
     * @return
     */
    @InterfaceLog
    @ApiOperation(value = "自动登录", notes = "小程序自动登陆")
    @RequestMapping(value = "/autoLogin", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp autoLogin(@RequestBody BaseReq<String> req, HttpServletRequest request,HttpServletResponse response){
        BaseResp<LoginResponse> resp = new BaseResp<>();
        String reqId = StringUtil.newGUID() + "_WeChat_MiniProgram_Member_WxminiLogin";
        String ip = this.getClientIP(request);
        String headChannelCode = request.getHeader(HEAD_CHANNEL_CODE);
        String channelCode = req.getChannelCode();
        try {
            this.checkRequest(req);
            //微信code换取session_key
            String appid;
            String appSecret;
            if (ChannelCodeEnum.CHECKIN.getChannelCode().equals(headChannelCode)) {
                appid = handConfig.getCiSmallAppId();
                appSecret = handConfig.getCiSmallSecret();
            } else {
                appid = handConfig.getWxSmallAppId();
                appSecret = handConfig.getWxSmallSecret();
            }
            String wxCode2SessionUrl = new StringBuilder(HandlerConstants.WEIXIN_API_URL+HandlerConstants.JSCODE2SESSION)
                    .append("?appid=").append(appid)
                    .append("&secret=").append(appSecret)
                    .append("&js_code=").append(req.getRequest())
                    .append("&grant_type=authorization_code")
                    .toString();
            long time1 = System.currentTimeMillis();
            HttpResult httpResult = HttpUtil.doGetClient(wxCode2SessionUrl,null,WX_READ_TIME_OUT,WX_CONNECT_TIME_OUT,null);
            if(!httpResult.isResult()){
                throw new OperationFailedException("网络繁忙，请稍后再试");
            }
            long time2 = System.currentTimeMillis();
            String result = httpResult.getResponse();
            log.info("微信小程序请求，url={}，耗时{}ms，响应参数={}，", wxCode2SessionUrl, time2 - time1, result);
            if (StringUtil.isNullOrEmpty(result)) {
                throw new OperationFailedException("登录失败");
            }
            CodeToSession codeToSession = (CodeToSession) JsonUtil.jsonToBean(result, CodeToSession.class);
            if (codeToSession.getErrcode() != 0) {
                throw new OperationFailedException(codeToSession.getErrmsg());
            }
            //自定义登录状态
            String key = HandlerConstants.W_CHANNEL_CODE.equals(headChannelCode) ? HandlerConstants.USER_INFO_KEY : HandlerConstants.USER_INFO_KEY_M;
            long timeStamp = System.currentTimeMillis();
            String loginToken = EncoderHandler.encodeByMD5(codeToSession.getOpenid() + timeStamp + key).toLowerCase();
            WxUserInfo wxUserInfo = new WxUserInfo();
            wxUserInfo.setOpenId(codeToSession.getOpenid());
            wxUserInfo.setUnionId(codeToSession.getUnionid());
            wxUserInfo.setSession_key(codeToSession.getSession_key());
            // 查找是否已绑定
            WxBindUserInfo wxBindUserInfo = findBoundUser(codeToSession.getOpenid());
            LoginResponse loginResponse = null;
            UserLoginToken userLoginToken = new UserLoginToken();
            userLoginToken.setSign(EncoderHandler.encodeByMD5(loginToken + timeStamp).toUpperCase());
            userLoginToken.setUserToken(loginToken);
            // 已绑定则直接登录
            if (null != wxBindUserInfo) {
                //根据手机号查询用户信息，查询不到用户信息的将注册手机号
                Map<String, Object> map = userMap(wxBindUserInfo.getFfpId(), wxBindUserInfo.getFfpCardNo(), "" + timeStamp, wxUserInfo, null);
                String[] items = {MemberDetailRequestItemsEnum.BASICINFO.eName, MemberDetailRequestItemsEnum.STATEINFO.eName,MemberDetailRequestItemsEnum.REALVERIFYINFOS.eName,
                        MemberDetailRequestItemsEnum.CERTIFICATEINFO.eName, MemberDetailRequestItemsEnum.CONTACTINFO.eName, MemberDetailRequestItemsEnum.ADDRESSINFOS.eName};
                //渠道暂时固定为MOBILE渠道
                PtApiCRMRequest<PtMemberDetailRequest> ptApiRequest = buildMemberDetailReq(null, wxBindUserInfo.getFfpId(), request, req.getChannelCode(), items);
                PtCRMResponse<PtMemberDetail> ptCRMResponse = memberService.memberDetail(ptApiRequest);

                if (ptCRMResponse.getCode() == 0) {

                    loginResponse = bindingUserInfo(ptCRMResponse, null, userLoginToken, channelCode, map,
                             null, headChannelCode);
                }
                apiRedisService.setMapData(MINI_REDIS_TABLE, headChannelCode, loginToken, map, DateUtils.addOrLessDay(new Date(), 30));
                resp.setObjData(loginResponse);
                resp.setTimeStamp(timeStamp);
                resp.setSign(userLoginToken.getSign());
                resp.setResultCode(WSEnum.SUCCESS.getResultCode());
                resp.setResultInfo(WSEnum.SUCCESS.getResultInfo());
                response.setHeader(HandlerConstants.CLIENT_HEADER_OPENID,codeToSession.getOpenid());
            } else {
                resp.setResultCode(WSEnum.INVALID_TOKEN.getResultCode());
                resp.setResultInfo("当前小程序未绑定吉祥账号，无法自动登陆");
            }
        } catch (Exception e) {
            this.logError(resp, reqId, ip, req, e);
        }

        return resp;
    }

    /**
     * 绑定微信用户与吉祥用户
     * @param ptCRMResponse 会员信息
     * @param encryptedUserInfo 微信账号登陆信息
     * @param userLoginToken 用户登陆信息
     * @param channelCode 渠道号
     * @param map 微信会员信息缓存
     * @param wxBizDataCrypt 微信加密信息
     * @param headChannelCode 用户实际访问渠道
     * @return
     */
    private LoginResponse bindingUserInfo(PtCRMResponse<PtMemberDetail> ptCRMResponse, EncryptedUserInfo encryptedUserInfo,
                                          UserLoginToken userLoginToken, String channelCode, Map map,WXBizDataCrypt wxBizDataCrypt,
                                          String headChannelCode) {
        LoginResponse loginInfo = new LoginResponse();
        MemberLoginResponse memberLoginResponse = formatLoginResp(ptCRMResponse.getData(), channelCode);
        loginInfo.setMemberLoginResponse(memberLoginResponse);
        loginInfo.setToken(EncoderHandler.encode("MD5", (ptCRMResponse.getData().getBasicInfo().getMemberId()
                + ptCRMResponse.getData().getBasicInfo().getCardNO()) + map.get(SESSION_KEY)).toUpperCase());
        //自定义登录状态
        String timeStamp = (String) map.get(TIMESTAMP);
        loginInfo.setTimeStamp(timeStamp);
        loginInfo.setUserToken(userLoginToken.getUserToken());
        loginInfo.setSign(userLoginToken.getSign());
        apiRedisService.setMapData(MINI_REDIS_TABLE, headChannelCode, userLoginToken.getUserToken(),
                userMap("" + ptCRMResponse.getData().getBasicInfo().getMemberId(), ptCRMResponse.getData().getBasicInfo().getCardNO(),
                        timeStamp, null, map), DateUtils.addOrLessDay(new Date(), 30));
        //解密微信用户信息  记录小程序与用户的关联关系
        WxUserInfo wxUserInfo = new WxUserInfo();
        ThirdAccountInfo thirdAccountInfo = new ThirdAccountInfo();
        thirdAccountInfo.setOpenId((String) map.get(OPENID));
        thirdAccountInfo.setUid((String) map.get("unionid"));
        thirdAccountInfo.setThirdPartyType("WXAPP");
        if (null != wxBizDataCrypt && null != encryptedUserInfo && StringUtils.isNotBlank(encryptedUserInfo.getEncryptedData())) {
            try {
                JSONObject decryptStr1 = wxBizDataCrypt.decrypt(encryptedUserInfo.getEncryptedData(), encryptedUserInfo.getIv());
                //完整微信用户信息
                wxUserInfo = (WxUserInfo) JsonUtil.jsonToBean(decryptStr1.toString(), WxUserInfo.class);
                log.info("{},微信解密结果:{}",MdcUtils.getRequestId(),decryptStr1);
                thirdAccountInfo.setNickName(wxUserInfo.getNickName());
                thirdAccountInfo.setHeadImageUrl(wxUserInfo.getAvatarUrl());
            } catch (Exception e) {
                log.error("{},解密微信小程序用户信息出现异常", MdcUtils.getRequestId(),e);
            }
        }
        wxUserInfo.setOpenId(thirdAccountInfo.getOpenId());
        wxUserInfo.setUnionId(thirdAccountInfo.getUid());
        loginInfo.setThirdPartInfo(thirdAccountInfo);
        // 私域--会员微信绑定
        try {
            BindWechatRelation bindWechatRelation = new BindWechatRelation();
            bindWechatRelation.setId(memberLoginResponse.getId());
            bindWechatRelation.setOpenId(thirdAccountInfo.getOpenId());
            bindWechatRelation.setUnionId(thirdAccountInfo.getUid());
            CrmMemberBaseApiRequest<BindWechatRelation> crmRequest = ControllerUtil.buildCommCrmMemberReq(headChannelCode, bindWechatRelation);
            memberService.bindWechatRelation(crmRequest);
        } catch (Exception e) {
            log.info("bindingUserInfo绑定异常，请求ID：{}", MdcUtils.getRequestId());
        }
        return loginInfo;
    }

    /**
     * 会员微信绑定
     * @param req
     * @param request
     * @return
     */
    @RequestMapping(value = "/bindWechatRelation", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @ApiOperation(value = "会员微信绑定", notes = "会员微信绑定")
    @InterfaceLog
    public BaseResp<Object> bindWechatRelation(@RequestBody BaseReq<EncryptedUserInfoWithSid> req, HttpServletRequest request) {
        this.checkRequest(req);
        String headChannelCode = request.getHeader(HEAD_CHANNEL_CODE);
        EncryptedUserInfoWithSid encryptedUserInfo = req.getRequest();
        // 私域--会员微信绑定
        WxUserInfo wxUserInfo = getWxUserInfo(request, encryptedUserInfo);
        BindWechatRelation bindWechatRelation = new BindWechatRelation();
        bindWechatRelation.setId(Long.valueOf(encryptedUserInfo.getFfpId()));
        bindWechatRelation.setOpenId(wxUserInfo.getOpenId());
        bindWechatRelation.setUnionId(wxUserInfo.getUnionId());
        CrmMemberBaseApiRequest<BindWechatRelation> crmRequest = ControllerUtil.buildCommCrmMemberReq(headChannelCode, bindWechatRelation);
        memberService.bindWechatRelation(crmRequest);
        return BaseResp.success(null);
    }

    /**
     * 解析微信授权数据
     * @param request
     * @param encryptedUserInfo
     * @return
     */
    private WxUserInfo getWxUserInfo(HttpServletRequest request, EncryptedUserInfoWithSid encryptedUserInfo) {
        String headChannelCode = request.getHeader(HEAD_CHANNEL_CODE);
        //从请求头中获取对用的登录信息
        String appId;
        if (ChannelCodeEnum.CHECKIN.getChannelCode().equals(headChannelCode)) {
            appId = handConfig.getCiSmallAppId();
        } else {
            appId = handConfig.getWxSmallAppId();
        }
        UserLoginToken userLoginToken = LoginUtil.getInfoFromHeader(request);
        if (userLoginToken == null) {
            throw new CommonException(WSEnum.ERROR.getResultCode(), WSEnum.INVALID_TOKEN.getResultInfo());
        }
        Map map = apiRedisService.getMapData(MINI_REDIS_TABLE + ":" + headChannelCode + ":" + userLoginToken.getUserToken());
        if (map == null || map.isEmpty()) {
            throw new CommonException(WSEnum.INVALID_TOKEN.getResultCode(), WSEnum.INVALID_TOKEN.getResultInfo());
        }
        try {
            //解密微信用户信息
            WXBizDataCrypt wxBizDataCrypt = new WXBizDataCrypt(appId, (String) map.get(SESSION_KEY));
            JSONObject decryptStr = wxBizDataCrypt.decrypt(encryptedUserInfo.getEncryptedData(), encryptedUserInfo.getIv());
            log.info("解密微信用户信息:{}", JSON.toJSONString(decryptStr));
            //完整微信用户信息
            WxUserInfo wxUserInfo = (WxUserInfo) JsonUtil.jsonToBean(decryptStr.toString(), WxUserInfo.class);
            return wxUserInfo;
        } catch (Exception e) {
            log.error("授权信息解析失败,渠道：{} 缓存数据：{} 请求参数：{} 异常原因：", headChannelCode, JSON.toJSONString(map), JSON.toJSONString(encryptedUserInfo), e);
            throw new CommonException(WSEnum.ERROR.getResultCode(), "授权信息解析失败");
        }
    }

    private LoginResponse registerAndBinding(HttpServletRequest request, String channelCode, String sid, String mobile,
                                             PtApiCRMRequest<PtMemberDetailRequest> ptApiRequest, EncryptedUserInfo encryptedUserInfo,
                                             UserLoginToken userLoginToken,Map map, WXBizDataCrypt wxBizDataCrypt, String headChannelCode){
        PtApiCRMRequest<PtRegisterRequest> ptApiCRMRequest = new PtApiCRMRequest();
        Header header = buildHeader(request, "-1", "");
        ptApiCRMRequest.setHeader(header);
        ptApiCRMRequest.setChannel(channelCode);
        ptApiCRMRequest.setChannelPwd(getClientPwd(channelCode));
        PtRegisterRequest target = new PtRegisterRequest();
        target.setMobile(mobile);
        target.setReferrer(sid);
        target.setSubmitDate(System.currentTimeMillis());
        target.setValidateMode(ValidateModeEnum.NO_VALID.validMethod);
        //性别未知
        target.setSex("U");
        ptApiCRMRequest.setData(target);
        PtCRMResponse<PtRegisterResponse> ptCRMResponse = memberService.register(ptApiCRMRequest);
        String ffpCardNo;
        if (ptCRMResponse.isIsSuccess()) {
            if (ptCRMResponse.getCode() == 0) {
                //注册成功
                ffpCardNo = ptCRMResponse.getData().getMemberCardNo();
            } else {
                throw new OperationFailedException("快速注册失败");
            }
        } else {
            throw new OperationFailedException("快速注册失败");
        }
        ptApiRequest.getData().setCardNO(ffpCardNo);
        PtCRMResponse<PtMemberDetail> detailPtCRMResponse = memberService.memberDetail(ptApiRequest);
        if (detailPtCRMResponse.getCode() != 0) {
            throw new OperationFailedException("快速注册失败");
        }
        return bindingUserInfo(detailPtCRMResponse, encryptedUserInfo, userLoginToken, channelCode, map, wxBizDataCrypt, headChannelCode);
    }

    /**
     * 会员详情请求类
     * 基本信息 状态信息 证件信息  联系信息 地址信息
     *
     * @param mobile
     * @param ffpId
     * @param request
     * @param channelCode
     * @param items       需要的请求信息
     * @return
     */
    private PtApiCRMRequest<PtMemberDetailRequest> buildMemberDetailReq(String mobile, String ffpId, HttpServletRequest request, String channelCode, String[] items) {
        PtMemberDetailRequest ptMemberDetailRequest = new PtMemberDetailRequest();
        ptMemberDetailRequest.setMobile(mobile);
        ptMemberDetailRequest.setRequestItems(items);
        Header header = buildHeader(request, ffpId, "");

        PtApiCRMRequest ptApiCRMRequest = new PtApiCRMRequest();
        ptApiCRMRequest.setHeader(header);
        ptApiCRMRequest.setChannel(channelCode);
        ptApiCRMRequest.setChannelPwd(getClientPwd(channelCode));
        ptApiCRMRequest.setData(ptMemberDetailRequest);
        return ptApiCRMRequest;
    }

    /**
     * 登录返回结果处理
     *
     * @param ptMemberDetail
     * @param channelCode
     * @return
     */
    private MemberLoginResponse formatLoginResp(PtMemberDetail ptMemberDetail, String channelCode) {
        MemberLoginResponse resp = new MemberLoginResponse();
        resp.setId(ptMemberDetail.getBasicInfo().getMemberId());
        resp.setMemberID(ptMemberDetail.getBasicInfo().getCardNO());
        dealBasicInfo(ptMemberDetail.getBasicInfo(), resp);
        resp.setLevelName(MemberLevelEnum.findLevelNameByLevelCode(ptMemberDetail.getStateInfo().getMemberLevelCode()));
        resp.setMemberLevelCode(ptMemberDetail.getStateInfo().getMemberLevelCode());
        resp.setMemberStatusCode(ptMemberDetail.getStateInfo().getMemberStatus());
        resp.setBalanceOfMileage(0);
        dealCertInfos(ptMemberDetail.getCertificateInfo(), resp);
        dealContactInfo(ptMemberDetail.getContactInfo(), resp);
        dealAddressInfos(ptMemberDetail.getAddressInfos(), resp);
        String key = HandlerConstants.W_CHANNEL_CODE.equals(channelCode) ? HandlerConstants.USER_INFO_KEY : HandlerConstants.USER_INFO_KEY_M;
        String infoIdKey = EncoderHandler.encode("MD5", (resp.getId()) + key).toUpperCase();
        log.debug("请求ID：{} id:{},key:{} channelCode:{} infoIdKey加密:{}", MdcUtils.getRequestId(), resp.getId(), key, channelCode, infoIdKey);
        resp.setLoginKeyInfo(infoIdKey);
        resp.setResultCode(WSEnum.SUCCESS.getResultCode());
        return resp;
    }
    /**
     * 会员基本信息处理
     *
     * @param basicInfo
     * @param resp
     */
    private void dealBasicInfo(MemberBasicInfoSoaModel basicInfo, MemberLoginResponse resp) {
        resp.setcLastName(basicInfo.getCLastName());
        resp.setcFirstName(basicInfo.getCFirstName());
        resp.seteLastName(basicInfo.getELastName());
        resp.seteFirstName(basicInfo.getEFirstName());
        resp.setName(basicInfo.getCLastName() + basicInfo.getCFirstName());
        SexEnum sex = SexEnum.formatSexCode(basicInfo.getSex());
        resp.setSex(sex == null ? "" : sex.showCode);
        resp.setBirthday(DateUtils.timeStampToDateStr(basicInfo.getBirthDay()));
        SalutationEnum salution = SalutationEnum.formatSalutationCode(basicInfo.getMemberSalutation());
        resp.setTitle(salution == null ? "" : salution.getDesc());
    }
    /**
     * 证件信息处理
     *
     * @param certList
     * @param resp
     */
    private void dealCertInfos(List<MemberCertificateSoaModelV2> certList, MemberLoginResponse resp) {
        if (StringUtil.isNullOrEmpty(certList)) {
            return;
        }
        List<CustomerCertificateInfo> certListNew = new ArrayList<>();
        int i = 0;
        for (MemberCertificateSoaModelV2 cert : certList) {
            CustomerCertificateInfo custCert = new CustomerCertificateInfo();
            CertificateTypeEnum cer = CertificateTypeEnum.checkType(cert.getCertificateType());
            if (cer != null) {
                custCert.setCertType(cer.geteName());
                custCert.setCertNumber(cert.getCertificateNumber());
                if (i == 0) {
                    resp.setCertType(cer.geteName());
                    resp.setCertNumber(cert.getCertificateNumber());
                    i++;
                }
                certListNew.add(custCert);
            }
        }
        resp.setCustomerCertificateInfos(certListNew);
    }
    //联系方式处理
    private void dealContactInfo(List<MemberContactSoaModel> contactList, MemberLoginResponse resp) {
        if (StringUtil.isNullOrEmpty(contactList)) {
            return;
        }
        for (MemberContactSoaModel con : contactList) {
            if (con.getContactType() == 1) {//手机号
                resp.setMemberTel(con.getContactNumber());
            } else if (con.getContactType() == 5) { //邮箱
                resp.setMemberEmail(con.getContactNumber());
            }
        }
    }

    //处理地址信息
    private void dealAddressInfos(List<MemberAddressSoaModel> addrList, MemberLoginResponse resp) {
        if (StringUtil.isNullOrEmpty(addrList)) {
            return;
        }
        MemberAddressSoaModel addr = addrList.get(0);
        resp.setCountryCode(addr.getCountryCode());
        resp.setProvinceCode(addr.getProvinceCode());
        resp.setAddressCityCode(addr.getCityCode());
        resp.setAddressContent(addr.getAddress());
        resp.setPostCode(addr.getPostCode());
    }
    //redis缓存信息
    private Map<String, Object> userMap(String id,String cardNo,String timestamp,WxUserInfo wxUserInfo,Map<String, Object> paramMap) {
        Map<String, Object> map = new HashMap<>();
        map.put("ffpId", id);
        map.put("ffpCardNo", cardNo);
        map.put(TIMESTAMP, timestamp);
        if (paramMap != null) {
            paramMap.forEach((key, value) -> {
                if (value != null) {
                    if (value instanceof String) {
                        if (StringUtils.isNotBlank((String) value)) {
                            map.put(key, value);
                        }
                    } else {
                        map.put(key, value);
                    }
                }
            });
        }
        if (wxUserInfo != null) {
            map.put(OPENID, wxUserInfo.getOpenId());
            if (!StringUtil.isNullOrEmpty(wxUserInfo.getUnionId())) {
                map.put("unionid", wxUserInfo.getUnionId());
            }
            map.put(SESSION_KEY, wxUserInfo.getSession_key());
            map.put("errcode", "" + wxUserInfo.getErrcode());
        }
        return map;
    }

    private String getClientPwd(String channelCode) {
        return getChannelInfo(channelCode, "40");
    }


    private WxBindUserInfo findBoundUser(String openId){
        QueryWxUserRecordRequest request = new QueryWxUserRecordRequest();
        request.setOpenId(openId);
        WxBindUserInfo wxBindUserInfo = null;
        HttpResult httpResult = this.doPostClient(request, HandlerConstants.WEIXIN_MEMBER_SAVE + HandlerConstants.WEIXIN_QUERY_BIND_RECORD_URL);
        if (httpResult.isResult() && StringUtils.isNotBlank(httpResult.getResponse())) {
            BaseResultDTO<List<WxBindUserInfo>> resultDTO = (BaseResultDTO<List<WxBindUserInfo>>)
                    JsonUtil.jsonToBean(httpResult.getResponse(), new TypeToken<BaseResultDTO<List<WxBindUserInfo>>>(){}.getType());
            if ("1001".equals(resultDTO.getResultCode())) {
                List<WxBindUserInfo> wxBindUserInfos = resultDTO.getResult();
                // 获取有效绑定记录中最新一次绑定的结果
                if (CollectionUtils.isNotEmpty(wxBindUserInfos)) {
                    wxBindUserInfos.stream().filter(info -> "T".equals(info.getIsValid()) && "T".equals(info.getIsBinding())
                            && StringUtils.isNotBlank(info.getFfpId())).collect(Collectors.toList())
                            .sort((e1, e2) -> e2.getBindingDate().compareTo(e1.getBindingDate()));
                    if (CollectionUtils.isNotEmpty(wxBindUserInfos)) {
                        wxBindUserInfo = wxBindUserInfos.get(0);
                    }
                }
            }
        } else {
            log.error("查询微信小程序绑定记录网络异常");
        }
        return wxBindUserInfo;
    }

    /**
     * 小程序模板消息授权记录
     * @param request
     * @param req
     * @return
     */
    @InterfaceLog
    @ApiOperation(value = "小程序模板消息授权记录", notes = "小程序模板消息授权记录")
    @RequestMapping(value = "/saveTemplateAuthorize", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public BaseResp<String> saveTemplateAuthorize(HttpServletRequest request, @RequestBody BaseReq<TemplateAuthorizeRequest> req) {
        BaseResp<String> resp = new BaseResp<>();
        String ip = this.getClientIP(request);
        String reqId = StringUtil.newGUID() + request.getRequestURI();
        try {
            this.checkRequest(req);
            //从请求头中获取对用的登录信息
            String headChannelCode = request.getHeader(HEAD_CHANNEL_CODE);
            UserLoginToken userLoginToken = LoginUtil.getInfoFromHeader(request);
            if (userLoginToken == null) {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo(WSEnum.INVALID_TOKEN.getResultInfo());
                return resp;
            }
            // 从redis获取操作人的会员信息
            TemplateAuthorizeRequest templateAuthorizeRequest = req.getRequest();
            for (TemplateAuthorizeRequest.TemplateAuthorize templateAuthorize : templateAuthorizeRequest.getAuthorizeList()){
                // 参数认证不通过返回异常
                if (StringUtils.isBlank(templateAuthorize.getCardNo()) && StringUtils.isBlank(templateAuthorize.getTicketNo())){
                    resp.setResultCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
                    resp.setResultInfo(WSEnum.ERROR_REQUEST_PARAMS.getResultInfo());
                    return resp;
                }
            }
//            templateAuthorizeRequest.setFfpId(String.valueOf(map.get("ffpId")));
//            templateAuthorizeRequest.setFfpCardNo(String.valueOf(map.get("ffpCardNo")));
            // 保存微信模板消息授权记录
            String url = HandlerConstants.WEIXIN_MEMBER_SAVE + HandlerConstants.WEIXIN_SAVE_TEMPLATE_AUTHORIZE;
            HttpResult httpResult = this.doPostClient(templateAuthorizeRequest, url);
            if (null != httpResult && httpResult.isResult()) {
                Type type = new TypeToken<BaseResultDTO<String>>() {}.getType();
                BaseResultDTO<String> resultDTO = (BaseResultDTO<String>) JsonUtil.jsonToBean(httpResult.getResponse(), type);
                resp.setResultCode(resultDTO.getResultCode());
                resp.setResultInfo(resultDTO.getErrorMsg());
            } else {
                resp.setResultCode(WSEnum.ERROR.getResultCode());
                resp.setResultInfo("记录授权失败");
            }
        } catch (Exception e){
            this.logError(resp, reqId, ip, req, e);
            resp.setResultCode(WSEnum.ERROR.getResultCode());
            resp.setResultInfo(WSEnum.ERROR.getResultInfo());
        }
        return resp;
    }
}

<?xml version="1.0" encoding="UTF-8"?>
<web-app xmlns="http://xmlns.jcp.org/xml/ns/javaee"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://xmlns.jcp.org/xml/ns/javaee http://xmlns.jcp.org/xml/ns/javaee/web-app_3_1.xsd"
         version="3.1">
  <display-name>mobile handler Web Application</display-name>

  <session-config>
    <session-timeout>30</session-timeout>
  </session-config>
  <listener>
    <listener-class>org.springframework.web.context.request.RequestContextListener
    </listener-class>
  </listener>
  <!--Spring ApplicationContext 载入 -->
  <listener>
    <listener-class>org.springframework.web.context.ContextLoaderListener</listener-class>
  </listener>

  <context-param>
    <param-name>contextConfigLocation</param-name>
    <param-value>classpath:ApplicationContext.xml</param-value>
  </context-param>
<!--  <filter>
    <filter-name>CORS</filter-name>
    <filter-class>com.thetransactioncompany.cors.CORSFilter</filter-class>
    <init-param>
      <param-name>cors.allowOrigin</param-name>
      <param-value>*</param-value>
    </init-param>
  </filter>
  <filter-mapping>
    <filter-name>CORS</filter-name>
    <url-pattern>/*</url-pattern>
  </filter-mapping>-->

  <!--cat-->
  <filter>
    <filter-name>cat-filter</filter-name>
    <filter-class>com.dianping.cat.servlet.CatFilter</filter-class>
  </filter>
  <filter-mapping>
    <filter-name>cat-filter</filter-name>
    <url-pattern>/*</url-pattern>
    <dispatcher>REQUEST</dispatcher>
    <dispatcher>FORWARD</dispatcher>
  </filter-mapping>
  <!--编码过滤器-->
  <filter>
    <filter-name>encodingFilter</filter-name>
    <filter-class>org.springframework.web.filter.CharacterEncodingFilter</filter-class>
    <init-param>
      <param-name>encoding</param-name>
      <param-value>utf-8</param-value>
    </init-param>
  </filter>
  <filter-mapping>
    <filter-name>encodingFilter</filter-name>
    <url-pattern>/*</url-pattern>
  </filter-mapping>
  <!--报文解密过滤器-->
  <filter>
    <filter-name>aesFilter</filter-name>
    <filter-class>com.juneyaoair.mobile.filter.ReqAesFilter</filter-class>
  </filter>
  <filter-mapping>
    <filter-name>aesFilter</filter-name>
    <url-pattern>/device/ds</url-pattern>
  </filter-mapping>
  <!--请求有效性验证过滤器-->
  <filter>
    <filter-name>interceptorFilter</filter-name>
    <filter-class>org.springframework.web.filter.DelegatingFilterProxy</filter-class>
    <init-param>
      <param-name>targetFilterLifecycle</param-name>
      <param-value>true</param-value>
    </init-param>
  </filter>
  <filter-mapping>
    <filter-name>interceptorFilter</filter-name>
    <url-pattern>/device/ds</url-pattern>
  </filter-mapping>
  <!--对m站和微信请求进来的，改变其渠道相关信息，已经注入会员信息-->
  <!--需要登录的接口过滤-->
  <filter>
    <filter-name>loginInfoFilter</filter-name>
    <filter-class>com.juneyaoair.mobile.filter.LoginInfoFilter</filter-class>
    <init-param>
      <param-name>excludeuri</param-name>
      <!--url以逗号分隔-->
      <param-value>
        /data/index/queryTabService,
        /nemPaymentService/initPayMethod,
        /anwreqsys/file/upload,
        /anwreqsys/file/filesUpload,
        /applyVisa/queryType,
        /smsOrderService/queryOrderDetailM,
        /electrionicproof/queryFlightChangeProof,
        /paymentResult/payResult,
        /aliMember/loginByCellPhoneNo,
        /aliMember/autoLogin,
      </param-value>
    </init-param>
    <init-param>
      <param-name>uploaduri</param-name>
      <!--url以逗号分隔-->
      <param-value>
        /anwreqsys/file/filesUploadV2
      </param-value>
    </init-param>
  </filter>
  <filter-mapping>
    <filter-name>loginInfoFilter</filter-name>
    <url-pattern>/*</url-pattern>
  </filter-mapping>

  <!--登陆返回参数过滤-->
  <filter>
    <filter-name>loginFilter</filter-name>
    <filter-class>com.juneyaoair.mobile.filter.LoginFilter</filter-class>
  </filter>
  <filter-mapping>
    <filter-name>loginFilter</filter-name>
    <url-pattern>/member/login</url-pattern>
    <url-pattern>/member/memberEncryptLogin</url-pattern>
    <url-pattern>/member/captchalogin</url-pattern>
    <url-pattern>/member/thirdlogin</url-pattern>
    <url-pattern>/member/thirdloginqq</url-pattern>
    <url-pattern>/member/bindthirdlogin</url-pattern>
    <url-pattern>/member/hwlogin</url-pattern>
    <url-pattern>/wechat/miniprogram/member/bindingByPhoneNo</url-pattern>
    <url-pattern>/wechat/miniprogram/member/bindingByCaptcha</url-pattern>
    <url-pattern>/wechat/miniprogram/member/bindingByPassword</url-pattern>
    <url-pattern>/wechat/miniprogram/member/bindLoginUser</url-pattern>
    <url-pattern>/wechat/miniprogram/member/autoLogin</url-pattern>
    <url-pattern>/oneClick/geetestLogin</url-pattern>、
    <url-pattern>/aliMember/loginByCellPhoneNo</url-pattern>
    <url-pattern>/aliMember/autoLogin</url-pattern>
  </filter-mapping>

  <servlet>
    <servlet-name>springmvc</servlet-name>
    <servlet-class>
      org.springframework.web.servlet.DispatcherServlet
    </servlet-class>
    <init-param>
      <param-name>contextConfigLocation</param-name>
      <param-value>classpath:springmvc-config.xml</param-value>
    </init-param>
    <load-on-startup>2</load-on-startup>
  </servlet>

  <servlet-mapping>
    <servlet-name>springmvc</servlet-name>
    <url-pattern>/</url-pattern>
  </servlet-mapping>

<!--  <servlet>
    <servlet-name>DruidStatView</servlet-name>
    <servlet-class>com.alibaba.druid.support.http.StatViewServlet</servlet-class>
  </servlet>
  <servlet-mapping>
    <servlet-name>DruidStatView</servlet-name>
    <url-pattern>/druid/*</url-pattern>
  </servlet-mapping>-->

  <!-- 默认的错误处理页面 -->
  <error-page>
    <error-code>403</error-code>
    <location>/error/403.html</location>
  </error-page>
  <error-page>
    <error-code>404</error-code>
    <location>/error/404.html</location>
  </error-page>
  <!-- 仅仅在调试的时候注视掉,在正式部署的时候不能注释 -->
  <!-- 这样配置也是可以的，表示发生500错误的时候，转到500.jsp页面处理。 -->
  <error-page>
    <error-code>500</error-code>
    <location>/error/500.html</location>
  </error-page>

  <!-- 这样的配置表示如果jsp页面或者servlet发生java.lang.Exception类型（当然包含子类）的异常就会转到500.jsp页面处理。 -->
  <error-page>
    <exception-type>java.lang.Exception</exception-type>
    <location>/error/500.html</location>
  </error-page>

  <error-page>
    <exception-type>java.lang.Throwable</exception-type>
    <location>/error/500.html</location>
  </error-page>
  <!--
  当error-code和exception-type都配置时，exception-type配置的页面优先级高
  及出现500错误，发生异常Exception时会跳转到500.jsp
   -->
  <welcome-file-list>
    <welcome-file>index.jsp</welcome-file>
  </welcome-file-list>
</web-app>

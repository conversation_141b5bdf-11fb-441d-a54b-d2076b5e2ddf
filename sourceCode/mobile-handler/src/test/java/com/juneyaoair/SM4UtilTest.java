package com.juneyaoair;

import com.juneyaoair.utils.util.SM4Util;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;

@Slf4j
public class SM4UtilTest {

    public static final String DEFAULT_KEY = "8sjZW5QeTJtCfcrW";
    public static final String DEFAULT_KEY2 = "hocc.sm4.pass.01";

    @Test
    public void test1() throws Exception {
        String str = "123qwe";
        log.debug(str);
        String encrypt = SM4Util.encryptEcb(str, DEFAULT_KEY);
        String encrypt2 = SM4Util.encryptEcb2Base64(str, DEFAULT_KEY);

        log.debug("16进制的加密字符串"+encrypt);
        log.debug("base64的加密字符串"+encrypt2);

        String decrypt = SM4Util.decryptEcb(encrypt, DEFAULT_KEY);
        String decrypt2 = SM4Util.base64decryptEcb(encrypt2, DEFAULT_KEY);
        log.debug(decrypt);
        log.debug(decrypt2);
        Assert.assertTrue(true);
    }

    @Test
    public void test2() throws Exception {
        String str = "Chang1232";
        log.debug(str);
        String encrypt = SM4Util.encryptEcb(str, DEFAULT_KEY2);
        String encrypt2 = SM4Util.encryptEcb2Base64(str, DEFAULT_KEY2);

        log.debug(encrypt);
        log.debug(encrypt2);

        String decrypt = SM4Util.decryptEcb(encrypt, DEFAULT_KEY2);
        String decrypt2 = SM4Util.encryptEcb2Base64(encrypt2, DEFAULT_KEY2);
        log.debug(decrypt);
        log.debug(decrypt2);
        Assert.assertTrue(true);
    }

    @Test
    public void test3() throws Exception {
        String encrypt = SM4Util.decryptEcb("C3A87457A06E1BA3D16B7180D8467053", DEFAULT_KEY);
        log.debug(encrypt);
        Assert.assertNotNull(encrypt);
    }
}

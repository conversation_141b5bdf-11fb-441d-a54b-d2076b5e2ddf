package com.juneyaoair;

import com.juneyaoair.utils.DES3;
import com.juneyaoair.utils.EncoderHandler;
import com.juneyaoair.utils.util.DateUtils;
import org.junit.Assert;
import org.junit.Test;

public class SmsOrderUtil {
    /**
     * 短信链接加密
     */
    @Test
    public void smsLinkEncode(){
        String orderNo = "ORD22041001460498";
        String channelOrderNo = "P202204102232256684";
        String customerNo = "3515335";
        String timeStamp = DateUtils.getCurrentTimeStr();
        String desKey = EncoderHandler.encodeByMD5("juneyaoair");
        desKey = EncoderHandler.encodeByMD5(desKey);
        //密文
        customerNo = DES3.des3EncodeECB(desKey, customerNo);
        String keyStr = "OrderNo=" + orderNo + "&ChannelOrderNo=" + channelOrderNo + "&CustomerNo=" + customerNo + "&Timestamp=" + timeStamp;
        keyStr = EncoderHandler.encodeByMD5(EncoderHandler.encodeByMD5(EncoderHandler.encodeByMD5(keyStr)) + "juneyaoair");
        System.out.println("加签结果：order:"+"key:"+keyStr);
        System.out.println("短信链接：https://t-m.hoair.cn/smsLink/index.html?OrderNo="+orderNo+"&ChannelOrderNo="+channelOrderNo+"&CustomerNo="+customerNo+"&Timestamp="+timeStamp+"&key="+keyStr);
        Assert.assertNotNull(keyStr);
    }
}

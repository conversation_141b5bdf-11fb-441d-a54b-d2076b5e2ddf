package com.juneyaoair;

import com.alibaba.fastjson.JSON;
import com.juneyaoair.dsop.sdk.client.OpenClient;
import com.juneyaoair.dsop.sdk.common.RequestMethod;
import com.juneyaoair.dsop.sdk.request.CommonRequest;
import com.juneyaoair.dsop.sdk.response.CommonResponse;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2025/5/29 15:26
 */
@Slf4j
public class DsopTest {
    @Test
    public void testDsop(){
        String url = "https://dsop.juneyaoair.com";
        String appId = "202505211374677390059372544";
        String privateKey = "MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQCuRrkmPFGfj6Vc47eodEOMSkvGAYN456nJiWxLqoGLV26LKK73piIs/ArEZ1ecP5nH5/6HZR5bj9YpkRKYGLw5jeA5E383H78tgvTCzQCi35BD9jphJUEQGvLCSRC8bRHSKS9/TgB+hjCDOA9HuTwHUNFjdvwopVXyV7sMT8oLLtS49KwQv5txJ9lzIAcv3hUXBwjXnk3am0BHEgLJUKQIZ7cKptIyQEZA5uqdNqJ4Xal+WHyoC2HdxDdiYzHA7aXYi0v8F/CNAHYX0imPvfjFhufS7uNcqZBQ4gvw5FNpiUH1Ih0g0iMYMMe/Gm914UUpqPYRZ0f/2blHRjvTxDHnAgMBAAECggEAdQFGjUUyB86JVzQLui1UrHu1I9sgSaw/ce+xpZt0Hfk1XniR//KqP0l4cSRwzZZlyLEZUg2xtAd94apksXjp5jo6sBdG8dt+ny+s5Jx+MNuoH/jQeShp5kYxdf9YHSmdLVw5Z18XNdsh9vdMSvsyMjcwXXclFKTsbyg3jKyFGbvWnDIXekUbsGsq9yW68kutJ8RAdiy4vIfsF1zmN2yH9ifL/tS0kudoN8R2Zd+BGPhsK6cERpTgHontQfNf+6QybR6N6ZjUCfMUukO15PPmb9DzjbL05RMEcTQ2AVxB6OqJ/XQfYgnIw+WhR06sZVd/t/Y20ea5rOg4js8pFPnfAQKBgQD/Q8vSuX658kB/2CvBqxa038Lid+NcVgXeA6XgXB9Fe8cEWg+YlSNidlpH5AvOZUoovW0w1niAJJZsRIew6vCzCrS7rm3XFSuBkE+PDsY15gTISYOBmXykyW26OKe+2d3RzqnFTjoS5PetBSGwCIHwnY3LGm/efGwMm+/bcL4dawKBgQCuxzcSDersWu5D/qaHSYjSaxwSq81J5oLm1Dg0LobUkq4XMH5Zl7yphWbxylrSoPwDnZ5/IC6tSxZEENIu1BoY3t453hox13zmHZ3g1IGn2udi8UVivVuYp1WOqWwJo7RzdH3Cv4zUWyscU6pAJgrGNuS3fBnJ12KocHWEHBJAdQKBgGXvceZ9ksC4n88tyRN2ugOS06VSUOXfqEonVMdKomZB7pCrUk0RSWcZciYBSEGsqzenpYH6M3agpb0ohLM+rs4guVm1Z0Gbv5rmwQ6UzMxbRY+h47UOUPsRQRM7aqlJIU6WY69Z9ND0xH9AbFp6m0E1+Zigvwmu0Asc/mS4GgNvAoGAP1Uo+33XlvYMSM9KANQApF7zuUBQD2tcG9Di3OXPLdBJDVYhMs3tK3CPsSYmaxSyvYysFlzPDTScSOXfg+CP5Fs+I7H7w/vV4nzw7LuUkAKevhe3kOQVAOgB9MQnxgZwKTYZLCzFF15B7O3UT5J5bQIPNv3dSmql8Wtu6gRSCNECgYBnEpcRwpurkC/ov/29Rf4vRcAQ3PndkyRVtaR4NneDd8NAozphczD8wmDKCXeNwkH188aKu4D0txAQ6zVY18Rdjsb9W1q8I15WxjPQAgLZUn94IM80DeciVyjb1yyXkKbJQxyzwO1VZ6ATzVPLXdi493AOzBbzBQuAyBcoJhBvig==";
        OpenClient client = new OpenClient(url, appId, privateKey, "");
        // 创建请求对象
        CommonRequest request = new CommonRequest("order.check.in.seat.selection.itinerary");
        request.setRequestMethod(RequestMethod.POST);
        Map<String,String> param = new HashMap<>();
        param.put("ticketNumber","0182340240088");
        request.setBizContent(JSON.toJSONString(param));
        client.execute(request);
    }
}

package com.juneyaoair.util;

import com.alibaba.fastjson.JSONObject;
import com.juneyaoair.appenum.WSEnum;
import com.juneyaoair.baseclass.upclass.QueryItineraryParam;
import com.juneyaoair.exception.CommonException;
import com.juneyaoair.utils.json.JsonUtil;
import com.juneyaoair.utils.util.AESTool;
import com.juneyaoair.utils.util.JJSCBase62Util;
import com.juneyaoair.utils.util.MdcUtils;
import com.juneyaoair.utils.util.RsaUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import java.nio.charset.StandardCharsets;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2025/6/5 13:39
 */
@Slf4j
public class RsaUtilTest {

    @Test
    public void generateKeyPair() throws Exception {
        Map<String, String> keyPair = RsaUtil.generateKeyPair();
        String publicKey = keyPair.get("publicKey");
        String privateKey = keyPair.get("privateKey");
        log.error("公钥:{}", publicKey);
        log.error("私钥:{}", privateKey);
    }

    @Test
    public void signAndVerify()throws Exception{
        String msg = "这是测试信息";
        Map<String, String> keyPair = RsaUtil.generateKeyPair();
        String publicKey = keyPair.get("publicKey");
        String privateKey = keyPair.get("privateKey");
        String sign = RsaUtil.sign(msg,privateKey);
        log.error("RSA签名认证结果:{}",RsaUtil.verify(msg,sign,publicKey));
    }

    @SneakyThrows
    @Test
    public void jjsc(){
        String aesKey = "9o3vRlBekTj1H7cQ24INp5UuWEbVxgqn";
        String ivParam = "VC12m8OURuJrP6kx";
        String privateKey = "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQDcSPL+Qfn0NUT8uM4IQBExYcpHfdtJvDgRgUkD5hDiXwC/cjgAg5eGnoo+lHq+04kg3VBIrdADDC++94o4/XXs2lbDCLWGe82d5Qkr/8I/V006S4DBneqM4IcGglarorwcp9NSssi9tNuFNiRygfINLT+yq/MH9GrPCmAjCYtwxNc8CaNfBtxKh+uiZZTHb+wHLG9yFC/+Ja98YfbBQTlFz4t0DRArFTqKnCdVlAxDDHCUMbi8lj4BykENyJMXc68ZVOG1V1f1IVqelWVVu7eLDfo9ocb5hzNvEVBM9anL6otCLt1D9H6xNmwzY8ETdW1fmyiwW49FP0EH1EumfGe1AgMBAAECggEActwmt6S3LDrKaewCohKl5FXVH0nkmaHmqln7NGk1zJM01J49zSbQT4VDGDZbejzad6RuRDc9CxnDm5N/IxAl+QYvzyovXJwZLodKLQMCyfw/bHIKBOrObf506VP01THZXCe17J0ERsaS3/bHMl3HVE+Psx4tbqxnNOV4E0zaxEUnO6Y5wexvp4ekM/VRIVXE060lLgi738C4RKIwnHYc/KdU29Tmty9mX4TqNxSokve/vimLNvPX5cCsYVAq9IrzGMmtPwufJ8H3AdXsOYX3DDUvtzdlwDyOlz1ecRn5xeD2DNZUsCitsm+WgTQJS1P/vrS6O/Pd5kpYyfxLDYoP0QKBgQDwKxy5m8bZl2FGvs6ntVc9vZ91M1u+l+jN6G1PQRxo6X5+UGwnfz3NwVGLtgaXEn/3CBIrMreGh1h7z0sF7pxFeTszjwAtGduKWppicSbQjuhPA50z9q3pjSZPc4U/anNqSoPrCiaCCWUhRcKteAmrFrcM8ECq516ngdEnN5jZ1wKBgQDqzkzLQZILuEfJ3sDuKNlQ/f3SwYIKT/+hYWocpeFwen1zHrNwU7ILrKYzfcJhYoeZmMJgy6cMY+At7jy7mgJL9gHXjrSsy0MvjyDfeifhqiiEJdt7ykkZydsE0PxONraY8CS3gO8OOOdPiX5bcMUBOSAX6N+D8yLr5c4+lG2RUwKBgHw/wZegqKpPb6r68cy/u0ecv08R5+fdjTphgZOb+sen7KIKlzmD0YSN+9MMs7IahwtVKJkinuHBSZyb7JNAE6a5hlAxLgUnOHYGU5KUOWFyXWrrUrvcZbLLLEO0Xv4k41rc/Axv7uYdWC1zerjlvhK+KKtP7BS+qDB+r6bbU393AoGBAN0P70Lpb9ty2ruW6WMPuFHFXyP+JTUaThycvy9LiSzBVbx9agjV82mEWVFfFBNllS6poE9LTPGgUl5Lzof9Hi5v5RoRc86+63kqMpPTyRpvfge0MKiiMSKDZZ1qODBW2KCybo8JZ4enUTcTxo/GYuOc6bc4+r6auQUI7Z0DVwibAoGAOQZNHW+mwtUK9UTKx9O8rZzy2HnrLH5lYtvriLPj9J4ZodvSDXNFdQhZ6uY+xUMw6k4RzXu6lykbKbSlofHobX33H8x1BjcpcZ+/5CaQsxR0vWtaXf+b3W2IKRUQ+i8aSRAtmQjQpoHTua37QpFYJViSIz1awBwD8oKPokB7ft4=";
        String publicKey = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA3Ejy/kH59DVE/LjOCEARMWHKR33bSbw4EYFJA+YQ4l8Av3I4AIOXhp6KPpR6vtOJIN1QSK3QAwwvvveKOP117NpWwwi1hnvNneUJK//CP1dNOkuAwZ3qjOCHBoJWq6K8HKfTUrLIvbTbhTYkcoHyDS0/sqvzB/RqzwpgIwmLcMTXPAmjXwbcSofromWUx2/sByxvchQv/iWvfGH2wUE5Rc+LdA0QKxU6ipwnVZQMQwxwlDG4vJY+AcpBDciTF3OvGVThtVdX9SFanpVlVbu3iw36PaHG+YczbxFQTPWpy+qLQi7dQ/R+sTZsM2PBE3VtX5sosFuPRT9BB9RLpnxntQIDAQAB";
        QueryItineraryParam queryItineraryParam = new QueryItineraryParam();
        queryItineraryParam.setMemberId("2881702961");
        queryItineraryParam.setRegion("");
        queryItineraryParam.setPhone("");
        queryItineraryParam.setNativeGivenName("姚春峰");
        queryItineraryParam.setSurname("");
        queryItineraryParam.setGivenName("");
        queryItineraryParam.setTimestamp(System.currentTimeMillis());
        String sign;
        String aes;
        try {
            log.info("{},RSA待签名参数:{}", MdcUtils.getRequestId(),queryItineraryParam.processRsaParam());
            sign = RsaUtil.sign(queryItineraryParam.processRsaParam(),privateKey);
            log.info("{},RSA签名结果:{}",MdcUtils.getRequestId(),sign);
            queryItineraryParam.setSign(sign);
            aes = AESTool.encrypt(JsonUtil.objectToJson(queryItineraryParam),aesKey,ivParam);
            log.info("{},AES加密结果:{}",MdcUtils.getRequestId(),aes);
        } catch (Exception e) {
            log.error("{},签名生成异常:", MdcUtils.getRequestId(),e);
            throw new CommonException(WSEnum.ERROR_CHK_ERROR.getResultCode(), "签名生成异常");
        }
        // 编码
        String encoded = JJSCBase62Util.encodeBase62(aes.getBytes(StandardCharsets.UTF_8));
        log.error("参数输出结果:{}", encoded);
        //解码
        byte[] bytes = JJSCBase62Util.decodeBase62(encoded);
        String decode = new String(bytes);
        log.error("Base62解码:{}", decode);
        String str = AESTool.decrypt(decode,aesKey,ivParam);
        log.error("aes解密:{}", str);
        JSONObject jsonObject = JSONObject.parseObject(str);
        String data = jsonObject.getString("memberId")
                +jsonObject.getString("region")
                +jsonObject.getString("phone")
                +jsonObject.getString("nativeGivenName")
                +jsonObject.getString("surname")
                +jsonObject.getString("givenName")
                +jsonObject.getString("timestamp")
                ;
        log.error("RSA签名认证结果:{}",RsaUtil.verify(data,jsonObject.getString("sign"),publicKey));

    }
}
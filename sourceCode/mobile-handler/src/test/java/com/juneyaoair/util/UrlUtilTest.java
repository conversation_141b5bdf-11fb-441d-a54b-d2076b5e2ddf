package com.juneyaoair.util;

import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;

import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @description
 * @date 2024/7/23 11:11
 */
@Slf4j
public class UrlUtilTest {

    @Test
    public void isOffUrl() {
        Set<String> set = new HashSet<>();
        set.add("//push/doPushBatchByBatch");
        boolean offline = UrlUtil.isContainUrl(set,"/push/doPushBatchByBatch");
        log.error("url匹配结果:{}",offline);
        Assert.assertTrue(offline);
    }

    @Test
    public void isOffUrl2() {
        Set<String> set = new HashSet<>();
        set.add("/push/doPushBatchByBatch");
        boolean offline = UrlUtil.isContainUrl(set,"/push/doPushBatchByBatch");
        log.error("url匹配结果:{}",offline);
        Assert.assertTrue(offline);
    }
}
package com.juneyaoair.mobile.handler.util;

import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Value;

import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.*;

/**
 * <AUTHOR>
 * @description
 * @date 2024/3/28 14:25
 */
public class FileUtilsTest {
    @Test
    public void useMinio() {
        List<String> list = Arrays.asList("IdPhotoImage");
        boolean flag = FileUtils.useMinio(list,"IdPhotoImage");
        Assert.assertTrue(flag);
    }
}
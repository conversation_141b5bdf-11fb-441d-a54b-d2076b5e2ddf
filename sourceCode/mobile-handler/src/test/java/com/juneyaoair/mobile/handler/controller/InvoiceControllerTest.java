package com.juneyaoair.mobile.handler.controller;

import com.juneyaoair.baseclass.basicsys.response.AirPortInfoDto;
import com.juneyaoair.exception.OperationFailedException;
import com.juneyaoair.mobile.core.bean.unitorder.response.OrderInvoiceInfoDto;
import com.juneyaoair.mobile.handler.HandlerConstants;
import com.juneyaoair.mobile.handler.bean.invoice.CouponSourceEnum;
import com.juneyaoair.mobile.handler.comm.CommodityEnum;
import com.juneyaoair.mobile.handler.service.LocalCacheService;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;


@RunWith(SpringJUnit4ClassRunner.class)
@WebAppConfiguration
@ContextConfiguration(locations = {"classpath:ApplicationContext.xml"})
public class InvoiceControllerTest {

    @Autowired
    private LocalCacheService localCacheService;

    /**
     * 国际逾重行李券 电子发票商品 编码
     * http://zentao.juneyaoair.com/zentao/task-view-19925.html
     *
     */
    @Test
    public void invoiceEncodeTest(){
        // 国内
        OrderInvoiceInfoDto invoiceOfDomestic = new OrderInvoiceInfoDto();
        invoiceOfDomestic.setCouponSource("BaggageCoupon");
        invoiceOfDomestic.setDepAirport("SHA");
        invoiceOfDomestic.setArrAirport("SYN");
        // 国际
        OrderInvoiceInfoDto invoiceOfInternational = new OrderInvoiceInfoDto();
        invoiceOfInternational.setCouponSource("BaggageCoupon");
        invoiceOfInternational.setDepAirport("PVG");
        invoiceOfInternational.setArrAirport("HEL");
        //
        Assert.assertEquals(this.invoiceEncodeTest(invoiceOfInternational), CommodityEnum.S01086);
        Assert.assertEquals(this.invoiceEncodeTest(invoiceOfDomestic), CommodityEnum.S01004);
    }

    /**
     * hotfix/fix-国际逾重行李券电子发票商品编码调整
     */
    public CommodityEnum invoiceEncodeTest(OrderInvoiceInfoDto orderInvoiceInfoDto) {

        CommodityEnum commodityEnum;
        CouponSourceEnum couponSourceEnum = CouponSourceEnum.getByCode(orderInvoiceInfoDto.getCouponSource());

        if (null == couponSourceEnum) {
            throw new OperationFailedException("附加服务类型错误");
        } else {
            switch (couponSourceEnum) {
                case UNLIMITED_FLY_V2:
                case UNLIMITED_FLY_V2_SF:
                case ADULTUNLIMITEDFLY:
                case COUPON:
                case CHILDUNLIMITEDFLY:
                case THEME_SEAFOOD_COUPON:
                case THEME_MILKTEA_COUPON:
                case THEME_CHERRY_COUPON:
                case THEME_OUTHIKE_COUPON:
                case THEME_VERMIC_COUPON:
                case THEME_SEALAND_COUPON:
                case THEME_SKITRIP_COUPON:
                case THEME_HOTPOT_COUPON: 
                    commodityEnum = CommodityEnum.S01001;break;
                case BAGGAGECOUPON:
                case Baggage:
                    if (this.isInternationalAirport(orderInvoiceInfoDto.getDepAirport()) || this.isInternationalAirport(orderInvoiceInfoDto.getArrAirport())) {
                        commodityEnum = CommodityEnum.S01086;
                    } else {
                        commodityEnum = CommodityEnum.S01004;
                    }
                    break;
                case LOUNGECOUPON:
                case Lounge:
                    commodityEnum = CommodityEnum.S01008;break;
                case UPGRADEUNLIMITED: // 无限升舱卡
                case AIRPLANE_UPGRADE: // 机上升舱
                case UPGRADECOUPON:
                case Upgrade:
                    commodityEnum = CommodityEnum.S01006;break;
                case MailTravel:
                    commodityEnum = CommodityEnum.S01034;break;
                case BRANDMEALS:
                    commodityEnum = CommodityEnum.S01458;break;
                case PAYSEAT:
                    commodityEnum = CommodityEnum.S01037;break;
                case EXTRABAGGAGE:
                    commodityEnum = CommodityEnum.S01001BAG;break;
                case RESCHEDULE:
                    commodityEnum = CommodityEnum.S01007;break;
                default:
                    throw new OperationFailedException("");
            }
            return commodityEnum;
        }


    }

    private boolean isInternationalAirport(String airportCode) {
        if (StringUtils.isBlank(airportCode)) {
            return false;
        }
        AirPortInfoDto airPortInfoDto = localCacheService.getLocalAirport(airportCode);
        if (null == airPortInfoDto) {
            return false;
        }
        return airPortInfoDto.getIsInternational().equals(HandlerConstants.TRIP_TYPE_I);
    }

}
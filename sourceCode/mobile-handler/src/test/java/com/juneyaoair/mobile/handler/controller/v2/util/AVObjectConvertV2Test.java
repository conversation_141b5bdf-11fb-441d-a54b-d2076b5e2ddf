package com.juneyaoair.mobile.handler.controller.v2.util;

import com.google.gson.reflect.TypeToken;
import com.juneyaoair.baseclass.change.ChangeAndRefundRule;
import com.juneyaoair.thirdentity.av.comm.PtRefundRuleInfo;
import com.juneyaoair.utils.json.JsonUtil;
import org.junit.Assert;
import org.junit.Test;

import java.util.List;

public class AVObjectConvertV2Test {

    @Test
    public void toChangeRules() {
    }

    @Test
    public void toRefundRules() {
        String rule  = "[{\"TicketUsage\":null,\"FlightTimeCondition\":\"0\",\"TimeConditionStart\":\"48/H\",\"TimeConditionEnd\":\"168/H\",\"Fee\":78,\"ShowFeeRate\":\"5%\",\"UsedTicketPrice\":null},{\"TicketUsage\":null,\"FlightTimeCondition\":\"0\",\"TimeConditionStart\":\"4/H\",\"TimeConditionEnd\":\"48/H\",\"Fee\":155,\"ShowFeeRate\":\"10%\",\"UsedTicketPrice\":null},{\"TicketUsage\":null,\"FlightTimeCondition\":\"0\",\"TimeConditionStart\":\"168/H\",\"TimeConditionEnd\":null,\"Fee\":78,\"ShowFeeRate\":\"5%\",\"UsedTicketPrice\":null},{\"TicketUsage\":null,\"FlightTimeCondition\":\"0\",\"TimeConditionStart\":\"0/H\",\"TimeConditionEnd\":\"4/H\",\"Fee\":310,\"ShowFeeRate\":\"20%\",\"UsedTicketPrice\":null},{\"TicketUsage\":null,\"FlightTimeCondition\":\"1\",\"TimeConditionStart\":\"0/H\",\"TimeConditionEnd\":null,\"Fee\":310,\"ShowFeeRate\":\"20%\",\"UsedTicketPrice\":null}]";
        List< PtRefundRuleInfo> ptRefundRuleInfoList = JsonUtil.fromJson(rule,new TypeToken<List< PtRefundRuleInfo>>(){});
        List<ChangeAndRefundRule> changeAndRefundRuleList = AVObjectConvertV2.toRefundRules(ptRefundRuleInfoList, "D");
        Assert.assertNotNull(changeAndRefundRuleList);
    }
}
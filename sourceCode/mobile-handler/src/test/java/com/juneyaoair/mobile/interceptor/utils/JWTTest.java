package com.juneyaoair.mobile.interceptor.utils;

import com.auth0.jwt.JWT;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.juneyaoair.mobile.handler.HandlerConstants;
import org.junit.Test;

/**
 * Created by yaocf on 2022/7/22  9:47.
 */

public class JWTTest {
    @Test
    public void JWTdecode(){
        DecodedJWT jwt = JWT.decode("eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJtb2JpbGVUb2tlblVzZXJJbmZvIjoie1widXNlcklkXCI6MTM4NzY1MTcsXCJtZW1iZXJJZFwiOlwiMzEwMjg5NTkxM1wiLFwiZGV2aWNlSWRcIjpcIjExMTExXCJ9IiwiZXhwIjoxNjY1MjQxMDUzfQ.bYXzVKXpfrPGDrudPv5DBkEfMk5jbzXOIgj2Cs5G4nA");
        String str = jwt.getClaim(HandlerConstants.MOBILE_TOKEN_USER_INFO).asString();
        System.out.println(str);
    }
}
package com.juneyaoair.mobile.handler.controller;

import org.junit.Assert;
import org.junit.Test;

import static org.junit.Assert.*;

public class NewCouponControllerTest {

    @Test
    public void removePassengerNameBracket() {
        NewCouponController newCouponController = new NewCouponController();
        Assert.assertEquals("周渝楷", newCouponController.removePassengerNameBracket("周渝楷(UM9)"));
        Assert.assertEquals("周渝楷", newCouponController.removePassengerNameBracket("周渝楷 (UM9)"));
        Assert.assertEquals("周渝楷", newCouponController.removePassengerNameBracket("周渝楷  (UM9)"));
        Assert.assertEquals("周渝楷", newCouponController.removePassengerNameBracket("周渝楷 (um9)"));
        Assert.assertEquals("周渝楷", newCouponController.removePassengerNameBracket("周渝楷 (CHILD)"));
        Assert.assertEquals("周渝楷", newCouponController.removePassengerNameBracket("周渝楷 (CHD)"));
        Assert.assertEquals("周渝楷", newCouponController.removePassengerNameBracket("周渝楷 (MS)"));
        Assert.assertEquals("周渝楷", newCouponController.removePassengerNameBracket("周渝楷 (MR)"));
        Assert.assertEquals("周渝楷CHD", newCouponController.removePassengerNameBracket("周渝楷CHD"));
        Assert.assertEquals("CUI/CAN", newCouponController.removePassengerNameBracket("CUI/CAN"));
        Assert.assertEquals("张三", newCouponController.removePassengerNameBracket("张三"));
    }
}
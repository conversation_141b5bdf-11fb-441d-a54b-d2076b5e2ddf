package com.juneyaoair.mobile.handler.util;

import com.juneyaoair.baseclass.basicsys.response.AirPortInfoDto;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;

import static org.junit.Assert.*;

/**
 * <AUTHOR>
 * @description
 * @date 2024/10/27 10:58
 */
@Slf4j
public class FlightUtilTest {

    @Test
    public void convertDateWithSummer() {
        AirPortInfoDto airPortInfoDto = new AirPortInfoDto();
        airPortInfoDto.setCityTimeZone("2");
        String str = FlightUtil.convertDateWithSummer("8","2024-10-26 13:25",airPortInfoDto);
        log.error("转换后时间:{}",str);
        Assert.assertNotNull(str);
    }
}
//package com.juneyaoair.mobile.handler.service.impl;
//
//import com.juneyaoair.baseclass.basicsys.response.AirLineInfoDepCityDto;
//import com.juneyaoair.mobile.handler.service.IBasicService;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.test.context.ContextConfiguration;
//import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
//import org.springframework.test.context.web.WebAppConfiguration;
//import org.springframework.util.StopWatch;
//
//import java.util.List;
//import java.util.Map;
//
//import static org.junit.Assert.*;
//
///**
// * <AUTHOR>
// * @description
// * @date 2021/6/7  12:59.
// */
//@RunWith(SpringJUnit4ClassRunner.class)
//@WebAppConfiguration
//@ContextConfiguration(locations = {"classpath:ApplicationContext.xml"})
//public class BasicServiceTest {
//
//    @Autowired
//    private IBasicService basicService;
//
//    @Test
//    public void selectCity() {
//        basicService.selectCity("SHA");
//    }
//
//    @Test
//    public void selectAirport() {
//        basicService.selectAirport("SHA");
//    }
//
//    @Test
//    public void selectAllAirline(){
//        StopWatch stopWatch = new StopWatch("查询航线");
//        stopWatch.start();
//        List<AirLineInfoDepCityDto> list = basicService.selectAllAirline();
//        for(AirLineInfoDepCityDto a:list){
//            System.out.println(a);
//        }
//        stopWatch.stop();
//        System.out.println(stopWatch.prettyPrint());
//    }
//
//    @Test
//    public void queryAllCityMap(){
//        StopWatch stopWatch = new StopWatch("查询所有城市");
//        stopWatch.start();
//        Map<String,String> map  = basicService.queryAllCityMap();
//        stopWatch.stop();
//        System.out.println(stopWatch.prettyPrint());
//    }
//}
package com.juneyaoair.mobile.handler.service;

import com.juneyaoair.baseclass.basicsys.response.AirPortInfoDto;
import com.juneyaoair.baseclass.basicsys.response.CityInfoDto;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import static org.junit.Assert.*;

/**
 * <AUTHOR>
 * @description
 * @date 2021/6/11  8:25.
 */
@RunWith(SpringJUnit4ClassRunner.class)
@WebAppConfiguration
@ContextConfiguration(locations = {"classpath:ApplicationContext.xml"})
public class LocalCacheServiceTest {
    @Autowired
    private LocalCacheService localCacheService;

    @Test
    public void getLocalCity(){
        //第一次获取
        CityInfoDto cityInfoDto = localCacheService.getLocalCity("SHA");
        Assert.assertNotNull(cityInfoDto);
        //第二次验证是否从缓存获取
        CityInfoDto cityInfoDto2 = localCacheService.getLocalCity("SHA");
        Assert.assertNotNull(cityInfoDto2);
    }

    @Test
    public void getLocalAirport(){
        //第一次获取
        AirPortInfoDto airPortInfoDto = localCacheService.getLocalAirport("SHA");
        Assert.assertNotNull(airPortInfoDto);
        //第二次验证是否从缓存获取
        AirPortInfoDto airPortInfoDto2 = localCacheService.getLocalAirport("SHA");
        Assert.assertNotNull(airPortInfoDto2);
    }

}
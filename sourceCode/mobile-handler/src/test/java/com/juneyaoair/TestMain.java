package com.juneyaoair;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.juneyaoair.baseclass.response.newCheckinSeat.AirportInfoResponse;
import com.juneyaoair.utils.json.JsonUtil;
import com.juneyaoair.utils.util.DateUtils;
import org.junit.Test;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2019/2/20  13:32.
 */
public class TestMain {
    public static void main(String[] args) {
        String ff= "{\"Result\":true,\"Response\":\"{\\\"resultCode\\\":\\\"10001\\\",\\\"errorMsg\\\":\\\"成功\\\",\\\"tairportCheckInSetDTOList\\\":[{\\\"airportCode\\\":\\\"SHA\\\",\\\"checkinType\\\":\\\"1,2,3,4,5,6\\\",\\\"checkinDepArr\\\":\\\"1,2\\\",\\\"checkinTypeAbroad\\\":\\\" \\\",\\\"checkinDomesticAbroad\\\":\\\"D\\\",\\\"airportName\\\":\\\"上海虹桥机场\\\",\\\"checkinInternalDepArr\\\":null,\\\"internalDepAirportCode\\\":null,\\\"internalArrAirportCode\\\":null,\\\"autoCheckin\\\":true,\\\"tairportCheckinChannelTimeDTOList\\\":[{\\\"airportCode\\\":\\\"SHA\\\",\\\"channelno\\\":\\\"10000\\\",\\\"openFre\\\":\\\"72\\\",\\\"openEnd\\\":\\\"50\\\",\\\"openDesc\\\":\\\"航班起飞前72小时到起飞前50分钟\\\"},{\\\"airportCode\\\":\\\"SHA\\\",\\\"channelno\\\":\\\"100001\\\",\\\"openFre\\\":\\\"72\\\",\\\"openEnd\\\":\\\"50\\\",\\\"openDesc\\\":\\\"航班起飞前72小时到起飞前50分钟\\\"},{\\\"airportCode\\\":\\\"SHA\\\",\\\"channelno\\\":\\\"10001\\\",\\\"openFre\\\":\\\"72\\\",\\\"openEnd\\\":\\\"50\\\",\\\"openDesc\\\":\\\"航班起飞前72小时到起飞前50分钟\\\"},{\\\"airportCode\\\":\\\"SHA\\\",\\\"channelno\\\":\\\"120000\\\",\\\"openFre\\\":\\\"72\\\",\\\"openEnd\\\":\\\"50\\\",\\\"openDesc\\\":\\\"航班起飞前72小时到起飞前50分钟\\\"},{\\\"airportCode\\\":\\\"SHA\\\",\\\"channelno\\\":\\\"20000\\\",\\\"openFre\\\":\\\"72\\\",\\\"openEnd\\\":\\\"50\\\",\\\"openDesc\\\":\\\"航班起飞前72小时到起飞前50分钟\\\"},{\\\"airportCode\\\":\\\"SHA\\\",\\\"channelno\\\":\\\"30000\\\",\\\"openFre\\\":\\\"72\\\",\\\"openEnd\\\":\\\"50\\\",\\\"openDesc\\\":\\\"航班起飞前72小时到起飞前50分钟\\\"},{\\\"airportCode\\\":\\\"SHA\\\",\\\"channelno\\\":\\\"40000\\\",\\\"openFre\\\":\\\" \\\",\\\"openEnd\\\":null,\\\"openDesc\\\":\\\" \\\"},{\\\"airportCode\\\":\\\"SHA\\\",\\\"channelno\\\":\\\"50000\\\",\\\"openFre\\\":\\\" \\\",\\\"openEnd\\\":null,\\\"openDesc\\\":\\\" \\\"},{\\\"airportCode\\\":\\\"SHA\\\",\\\"channelno\\\":\\\"60000\\\",\\\"openFre\\\":\\\" \\\",\\\"openEnd\\\":null,\\\"openDesc\\\":\\\" \\\"},{\\\"airportCode\\\":\\\"SHA\\\",\\\"channelno\\\":\\\"80000\\\",\\\"openFre\\\":\\\"72\\\",\\\"openEnd\\\":\\\"50\\\",\\\"openDesc\\\":\\\"航班起飞前72小时到起飞前50分钟\\\"},{\\\"airportCode\\\":\\\"SHA\\\",\\\"channelno\\\":\\\"90000\\\",\\\"openFre\\\":\\\"72\\\",\\\"openEnd\\\":\\\"50\\\",\\\"openDesc\\\":\\\"航班起飞前72小时到起飞前50分钟\\\"}]}]}\",\"serverCode\":\"200\"}";
        JSONObject jsonObject = JSON.parseObject(ff);
        AirportInfoResponse resp = (AirportInfoResponse) JsonUtil.jsonToBean(jsonObject.getString("Response"), AirportInfoResponse.class);
        AirportInfoResponse response = JSON.parseObject(jsonObject.getString("Response"), AirportInfoResponse.class);
        System.out.println(JSON.toJSONString(resp));
    }
}

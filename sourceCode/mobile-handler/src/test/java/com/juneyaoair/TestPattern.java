package com.juneyaoair;

import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

/**
 * Created by yaocf on 2023/1/5  17:58.
 */
@Slf4j
public class TestPattern {
    @Test
    public void testName(){
        String ticketName0 = "ZHANG/ZILAN";
        String ticketName1 = "ZHANG/ZILAN MS";
        String ticketName2 = "ZHANG/ZILAN INF(NOV21) (INFANT)";
        String pattern1 = ticketName0 + "\\s*(CHD)?\\s*(\\(CHILD\\))?\\s*(MS)?\\s*(MR)?\\s*(MISS)?\\s*(GM)?\\s*(INF\\(\\w{3}\\d{2}\\))?\\s*(\\(INFANT\\))?\\s*";
        String pattern2 = ticketName0 + "(\\s*(CHD)?\\s*(\\(CHILD\\))?\\s*|\\s*(MR)?\\s*|\\s*(MS)?\\s*|\\s*(INF\\(\\w{3}\\d{2}\\))?\\s*(\\(INFANT\\))?\\s*)";
        //String pattern = name + "\\s*INF\\(\\w{3}\\d{2}\\)\\s*\\(INFANT\\)\\s*";
        log.info("ticketName0匹配pattern1结果:{}",ticketName0.matches(pattern1));
        log.info("ticketName1匹配pattern1结果:{}",ticketName1.matches(pattern1));
        log.info("ticketName2匹配pattern1结果:{}",ticketName2.matches(pattern1));
        log.info("ticketName0匹配pattern2结果:{}",ticketName0.matches(pattern2));
        log.info("ticketName1匹配pattern2结果:{}",ticketName1.matches(pattern2));
        log.info("ticketName2匹配pattern2结果:{}",ticketName2.matches(pattern2));
    }
}

package com.juneyaoair;

import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.util.Base64Utils;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

/**
 * <AUTHOR>
 * @description
 * @date 2025/7/3 9:58
 */
@Slf4j
public class CustomerTest {
    @Test
    public void testCustomer() throws UnsupportedEncodingException {
        String url = "https://ocs.juneyaoair.com/out/ClientWeb/index.html?";
        String param = "company_id=gh_b4d9tomisoft_web3&custLevel=5&custType=金卡&username=潘佳芝&sex=女&phone=18602587860";
        //加密 拼装链接
        String encode = URLEncoder.encode(param, "UTF-8");
        String info = Base64Utils.encodeToString(encode.getBytes());
        url = url + info;
        log.error("输出地址：{}",url);
    }
}

package com.juneyaoair;

import com.juneyaoair.utils.DES3;
import com.juneyaoair.utils.EncoderHandler;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @description
 * @date 2024/12/19 20:10
 */
public class DesTest {
    private static final Logger log = LoggerFactory.getLogger(DesTest.class);

    @Test
    public void testDes(){
        String desKey = EncoderHandler.encodeByMD5("juneyaoair");
        desKey = EncoderHandler.encodeByMD5(desKey);
        //原始明文
        String customerNoSource = DES3.des3DecodeECB(desKey, "D664E908E551123D");
        log.info("解密后信息:{}",customerNoSource);
    }
}

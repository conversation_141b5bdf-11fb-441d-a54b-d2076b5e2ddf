package com.juneyaoair;

import com.juneyaoair.pattern.PatternCommon;
import junit.framework.Assert;
import org.junit.Test;

import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @description
 * @date 2025/1/21 17:11
 */
public class PatternCommonTest {

    @Test
    public void testGvGroup() {
        String str = "O/NONEND/PENALTY APPLIE/GV4";
        Pattern pattern = Pattern.compile(PatternCommon.GV_GROUP);
        Assert.assertTrue(pattern.matcher(str).find());
    }

}
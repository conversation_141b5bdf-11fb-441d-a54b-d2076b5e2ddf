<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.juneyaoair</groupId>
        <artifactId>super-pom</artifactId>
        <version>1.0.3</version>
    </parent>
    <groupId>com.juneyaoair</groupId>
    <artifactId>mobile</artifactId>
    <packaging>pom</packaging>
    <version>1.0-SNAPSHOT</version>
    <modules>
        <module>common-utils</module>
        <module>mobile-core</module>
        <module>mobile-handler</module>
        <module>common-base-class</module>
        <module>crm-base</module>
        <module>component-jpasskit</module>
    </modules>

    <properties>
        <java-version>1.8</java-version>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.build.locales>zh_CN</project.build.locales>
        <project.build.jdk>1.8</project.build.jdk>
        <skipTests>true</skipTests>
        <javaee.version>7.0</javaee.version>
        <spring.version>4.2.5.RELEASE</spring.version>
        <spring.data.mongo.version>1.8.4.RELEASE</spring.data.mongo.version>
        <spring.data.redis.version>1.8.4.RELEASE</spring.data.redis.version>
        <hibernate.version>5.1.0.Final</hibernate.version>
        <hibernate.validator.version>5.2.1.Final</hibernate.validator.version>
        <cxf.version>3.1.5</cxf.version>
        <oracle.driver.version>12.0.2</oracle.driver.version>
        <jedis.driver.version>2.8.1</jedis.driver.version>
        <mongo.driver.version>3.1.0</mongo.driver.version>
        <alibaba.fastjson.version>1.2.83</alibaba.fastjson.version>
        <alibaba.druid.version>1.0.17</alibaba.druid.version>
        <log4j.version>1.2.16</log4j.version>
        <apache.log4j.version>2.17.1</apache.log4j.version>
        <apache.lang3.version>3.4</apache.lang3.version>
        <apache.httpclient.version>4.5.2</apache.httpclient.version>
        <junit.version>4.12</junit.version>
        <com.jackson.version>2.10.3</com.jackson.version>
        <!--不可随意升级，升级后导致cLastName此风格属性无法解析-->
        <lombok.version>1.16.12</lombok.version>
        <!--<lombok.version>1.18.24</lombok.version>-->
        <com.gson.version>2.6.2</com.gson.version>
        <cat.client.version>3.0.0</cat.client.version>
        <slf4j.api.version>1.7.21</slf4j.api.version>
        <apollo.client.version>1.6.1</apollo.client.version>
        <swagger.annotations.version>1.5.21</swagger.annotations.version>
        <io.springfox.version>2.9.2</io.springfox.version>
        <knife4j.version>2.0.5</knife4j.version>
        <common.dto.base.version>1.1.0</common.dto.base.version>
        <cuss.dto.booking.version>2.0.4-SNAPSHOT</cuss.dto.booking.version>
        <javax.servlet.version>3.0.1</javax.servlet.version>
        <commons.httpclient.version>3.1</commons.httpclient.version>
        <commons.lang.version>2.5</commons.lang.version>
        <commons.codec.version>1.11</commons.codec.version>
        <commons.logging.version>1.2</commons.logging.version>
        <cglib.version>3.2.5</cglib.version>
        <mybatis.version>3.2.5</mybatis.version>
        <jboss.logging.version>3.1.3.GA</jboss.logging.version>
        <maven.compiler.plugin.version>3.6.0</maven.compiler.plugin.version>
        <guava.version>24.0-jre</guava.version>
        <bouncycastle.version>1.60</bouncycastle.version>
        <commons-io.version>2.6</commons-io.version>
        <pushy.version>0.13.10</pushy.version>
        <pinyin4j.version>2.5.1</pinyin4j.version>
        <io.jsonwebtoken.jjwt>0.9.1</io.jsonwebtoken.jjwt>
        <com.auth0.jwks-rsa>0.9.0</com.auth0.jwks-rsa>
        <org.apache.zookeeper.zookeeper>3.4.6</org.apache.zookeeper.zookeeper>
        <com.alibaba.dubbo>2.8.4</com.alibaba.dubbo>
        <io.netty.netty-all>4.1.37.Final</io.netty.netty-all>
        <org.mapstruct.version>1.4.2.Final</org.mapstruct.version>
        <caffeine.version>2.9.0</caffeine.version>
        <minio.version>8.0.0</minio.version>
        <javassist.version>3.25.0-GA</javassist.version>
        <hutool.version>5.8.26</hutool.version>
        <sonar.exclusions>**/generated-sources/**,**/test/**</sonar.exclusions>
    </properties>


    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>javax</groupId>
                <artifactId>javaee-api</artifactId>
                <version>${javaee.version}</version>
            </dependency>
            <dependency>
                <groupId>javax.servlet</groupId>
                <artifactId>javax.servlet-api</artifactId>
                <version>${javax.servlet.version}</version>
            </dependency>
            <!-- ****** Spring相关 开始 -->
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-web</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-webmvc</artifactId>
                <version>${spring.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-jdbc</artifactId>
                <version>${spring.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-context</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-context-support</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-beans</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-aop</artifactId>
                <version>${spring.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-core</artifactId>
                <version>${spring.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-tx</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-expression</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-orm</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-aspects</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-test</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.data</groupId>
                <artifactId>spring-data-mongodb</artifactId>
                <version>${spring.data.mongo.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.data</groupId>
                <artifactId>spring-data-mongodb-cross-store</artifactId>
                <version>${spring.data.mongo.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.data</groupId>
                <artifactId>spring-data-mongodb-log4j</artifactId>
                <version>${spring.data.mongo.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>log4j</groupId>
                        <artifactId>log4j</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.springframework.data</groupId>
                <artifactId>spring-data-redis</artifactId>
                <version>${spring.data.redis.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework.data</groupId>
                        <artifactId>spring-data-mongodb-log4j</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- ****** Spring相关 结束 -->
            <!-- hibernate部分 -->
            <dependency>
                <groupId>org.hibernate</groupId>
                <artifactId>hibernate-core</artifactId>
                <version>${hibernate.version}</version>
            </dependency>
            <dependency>
                <groupId>org.hibernate</groupId>
                <artifactId>hibernate-validator</artifactId>
                <version>${hibernate.validator.version}</version>
            </dependency>
            <!--****** Apache 相关 开始 -->
            <!-- log4j2 日志实现 START -->
            <dependency>
                <groupId>log4j</groupId>
                <artifactId>log4j</artifactId>
                <version>${log4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-api</artifactId>
                <version>${apache.log4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-core</artifactId>
                <version>${apache.log4j.version}</version>
            </dependency>
            <!-- log4j2 日志实现 END-->

            <!-- 日志桥接包   桥接包的版本须对应log4j2的版本 -->
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-slf4j-impl</artifactId>
                <version>${apache.log4j.version}</version>
            </dependency>
            <!-- 日志框架(门面) -->
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-api</artifactId>
                <version>${slf4j.api.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-logging</groupId>
                <artifactId>commons-logging</artifactId>
                <version>${commons.logging.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-lang</groupId>
                <artifactId>commons-lang</artifactId>
                <version>${commons.lang.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${apache.lang3.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpclient</artifactId>
                <version>${apache.httpclient.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-codec</groupId>
                <artifactId>commons-codec</artifactId>
                <version>${commons.codec.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <!--commons-httpclient已经不在维护，后期剔除相关引用-->
            <dependency>
                <groupId>commons-httpclient</groupId>
                <artifactId>commons-httpclient</artifactId>
                <version>${commons.httpclient.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>commons-codec</artifactId>
                        <groupId>commons-codec</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-logging</artifactId>
                        <groupId>commons-logging</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!--****** Apache 相关 结束 -->


            <!--****** Alibaba 相关 开始 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${alibaba.fastjson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid</artifactId>
                <version>${alibaba.druid.version}</version>
            </dependency>

            <!--****** Alibaba 相关 结束 -->

            <!-- jackson -->
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-core</artifactId>
                <version>${com.jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-annotations</artifactId>
                <version>${com.jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>${com.jackson.version}</version>
            </dependency>

            <!-- gson -->
            <dependency>
                <groupId>com.google.code.gson</groupId>
                <artifactId>gson</artifactId>
                <version>${com.gson.version}</version>
            </dependency>

            <dependency>
                <groupId>com.oracle</groupId>
                <artifactId>ojdbc7</artifactId>
                <version>${oracle.driver.version}</version>
            </dependency>
            <dependency>
                <groupId>redis.clients</groupId>
                <artifactId>jedis</artifactId>
                <version>${jedis.driver.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mongodb</groupId>
                <artifactId>mongo-java-driver</artifactId>
                <version>${mongo.driver.version}</version>
            </dependency>
            <!-- 拼音工具包 -->
            <dependency>
                <groupId>com.belerweb</groupId>
                <artifactId>pinyin4j</artifactId>
                <version>${pinyin4j.version}</version>
            </dependency>

            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>${junit.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.cxf</groupId>
                <artifactId>cxf-rt-frontend-jaxws</artifactId>
                <version>${cxf.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.cxf</groupId>
                <artifactId>cxf-rt-transports-http</artifactId>
                <version>${cxf.version}</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
                <scope>provided</scope>
            </dependency>

            <dependency>
                <groupId>com.juneyaoair</groupId>
                <artifactId>common-base-class</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.juneyaoair</groupId>
                <artifactId>common-utils</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.juneyaoair</groupId>
                <artifactId>mobile-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.juneyaoair</groupId>
                <artifactId>crm</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.juneyao</groupId>
                <artifactId>elastic-job</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.cat</groupId>
                <artifactId>cat-client</artifactId>
                <version>${cat.client.version}</version>
            </dependency>
            <!--吉祥内部 jar-->
            <dependency>
                <groupId>com.ctrip.framework.apollo</groupId>
                <artifactId>apollo-client</artifactId>
                <version>${apollo.client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.juneyaoair</groupId>
                <artifactId>cuss-dto-booking</artifactId>
                <version>${cuss.dto.booking.version}</version>
            </dependency>
            <dependency>
                <groupId>com.juneyaoair</groupId>
                <artifactId>common-dto-base</artifactId>
                <version>${common.dto.base.version}</version>
            </dependency>
            <dependency>
                <groupId>io.swagger</groupId>
                <artifactId>swagger-annotations</artifactId>
                <version>${swagger.annotations.version}</version>
                <scope>compile</scope>
            </dependency>
            <!--API-->
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-swagger2</artifactId>
                <version>${io.springfox.version}</version>
            </dependency>
            <!--引入Knife4j-->
            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-spring</artifactId>
                <!--在引用时请在maven中央仓库搜索最新版本号-->
                <version>${knife4j.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-spring-ui</artifactId>
                <!--在引用时请在maven中央仓库搜索最新版本号-->
                <version>${knife4j.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
                <scope>compile</scope>
            </dependency>
            <dependency>
                <groupId>cglib</groupId>
                <artifactId>cglib</artifactId>
                <version>${cglib.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis</artifactId>
                <version>${mybatis.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jboss.logging</groupId>
                <artifactId>jboss-logging</artifactId>
                <version>${jboss.logging.version}</version>
            </dependency>
            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcprov-jdk15on</artifactId>
                <version>${bouncycastle.version}</version>
            </dependency>
            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcpkix-jdk15on</artifactId>
                <version>${bouncycastle.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons-io.version}</version>
            </dependency>
            <dependency>
                <groupId>com.turo</groupId>
                <artifactId>pushy</artifactId>
                <version>${pushy.version}</version>
            </dependency>
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt</artifactId>
                <version>${io.jsonwebtoken.jjwt}</version>
            </dependency>
            <dependency>
                <groupId>com.auth0</groupId>
                <artifactId>jwks-rsa</artifactId>
                <version>${com.auth0.jwks-rsa}</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/io.netty/netty-all -->
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-all</artifactId>
                <version>${io.netty.netty-all}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${org.mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${org.mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.ben-manes.caffeine</groupId>
                <artifactId>caffeine</artifactId>
                <version>${caffeine.version}</version>
            </dependency>
            <dependency>
                <groupId>org.javassist</groupId>
                <artifactId>javassist</artifactId>
                <version>${javassist.version}</version>
            </dependency>
            <dependency>
                <groupId>io.minio</groupId>
                <artifactId>minio</artifactId>
                <version>${minio.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <!-- 配置私服仓库 -->
    <repositories>
        <repository>
            <id>nexus</id>
            <name>nexus</name>
            <url>http://nexus.juneyaoair.com:8081/repository/maven-public/</url>
           <!-- <url>http://nexus.juneyaoair.com:8081/repository/maven-snapshots/</url>-->
            <!-- true表示开启该远程仓库中release版本的下载，下同 -->
            <releases>
                <enabled>true</enabled>
            </releases>
            <!-- true表示开启该远程仓库中snapshots版本的下载，下同 -->
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
    </repositories>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.6.0</version>
                <configuration>
                    <source>${java-version}</source>
                    <target>${java-version}</target>
                    <skip>true</skip>
                    <encoding>UTF-8</encoding>
               <!--     <compilerArgs>
                        <arg>-bootclasspath</arg>
                        <arg>${java.home}/lib/rt.jar${path.separator}${java.home}/lib/jce.jar</arg>
                    </compilerArgs>-->
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${org.mapstruct.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
            <plugin>
                <groupId>pl.project13.maven</groupId>
                <artifactId>git-commit-id-plugin</artifactId>
                <version>4.0.2</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>revision</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <verbose>true</verbose>
                    <dateFormat>yyyy-MM-dd'T'HH:mm:ssZ</dateFormat>
                    <generateGitPropertiesFile>true</generateGitPropertiesFile>
                    <failOnNoGitDirectory>false</failOnNoGitDirectory>
                    <offline>true</offline>
                </configuration>
            </plugin>
        </plugins>
        <resources>
            <resource>
                <directory>src/main/resources/</directory>
                <!--打包时先排除profile文件夹-->
                <excludes>
                    <exclude>profile/**</exclude>
                </excludes>
                <includes>
                    <!--如果有其他定义通用文件，需要包含进来-->
                    <!--<include>messages/*</include>-->
                </includes>
            </resource>
            <resource>
                <!--根据不同的环境，把对应文件夹里的配置文件打包-->
                <directory>src/main/resources/profile/${profiles.active}</directory>
            </resource>
            <resource>
                <directory>src/main/resources/common/</directory>
            </resource>
        </resources>
    </build>
    <profiles>
        <profile>
            <!--不同环境Profile的唯一id-->
            <id>dev</id>
            <properties>
                <!--profiles.active是自定义的字段，自定义字段可以有多个-->
                <profiles.active>dev</profiles.active>
            </properties>
            <!--activation用来指定激活方式，可以根据jdk环境，环境变量，文件的存在或缺失-->
            <activation>
                <!--这个字段表示默认激活-->
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>test</id>
            <properties>
                <profiles.active>test</profiles.active>
            </properties>
        </profile>
        <profile>
            <id>pre-pro</id>
            <properties>
                <profiles.active>pre-pro</profiles.active>
            </properties>
        </profile>
        <profile>
            <id>pro</id>
            <properties>
                <profiles.active>pro</profiles.active>
            </properties>
        </profile>
    </profiles>

</project>
//package com.juneyaoair.dao;
//
//import com.juneyaoair.mobile.mongo.dao.push.IPushDao;
//import com.juneyaoair.mobile.mongo.entity.ReportDetailInfo;
//import com.juneyaoair.utils.json.JsonUtil;
//import com.juneyaoair.utils.util.DateUtils;
//import com.mongodb.BasicDBObject;
//import org.apache.logging.log4j.LogManager;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.data.mongodb.core.query.BasicUpdate;
//import org.springframework.data.mongodb.core.query.Criteria;
//import org.springframework.data.mongodb.core.query.Query;
//import org.springframework.data.mongodb.core.query.Update;
//import org.springframework.test.context.ContextConfiguration;
//import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
//
//import java.lang.reflect.Type;
//import java.util.Date;
//
///**
// * Created by zc on 2016/9/20.
// */
//@RunWith(SpringJUnit4ClassRunner.class)
//@ContextConfiguration(locations = "classpath*:/ApplicationContext-Core.xml")
//public class PushDaoTest {
//    public static org.apache.logging.log4j.Logger logger = LogManager.getLogger(PushDaoTest.class.getName());
//    @Autowired
//    private IPushDao iPushDao;
//    @Test
//    public void insert(){
//        ReportDetailInfo info = new ReportDetailInfo();
//        info.setMsg_id("0014");
//        info.setReportTime(DateUtils.convertToDateString(new Date()));
//        info.setAndroid_received("23");
//        iPushDao.insert(info);
//    }
//
//    @Test
//    public void upSet(){
//        ReportDetailInfo info = new ReportDetailInfo();
//        info.setMsg_id("0014");
//        info.setReportTime(DateUtils.convertToDateString(new Date()));
//        info.setAndroid_received("256");
//
//        Type type = new com.google.gson.reflect.TypeToken<BasicDBObject>(){}.getType();
//        logger.debug("Type :"+type.toString());
//        Query query = new Query(Criteria.where("msg_id").is(info.getMsg_id()));
//        BasicDBObject dbObject = new BasicDBObject();
//        dbObject.put("$set", JsonUtil.jsonToMap(JsonUtil.objectToJson(info),new com.google.gson.reflect.TypeToken<BasicDBObject>(){}.getType()));
//      BasicDBObject newdocument = new BasicDBObject();
//        newdocument.put("reportTime",DateUtils.convertToDateString(new Date()));
////       dbObject.put("$set",new BasicDBObject("reportTime",DateUtils.convertToDateString(new Date())));
//        Update update = new BasicUpdate(dbObject);
//        iPushDao.upsert(query,update,ReportDetailInfo.class);
//
//    }
//}

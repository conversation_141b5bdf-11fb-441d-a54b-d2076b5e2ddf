package com.juneyaoair.weixin.bean.user;

import lombok.Data;

/**
 * @ClassName WxBindUserInfo
 * @Description 微信绑定会员信息记录
 * <AUTHOR>
 * @Date 2019/10/28 17:58
 **/
@Data
public class WxBindUserInfo {
    private String userId;//记录ID
    private String groupId;
    private String isBinding;//是否已绑定 T:F 已绑：未绑
    private String ffpId;// 会员ID
    private String ffpCardNo; //会员卡号ID
    private String openId;// 微信openId
    private String nickName; // 昵称
    private String sex; // 性别
    private String city; // 城市
    private String country; // 国家
    private String province; // 省份
    private String language; //语言
    private String headImageUrl; //头像地址
    private String subscribeDate; //关注时间
    private String cancelSubscribeDate; //取关时间
    private String lastContactDate;
    private String userMark;
    private String isValid;//   T:F 有效：无效
    private String createDate;// 创建日期
    private String modifyUserId;// 修改人ID
    private String modifyDate;// 修改日期
    private String bindingDate;//绑定日期
    private String bindingSource; //绑定来源
    private String sid;
    private String unionId; //微信unionid
    private int type;// 0-微信公众号  1-微信小程序
}

package com.juneyaoair.weixin.dao.message;

import com.juneyaoair.weixin.bean.message.MessageAnswer;
import com.juneyaoair.weixin.bean.message.UsersMessage;

import java.util.List;

/**
 * Created by zc on 2017/1/11.
 */
public interface IWxUsersMessageDao {
    List<UsersMessage> queryUsersMessageList(UsersMessage usersMessage);
    UsersMessage queryUsersMessageById(String id);
    int queryUsersMessageListCnt(UsersMessage usersMessage);
    void updateUsersMessage(UsersMessage usersMessage);
    List<MessageAnswer> queryMessageAnswerById(String messageId);
    int saveMessageAnswer(MessageAnswer messageAnswer);
    int saveUsersMessage(UsersMessage usersMessage);
}

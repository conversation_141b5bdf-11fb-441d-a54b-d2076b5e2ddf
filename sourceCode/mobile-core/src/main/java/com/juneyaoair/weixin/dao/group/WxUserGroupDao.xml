<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD iBatis Mapper 3.0 //EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="WxUserGroupDao">

    <resultMap id="userGroupDetailsMap" type="userGroupDetails">
        <id column="GROUP_ID" property="groupId" ></id>
        <result column="rownum" property="rowNum"></result>
        <result column="GROUP_NAME" property="groupName"></result>
        <result column="GROUP_USER_COUNT" property="groupUserCount"></result>
        <result column="IS_VALID" property="isValid"></result>
        <result column="CREATE_USER_ID" property="createUserId"></result>
        <result column="CREATE_DATE" property="createDate" ></result>
        <result column="MODIFY_USER_ID" property="modifyUserId" ></result>
        <result column="MODIFY_DATE" property="modifyDate"></result>
    </resultMap>

    <select id="querySyncGroupInfoList" parameterType="userGroupDetails" resultMap="userGroupDetailsMap">
      SELECT T.GROUP_ID,
        T.GROUP_NAME,
        T.GROUP_USER_COUNT,
        T.IS_VALID,
        T.CREATE_USER_ID,
        TO_CHAR(T.CREATE_DATE, 'YYYY-MM-DD HH24:MI:SS') CREATE_DATE,
        T.MODIFY_USER_ID,
        TO_CHAR(T.MODIFY_DATE,'YYYY-MM-DD HH24:MI:SS') MODIFY_DATE
        FROM MP_USER_GROUP T
        WHERE 1=1
        <if test="groupId != null">
            and T.GROUP_ID = #{groupId,jdbcType=NUMERIC}
        </if>
        ORDER BY GROUP_ID
    </select>

    <select id="queryGroupInfoList" parameterType="userGroupDetails" resultMap="userGroupDetailsMap">
                    SELECT T.GROUP_ID,
                                  T.GROUP_NAME,
                                  T.GROUP_USER_COUNT,
                                  T.IS_VALID,
                                  T.CREATE_USER_ID,
                                  TO_CHAR(T.CREATE_DATE, 'YYYY-MM-DD HH24:MI:SS') CREATE_DATE,
                                  T.MODIFY_USER_ID,
                                  TO_CHAR(T.MODIFY_DATE,'YYYY-MM-DD HH24:MI:SS') MODIFY_DATE
                                  FROM MP_USER_GROUP T
                                  WHERE T.IS_VALID = 'T'
                                <if test="groupId != null">
                                    and T.GROUP_ID = #{groupId,jdbcType=NUMERIC}
                                </if>
        ORDER BY GROUP_ID
    </select>

    <select id="queryGroupInfoListCnt" parameterType="userGroupDetails" resultType="int">
        SELECT count(1)
        FROM MP_USER_GROUP T
        WHERE T.IS_VALID = 'T'
    </select>

    <insert id="addUserGroupInfo" parameterType="userGroupDetails">
        INSERT INTO MP_USER_GROUP T
        VALUES(#{groupId,jdbcType=NUMERIC},
                      #{groupName,jdbcType=VARCHAR},
                      #{groupUserCount,jdbcType=NUMERIC},
                      'T',
                      #{createUserId,jdbcType=NUMERIC},
                     sysdate,
                      #{modifyUserId,jdbcType=NUMERIC},
                      sysdate)
    </insert>

    <update id="updateUserGroupInfo" parameterType="userGroupDetails">
         UPDATE MP_USER_GROUP T
          SET T.GROUP_NAME = #{groupName,jdbcType=VARCHAR},
                  T.MODIFY_USER_ID =#{modifyUserId,jdbcType=NUMERIC},
                  T.MODIFY_DATE = SYSDATE,
                  T.GROUP_USER_COUNT =  #{groupUserCount,jdbcType=NUMERIC}
          WHERE T.IS_VALID = 'T'
          AND T.GROUP_ID = #{groupId,jdbcType=NUMERIC}
    </update>

    <delete id="deleteUserGroupInfo" parameterType="userGroupDetails">
        UPDATE MP_USER_GROUP T
          SET
                  T.MODIFY_USER_ID =#{modifyUserId,jdbcType=NUMERIC},
                  T.MODIFY_DATE = SYSDATE,
                  T.IS_VALID = 'F'
          WHERE  T.GROUP_ID = #{groupId,jdbcType=NUMERIC}
    </delete>
</mapper>
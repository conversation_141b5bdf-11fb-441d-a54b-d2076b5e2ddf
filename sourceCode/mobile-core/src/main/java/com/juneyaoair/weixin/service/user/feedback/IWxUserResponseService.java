package com.juneyaoair.weixin.service.user.feedback;

import com.juneyaoair.weixin.bean.user.feedback.CustomFeedBack;
import com.juneyaoair.weixin.bean.user.feedback.CustomFeedBackRes;
import com.juneyaoair.weixin.bean.user.feedback.paramFeedBack;

import java.util.List;

/**
 * Created by zc on 2017/2/16.
 */
public interface IWxUserResponseService {
        List<CustomFeedBack> queryAllCustomFeedBackInfoList(paramFeedBack paramFeedBack);
        int queryAllCustomFeedBackInfoListCnt(paramFeedBack paramFeedBack);
        List<CustomFeedBackRes> queryCustomBackResById(String id);
        void ignoreFeedBack(paramFeedBack paramFeedBack);
        void updateCustomFeedBack(paramFeedBack paramFeedBack);
        void insertCustomFeedBackRes(paramFeedBack paramFeedBack);
}

package com.juneyaoair.weixin.dao.BlackUserIP;

import com.juneyaoair.weixin.bean.BlackUserIP.MpBlackUerIP;
import com.juneyaoair.weixin.bean.BlackUserIP.MpBlackUserVisitLog;

import java.util.List;

public interface IMpBlackUerIPDao {

    int insert( MpBlackUerIP pojo);

    int insertList( List<MpBlackUerIP> pojo);

    List<MpBlackUerIP> select( MpBlackUerIP pojo);

    int update(MpBlackUerIP pojo);

    int insertLog(MpBlackUserVisitLog pojo);

    List<MpBlackUserVisitLog> selectLogs(MpBlackUserVisitLog pojo);
}

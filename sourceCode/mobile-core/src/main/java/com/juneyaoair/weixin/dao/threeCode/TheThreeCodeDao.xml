<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD iBatis Mapper 3.0 //EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="TheThreeCodeDao">
    <select id="getAirportCode" parameterType="string" resultType="com.juneyaoair.weixin.bean.detr.TCityAirportInfo">
       select t1.airport_code airportCode,
       t1.airport_name airportName,
       t1.airport_e_name airportEName,
       t1.airport_pin_yin_abb airportPinYinAbb,
       t1.city_code cityCode,
       t1.city_name cityName,
       t1.city_e_name cityEName,
       t1.city_pin_yin_abb cityPinYinAbb,
       t1.province_id provinceId,
       t1.province_name provinceName,
       t1.country_code countryCode,
       t1.is_international_city isInternationalCity,
       t1.is_hot_city isHotCity,
       t1.baidu_map_point baiduMapPoint,
       t1.office_address officeAddress,
       t1.office_tel officeTel,
       t1.create_datetime createDatetime,
       t1.create_id createId,
       t1.is_top_city isTopCity
  from t_city_airport_info t1
 where t1.airport_code = #{threecode,jdbcType=VARCHAR}
    </select>

    <select id="getAirportCodeList"  resultType="com.juneyaoair.weixin.bean.detr.TCityAirportInfo">
        select t1.airport_code airportCode,
        t1.airport_name airportName,
        t1.airport_e_name airportEName,
        t1.airport_pin_yin_abb airportPinYinAbb,
        t1.city_code cityCode,
        t1.city_name cityName,
        t1.city_e_name cityEName,
        t1.city_pin_yin_abb cityPinYinAbb,
        t1.province_id provinceId,
        t1.province_name provinceName,
        t1.country_code countryCode,
        t1.is_international_city isInternationalCity,
        t1.is_hot_city isHotCity,
        t1.baidu_map_point baiduMapPoint,
        t1.office_address officeAddress,
        t1.office_tel officeTel,
        t1.create_datetime createDatetime,
        t1.create_id createId,
        t1.is_top_city isTopCity
        from t_city_airport_info t1
    </select>
</mapper>
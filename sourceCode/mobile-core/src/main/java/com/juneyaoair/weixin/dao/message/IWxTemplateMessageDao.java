package com.juneyaoair.weixin.dao.message;

import com.juneyaoair.weixin.bean.message.TemplateMessage;
import com.juneyaoair.weixin.bean.message.TemplateMessageRes;

import java.util.List;


/**
 * Created by zc on 2017/1/24.
 */
public interface IWxTemplateMessageDao {
    List<TemplateMessage> queryTemplateMessageList(TemplateMessage templateMessage);
    int queryTemplateMessageListCnt();
    void updateTemplateMessage(TemplateMessage actionMessage);
    void deleteTemplateMessage(TemplateMessage actionMessage);
    void addTemplateMessage(TemplateMessage actionMessage);
    List<TemplateMessageRes> getTimeSendTemplateList();
    List<TemplateMessageRes> getSendTemplateList();
    TemplateMessageRes getTemplateMessageRespById(Integer id);
    void updateTemplateRes(TemplateMessageRes templateMessageRes);
    TemplateMessage queryTemplateMessage(TemplateMessage templateMessage);
    void saveTemplateRes(TemplateMessageRes templateMessageRes);
}

package com.juneyaoair.console.core.service.pushtask.impl;

import com.juneyaoair.console.core.bean.pushtask.PushTask;
import com.juneyaoair.console.core.dao.pushtask.IPushTaskDao;
import com.juneyaoair.console.core.service.pushtask.IPushTaskService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * Created by yaocf on 2017/5/9.
 */
@Service("pushTaskService")
public class PushTaskServiceImpl implements IPushTaskService {
    private Logger log = LoggerFactory.getLogger(this.getClass());
    @Autowired
    private IPushTaskDao pushTaskDao;
    @Override
    public List<PushTask> queryNoPushTask() {
        return pushTaskDao.queryNoPushTask();
    }

    @Override
    public int updataTaskState(Map pushTask) {
        try{
            return pushTaskDao.updataTaskState(pushTask);
        }catch (Exception e){
            log.error("更新数据异常:"+e.getMessage());
            return  0;
        }
    }

    @Override
    //批量插入任务数据
    public int insertPushtask(List<PushTask> pushTaskList) {
        try{
            return pushTaskDao.insertPushtask(pushTaskList);
        }catch (Exception e){
            log.error("插入数据异常:"+e.getMessage());
            return  0;
        }
    }
}

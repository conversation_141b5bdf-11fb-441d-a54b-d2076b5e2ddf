package com.juneyaoair.console.core.dao.pushtask;

import com.juneyaoair.console.core.bean.pushtask.PushCondition;

import java.util.List;

/**
 * Created by yaocf on 2018/1/26.
 */
public interface IPushConditionDao {
    int insertPushCondition(PushCondition pushCondition);
    int updatePushCondition(PushCondition pushCondition);
    PushCondition queryPushCondition(PushCondition pushCondition);
    List<PushCondition> queryNotDo(PushCondition pushCondition);
}

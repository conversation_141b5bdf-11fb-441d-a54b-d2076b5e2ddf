
package com.juneyaoair.mobile.webservice.client.crm;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>RemainSiteQueryResponseForClient complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType name="RemainSiteQueryResponseForClient"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="MessageHeader" type="{JuneyaoFFP2013.Service.Schema}ResponseMessageHeaderType" minOccurs="0"/&gt;
 *         &lt;element name="SiteCount" type="{http://www.w3.org/2001/XMLSchema}long"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "RemainSiteQueryResponseForClient", propOrder = {
    "messageHeader",
    "siteCount"
})
public class RemainSiteQueryResponseForClient {

    @XmlElement(name = "MessageHeader")
    protected ResponseMessageHeaderType messageHeader;
    @XmlElement(name = "SiteCount")
    protected long siteCount;

    /**
     * 获取messageHeader属性的值。
     * 
     * @return
     *     possible object is
     *     {@link ResponseMessageHeaderType }
     *     
     */
    public ResponseMessageHeaderType getMessageHeader() {
        return messageHeader;
    }

    /**
     * 设置messageHeader属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link ResponseMessageHeaderType }
     *     
     */
    public void setMessageHeader(ResponseMessageHeaderType value) {
        this.messageHeader = value;
    }

    /**
     * 获取siteCount属性的值。
     * 
     */
    public long getSiteCount() {
        return siteCount;
    }

    /**
     * 设置siteCount属性的值。
     * 
     */
    public void setSiteCount(long value) {
        this.siteCount = value;
    }

}

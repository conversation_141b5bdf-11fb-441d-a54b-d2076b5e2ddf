
package com.juneyaoair.mobile.webservice.client.crm;

import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>EmailAndSMSCustomQueryResponseForClient complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType name="EmailAndSMSCustomQueryResponseForClient"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="MessageHeader" type="{JuneyaoFFP2013.Service.Schema}ResponseMessageHeaderType" minOccurs="0"/&gt;
 *         &lt;element name="SmsAndEmailCustom" type="{JuneyaoFFP2013.Service.Schema}SmsAndEmailCustomType" maxOccurs="unbounded" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "EmailAndSMSCustomQueryResponseForClient", propOrder = {
    "messageHeader",
    "smsAndEmailCustom"
})
public class EmailAndSMSCustomQueryResponseForClient {

    @XmlElement(name = "MessageHeader")
    protected ResponseMessageHeaderType messageHeader;
    @XmlElement(name = "SmsAndEmailCustom")
    protected List<SmsAndEmailCustomType> smsAndEmailCustom;

    /**
     * 获取messageHeader属性的值。
     * 
     * @return
     *     possible object is
     *     {@link ResponseMessageHeaderType }
     *     
     */
    public ResponseMessageHeaderType getMessageHeader() {
        return messageHeader;
    }

    /**
     * 设置messageHeader属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link ResponseMessageHeaderType }
     *     
     */
    public void setMessageHeader(ResponseMessageHeaderType value) {
        this.messageHeader = value;
    }

    /**
     * Gets the value of the smsAndEmailCustom property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the smsAndEmailCustom property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getSmsAndEmailCustom().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link SmsAndEmailCustomType }
     * 
     * 
     */
    public List<SmsAndEmailCustomType> getSmsAndEmailCustom() {
        if (smsAndEmailCustom == null) {
            smsAndEmailCustom = new ArrayList<SmsAndEmailCustomType>();
        }
        return this.smsAndEmailCustom;
    }

}

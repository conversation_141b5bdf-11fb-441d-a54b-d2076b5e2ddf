
package com.juneyaoair.mobile.webservice.client.crm;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>CertificateUpdateResponseForClient complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType name="CertificateUpdateResponseForClient"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="MessageHeader" type="{JuneyaoFFP2013.Service.Schema}ResponseMessageHeaderType" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "CertificateUpdateResponseForClient", propOrder = {
    "messageHeader"
})
public class CertificateUpdateResponseForClient {

    @XmlElement(name = "MessageHeader")
    protected ResponseMessageHeaderType messageHeader;

    /**
     * 获取messageHeader属性的值。
     * 
     * @return
     *     possible object is
     *     {@link ResponseMessageHeaderType }
     *     
     */
    public ResponseMessageHeaderType getMessageHeader() {
        return messageHeader;
    }

    /**
     * 设置messageHeader属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link ResponseMessageHeaderType }
     *     
     */
    public void setMessageHeader(ResponseMessageHeaderType value) {
        this.messageHeader = value;
    }

}

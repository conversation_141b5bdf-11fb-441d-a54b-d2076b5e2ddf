package com.juneyaoair.mobile.mongo.service.appAdvert;

import com.juneyaoair.mobile.mongo.entity.AppAdvertDetailInfo;

import java.util.List;

/**
 * @ClassName IAppAdvertService
 * <AUTHOR>
 * @Description
 * @Date 2021-02-19 10:39
 **/
public interface IAppAdvertService {
    /**
     * 根据条件查询弹窗信息
     * @param ffpId
     * @param messageType
     * @return
     */
    List<AppAdvertDetailInfo> queryAppAdvertDetailInfo(String ffpId, String messageType);

    /**
     * 更新弹窗状态
     * 根据消息ID+ffpId
     * @return
     */
    void updatePopupState (String _id,String ffpId,String state);
}

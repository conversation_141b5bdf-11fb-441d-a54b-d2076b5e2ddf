
package com.juneyaoair.mobile.webservice.client.crm;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>PurchaseMileageInfoType complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType name="PurchaseMileageInfoType"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="MemberID" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="PurchaseType" type="{JuneyaoFFP2013.Service.Schema}PurchaseTypeType"/&gt;
 *         &lt;element name="PurchaseMemberID" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="PurchaseMemberENName" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="PurchaseMiles" type="{http://www.w3.org/2001/XMLSchema}long"/&gt;
 *         &lt;element name="IsNeedInvoice" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="Address" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="ZipCode" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="Telephone" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "PurchaseMileageInfoType", propOrder = {
    "memberID",
    "purchaseType",
    "purchaseMemberID",
    "purchaseMemberENName",
    "purchaseMiles",
    "isNeedInvoice",
    "address",
    "zipCode",
    "telephone"
})
public class PurchaseMileageInfoType {

    @XmlElement(name = "MemberID")
    protected String memberID;
    @XmlElement(name = "PurchaseType", required = true)
    @XmlSchemaType(name = "string")
    protected PurchaseTypeType purchaseType;
    @XmlElement(name = "PurchaseMemberID")
    protected String purchaseMemberID;
    @XmlElement(name = "PurchaseMemberENName")
    protected String purchaseMemberENName;
    @XmlElement(name = "PurchaseMiles")
    protected long purchaseMiles;
    @XmlElement(name = "IsNeedInvoice")
    protected String isNeedInvoice;
    @XmlElement(name = "Address")
    protected String address;
    @XmlElement(name = "ZipCode")
    protected String zipCode;
    @XmlElement(name = "Telephone")
    protected String telephone;

    /**
     * 获取memberID属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMemberID() {
        return memberID;
    }

    /**
     * 设置memberID属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMemberID(String value) {
        this.memberID = value;
    }

    /**
     * 获取purchaseType属性的值。
     * 
     * @return
     *     possible object is
     *     {@link PurchaseTypeType }
     *     
     */
    public PurchaseTypeType getPurchaseType() {
        return purchaseType;
    }

    /**
     * 设置purchaseType属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link PurchaseTypeType }
     *     
     */
    public void setPurchaseType(PurchaseTypeType value) {
        this.purchaseType = value;
    }

    /**
     * 获取purchaseMemberID属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPurchaseMemberID() {
        return purchaseMemberID;
    }

    /**
     * 设置purchaseMemberID属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPurchaseMemberID(String value) {
        this.purchaseMemberID = value;
    }

    /**
     * 获取purchaseMemberENName属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPurchaseMemberENName() {
        return purchaseMemberENName;
    }

    /**
     * 设置purchaseMemberENName属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPurchaseMemberENName(String value) {
        this.purchaseMemberENName = value;
    }

    /**
     * 获取purchaseMiles属性的值。
     * 
     */
    public long getPurchaseMiles() {
        return purchaseMiles;
    }

    /**
     * 设置purchaseMiles属性的值。
     * 
     */
    public void setPurchaseMiles(long value) {
        this.purchaseMiles = value;
    }

    /**
     * 获取isNeedInvoice属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getIsNeedInvoice() {
        return isNeedInvoice;
    }

    /**
     * 设置isNeedInvoice属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setIsNeedInvoice(String value) {
        this.isNeedInvoice = value;
    }

    /**
     * 获取address属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAddress() {
        return address;
    }

    /**
     * 设置address属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAddress(String value) {
        this.address = value;
    }

    /**
     * 获取zipCode属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZipCode() {
        return zipCode;
    }

    /**
     * 设置zipCode属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZipCode(String value) {
        this.zipCode = value;
    }

    /**
     * 获取telephone属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTelephone() {
        return telephone;
    }

    /**
     * 设置telephone属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTelephone(String value) {
        this.telephone = value;
    }

}

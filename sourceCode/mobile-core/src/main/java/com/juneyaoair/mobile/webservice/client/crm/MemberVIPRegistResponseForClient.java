
package com.juneyaoair.mobile.webservice.client.crm;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>MemberVIPRegistResponseForClient complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType name="MemberVIPRegistResponseForClient"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="MessageHeader" type="{JuneyaoFFP2013.Service.Schema}ResponseMessageHeaderType" minOccurs="0"/&gt;
 *         &lt;element name="ID" type="{http://www.w3.org/2001/XMLSchema}long"/&gt;
 *         &lt;element name="MemberID" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "MemberVIPRegistResponseForClient", propOrder = {
    "messageHeader",
    "id",
    "memberID"
})
public class MemberVIPRegistResponseForClient {

    @XmlElement(name = "MessageHeader")
    protected ResponseMessageHeaderType messageHeader;
    @XmlElement(name = "ID")
    protected long id;
    @XmlElement(name = "MemberID")
    protected String memberID;

    /**
     * 获取messageHeader属性的值。
     * 
     * @return
     *     possible object is
     *     {@link ResponseMessageHeaderType }
     *     
     */
    public ResponseMessageHeaderType getMessageHeader() {
        return messageHeader;
    }

    /**
     * 设置messageHeader属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link ResponseMessageHeaderType }
     *     
     */
    public void setMessageHeader(ResponseMessageHeaderType value) {
        this.messageHeader = value;
    }

    /**
     * 获取id属性的值。
     * 
     */
    public long getID() {
        return id;
    }

    /**
     * 设置id属性的值。
     * 
     */
    public void setID(long value) {
        this.id = value;
    }

    /**
     * 获取memberID属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMemberID() {
        return memberID;
    }

    /**
     * 设置memberID属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMemberID(String value) {
        this.memberID = value;
    }

}

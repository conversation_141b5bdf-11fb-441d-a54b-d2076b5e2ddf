
package com.juneyaoair.mobile.webservice.client.crm;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>MemberBaseInfoUpdateRequestFromClient complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType name="MemberBaseInfoUpdateRequestFromClient"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="MessageHeader" type="{JuneyaoFFP2013.Service.Schema}RequestMessageHeaderType" minOccurs="0"/&gt;
 *         &lt;element name="ID" type="{http://www.w3.org/2001/XMLSchema}long"/&gt;
 *         &lt;element name="CustomerUpdateInfo" type="{JuneyaoFFP2013.Service.Schema}CustomerUpdateInfoType" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "MemberBaseInfoUpdateRequestFromClient", propOrder = {
    "messageHeader",
    "id",
    "customerUpdateInfo"
})
public class MemberBaseInfoUpdateRequestFromClient {

    @XmlElement(name = "MessageHeader")
    protected RequestMessageHeaderType messageHeader;
    @XmlElement(name = "ID")
    protected long id;
    @XmlElement(name = "CustomerUpdateInfo")
    protected CustomerUpdateInfoType customerUpdateInfo;

    /**
     * 获取messageHeader属性的值。
     * 
     * @return
     *     possible object is
     *     {@link RequestMessageHeaderType }
     *     
     */
    public RequestMessageHeaderType getMessageHeader() {
        return messageHeader;
    }

    /**
     * 设置messageHeader属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link RequestMessageHeaderType }
     *     
     */
    public void setMessageHeader(RequestMessageHeaderType value) {
        this.messageHeader = value;
    }

    /**
     * 获取id属性的值。
     * 
     */
    public long getID() {
        return id;
    }

    /**
     * 设置id属性的值。
     * 
     */
    public void setID(long value) {
        this.id = value;
    }

    /**
     * 获取customerUpdateInfo属性的值。
     * 
     * @return
     *     possible object is
     *     {@link CustomerUpdateInfoType }
     *     
     */
    public CustomerUpdateInfoType getCustomerUpdateInfo() {
        return customerUpdateInfo;
    }

    /**
     * 设置customerUpdateInfo属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link CustomerUpdateInfoType }
     *     
     */
    public void setCustomerUpdateInfo(CustomerUpdateInfoType value) {
        this.customerUpdateInfo = value;
    }

}

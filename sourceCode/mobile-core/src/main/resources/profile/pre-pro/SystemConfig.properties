#===========================会员相关配置=================================
#测试接口
webservice.newcrm.url=http://mileage.juneyaoair.com:802/juneyaoffpmw.asmx
webservice.crm.http.url = http://mileage.juneyaoair.com:8090/fun003/MemberLogin
#crm�ӿڵ�ַV3 pro
crm.http.api.url = http://mileage.juneyaoair.com:804
crm.client.language.code=CN
mobile.client.passwd=MOBILE2014

#ͼƬ��������ַ
ueditor.url.mobile=$1src=\"http://media.juneyaoair.com/mobile
#���ı���Ŀ��ַ
ueditor.url.pc=(.*?)src=\"http://172.20.70.146:90
#===========================订单相关配置=================================
UnitOrder.WebAPI.VERSION=10
UnitOrder.WebAPI.M.CHANNEL_CODE=MOBILE
#test
UnitOrder.NewAPI.URL=http://openapi-uat.juneyaoair.com:9031
#===========================税务相关配置=================================
#˰����أ���ʽ
invoice.secSys=GW
invoice.sellerTaxId=913100007867226104
#�Ϻ����麽�չɷ����޹�˾
invoice.sellerName=上海吉祥航空股份有限公司
#���ѿ�
invoice.billinger=王佳俊
#�˳�˰��ϵͳ
webservice.inspurtax.url=http://invoice.juneyaoair.com/cwbase/JTGL/FPGLInterface.asmx

Swagger.Enable=true
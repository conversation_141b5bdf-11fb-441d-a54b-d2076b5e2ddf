/**
 * Copyright (C) 2019 Patrice Brend'amour <<EMAIL>>
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *         http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.juneyaoair.jpasskit.semantics;

import java.io.Serializable;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;

import org.apache.commons.lang3.builder.ToStringBuilder;


@JsonDeserialize(builder = PKCurrencyAmountBuilder.class)
public class PKCurrencyAmount implements Cloneable, Serializable {

    private static final long serialVersionUID = -8422267622415789780L;

    protected String currencyCode;
    protected String amount ;

    public String getCurrencyCode() {
        return currencyCode;
    }

    public String getAmount() {
        return amount;
    }

    @Override
    protected PKCurrencyAmount clone() {
        try {
            return (PKCurrencyAmount) super.clone();
        } catch (CloneNotSupportedException ex) {
            throw new IllegalStateException("Failed to clone PKCurrencyAmount instance", ex);
        }
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }

    public static PKCurrencyAmountBuilder builder() {
        return new PKCurrencyAmountBuilder();
    }

    public static PKCurrencyAmountBuilder builder(PKCurrencyAmount currencyAmount) {
        return builder().of(currencyAmount);
    }
}
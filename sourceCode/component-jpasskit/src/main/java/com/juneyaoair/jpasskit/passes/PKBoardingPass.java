/**
 * Copyright (C) 2019 Patrice Brend'amour <<EMAIL>>
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *         http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.juneyaoair.jpasskit.passes;

import com.juneyaoair.jpasskit.enums.PKPassType;
import com.juneyaoair.jpasskit.enums.PKTransitType;

public class PKBoardingPass extends PKGenericPass {

    private static final long serialVersionUID = 6099662826698064150L;

    protected PKTransitType transitType;

    protected PKBoardingPass() {
    }

    public PKTransitType getTransitType() {
        return transitType;
    }

    public static PKGenericPassBuilder builder() {
        return new PKGenericPassBuilder(PKPassType.PKBoardingPass);
    }

    public static PKGenericPassBuilder builder(PKBoardingPass pass) {
        PKGenericPassBuilder passBuilder = builder();
        return passBuilder.of(pass);
    }
}
